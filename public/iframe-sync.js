// Simple iframe communication script
// eslint-disable-next-line padding-line-between-statements
;(function () {
  // Only run if in iframe
  if (window.parent === window) return

  let currentUrl = window.location.href
  let isNavigating = false

  // Function to notify parent of URL changes
  function notifyParent(url) {
    if (isNavigating) return // Prevent loops

    window.parent.postMessage(
      {
        type: 'IFRAME_URL_CHANGE',
        url: url
      },
      '*'
    )
  }

  // Listen for URL changes
  function handleUrlChange() {
    const newUrl = window.location.href

    if (newUrl !== currentUrl) {
      currentUrl = newUrl
      notifyParent(newUrl)
    }
  }

  // Listen for parent messages
  window.addEventListener('message', function (event) {
    if (event.data.type === 'PARENT_URL_CHANGE') {
      const expectedUrl = event.data.url

      if (currentUrl !== expectedUrl) {
        isNavigating = true

        try {
          const url = new URL(expectedUrl)
          const newPath = url.pathname + url.search + url.hash

          window.history.pushState({}, '', newPath)
          currentUrl = expectedUrl

          // Trigger Next.js router update
          window.dispatchEvent(new PopStateEvent('popstate'))

          // Also trigger a custom event for Next.js
          window.dispatchEvent(
            new CustomEvent('iframe-navigation', {
              detail: { url: expectedUrl, path: newPath }
            })
          )
        } catch (error) {
          console.error('Error navigating to:', expectedUrl)
        }

        setTimeout(() => {
          isNavigating = false
        }, 100)
      }
    }
  })

  // Override history methods
  const originalPushState = history.pushState
  const originalReplaceState = history.replaceState

  history.pushState = function (...args) {
    originalPushState.apply(history, args)
    setTimeout(handleUrlChange, 0)
  }

  history.replaceState = function (...args) {
    originalReplaceState.apply(history, args)
    setTimeout(handleUrlChange, 0)
  }

  // Listen for popstate
  window.addEventListener('popstate', handleUrlChange)

  // Send initial URL and request parent context
  setTimeout(() => {
    notifyParent(currentUrl)

    // Request parent context for navigation
    window.parent.postMessage(
      {
        type: 'REQUEST_PARENT_CONTEXT'
      },
      '*'
    )
  }, 100)

  // Handle link clicks for middle-click and new tab
  document.addEventListener('click', function (e) {
    const link = e.target.closest('a')

    if (!link || !link.href) return

    // Check if it's middle-click or ctrl+click (new tab)
    if (e.button === 1 || e.ctrlKey || e.metaKey) {
      e.preventDefault()

      try {
        const iframeUrl = new URL(link.href)
        const iframePath = iframeUrl.pathname + iframeUrl.search

        // Get parent origin and construct parent URL
        const parentOrigin = window.parent.location.origin
        let parentUrl

        // Determine parent prefix based on current parent URL
        try {
          const currentParentUrl = window.parent.location.href

          if (currentParentUrl.includes('/admin-portal/')) {
            parentUrl = parentOrigin + '/admin-portal' + iframePath
          } else if (currentParentUrl.includes('/affiliate/')) {
            parentUrl = parentOrigin + '/affiliate' + iframePath
          } else if (currentParentUrl.includes('/partner-portal/')) {
            parentUrl = parentOrigin + '/partner-portal/service' + iframePath
          } else {
            // Fallback - try to get from postMessage
            window.parent.postMessage(
              {
                type: 'OPEN_IN_NEW_TAB',
                iframePath: iframePath
              },
              '*'
            )

            return
          }

          window.open(parentUrl, '_blank')
        } catch (crossOriginError) {
          // Can't access parent location due to cross-origin, use postMessage
          window.parent.postMessage(
            {
              type: 'OPEN_IN_NEW_TAB',
              iframePath: iframePath
            },
            '*'
          )
        }
      } catch (error) {
        console.error('Error opening link in new tab:', error)
      }
    }
  })

  // Also handle middle mouse button down
  document.addEventListener('mousedown', function (e) {
    if (e.button === 1) {
      // Middle mouse button
      const link = e.target.closest('a')

      if (link && link.href) {
        e.preventDefault()
      }
    }
  })
})()
