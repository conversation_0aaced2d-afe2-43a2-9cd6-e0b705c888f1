var AFFILIATE = {
  tracking: function () {
    var trafficIdUrl = this.getFromUrl('aff_content')
    var trafficUser = this.getFromUrl('aff_developer')
    var sourceUrl = this.getFromUrl('aff_source')

    // L<PERSON>y thời gian hết hạn cookie từ attribute trên script
    var scriptTag = document.getElementById('affiliate-script')
    var cookieExpireTime = parseFloat(scriptTag?.getAttribute('data-cookie-expire-time')) || 0

    if (sourceUrl === 'affiliate' && trafficIdUrl) {
      this.setCookie('affiliate_link_code', trafficIdUrl, cookieExpireTime, true)
      this.setCookie('aff_u', trafficUser, cookieExpireTime, true)
      this.setCookie('aff_source', sourceUrl, cookieExpireTime, true)

      return true
    }

    return false
  },

  setCookie: function (key, value, days, subdomain) {
    var d = new Date()

    d.setTime(d.getTime() + days * 24 * 60 * 60 * 1000)
    var expires = 'expires=' + d.toUTCString()
    var domain = subdomain ? '' : 'domain=' + window.location.hostname + '; '

    document.cookie = key + '=' + value + '; ' + expires + '; ' + domain + 'path=/'
  },

  getFromUrl: function (name, url) {
    if (!url) url = location.href
    name = name.replace(/[[]/, '\\[').replace(/[]]/, '\\]')
    var regex = new RegExp('[\\?&]' + name + '=([^&#]*)')
    var results = regex.exec(url)

    return results == null ? null : results[1]
  },

  removeCookie: function () {
    this.setCookie('affiliate_link_code', '', 0, true)
    this.setCookie('aff_u', '', 0, true)
    this.setCookie('aff_source', '', 0, true)
  }
}

// Bắt đầu theo dõi ngay khi script được tải
AFFILIATE.tracking()
