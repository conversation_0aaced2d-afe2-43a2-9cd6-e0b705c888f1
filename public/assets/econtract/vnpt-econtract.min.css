:root {
  --econtract-blue: #007bff;
  --econtract-indigo: #6610f2;
  --econtract-purple: #6f42c1;
  --econtract-pink: #e83e8c;
  --econtract-red: #dc3545;
  --econtract-orange: #fd7e14;
  --econtract-yellow: #ffc107;
  --econtract-green: #28a745;
  --econtract-teal: #20c997;
  --econtract-cyan: #17a2b8;
  --econtract-white: #fff;
  --econtract-gray: #6c757d;
  --econtract-gray-light: #c3c3c3;
  --econtract-gray-dark: #343a40;
  --econtract-primary: #007bff;
  --econtract-secondary: #6c757d;
  --econtract-success: #28a745;
  --econtract-info: #17a2b8;
  --econtract-warning: #ffc107;
  --econtract-danger: #dc3545;
  --econtract-light: #f8f9fa;
  --econtract-dark: #343a40;
  --econtract-border-gray: #dee2e6;
  --econtract-breakpoint-xs: 0;
  --econtract-breakpoint-sm: 576px;
  --econtract-breakpoint-md: 768px;
  --econtract-breakpoint-lg: 992px;
  --econtract-breakpoint-xl: 1200px;
  --font-family-esign: 'Source Sans Pro', sans-serif;
}
#econtract-workspace #overlay-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.2);
  z-index: 1048;
}
#econtract-workspace .spinner-dual-ring {
  position: fixed;
  top: 50%;
  left: 50%;
  height: 80px;
  min-width: 80px;
  transform: translate(-50%, -50%);
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 0 7px 0 rgba(0, 0, 0, 0.1);
  padding: 10px;
  z-index: 1049;
  display: flex;
  justify-content: center;
  align-items: center;
}
#econtract-workspace .spinner-dual-ring span {
  padding-left: 8px;
}
#econtract-workspace .loader {
  border: 4px solid #f3f3f3;
  border-radius: 50%;
  border-top: 4px solid var(--econtract-primary);
  width: 32px;
  height: 32px;
  -webkit-animation: spin 2s linear infinite;
  animation: spin 2s linear infinite;
}
@-webkit-keyframes spin {
  0% {
    -webkit-transform: rotate(0);
  }
  100% {
    -webkit-transform: rotate(360deg);
  }
}
@keyframes spin {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
