!(function (n, o) {
  'object' == typeof exports && 'underfind' != typeof module
    ? (module.exports = o())
    : 'function' == typeof define && define.amd
      ? define(o)
      : (n.VNPTeContract = o())
})(this, function () {
  'use strict'
  var n = (function (n) {
    const o = 'https://hopdong-demo.vnptit3.vn'
    var e,
      t,
      c = {}

    ;(n.init = function (n = null) {
      n, (document.head.innerHTML += s), (document.head.innerHTML += a), (document.body.innerHTML += i)
    }),
      (n.close = function () {}),
      (n.open = function (i = null) {
        ;(c.accessToken = i.accessToken || ''),
          (c.contractGroupId = i.contractGroupId || ''),
          (c.documentId = i.documentId || ''),
          (c.action = i.action || ''),
          (function (c) {
            let i = window.screen.availHeight - 100,
              s = window.screen.availWidth - 150

            ;(e = window.open(
              c,
              'targetWindow',
              `toolbar=no,\n            location=no,\n            status=no,\n            menubar=no,\n            scrollbars=yes,\n            resizable=yes,\n            width=${s},\n            height=${i}`
            )),
              window.addEventListener(
                'message',
                function (e) {
                  if (
                    (console.log('-------------------'),
                    console.log('You have message: ', e),
                    console.log('Origin: ', e.origin),
                    console.log('-------------------'),
                    e.origin != o)
                  )
                    throw new Error('Không đúng origin!')
                  n.callBackEndSign(e.data)
                },
                !1
              ),
              clearInterval(t),
              (t = setInterval(function () {
                e.closed &&
                  (console.log('window popup is closed'), n.callBackEndSign({ status: 'CANCEL' }), clearInterval(t))
              }, 500))
          })(
            'https://hopdong-demo.vnptit3.vn/wv/login?accessToken=' +
              c.accessToken +
              '&contractGroupId=' +
              c.contractGroupId +
              '&documentId=' +
              c.documentId +
              '&action=' +
              c.action
          )
      }),
      (n.getAccessToken = function () {
        return c.accessToken || ''
      }),
      (n.callBackEndSign = function () {})

    const i = '<div id="econtract-workspace"></div>',
      s =
        '<link href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:ital,wght@0,200;0,300;0,400;0,600;0,700;1,200;1,300;1,400;1,600;1,700&display=swap" rel="stylesheet">',
      a =
        '<link href="https://fonts.googleapis.com/icon?family=Material+Icons|Material+Icons+Outlined" rel="stylesheet"/>'

    return n
  })(n || {})

  return n
})
