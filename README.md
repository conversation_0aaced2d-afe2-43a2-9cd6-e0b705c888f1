# OneX Frontend

OneX Frontend là dự án giao diện người dùng cho nền tảng số OneX của VNPT, phục vụ đa dạng đối tượng (c<PERSON> nh<PERSON>, <PERSON><PERSON><PERSON> nghi<PERSON>, đ<PERSON><PERSON> t<PERSON>, qu<PERSON><PERSON> tr<PERSON> viên). Dự án hướng tới trải nghiệm hiện đại, nh<PERSON><PERSON> quán, responsive, tích hợp sâu với backend VNPT.

## 🚀 Tech stack

- Next.js (App Router, TypeScript)
- Ant Design, Tailwind CSS
- Redux Toolkit, React Query
- Formik, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, E<PERSON>int, Prettier

## 📁 Cấu trúc thư mục chính

```
src/
  app/           # Routing & layout (Next.js App Router)
  components/    # Component UI nhỏ, tái sử dụng
  views/         # View tổng hợp (logic, layout, truyền props)
  constants/     # Hằng số chia theo từng chức năng
  types/         # TypeScript types chia theo từng chức năng
  utils/         # Tiện ích chia theo từng chức năng
  validator/     # Logic xác thực chia theo từng chức năng
  hooks/         # Custom hooks chia theo từng chức năng
  models/        # API models chia theo từng chức năng
  redux-store/   # Redux slices chia theo từng chức năng
```

## 📚 Tài liệu quy tắc & phát triển

- [Quy tắc tổ chức code FE (frontend-structure)](docs/frontend-structure.md)
- [Quy tắc đặt tên, convention file/folder/component](docs/frontend-structure.md#convention-đặt-tên-file-folder-component)
- [Quy tắc Clean Code React/JS](docs/clean-code.md)
- [Quy tắc Git chuẩn hóa dự án](docs/git-rule.md)

**Tất cả thành viên phải đọc kỹ các tài liệu trên trước khi phát triển hoặc review code.**

## ⚡️ Hướng dẫn cài đặt & phát triển

```bash
# Cài dependencies
yarn install

# Chạy dev
yarn dev

# Kiểm tra lint & type
yarn lint
yarn tsc --noEmit

# Build production
yarn build
```

- Yêu cầu: Node.js >= 16.x, Yarn >= 1.22.x

## 🤝 Đóng góp & liên hệ

- Đóng góp code, báo lỗi, thảo luận: tạo issue hoặc MR, luôn gắn link Jira/task liên quan.
- Mọi thay đổi lớn phải cập nhật tài liệu liên quan.

---

> Đọc kỹ các tài liệu trong thư mục `docs/` và `memory-bank/` trước khi phát triển hoặc review code để đảm bảo tuân thủ quy trình và tiêu chuẩn dự án.
