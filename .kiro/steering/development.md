---
description: Development best practices and quality standards for OneX Frontend
globs: ['**/*']
alwaysApply: true
---

# Best Practices Phát Triển & Tiêu <PERSON>n Chất Lượng

## Nhận Diện AI & Ngữ Cảnh

**LƯU Ý CỰC KỲ QUAN TRỌNG:** Khi làm việc trong môi trường <PERSON>, AI là Kiro + Memory Bank, KHÔNG PHẢI bất kỳ model nào (như <PERSON> hay model khác).

## Yêu Cầu Chất Lượng Code

### Kiểm Tra Chất Lượng Bắt Buộc

- **ESLint**: Tất cả code phải pass `yarn lint` không có lỗi hoặc cảnh báo
- **TypeScript**: Tất cả code phải pass `yarn check-types` không có lỗi type
- **Runtime**: Không có lỗi console hoặc cảnh báo trong browser console
- **Build**: Code phải compile thành công không có lỗi

### Quy Tắc Sử Dụng Constants

- **LUÔN LUÔN** đóng gói các magic numbers và strings thành constants có tên rõ ràng
- **TUYỆT ĐỐI KHÔNG** sử dụng trực tiếp các số như 0, 1, 2 hoặc strings mà không giải thích ý nghĩa
- **PHẢI** tạo constants với tên mô tả rõ ràng để giải thích giá trị đó là gì

```typescript
// ❌ SAI - Magic numbers và strings không rõ nghĩa
if (status === 1) {
  return items.slice(0, 10)
}
setTimeout(() => {}, 3000)
const apiUrl = '/api/users'

// ✅ ĐÚNG - Sử dụng constants có tên rõ ràng
const USER_STATUS = {
  ACTIVE: 1,
  INACTIVE: 0,
  PENDING: 2
} as const

const PAGINATION = {
  DEFAULT_PAGE_SIZE: 10,
  MAX_PAGE_SIZE: 100
} as const

const TIMEOUTS = {
  API_TIMEOUT: 3000,
  DEBOUNCE_DELAY: 300
} as const

const API_ENDPOINTS = {
  USERS: '/api/users',
  ORDERS: '/api/orders'
} as const

if (status === USER_STATUS.ACTIVE) {
  return items.slice(0, PAGINATION.DEFAULT_PAGE_SIZE)
}
setTimeout(() => {}, TIMEOUTS.API_TIMEOUT)
const apiUrl = API_ENDPOINTS.USERS
```

### Quy Tắc Đặt Tên Constants

- Sử dụng UPPER_SNAKE_CASE cho constants
- Nhóm các constants liên quan thành objects
- Sử dụng `as const` để đảm bảo type safety
- Đặt tên mô tả rõ ràng mục đích sử dụng

### Tiêu Chuẩn Pre-commit

- Xóa tất cả `console.log`, `debugger` statements trước khi commit
- Sửa tất cả unused imports và variables
- Đảm bảo key props đúng cho các phần tử trong array
- Thêm TypeScript interfaces đúng cho tất cả props

## Mẫu Phát Triển Component

### Client vs Server Components

```typescript
// Client Component (sử dụng hooks, event handlers)
'use client'
import { useState } from 'react'

export default function InteractiveComponent() {
  const [state, setState] = useState(false)
  return <button onClick={() => setState(!state)}>Toggle</button>
}

// Server Component (không hooks, static rendering)
export default function StaticComponent() {
  return <div>Static content</div>
}
```

### Template Cấu Trúc Component

```typescript
'use client' // Chỉ khi sử dụng React hooks

import type { FC } from 'react'

interface ComponentProps {
  /** Mô tả prop */
  title: string
  /** Callback function tùy chọn */
  onAction?: () => void
}

const Component: FC<ComponentProps> = ({ title, onAction }) => {
  return (
    <div className="flex items-center gap-4">
      <h2 className="text-lg font-semibold">{title}</h2>
      {onAction && (
        <button onClick={onAction} className="btn-primary">
          Action
        </button>
      )}
    </div>
  )
}

export default Component
```

## Hướng Dẫn Styling

### Sử Dụng Tailwind CSS

- **Phương pháp chính**: Sử dụng Tailwind utility classes
- **Component styling**: Sử dụng Ant Design components với Tailwind modifications
- **Custom styles**: Sử dụng CSS-in-JS với Emotion khi cần
- **Cấm**: Không sử dụng file `.css` riêng biệt

## QUY TẮC TỐI ƯU HÓA SỬ DỤNG TAILWIND CSS

### TUYỆT ĐỐI KHÔNG DÙNG MÀU TÙY CHỈNH

- **KHÔNG BAO GIỜ** sử dụng cú pháp `text-[#HEX]` hoặc `bg-[#HEX]` để định nghĩa màu
- **LUÔN LUÔN** sử dụng các lớp màu đã được định nghĩa trong theme của dự án:
  - Màu chữ: `text-gray-8`, `text-gray-11`, `text-bright-blue-9`, v.v.
  - Màu nền: `bg-blue-1`, `bg-gray-1`, `bg-white`, v.v.
  - Màu viền: `border-gray-3`, `border-blue-5`, v.v.

### QUY TRÌNH THÊM MÀU MỚI VÀO TAILWIND CONFIG

Nếu cần sử dụng màu mới chưa có trong theme, PHẢI tuân thủ quy trình sau:

1. **KIỂM TRA TRƯỚC** xem màu đã tồn tại trong tailwind.config.ts chưa
2. **THÊM MÀU MỚI** vào phần `baseColorLight` trong tailwind.config.ts:
   ```typescript
   const baseColorLight = {
     // Các màu hiện có...
     'tên-màu-mới': '#HEX_CODE'
   }
   ```
3. **ĐẶT TÊN THEO QUY ƯỚC**:
   - Tuân thủ quy ước đặt tên hiện tại: `[tên-màu]-[cấp độ]`
   - Ví dụ: `light-blue-1`, `gray-8`, `bright-blue-9`

### BẢNG THAM CHIẾU MÀU THƯỜNG DÙNG

#### Màu chữ

- `#0F1319` → `text-gray-11`
- `#394867` → `text-gray-8` hoặc `text-text-neutral-medium`
- `#0A6FD0` → `text-bright-blue-9`
- `rgba(2,23,60,0.25)` → `text-gray-4`

#### Màu nền

- `#F2F4F9` → `bg-gray-1`
- `#D9ECFF` → `bg-blue-1`
- `#EBF5FE` → `bg-light-blue-1`

### Mẫu Responsive Design

```typescript
// Mobile-first responsive classes
<div className="w-full md:w-1/2 lg:w-1/3 xl:w-1/4">
  <div className="p-4 sm:p-6 md:p-8">
    <h2 className="text-lg sm:text-xl md:text-2xl">Responsive Title</h2>
  </div>
</div>
```

### Sử Dụng Hệ Thống Màu

```typescript
// Sử dụng color tokens đã định nghĩa từ tailwind.config.ts
<div className="bg-primary text-white">
  <span className="text-sme-blue-7">Primary text</span>
  <span className="text-neutral-strong">Secondary text</span>
</div>
```

## Mẫu Quản Lý State

### Local State (useState)

```typescript
'use client'
import { useState } from 'react'

const Component = () => {
  const [loading, setLoading] = useState(false)
  const [data, setData] = useState<DataType[]>([])

  return <div>Component content</div>
}
```

### Global State (Redux)

```typescript
// Mẫu Redux slice
import { createSlice, PayloadAction } from '@reduxjs/toolkit'

interface StateType {
  items: Item[]
  loading: boolean
}

const slice = createSlice({
  name: 'feature',
  initialState: { items: [], loading: false } as StateType,
  reducers: {
    setItems: (state, action: PayloadAction<Item[]>) => {
      state.items = action.payload
    }
  }
})
```

### Server State (React Query)

```typescript
import { useQuery } from '@tanstack/react-query'

const useFeatureData = (id: string) => {
  return useQuery({
    queryKey: ['feature', id],
    queryFn: () => fetchFeatureData(id),
    enabled: !!id
  })
}
```

## Tiêu Chuẩn Tích Hợp API

### Mẫu API Client

```typescript
// src/models/FeatureAPI.ts
import { apiClient } from '@/utils/api'

export interface FeatureData {
  id: string
  name: string
}

export const FeatureAPI = {
  getAll: (): Promise<FeatureData[]> => apiClient.get('/features').then(res => res.data),

  getById: (id: string): Promise<FeatureData> => apiClient.get(`/features/${id}`).then(res => res.data),

  create: (data: Omit<FeatureData, 'id'>): Promise<FeatureData> =>
    apiClient.post('/features', data).then(res => res.data)
}
```

### Mẫu Xử Lý Lỗi

```typescript
import { message } from 'antd'

const handleApiError = (error: any) => {
  const errorMessage = error?.response?.data?.message || 'Đã xảy ra lỗi'
  message.error(errorMessage)
  console.error('API Error:', error)
}

// Sử dụng trong component
try {
  const result = await FeatureAPI.create(formData)
  message.success('Tạo thành công')
} catch (error) {
  handleApiError(error)
}
```

## Tiêu Chuẩn Xử Lý Form

### Mẫu Ant Design Form

```typescript
import { Form, Input, Button } from 'antd'

interface FormData {
  name: string
  email: string
}

const FormComponent = () => {
  const [form] = Form.useForm<FormData>()

  const onFinish = async (values: FormData) => {
    try {
      await submitForm(values)
      message.success('Gửi form thành công')
      form.resetFields()
    } catch (error) {
      handleApiError(error)
    }
  }

  return (
    <Form form={form} onFinish={onFinish} layout="vertical">
      <Form.Item name="name" label="Tên" rules={[{ required: true }]}>
        <Input />
      </Form.Item>
      <Form.Item name="email" label="Email" rules={[{ required: true, type: 'email' }]}>
        <Input />
      </Form.Item>
      <Form.Item>
        <Button type="primary" htmlType="submit">
          Gửi
        </Button>
      </Form.Item>
    </Form>
  )
}
```

## Đa Ngôn Ngữ (i18n)

### Sử Dụng Translation

```typescript
import { useLng } from '@/hooks/useLng'

const Component = () => {
  const { t } = useLng()

  return (
    <div>
      <h1>{t('common.welcome')}</h1>
      <p>{t('feature.description')}</p>
    </div>
  )
}
```

### Quy Tắc Translation

- **Không bao giờ hardcode text tiếng Việt** trong components
- Tất cả text hiển thị cho người dùng phải sử dụng translation keys
- Sử dụng translation keys mô tả rõ ràng: `feature.button.submit` không phải `btn1`

## Tối Ưu Hóa Hiệu Suất

### Code Splitting

```typescript
import dynamic from 'next/dynamic'

// Lazy load các component nặng
const HeavyComponent = dynamic(() => import('./HeavyComponent'), {
  loading: () => <div>Đang tải...</div>
})
```

### Tối Ưu Hình Ảnh

```typescript
import Image from 'next/image'

// Sử dụng Next.js Image component để tối ưu
<Image
  src="/assets/images/hero.jpg"
  alt="Hero image"
  width={800}
  height={400}
  priority // Cho hình ảnh above-the-fold
/>
```

## Tiêu Chuẩn Testing

### Template Component Testing

```typescript
import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import Component from './Component'

describe('Component', () => {
  it('renders correctly', () => {
    render(<Component title="Test" />)
    expect(screen.getByText('Test')).toBeInTheDocument()
  })

  it('handles user interaction', async () => {
    const user = userEvent.setup()
    const onAction = jest.fn()

    render(<Component title="Test" onAction={onAction} />)

    await user.click(screen.getByRole('button'))
    expect(onAction).toHaveBeenCalled()
  })
})
```

## Tiêu Chuẩn Documentation

### Documentation Component

```typescript
/**
 * Component tính năng để hiển thị và quản lý dữ liệu tính năng
 *
 * @param title - Tiêu đề để hiển thị
 * @param data - Mảng các item tính năng
 * @param onSelect - Callback khi item được chọn
 * @returns JSX element
 */
interface FeatureComponentProps {
  /** Tiêu đề chính */
  title: string
  /** Mảng dữ liệu tính năng */
  data: FeatureItem[]
  /** Callback lựa chọn */
  onSelect?: (item: FeatureItem) => void
}
```

### Documentation Function

```typescript
/**
 * Format giá trị tiền tệ với locale và ký hiệu phù hợp
 * @param amount - Số tiền cần format
 * @param currency - Mã tiền tệ (VND, USD, etc.)
 * @returns Chuỗi tiền tệ đã format
 */
export const formatCurrency = (amount: number, currency = 'VND'): string => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency
  }).format(amount)
}
```

## QUY TẮC SỬA LỖI LINTER, TYPE CHECKING, COMPILE, RUNTIME

- **PHẢI SỬA BẰNG ĐƯỢC TẤT CẢ LỖI** trước khi hoàn thành bất kỳ nhiệm vụ nào liên quan đến code:

  - Lỗi ESLint (yarn lint)
  - Lỗi TypeScript (yarn tsc --noEmit)
  - Lỗi Compile (như thiếu directive "use client")
  - Lỗi Runtime
  - Warnings (kể cả warning nhỏ nhất)

- **KHÔNG ĐƯỢC BỎ QUA BẤT KỲ LỖI NÀO** – kể cả lỗi nhỏ nhất, kể cả khi chỉ là warning

### QUY TRÌNH KIỂM TRA TRƯỚC KHI BÁO HOÀN THÀNH:

- PHẢI chạy lệnh `yarn lint` và `yarn tsc --noEmit` để kiểm tra
- PHẢI kiểm tra console trong developer tools để đảm bảo không có lỗi runtime
- PHẢI sửa tất cả lỗi và warnings được phát hiện
- CHỈ được báo hoàn thành khi code hoàn toàn sạch lỗi
- **TUYỆT ĐỐI KHÔNG ĐƯỢC BÁO HOÀN THÀNH khi code còn lỗi hoặc warning**

## QUY TẮC TUYỆT ĐỐI TUÂN THỦ FIGMA 100%

- **PHẢI làm đúng thiết kế Figma 100% đến từng chi tiết nhỏ nhất:**

  - Từng text, từng icon, từng trạng thái (hover, active, disabled, error, focus)
  - Từng spacing, từng padding, từng margin, từng khoảng cách giữa các phần tử
  - Từng label, từng mô tả, từng placeholder, từng tooltip
  - Từng button, từng input, từng radio/checkbox/tab, từng dropdown
  - Từng màu sắc, từng font, từng font-weight, từng font-size
  - Từng border, từng border-radius, từng shadow, từng opacity
  - Từng pixel, từng vị trí, từng kích thước
  - Từng thành phần UI như thanh search, filter, pagination
  - Từng trạng thái của dữ liệu (loading, empty, error, success)
  - Từng animation, transition, và hiệu ứng

- **KHÔNG ĐƯỢC tự ý thay đổi thiết kế dưới BẤT KỲ hình thức nào:**
  - KHÔNG được tự ý thay đổi bố cục, vị trí, thứ tự các thành phần
  - KHÔNG được tự ý thay đổi màu sắc, font chữ, kích thước, khoảng cách
  - KHÔNG được tự ý thay đổi nội dung text, placeholder, label, mô tả
  - KHÔNG được tự ý thay đổi icon, hình ảnh, animation
  - KHÔNG được tự ý thay đổi component (ví dụ: dùng Radio thay vì Checkbox)
  - KHÔNG được tự ý thay đổi, giản lược, hoặc "làm gần giống" bất kỳ phần nào
  - KHÔNG được tự ý thêm, bớt, hoặc điều chỉnh bất kỳ chi tiết nào
  - KHÔNG được bỏ qua bất kỳ thành phần UI nào trong thiết kế
  - KHÔNG được bỏ qua bất kỳ trạng thái nào của giao diện

### QUY TRÌNH PHÂN TÍCH THIẾT KẾ FIGMA TRƯỚC KHI CODE:

- PHẢI phân tích kỹ lưỡng toàn bộ thiết kế Figma trước khi bắt đầu code
- PHẢI liệt kê tất cả các thành phần UI có trong thiết kế
- PHẢI kiểm tra tất cả các layer, group, và component trong file Figma
- PHẢI kiểm tra các trạng thái khác nhau của giao diện (nếu có)
- PHẢI kiểm tra các biến thể của component (nếu có)
- PHẢI kiểm tra các thuộc tính chính xác (màu sắc, font, kích thước) từ Figma
- PHẢI đảm bảo không bỏ sót bất kỳ chi tiết nào trong thiết kế

### QUY TRÌNH KIỂM TRA TRƯỚC KHI BÁO HOÀN THÀNH:

- PHẢI so sánh trực tiếp với ảnh chụp màn hình hoặc file thiết kế Figma
- PHẢI kiểm tra từng chi tiết, từng pixel, từng text, từng spacing
- PHẢI sửa cho đến khi GIỐNG HOÀN TOÀN 100%, không còn bất kỳ điểm nào khác biệt
- PHẢI kiểm tra tất cả các trạng thái (hover, active, disabled, error, focus)
- PHẢI kiểm tra responsive trên các kích thước màn hình khác nhau (nếu có)
- PHẢI kiểm tra lại từng thành phần UI đã được liệt kê trong bước phân tích
- CHỈ được báo hoàn thành khi đã kiểm tra kỹ và KHÔNG CÒN BẤT KỲ ĐIỂM NÀO KHÁC BIỆT
- **TUYỆT ĐỐI KHÔNG ĐƯỢC BÁO HOÀN THÀNH khi chưa giống 100% thiết kế Figma**

### TUYỆT ĐỐI KHÔNG ĐƯỢC TỰ SÁNG TẠO khi không xem được thiết kế Figma:

- Nếu không kết nối được MCP Server hoặc không xem được thiết kế Figma, KHÔNG ĐƯỢC tự ý vẽ giao diện
- PHẢI báo cáo lỗi kết nối và đề xuất cách khắc phục MCP Server
- CHỈ thực hiện vẽ giao diện khi đã xem được thiết kế Figma đầy đủ
- PHẢI đảm bảo hiểu rõ 100% thiết kế trước khi bắt đầu code
- KHÔNG ĐƯỢC phỏng đoán hoặc tự sáng tạo bất kỳ phần nào của giao diện khi không thấy rõ thiết kế
- Khi không xem được thiết kế, PHẢI ưu tiên sửa lỗi kết nối MCP Server trước khi tiếp tục
