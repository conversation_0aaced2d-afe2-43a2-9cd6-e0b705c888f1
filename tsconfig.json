{
  "compilerOptions": {
    "target": "ESNext",
    "downlevelIteration": true,
    "lib": [
      "DOM",
      "DOM.Iterable",
      "ESNext"
    ],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "ESNext",
    "moduleResolution": "Bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "tsBuildInfoFile": "./.tsbuildinfo",
    "plugins": [
      {
        "name": "next"
      }
    ],
    "paths": {
      "@/*": [
        "./src/*"
      ],
      "@core/*": [
        "./src/@core/*"
      ],
      "@layouts/*": [
        "./src/@layouts/*"
      ],
      "@menu/*": [
        "./src/@menu/*"
      ],
      "@assets/*": [
        "./src/assets/*"
      ],
      "@components/*": [
        "./src/components/*"
      ],
      "@configs/*": [
        "./src/configs/*"
      ],
      "@views/*": [
        "./src/views/*"
      ],
      "@public/*": [
        "./public/*"
      ]
    }
  },
  "include": [
    "next.config.mjs",
    "tailwind.config.ts",
    "next-env.d.ts",
    "**/*.ts",
    "**/*.tsx",
    ".next/types/**/*.ts",
    "interface/**/*.ts",
    "global.d.ts",
    "public/assets/affiliate/affiliate-tracking.js"
  ],
  "exclude": [
    "node_modules"
  ],
}
