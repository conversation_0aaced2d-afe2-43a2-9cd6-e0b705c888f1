# Project Brief: OneX Frontend

## Tổng quan dự án

Dự án OneX Frontend (onedx_fe_new) là ứng dụng giao diện người dùng cho hệ thống OneX của VNPT. Dự án được xây dựng bằng Next.js và phục vụ cho nhiều loại người dùng khác nhau.

## Mục tiêu

- Xây dựng giao diện người dùng hiện đại, đ<PERSON><PERSON> <PERSON><PERSON>, dễ sử dụng
- Hỗ trợ nhiều loại thiết bị (responsive design)
- <PERSON><PERSON><PERSON> hợp với các dịch vụ backend của VNPT
- Đảm bảo trải nghiệm người dùng tốt nhất

## Yêu cầu kỹ thuật

- Framework: Next.js
- UI Framework: MUI (Material-UI)
- State Management: Redux
- Styling: Tailwind CSS

## Quy tắc sử dụng ảnh Figma cho UI

- <PERSON><PERSON> phát triển U<PERSON> từ Figma, PHẢI tự động lấy ảnh từ Figma về, lưu vào `public/assets/images/pages/[module]/[feature]/`.
- Đặt tên file ảnh rõ ràng, không dấu, không khoảng trắng (ví dụ: `banner-hero.png`, `challenge-drone.png`).
- KHÔNG dùng ảnh ngoài hoặc ảnh mẫu (Unsplash, Pixabay, ...).
- Khi vẽ UI, PHẢI sử dụng đúng ảnh đã lấy từ Figma, đảm bảo giống thiết kế 100%.
- Nếu ảnh thay đổi trên Figma, PHẢI cập nhật lại ảnh local tương ứng.
