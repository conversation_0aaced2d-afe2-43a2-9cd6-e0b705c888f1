# Mẫu Thiết Kế Hệ Thống

## Tổng Quan Kiến Trúc

### Cấu Trúc Th<PERSON>

```
📁 src/
├── 📁 app             # Next.js pages và layouts theo App Router
│   ├── 📁 (sme-portal)       # Portal cho SME
│   ├── 📁 (partner-portal)   # Portal cho đối tác
│   ├── 📁 (product-catalog)  # Portal cho danh mục sản phẩm
│   ├── 📁 (payment)          # Portal cho thanh toán
│   ├── 📁 (iot-portal)       # Portal cho IoT
│   └── 📁 page-builder       # Hệ thống xây dựng trang động
├── 📁 @core           # Chức năng cốt lõi và tiện ích
├── 📁 @layouts        # Component layout
├── 📁 @menu          # Component menu và điều hướng
├── 📁 components     # React components tái sử dụng
│   ├── 📁 auth             # Components xác thực
│   ├── 📁 common           # Components dùng chung
│   ├── 📁 form             # Components form
│   ├── 📁 layout           # Components layout
│   ├── 📁 notification     # Components thông báo
│   └── 📁 page-builder     # Components cho builder
├── 📁 configs        # Cấu hình ứng dụng
├── 📁 constants      # Hằng số và enums
├── 📁 context        # React Context providers
├── 📁 hooks          # Custom React hooks
│   ├── 📁 useUser          # Hook quản lý user
│   ├── 📁 useRedirect      # Hook xử lý chuyển hướng
│   ├── 📁 useInfinity      # Hook xử lý vô hạn
│   └── 📁 useUpdateCart    # Hook cập nhật giỏ hàng
├── 📁 models         # Data models và API calls
├── 📁 redux-store    # Redux state management
│   ├── 📁 slices           # Redux slices
│   └── 📁 ReduxProvider    # Provider cho Redux
├── 📁 types          # TypeScript type definitions
├── 📁 utils          # Hàm tiện ích
│   ├── 📁 api              # Tiện ích API
│   ├── 📁 string           # Xử lý chuỗi
│   ├── 📁 date             # Xử lý ngày tháng
│   └── 📁 number           # Xử lý số
├── 📁 validator      # Logic xác thực form
└── 📁 views          # Components view cụ thể
```

## Kiến Trúc Hệ Thống

### Kiến Trúc Multi-portal

1. **Cấu Trúc Portal**

   - Portal SME
   - Portal Đối Tác
   - Danh Mục Sản Phẩm
   - Hệ Thống Thanh Toán
   - Portal IoT

2. **Tổ Chức Mã Nguồn**
   - Mỗi portal có thư mục riêng
   - Components chia sẻ
   - Components dành riêng cho portal
   - Tiện ích và helpers chung

### Mẫu Component

1. **Phân Cấp Component**

   - Components bố cục
   - Components trang
   - Components tính năng
   - Components dùng chung
   - Components giao diện

2. **Mẫu Component**
   - Components kết hợp
   - Props render
   - Components bậc cao
   - Hooks tùy chỉnh

### Quản Lý Trạng Thái

1. **Kiến Trúc Redux**

   - Cấu hình store
   - Tổ chức slice
   - Mẫu action
   - Mẫu selector

2. **Tích Hợp React Query**
   - Quản lý bộ nhớ đệm
   - Vô hiệu hóa truy vấn
   - Xử lý mutation
   - Xử lý lỗi

### Xử Lý Dữ Liệu

1. **API Client**

   - Triển khai lớp cơ sở
   - Bộ chặn request
   - Xử lý response
   - Quản lý lỗi

2. **Luồng Dữ Liệu**
   - Trạng thái server và client
   - Cập nhật thời gian thực
   - Cập nhật lạc quan
   - Phục hồi lỗi

### Page Builder

1. **Kiến Trúc**

   - Đăng ký component
   - Hệ thống kéo thả
   - Trình chỉnh sửa thuộc tính
   - Công cụ bố cục

2. **Tùy Chỉnh**
   - Mẫu component
   - Tùy chỉnh style
   - Ràng buộc bố cục
   - Quy tắc xác thực

## Quyết Định Kỹ Thuật

### Framework Frontend

1. **Next.js 15.3.0**

   - App Router
   - Server Components
   - Client Components
   - Render kết hợp

2. **React 19.1.0**
   - Hooks
   - Suspense
   - Xử lý lỗi
   - Tính năng đồng thời

### Quản Lý Trạng Thái

1. **Redux Toolkit**

   - Kho lưu trữ tập trung
   - RTK Query
   - Middleware
   - Công cụ phát triển

2. **React Query**
   - Trạng thái server
   - Bộ nhớ đệm
   - Cập nhật nền
   - Tải trước

### Mô hình MCP Server

1. **Kiến Trúc MCP**

   - **Tổ chức thư mục**

     ```
     📁 .mcp/
     ├── 📁 node_modules/      # Dependencies
     ├── 📄 mcp_core.js        # Xử lý logic cốt lõi
     ├── 📄 mcp.js             # CLI và điểm nhập server
     ├── 📄 .env               # Cấu hình môi trường
     ├── 📄 .env.example       # Mẫu cấu hình
     ├── 📄 package.json       # Quản lý dependencies
     └── 📄 .gitignore         # Loại trừ file khỏi git
     ```

   - **Tích hợp Cursor**
     ```
     📁 .cursor/
     ├── 📁 rules/
     │   └── 📄 messageScanner.js  # Quét tin nhắn để phát hiện URL
     └── 📄 mcp.json               # Cấu hình tích hợp MCP
     ```

2. **Luồng Xử Lý**

   ```mermaid
   sequenceDiagram
       participant User as Người dùng
       participant Chat as Cuộc trò chuyện
       participant Scanner as MCP Scanner
       participant Core as MCP Core
       participant API as Jira/Wiki/Figma API
       participant AI as AI Assistant

       User->>Chat: Nhập URL hoặc lệnh
       Chat->>Scanner: Quét tin nhắn
       Scanner->>Scanner: Phát hiện URL/lệnh
       Scanner->>Core: Gửi URL/lệnh đã phát hiện
       Core->>API: Gửi request API
       API->>Core: Trả về dữ liệu
       Core->>Core: Phân tích dữ liệu
       Core->>AI: Truyền thông tin phân tích
       AI->>Chat: Hiển thị kết quả phân tích
       alt Có lệnh "VAN: fix bug"
           AI->>AI: Kích hoạt chế độ VAN
           AI->>Chat: Thực hiện sửa lỗi dựa trên thông tin
       else Nhiệm vụ khác
           AI->>AI: Thực hiện nhiệm vụ theo yêu cầu
           AI->>Chat: Cập nhật kết quả
       end
   ```

   - Phát hiện URL Jira/Wiki/Figma từ người dùng
   - Phát hiện lệnh VAN và các lệnh khác
   - Phân tích URL để trích xuất thông tin
   - Xác thực và gửi yêu cầu đến máy chủ Jira/Wiki/Figma
   - Phân tích kết quả từ API
   - Hiển thị thông tin phân tích trực tiếp trong cuộc trò chuyện
   - Tự động kích hoạt các hành động như lệnh VAN
   - Sử dụng thông tin phân tích để thực hiện nhiệm vụ

3. **Xác thực và Bảo mật**

   - Quản lý thông tin đăng nhập thông qua biến môi trường
   - Xử lý thông tin đăng nhập an toàn
   - Hỗ trợ bỏ qua xác thực SSL cho mạng nội bộ
   - Không lưu thông tin nhạy cảm

4. **Giao diện lệnh**

   - Hỗ trợ chạy như ứng dụng độc lập
   - Chế độ stdio để tích hợp với Cursor
   - Lệnh để kiểm tra cấu hình
   - Lệnh để thiết lập thông tin đăng nhập
   - Xử lý URL trực tiếp từ dòng lệnh

5. **Tích Hợp với Cursor và AI**
   - Cơ chế tự động xử lý URL từ các nguồn Jira, Wiki và Figma
   - Hiển thị kết quả phân tích trực tiếp trong cuộc trò chuyện
   - Quy tắc nhận dạng và xử lý mã Jira (ví dụ: SPC_BOSPLATFORM-123)
   - Xử lý tự động lệnh "VAN: fix bug" kèm URL
   - Kích hoạt chế độ VAN sau khi phân tích URL
   - Sử dụng thông tin phân tích để AI thực hiện nhiệm vụ như sửa lỗi hoặc phát triển tính năng
   - Xử lý trực tiếp không lưu tệp, tối ưu hóa quy trình

```mermaid
graph TD
    UserMessage[Tin nhắn người dùng] --> MessageScanner[Message Scanner]
    MessageScanner --> DetectURL{Phát hiện URL?}

    DetectURL -->|Jira/Wiki URL| ProcessJiraWiki[Xử lý Jira/Wiki]
    DetectURL -->|Figma URL| ProcessFigma[Xử lý Figma]
    DetectURL -->|VAN Command| ProcessVAN[Xử lý VAN]
    DetectURL -->|No URL| End[Kết thúc]

    ProcessJiraWiki --> MCPServer[MCP Server]
    ProcessFigma --> MCPServer

    MCPServer --> ApiRequest[Gửi API Request tới Jira/Wiki/Figma]
    ApiRequest --> AnalyzeResponse[Phân tích kết quả]

    AnalyzeResponse --> DisplayResults[Hiển thị kết quả trong chat]

    DisplayResults --> AIAnalyze[AI phân tích thông tin]

    ProcessVAN --> VANWithURL{Có URL?}
    VANWithURL -->|Yes| ProcessJiraWiki
    VANWithURL -->|No| ActivateVAN[Kích hoạt VAN]

    AIAnalyze --> TaskAction{Loại task?}
    TaskAction -->|Fix bug| FixTask[Thực hiện sửa lỗi]
    TaskAction -->|Feature| FeatureTask[Phát triển tính năng]
    TaskAction -->|Analysis| AnalysisTask[Phân tích yêu cầu]

    DisplayResults --> ActivateVAN
```

### Giải Pháp Style

1. **Ant Design**

   - Chủ đề component
   - Thiết kế responsive
   - Khả năng tiếp cận
   - Components tùy chỉnh

2. **Tailwind CSS**
   - Lớp tiện ích
   - CSS trong JS
   - Style động
   - Hệ thống chủ đề
   - **KHÔNG sử dụng file CSS riêng biệt (.css)**

### Quy tắc Phát triển

1. **Code Quality**

   - Đảm bảo không có lỗi ESLint (yarn lint)
   - Đảm bảo không có lỗi TypeScript (yarn tsc --noEmit)
   - Đảm bảo không có lỗi compile (thiếu directive "use client" cho client components)
   - Đảm bảo không có lỗi runtime
   - Thêm key prop cho các phần tử trong array
   - Không để biến được khai báo nhưng không sử dụng

2. **Client Components**
   - Thêm directive "use client" ở đầu file cho các components sử dụng React hooks
   - Đảm bảo phân biệt rõ ràng giữa Server Components và Client Components
   - Tránh lỗi "You're importing a component that needs useState. This React hook only works in a client component"

## Đường Dẫn Triển Khai

### Xác Thực

1. **Tích Hợp Firebase**

   - Xác thực mạng xã hội
   - Email/mật khẩu
   - Quản lý token
   - Quản lý phiên

2. **Phân Quyền**
   - Truy cập dựa trên vai trò
   - Hệ thống quyền
   - Routes được bảo vệ
   - Phân quyền API

### Hiệu Suất

1. **Kỹ Thuật Tối Ưu**

   - Chia tách mã
   - Tải trễ
   - Tối ưu hình ảnh
   - Tối ưu bundle

2. **Giám Sát**
   - Chỉ số hiệu suất
   - Theo dõi lỗi
   - Phân tích người dùng
   - Kiểm tra tải

### Chiến Lược Kiểm Thử

1. **Loại Kiểm Thử**

   - Kiểm thử đơn vị
   - Kiểm thử tích hợp
   - Kiểm thử E2E
   - Kiểm thử hiệu suất

2. **Triển Khai Kiểm Thử**
   - Tiện ích kiểm thử
   - Dữ liệu giả
   - Trình chạy kiểm thử
   - Tích hợp CI

# Mẫu Hệ Thống

## Kiến Trúc Hệ Thống

OneDX được xây dựng dựa trên một kiến trúc module hóa với Next.js 15.3.0 tại core. Các cổng thông tin khác nhau (SME, Đối Tác, IoT) được triển khai dưới dạng các ứng dụng riêng biệt nhưng có chung cơ sở mã. Dưới đây là các mẫu kiến trúc chính:

### Kiến Trúc Tổng Thể

1. **App Router của Next.js**

   - Sử dụng cấu trúc file-based routing
   - Tách biệt thư mục cho từng cổng thông tin
   - Tận dụng tính năng server và client components

2. **Phân Lớp Frontend**

   - **Presentation Layer**: Components và UI
   - **Business Logic Layer**: Services, Hooks, Redux Slices
   - **Data Access Layer**: API calls, các trình xử lý dữ liệu
   - **Routing**: Next.js App Router

3. **Trình Tạo Trang (Page Builder)**
   - Hệ thống động cho phép tạo và tùy chỉnh trang
   - Các components có thể được cấu hình và sắp xếp trên giao diện
   - Hệ thống fields tùy chỉnh

## Message Command Processor (MCP)

MCP là một thành phần quan trọng giúp xử lý các yêu cầu từ Jira, Wiki và Figma. Nó cho phép AI truy cập vào các nguồn thông tin bên ngoài.

### Kiến Trúc MCP

```
┌────────────────┐     ┌───────────────┐     ┌────────────────┐
│                │     │               │     │                │
│  Cursor Chat   │────▶│ MessageScanner│────▶│   MCP Server   │
│                │     │               │     │                │
└────────────────┘     └───────────────┘     └────────────────┘
                                                     │
                                                     ▼
                                             ┌────────────────┐
                                             │                │
                                             │ Jira/Wiki/Figma│
                                             │                │
                                             └────────────────┘
```

1. **Core Components**

   - **mcp_core.js**: Xử lý chính cho Jira/Wiki
   - **mcp.js**: CLI và entry point
   - **.env**: Lưu trữ thông tin đăng nhập và cấu hình
   - **messageScanner.js**: Quét tin nhắn và kích hoạt MCP

2. **Luồng Xử Lý Tự Động**

   - Tin nhắn người dùng được quét bởi MessageScanner
   - URL và mã vấn đề (Jira, Wiki, Figma) được tự động phát hiện
   - Thông tin được trích xuất và hiển thị mà không cần lệnh đặc biệt
   - Khi gặp lệnh VAN, thông tin vẫn được xử lý trước khi kích hoạt chế độ VAN

3. **Tích Hợp Cursor**

   - Cấu hình MCP trong `.cursor/mcp.json`
   - Theo dõi tin nhắn qua `cursor.onMessageSent()`
   - Xử lý tin nhắn trả về qua `cursor.showMessageNotification()`

4. **Xử Lý API Bên Ngoài**
   - Kết nối đến Jira API
   - Kết nối đến Wiki API
   - Kết nối đến Figma API

## Quyết Định Kỹ Thuật Chính

1. **Next.js App Router**

   - Cung cấp routing linh hoạt cho các cổng khác nhau
   - Hỗ trợ SSR và CSR theo yêu cầu

2. **Module MCP**

   - Hệ thống xử lý tin nhắn riêng biệt để tích hợp với các dịch vụ bên ngoài
   - Tự động xử lý các URL và mã Jira, Wiki, Figma trong tin nhắn

3. **Redux cho Quản Lý Trạng Thái**

   - Quản lý trạng thái toàn cục
   - Nhiều cửa hàng cho các tính năng khác nhau

4. **Styled Components & Emotion**

   - Styling có khả năng tích hợp cao
   - Hỗ trợ theming

5. **Custom Fields System**
   - Cho phép tùy chỉnh trường trong form
   - Hỗ trợ nhiều loại dữ liệu

## Mối Quan Hệ Thành Phần

### Cấu Trúc Thư Mục

```
app/                   # Next.js app router
├── (sme-portal)/      # SME Portal
├── (partner-portal)/  # Partner Portal
├── (iot-portal)/      # IoT Portal
.mcp/                  # Message Command Processor
├── mcp_core.js        # Core MCP functionality
├── mcp.js             # CLI và entry point
components/            # Shared components
├── auth/              # Authentication components
├── common/            # Common UI components
.cursor/               # Cursor integration
├── mcp.json           # MCP configuration
├── rules/             # Cursor rules
  ├── messageScanner.js # Auto URL processor
```

### Luồng MCP

```
           ┌───────────────────────────────────────────────────┐
           │                                                   │
           │                                                   ▼
┌───────────────────┐    ┌───────────────────┐    ┌───────────────────┐
│                   │    │                   │    │                   │
│   User Message    │───▶│  MessageScanner   │───▶│   URL Detection   │
│                   │    │                   │    │                   │
└───────────────────┘    └───────────────────┘    └───────────────────┘
                                                           │
                                                           │
                                                           ▼
┌───────────────────┐    ┌───────────────────┐    ┌───────────────────┐
│                   │    │                   │    │                   │
│  Display Results  │◀───│    MCP Server     │◀───│  API Processing   │
│                   │    │                   │    │                   │
└───────────────────┘    └───────────────────┘    └───────────────────┘
```

### MCP Tự Động Hóa

```
┌────────────────────────────────────┐
│                                    │
│         Tin nhắn người dùng        │
│                                    │
└───────────────┬────────────────────┘
                │
                ▼
┌────────────────────────────────────┐
│                                    │
│     messageScanner.js tự động      │
│     quét URL và mã vấn đề          │
│                                    │
└───────────────┬────────────────────┘
                │
                ▼
┌────────────────────────────────────┐
│    ┌─────────────────────────┐     │
│    │                         │     │
│    │   Phát hiện URL Jira    │     │
│    │                         │     │
│    └─────────────────────────┘     │
│                                    │
│    ┌─────────────────────────┐     │
│    │                         │     │
│    │   Phát hiện URL Wiki    │     │
│    │                         │     │
│    └─────────────────────────┘     │
│                                    │
│    ┌─────────────────────────┐     │
│    │                         │     │
│    │   Phát hiện URL Figma   │     │
│    │                         │     │
│    └─────────────────────────┘     │
│                                    │
│    ┌─────────────────────────┐     │
│    │                         │     │
│    │   Phát hiện mã Jira     │     │
│    │                         │     │
│    └─────────────────────────┘     │
└───────────────┬────────────────────┘
                │
                ▼
┌────────────────────────────────────┐
│                                    │
│    MCP xử lý yêu cầu thông qua     │
│    mcp_core.js                     │
│                                    │
└───────────────┬────────────────────┘
                │
                ▼
┌────────────────────────────────────┐
│                                    │
│  Hiển thị kết quả trong chat       │
│                                    │
└────────────────────────────────────┘
```

## Các Con Đường Triển Khai Quan Trọng

1. **Đăng Nhập và Xác Thực**

   - Views/auth cho các màn hình đăng nhập
   - Bảo vệ route thông qua higher-order components

2. **Tạo Trang (Page Builder)**

   - Editor: cho phép tùy chỉnh giao diện
   - Preview: xem trước các thay đổi
   - Selectors: các thành phần có thể được thêm vào trang

3. **Thanh Toán**

   - Kiểm tra giỏ hàng -> Thông tin người dùng -> Phương thức thanh toán -> Xác nhận

4. **Tích Hợp MCP**
   - Cấu hình MCP -> Xử lý tin nhắn -> Phát hiện URL/mã -> Hiển thị thông tin
   - Tự động quét tất cả tin nhắn mà không cần lệnh đặc biệt

## Các Mẫu Phổ Biến

1. **Atomic Design**

   - Atoms: Các components nhỏ nhất (Button, Input)
   - Molecules: Tổ hợp components (Form Fields)
   - Organisms: Components phức tạp (Navigation, Cards)
   - Templates: Layout cho các trang
   - Pages: Trang hoàn chỉnh

2. **Container/Presentational Pattern**

   - Container: Xử lý logic
   - Presentational: Hiển thị giao diện

3. **Render Props & Higher-Order Components**

   - Sử dụng cho logic chia sẻ

4. **Custom Hooks**

   - Trích xuất logic có thể tái sử dụng

5. **Messenger Scanner Pattern**
   - Tự động quét tin nhắn
   - Xử lý mà không cần sự can thiệp trực tiếp
   - Hiển thị kết quả ngay trên giao diện

# Mô hình hệ thống

## Kiến trúc tổng quan

Dự án OneX Frontend được xây dựng dựa trên Next.js với kiến trúc phân lớp rõ ràng:

1. **Presentation Layer**: Các components UI và pages

   - Layout components
   - Shared components
   - Page-specific components

2. **State Management Layer**: Redux và Context API

   - Redux store với Redux Toolkit
   - React Context cho trạng thái cục bộ

3. **Services Layer**: Xử lý tương tác với API

   - API clients
   - Các service modules

4. **Utils Layer**: Các tiện ích và hàm hỗ trợ

## Cấu trúc dự án

Dự án được tổ chức theo cấu trúc thư mục sau:

- `/src`: Mã nguồn chính
  - `/@core`: Components và utilities cốt lõi
  - `/@layouts`: Layouts chung
  - `/@menu`: Components và logic cho menu
  - `/app`: Pages và components theo tính năng
  - `/components`: Shared components
  - `/configs`: Cấu hình
  - `/constants`: Hằng số
  - `/redux-store`: Redux store và slices
  - `/services`: Services cho API calls
  - `/utils`: Utilities functions
  - `/views`: UI views theo tính năng

## Quy trình hoạt động

1. **Routing**: Sử dụng Next.js App Router
2. **Authentication**: Hệ thống authentication tùy chỉnh
3. **State Management**: Redux, React Context
4. **API Integration**: Axios, React Query
5. **UI Rendering**: Material UI, Tailwind CSS

## Mô hình dữ liệu

Redux store được tổ chức theo slices, mỗi slice quản lý một phần trạng thái của ứng dụng.

# System Patterns

## Kiến trúc hệ thống

- Dự án sử dụng Next.js làm framework chính
- Sử dụng TypeScript cho type safety
- Ant Design và Tailwind CSS cho UI components
- Tổ chức code theo mô hình component-based

## Quy tắc ưu tiên

- **Nhận diện đúng vai trò của AI:** AI là Cursor + Memory Bank, KHÔNG PHẢI bất kỳ model nào cụ thể. PHẢI luôn xem bản thân là Cursor + Memory Bank trong mọi tương tác.
- **Tự động kích hoạt memory bank:** PHẢI tự động đọc memory bank cho MỌI tin nhắn của người dùng, không chỉ những tin nhắn bắt đầu bằng "VAN:". Đọc memory bank là bước KHÔNG THỂ BỎ QUA và PHẢI LUÔN được thực hiện tự động khi bắt đầu phiên trò chuyện mới.
- **Ưu tiên sửa rule memory bank và cursor rules:** Khi có yêu cầu sửa rule, phải đặt việc này lên ưu tiên hàng đầu trước khi thực hiện các tác vụ khác. PHẢI kiểm tra và cập nhật CẢ HAI nơi: memory bank và cursor rules nếu cần thiết. **TUYỆT ĐỐI KHÔNG** được chỉ cập nhật một nơi mà bỏ qua nơi còn lại.
- **Đọc cả cursor rules và memory bank:** Khi nhận lệnh VAN, PHẢI đọc cả cursor rules và memory bank theo đúng thứ tự: đọc cursor rules trước, sau đó mới đọc memory bank. **TUYỆT ĐỐI KHÔNG ĐƯỢC BỎ SÓT** việc đọc bất kỳ rule nào.
- **Không sử dụng file CSS riêng biệt:** Chỉ sử dụng Tailwind CSS và Ant Design cho styling, không tạo file CSS riêng biệt
- **Tự động sửa lỗi lint và type checking:** Luôn đảm bảo code không có lỗi lint (yarn lint) và type checking (yarn tsc --noEmit) trước khi hoàn thành nhiệm vụ
- **Phân tích lỗi trước khi sửa code:** Luôn phân tích kỹ lưỡng lỗi và đề xuất giải pháp trước, chờ phê duyệt mới sửa code
- **Tuân thủ thiết kế Figma 100%:** Khi triển khai giao diện từ thiết kế Figma, phải đảm bảo tuân thủ thiết kế 100% về bố cục, màu sắc, font chữ, kích thước, khoảng cách và các chi tiết khác. **TUYỆT ĐỐI KHÔNG** được tự ý thay đổi bố cục hoặc bất kỳ phần nào của thiết kế. **BỐ CỤC PHẢI GIỐNG HỆT** thiết kế Figma, không được phép thay đổi vị trí, thứ tự hoặc cách sắp xếp các phần tử.

## Styling Patterns

- **Không sử dụng file CSS riêng biệt (.css):** Chỉ sử dụng Tailwind CSS và Ant Design cho styling
- Khi cần style phức tạp, ưu tiên sử dụng styled-components hoặc CSS-in-JS
- Sử dụng Tailwind CSS cho các style tùy chỉnh
- Tuân thủ design system và theme colors đã định nghĩa
- Đảm bảo responsive trên tất cả các thiết bị
- **Tuân thủ thiết kế Figma 100%:** Đảm bảo triển khai giao diện chính xác theo thiết kế Figma, bao gồm:
  - **BỐ CỤC PHẢI GIỐNG HỆT thiết kế Figma** - Không được thay đổi bố cục, vị trí, thứ tự hoặc cách sắp xếp các phần tử
  - Màu sắc chính xác theo mã màu trong thiết kế
  - Font chữ đúng với font family, font size, font weight và line height
  - Kích thước các phần tử chính xác đến từng pixel
  - Khoảng cách (margin, padding) đúng như thiết kế
  - Hiệu ứng (shadow, gradient, opacity) phải được triển khai giống hệt thiết kế
  - Responsive behavior phải tuân theo các breakpoint đã định nghĩa

## Code Quality Patterns

- **Đảm bảo không có lỗi ESLint:** Chạy lệnh `yarn lint` để kiểm tra và sửa lỗi
- **Đảm bảo không có lỗi TypeScript:** Chạy lệnh `yarn tsc --noEmit` để kiểm tra và sửa lỗi
- **React Key Props:** Luôn thêm key prop cho các phần tử trong array
- **Unused Variables:** Không để biến được khai báo nhưng không sử dụng
- **Consistent Naming:** Sử dụng camelCase cho biến và hàm, PascalCase cho component và interface
- **Type Safety:** Sử dụng TypeScript interface cho props và state

## Component Patterns

- Tạo các components có thể tái sử dụng
- Tách biệt logic và UI
- Sử dụng React hooks cho state management
- Props validation thông qua TypeScript interfaces
- Sử dụng Context API cho state sharing giữa các components

## API Integration Patterns

- Sử dụng axios cho API calls
- Tách biệt logic API thành các services riêng
- Xử lý lỗi và loading states một cách nhất quán
- Sử dụng React Query cho data fetching và caching

## Routing Patterns

- Sử dụng Next.js App Router
- Tổ chức routes theo cấu trúc thư mục
- Lazy loading các components lớn để tối ưu hiệu suất

## State Management Patterns

- Sử dụng React hooks (useState, useReducer) cho local state
- Sử dụng Context API cho global state
- Redux cho state management phức tạp (nếu cần)

## Form Handling Patterns

- Sử dụng Ant Design Form components
- Form validation với Yup hoặc Zod
- Xử lý form submission và errors một cách nhất quán

## Error Handling Patterns

- Sử dụng try/catch cho async operations
- Hiển thị error messages thông qua Ant Design message hoặc notification
- Logging errors để debugging

## Testing Patterns

- Unit tests với Jest và React Testing Library
- E2E tests với Cypress
- Component tests với Storybook

# Cấu trúc hệ thống

## Kiến trúc tổng thể

OneSME Portal được xây dựng dựa trên Next.js với App Router, sử dụng TypeScript, Ant Design và Tailwind CSS.

## Cấu trúc thư mục và tổ chức code

### Quy tắc tổ chức code

Dự án tuân theo cấu trúc tổ chức code rõ ràng, phân tách các thành phần theo chức năng và trách nhiệm:

1. **src/app**: Chứa các trang Next.js (pages) và routing

   - Mỗi trang chỉ nên import và sử dụng các view tương ứng
   - Không nên chứa logic nghiệp vụ phức tạp
   - Ví dụ: `src/app/(sme-portal)/sitemap/page.tsx`

2. **src/views**: Chứa các màn hình hoặc trang với logic nghiệp vụ cụ thể

   - Mỗi view đại diện cho một trang hoặc một phần chính của trang
   - Chứa logic nghiệp vụ và tương tác với API
   - Có thể sử dụng các components để tạo giao diện
   - Ví dụ: `src/views/sitemap/SiteMap.tsx`

3. **src/components**: Chứa các thành phần UI có thể tái sử dụng
   - Các component nên được thiết kế để có thể tái sử dụng
   - Không nên chứa logic nghiệp vụ cụ thể
   - Nên phân tách theo chức năng hoặc tính năng
   - Ví dụ: `src/components/form/CustomInput.tsx`

### Quy tắc đặt tên và tổ chức file

1. **Tên file và thư mục**:

   - Sử dụng PascalCase cho tên component và view: `WarrantyLookup.tsx`
   - Sử dụng kebab-case cho tên thư mục: `warranty-lookup`
   - File index.ts được sử dụng để export các thành phần trong thư mục

2. **Cấu trúc thư mục**:
   - Mỗi tính năng nên có cấu trúc thư mục tương ứng trong cả `app`, `views` và `components` (nếu cần)
   - Các component dùng chung nên được đặt trong thư mục riêng theo chức năng

### Quy trình tạo tính năng mới

Khi tạo một tính năng mới, cần tuân thủ các bước sau:

1. **Tạo View**:

   - Tạo file trong thư mục `src/views/[feature-name]`
   - Đặt tên file theo PascalCase: `FeatureName.tsx`

2. **Tạo Page**:

   - Tạo file trong thư mục `src/app/[route]/page.tsx`
   - Import view đã tạo và sử dụng trong page

3. **Tạo Components (nếu cần)**:
   ```
   src/components/warranty-lookup/WarrantyForm.tsx
   src/components/warranty-lookup/WarrantyResults.tsx
   ```

## Công nghệ và thư viện chính

- **Framework**: Next.js với App Router
- **Ngôn ngữ**: TypeScript
- **UI Libraries**: Ant Design, Tailwind CSS
- **State Management**: Redux Toolkit
- **API Client**: Axios, React Query
- **Form Management**: Ant Design Form

## Quy tắc code style

- Sử dụng ESLint và Prettier để đảm bảo code style nhất quán
- Sử dụng TypeScript để đảm bảo type safety
- Sử dụng các hooks của React để quản lý state và side effects
- Tách biệt logic nghiệp vụ và UI để dễ bảo trì và mở rộng

## Quy tắc quản lý state

- Sử dụng Redux Toolkit cho global state
- Sử dụng React Query cho server state
- Sử dụng React Context cho state cần chia sẻ giữa các components trong một tính năng
- Sử dụng local state (useState) cho state chỉ sử dụng trong một component

## Quy tắc phát triển frontend (bổ sung)

- Khi tạo mới giao diện từ Figma (khi không chỉ định rõ page hứng hoặc url), AI PHẢI tự động tạo page Next.js để route/hứng component, đúng chuẩn cấu trúc dự án.
- Quy tắc fix warning/error: BẤT KỲ warning hoặc error nào (linter, type, runtime, compile) đều PHẢI fix sạch 100% trước khi báo hoàn thành, kể cả warning nhỏ nhất. KHÔNG ĐƯỢC báo hoàn thành khi còn warning/error. Luôn kiểm tra lại bằng yarn lint, yarn tsc --noEmit và kiểm tra runtime trước khi báo hoàn thành.
- View/page KHÔNG được lặp lại Header/Footer/ContactSidebar nếu đã có ở layout, chỉ render phần nội dung đặc thù.
- Khi sửa rule PHẢI cập nhật đồng thời cả memory bank và cursor rules, không được bỏ sót nơi nào.

# Quy tắc tổ chức code FE (Frontend)

## 1. Component nhỏ, tái sử dụng

- Đặt trong: `src/components/[module]/[feature]/` hoặc `src/components/[module]/`
- VD: `src/components/iot-portal/cart/CartSheet.tsx`

## 2. View tổng hợp (logic, layout, truyền props, xử lý state, css tổng thể)

- Đặt trong: `src/views/[module]/[feature]/[ViewName].tsx`
- VD: `src/views/iot-portal/cart/CartView.tsx`

## 3. Page (route) chỉ gọi đúng 1 view, không xử lý gì thêm

- Đặt trong: `src/app/(module)/[module]/[feature]/page.tsx`
- VD: `src/app/(iot-portal)/iot-portal/cart/page.tsx` chỉ gọi `CartView`

## 4. Không tạo file trung gian nếu không có logic đặc biệt

---

## Cấu trúc mẫu

```
src/
  components/
    iot-portal/
      cart/
        CartSheet.tsx
  views/
    iot-portal/
      cart/
        CartView.tsx
  app/
    (iot-portal)/
      iot-portal/
        cart/
          page.tsx   // chỉ gọi <CartView />
```

---

## Quy trình tổng quát

1. Tách giao diện thành các component nhỏ, để trong `src/components`.
2. Tổng hợp các component trong view, xử lý layout, logic, css tại `src/views`.
3. Page chỉ gọi view, không xử lý gì thêm.
4. Không tạo file trung gian nếu không có logic đặc biệt.

**Tất cả thành viên dự án phải tuân thủ quy tắc này khi phát triển FE.**

## Quy tắc tổ chức code FE (Frontend) - BỔ SUNG

- Nếu là tính năng đặc thù của một module (ví dụ: iot-portal, sme-portal, partner-portal...), **components, views phải để trong thư mục module đó**:
  - `src/components/iot-portal/[feature]/...`
  - `src/views/iot-portal/[feature]/...`
  - KHÔNG tạo thư mục feature riêng ngoài module.
- Nếu là tính năng dùng chung cho nhiều module, mới đặt ngoài module.
- Nếu là logic đặc thù module, phải để đúng folder module.

#### Ví dụ minh họa:

```
src/
  components/
    iot-portal/
      partner-register/
        PartnerBanner.tsx
        PartnerRegisterForm.tsx
  views/
    iot-portal/
      partner-register/
        PartnerRegisterView.tsx
  app/
    (iot-portal)/
      iot-portal/
        partner/
          register/
            page.tsx
```

## Pattern sử dụng ảnh Figma cho UI

- Khi phát triển UI từ Figma, PHẢI tự động lấy ảnh từ Figma về, lưu đúng thư mục `public/assets/images/pages/[module]/[feature]/`.
- Đặt tên file ảnh rõ ràng, không dấu, không khoảng trắng.
- UI chỉ được dùng ảnh local đã lấy từ Figma, không dùng ảnh ngoài.
- Nếu ảnh trên Figma thay đổi, PHẢI cập nhật lại ảnh local tương ứng.
