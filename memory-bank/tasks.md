# Theo <PERSON>õi <PERSON>hiệ<PERSON> Vụ

## Nhiệm V<PERSON>ang Thực Hiện

1. [ĐANG THỰC HIỆN] Sửa lỗi SPC_BOSPLATFORM-368: Không hiển thị chiến lược định giá mặc định trong Popup biểu giá tạm tính

   - [x] Phân tích yêu cầu từ Jira
   - [x] Xác định vấn đề trong mã nguồn
   - [x] Đề xuất giải pháp sửa lỗi
   - [ ] Triển khai sửa lỗi sau khi được xác nhận
   - [ ] Kiểm thử
   - [ ] Cập nhật tài liệu

2. [ĐANG THỰC HIỆN] Phát triển tính năng thiết lập quy tắc thông báo tài khoản bị lộ mật khẩu

   - [x] Phân tích yêu cầu từ file PDF
   - [x] Tạo tài liệu phân tích yêu cầu và giải pháp thi công
   - [ ] <PERSON><PERSON><PERSON> định cấu trúc UI cho chức năng automation rules
   - [ ] Thiết kế giao diện thêm quy tắc thông báo tài khoản bị lộ mật khẩu
   - [ ] Tích hợp API lấy danh sách tài khoản bị lộ mật khẩu
   - [ ] Xử lý dữ liệu từ API để đánh dấu tài khoản bị lộ mật khẩu
   - [ ] Thiết kế UI hiển thị danh sách tài khoản bị lộ mật khẩu
   - [ ] Cải thiện trải nghiệm người dùng với thông tin chi tiết

3. [ĐANG THỰC HIỆN] Phân tích kiến trúc mã nguồn

   - [x] Xem xét cấu trúc thư mục dự án
   - [x] Đánh giá cấu trúc component chính
   - [x] Kiểm tra các Redux slices
   - [x] Phân tích chi tiết các custom hooks
   - [x] Tìm hiểu kiến trúc multi-portal
   - [x] Nghiên cứu hệ thống Page Builder
   - [x] Phân tích cơ chế tích hợp API

4. [HOÀN THÀNH] Tự động phát hiện URL và kết nối với Jira/Wiki

   - [x] Phân tích cấu trúc hiện tại của MCP Server
   - [x] Tạo file cursorHook.js để tự động quét tin nhắn
   - [x] Cập nhật mcp.js để tích hợp với cursorHook
   - [x] Tạo file cấu hình mcp_auto_detect.json
   - [x] Tạo hook cho Cursor để tự động phát hiện URL
   - [x] Cập nhật file cấu hình mcp.json
   - [x] Tạo file cấu hình settings.json cho Cursor
   - [x] Kiểm tra và cập nhật tài liệu

5. [ĐANG THỰC HIỆN] Tài liệu hóa luồng dữ liệu

   - [x] Ghi chép luồng trạng thái Redux
   - [x] Mô tả tương tác React Query
   - [x] Tài liệu hóa các API endpoints
   - [x] Phân tích luồng xác thực
   - [ ] Tài liệu hóa luồng dữ liệu giữa các portal

6. [ĐANG THỰC HIỆN] Nâng cấp và tài liệu hóa MCP Server

   - [x] Cập nhật hướng dẫn auto_mcp_trigger để bổ sung thông tin về troubleshooting
   - [x] Bổ sung thông tin khởi động tự động MCP trong activeContext.md
   - [x] Cấu hình MessageScanner để tự động phát hiện và khởi động MCP Server CHÍNH KHI phát hiện URL hoặc mã Jira/Wiki
   - [x] Cập nhật cấu hình trong .cursor/mcp.json để tự động xử lý URL
   - [x] Xóa bỏ file messageScanner.js trùng lặp để tránh xung đột
   - [x] Cấu hình MCP Server để tự động khởi động ngay khi phát hiện URL, không cần bất kỳ thao tác thủ công nào
   - [ ] Tối ưu hóa messageScanner để phát hiện tốt hơn các URL và mã Jira
   - [ ] Cải thiện tích hợp với Cursor để xử lý URL tốt hơn
   - [ ] Phát triển cơ chế ghi nhớ các URL đã quét để tránh quét lại
   - [x] Thêm tùy chọn --attachments để lấy danh sách file đính kèm từ Jira
   - [x] Thêm tùy chọn download để tải nội dung file đính kèm
   - [x] Cập nhật activeContext.md với quy tắc phân tích ảnh và file đính kèm
   - [ ] Cải thiện định dạng hiển thị kết quả phân tích file đính kèm

7. [ĐANG THỰC HIỆN] Tài liệu hóa quy trình phát triển

   - [ ] Tài liệu hóa quy trình build và triển khai
   - [ ] Tài liệu hóa phương pháp kiểm thử
   - [ ] Tài liệu hóa tiêu chuẩn chất lượng mã
   - [ ] Tài liệu hóa hướng dẫn hợp tác
   - [x] Xây dựng quy trình đọc và hiểu, trình bày lại giải pháp thi công
   - [x] Thiết lập quy trình phân tích code trước khi bắt đầu code
   - [x] Xây dựng quy trình viết unit test tự động
   - [x] Tài liệu hóa quy trình tự test và fix bug
   - [x] Thiết lập cơ chế tự động thực hiện tất cả các quy trình phát triển

8. [HOÀN THÀNH] Cập nhật quy tắc MCP Server để tự động chạy khi gặp link wiki/jira

- [x] Thêm quy tắc rõ ràng về việc tự động chạy MCP Server khi gặp link wiki hoặc Jira trong phần "Quy tắc và ràng buộc" trong activeContext.md
- [x] Cập nhật phần "MCP Server tự động phát hiện và kết nối với các nguồn thông tin bên ngoài" để làm rõ hơn về quy trình tự động
- [x] Thêm chi tiết về việc tự động chạy MCP Server ngay lập tức khi phát hiện URL hoặc mã Jira/Wiki
- [x] Thêm hướng dẫn về việc tự động thử lại với các đường dẫn khác nhau nếu lệnh không thành công
- [x] Cập nhật tasks.md để ghi lại nhiệm vụ đã hoàn thành
- [x] Cập nhật progress.md để ghi lại tiến độ mới

10. [HOÀN THÀNH] Sửa lỗi xử lý URL Wiki có chứa fragment (#)

- [x] Phân tích lỗi trong MCP Server khi xử lý URL Wiki có chứa fragment (#)
- [x] Xác định vấn đề trong hàm extractWikiPageIdFromURL trong file scanner.js
- [x] Sửa hàm extractWikiPageIdFromURL để loại bỏ fragment (#) từ URL trước khi trích xuất pageId
- [x] Kiểm tra lại việc xử lý URL Wiki có chứa fragment
- [x] Cập nhật activeContext.md để ghi lại thay đổi
- [x] Cập nhật tasks.md để ghi lại nhiệm vụ đã hoàn thành

11. [ĐANG THỰC HIỆN] Cải thiện MCP Server để phân tích file đính kèm từ Jira

- [x] Thêm tùy chọn --attachments vào index.js để lấy danh sách file đính kèm
- [x] Thêm tùy chọn download để tải nội dung file đính kèm
- [x] Cập nhật hàm showHelp() với các tùy chọn mới
- [x] Cập nhật activeContext.md với quy tắc phân tích ảnh và file đính kèm
- [x] Cải thiện lệnh jira để tự động lấy đầy đủ thông tin bao gồm cả đính kèm và bình luận
- [x] Thêm hàm fetchComments để lấy danh sách bình luận từ Jira
- [x] Thêm hàm getIssueWithDetails để lấy toàn bộ thông tin vấn đề Jira
- [x] Định dạng hiển thị kết quả phân tích file đính kèm cùng với thông tin vấn đề
- [ ] Thêm chức năng trích xuất text từ ảnh (OCR) cho file ảnh
- [ ] Cải thiện cơ chế cache cho file đính kèm đã tải

12. [HOÀN THÀNH] Cập nhật quy tắc xử lý tự động trang Wiki VNPT Technology

- [x] Cập nhật quy tắc trong activeContext.md để thêm hướng dẫn xử lý trang Wiki một cách chủ động
- [x] Thêm quy tắc về việc tự động tìm kiếm trong codebase để xác định file liên quan
- [x] Thêm quy tắc về việc đưa ra phân tích và đề xuất triển khai sau khi có thông tin
- [x] Bổ sung hướng dẫn sử dụng tham số --full, --content, --body khi truy cập trang Wiki
- [x] Cập nhật quy tắc về việc phân tích cấu trúc nội dung Wiki và file đính kèm
- [x] Thêm quy tắc về việc tự động phân tích khi nhận lệnh "VAN: phân tích" với URL Wiki

13. [HOÀN THÀNH] Tối ưu hóa quy tắc xử lý Wiki/Jira và giảm thiểu số lượng lệnh

- [x] Cập nhật quy tắc trong activeContext.md để làm rõ việc không chạy nhiều lệnh liên tiếp khi xử lý Wiki/Jira
- [x] Thêm hướng dẫn sử dụng một lệnh duy nhất có đủ thông tin (--full cho Wiki và lệnh cơ bản cho Jira)
- [x] Làm rõ quy tắc về việc phân tích ngay sau khi có thông tin đầy đủ
- [x] Thêm hướng dẫn về việc xử lý ảnh đính kèm một cách hiệu quả
- [x] Cập nhật quy tắc trong để ưu tiên phân tích nội dung văn bản trước khi tải xuống ảnh

14. [ĐANG THỰC HIỆN] Cải thiện tích hợp Figma và MCP Server

- [x] Kiểm tra và cải thiện lệnh figma trong MCP Server
- [x] Thử nghiệm kết nối với Figma API
- [x] Cập nhật hàm handleFigmaCommand để xử lý đúng các tham số
- [x] Thêm tùy chọn --node-id hoặc -n để trích xuất thông tin chi tiết về node cụ thể
- [x] Cập nhật function extractFigmaIdsFromUrl để hỗ trợ nhiều định dạng URL Figma
- [ ] Thêm tích hợp với Figma REST API để trích xuất thông tin thiết kế
- [ ] Cải thiện xử lý và hiển thị kết quả từ Figma API
- [ ] Thêm tùy chọn tải xuống ảnh từ thiết kế Figma
- [ ] Thêm tùy chọn lưu kết quả vào file JSON để sử dụng sau

15. [ĐANG THỰC HIỆN] Cập nhật quy tắc tạo giao diện từ thiết kế Figma

- [x] Cập nhật activeContext.md với quy tắc không vẽ giao diện nếu không kết nối được Figma API
- [x] Thêm hướng dẫn về việc kiểm tra khả năng kết nối với Figma API trước khi tạo giao diện
- [ ] Thêm quy tắc về việc sử dụng Ant Design và Tailwind CSS khi tạo giao diện từ Figma
- [ ] Bổ sung hướng dẫn phân tích và chuyển đổi thiết kế Figma thành components
- [ ] Cập nhật hướng dẫn về việc tuân thủ thiết kế và giữ tính nhất quán
- [ ] Thêm quy tắc về việc sử dụng hệ thống Page Builder để tạo các trang mới
- [ ] Thêm quy tắc về việc sử dụng các utils và hooks có sẵn trong dự án
- [ ] Cập nhật tasks.md để ghi lại nhiệm vụ đã hoàn thành
- [ ] Cập nhật progress.md để ghi lại tiến độ mới

## Nhiệm Vụ Đã Hoàn Thành

1. [HOÀN THÀNH] Cập nhật quy tắc đọc memory bank trong mcp_rules.mdc

   - [x] Thêm phần "LUÔN ĐỌC MEMORY BANK KHI NHẬN LỆNH VAN:" vào mcp_rules.mdc
   - [x] Thêm quy tắc bắt buộc về việc đọc memory bank trước khi thực hiện bất kỳ hành động nào
   - [x] Cập nhật phần "Quy trình VAN với MCP" để nhấn mạnh rằng đọc memory bank là bước đầu tiên
   - [x] Thêm phần "TẤT CẢ QUY TẮC ĐỀU TỰ ĐỘNG ÁP DỤNG" để nhấn mạnh rằng không cần nhắc nhở
   - [x] Thêm chi tiết về trách nhiệm chủ động áp dụng quy tắc thuộc về AI
   - [x] Đảm bảo quy tắc này được thực hiện tự động mà không cần nhắc nhở từ người dùng
   - [x] Nhấn mạnh việc đọc memory bank là bước KHÔNG THỂ BỎ QUA trong mọi tình huống

2. [HOÀN THÀNH] Sửa lỗi MCP Server không thể xử lý lệnh wiki và jira

   - [x] Phân tích lỗi trong file commandHandler.js
   - [x] Sửa hàm handleWikiCommand để gọi đúng đến processWikiPage
   - [x] Sửa hàm processCommandLine để xử lý lệnh wiki đúng cách
   - [x] Xác định cách chạy lệnh wiki đúng từ thư mục gốc dự án
   - [x] Cập nhật README.md của MCP Server với hướng dẫn rõ ràng
   - [x] Thêm quy tắc nhắc nhở về việc đọc memory bank khi bắt đầu chat với VAN
   - [x] Thử nghiệm thành công lệnh wiki với URL thực tế
   - [x] Cập nhật memory bank để ghi lại thay đổi và bài học

3. [HOÀN THÀNH] Tích hợp hiển thị thiết kế từ Figma

   - [x] Phân tích yêu cầu tích hợp Figma
   - [x] Khảo sát cấu trúc hiện tại liên quan đến Figma
   - [x] Đơn giản hóa cấu hình Figma, chỉ dùng 1 phương pháp thay vì nhiều cách
   - [x] Loại bỏ file không cần thiết (figmaLauncher.js và các file demo-figma)
   - [x] Xóa file `.cursor/mcp.json` không cần thiết vì đã tích hợp trực tiếp vào MCP Server
   - [x] Cập nhật `.mcp/config/config.js` để sử dụng biến môi trường thay vì hardcode API key
   - [x] Cải thiện hàm handleFigmaCommand để xử lý tham số tốt hơn và cải thiện UX
   - [x] Nâng cấp hàm extractFigmaIdsFromUrl để hỗ trợ nhiều định dạng URL Figma hơn
   - [x] Thử nghiệm kết nối thành công với URL Figma thực tế
   - [x] Thêm hướng dẫn sử dụng với các tham số mở rộng (nodeId, --images, --complete, --save)
   - [x] Cập nhật memory bank về trạng thái tích hợp Figma

4. [HOÀN THÀNH] Khởi tạo và cấu trúc Memory Bank

   - Tạo file tasks.md
   - Cập nhật productContext.md với thông tin portal
   - Tăng cường techContext.md với thông số kỹ thuật chi tiết
   - Cập nhật activeContext.md với trọng tâm hiện tại
   - Cải thiện progress.md với trạng thái dự án chính xác
   - Đảm bảo cấu trúc Memory Bank toàn diện

5. [HOÀN THÀNH] Phân tích cơ sở hạ tầng API

   - Phân tích Base class và cơ chế API calls
   - Tài liệu hóa xử lý authentication
   - Mô tả cơ chế xử lý lỗi
   - Nghiên cứu mô hình tích hợp dữ liệu

6. [HOÀN THÀNH] Cập nhật quy tắc styling UI

   - Thêm quy tắc ưu tiên sử dụng Tailwind CSS cho styling
   - Thêm quy tắc ưu tiên sử dụng Ant Design cho components UI
   - Cập nhật activeContext.md với quy tắc mới
   - Cập nhật progress.md để phản ánh thay đổi

7. [HOÀN THÀNH] Triển khai MCP Server

   - [x] Xây dựng cấu trúc MCP Server trong thư mục .mcp/
   - [x] Phát triển mcp_core.js để xử lý các yêu cầu MCP
   - [x] Tạo mcp.js làm giao diện dòng lệnh và điểm nhập server
   - [x] Thiết lập quản lý cấu hình thông qua biến môi trường với dotenv
   - [x] Phát triển messageScanner.js để tự động phát hiện URL và lệnh
   - [x] Tích hợp với Cursor thông qua tệp .cursor/mcp.json
   - [x] Thêm hỗ trợ cho Jira, Wiki và Figma URLs
   - [x] Cài đặt hệ thống tự động xử lý và hiển thị kết quả trong chat
   - [x] Tích hợp trực tiếp với Cursor để hiển thị thông tin phân tích
   - [x] Tài liệu hóa MCP Server trong Memory Bank

8. [HOÀN THÀNH] Cập nhật quy tắc tự động xử lý URL

   - [x] Thêm quy tắc tự động xử lý URL Jira/Wiki/Figma vào Memory Bank
   - [x] Cập nhật activeContext.md với quy tắc tự động xử lý URL trực tiếp trong chat
   - [x] Tích hợp MCP Server với Cursor để xử lý và hiển thị kết quả phân tích ngay trong cuộc trò chuyện
   - [x] Thiết lập cơ chế tự động phân tích thông tin và thực hiện task từ thông tin phân tích
   - [x] Cập nhật tasks.md để phản ánh quy tắc mới
   - [x] Loại bỏ phần lưu logs và báo cáo không cần thiết

9. [HOÀN THÀNH] Cập nhật quy tắc xử lý giới hạn môi trường cho MCP Server

   - [x] Cập nhật quy tắc #9 trong activeContext.md để thêm hướng dẫn xử lý khi AI không thể truy cập URL
   - [x] Thêm chỉ dẫn rõ ràng cho AI thông báo giới hạn chính xác một lần
   - [x] Thêm hướng dẫn đề nghị người dùng cung cấp nội dung bug/yêu cầu một cách lịch sự
   - [x] Đảm bảo AI không yêu cầu xác nhận lại và tiếp tục quy trình VAN ngay khi có thông tin
   - [x] Cập nhật progress.md để phản ánh thay đổi trong quy tắc

10. [HOÀN THÀNH] Cậi tiến lệnh Wiki để tự động trả về thông tin đầy đủ mà không cần flag --full

- [x] Sửa file index.js để tự động thêm flag --full khi không có flag nào được chỉ định
- [x] Cập nhật hàm handleWikiPage trong mainHandler.js để hỗ trợ chức năng mới
- [x] Tái cấu trúc hàm getWikiPage trong api.js để xử lý các tham số hiệu quả hơn
- [x] Bổ sung các hàm getFullWikiPage, getWikiPageContent, getWikiPageBody vào wikiAPI.js
- [x] Cập nhật tài liệu README.md để phản ánh thay đổi mới
- [x] Cập nhật hàm showHelp() trong help.js để cập nhật hướng dẫn sử dụng
- [x] Cập nhật quy tắc trong activeContext.md để làm rõ không cần flag --full nữa
- [x] Thêm cập nhật mới vào progress.md để ghi lại thay đổi này

11. [HOÀN THÀNH] Cập nhật quy tắc MCP Server để tự động chạy khi gặp link wiki/jira

- [x] Thêm quy tắc rõ ràng về việc tự động chạy MCP Server khi gặp link wiki hoặc Jira trong phần "Quy tắc và ràng buộc" trong activeContext.md
- [x] Cập nhật phần "MCP Server tự động phát hiện và kết nối với các nguồn thông tin bên ngoài" để làm rõ hơn về quy trình tự động
- [x] Thêm chi tiết về việc tự động chạy MCP Server ngay lập tức khi phát hiện URL hoặc mã Jira/Wiki
- [x] Thêm hướng dẫn về việc tự động thử lại với các đường dẫn khác nhau nếu lệnh không thành công
- [x] Cập nhật tasks.md để ghi lại nhiệm vụ đã hoàn thành
- [x] Cập nhật progress.md để ghi lại tiến độ mới

12. [HOÀN THÀNH] Cập nhật quy tắc truy cập MCP Server khi gặp link Jira/Wiki

- [x] Phân tích quy trình xử lý hiện tại khi gặp link Jira/Wiki
- [x] Cập nhật quy tắc trong activeContext.md để nhấn mạnh việc NGHIÊM CẤM sử dụng web_search khi chưa truy cập qua MCP Server
- [x] Thêm hướng dẫn chi tiết về việc ưu tiên sử dụng MCP Server khi nhận lệnh VAN với URL
- [x] Bổ sung quy tắc về việc LUÔN LUÔN sử dụng lệnh `node .mcp/index.js wiki URL` hoặc `node .mcp/index.js jira ISSUE_KEY`
- [x] Nhấn mạnh việc TUYỆT ĐỐI KHÔNG được phép sử dụng công cụ web_search khi chưa thử truy cập thông qua MCP Server
- [x] Cập nhật tasks.md để ghi lại nhiệm vụ đã hoàn thành

13. [HOÀN THÀNH] Gộp các quy tắc MCP Server vào một file duy nhất

- [x] Phân tích các quy tắc hiện có liên quan đến MCP Server
- [x] Kiểm tra các file mdc trong thư mục .cursor/rules
- [x] Gộp tất cả quy tắc vào file language.mdc duy nhất
- [x] Thêm quy tắc tự động kết nối MCP Server khi bắt đầu chat mới
- [x] Đảm bảo tất cả quy tắc về MCP Server được tuân thủ
- [x] Cập nhật tasks.md và progress.md để ghi lại thay đổi

14. [HOÀN THÀNH] Tối ưu hóa cấu trúc quy tắc MCP Server và loại bỏ file không cần thiết

- [x] Đổi tên file language.mdc thành mcp_rules.mdc để đặt tên chuẩn hơn
- [x] Tách quy tắc ngôn ngữ ra file required_rules.mdc riêng biệt
- [x] Xóa file messageScanner.js không cần thiết
- [x] Đơn giản hóa cấu hình trong file mcp.json
- [x] Loại bỏ tham chiếu đến messageScanner.js trong mcp.json
- [x] Cập nhật tasks.md và progress.md để ghi lại thay đổi

15. [HOÀN THÀNH] Cập nhật quy tắc MCP Server để chỉ chạy một lệnh duy nhất

- [x] Cập nhật quy tắc trong activeContext.md để nhấn mạnh việc TUYỆT ĐỐI CHỈ CHẠY MỘT LỆNH DUY NHẤT khi xử lý Wiki/Jira
- [x] Thêm quy tắc rõ ràng về việc không chạy nhiều lệnh MCP Server liên tiếp
- [x] Nhấn mạnh việc không bao giờ chạy lệnh wiki/jira nhiều lần cho cùng một URL
- [x] Thêm hướng dẫn đảm bảo người dùng không phải chờ đợi nhiều lần khi xử lý URL
- [x] Cập nhật quy tắc về việc sử dụng tham số bổ sung trong lần chạy đầu tiên nếu cần thêm thông tin
- [x] Cập nhật tasks.md để ghi lại nhiệm vụ đã hoàn thành
- [x] Cập nhật progress.md để ghi lại tiến độ mới

## Danh Sách Chờ

1. [CHỜ XỬ LÝ] Tạo tài liệu component toàn diện
2. [CHỜ XỬ LÝ] Phát triển chiến lược kiểm thử
3. [CHỜ XỬ LÝ] Đánh giá hiệu suất
4. [CHỜ XỬ LÝ] Đánh giá tuân thủ accessibility
5. [CHỜ XỬ LÝ] Tài liệu hóa phương pháp quốc tế hóa

## Ghi Chú

- Dự án là ứng dụng Next.js 15.3.0 với React 19.1.0
- Nhiều thư viện UI đang được sử dụng (Material UI, Ant Design)
- Tập trung mạnh vào type safety với TypeScript
- Nhiều cấu hình môi trường
- Kiến trúc multi-portal đã xác định:
  - SME Portal
  - Partner Portal
  - Product Catalog
  - Payment
  - IoT Portal
- Sử dụng Redux Toolkit và React Query cho quản lý trạng thái
- Nhiều custom hooks đã được phát triển để tái sử dụng logic
- Hệ thống Page Builder sử dụng CraftJS cho việc tạo trang động
- API Client dựa trên Base class với các phương thức chuẩn hóa

## Đã hoàn thành

- [x] Khởi tạo Memory Bank
- [x] Tạo các file cơ sở trong Memory Bank
- [x] Phân tích cấu trúc mã nguồn
- [x] Tài liệu hóa kiến trúc hệ thống
- [x] Tài liệu hóa API Client
- [x] Triển khai MCP Server
- [x] Cập nhật quy tắc tự động xử lý URL trong Memory Bank
- [x] Cập nhật quy tắc xử lý URL trực tiếp trong chat
- [x] Tạo tệp messageScanner.js để tự động phát hiện URL và kết nối MCP Server
- [x] Tạo tệp cấu hình mcp.json để tự động tải messageScanner.js
- [x] Gộp tất cả quy tắc MCP Server vào một file language.mdc duy nhất
- [x] Loại bỏ các file mdc thừa để tối ưu hóa cấu trúc

## Sắp tới

- [ ] Tài liệu hóa các endpoints API cho từng portal
- [ ] Tài liệu hóa strategy kiểm thử
- [ ] Tài liệu hóa phương pháp tối ưu hiệu suất
- [ ] Tạo tài liệu onboarding toàn diện cho thành viên mới
- [ ] Phát triển hướng dẫn best practices
- [ ] Tạo tài liệu hướng dẫn tương tác với API
- [ ] Tạo hướng dẫn sử dụng và mở rộng Page Builder
