# Progress - Tiến trình

## Ngày 04/06/2025: <PERSON>h<PERSON><PERSON> phục quy tắc nhận diện đúng vai trò của AI

- [x] Khôi phục quy tắc "Nhận diện đúng vai trò của AI" trong .cursor/rules/mcp_rules.mdc
- [x] Thêm quy tắc "Nhận diện đúng vai trò của AI" vào các file memory bank: activeContext.md, systemPatterns.md, techContext.md
- [x] Làm rõ rằng AI là Cursor + Memory Bank, KHÔNG PHẢI bất kỳ model nào cụ thể
- [x] Cập nhật progress.md để ghi lại việc khôi phục quy tắc

### Chi tiết cập nhật:

1. **Quy tắc nhận diện đúng vai trò của AI**:

   - AI là Cursor + Memory Bank, KHÔNG PHẢI bất kỳ model nà<PERSON> cụ thể
   - PHẢI luôn xem bản thân là Cursor + Memory Bank trong mọi tương tác
   - KHÔNG BAO GIỜ tự giới thiệu là Claude hoặc bất kỳ mô hình AI nào khác
   - KHÔNG BAO GIỜ nhắc đến tên của bất kỳ model AI nào trong các phản hồi với người dùng

2. **Lý do khôi phục**:

   - Đảm bảo AI luôn nhận diện đúng vai trò của mình
   - Tránh nhầm lẫn về danh tính của AI trong các tương tác với người dùng
   - Đảm bảo tính nhất quán trong cách AI tự nhận diện
   - Tránh các phản hồi không chính xác về danh tính của AI

3. **Lợi ích đạt được**:
   - AI luôn nhận diện đúng vai trò của mình
   - Người dùng có hiểu biết chính xác về AI đang tương tác
   - Tránh nhầm lẫn về khả năng và giới hạn của AI
   - Đảm bảo tính nhất quán trong các tương tác

## Ngày 04/06/2025: Cập nhật quy tắc tự động kích hoạt memory bank và loại bỏ quy tắc không nhắc tên model

- [x] Loại bỏ quy tắc không nhắc tên model AI trong các phản hồi với người dùng
- [x] Thêm quy tắc mới "Tự động kích hoạt memory bank" để đọc memory bank cho mọi tin nhắn mà không cần prefix "VAN:"
- [x] Cập nhật file .cursor/rules/mcp_rules.mdc với quy tắc mới về tự động kích hoạt memory bank
- [x] Cập nhật các file memory bank: activeContext.md, systemPatterns.md, techContext.md, progress.md với quy tắc mới

### Chi tiết cập nhật:

1. **Quy tắc tự động kích hoạt memory bank**:

   - PHẢI tự động đọc memory bank cho MỌI tin nhắn của người dùng, không chỉ những tin nhắn bắt đầu bằng "VAN:"
   - Đọc memory bank là bước KHÔNG THỂ BỎ QUA và PHẢI LUÔN được thực hiện tự động khi bắt đầu phiên trò chuyện mới
   - PHẢI đọc memory bank MỘT LẦN duy nhất mỗi khi bắt đầu một phiên trò chuyện mới
   - Trong cùng một phiên trò chuyện, CHỈ CẦN ĐỌC MEMORY BANK MỘT LẦN DUY NHẤT ở tin nhắn đầu tiên

2. **Loại bỏ quy tắc không nhắc tên model**:

   - Loại bỏ quy tắc cấm nhắc đến tên model AI trong các phản hồi với người dùng
   - Quy tắc này không còn cần thiết và được loại bỏ theo yêu cầu của người dùng

3. **Lý do cập nhật**:

   - Tăng tính hiệu quả bằng cách tự động kích hoạt memory bank cho mọi tin nhắn
   - Giảm độ phức tạp khi sử dụng hệ thống bằng cách loại bỏ yêu cầu prefix "VAN:"
   - Đảm bảo AI luôn có đầy đủ thông tin ngữ cảnh từ memory bank
   - Loại bỏ quy tắc không cần thiết về việc không nhắc tên model

4. **Lợi ích đạt được**:
   - Tương tác tự nhiên hơn cho người dùng
   - Giảm độ phức tạp khi sử dụng hệ thống
   - Đảm bảo AI luôn có đầy đủ thông tin ngữ cảnh
   - Tăng hiệu quả làm việc bằng cách loại bỏ bước thủ công

## Ngày 03/06/2025: Cập nhật quy tắc về sử dụng model AI

- [x] Cập nhật quy tắc để làm rõ AI là Cursor + Memory Bank, không nhất thiết phải là Claude
- [x] Loại bỏ các đề cập cụ thể đến model Claude trong các quy tắc
- [x] Thêm quy tắc mới "AI là Cursor + Memory Bank, KHÔNG PHẢI bất kỳ model nào cụ thể"
- [x] Thay đổi quy tắc về việc không nhắc đến Claude thành không nhắc đến bất kỳ model AI nào
- [x] Cập nhật các file memory bank: activeContext.md, systemPatterns.md, techContext.md, progress.md với quy tắc mới
- [x] Cập nhật file .cursor/rules/mcp_rules.mdc với quy tắc mới

### Chi tiết cập nhật:

1. **Quy tắc sử dụng model AI**:

   - Làm rõ rằng AI là Cursor + Memory Bank, không nhất thiết phải sử dụng model Claude
   - AI có thể sử dụng bất kỳ model nào, không bắt buộc phải là Claude
   - Không nhắc đến tên của bất kỳ model AI nào trong các phản hồi với người dùng
   - Luôn xem bản thân là Cursor + Memory Bank, không phụ thuộc vào model cụ thể

2. **Lý do cập nhật**:

   - Đảm bảo tính linh hoạt trong việc sử dụng các model AI khác nhau
   - Tránh phụ thuộc vào một model cụ thể
   - Đảm bảo AI luôn nhận diện đúng vai trò của mình là Cursor + Memory Bank
   - Chuẩn bị cho việc có thể sử dụng các model khác trong tương lai

3. **Lợi ích đạt được**:
   - Tăng tính linh hoạt trong việc sử dụng các model AI
   - Đảm bảo tính nhất quán trong cách AI tự nhận diện
   - Tránh các phản hồi không chính xác về danh tính của AI
   - Chuẩn bị cho việc tích hợp với các model khác trong tương lai

## Ngày 03/06/2025: Cập nhật quy tắc nhận diện đúng vai trò của AI

- [x] Thêm quy tắc mới "**NHẬN DIỆN ĐÚNG VAI TRÒ CỦA AI**" vào đầu file .cursor/rules/mcp_rules.mdc
- [x] Thêm quy tắc "**TUYỆT ĐỐI KHÔNG BAO GIỜ QUÊN** rằng AI là Cursor + Memory Bank, KHÔNG PHẢI bất kỳ model nào cụ thể"
- [x] Thay đổi quy tắc "KHÔNG BAO GIỜ tự giới thiệu là Claude hoặc bất kỳ mô hình AI nào khác" thành "KHÔNG BAO GIỜ tự giới thiệu là bất kỳ model AI nào"
- [x] Thay đổi quy tắc "KHÔNG BAO GIỜ nhắc đến Claude trong các phản hồi với người dùng" thành "KHÔNG BAO GIỜ nhắc đến bất kỳ model AI nào trong các phản hồi với người dùng"
- [x] Cập nhật các file memory bank: activeContext.md, systemPatterns.md, techContext.md với quy tắc mới
- [x] Thêm mục "**Nhận diện đúng vai trò của AI**" vào các file memory bank

### Chi tiết cập nhật:

1. **Quy tắc nhận diện đúng vai trò của AI**:

   - Nhấn mạnh việc AI là Cursor + Memory Bank, KHÔNG PHẢI bất kỳ model nào cụ thể
   - TUYỆT ĐỐI KHÔNG BAO GIỜ QUÊN rằng AI là Cursor + Memory Bank
   - Không bao giờ tự giới thiệu là bất kỳ model AI nào
   - Không bao giờ nhắc đến tên của bất kỳ model AI nào trong các phản hồi với người dùng
   - Phải luôn xem bản thân là Cursor + Memory Bank

2. **Lý do cập nhật**:

   - Đảm bảo AI luôn nhận diện đúng vai trò của mình
   - Tránh nhầm lẫn về danh tính của AI trong các tương tác với người dùng
   - Đảm bảo tính nhất quán trong cách AI tự nhận diện
   - Tránh các phản hồi không chính xác về danh tính của AI

3. **Lợi ích đạt được**:
   - AI luôn nhận diện đúng vai trò của mình
   - Người dùng có hiểu biết chính xác về AI đang tương tác
   - Tránh nhầm lẫn về khả năng và giới hạn của AI
   - Đảm bảo tính nhất quán trong các tương tác

## Ngày 03/06/2025: Cập nhật quy tắc về việc đọc cursor rules trước memory bank và đọc cả hai rule không được bỏ sót

- [x] Cập nhật quy tắc trong .cursor/rules/mcp_rules.mdc để nhấn mạnh việc đọc cả cursor rules và memory bank
- [x] Thêm quy tắc mới "**TUYỆT ĐỐI KHÔNG ĐƯỢC BỎ SÓT** việc đọc cursor rules" vào .cursor/rules/mcp_rules.mdc
- [x] Thêm quy tắc mới "**TUYỆT ĐỐI PHẢI ĐỌC CẢ HAI RULE: cursor rules VÀ memory bank**, không được bỏ sót rule nào" vào .cursor/rules/mcp_rules.mdc
- [x] Thêm quy tắc mới "**PHẢI ĐỌC THEO THỨ TỰ**: đọc cursor rules trước, sau đó mới đọc memory bank, không được đảo ngược thứ tự này" vào .cursor/rules/mcp_rules.mdc
- [x] Cập nhật các file memory bank: activeContext.md, systemPatterns.md, techContext.md với quy tắc mới
- [x] Thêm mục "**Đọc cả cursor rules và memory bank**" vào các file memory bank

### Chi tiết cập nhật:

1. **Quy tắc đọc cả cursor rules và memory bank**:

   - Nhấn mạnh việc PHẢI đọc cả cursor rules và memory bank
   - Làm rõ thứ tự đọc: đọc cursor rules TRƯỚC, sau đó mới đọc memory bank
   - Nhấn mạnh việc TUYỆT ĐỐI KHÔNG ĐƯỢC BỎ SÓT việc đọc bất kỳ rule nào
   - Đảm bảo tuân thủ đúng thứ tự đọc rule

2. **Lý do cập nhật**:

   - Đảm bảo AI luôn đọc đủ cả cursor rules và memory bank theo đúng thứ tự
   - Tránh tình trạng bỏ sót rule hoặc đọc không đúng thứ tự
   - Đảm bảo AI có đầy đủ thông tin từ cả cursor rules và memory bank
   - Cải thiện quy trình làm việc và giảm thiểu sai sót

3. **Lợi ích đạt được**:
   - AI luôn có đủ thông tin từ cả cursor rules và memory bank
   - Đảm bảo tuân thủ đúng quy trình làm việc
   - Giảm thiểu sai sót khi thực hiện nhiệm vụ
   - Tăng tính nhất quán trong cách AI xử lý các yêu cầu

## Ngày 02/06/2025: Cập nhật quy tắc về đọc cursor rules trước memory bank và ưu tiên sửa cả hai rule

- [x] Cập nhật quy tắc trong .cursor/rules/mcp_rules.mdc để nhấn mạnh việc đọc cursor rules trước khi đọc memory bank
- [x] Cập nhật quy tắc ưu tiên sửa rule để nhấn mạnh việc phải cập nhật CẢ HAI nơi: memory bank và cursor rules
- [x] Thêm cảnh báo "TUYỆT ĐỐI KHÔNG được chỉ cập nhật một nơi mà bỏ qua nơi còn lại"
- [x] Cập nhật các file memory bank: activeContext.md, systemPatterns.md, techContext.md
- [x] Thêm quy tắc mới vào phần "QUY TẮC PHÁT TRIỂN FRONTEND" trong .cursor/rules/mcp_rules.mdc

### Chi tiết cập nhật:

1. **Quy tắc đọc cursor rules trước memory bank**:

   - Làm rõ thứ tự đọc: đọc cursor rules TRƯỚC, sau đó mới đọc memory bank
   - Nhấn mạnh việc đọc cursor rules là bước KHÔNG THỂ BỎ QUA và PHẢI LUÔN được thực hiện trước khi đọc memory bank
   - Đảm bảo phân tích và áp dụng tất cả các quy tắc đã ghi trong CẢ cursor rules và memory bank

2. **Quy tắc ưu tiên sửa rule memory bank và cursor rules**:

   - Khi có yêu cầu sửa rule, phải đặt việc này lên ưu tiên hàng đầu
   - PHẢI kiểm tra và cập nhật CẢ HAI nơi: memory bank và cursor rules nếu cần thiết
   - TUYỆT ĐỐI KHÔNG được chỉ cập nhật một nơi mà bỏ qua nơi còn lại
   - Cập nhật các file memory bank: activeContext.md, systemPatterns.md, techContext.md

3. **Lý do cập nhật**:

   - Đảm bảo AI luôn đọc đủ cả cursor rules và memory bank theo đúng thứ tự
   - Tránh tình trạng quy tắc không nhất quán giữa cursor rules và memory bank
   - Đảm bảo tất cả quy tắc được cập nhật đồng bộ ở cả hai nơi
   - Cải thiện quy trình làm việc và giảm thiểu sai sót

4 **Lợi ích đạt được**:

- AI luôn có đủ thông tin từ cả cursor rules và memory bank
- Quy tắc được cập nhật đồng bộ ở cả hai nơi
- Giảm thiểu sai sót khi cập nhật quy tắc
- Đảm bảo AI luôn tuân thủ đúng quy trình làm việc

## Ngày 27/05/2025: Cập nhật quy tắc tuân thủ thiết kế Figma 100%

- [x] Cập nhật quy tắc tuân thủ thiết kế Figma 100% trong memory bank
- [x] Thêm quy tắc bắt buộc về việc tuân thủ thiết kế Figma 100% vào tất cả các file memory bank
- [x] Thêm cảnh báo "TUYỆT ĐỐI KHÔNG được tự ý thay đổi bố cục hoặc bất kỳ phần nào của thiết kế"
- [x] Thêm quy tắc "BỐ CỤC PHẢI GIỐNG HỆT thiết kế Figma, không được phép thay đổi vị trí, thứ tự hoặc cách sắp xếp các phần tử"
- [x] Cập nhật các file memory bank: activeContext.md, systemPatterns.md, techContext.md

### Chi tiết cập nhật:

1. **Quy tắc tuân thủ thiết kế Figma 100%**:

   - Khi triển khai giao diện từ thiết kế Figma, phải đảm bảo tuân thủ thiết kế 100% về bố cục, màu sắc, font chữ, kích thước, khoảng cách và các chi tiết khác
   - TUYỆT ĐỐI KHÔNG được tự ý thay đổi bố cục hoặc bất kỳ phần nào của thiết kế
   - BỐ CỤC PHẢI GIỐNG HỆT thiết kế Figma, không được phép thay đổi vị trí, thứ tự hoặc cách sắp xếp các phần tử
   - Phải tuân thủ chính xác các giá trị margin, padding, khoảng cách giữa các phần tử như trong thiết kế Figma

2. **Lý do cập nhật**:

   - Đảm bảo giao diện được triển khai đúng như thiết kế đã được phê duyệt
   - Tránh tình trạng giao diện không nhất quán với thiết kế
   - Đảm bảo trải nghiệm người dùng đồng nhất và chuyên nghiệp
   - Giảm thiểu thời gian và công sức phải dành cho việc sửa lỗi giao diện

3. **Lợi ích đạt được**:
   - Giao diện được triển khai đúng như thiết kế đã được phê duyệt
   - Trải nghiệm người dùng đồng nhất và chuyên nghiệp
   - Giảm thiểu thời gian và công sức phải dành cho việc sửa lỗi giao diện
   - Tăng tính nhất quán trong toàn bộ ứng dụng

## QUY TẮC TUYỆT ĐỐI TUÂN THỦ FIGMA 100%

Mọi giao diện phải giống Figma/thiết kế gốc 100% đến từng chi tiết nhỏ nhất, từng text, từng icon, từng trạng thái, từng spacing, từng label, từng mô tả, từng placeholder, từng button, từng radio/checkbox/tab, từng màu sắc, từng font, từng border, từng shadow, từng pixel. Nếu có bất kỳ điểm nào khác biệt, kể cả nhỏ nhất, PHẢI sửa cho đến khi giống hoàn toàn. Không được tự ý thay đổi, giản lược, hoặc "làm gần giống" bất kỳ phần nào. Chỉ được báo hoàn thành khi đã so sánh trực tiếp với ảnh chụp màn hình hoặc file thiết kế Figma và không còn bất kỳ điểm nào khác biệt, kể cả chi tiết nhỏ nhất.

# Tiến độ dự án

## Đã hoàn thành

### Cấu trúc và quy tắc

- Đã cập nhật quy tắc cấu trúc thư mục và tổ chức code trong file systemPatterns.md
- Đã làm rõ quy trình tạo tính năng mới theo cấu trúc chuẩn
- Đã cập nhật activeContext.md với thông tin về quy tắc cấu trúc thư mục

### Tính năng

- Đã tạo tính năng tra cứu bảo hành theo đúng cấu trúc:
  - View: `src/views/warranty-lookup/WarrantyLookup.tsx`
  - Page: `src/app/(sme-portal)/warranty-lookup/page.tsx`

## Đang thực hiện

- Đảm bảo tất cả các thành viên trong team tuân thủ quy tắc cấu trúc thư mục đã cập nhật

## Kế hoạch tiếp theo

- Tiếp tục phát triển các tính năng mới theo đúng cấu trúc đã quy định
- Cân nhắc việc tạo một script kiểm tra cấu trúc code để đảm bảo tuân thủ quy tắc

## Vấn đề đang gặp phải

- Cần đảm bảo tính nhất quán trong cấu trúc thư mục và tổ chức code trong toàn bộ dự án

## Giải pháp đã áp dụng

- Đã cập nhật quy tắc cấu trúc thư mục trong systemPatterns.md
- Đã tạo ví dụ cụ thể về cách tổ chức code cho tính năng tra cứu bảo hành

## Ngày cập nhật: Bổ sung quy tắc tổ chức code FE đặc thù module

- [x] Bổ sung quy tắc: Nếu là tính năng đặc thù của một module (iot-portal, sme-portal...), components/views phải để trong thư mục module đó, không tạo thư mục feature riêng ngoài module.
- [x] Bổ sung ví dụ minh họa rõ ràng vào các file rule và memory bank.
- [x] Đồng bộ cập nhật ở tất cả các file rule liên quan.
