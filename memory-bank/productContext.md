# Ngữ cảnh sản phẩm

## OneX Platform là gì?

OneX là nền tảng số tích hợp của VNPT cung cấp các giải pháp và dịch vụ số cho cả doanh nghiệp và cá nhân. Nền tảng cung cấp nhiều dịch vụ khác nhau như:

- <PERSON><PERSON><PERSON> vụ viễn thông
- <PERSON><PERSON><PERSON><PERSON> pháp doanh nghiệp
- <PERSON><PERSON><PERSON> vụ số cho cá nhân
- Quản lý dịch vụ và thanh toán

## Các loại người dùng

Nền tảng phục vụ cho nhiều loại người dùng khác nhau:

1. <PERSON><PERSON><PERSON><PERSON> hàng cá nhân
2. Kh<PERSON>ch hàng doanh nghiệp (SME)
3. <PERSON><PERSON><PERSON> tá<PERSON> (Partner)
4. <PERSON><PERSON><PERSON> cung cấp (Supplier)
5. Quản tr<PERSON> viên (Admin)

## G<PERSON><PERSON> trị mang lại

- Giúp người dùng quản lý tất cả dịch vụ VNPT trong một nền tảng duy nhất
- C<PERSON> cấp giao diện thân thiện, dễ sử dụng
- Đơn giản hóa quy trình đăng ký và thanh toán
- Tăng cường trải nghiệm khách hàng số

## QUY TẮC TUYỆT ĐỐI TUÂN THỦ FIGMA 100%

Mọi giao diện phải giống Figma/thiết kế gốc 100% đến từng chi tiết nhỏ nhất, từng text, từng icon, từng trạng thái, từng spacing, từng label, từng mô tả, từng placeholder, từng button, từng radio/checkbox/tab, từng màu sắc, từng font, từng border, từng shadow, từng pixel. Nếu có bất kỳ điểm nào khác biệt, kể cả nhỏ nhất, PHẢI sửa cho đến khi giống hoàn toàn. Không được tự ý thay đổi, giản lược, hoặc "làm gần giống" bất kỳ phần nào. Chỉ được báo hoàn thành khi đã so sánh trực tiếp với ảnh chụp màn hình hoặc file thiết kế Figma và không còn bất kỳ điểm nào khác biệt, kể cả chi tiết nhỏ nhất.

## Quy tắc tổ chức code FE (Frontend)

- Component nhỏ, tái sử dụng: để trong `src/components/[module]/[feature]/`
- View tổng hợp: để trong `src/views/[module]/[feature]/[ViewName].tsx`
- Page (route): chỉ gọi đúng 1 view, không xử lý gì thêm, đặt trong `src/app/(module)/[module]/[feature]/page.tsx`
- Không tạo file trung gian nếu không có logic đặc biệt
- Tách biệt hoàn toàn UI (component) – logic/layout (view) – route (page)

### **BỔ SUNG QUY TẮC ĐẶC THÙ MODULE**

- Nếu là tính năng đặc thù của một module (ví dụ: iot-portal, sme-portal, partner-portal...), **components, views phải để trong thư mục module đó**:
  - `src/components/iot-portal/[feature]/...`
  - `src/views/iot-portal/[feature]/...`
  - KHÔNG tạo thư mục feature riêng ngoài module.
- Nếu là tính năng dùng chung cho nhiều module, mới đặt ngoài module.
- Nếu là logic đặc thù module, phải để đúng folder module.

#### Ví dụ minh họa:

```
src/
  components/
    iot-portal/
      partner-register/
        PartnerBanner.tsx
        PartnerRegisterForm.tsx
  views/
    iot-portal/
      partner-register/
        PartnerRegisterView.tsx
  app/
    (iot-portal)/
      iot-portal/
        partner/
          register/
            page.tsx
```
