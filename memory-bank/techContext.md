# Ngữ cảnh kỹ thuật

## Stack công nghệ

Dự án OneX Frontend sử dụng các công nghệ chính sau:

### Frontend Framework

- **Next.js**: Framework React cho server-side rendering và static site generation
- **React**: Thư viện UI
- **TypeScript**: Ngôn ngữ lập trình với type safety

### UI Libraries

- **Material UI (MUI)**: Thư viện component dựa trên Material Design
- **Tailwind CSS**: Framework CSS dựa trên utility classes
- **Emotion**: CSS-in-JS solution

### State Management

- **Redux Toolkit**: Quản lý state toàn cục
- **React Context**: Quản lý state cục bộ
- **React Query**: Quản lý state từ server và data fetching

### Routing

- **Next.js App Router**: Định tuyến dựa trên cấu trúc thư mục

### Build Tools

- **Webpack**: Bundling (đ<PERSON><PERSON><PERSON> tích hợp trong Next.js)
- **PostCSS**: Xử lý CSS
- **TypeScript Compiler**: Biên dịch TypeScript

### Testing

- **Jest**: Unit testing
- **Testing Library**: Component testing

### Linting & Formatting

- **ESLint**: Linting JavaScript/TypeScript
- **Prettier**: Code formatting
- **Stylelint**: CSS linting

### Version Control

- **Git**: Quản lý mã nguồn
- **Husky**: Git hooks

## Development Environment

- **Node.js**: Runtime environment
- **Yarn**: Package manager
- **VS Code**: IDE được khuyến nghị

## Môi trường triển khai

Dự án được triển khai trên nhiều môi trường khác nhau:

- development
- production
- production.devonedx
- production.devonesme
- production.onesme
- production.stagingonedx

## MCP Server

MCP (Message Command Processor) Server là một công cụ nội bộ được sử dụng để xử lý các yêu cầu liên quan đến Jira, Wiki . Cấu trúc chính của MCP Server:

1. **Thư mục .mcp/**

   - **index.js**: Điểm vào chính, chuyển tiếp lệnh đến các handler
   - **handlers/**: Chứa các handler xử lý từng loại lệnh cụ thể
     - **commandHandler.js**: Điều phối lệnh đến các handler chuyên biệt
     - **jiraHandler.js**: Xử lý các lệnh liên quan đến Jira
     - **wikiHandler.js**: Xử lý các lệnh liên quan đến Wiki
   - **api/**: Chứa các module giao tiếp với API bên ngoài
     - **jiraAPI.js**: Giao tiếp với Jira API
     - **wikiAPI.js**: Giao tiếp với Wiki API
   - **utils/**: Chứa các tiện ích dùng chung
     - **formatter.js**: Định dạng dữ liệu đầu ra
   - **config/**: Chứa cấu hình
     - **config.js**: Cấu hình chung

2. **Cấu hình MCP Server**

   - Sử dụng file `.env` trong thư mục `.mcp/` để cấu hình
   - Cần cấu hình API key cho Jira, Wiki

3. **Sử dụng MCP Server**

   - Lệnh Jira: `node .mcp/index.js jira ISSUE_KEY`
   - Lệnh Wiki: `node .mcp/index.js wiki URL`

4. **Vấn đề đã biết**
   - Khi không có API key, MCP Server sẽ trả về lỗi nhưng cung cấp URL truy cập trực tiếp
   - Đã cải thiện xử lý lỗi và thông báo để dễ dàng khắc phục

## Technical Context

### Frontend

- **Framework:** Next.js (App Router)
- **Language:** TypeScript
- **UI Library:** Ant Design
- **Styling:** Tailwind CSS (không sử dụng file CSS riêng biệt)
- **State Management:** React Hooks, Context API, Redux (cho state phức tạp)
- **Form Handling:** Ant Design Form, Formik
- **API Integration:** Axios, React Query
- **Testing:** Jest, React Testing Library, Cypress

### Development Tools

- **Package Manager:** Yarn
- **Linting:** ESLint
- **Formatting:** Prettier
- **Version Control:** Git
- **CI/CD:** GitHub Actions

## Development Setup

- Node.js >= 16.x
- Yarn >= 1.22.x
- Next.js 13+ (App Router)
- TypeScript 5.x

## Technical Constraints

- **Browser Support:** Chrome, Firefox, Safari, Edge (latest 2 versions)
- **Responsive Design:** Mobile, tablet, desktop
- **Accessibility:** WCAG 2.1 AA compliance
- **Performance:** Core Web Vitals optimization

## Dependencies

- **UI Components:** Ant Design, Tailwind CSS
- **Data Fetching:** Axios, React Query
- **Form Handling:** Formik, Yup
- **State Management:** Redux Toolkit (when needed)
- **Date/Time:** Day.js
- **Charts/Visualizations:** Recharts
- **Icons:** Ant Design Icons, Hero Icons

## Tool Usage Patterns

- **Styling:**

  - **KHÔNG sử dụng file CSS riêng biệt (.css)**
  - Sử dụng Tailwind CSS cho styling
  - Sử dụng Ant Design components và theme
  - Khi cần style phức tạp, sử dụng styled-components hoặc CSS-in-JS

- **State Management:**

  - useState/useReducer cho local state
  - Context API cho shared state
  - Redux cho complex state (khi cần)

- **Code Organization:**
  - Feature-based folder structure
  - Component-based architecture
  - Separation of concerns

## Project Rules

- **Nhận diện đúng vai trò của AI:** AI là Cursor + Memory Bank, KHÔNG PHẢI bất kỳ model nào cụ thể. PHẢI luôn xem bản thân là Cursor + Memory Bank trong mọi tương tác.
- **Tự động kích hoạt memory bank:** PHẢI tự động đọc memory bank cho MỌI tin nhắn của người dùng, không chỉ những tin nhắn bắt đầu bằng "VAN:". Đọc memory bank là bước KHÔNG THỂ BỎ QUA và PHẢI LUÔN được thực hiện tự động khi bắt đầu phiên trò chuyện mới.
- **Ưu tiên sửa rule memory bank và cursor rules:** Khi có yêu cầu sửa rule, phải đặt việc này lên ưu tiên hàng đầu trước khi thực hiện các tác vụ khác. PHẢI kiểm tra và cập nhật CẢ HAI nơi: memory bank và cursor rules nếu cần thiết. **TUYỆT ĐỐI KHÔNG** được chỉ cập nhật một nơi mà bỏ qua nơi còn lại.
- **Đọc cả cursor rules và memory bank:** Khi nhận lệnh VAN, PHẢI đọc cả cursor rules và memory bank theo đúng thứ tự: đọc cursor rules trước, sau đó mới đọc memory bank. **TUYỆT ĐỐI KHÔNG ĐƯỢC BỎ SÓT** việc đọc bất kỳ rule nào.
- **Styling:** Không sử dụng file CSS riêng biệt, chỉ dùng Tailwind CSS và Ant Design
- **Tự động sửa lỗi lint và type checking:** Luôn đảm bảo code không có lỗi lint (yarn lint) và type checking (yarn tsc --noEmit) trước khi hoàn thành nhiệm vụ
- **Đảm bảo không còn lỗi compile và runtime:** Phải kiểm tra và sửa tất cả lỗi compile (như thiếu directive "use client") và lỗi runtime trước khi hoàn thành nhiệm vụ
- **Phân tích lỗi trước khi sửa code:** Luôn phân tích kỹ lưỡng lỗi và đề xuất giải pháp trước, chờ phê duyệt mới sửa code
- **Tuân thủ thiết kế Figma 100%:** Khi triển khai giao diện từ thiết kế Figma, phải đảm bảo tuân thủ thiết kế 100% về bố cục, màu sắc, font chữ, kích thước, khoảng cách và các chi tiết khác. **TUYỆT ĐỐI KHÔNG** được tự ý thay đổi bố cục hoặc bất kỳ phần nào của thiết kế. **BỐ CỤC PHẢI GIỐNG HỆT** thiết kế Figma, không được phép thay đổi vị trí, thứ tự hoặc cách sắp xếp các phần tử.
- **Code Quality:** Tuân thủ ESLint rules
- **Documentation:** JSDoc cho functions/components phức tạp
- **Testing:** Unit tests cho business logic quan trọng

## Next.js App Router Guidelines

- **Client Components:**
  - Thêm directive "use client" ở đầu file cho các components sử dụng React hooks
  - Đảm bảo phân biệt rõ ràng giữa Server Components và Client Components
  - Tránh lỗi "You're importing a component that needs useState. This React hook only works in a client component"
- **Server Components:**

  - Không sử dụng React hooks trong Server Components
  - Sử dụng Server Components cho các components không cần tương tác người dùng
  - Tối ưu hóa hiệu suất bằng cách giảm JavaScript gửi đến client

- Mọi giao diện phải giống Figma/thiết kế gốc 100% đến từng chi tiết nhỏ nhất, từng text, từng icon, từng trạng thái, từng spacing, từng label, từng mô tả, từng placeholder, từng button, từng radio/checkbox/tab, từng màu sắc, từng font, từng border, từng shadow, từng pixel. Nếu có bất kỳ điểm nào khác biệt, kể cả nhỏ nhất, PHẢI sửa cho đến khi giống hoàn toàn. Không được tự ý thay đổi, giản lược, hoặc "làm gần giống" bất kỳ phần nào. Chỉ được báo hoàn thành khi đã so sánh trực tiếp với ảnh chụp màn hình hoặc file thiết kế Figma và không còn bất kỳ điểm nào khác biệt, kể cả chi tiết nhỏ nhất.

## Quy tắc tổ chức code FE (Frontend) - BỔ SUNG

- Nếu là tính năng đặc thù của một module (ví dụ: iot-portal, sme-portal, partner-portal...), **components, views phải để trong thư mục module đó**:
  - `src/components/iot-portal/[feature]/...`
  - `src/views/iot-portal/[feature]/...`
  - KHÔNG tạo thư mục feature riêng ngoài module.
- Nếu là tính năng dùng chung cho nhiều module, mới đặt ngoài module.
- Nếu là logic đặc thù module, phải để đúng folder module.

#### Ví dụ minh họa:

```
src/
  components/
    iot-portal/
      partner-register/
        PartnerBanner.tsx
        PartnerRegisterForm.tsx
  views/
    iot-portal/
      partner-register/
        PartnerRegisterView.tsx
  app/
    (iot-portal)/
      iot-portal/
        partner/
          register/
            page.tsx
```
