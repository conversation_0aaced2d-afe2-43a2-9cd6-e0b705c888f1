# Agenda buổi demo Memory Bank + Cursor

## Thông tin buổi trình bày

- **Thời gian:** Thứ 4, 14:00 - 15:00
- **Đ<PERSON><PERSON> điểm:** Phòng họp 1
- **<PERSON><PERSON><PERSON> tượng tham dự:** Dev lead + team leader

## Nội dung trình bày

### 1. <PERSON><PERSON><PERSON><PERSON> thiệu tổng quan (5 phút)

- <PERSON>h<PERSON><PERSON> thức trong phát triển dự án phức tạp
- Memory Bank là gì và giải quyết vấn đề gì
- Tích hợp với Cursor

### 2. <PERSON><PERSON><PERSON> đặt và thiết lập (10 phút)

- <PERSON><PERSON>i đặt Cursor và cấu hình
- Cấu trúc Memory Bank
- Các MODE chính và quy trình làm việc

### 3. <PERSON><PERSON> thực tế (30 phút)

- **Case 1: <PERSON><PERSON><PERSON> triển tính năng mới** (10 phút)
  - V<PERSON> dụ: Tạo component Button tùy chỉnh
  - Quy trình: VAN → PLAN → CREATIVE → IMPLEMENT → REFLECT → ARCHIVE
- **Case 2: Cập nhật tính năng có sẵn** (10 phút)
  - Ví dụ: Cập nhật Form Validation
  - Quy trình: VAN → PLAN → IMPLEMENT → REFLECT
- **Case 3: Fix bug** (10 phút)
  - Ví dụ: Sửa lỗi UI trên mobile view
  - Quy trình rút gọn: VAN → IMPLEMENT → REFLECT

### 4. Hiệu quả đo lường (5 phút)

- Thời gian phát triển
- Chất lượng code
- Tài liệu và onboarding

### 5. Thảo luận và Q&A (10 phút)

- Giải đáp thắc mắc
- Thảo luận về triển khai thực tế

## Chuẩn bị

### Môi trường demo

- Máy tính đã cài đặt Cursor và cấu hình sẵn các MODE
- Dự án ONEDX với Memory Bank đã thiết lập
- Kết nối internet ổn định để truy cập API

### Tài liệu phân phát

- Hướng dẫn cài đặt (nếu cần)
- Link tới slide để tham khảo sau buổi demo
