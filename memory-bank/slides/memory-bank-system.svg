<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 1320.5625 251" style="max-width: 3840px; background-color: white; max-height: 3840px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="my-svg"><style>#my-svg{font-family:arial,sans-serif;font-size:14px;fill:#333;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#my-svg .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#my-svg .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#my-svg .error-icon{fill:#ffffff;}#my-svg .error-text{fill:#000000;stroke:#000000;}#my-svg .edge-thickness-normal{stroke-width:2px;}#my-svg .edge-thickness-thick{stroke-width:3.5px;}#my-svg .edge-pattern-solid{stroke-dasharray:0;}#my-svg .edge-thickness-invisible{stroke-width:0;fill:none;}#my-svg .edge-pattern-dashed{stroke-dasharray:3;}#my-svg .edge-pattern-dotted{stroke-dasharray:2;}#my-svg .marker{fill:#000000;stroke:#000000;}#my-svg .marker.cross{stroke:#000000;}#my-svg svg{font-family:arial,sans-serif;font-size:14px;}#my-svg p{margin:0;}#my-svg .label{font-family:arial,sans-serif;color:#333;}#my-svg .cluster-label text{fill:#000000;}#my-svg .cluster-label span{color:#000000;}#my-svg .cluster-label span p{background-color:transparent;}#my-svg .label text,#my-svg span{fill:#333;color:#333;}#my-svg .node rect,#my-svg .node circle,#my-svg .node ellipse,#my-svg .node polygon,#my-svg .node path{fill:#ffffff;stroke:#000000;stroke-width:2px;}#my-svg .rough-node .label text,#my-svg .node .label text,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-anchor:middle;}#my-svg .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#my-svg .rough-node .label,#my-svg .node .label,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-align:center;}#my-svg .node.clickable{cursor:pointer;}#my-svg .root .anchor path{fill:#000000!important;stroke-width:0;stroke:#000000;}#my-svg .arrowheadPath{fill:#000000;}#my-svg .edgePath .path{stroke:#000000;stroke-width:2px;}#my-svg .flowchart-link{stroke:#000000;fill:none;}#my-svg .edgeLabel{background-color:hsl(-120, 0%, 80%);text-align:center;}#my-svg .edgeLabel p{background-color:hsl(-120, 0%, 80%);}#my-svg .edgeLabel rect{opacity:0.5;background-color:hsl(-120, 0%, 80%);fill:hsl(-120, 0%, 80%);}#my-svg .labelBkg{background-color:rgba(204, 204, 204, 0.5);}#my-svg .cluster rect{fill:#ffffff;stroke:hsl(0, 0%, 90%);stroke-width:2px;}#my-svg .cluster text{fill:#000000;}#my-svg .cluster span{color:#000000;}#my-svg div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:arial,sans-serif;font-size:12px;background:#ffffff;border:1px solid hsl(0, 0%, 90%);border-radius:2px;pointer-events:none;z-index:100;}#my-svg .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#333;}#my-svg rect.text{fill:none;stroke-width:0;}#my-svg .icon-shape,#my-svg .image-shape{background-color:hsl(-120, 0%, 80%);text-align:center;}#my-svg .icon-shape p,#my-svg .image-shape p{background-color:hsl(-120, 0%, 80%);padding:2px;}#my-svg .icon-shape rect,#my-svg .image-shape rect{opacity:0.5;background-color:hsl(-120, 0%, 80%);fill:hsl(-120, 0%, 80%);}#my-svg .node .neo-node{stroke:#000000;}#my-svg [data-look="neo"].node rect,#my-svg [data-look="neo"].cluster rect,#my-svg [data-look="neo"].node polygon{stroke:url(#my-svg-gradient);filter:drop-shadow( 0px 1px 2px rgba(0, 0, 0, 0.25));}#my-svg [data-look="neo"].node path{stroke:url(#my-svg-gradient);stroke-width:2;}#my-svg [data-look="neo"].node .outer-path{filter:drop-shadow( 0px 1px 2px rgba(0, 0, 0, 0.25));}#my-svg [data-look="neo"].node .neo-line path{stroke:#000000;filter:none;}#my-svg [data-look="neo"].node circle{stroke:url(#my-svg-gradient);filter:drop-shadow( 0px 1px 2px rgba(0, 0, 0, 0.25));}#my-svg [data-look="neo"].node circle .state-start{fill:#000000;}#my-svg [data-look="neo"].statediagram-cluster rect{fill:#ffffff;stroke:url(#my-svg-gradient);stroke-width:2;}#my-svg [data-look="neo"].icon-shape .icon{fill:url(#my-svg-gradient);filter:drop-shadow( 0px 1px 2px rgba(0, 0, 0, 0.25));}#my-svg [data-look="neo"].icon-shape .icon-neo path{stroke:url(#my-svg-gradient);filter:drop-shadow( 0px 1px 2px rgba(0, 0, 0, 0.25));}#my-svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="14" markerWidth="10.5" markerUnits="userSpaceOnUse" refY="7" refX="7.75" viewBox="0 0 11.5 14" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointEnd"><path style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 11.5 7 L 0 14 z"/></marker><marker orient="auto" markerHeight="14" markerWidth="11.5" markerUnits="userSpaceOnUse" refY="7" refX="4" viewBox="0 0 11.5 14" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointStart"><polygon style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" points="0,7 11.5,14 11.5,0"/></marker><marker orient="auto" markerHeight="14" markerWidth="10.5" markerUnits="userSpaceOnUse" refY="7" refX="11.5" viewBox="0 0 11.5 14" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointEnd-margin"><path style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 11.5 7 L 0 14 z"/></marker><marker orient="auto" markerHeight="14" markerWidth="11.5" markerUnits="userSpaceOnUse" refY="7" refX="1" viewBox="0 0 11.5 14" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointStart-margin"><polygon style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" points="0,7 11.5,14 11.5,0"/></marker><marker orient="auto" markerHeight="14" markerWidth="14" markerUnits="userSpaceOnUse" refX="10.75" refY="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleEnd"><circle style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="14" markerWidth="14" markerUnits="userSpaceOnUse" refY="5" refX="0" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleStart"><circle style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="14" markerWidth="14" markerUnits="userSpaceOnUse" refX="12.25" refY="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleEnd-margin"><circle style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="14" markerWidth="14" markerUnits="userSpaceOnUse" refY="5" refX="-2" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleStart-margin"><circle style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="7.5" refX="17.7" viewBox="0 0 15 15" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossEnd"><path style="stroke-width: 2.5;" class="arrowMarkerPath" d="M 1,1 L 14,14 M 1,14 L 14,1"/></marker><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="7.5" refX="-3.5" viewBox="0 0 15 15" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossStart"><path style="stroke-width: 2.5; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 L 14,14 M 1,14 L 14,1"/></marker><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="7.5" refX="17.7" viewBox="0 0 15 15" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossEnd-margin"><path style="stroke-width: 2.5;" class="arrowMarkerPath" d="M 1,1 L 14,14 M 1,14 L 14,1"/></marker><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="7.5" refX="-3.5" viewBox="0 0 15 15" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossStart-margin"><path style="stroke-width: 2.5; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 L 14,14 M 1,14 L 14,1"/></marker><g class="root"><g class="clusters"/><g class="edgePaths"><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6ODMyLjEwMTU2MjUsInkiOjQyLjkyNDU3OTY1NDA0NjIwNX0seyJ4Ijo1OTMuNjQwNjI1LCJ5Ijo3OH0seyJ4Ijo1OTMuNjQwNjI1LCJ5IjoxMDN9XQ==" data-id="L_Main_Modes_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 250.61654663085938 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Main_Modes_0" d="M832.1015625,42.924579654046205L601.2087324204588,76.88680069876887Q593.640625,78 593.640625,85.64954002616261L593.640625,99"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6ODYyLjcxNDYzODE1Nzg5NDgsInkiOjUzfSx7IngiOjgwMi44NzUsInkiOjc4fSx7IngiOjgwMi44NzUsInkiOjEwM31d" data-id="L_Main_Rules_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 74.87271881103516 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Main_Rules_0" d="M862.7146381578948,53L811.1981397467831,74.52273141892108Q802.875,78 802.875,87.02031329996026L802.875,99"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6OTcwLjQyNTk4Njg0MjEwNTIsInkiOjUzfSx7IngiOjEwMzAuMjY1NjI1LCJ5Ijo3OH0seyJ4IjoxMDMwLjI2NTYyNSwieSI6MTAzfV0=" data-id="L_Main_Visual_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 74.87271881103516 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Main_Visual_0" d="M970.4259868421052,53L1021.9424852532169,74.52273141892108Q1030.265625,78 1030.265625,87.02031329996026L1030.265625,99"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6MTAwMS4wMzkwNjI1LCJ5Ijo0My4wMjAyOTU0NzI4Mjk2NjZ9LHsieCI6MTIzNy4wMzEyNSwieSI6Nzh9LHsieCI6MTIzNy4wMzEyNSwieSI6MTAzfV0=" data-id="L_Main_Token_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 248.16229248046875 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Main_Token_0" d="M1001.0390625,43.020295472829666L1229.459519433918,76.87768785582833Q1237.03125,78 1237.03125,85.65445545511196L1237.03125,99"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6NTMwLjU2MjUsInkiOjEzMS4zMTQ1MzM0OTEwMDk0M30seyJ4Ijo3OC4zNDM3NSwieSI6MTczfSx7IngiOjc4LjM0Mzc1LCJ5IjoxOTh9XQ==" data-id="L_Modes_VAN_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 463.6317443847656 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Modes_VAN_0" d="M530.5625,131.31453349100943L85.7322231784915,172.3189314878373Q78.34375,173 78.34375,180.41979718237283L78.34375,194"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6NTMwLjU2MjUsInkiOjEzNS4wNjI3NzI3MjE2MDU4fSx7IngiOjI4MC4zMjAzMTI1LCJ5IjoxNzN9LHsieCI6MjgwLjMyMDMxMjUsInkiOjE5OH1d" data-id="L_Modes_PLAN_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 262.698974609375 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Modes_PLAN_0" d="M530.5625,135.0627727216058L287.90282317377756,171.850475878405Q280.3203125,173 280.3203125,180.66915078897784L280.3203125,194"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6NTQzLjY1OTUzOTQ3MzY4NDIsInkiOjE0OH0seyJ4Ijo0ODguMTI1LCJ5IjoxNzN9LHsieCI6NDg4LjEyNSwieSI6MTk4fV0=" data-id="L_Modes_CREATIVE_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 70.9697265625 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Modes_CREATIVE_0" d="M543.6595394736842,148L496.5228979773343,169.21951579281856Q488.125,173 488.125,182.2096010379638L488.125,194"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6NjQzLjYyMTcxMDUyNjMxNTgsInkiOjE0OH0seyJ4Ijo2OTkuMTU2MjUsInkiOjE3M30seyJ4Ijo2OTkuMTU2MjUsInkiOjE5OH1d" data-id="L_Modes_IMPLEMENT_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 70.9697494506836 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Modes_IMPLEMENT_0" d="M643.6217105263158,148L690.7583520226657,169.21951579281856Q699.15625,173 699.15625,182.2096010379638L699.15625,194"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6NjU2LjcxODc1LCJ5IjoxMzUuMDMxODc1MjMzMDA2MDh9LHsieCI6OTA3Ljk3NjU2MjUsInkiOjE3M30seyJ4Ijo5MDcuOTc2NTYyNSwieSI6MTk4fV0=" data-id="L_Modes_REFLECT_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 263.70697021484375 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Modes_REFLECT_0" d="M656.71875,135.03187523300608L900.3956130336071,171.85442592877672Q907.9765625,173 907.9765625,180.6670160274165L907.9765625,194"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6NjU2LjcxODc1LCJ5IjoxMzEuMDg1ODc0OTE2MjUxNTZ9LHsieCI6MTEzMC4wMzEyNSwieSI6MTczfSx7IngiOjExMzAuMDMxMjUsInkiOjE5OH1d" data-id="L_Modes_ARCHIVE_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 484.6543273925781 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Modes_ARCHIVE_0" d="M656.71875,131.08587491625156L1122.6549037736477,172.34678864726294Q1130.03125,173 1130.03125,180.4052122672052L1130.03125,194"/></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_Main_Modes_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_Main_Rules_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_Main_Visual_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_Main_Token_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_Modes_VAN_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_Modes_PLAN_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_Modes_CREATIVE_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_Modes_IMPLEMENT_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_Modes_REFLECT_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_Modes_ARCHIVE_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(916.5703125, 30.5)" data-look="neo" data-et="node" data-node="true" data-id="Main" id="flowchart-Main-0" class="node default"><rect stroke="url(#gradient)" height="45" width="168.9375" y="-22.5" x="-84.46875" data-id="Main" style="fill:#4da6ff !important;stroke:#0066cc !important" class="basic label-container"/><g transform="translate(-68.46875, -10.5)" style="color:white !important" class="label"><rect/><foreignObject height="21" width="136.9375"><div xmlns="http://www.w3.org/1999/xhtml" style="color: white !important; display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:white !important"><p>Memory Bank System</p></span></div></foreignObject></g></g><g transform="translate(593.640625, 125.5)" data-look="neo" data-et="node" data-node="true" data-id="Modes" id="flowchart-Modes-1" class="node default"><rect stroke="url(#gradient)" height="45" width="126.15625" y="-22.5" x="-63.078125" data-id="Modes" style="fill:#f8d486 !important;stroke:#e8b84d !important" class="basic label-container"/><g transform="translate(-47.078125, -10.5)" style="color:black !important" class="label"><rect/><foreignObject height="21" width="94.15625"><div xmlns="http://www.w3.org/1999/xhtml" style="color: black !important; display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:black !important"><p>Custom Modes</p></span></div></foreignObject></g></g><g transform="translate(802.875, 125.5)" data-look="neo" data-et="node" data-node="true" data-id="Rules" id="flowchart-Rules-3" class="node default"><rect stroke="url(#gradient)" height="45" width="192.3125" y="-22.5" x="-96.15625" data-id="Rules" style="fill:#80ffaa !important;stroke:#4dbb5f !important" class="basic label-container"/><g transform="translate(-80.15625, -10.5)" style="color:black !important" class="label"><rect/><foreignObject height="21" width="160.3125"><div xmlns="http://www.w3.org/1999/xhtml" style="color: black !important; display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:black !important"><p>Hierarchical Rule Loading</p></span></div></foreignObject></g></g><g transform="translate(1030.265625, 125.5)" data-look="neo" data-et="node" data-node="true" data-id="Visual" id="flowchart-Visual-5" class="node default"><rect stroke="url(#gradient)" height="45" width="162.46875" y="-22.5" x="-81.234375" data-id="Visual" style="fill:#d9b3ff !important;stroke:#b366ff !important" class="basic label-container"/><g transform="translate(-65.234375, -10.5)" style="color:black !important" class="label"><rect/><foreignObject height="21" width="130.46875"><div xmlns="http://www.w3.org/1999/xhtml" style="color: black !important; display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:black !important"><p>Visual Process Maps</p></span></div></foreignObject></g></g><g transform="translate(1237.03125, 125.5)" data-look="neo" data-et="node" data-node="true" data-id="Token" id="flowchart-Token-7" class="node default"><rect stroke="url(#gradient)" height="45" width="151.0625" y="-22.5" x="-75.53125" data-id="Token" style="fill:#ff9980 !important;stroke:#ff5533 !important" class="basic label-container"/><g transform="translate(-59.53125, -10.5)" style="color:black !important" class="label"><rect/><foreignObject height="21" width="119.0625"><div xmlns="http://www.w3.org/1999/xhtml" style="color: black !important; display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:black !important"><p>Token Optimization</p></span></div></foreignObject></g></g><g transform="translate(78.34375, 220.5)" data-look="neo" data-et="node" data-node="true" data-id="VAN" id="flowchart-VAN-9" class="node default"><rect stroke="url(#gradient)" height="45" width="140.6875" y="-22.5" x="-70.34375" data-id="VAN" style="" class="basic label-container"/><g transform="translate(-54.34375, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="108.6875"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>VAN: Initialization</p></span></div></foreignObject></g></g><g transform="translate(280.3203125, 220.5)" data-look="neo" data-et="node" data-node="true" data-id="PLAN" id="flowchart-PLAN-11" class="node default"><rect stroke="url(#gradient)" height="45" width="163.265625" y="-22.5" x="-81.6328125" data-id="PLAN" style="" class="basic label-container"/><g transform="translate(-65.6328125, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="131.265625"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>PLAN: Task Planning</p></span></div></foreignObject></g></g><g transform="translate(488.125, 220.5)" data-look="neo" data-et="node" data-node="true" data-id="CREATIVE" id="flowchart-CREATIVE-13" class="node default"><rect stroke="url(#gradient)" height="45" width="152.34375" y="-22.5" x="-76.171875" data-id="CREATIVE" style="" class="basic label-container"/><g transform="translate(-60.171875, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="120.34375"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>CREATIVE: Design</p></span></div></foreignObject></g></g><g transform="translate(699.15625, 220.5)" data-look="neo" data-et="node" data-node="true" data-id="IMPLEMENT" id="flowchart-IMPLEMENT-15" class="node default"><rect stroke="url(#gradient)" height="45" width="169.71875" y="-22.5" x="-84.859375" data-id="IMPLEMENT" style="" class="basic label-container"/><g transform="translate(-68.859375, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="137.71875"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>IMPLEMENT: Building</p></span></div></foreignObject></g></g><g transform="translate(907.9765625, 220.5)" data-look="neo" data-et="node" data-node="true" data-id="REFLECT" id="flowchart-REFLECT-17" class="node default"><rect stroke="url(#gradient)" height="45" width="147.921875" y="-22.5" x="-73.9609375" data-id="REFLECT" style="" class="basic label-container"/><g transform="translate(-57.9609375, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="115.921875"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>REFLECT: Review</p></span></div></foreignObject></g></g><g transform="translate(1130.03125, 220.5)" data-look="neo" data-et="node" data-node="true" data-id="ARCHIVE" id="flowchart-ARCHIVE-19" class="node default"><rect stroke="url(#gradient)" height="45" width="196.1875" y="-22.5" x="-98.09375" data-id="ARCHIVE" style="" class="basic label-container"/><g transform="translate(-82.09375, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="164.1875"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ARCHIVE: Documentation</p></span></div></foreignObject></g></g></g></g></g><defs><filter width="130%" height="130%" id="drop-shadow"><feDropShadow flood-color="#FFFFFF" flood-opacity="0.06" stdDeviation="0" dy="4" dx="4"/></filter></defs><defs><filter width="150%" height="150%" id="drop-shadow-small"><feDropShadow flood-color="#FFFFFF" flood-opacity="0.06" stdDeviation="0" dy="2" dx="2"/></filter></defs><linearGradient y2="0%" x2="100%" y1="0%" x1="0%" gradientUnits="objectBoundingBox" id="my-svg-gradient"><stop stop-opacity="1" stop-color="#0042eb" offset="0%"/><stop stop-opacity="1" stop-color="#eb0042" offset="100%"/></linearGradient></svg>