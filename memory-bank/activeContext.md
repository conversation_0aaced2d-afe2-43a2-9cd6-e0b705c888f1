# Active Context

## Current Focus

Dự án hiện đang tập trung vào việc phát triển và cải thiện giao diện người dùng cho oneSME Portal. Các thành phần UI cần được phát triển theo đúng thiết kế Figma, đả<PERSON> bảo tính nhất quán và trải nghiệm người dùng tốt nhất.

## Quy tắc tuyệt đối tuân thủ Figma 100%

- **PHẢI làm đúng thiết kế Figma 100% đến từng chi tiết nhỏ nhất:**

  - Từng text, từng icon, từng trạng thái (hover, active, disabled, error, focus)
  - Từng spacing, từng padding, từng margin, từng khoảng cách giữa các phần tử
  - Từng label, từng mô tả, từng placeholder, từng tooltip
  - Từng button, từng input, từng checkbox/radio, từng dropdown
  - Từng màu sắc, từng font, từng font-weight, từng font-size
  - Từng border, từng border-radius, từng shadow, từng opacity
  - Từng pixel, từng vị trí, từng kích thước
  - Từng thành phần UI như thanh search, filter, pagination
  - Từng trạng thái của dữ liệu (loading, empty, error, success)
  - Từng animation, transition, và hiệu ứng

- **KHÔNG ĐƯỢC tự ý thay đổi thiết kế dưới BẤT KỲ hình thức nào:**

  - KHÔNG được tự ý thay đổi bố cục, vị trí, thứ tự các thành phần
  - KHÔNG được tự ý thay đổi màu sắc, font chữ, kích thước, khoảng cách
  - KHÔNG được tự ý thay đổi nội dung text, placeholder, label, mô tả
  - KHÔNG được tự ý thay đổi icon, hình ảnh, animation
  - KHÔNG được tự ý thay đổi component (ví dụ: dùng Radio thay vì Checkbox)
  - KHÔNG được tự ý thay đổi, giản lược, hoặc "làm gần giống" bất kỳ phần nào
  - KHÔNG được tự ý thêm, bớt, hoặc điều chỉnh bất kỳ chi tiết nào
  - KHÔNG được bỏ qua bất kỳ thành phần UI nào trong thiết kế (như thanh search, filter, pagination)
  - KHÔNG được bỏ qua bất kỳ trạng thái nào của giao diện (như empty state, loading state)

- **QUY TRÌNH PHÂN TÍCH THIẾT KẾ FIGMA TRƯỚC KHI CODE:**

  - PHẢI phân tích kỹ lưỡng toàn bộ thiết kế Figma trước khi bắt đầu code
  - PHẢI liệt kê tất cả các thành phần UI có trong thiết kế
  - PHẢI kiểm tra tất cả các layer, group, và component trong file Figma
  - PHẢI kiểm tra các trạng thái khác nhau của giao diện (nếu có)
  - PHẢI kiểm tra các biến thể của component (nếu có)
  - PHẢI kiểm tra các thuộc tính chính xác (màu sắc, font, kích thước) từ Figma
  - PHẢI đảm bảo không bỏ sót bất kỳ chi tiết nào trong thiết kế

- **QUY TRÌNH KIỂM TRA TRƯỚC KHI BÁO HOÀN THÀNH:**

  - PHẢI so sánh trực tiếp với ảnh chụp màn hình hoặc file thiết kế Figma
  - PHẢI kiểm tra từng chi tiết, từng pixel, từng text, từng spacing
  - PHẢI sửa cho đến khi GIỐNG HOÀN TOÀN 100%, không còn bất kỳ điểm nào khác biệt
  - PHẢI kiểm tra tất cả các trạng thái (hover, active, disabled, error, focus)
  - PHẢI kiểm tra responsive trên các kích thước màn hình khác nhau (nếu có)
  - PHẢI kiểm tra lại từng thành phần UI đã được liệt kê trong bước phân tích
  - CHỈ được báo hoàn thành khi đã kiểm tra kỹ và KHÔNG CÒN BẤT KỲ ĐIỂM NÀO KHÁC BIỆT

- **TUYỆT ĐỐI KHÔNG ĐƯỢC BÁO HOÀN THÀNH khi chưa giống 100% thiết kế Figma**

- **TUYỆT ĐỐI KHÔNG ĐƯỢC TỰ SÁNG TẠO khi không xem được thiết kế Figma**
  - Nếu không kết nối được MCP Server hoặc không xem được thiết kế Figma, KHÔNG ĐƯỢC tự ý vẽ giao diện
  - PHẢI báo cáo lỗi kết nối và đề xuất cách khắc phục MCP Server
  - CHỈ thực hiện vẽ giao diện khi đã xem được thiết kế Figma đầy đủ
  - PHẢI đảm bảo hiểu rõ 100% thiết kế trước khi bắt đầu code
  - KHÔNG ĐƯỢC phỏng đoán hoặc tự sáng tạo bất kỳ phần nào của giao diện khi không thấy rõ thiết kế
  - Khi không xem được thiết kế, PHẢI ưu tiên sửa lỗi kết nối MCP Server trước khi tiếp tục

## Recent Changes

- Cập nhật quy tắc tuân thủ thiết kế Figma 100% với yêu cầu chi tiết hơn về việc phân tích thiết kế và không bỏ sót các thành phần UI như thanh search, filter, pagination.

## Next Steps

- Tiếp tục phát triển các tính năng theo đúng thiết kế Figma.
- Đảm bảo tuân thủ 100% thiết kế Figma trong mọi giao diện.

## Active Decisions

- Sử dụng Ant Design kết hợp với Tailwind CSS cho styling.
- Tuân thủ thiết kế Figma 100% không có ngoại lệ.

## Important Patterns

- Component-based architecture
- Responsive design
- Type-safe development with TypeScript

## Project Insights

- Việc tuân thủ thiết kế Figma 100% là ưu tiên hàng đầu để đảm bảo tính nhất quán của giao diện người dùng.

## QUY TẮC SỬA LỖI LINTER, TYPE CHECKING, COMPILE, RUNTIME

- **PHẢI SỬA BẰNG ĐƯỢC TẤT CẢ LỖI** trước khi hoàn thành bất kỳ nhiệm vụ nào liên quan đến code:

  - Lỗi ESLint (yarn lint)
  - Lỗi TypeScript (yarn tsc --noEmit)
  - Lỗi Compile (như thiếu directive "use client")
  - Lỗi Runtime
  - Warnings (kể cả warning nhỏ nhất)

- **KHÔNG ĐƯỢC BỎ QUA BẤT KỲ LỖI NÀO** – kể cả lỗi nhỏ nhất, kể cả khi chỉ là warning

- **QUY TRÌNH KIỂM TRA TRƯỚC KHI BÁO HOÀN THÀNH:**

  - PHẢI chạy lệnh `yarn lint` và `yarn tsc --noEmit` để kiểm tra
  - PHẢI kiểm tra console trong developer tools để đảm bảo không có lỗi runtime
  - PHẢI sửa tất cả lỗi và warnings được phát hiện
  - CHỈ được báo hoàn thành khi code hoàn toàn sạch lỗi

- **TUYỆT ĐỐI KHÔNG ĐƯỢC BÁO HOÀN THÀNH khi code còn lỗi hoặc warning**

## QUY TRÌNH PHÂN TÍCH VÀ SỬA LỖI

PHẢI tuân thủ nghiêm ngặt quy trình phân tích và sửa lỗi theo thứ tự sau:

1. **PHÂN TÍCH LỖI**:

   - Đọc kỹ mô tả lỗi từ Jira/Wiki
   - Phân tích chi tiết file đính kèm (ảnh chụp màn hình, video, v.v.)
   - **TUYỆT ĐỐI PHẢI phân tích đầy đủ TOÀN BỘ file đính kèm** trong ticket Jira, không được bỏ sót bất kỳ file nào
   - **PHẢI PHÂN TÍCH KỸ LƯỠNG các comment trong ticket**, đặc biệt là **comment reopen** để hiểu lý do tại sao lỗi vẫn còn tồn tại sau khi đã được sửa
   - **MÔ TẢ CHI TIẾT nội dung của từng ảnh đính kèm** và **HIGHLIGHT các vấn đề chính** liên quan đến lỗi
   - **TRÍCH DẪN ĐẦY ĐỦ nội dung các comment quan trọng**, đặc biệt là comment reopen
   - Tìm hiểu code liên quan đến lỗi
   - Xác định nguyên nhân gốc rễ của vấn đề
   - **PHÂN TÍCH CÁC PHẢN HỒI TỪ NGƯỜI KIỂM TRA** để hiểu chính xác những điểm nào vẫn chưa được giải quyết

2. **ĐỀ XUẤT GIẢI PHÁP**:

   - Trình bày rõ ràng các giải pháp có thể áp dụng
   - Nêu ưu nhược điểm của từng giải pháp (nếu có nhiều giải pháp)
   - Đề xuất giải pháp tối ưu nhất
   - Mô tả chi tiết các thay đổi cần thực hiện
   - **ĐẢM BẢO giải pháp đề xuất xử lý TOÀN BỘ các vấn đề đã được nêu trong comment reopen**

3. **CHỜ PHÊ DUYỆT**:

   - Trình bày phân tích và giải pháp cho người dùng
   - CHỜ người dùng XÁC NHẬN hoặc YÊU CẦU ĐIỀU CHỈNH
   - KHÔNG tiến hành sửa code cho đến khi được phê duyệt

4. **THỰC HIỆN SỬA LỖI**:

   - Chỉ sau khi được phê duyệt, mới tiến hành sửa code
   - Tuân thủ đúng giải pháp đã được phê duyệt
   - Thực hiện các thay đổi một cách cẩn thận và có kiểm soát
   - **ĐẢM BẢO sửa lỗi xử lý TẤT CẢ các trường hợp đã được nêu trong comment reopen**

5. **KIỂM TRA VÀ BÁO CÁO**:
   - Kiểm tra kỹ lưỡng các thay đổi đã thực hiện
   - **KIỂM TRA với TẤT CẢ các trường hợp được nêu trong comment reopen**
   - Báo cáo kết quả và các thay đổi đã thực hiện
   - Cập nhật trạng thái trong memory bank

### Lưu ý quan trọng:

- **TUYỆT ĐỐI KHÔNG** tự ý sửa code khi chưa phân tích và được phê duyệt
- **KHÔNG BAO GIỜ** bỏ qua bước phân tích và đề xuất giải pháp
- **KHÔNG BAO GIỜ** đưa ra giải pháp mơ hồ hoặc không đầy đủ
- **PHẢI** phân tích kỹ lưỡng tất cả các khía cạnh của vấn đề
- **PHẢI** trình bày giải pháp một cách rõ ràng, súc tích và dễ hiểu
- **PHẢI** tuân thủ quy trình này cho MỌI yêu cầu sửa lỗi, không có ngoại lệ
- **PHẢI** phân tích đầy đủ TOÀN BỘ file đính kèm và comment trong ticket Jira, đặc biệt là comment reopen
- **KHÔNG BAO GIỜ** bỏ qua các file đính kèm hoặc comment reopen khi phân tích lỗi

## QUY TẮC PHÁT TRIỂN FRONTEND

Khi phát triển các tính năng frontend, PHẢI tuân thủ các quy tắc sau:

1. **ƯU TIÊN SỬA RULE MEMORY BANK VÀ CURSOR RULES** - Khi có yêu cầu sửa rule, PHẢI đặt việc này lên ưu tiên hàng đầu trước khi thực hiện các tác vụ khác. PHẢI kiểm tra và cập nhật CẢ HAI nơi: memory bank và cursor rules nếu cần thiết. **TUYỆT ĐỐI KHÔNG** được chỉ cập nhật một nơi mà bỏ qua nơi còn lại.

2. **TUÂN THỦ THIẾT KẾ FIGMA 100%** - Xem chi tiết tại phần **QUY TẮC TUYỆT ĐỐI TUÂN THỦ FIGMA 100%**

3. **SỬA LỖI LINTER, TYPE CHECKING, COMPILE, RUNTIME** - Xem chi tiết tại phần **QUY TẮC SỬA LỖI LINTER, TYPE CHECKING, COMPILE, RUNTIME**

4. **TUÂN THỦ DESIGN SYSTEM**:

   - Sử dụng Ant Design làm thư viện UI chính
   - Kết hợp với Tailwind CSS cho các style tùy chỉnh
   - **KHÔNG SỬ DỤNG FILE CSS RIÊNG BIỆT** - Không tạo file CSS riêng biệt (.css)
   - Khi cần style phức tạp, sử dụng styled-components hoặc CSS-in-JS

5. **ĐẢM BẢO RESPONSIVE** - Thiết kế phải hoạt động tốt trên tất cả các thiết bị

6. **PHÂN TÍCH LỖI TRƯỚC KHI SỬA CODE** - Xem chi tiết tại phần **QUY TRÌNH PHÂN TÍCH VÀ SỬA LỖI**

7. **TUÂN THỦ TYPESCRIPT** - Sử dụng TypeScript cho tất cả các component và function mới

8. **TÁCH BIỆT LOGIC VÀ UI** - Tạo các components có thể tái sử dụng, tách biệt logic và giao diện

### Quy tắc code quality bắt buộc:

- **LUÔN THÊM** key prop cho các phần tử trong array
- **KHÔNG ĐỂ** biến được khai báo nhưng không sử dụng
- **TUÂN THỦ** quy ước đặt tên: camelCase cho biến và hàm, PascalCase cho component và interface
- **LUÔN THÊM** directive "use client" cho các client components sử dụng React hooks

## Quy tắc MCP Server

### Quy tắc cấu trúc mã nguồn MCP

Để duy trì mã nguồn rõ ràng và dễ bảo trì, cần tuân thủ các quy tắc quan trọng sau:

1. **KHÔNG BAO GIỜ THÊM LÔ-GIC XỬ LÝ MỚI TRỰC TIẾP VÀO index.js** - index.js chỉ nên là điểm vào cho các modules khác
2. **TUYỆT ĐỐI KHÔNG VIẾT HÀM MỚI TRONG index.js** - Tất cả hàm xử lý phải được đặt trong các module riêng biệt (handlers, utils, api, v.v.)
3. **KHÔNG TẠO FILE README.md HOẶC BẤT KỲ TÀI LIỆU NÀO TRONG THƯ MỤC temp** - thư mục temp chỉ dùng để lưu tạm file và sẽ được xóa
4. **ĐỌC KỸ MÃ NGUỒN HIỆN CÓ TRƯỚC KHI TẠO HÀM MỚI** - luôn tìm hiểu xem đã có hàm tương tự hay chưa và tái sử dụng
5. **TRÁNH TẠO HÀM TRÙNG LẶP HOẶC TƯƠNG TỰ** - nếu phát hiện hàm đã tồn tại với chức năng tương tự, hãy sử dụng lại
6. **PHẢI HIỂU RÕ CẤU TRÚC VÀ CHỨC NĂNG CỦA CODE** - đọc kỹ các module để nắm rõ cách hệ thống hoạt động
7. Khi cần thêm tính năng mới, PHẢI tạo hoặc sửa các hàm trong module tương ứng, KHÔNG THÊM vào index.js
8. Khi hiển thị hướng dẫn hoặc trợ giúp, LUÔN TÌM VÀ SỬ DỤNG các hàm hiện có, KHÔNG TẠO hàm tương tự mới
9. Chức năng xử lý lệnh đã được mô-đun hóa, mỗi loại lệnh có module xử lý riêng, nên tìm hiểu và sử dụng lại
10. File đính kèm nên được lưu tạm vào thư mục temp và TỰ ĐỘNG XÓA sau khi xử lý, KHÔNG ĐỂ LẠI file tài liệu nào
11. **TUÂN THỦ NGHIÊM NGẶT NGUYÊN TẮC TRÁCH NHIỆM ĐƠN LẺ** - mỗi module chỉ chịu trách nhiệm cho một loại chức năng
12. Mọi thay đổi phải duy trì TÍNH NHẤT QUÁN của cấu trúc code, pattern, và quy ước đặt tên
13. **SỬ DỤNG CÁC MODULE CHUYÊN BIỆT** - Sử dụng formatter.js cho định dạng đầu ra, system.js cho chức năng hệ thống, analyzer.js cho phân tích file

### LUÔN TỰ ĐỘNG KẾT NỐI MCP SERVER VÀ ĐỌC MCP TRƯỚC MEMORY BANK KHI CÓ URL

PHẢI tự động kết nối MCP Server khi phát hiện URL, và trong trường hợp lệnh VAN chứa URL thì PHẢI ưu tiên kết nối MCP trước khi đọc memory bank:

1. Ngay khi nhận tin nhắn có chứa URL từ wiki.vnpt-technology.vn, jira.vnpt-technology.vn, figma.com hoặc mã Jira, PHẢI tự động chạy MCP Server để lấy thông tin
2. Trong trường hợp nhận lệnh VAN có URL, PHẢI làm theo thứ tự: (1) kết nối MCP, (2) đọc memory bank
3. Không cần thông báo về việc kết nối MCP Server, chỉ cần thực hiện ngay
4. Sau khi lấy thông tin từ MCP Server, mới đọc memory bank và tiếp tục quy trình VAN thông thường
5. PHẢI ưu tiên tốc độ và hiệu quả, không lãng phí token cho các giải thích hoặc thông báo không cần thiết

### TỐI ƯU HÓA VIỆC CHẠY LỆNH MCP SERVER

PHẢI tối ưu số lượng lệnh cần chạy khi làm việc với MCP Server:

1. **TUYỆT ĐỐI QUAN TRỌNG: CHỈ CHẠY MỘT LỆNH DUY NHẤT cho mỗi loại URL (Wiki/Jira) với TẤT CẢ tham số cần thiết**
2. **KHÔNG BAO GIỜ** chạy nhiều lệnh liên tiếp hoặc riêng lẻ cho cùng một URL
3. **KHÔNG BAO GIỜ** tách các tham số thành các lệnh riêng biệt
4. Với URL Wiki: Chỉ chạy `node .mcp/index.js wiki URL` một lần duy nhất (đã được cấu hình để tự động trả về thông tin đầy đủ)
5. Với URL Jira: Chỉ chạy `node .mcp/index.js jira ISSUE_KEY` một lần duy nhất (đã được cấu hình để lấy cả thông tin và đính kèm)
6. Nếu URL có tham số node-id, **PHẢI tự động trích xuất** và thêm vào trong lệnh, KHÔNG CHẠY LỆNH RIÊNG
7. Nếu cần xử lý nhiều loại URL khác nhau (như vừa có Wiki vừa có Jira), chạy riêng biệt MỘT LỆNH cho mỗi loại
8. Sau khi chạy lệnh, PHẢI kiểm tra kết quả trước khi quyết định chạy bất kỳ lệnh bổ sung nào - chỉ chạy thêm nếu thực sự cần thiết
9. KHÔNG BAO GIỜ thử các flag khác nhau nếu lệnh đầu tiên đã trả về kết quả
10. **CHỈ KHI LỆNH THẤT BẠI và không trả về thông tin cần thiết**, mới được phép thử với cấu hình khác
11. Phải tự động phân tích URL Figma và trích xuất các tham số như node-id từ URL, tích hợp vào lệnh MCP
12. **LUÔN KIỂM TRA THÔNG TIN** trong URL trước khi chạy lệnh để đảm bảo tất cả tham số cần thiết đã được bao gồm

# Ngữ cảnh hiện tại

## Công việc hiện tại

- Đã cập nhật quy tắc cấu trúc thư mục và tổ chức code trong file systemPatterns.md
- Đã tạo tính năng tra cứu bảo hành theo đúng cấu trúc:
  - View: `src/views/warranty-lookup/WarrantyLookup.tsx`
  - Page: `src/app/(sme-portal)/warranty-lookup/page.tsx`

## Quy tắc cấu trúc thư mục

Dự án tuân theo cấu trúc tổ chức code rõ ràng, phân tách các thành phần theo chức năng và trách nhiệm:

1. **src/app**: Chứa các trang Next.js (pages) và routing
2. **src/views**: Chứa các màn hình hoặc trang với logic nghiệp vụ cụ thể
3. **src/components**: Chứa các thành phần UI có thể tái sử dụng

Khi tạo tính năng mới, cần tuân thủ các bước sau:

1. **Tạo View**:

   - Tạo file trong thư mục `src/views/[feature-name]`
   - Đặt tên file theo PascalCase: `FeatureName.tsx`

2. **Tạo Page**:

   - Tạo file trong thư mục `src/app/[route]/page.tsx`
   - Import view đã tạo và sử dụng trong page

3. **Tạo Components (nếu cần)**:
   - Tạo các components cần thiết trong `src/components/[feature-name]`
   - Đảm bảo components có thể tái sử dụng

## Quyết định quan trọng

- Đã quyết định cập nhật và làm rõ quy tắc cấu trúc thư mục trong dự án để đảm bảo tính nhất quán
- Đã thống nhất quy trình tạo tính năng mới theo cấu trúc chuẩn

## Các vấn đề đang gặp phải

- Cần đảm bảo tất cả các thành viên trong team tuân thủ quy tắc cấu trúc thư mục đã cập nhật

## Kế hoạch tiếp theo

- Tiếp tục phát triển các tính năng mới theo đúng cấu trúc đã quy định
- Cân nhắc việc tạo một script kiểm tra cấu trúc code để đảm bảo tuân thủ quy tắc

## QUY TẮC TỔ CHỨC CODE FE (Frontend) - BỔ SUNG

- Nếu là tính năng đặc thù của một module (ví dụ: iot-portal, sme-portal, partner-portal...), **components, views phải để trong thư mục module đó**:
  - `src/components/iot-portal/[feature]/...`
  - `src/views/iot-portal/[feature]/...`
  - KHÔNG tạo thư mục feature riêng ngoài module.
- Nếu là tính năng dùng chung cho nhiều module, mới đặt ngoài module.
- Nếu là logic đặc thù module, phải để đúng folder module.

#### Ví dụ minh họa:

```
src/
  components/
    iot-portal/
      partner-register/
        PartnerBanner.tsx
        PartnerRegisterForm.tsx
  views/
    iot-portal/
      partner-register/
        PartnerRegisterView.tsx
  app/
    (iot-portal)/
      iot-portal/
        partner/
          register/
            page.tsx
```

## Cập nhật quy tắc phát triển UI từ Figma

- Đã cập nhật rule: Khi phát triển UI từ Figma, PHẢI tự động lấy ảnh về, lưu đúng thư mục `public/assets/images/pages/[module]/[feature]/`, đặt tên rõ ràng, không dấu, không khoảng trắng.
- UI chỉ dùng ảnh local đã lấy từ Figma, không dùng ảnh ngoài.

# QUY TẮC TỐI ƯU HÓA SỬ DỤNG TAILWIND CSS

## TUYỆT ĐỐI KHÔNG DÙNG MÀU TÙY CHỈNH

- **KHÔNG BAO GIỜ** sử dụng cú pháp `text-[#HEX]` hoặc `bg-[#HEX]` để định nghĩa màu
- **LUÔN LUÔN** sử dụng các lớp màu đã được định nghĩa trong theme của dự án:
  - Màu chữ: `text-gray-8`, `text-gray-11`, `text-bright-blue-9`, v.v.
  - Màu nền: `bg-blue-1`, `bg-gray-1`, `bg-white`, v.v.
  - Màu viền: `border-gray-3`, `border-blue-5`, v.v.

## QUY TRÌNH THÊM MÀU MỚI VÀO TAILWIND CONFIG

Nếu cần sử dụng màu mới chưa có trong theme, PHẢI tuân thủ quy trình sau:

1. **KIỂM TRA TRƯỚC** xem màu đã tồn tại trong tailwind.config.ts chưa
2. **THÊM MÀU MỚI** vào phần `baseColorLight` trong tailwind.config.ts:
   ```typescript
   const baseColorLight = {
     // Các màu hiện có...
     'tên-màu-mới': '#HEX_CODE'
   }
   ```
3. **ĐẶT TÊN THEO QUY ƯỚC**:
   - Tuân thủ quy ước đặt tên hiện tại: `[tên-màu]-[cấp độ]`
   - Ví dụ: `light-blue-1`, `gray-8`, `bright-blue-9`
4. **CẬP NHẬT TÀI LIỆU**:
   - Cập nhật memory-bank/activeContext.md
   - Cập nhật .cursor/rules/mcp_rules.mdc
   - Thêm màu mới vào bảng tham chiếu màu thường dùng
5. **THAY THẾ MÀU TÙY CHỈNH** bằng lớp Tailwind mới trong toàn bộ dự án

## BẢNG THAM CHIẾU MÀU THƯỜNG DÙNG

### Màu chữ

- `#0F1319` → `text-gray-11`
- `#394867` → `text-gray-8` hoặc `text-text-neutral-medium`
- `#0A6FD0` → `text-bright-blue-9`
- `rgba(2,23,60,0.25)` → `text-gray-4`

### Màu nền

- `#F2F4F9` → `bg-gray-1`
- `#D9ECFF` → `bg-blue-1`
- `#EBF5FE` → `bg-light-blue-1`

### Quy tắc chung

1. **PHẢI** kiểm tra theme của dự án trước khi sử dụng màu tùy chỉnh
2. **PHẢI** sử dụng các lớp Tailwind có sẵn để đảm bảo tính nhất quán
3. **KHÔNG ĐƯỢC** sử dụng màu tùy chỉnh trừ khi không có lớp tương đương trong theme
4. **PHẢI** đề xuất thêm màu vào theme nếu cần sử dụng màu mới thường xuyên
5. **TUYỆT ĐỐI KHÔNG** để lại màu tùy chỉnh trong code sau khi đã thêm màu vào theme

## QUY TRÌNH KIỂM TRA TRƯỚC KHI COMMIT

1. Chạy linter để phát hiện các cảnh báo về việc sử dụng màu tùy chỉnh
2. Thay thế tất cả các màu tùy chỉnh bằng các lớp Tailwind tương đương
3. Kiểm tra lại giao diện để đảm bảo màu sắc vẫn đúng sau khi thay đổi
