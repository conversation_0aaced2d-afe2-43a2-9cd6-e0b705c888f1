{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  // Next.js debugging https://nextjs.org/docs/pages/building-your-application/configuring/debugging
  "version": "0.3.0",
  "configurations": [
    {
      "type": "node",
      "request": "launch",
      "name": "Next.js Dev",
      "program": "${workspaceFolder}/node_modules/next/dist/bin/next",
      "args": [
        "dev"
      ],
      "cwd": "${workspaceFolder}",
      "skipFiles": [
        "<node_internals>/**"
      ],
      "resolveSourceMapLocations": [
        "${workspaceFolder}/**",
        "!**/node_modules/**"
      ],
      "serverReadyAction": {
        "action": "debugWithEdge",
        "pattern": "listening on port ([0-9]+)",
        "uriFormat": "http://localhost:%s",
        "webRoot": "${workspaceFolder}",
        "killOnServerStop": false
      },
      "preLaunchTask": "npm: install"
    },
    {
      "name": "Next.js: debug client-side",
      "type": "chrome",
      "request": "launch",
      "url": "http://localhost:3000"
    },
    // {
    //   "name": "Next.js: debug server-side",
    //   "type": "node-terminal",
    //   "request": "launch",
    //   "command": "npm run dev"
    // },
    {
      "name": "Run npm build and start",
      "type": "node-terminal",
      "request": "launch",
      "command": "npm start",
      "preLaunchTask": "npm: install & npm: build"
    },
  ]
}
