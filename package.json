{"name": "onesme", "version": "3.0.0", "license": "Commercial", "private": true, "scripts": {"dev": "(yarn lint || yarn lint:fix) && yarn check-types && next dev --turbopack", "dev:devonedx": "env-cmd -f .env.production.devonedx yarn dev", "dev:devonesme": "env-cmd -f .env.production.devonesme yarn dev", "dev:onesme": "env-cmd -f .env.production.onesme yarn dev", "dev:stagingonedx": "env-cmd -f .env.production.stagingonedx yarn dev", "dev:production": "env-cmd -f .env.production yarn dev", "dev:bos": "env-cmd -f .env.production.bos yarn dev", "build": "next build", "build:devonedx": "env-cmd -f .env.production.devonedx yarn build", "build:devonesme": "env-cmd -f .env.production.devonesme yarn build", "build:onesme": "env-cmd -f .env.production.onesme yarn build", "build:stagingonedx": "env-cmd -f .env.production.stagingonedx yarn build", "build:production": "env-cmd -f .env.production yarn build", "build:bos": "env-cmd -f .env.production.bos yarn build", "build:icons": "tsx src/assets/iconify-icons/bundle-icons-css.ts", "start": "next start", "start:standalone": "cp -r .next/static .next/standalone/.next/static && cp -r public .next/standalone/public && node .next/standalone/server.js", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx}\"", "postinstall": "npm run build:icons", "prepare": "husky", "optimize-images": "python scripts/optimize_images.py && yarn format --list-different", "check-types": "tsc --noEmit", "analyze": "cross-env NEXT_PUBLIC_ANALYZE=true next build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx,json,css,scss,md}": ["prettier --write --ignore-unknown", "eslint"]}, "dependencies": {"@ant-design/charts": "^2.2.1", "@ant-design/icons": "^5.5.1", "@ant-design/nextjs-registry": "^1.0.2", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@ckeditor/ckeditor5-build-classic": "38.1.1", "@ckeditor/ckeditor5-react": "^9.5.0", "@ckeditor/ckeditor5-upload": "^45.2.0", "@craftjs/core": "^0.2.12", "@craftjs/layers": "^0.2.6", "@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@egjs/react-flicking": "^4.11.4", "@emotion/cache": "11.14.0", "@emotion/react": "11.14.0", "@emotion/styled": "11.14.0", "@floating-ui/react": "0.27.2", "@hello-pangea/dnd": "^18.0.1", "@mui/lab": "6.0.0-beta.31", "@mui/material": "6.4.8", "@mui/material-nextjs": "6.4.3", "@next/third-parties": "15.3.2", "@react-pdf-viewer/core": "^3.12.0", "@react-pdf-viewer/default-layout": "^3.12.0", "@react-spring/web": "^9.7.5", "@reduxjs/toolkit": "^2.2.7", "@socialgouv/matomo-next": "^1.9.1", "@tanstack/react-query": "^5.51.18", "@tanstack/react-query-devtools": "^5.51.18", "@types/dompurify": "^3.0.5", "@types/react-google-recaptcha": "^2.1.9", "@types/react-slider": "^1.3.6", "@xyflow/react": "^12.8.2", "antd": "^5.24.4", "apexcharts": "^3.54.1", "axios": "^1.9.0", "canvas": "^3.1.0", "classnames": "2.5.1", "compression-webpack-plugin": "^11.1.0", "cookies-next": "^4.2.1", "cross-env": "^7.0.3", "debounce": "^2.2.0", "dompurify": "^3.1.6", "env-cmd": "^10.1.0", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "firebase": "^10.14.0", "html-entities": "^2.6.0", "html-to-image": "^1.11.13", "html2canvas-pro": "^1.5.8", "isomorphic-dompurify": "^2.14.0", "js-cookie": "^3.0.5", "jszip-utils": "^0.1.0", "lodash": "^4.17.21", "moment": "^2.30.1", "next": "15.3.2", "next-nprogress-bar": "^2.4.7", "nprogress": "^0.2.0", "pdfjs-dist": "^3.11.174", "public-ip": "^6.0.2", "re-resizable": "^6.10.0", "react": "19.1.0", "react-apexcharts": "^1.4.1", "react-card-flip": "^1.2.3", "react-colorful": "5.6.1", "react-dom": "19.1.0", "react-google-recaptcha": "^3.1.0", "react-html-parser": "^2.0.2", "react-infinite-scroll-component": "^6.1.0", "react-intersection-observer": "^9.13.1", "react-otp-input": "^3.1.1", "react-perfect-scrollbar": "1.5.8", "react-player": "^2.16.0", "react-redux": "^9.1.2", "react-slider": "^2.0.6", "react-tiff": "^0.0.11", "react-use": "17.6.0", "react-use-draggable-scroll": "^0.4.7", "server-only": "0.0.1", "sharp": "^0.33.5", "styled-components": "^6.1.12", "superagent": "^10.2.0", "swiper": "^11.1.8", "terser-webpack-plugin": "^5.3.10", "uuid": "^8.3.2", "webpack-bundle-analyzer": "^4.10.2", "xlsx": "^0.18.5"}, "devDependencies": {"@commitlint/cli": "^19.3.0", "@commitlint/config-conventional": "^19.2.2", "@iconify/json": "2.2.286", "@iconify/tools": "4.1.1", "@iconify/types": "2.0.0", "@iconify/utils": "2.2.1", "@next/bundle-analyzer": "15.3.2", "@types/file-saver": "^2.0.7", "@types/lodash": "^4.17.7", "@types/node": "^22.10.2", "@types/nprogress": "^0.2.3", "@types/react": "19.1.4", "@types/react-dom": "19.1.5", "@types/react-html-parser": "^2.0.6", "@types/styled-components": "^5.1.34", "@types/superagent": "^8.1.9", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "7.18.0", "@typescript-eslint/parser": "7.18.0", "autoprefixer": "10.4.20", "cheerio": "^1.0.0", "critters": "^0.0.24", "crypto": "^1.0.1", "css-minimizer-webpack-plugin": "^7.0.0", "eslint": "8.57.1", "eslint-config-next": "15.3.2", "eslint-config-prettier": "9.1.0", "eslint-import-resolver-typescript": "3.7.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-tailwindcss": "^3.17.4", "eslint-plugin-unused-imports": "^4.1.3", "figma-developer-mcp": "^0.4.0", "husky": "^9.1.1", "lint-staged": "^15.2.7", "postcss": "8.4.49", "postcss-styled-syntax": "0.7.0", "prettier": "3.4.2", "sass": "^1.77.8", "stylelint": "16.12.0", "stylelint-use-logical-spec": "5.0.1", "stylis": "4.3.4", "stylis-plugin-rtl": "2.1.1", "tailwindcss": "3.4.17", "tailwindcss-logical": "3.0.1", "tsx": "4.19.2", "typescript": "5.5.4", "workbox-webpack-plugin": "^7.1.0"}, "resolutions": {"rimraf": "^5.0.7", "@types/react": "19.1.4", "@types/react-dom": "19.1.5"}, "overrides": {"rimraf": "^5.0.7", "@types/react": "19.1.4", "@types/react-dom": "19.1.5"}}