# Hướng dẫn sử dụng Page Builder

## Giới thiệu

Page Builder là công cụ cho phép người dùng tạo và tùy chỉnh giao diện trang web một cách trực quan thông qua giao diện kéo thả, không cần kiến thức lập trình. Công cụ này giúp bạn dễ dàng thiết kế các trang web chuyên nghiệp với nhiều thành phần đa dạng.

## Truy cập Page Builder

Để truy cập Page Builder, bạn có thể vào một trong các đường dẫn sau:

- `/page-builder/editor/home` - Chỉnh sửa trang chủ
- `/page-builder/editor/enterprise` - Chỉnh sửa trang doanh nghiệp
- `/page-builder/editor/house-hold` - Chỉnh sửa trang hộ gia đình
- `/page-builder/editor/personal` - Chỉnh sửa trang cá nhân
- `/page-builder/editor/[id]` - Chỉnh sửa trang theo ID

## Quy trình cập nhật layout và content

### Quan trọng: Quy trình cập nhật giao diện

1. **Truy cập vào trang editor**:

   - Truy cập vào `http://localhost:3000/page-builder/editor/home` (hoặc đường dẫn tương ứng với loại trang)
   - Đây là nơi bạn có thể kiểm tra và cập nhật layout content

2. **Chỉnh sửa và lưu layout**:

   - Thực hiện các thay đổi cần thiết trên giao diện editor
   - Nhấn nút "Lưu" để lưu các thay đổi

3. **Cập nhật API**:

   - Sau khi lưu, API sẽ được cập nhật và trả về content mới
   - **Lưu ý quan trọng**: Chỉ khi API đã được cập nhật, người dùng mới có thể thấy giao diện mới khi truy cập vào `/home`

4. **Kiểm tra giao diện mới**:
   - Truy cập vào `/home` (hoặc đường dẫn tương ứng) để xem giao diện mới đã được áp dụng
   - Đảm bảo mọi thay đổi đều hiển thị đúng như mong đợi

### Lưu ý về quy trình cập nhật

- Quy trình này áp dụng cho tất cả các loại trang (home, enterprise, house-hold, personal)
- Cần đảm bảo API đã được cập nhật thành công trước khi thông báo hoàn thành việc thay đổi giao diện
- Nếu giao diện mới không hiển thị khi truy cập trang, có thể API chưa được cập nhật hoặc có lỗi trong quá trình cập nhật

## Giao diện chính

Giao diện Page Builder gồm 3 phần chính:

1. **Thanh công cụ (bên trái)**: Chứa các thành phần có thể kéo thả vào trang.
2. **Khu vực chỉnh sửa (giữa)**: Hiển thị giao diện trang đang được chỉnh sửa.
3. **Thanh thuộc tính (bên phải)**: Hiển thị khi chọn một thành phần, cho phép tùy chỉnh thuộc tính của thành phần đó.

## Các thao tác cơ bản

### 1. Thêm thành phần vào trang

- Từ thanh công cụ bên trái, chọn thành phần bạn muốn thêm vào trang.
- Kéo và thả thành phần vào vị trí mong muốn trên trang.
- Hoặc nhấp vào thành phần để thêm vào vị trí đang được chọn.

### 2. Chỉnh sửa thành phần

- Nhấp vào thành phần trên trang để chọn.
- Sử dụng thanh thuộc tính bên phải để tùy chỉnh các thuộc tính của thành phần.
- Các thuộc tính có thể bao gồm: kích thước, màu sắc, nội dung, liên kết, v.v.

### 3. Sắp xếp và định vị thành phần

- Sử dụng các thành phần Row (hàng) và Column (cột) để tạo bố cục cho trang.
- Kéo và thả các thành phần để thay đổi vị trí.
- Sử dụng Spacer (khoảng trắng) để tạo khoảng cách giữa các thành phần.

### 4. Xem trước và lưu

- Nhấp vào nút "Xem trước" để xem trang web như người dùng cuối sẽ thấy.
- Nhấp vào nút "Lưu" để lưu các thay đổi của bạn.

## Các thành phần có sẵn

### Thành phần cấu trúc

- **Row (Hàng)**: Tạo một hàng ngang để chứa các thành phần khác.
- **Column (Cột)**: Tạo cột trong một hàng để chia bố cục.
- **Spacer (Khoảng trắng)**: Tạo khoảng cách giữa các thành phần.

### Thành phần nội dung

- **Text (Văn bản)**: Thêm và định dạng văn bản.
- **Button (Nút)**: Thêm nút với hành động tùy chỉnh.
- **Image (Ảnh)**: Thêm hình ảnh vào trang.
- **Video**: Thêm video từ YouTube hoặc nguồn khác.
- **Image List (Danh sách ảnh)**: Hiển thị nhiều hình ảnh theo dạng lưới.
- **Banner List (Danh sách banner)**: Hiển thị các banner quảng cáo.

### Thành phần tương tác

- **Menu Bar (Thanh menu)**: Tạo menu điều hướng.
- **Dropdown Menu (Menu thả xuống)**: Tạo menu có các mục con.
- **Input Form (Biểu mẫu nhập liệu)**: Tạo form thu thập thông tin.
- **Contact Form (Form liên hệ)**: Tạo form liên hệ với các trường cơ bản.
- **Survey (Khảo sát)**: Tạo biểu mẫu khảo sát.
- **Filter (Bộ lọc)**: Thêm bộ lọc cho danh sách sản phẩm/dịch vụ.

### Thành phần mạng xã hội

- **Social Media (Mạng xã hội)**: Thêm các biểu tượng mạng xã hội với liên kết.

### Thành phần sản phẩm/dịch vụ

- **Product Service Category (Danh mục SPDV)**: Hiển thị danh mục sản phẩm/dịch vụ.
- **List Products (Danh sách SPDV)**: Hiển thị danh sách sản phẩm/dịch vụ.
- **Information Card (Thẻ thông tin)**: Hiển thị thông tin dưới dạng thẻ.

### Thành phần khác

- **Icon Library (Thư viện Icon)**: Thêm biểu tượng từ thư viện icon.

## Tùy chỉnh cho từng loại màn hình

Page Builder hỗ trợ thiết kế responsive cho các loại màn hình khác nhau:

1. Sử dụng nút chọn loại màn hình ở thanh công cụ để chuyển đổi giữa các chế độ:

   - **Desktop**: Thiết kế cho màn hình máy tính.
   - **Tablet**: Thiết kế cho máy tính bảng.
   - **Mobile**: Thiết kế cho điện thoại di động.

2. Mỗi thành phần có thể có các thuộc tính khác nhau cho từng loại màn hình, giúp trang web hiển thị tối ưu trên mọi thiết bị.

## Cập nhật template

Để cập nhật template (chỉ có sẵn ở môi trường dev):

1. Đảm bảo đã cập nhật template vào các file JSON và commit lên git.
2. Nhấp vào nút "Cập nhật template".
3. Đăng nhập vào môi trường muốn cập nhật trong cửa sổ modal.
4. Sau khi đăng nhập thành công, nhấp vào "Cập nhật" để cập nhật tất cả template cùng một lúc.

### Thông tin về API cập nhật template

```
URL: {{service_local}}/api/admin-portal/page-builder/internal/update-page-builder
Type: PUT

Body:
{
  "id": 8, // id của page_builder or builder_template (tương ứng với trường type)
  "type": "TEMPLATE_CONTENT", // PAGE_CONTENT (page_builder) or TEMPLATE_CONTENT (builder_template)
  "content": {{contentString}} // dùng postman test thì insert body ở script pre-request (script là stringify JSON trong javascript trước khi truyền vào)
}

// response trả về có các trường:
    // "id": là id của request,
    // "type": là type của request,
    // "oldContent": là page content của id TRƯỚC khi bị update, dùng oldContent để rollback nếu bị update nhầm
    // "newContent": là content của request
```

### IDs tương ứng với môi trường IT

**PAGE_CONTENT**

- id home: 158
- id KHDN: 198
- id HKD: 199
- id CN: 200

**TEMPLATE_CONTENT**

- id home: 5
- id KHDN: 6
- id HKD: 7
- id CN: 8

### IDs tương ứng với môi trường Staging

**PAGE_CONTENT**

- id home: 257
- id KHDN: 258
- id HKD: 259
- id CN: 260

**TEMPLATE_CONTENT**

- id home: 5
- id KHDN: 6
- id HKD: 7
- id CN: 8

## Khôi phục về mặc định

Nếu bạn muốn khôi phục trang về thiết kế mặc định:

1. Nhấp vào nút "Trở về mặc định" trong thanh công cụ.
2. Xác nhận hành động khi được hỏi.
3. Hệ thống sẽ khôi phục trang về thiết kế mặc định tương ứng với loại trang (home, enterprise, house-hold, personal).

## Xem trước trang

Để xem trước trang đã thiết kế:

1. Truy cập đường dẫn `/page-builder/preview/[id]` với [id] là ID của trang.
2. Trang sẽ hiển thị như người dùng cuối sẽ thấy, không có các công cụ chỉnh sửa.

## Responsive

Page Builder hỗ trợ thiết kế responsive thông qua:

- **screenType**: Được lưu trữ trong Redux, xác định loại màn hình đang được chỉnh sửa
- Mỗi component có props gồm 3 key: desktop, tablet, mobile với giá trị tương ứng cho từng loại màn hình
- Sử dụng defaultProps là một hàm cho phép truyền vào screenType
- Tạo biến defaultResponseProps gồm 3 trường desktop, tablet, mobile là defaultProps
- Gán vào defaultProps của component và truyền vào khai báo ở Toolbar khi cần

## Lưu ý quan trọng

- Luôn nhớ lưu các thay đổi của bạn trước khi rời khỏi trang.
- Sử dụng các thành phần Row và Column để tạo bố cục responsive.
- Kiểm tra trang web trên các loại màn hình khác nhau trước khi xuất bản.
- Không nên thêm quá nhiều thành phần vào một trang, điều này có thể làm chậm tải trang.
- **Quan trọng**: Sau khi cập nhật layout trong editor, phải đảm bảo API đã được cập nhật thành công để giao diện mới hiển thị khi người dùng truy cập trang.

## Liên hệ hỗ trợ

Nếu bạn gặp vấn đề hoặc cần hỗ trợ thêm về Page Builder, vui lòng liên hệ với đội ngũ hỗ trợ kỹ thuật.

---

Tài liệu này cung cấp hướng dẫn cơ bản để sử dụng tính năng Page Builder. Khi làm quen với công cụ, bạn sẽ có thể tạo ra các trang web đẹp mắt và chuyên nghiệp một cách dễ dàng.
