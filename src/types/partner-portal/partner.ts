import type { IPagination } from '../paginationTypes'

// #region constants types
export type PartnerStatus = 'ACTIVE' | 'INACTIVE'

export type CustomerType = 'PERSONAL' | 'ENTERPRISE'

export type PortalType = 'DEV' | 'SME' // NOTE: Không chắc chắn về các giá trị khác

export type Gender = 'MALE' | 'FEMALE' | 'OTHER'

export type CertificateType = 'CCCD' | 'CMTND_CCCD' | 'PASSPORT' | 'OTHER' | 'ALL'

export type DocumentType = 'CONTRACT' | 'LICENSE' | 'CERTIFICATE' // NOTE: Không chắc chắn về các loại khác

export type CapabilityType =
  | 'EQUIPMENT'
  | 'INSTALLATION_SERVICE'
  | 'MAINTENANCE_SERVICE'
  | 'SHIPPING_SERVICE'
  | 'SOFTWARE'
  | 'IOT_CONSULTING'

// #endregion

// #region Business Logic Types
/**
 * Thông tin địa bàn hoạt động
 */
export interface OperatingArea {
  provinceId: number
  wardId: number
  streetId: number
  provinceName: string
  wardName: string
  streetName: string
}

/**
 * Thông tin người đại diện
 */
export interface Representative {
  name: string
  position: string
  gender: Gender
  dateOfBirth: string // Format: YYYY-MM-DD
  certType: CertificateType
  certNo: string
  certIssueDate: string // Format: YYYY-MM-DD
  certIssuePlace: string
  registeredResidence: string
  currentAddress: string
}

/**
 * Thông tin tài liệu/hợp đồng
 */
export interface ContactDocument {
  fileUuid: string
  type: DocumentType
  fileName: string
  expireAt: string // Format: YYYY-MM-DD
}

/**
 * Thông tin liên hệ phụ
 */
export interface SecondaryContact {
  name?: string
  phone?: string
  email?: string
  position?: string
}

/**
 * Thông tin tài khoản ngân hàng
 */
export interface BankAccount {
  name: string
  number: string
  bank: string
  branch: string
}

/**
 * Thông tin cơ bản của đối tác
 */
export interface PartnerBasicInfo {
  partnerId?: string // Optional khi tạo mới
  code?: string
  customerType: CustomerType
  portalType?: PortalType
  status: PartnerStatus
  state?: string
  name: string
  // # Thông tin liên hệ
  phoneNumber: string
  primaryEmail: string
  officeAddress?: string // Địa chỉ văn phòng làm việc
}

export interface PartnerPersonalExtra {
  repPersonalCertType: CertificateType // Loại giấy chứng nhận cá nhân
  repPersonalCertNumber: string // Số giấy chứng nhận cá nhân
  residentAddress: string // Địa chỉ thường trú
}

export interface PartnerEnterpriseExtra {
  socialInsuranceNumber: string // Số bảo hiểm xã hội
  taxCode: string // Mã số thuế
  website?: string // Website của doanh nghiệp
  businessRegistrationAddress: string
}

/**
 * Thông tin địa chỉ chi tiết
 */
export interface AddressInfo {
  // nationId: number
  provinceId: number
  provinceCode?: string
  streetId?: number // NOTE: Có thể optional
  wardId?: number // NOTE: Có thể optional

  provinceName?: string
  wardName?: string
  streetName?: string
}

type PartnerExtraInfo = {
  partnerId?: number // ID của đối tác, optional khi tạo mới

  // # Năng lực và Địa bàn
  // * Năng lực cung cấp
  capabilities: CapabilityType[]
  // * Địa bàn hoạt động
  operatingAreas: OperatingArea[]

  // # Thông tin người đại diện
  representative?: Representative

  // * Liên hệ phụ
  secondaryContacts?: SecondaryContact[]

  // # Hợp đồng & tài liệu
  contracts?: ContactDocument[]

  // # Thông tin tài chính & ngân hàng
  // * Tài Khoản ngân hàng
  bankAccount?: BankAccount
}

type PartnerAccount = {
  email: string
  password: string
  welcomeEmail?: boolean
}

/**
 * Type tổng hợp thông tin đối tác
 * @template T - Loại khách hàng (PERSONAL | ENTERPRISE)
 * @description Sử dụng để định nghĩa cấu trúc dữ liệu của đối tác trong hệ thống
 * @note Type này chỉ dùng 1 trong 2 loại CustomerType
 * @see Partner dùng được cả 2 loại trên
 */
export type PartnerProfile<T extends CustomerType> = PartnerBasicInfo &
  AddressInfo &
  (T extends 'ENTERPRISE' ? PartnerEnterpriseExtra : PartnerPersonalExtra) &
  PartnerExtraInfo

/**
 * Tổng hợp thông tin chung & Pháp lý
 */
export type PartnerInfo = PartnerBasicInfo & AddressInfo & (PartnerPersonalExtra & PartnerEnterpriseExtra)
// #endregion Business Logic Type

// #region Type Đối tác
/**
 * Chi tiết Đối tác
 */
export type Partner = PartnerInfo & PartnerExtraInfo

/**
 * Type cho việc tạo mới đối tác
 */
type NotInCreateForm =
  | 'code'
  | 'state'
  | 'createdAt'
  | 'updatedAt'
  | 'createdBy'
  | 'lastUpdatedBy'
  | 'provinceName'
  | 'wardName'
  | 'streetName'
export type CreatePartnerRequest = Omit<Partner & PartnerAccount, 'partnerId' | NotInCreateForm>

/**
 * Type cho việc cập nhật thông tin đối tác
 */
export type UpdatePartnerRequest = Partial<Omit<Partner & PartnerAccount, NotInCreateForm>>

/**
 * Type cho response API khi lấy danh sách đối tác
 */
export type PartnerListResponse = IPagination<any>

/**
 * Type cho filter khi tìm kiếm đối tác
 */
export interface PartnerFilter {
  page?: number
  size?: number
  value?: string
  isEmail?: 0 | 1 | number
  isName?: 0 | 1 | number
  isPhoneNumber?: 0 | 1 | number
  customerType?: CustomerType & 'ALL'
  capabilities?: CapabilityType & 'ALL'
  taxCodes?: string & 'ALL'
  repPersonalCertNumbers?: string & 'ALL'
}

// #endregion Type Đối tác

// #region Validate Fields

// Thông tin chung & Pháp lý
export type GeneralInfoFields =
  | keyof PartnerBasicInfo
  | keyof AddressInfo
  | keyof PartnerPersonalExtra
  | keyof PartnerEnterpriseExtra

// Năng lực & Địa bàn
export type CapacityLocationFields = 'operatingAreas' | 'capabilities' | keyof OperatingArea

// Thông tin người đại diện
export type RepresentativeFields = keyof Representative

// Thông tin liên hệ
export type ContactFields =
  | keyof SecondaryContact
  | 'secondaryContacts'
  | 'primaryEmail'
  | 'phoneNumber'
  | 'officeAddress'

// Thông tin hợp đồng & tài liệu
export type ContractFields = keyof ContactDocument

// Thông tin tài chính & Ngân hàng
export type FinancialFields = keyof BankAccount | 'email' | 'password' | 'welcomeEmail'

// #endregion

export interface Log {
  id: number
  content: string
  createdAt: string
  name: string
  email: string
  actionName: string
  actorRole: string
}

export interface Contract {
  key?: string
  name: string
  type: 'CONTRACT' | 'LEGAL'
  createdBy: string | null
  createdAt: string
  expireAt: string
}

export interface RevenueInfo {
  total: number
  currentMonth: number | null
  oneMonthAgo: number | null
  twoMonthAgo: number | null
  threeMonthAgo: number | null
  samePeriodTwoMonthAgo: number | null
  samePeriodThreeMonthAgo: number | null
  samePeriodTwelveMonthAgo: number | null
  twelveMonthAgo: number | null
  samePeriodOneMonthAgo: number | null
}

export interface CustomerInfo {
  totalSub: number
  totalUser: number
  totalSolution: number
  totalService: number
}

export interface SalesDetail {
  label: string
  value: string
  rate?: string | null
}

export interface SalesInfo {
  total: string
  detail: SalesDetail[]
}
