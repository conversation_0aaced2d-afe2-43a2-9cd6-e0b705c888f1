import type { BaseCustomizeProps } from './baseTypes'

export type DisplayType = 'NAME' | 'URL' | 'ICON'

export type SocialListProps = {
  icon?: string
  name?: string | undefined
  iconColor?: string
  textColor?: string
  url?: string
  displayType?: DisplayType
}

export interface SocialMediaCustomizeProps extends BaseCustomizeProps {
  iconAlign?: string
  size?: number
  socialList?: SocialListProps[]
}
