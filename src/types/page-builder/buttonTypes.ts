import type { ButtonProps as AntButtonProps } from 'antd'

import type { BaseCustomizeProps } from './baseTypes'

export type ButtonType = 'primary' | 'default'
export type ButtonSize = NonNullable<AntButtonProps['size']>
export type ButtonStyle = 'fill' | 'outline'

export interface ButtonCustomizeProps extends BaseCustomizeProps {
  buttonType?: ButtonType
  size?: ButtonSize
  radius?: number
  href?: string
  hasIcon?: boolean
  buttonAlign?: string
  icon?: string
  iconAlign?: 'start' | 'end'
  iconColor?: string
  isButtonWorkplace?: boolean
  width?: string
  hide?: boolean
  buttonBackgroundColor?: string
  border?: string
  className?: string
  boxShadow?: string
  containerClassName?: string
  // style?: ButtonStyle
}
