import type { ReactNode } from 'react'

export interface NotificationInfoType {
  iconType?: 'ERROR' | 'WARNING' | 'SUCCESS' | 'DELETE' | 'NOTIFY' | 'ACTIVE'
  subTitle?: ReactNode
  redirectPage?: string
  textButton?: string
  typeButton?: 'primary' | 'secondary'
  title?: ReactNode
  typeEmail?: boolean
  isContact?: boolean
  isCloseModal?: boolean
  closeModalPosition?: 'first' | 'second'
  closeModalTextButton?: string
  closeModalTypeButton?: 'primary' | 'secondary'
  haveHotline?: boolean
  handleConfirm?: any
  handleConfirmUpdate?: any
  handleCancel?: any
  handleConfirmSendOTP?: any
  methodOTP?: { emailMask?: string; phoneMask?: string }
  customerType?: string
  width?: number
}
