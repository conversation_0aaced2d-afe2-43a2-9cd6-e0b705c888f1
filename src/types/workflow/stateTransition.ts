// Đ<PERSON><PERSON> nghĩa các kiểu dữ liệu cho quản lý trạng thái

import type { MessageInstance } from 'antd/es/message/interface'
import type { Node, Edge } from '@xyflow/react'

import type { CheckedState } from '@/components/filter/SettingInput'

// Kiểu dữ liệu cho form tạo/chỉnh sửa trạng thái
export interface StateFormData {
  name: string
  code: string
  colorCode: string
  typeId: string
  applyAll: boolean
  status: string
  description?: string
  displayName?: string
  objectType?: string
  typeName?: string
}

// Kiểu dữ liệu cho chi tiết trạng thái
export interface StateDetailData extends StateFormData {
  id: string
  createdAt: string
  createdBy: string
  updatedAt: string
  updatedBy: string
  objectType: string
  displayName: string
  typeName: string
}

// Kiểu dữ liệu cho danh sách trạng thái
export interface StateListItem {
  id: string
  stateName: string
  stateCode: string
  stateColor: string
  stateType: string
  targetObject: string
  isActive: boolean
  isSharedForAllObjects: boolean
  createdAt: string
  updatedAt: string
}

// Kiểu dữ liệu phân trang
export interface Pagination {
  current: number
  pageSize: number
  total: number
}

// Kiểu dữ liệu tham số lọc
export interface StateFilterParams {
  page: number
  size: number
  search: string
  isName: number
  isCode: number
  status?: boolean
  objectTypes?: string[] | string
  startTime?: string
  endTime?: string
  applyAll?: boolean
  isType?: number
}

// Kiểu dữ liệu kết quả trả về từ API danh sách
export interface StateListResponse {
  data: StateListItem[]
  pagination: Pagination
}

// Các interface cho props của components
export interface DetailStateProps {
  stateId: string
  onClose: () => void
  open: boolean
  messageApi: MessageInstance
}

export interface EditStateProps {
  stateId: string
  onSuccess?: () => void
  onCancel?: () => void
  open: boolean
  messageApi: MessageInstance
  onClose: () => void
}

// Types cho ListState
export interface StateTableItem {
  key: string
  code: string
  name: string
  type: string
  object: string
  description: string
  creator: string
  updated: string
  status: boolean
}

export interface ColumnConfig {
  key: string
  title: string
  disabled: boolean
}

export interface CheckboxOption {
  label: string
  key: string
}

// Interface cho trigger object
// Thêm interface mới cho cấu trúc rule engine
export interface IRuleEngineCondition {
  id: number
  key: string
  ifconds?: Array<{
    id: number
    key: string
    operandId: number
    operator: number
    data?: {
      value: string[]
    }
  }>
}

// Cập nhật ITrigger interface
export interface ITrigger {
  type?: 'MANUAL' | 'API' | 'WEBHOOK' | 'SCHEDULE' | 'RULE_ENGINE'
  manual?: {
    agentTypes: string[]
    roles: string[]
  }
  schedule?: any
  ruleEngine?: {
    conditions: IRuleEngineCondition[]
  }
  api?: any
  webhook?: any
  state?: any
}

// Interface cho pre-state object
export interface IPreState {
  stateId: number
  stateCode: string
  stateName: string
  triggers: ITrigger[]
}

// Types cho DetailTransitionView component
export interface ITriggerCondition {
  type: string
  ruleEngine?: {
    conditions: Array<{ key: string }>
  }
}

// Interface cho condition detail object
export interface IConditionDetail {
  postActions: any[]
  id: number
  stateId: number
  stateName: string
  stateCode: string
  stateTypeName: string
  stateColorCode: string
  description?: string
  objectType?: string
  preStates: IPreState[]
}

// Interface chính cho transition item
export interface IStateTransitionItem {
  transitionItemId: number
  stateId: number
  stateDisplayName: string
  stateName: string
  stateCode: string
  stateColorCode: string
  stateIcon: string
  description?: string
  objectType?: string
  conditionDetails: IConditionDetail[]
}

// Interface cho API response danh sách transition items (chi tiết cấu hình chuyển đổi)
export interface IStateTransitionConfigResponse extends Array<IStateTransitionItem> {}

// Interface cho dataSource của table
export interface IStateTransitionTableItem {
  key: string
  index: number
  name: string
  displayName: string
  code: string
  type: string
  previousState: string
  triggers: ITrigger[]
  condition: string
  postActions?: any[]
  icon?: string
  color: string
}

export type IStateTransitionTableResponse = IStateTransitionTableItem[]

// Interface cho history params
export interface IStateTransitionHistoryParams {
  value?: string
  isName?: number
  isChangeSummary?: number
  isActor?: number
  startDate?: string
  endDate?: string
  versionFilter?: string
  sort?: string
  page?: number
  size?: number
}

// Interface cho query params
export interface IStateTransitionQueryParams {
  search: string
  isName: number
  isCode: number
  lstTypeId: number
  lstTriggerType: 'UNSET' | 'MANUAL' | 'API' | 'WEBHOOK' | 'SCHEDULE' | 'RULE_ENGINE'
  lstTriggerRole: 'UNSET' | string
}

export interface IStateTransitionSearchParams {
  search?: string
  isName?: number
  isCode?: number
  lstTypeId?: string | null
  lstTriggerType?: string
  lstTriggerRole?: string
}

// Interface cho API create state-transition config
export interface CreateStateTransitionConfigRequest {
  name: string
  objectType: string
  description: string
  status: number
  items: any[]
}

export interface CreateStateTransitionConfigResponse {
  success: boolean
  message: string
  data?: any
}

// Interface cho SearchFilterBar component
export interface ISearchFilterBarProps {
  readOnly: boolean
  selectedColumns: string[]
  setSelectedColumns: (columns: string[]) => void
  handleAddState?: () => void
  searchFilters: {
    searchValue: string
    checkedFilter: CheckedState
    lstTypeId: number[]
    lstTriggerRole: string[]
    lstTriggerType?: string[]
    postActionTypes?: string[]
    startTime?: string
    endTime?: string
  }
  updateSearchFilter: (key: string, value: any) => void
}

// Configuration Section Component Props
export interface HeaderProps {
  showTabs: boolean
  activeTab: string
  setActiveTab: (tab: string) => void
  configCollapsed: boolean
  setConfigCollapsed: (collapsed: boolean) => void
}

export interface ContentProps {
  activeTab: string
  readOnly: boolean
  selectedColumns: string[]
  setSelectedColumns: (columns: string[]) => void
  searchFilters: any
  updateSearchFilter: (key: string, value: any) => void
  isLoading: boolean
  configData: IStateTransitionTableResponse
  configCollapsed: boolean
  handleNodeClick: (node: Node) => void
  handleEdgeClick: (edge: Edge) => void
  onDeleteItems?: (deletedKeys: string[]) => void
}
