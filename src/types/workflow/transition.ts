import type { Node, Edge } from '@xyflow/react'

import type { ITrigger } from './stateTransition'

// #region HISTORY TYPES
// Interface cho lịch sử chuyển đổi trạng thái
export interface HistoryRecord {
  key: string
  index: number
  name: string
  version: string
  changeSummary: string
  actorInfo: string
  modifiedAt: string
}

// Interface cho lịch sử đơn hàng
export interface OrderRecord {
  id: string | number
  key: string
  preStateName: string
  stateName: string
  result: 'Thành công' | 'Thất bại'
  triggerType: string
  role: string
  createdAt: string
}
// #endregion HISTORY TYPES

// #region FORM TYPES
// Interface cho form data
export interface TransitionFormData {
  configName: string
  targetObject: string
  categories: string[]
  description?: string
  isActive: boolean
}
// #endregion FORM TYPES

// #region STATE CONFIGURATION TYPES
// Interface cho StateTableItem
export interface TransitionStateTableItem {
  key: string
  index: number
  name: string
  displayName: string
  code: string
  type: string
  previousState: string
  previousStateId?: string
  triggers: ITrigger[]
  condition: string
  postActions?: any[]
  color: string
  description?: string
  objectType?: string
  updateRole?: string
  systemAction?: string
  stateId?: string | number
  status?: string | number
  typeId?: string | number
  applyAll?: boolean
  triggerConditions?: any[]
  icon?: string
  [key: string]: any // Thêm index signature để cho phép truy cập động
}
// #endregion STATE CONFIGURATION TYPES

// #region COMPONENT PROPS
// Props cho các component con
export interface GeneralInfoProps {
  form: any
  generalCollapsed: boolean
  setGeneralCollapsed: (collapsed: boolean) => void
  selectedObject: string | undefined
  setSelectedObject: (object: string | undefined) => void
}

export interface ConfigurationProps {
  configCollapsed: boolean
  setConfigCollapsed: (collapsed: boolean) => void
  setConfigType?: (type: 'table' | 'diagram' | null) => void
  stateTableData?: TransitionStateTableItem[]
  readOnly?: boolean
  showTabs?: boolean
  setDataConfig?: (data: any) => void
  onDeleteItems?: (codes: string[]) => void
}

export interface TableConfigProps {
  // Props cho cấu hình dạng bảng
}

export interface DiagramConfigProps {
  // Props cho cấu hình dạng sơ đồ
}

// #endregion COMPONENT PROPS

// #region DRAWER CONFIGURATION
// Interface cho TableConfigDrawer
export interface TableConfigDrawerProps {
  open: boolean
  onClose: (shouldRefresh?: boolean, newRows?: TransitionStateTableItem[], rawMappedData?: any) => void
  maskClosable?: boolean
  selectedObject?: string
  tableStates?: TransitionStateTableItem[] // Danh sách trạng thái đã có trong bảng
}

// Interface cho form data của TableConfigDrawer
export interface TableConfigFormData {
  // Thông tin chung
  description?: string
  stateName: string
  stateCode: string
  stateColor: string
  stateType: 'initial' | 'intermediate' | 'final' | 'start' | 'progress' | 'end'
  isSharedForAllObjects: boolean

  // Trường để phân biệt trạng thái mới/cũ
  stateId?: string | number

  // Trạng thái tiền nhiệm
  predecessorStates: string[]
  previousStates: string[]

  // Trigger cập nhật trạng thái
  triggerType: 'manual' | 'api' | 'webhook' | 'schedule' | 'rule'

  // Các trường cho Manual trigger
  roles?: string[]

  // Các trường cho API trigger
  apiCall?: string
  apiEndpoint?: string
  apiMethod?: string
  httpMethod?: 'GET' | 'POST' | 'PUT' | 'DELETE'
  headers?: string
  bodyTemplate?: string
  responseCondition?: string

  // Các trường cho Webhook trigger
  webhook?: string
  webhookUrl?: string
  webhookSecret?: string
  webhookMethod?: string
  webhookHeaders?: string
  webhookBodyTemplate?: string
  webhookResponseCondition?: string

  // Các trường cho Rule Engine trigger
  ruleConfig?: string
  ruleConditions?: string
  ruleCondition?: string
  ruleField?: string
  ruleOperator?: string
  ruleValue?: string

  // Các trường cho Schedule trigger
  frequency?: 'daily' | 'weekly' | 'monthly' | 'custom'
  scheduleType?: 'daily' | 'weekly' | 'monthly'
  scheduleTime?: string
  scheduleFrequency?: 'daily' | 'weekly' | 'monthly'
  startDate?: string
  endDate?: string
  daysOfWeek?: string[]
  daysOfMonth?: string[]
  dateRange?: [string, string]
  executionTime?: string
  cronExpression?: string

  // Hành động sau khi chuyển đổi trạng thái
  actionType?: 'webhook' | 'notification' | 'task' | 'email'
  webhookLink?: string
  authType?: 'key' | 'basic'
  secretKey?: string
  basicAuth?: string

  // Notification action fields
  notificationType?: 'email' | 'push'
  notificationTemplate?: string
  notificationContent?: string

  // Task action fields
  taskName?: string
  taskAssignee?: string
  taskDescription?: string

  // Các trường chung cho hành động
  actions: Array<{
    actionType: 'email' | 'notification' | 'webhook' | 'api' | 'function' | 'task'
    actionValue?: string

    // Email action fields
    recipients?: string
    template?: string

    // Notification action fields
    notificationType?: 'system' | 'push' | 'sms'
    content?: string

    // Webhook/API action fields
    endpoint?: string
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE'

    // Generic config
    config?: any
  }>

  isActive: boolean
}
// #endregion DRAWER CONFIGURATION

// #region HISTORY SECTION PROPS
// Types cho tab lịch sử
export interface HistorySectionProps {
  data: HistoryRecord[]
}
// #endregion HISTORY SECTION PROPS

// #region PRODUCT TYPES
// Types cho Product Popover
export interface ProductData {
  key: number
  image: string
  name: string
  variant?: string
  package: string
  cycle?: React.ReactNode
  sku: string
  price: number
  quantity: number
  status: string
}

// Types cho Product Popover
export interface ProductPopoverProps {
  children: React.ReactNode
  orderItems?: OrderItem[]
}

// #endregion PRODUCT TYPES

// #region ORDER TYPES
// Types riêng cho Detail view
export interface OrderApplyRecord {
  subId: number
  subCode: string
  code: string
  statusId: any
  statusName: any
  imgName: string
  imgPath: string
  userName: string
  userPhone: any
  userEmail: string
  providerName: string
  billTotal: number
  createdAt: string
  createSource: string
  invoiceAddress: string
  address: any
  deliveryAddress: string
  assigneeName: string
  assigneePhone: any
  isCart: boolean
  processStatus: any
  installationConfiguration: number
  allProcessStatusForSubs: any
  paymentMethod: string
  exportInvoice: boolean
  confirmPayment: boolean
  pricingType: string
  customerType: string
  orderItems: OrderItem[]
  solutionId: any
  solutionName: any
  packageId: any
  packageName: any
  totalProgressStatusEnum: any
  totalProgressStatus: any
  totalProgressName: any
  workflowId: number
  workflowName: string
  workflowStepId: number
  workflowStepName: string
  workflowStepCode: string
}

export interface OrderItem {
  subId: number
  subCode: string
  serviceId: number
  serviceName: string
  classification: string
  sku: string
  serviceIconUrl: string
  variantId: number
  variantName: string
  pricingId: number
  pricingName: string
  pricingMultiPlanId: number
  isOneTime: number
  cycleType: string
  paymentCycle: number
  numberOfCycles: number
  unitPrice: number
  quantity: number
  billAmount: any
  discountValue: any
  addons: any
  itemType: string
  processStatus: any
  progressStep: any
  progressStatus: any
  productOrderId: number
  orderItemStatus: number
  orderItemStatusCode: string
  orderItemStatusName?: string
  allOrderItemStatusSteps: AllOrderItemStatusStep[]
  stateTransitionId: number
  stateId: number
  stateName: string
  stateColorCode: string
  stateDisplayName: string
}

export interface AllOrderItemStatusStep {
  status: number
  code: string
  name: string
}
// #endregion ORDER TYPES

// #region READONLY PROPS
// Types cho thông tin chung ở chế độ chỉ đọc
export interface GeneralInfoReadonlyProps {
  generalInfo: {
    name: string
    targetObject: string
    category: string
    product: string
    description: string
  }
}
// #endregion READONLY PROPS

// #region FILTER AND PAGINATION
export interface TransitionFilterParams {
  search?: string
  startTime?: string
  endTime?: string
  lstObjectType?: string
  lstCategoryId?: string
  page?: number
  size?: number
  sort?: string
}

// Kiểu dữ liệu cho danh sách trạng thái
export interface TransitionListItem {
  id: number
  name: string
  lstCategoryName: string
  modifiedAt: string
  objectType: string
}

// Kiểu dữ liệu phân trang
export interface Pagination {
  current: number
  pageSize: number
  total: number
}

export interface TransitionListResponse {
  data: TransitionListItem[]
  pagination: Pagination
}
// #endregion FILTER AND PAGINATION

// #region DIAGRAM PROPS
export interface TransitionDiagramProps {
  nodes?: Node[]
  edges?: Edge[]
  stateTableData?: TransitionStateTableItem[]
  layoutType?: 'horizontal' | 'even' | 'grid'
  gridColumns?: number
  isCollapsed?: boolean
  onCreateState?: () => void
  onCreateTransition?: () => void
  onNodeClick?: (node: Node) => void
  onEdgeClick?: (edge: Edge) => void
}
// #endregion DIAGRAM PROPS

// #region API TYPES
export type MappingDataType = 'webhook' | 'api' | 'action' | undefined

// Type cho response API chi tiết Thông tin chung cấu hình chuyển đổi
export interface IStateTransitionDetail {
  id: number
  name: string
  objectType: string
  description?: string
  status?: number
  categoryNames?: string
}

export interface EndpointConfigProps {
  backgroundColor?: string
  name: string // prefix cho các field name
}
// #endregion API TYPES
