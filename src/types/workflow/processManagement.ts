//  params cho bộ lọc danh sách
export interface ListProcessParams {
  search?: string | null
  isName?: number
  isCode?: number
  isLatest?: number
  status?: string
  startTime?: string
  endTime?: string
  lstStatus?: string[]
  page?: number
  size?: number
  sort?: string
}

export interface ProcessListType {
  id?: number
  name?: string
  code?: string
  productType?: string
  version?: string
  status?: string
  modifiedAt?: string
  isDefault?: boolean
}

// Types cho Process Detail
export interface ManualTriggerConfig {
  agentTypes: string[]
  roles: string[]
}

export interface WebhookTriggerConfig {
  action: string
  status: string[]
}

export interface RuleEngineCondition {
  id: number
  key: string
  ifconds: Array<{
    id: number
    key: string
    operandId: number
    operator: number
    data: {
      value: number[]
    }
  }>
}

export interface RuleEngineTriggerConfig {
  conditions: RuleEngineCondition[]
}

export interface TriggerConfigDetail {
  type: 'MANUAL' | 'WEBHOOK' | 'RULE_ENGINE' | 'SCHEDULE' | 'API' | 'STATE'
  manual: ManualTriggerConfig | null
  schedule: any | null
  ruleEngine: RuleEngineTriggerConfig | null
  api: any | null
  webhook: WebhookTriggerConfig | null
  state: any | null
}

export interface ProcessStep {
  name: string
  code: string
  description: string
  index: number
  postActions: any | null
  triggerLogic: 'AND' | 'OR'
  id: number
  triggerConfigDetail: TriggerConfigDetail[]
}

export interface ProcessDetail {
  processInfo: {
    name: string
    code: string
    status: string
    description: string
    tags: string[]
    objectTypes: string[]
    productTypes: string[]
    physicalStateTransitionId: number | null
    serviceStateTransitionId: number | null
    digitalStateTransitionId: number | null
    id: number
    lstSteps: ProcessStep[]
    lstUserApply: any | null
    physicalStateTransitionName: string | null
    serviceStateTransitionName: string | null
    digitalStateTransitionName: string | null
  }
}
