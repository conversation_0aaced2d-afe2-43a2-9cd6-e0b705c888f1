// Interface definitions for Post Actions in workflow state transitions

// Interface cho webhook configuration
export interface WebhookConfig {
  apiConfig?: any
  url?: string
  method?: string
  headers?: Record<string, string>
  payload?: any
}

// Interface cho notification configuration
export interface NotificationConfig {
  notificationType: 'EMAIL' | 'NOTIFICATION'
  contentType?: 'CUSTOM' | 'TEMPLATE'
  customTitle?: string
  customContent?: string
  templateCode?: string
  templateHtmlBody?: string
}

// Interface cho PostAction
export interface PostAction {
  postActionType: 'WEBHOOK' | 'NOTIFICATION'
  webhook?: WebhookConfig
  notifications?: NotificationConfig
}
