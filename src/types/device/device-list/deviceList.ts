export interface DeviceBOS {
  id: string
  code: string
  deviceName: string
  deviceModel: string
  serialNumber: string
  status: string
  customerName: string
  orderCode: string
  profileCommands: any
}

export interface DeviceDetailInfo {
  deviceId: number
  deviceName: string
  serialNumber: string
  serviceIconUrl: string
  variantName: string
  pricingName: string
  subscriptionId: number
  model: string
  currentStatus: CurrentStatus
  businessStatus: BusinessStatus
  detailedInformation: DetailedInformation
  identifier: Identifier
}

export interface CurrentStatus {
  status: string
  operatingStatus: string
  lastConnectionTime: string
  statusInformation: string
}

export interface StatusInfoItem {
  id: number
  name: string
  displayName: string
  unit?: string
  description?: string
  value?: string
}

export interface BusinessStatus {
  clientName: string
  orderCode: string
}

export interface DetailedInformation {
  deviceName: string
  deviceModel: string
  supplier: string
  attributeInformation: string
  manufacture: string
  firmwareVersion: string
  hardwareVersion: string
}

export interface AttributeInformation {
  id: number
  name: string
  displayName: string
  value?: string
  description?: string
}

export interface Identifier {
  idBOS: string
  idPlatform: string
  idPartner: string
}

export interface ActivityEvent {
  id: number
  createdAt: number
  displayCommandName: string
  commandName: string
  params: string
  createdBy: string
  status: string
}

export interface ReplaceDevice {
  id: number
  serialNumber: string
  orderCode: string
  customerName: string
  status: string
  deviceName: string
}
