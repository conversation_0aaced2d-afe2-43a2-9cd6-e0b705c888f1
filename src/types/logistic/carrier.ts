export type CarrierState =
  | 'DRAFT'
  | 'PENDING_MAPPING'
  | 'READY_TO_TEST'
  | 'ACTIVE'
  | 'INACTIVE'
  | 'ERROR'
  | 'DEPRECATED'
  | 'DELETED'
export type CarrierStatus = 'ACTIVE' | 'INACTIVE'

export interface LogisticCarrier {
  id: string
  uuid: string
  name: string
  code: string
  description?: string
  partnerCode?: string
  logoUrl?: string
  supportEmail: string
  supportPhone: string
  state: CarrierState
  status: CarrierStatus
  createdBy?: number
  createdAt: string
  modifiedBy?: number
  modifiedAt?: string
  deletedFlag?: number
  requestTotal: number
  totalApiCalls?: number
  successRate?: string
  failRate?: string
  timeResponseAvg?: string
  connectList?: CarrierApiConfig[]
}

export interface CreateLogisticCarrierDto
  extends Omit<
    LogisticCarrier,
    'id' | 'uuid' | 'created_at' | 'modified_at' | 'created_by' | 'modified_by' | 'deleted_flag'
  > {}

export interface UpdateLogisticCarrierDto extends Partial<CreateLogisticCarrierDto> {
  id?: number
}

export interface DeliveryProduct {
  id: number
  name: string
  quantity: number
  classification: 'PHYSICAL' | 'DIGITAL'
}

export interface DeliveryTracking {
  id: string
  code: string
  products: DeliveryProduct[]
  amount: number
  assignee?: string
  created_at: string
  status: string
}

// Sử dụng API thay cho việc defined
export type CarrierAction = string
// | 'CALCULATE_FEE'
// | 'CREATE_ORDER'
// | 'CANCEL_ORDER'
// | 'GET_STATUS'
// | 'LIVE_TRACKING'
// | 'CONFIRM_DELIVERY'
// | 'RETURN_STATUS'
// | 'REGISTER_COD'
// | 'GET_COD_STATUS'
// | 'SETTLE_COD'
// | 'GET_INVENTORY'
// | 'STORE_PANEL'
// | 'FULFILL_ORDER'
// | 'REGISTER_WEBHOOK'
// | 'RECONCILE_SHIPPING_FEE'

export type CarrierActionList = {
  name: string
  code: CarrierAction | string
  required: boolean
}

export type ApiMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE'

export type ApiStatus = 'READY_FOR_TEST' | 'ACTIVE' | 'FAILURE' | 'INACTIVE' | 'DRAFT'

export type AuthMethod = 'NONE' | 'BASIC' | 'BEARER' | 'API_KEY'

export type ApiConfig = {
  method: ApiMethod
  url: {
    raw: string
    variables?: {
      key: string
      value: string
    }[]
  }
  headers?: {
    key: string
    value: string
    description?: string
  }[]
  auth: {
    type: AuthMethod
    apiKey?: {
      key: string
      value: string
      type: 'header' | 'query'
    }
    basic?: {
      username: string
      password: string
    }
    bearer?: {
      key: string
      value: string
    }
  }
  securities: {
    hmac?: {
      field: string
      participants: string[]
      encode: string
      secretKey: string
      separator: string
    }
  }
  additionalParams?: Record<string, any>
}

export interface ApiMapping {
  source: string
  dest: string
  conversions?:
    | {
        type: string
        rules: any
      }[]
    | undefined
}

export interface CarrierApiConfig {
  id: number // ID API của đối tác
  partnerId: string // 	ID đối tác (ID bảng partners)
  // name: string // Tên kết nối
  // code: string // Mã kết nối
  // type: 'PULL_API' | 'PUSH_API' |'WEBHOOK' // Loại kết nối
  modifiedAt?: string // Thời gian sửa đổi - dd/MM/yy HH:mm:ss
  action: CarrierAction // Hành động
  actionName?: string // Tên hành động - Extra params
  apiConfig: ApiConfig
  inputMapping: ApiMapping[]
  outputMapping: ApiMapping[]
  errorMapping?: {
    httpStatusCode: number
    partnerCode: string
    bosCode: string
    note: string
  }[]
  dxReqFields?: any // Lưu trữ thếm
  dxRespFields?: any // Lưu trữ thếm
  partnerReqFields?: any // Lưu trữ thếm
  partnerRespFields?: any // Lưu trữ thếm
  timeout?: number
  retryNumber?: number
  retryInterval?: number
  state: ApiStatus
  status: CarrierStatus
}

export interface CreateCarrierApiConfigDto extends Omit<CarrierApiConfig, 'id'> {}

export interface UpdateCarrierApiConfigDto extends Partial<CarrierApiConfig> {}

export interface EndpointConfigDetailProps {
  open: boolean
  action: string
  setOpen: (open: boolean) => void
}

export enum TabKey {
  Request = 'REQUEST',
  Response = 'RESPONSE'
}

export interface ErrorMapping {
  httpStatusCode: number
  partnerCode: string
  note: string
  bosCode?: string
}

export interface ConfigItem {
  key: string
  action: string
  endpointUrl: string
  connectionMethod: string
  authenticationMethod: string
  modifiedAt: string | null
  state: string
  actionType: string
}

export interface FilterParam {
  states: string[] | null
  connectMethods: string[] | null
  authType: string | null
  startDate: string | null
  endDate: string | null
}
