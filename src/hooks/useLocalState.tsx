import type { Dispatch } from 'react'
import { useContext } from 'react'

import set from 'lodash/set'

import { initialStateLocal, LocalStateContext } from '@/context/LocalStateContext'

interface Action {
  type: string
  payload?: any
}

interface LocalStateHook {
  isInitialized: boolean
  state: any
  dispatch: Dispatch<Action>
  setState: (payload: any) => void
  setFieldsValue: (value: any | ((prevState: any) => any)) => void
  setFieldValue: (key: string | string[], value: any | ((prevValue: any) => any)) => void
  getFieldValue: (key: string | string[]) => any
  getFieldsValue: () => any
  reset: () => void
  subscribe: (callback: (newState?: any, oldState?: any) => void) => void
  unsubscribe: (callback: (newState?: any, oldState?: any) => void) => void
}

// #region Utils
const convertArrayToStringPath = (path: string[]) => {
  return path.join('.')
}

const getNestedValue = (obj: any, path: string | string[]): any => {
  if (typeof path === 'string') {
    path = path.split('.')
  }

  return path.reduce((current, key) => {
    return current && typeof current === 'object' ? current[key] : undefined
  }, obj)
}

/**
 * Flatten nested object to dot notation keys
 * @param obj - Object to flatten
 * @param prefix - Prefix for keys
 * @returns Flattened object with dot notation keys
 *
 * @example
 * flattenObject({ user: { name: 'John', age: 25 } })
 * // Returns: { 'user.name': 'John', 'user.age': 25 }
 */
const flattenObject = (obj: any, prefix = ''): Record<string, any> => {
  const flattened: Record<string, any> = {}

  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const newKey = prefix ? `${prefix}.${key}` : key

      if (obj[key] !== null && typeof obj[key] === 'object' && !Array.isArray(obj[key])) {
        // Recursively flatten nested objects
        Object.assign(flattened, flattenObject(obj[key], newKey))
      } else {
        // Add primitive values or arrays directly
        flattened[newKey] = obj[key]
      }
    }
  }

  return flattened
}
// #endregion

// #region Helper functions
const setState = (dispatch: Dispatch<Action>, payload: any) => {
  dispatch({ type: 'SET_STATE', payload })
}

const setFieldsValue = (dispatch: Dispatch<Action>, value: any, currentState?: any) => {
  // Check if value is a function to support callback pattern
  const resolvedValue = typeof value === 'function' && currentState !== undefined ? value(currentState) : value

  // If resolved value is not an object, use simple SET_FIELDS_VALUE
  if (!resolvedValue || typeof resolvedValue !== 'object') {
    dispatch({ type: 'SET_FIELDS_VALUE', payload: resolvedValue })

    return
  }

  // Flatten nested objects to dot notation keys (similar to Ant Design Form)
  const flattened = flattenObject(resolvedValue)

  // Apply each field using SET_FIELD_VALUE action to support nested paths
  let newState = currentState ? { ...currentState } : {}

  Object.entries(flattened).forEach(([key, fieldValue]) => {
    newState = set(newState, key, fieldValue)
  })

  // Dispatch the final merged state
  dispatch({ type: 'SET_STATE', payload: newState })
}

const setFieldValue = (dispatch: Dispatch<Action>, key: string | string[], value: any, currentState?: any) => {
  if (Array.isArray(key)) {
    key = convertArrayToStringPath(key)
  }

  // Check if value is a function to support callback pattern
  const finalValue =
    typeof value === 'function' && currentState !== undefined ? value(getNestedValue(currentState, key)) : value

  dispatch({ type: 'SET_FIELD_VALUE', payload: { key, value: finalValue } })
}

const getFieldValue = (key: string | string[], currentState?: any) => {
  if (Array.isArray(key)) {
    key = convertArrayToStringPath(key)
  }

  return getNestedValue(currentState, key)
}

const getFieldsValue = (currentState?: any) => {
  return currentState || {}
}
// #endregion

/**
 * Custom hook to access and manipulate the local state within a `LocalStateProvider`.
 * LocalStateContext is used to manage the local state of the application.
 *
 * @throws {Error} If the hook is used outside of a `LocalStateProvider`.
 *
 * @returns {Object} An object containing the following properties:
 * - `state`: The current state from the context.
 * - `dispatch`: The dispatch function to update the state.
 * - `setState`: Function to set the state with a given payload.
 * - `setFieldsValue`: Function to set multiple fields in the state. Supports nested objects, dot notation keys, and callback functions (similar to Ant Design Form).
 * - `setFieldValue`: Function to set a specific field in the state with a given key and value or callback function.
 * - `getFieldValue`: Function to get a specific field value from the state using dot notation or array path.
 * - `getFieldsValue`: Function to get all field values (returns the entire state).
 * - `reset`: Function to reset the state to its initial value.
 *
 * @param {Object} options - An object containing the following properties:
 * - `disabledWarning`: A boolean value to disable the warning message when the context is not available.
 *
 * @version 1.3: Almost complete!
 */
const useLocalState = (options?: { disabledWarning?: boolean }): LocalStateHook => {
  const context = useContext(LocalStateContext)

  // ! Update when the context is not available, the program will continue to run
  // if (context === undefined || context === null) {
  //   throw new Error('useLocalState must be used within a LocalStateProvider')
  // }

  if (context === undefined || context === null) {
    if (!options?.disabledWarning) {
      console.warn('useLocalState must be used within a LocalStateProvider')
    }

    return {
      isInitialized: false,
      state: initialStateLocal,
      dispatch: () => {},
      setState: () => {},
      setFieldsValue: () => {},
      setFieldValue: () => {},
      getFieldValue: () => undefined,
      getFieldsValue: () => ({}),
      reset: () => {},
      subscribe: () => {},
      unsubscribe: () => {}
    }
  }

  return {
    isInitialized: true,
    state: context.state,
    dispatch: context.dispatch,
    setState: (payload: any) => setState(context.dispatch, payload),
    setFieldsValue: (value: any | ((prevState: any) => any)) => setFieldsValue(context.dispatch, value, context.state),
    setFieldValue: (key: string | string[], value: any | ((prevValue: any) => any)) =>
      setFieldValue(context.dispatch, key, value, context.state),
    getFieldValue: (key: string | string[]) => getFieldValue(key, context.state),
    getFieldsValue: () => getFieldsValue(context.state),
    reset: () => context.dispatch({ type: 'RESET' }),
    subscribe: context.subscribe,
    unsubscribe: context.unsubscribe
  }
}

export default useLocalState
