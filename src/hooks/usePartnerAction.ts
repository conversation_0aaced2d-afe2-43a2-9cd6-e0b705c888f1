import { useMutation, useQueryClient } from '@tanstack/react-query'

import partnerManagementInstance from '@/models/PartnerManagement'
import { showMessage } from '@/components/notification/NotificationProvider'
import { message } from '@/components/notification'
import { PARTNER_STATUS } from '@/components/partner-management/common/constants'

interface UsePartnerActionsProps {
  option: (typeof PARTNER_STATUS)[keyof typeof PARTNER_STATUS] | null
  comment: string
  onSuccess?: () => void
}

interface PasswordChangeProps {
  newPassword: string
  confirmPassword: string
  onSuccess?: () => void
}

export const usePartnerActions = (id: string | number) => {
  const queryClient = useQueryClient()

  const updatePassword = useMutation({
    mutationFn: partnerManagementInstance.updatePassword,
    onSuccess: () => {
      showMessage.success('Đổi mật khẩu thành công')
    },
    onError: () => {
      showMessage.error('C<PERSON> lỗi xảy ra khi đổi mật khẩu')
    }
  })

  const approvePartner = useMutation({
    mutationFn: partnerManagementInstance.approvePartner,
    onSuccess: () => {
      showMessage.success('Cập nhật thành công')
      queryClient.invalidateQueries({
        queryKey: ['getPartnerDetailInfor', id]
      })
    },
    onError: () => {
      showMessage.error('Có lỗi xảy ra khi phê duyệt')
    }
  })

  const handleChangePassword = ({ newPassword, confirmPassword, onSuccess }: PasswordChangeProps) => {
    if (!newPassword.trim() || !confirmPassword.trim()) {
      message.error('Vui lòng nhập đầy đủ thông tin!')

      return
    }

    if (newPassword !== confirmPassword) {
      message.error('Mật khẩu mới và xác nhận không khớp!')

      return
    }

    updatePassword.mutate(
      { newPassword, id },
      {
        onSuccess
      }
    )
  }

  const handleApprove = ({ option, comment, onSuccess }: UsePartnerActionsProps) => {
    if ((option === PARTNER_STATUS.REJECTED || option === PARTNER_STATUS.PROFILE_COMPLETED) && !comment.trim()) {
      message.error('Vui lòng nhập nội dung comment!')

      return
    }

    approvePartner.mutate(
      {
        id,
        approvedStatus: option,
        comment: option === PARTNER_STATUS.REJECTED || option === PARTNER_STATUS.PROFILE_COMPLETED ? comment : ''
      },
      {
        onSuccess
      }
    )
  }

  return {
    handleApprove,
    handleChangePassword,
    approvePartner,
    updatePassword
  }
}
