import { useCallback, useRef } from 'react'

import { isNil } from 'lodash'

import { toBoolean } from '@/utils/string'

interface AsyncValidatorConfig {
  validatorFn: (value: string, ...args: any[]) => Promise<boolean>
  resultPassed?: boolean
  debounceMs?: number
  errorMessage?: string
  validationCondition?: (value: string) => boolean
  onSuccess?: (value: boolean) => void
  onFailure?: (value: boolean) => void
  onFinally?: (value: boolean) => void
}

/**
 * Hook để sử dụng validator bất đồng bộ với debounce
 * @note chỉ dùng với các api validate trả về dạng true / false
 * @note Dùng lodash debounce không phù hợp vì nó không thể hủy promise trước đó được => lỗi trả về result cũ
 * @param config
 * @param config.resultPassed mặc định true, tức là nếu api trả về true thì hợp lệ, false thì không hợ<PERSON> lệ, báo lỗi errorMessage
 *                            nếu đặt false thì ngược lại, api trả về false thì hợp lệ, true thì không hợp lệ, báo lỗi errorMessage
 * @param config.validationCondition điều kiện để thực hiện validate, nếu không thỏa điều kiện này thì bỏ qua validate
 * @returns
 */
export const useAsyncValidator = (config: AsyncValidatorConfig) => {
  const {
    validatorFn,
    debounceMs = 500,
    errorMessage = 'Có lỗi xảy ra',
    resultPassed = true,
    validationCondition, // Trigger before validate
    onSuccess,
    onFailure,
    onFinally
  } = config

  const timeoutRef = useRef<NodeJS.Timeout | null>(null)

  const pendingPromiseRef = useRef<{
    resolve: (value?: any) => void
    reject: (reason?: any) => void
  } | null>(null)

  const validator = useCallback(
    (_: any, value: string, ...args: any[]) => {
      // Clear timeout trước đó nếu có
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }

      // Reject promise trước đó nếu có
      if (pendingPromiseRef.current) {
        pendingPromiseRef.current.resolve() // Resolve thay vì reject để không gây lỗi
        pendingPromiseRef.current = null
      }

      return new Promise<void>((resolve, reject) => {
        // Lưu promise handlers
        pendingPromiseRef.current = { resolve, reject }

        // Tạo timeout mới
        timeoutRef.current = setTimeout(async () => {
          try {
            const response = await validatorFn(value, ...args)

            // Kiểm tra xem promise này có còn là pending promise hiện tại không
            if (pendingPromiseRef.current?.resolve !== resolve) {
              return // Ignore nếu đã có validation mới hơn
            }

            if (isNil(response)) {
              resolve()

              return
            }

            // Nếu có điều kiện validate phụ, và không thỏa điều kiện này thì bỏ qua validate
            if (validationCondition && !validationCondition(value)) {
              resolve()

              return
            }

            const result = resultPassed ? toBoolean(response) : !toBoolean(response)

            if (!result) {
              onFailure && onFailure(response)
              reject(new Error(errorMessage))
            } else {
              onSuccess && onSuccess(response)
              resolve()
            }

            // Clear pending promise
            onFinally && onFinally(response)
            pendingPromiseRef.current = null
          } catch (error: any) {
            // Kiểm tra xem promise này có còn là pending promise hiện tại không
            if (pendingPromiseRef.current?.reject !== reject) {
              return // Ignore nếu đã có validation mới hơn
            }

            console.warn('Async validation error:', error)
            reject(new Error(error?.message || 'Có lỗi xảy ra'))

            // Clear pending promise
            onFinally && onFinally(false)
            pendingPromiseRef.current = null
          }
        }, debounceMs)
      })
    },
    // ! Important Note: Để các function vào dependency sẽ impact performance do hàm validator sẽ bị tạo lại nhiều lần
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [debounceMs, validatorFn, resultPassed, errorMessage]
  )

  return { validator }
}
