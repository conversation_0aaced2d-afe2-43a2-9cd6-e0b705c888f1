import { useQuery } from '@tanstack/react-query'

import { stateTransitionConfig } from '@/models/workflow/stateTransitionConfig'
import type { IStateTransitionConfigResponse, IStateTransitionTableResponse } from '@/types/workflow/stateTransition'
import { DEFAULT_QUERY_PARAMS } from '@/constants/workflow/stateTransition'
import { transformApiDataToTableFormat } from '@/utils/workflow'

export const useStateTransitionData = (stateTransitionId: string) => {
  return useQuery<IStateTransitionTableResponse>({
    queryKey: ['stateTransitionConfig', stateTransitionId],
    queryFn: async (): Promise<IStateTransitionTableResponse> => {
      if (!stateTransitionId) return []

      const response: IStateTransitionConfigResponse = await stateTransitionConfig.getStateTransitionConfig(
        stateTransitionId,
        DEFAULT_QUERY_PARAMS
      )

      return transformApiDataToTableFormat(response as any)
    },
    enabled: !!stateTransitionId
  })
}
