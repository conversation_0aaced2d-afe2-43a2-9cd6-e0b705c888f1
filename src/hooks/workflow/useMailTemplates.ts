'use client'

import { useQuery } from '@tanstack/react-query'

import { stateTransitionAdmin } from '@/models/workflow/stateTransition'

interface MailTemplateItem {
  id: number
  code: string
  name: string
  titleDefault?: string
  contentHtml?: string
  emailType?: number
}

export const useMailTemplates = (enabled: boolean, params?: { emailType?: number; actionType?: string }) => {
  return useQuery<MailTemplateItem[], Error>({
    queryKey: ['mailTemplates', params?.emailType ?? -1, params?.actionType ?? ''],
    queryFn: async () => {
      const data = await stateTransitionAdmin.getMailTemplates({
        emailType: params?.emailType ?? -1,
        actionType: params?.actionType ?? ''
      })

      return Array.isArray(data) ? (data as MailTemplateItem[]) : data?.data || []
    },
    enabled
  })
}

export default useMailTemplates
