import { useMemo } from 'react'

import type { TreeDataNode } from 'antd'

import {
  OPERAND_CODE,
  OPERATORS,
  RULES_ENGINE_CONDITIONS,
  RULES_ENGINE_CONDITION_VALUES
} from '@/constants/workflow/trigger'
import { useComboboxCategory } from '@/hooks/workflow'
import { ConditionNodeContent } from '@/components/workflow/transition-management/state-transition-sections/configuration/components/state-transition-detail/sections'

export const useConditionRenderer = () => {
  const { comboboxCategory } = useComboboxCategory()

  const renderConditionText = useMemo(() => {
    return (condition: any) => {
      if (condition.ifconds && Array.isArray(condition.ifconds)) {
        const conditionTexts: { conditionLabel: string; operatorLabel: string; valueText: string }[] = []

        condition.ifconds.forEach((ifcond: any) => {
          // Tìm tên điều kiện dựa trên operandId
          const operandKey = Object.keys(OPERAND_CODE).find(
            key => OPERAND_CODE[key as keyof typeof OPERAND_CODE] === ifcond.operandId
          )

          if (operandKey) {
            // Tìm label của điều kiện
            const conditionItem = RULES_ENGINE_CONDITIONS.find(item => item.value === operandKey)
            const conditionLabel = conditionItem?.label || `Operand ${ifcond.operandId}`

            // Tìm operator text
            const operatorItem = OPERATORS.find(op => op.value === ifcond.operator)
            const operatorLabel = operatorItem?.label || '='

            if (ifcond.data?.value && Array.isArray(ifcond.data.value)) {
              // Xử lý đặc biệt cho OPERAND_CODE.ID (5004) - Danh mục
              if (ifcond.operandId === OPERAND_CODE.ID) {
                // Tạo một phần tử conditionTexts cho mỗi giá trị
                ifcond.data.value.forEach((val: any) => {
                  const categoryItem = comboboxCategory?.find((item: any) => item?.id === val)
                  const valueText = categoryItem?.name || val.toString()

                  conditionTexts.push({ conditionLabel, operatorLabel, valueText })
                })
              } else {
                const conditionValues =
                  RULES_ENGINE_CONDITION_VALUES[operandKey as keyof typeof RULES_ENGINE_CONDITION_VALUES]

                if (conditionValues && conditionValues.length > 0) {
                  // Tạo một phần tử conditionTexts cho mỗi giá trị
                  ifcond.data.value.forEach((val: any) => {
                    const valueItem = conditionValues.find(item => item.value === val)
                    const valueText = valueItem?.label || val.toString()

                    conditionTexts.push({ conditionLabel, operatorLabel, valueText })
                  })
                } else {
                  // Nếu không có mapping, vẫn tạo một phần tử conditionTexts cho mỗi giá trị
                  ifcond.data.value.forEach((val: any) => {
                    conditionTexts.push({ conditionLabel, operatorLabel, valueText: val.toString() })
                  })
                }
              }
            } else {
              // Nếu không có giá trị hoặc không phải mảng, vẫn tạo một phần tử conditionTexts
              conditionTexts.push({ conditionLabel, operatorLabel, valueText: '' })
            }
          }
        })

        return conditionTexts
      }

      return []
    }
  }, [comboboxCategory])

  const convertToTreeData = useMemo(() => {
    return (
      conditionTexts: { conditionLabel: string; operatorLabel: string; valueText: string }[],
      conditionIndex: number
    ): TreeDataNode[] => {
      if (!conditionTexts || conditionTexts.length === 0) {
        return []
      }

      // Tạo trực tiếp danh sách các node
      return conditionTexts.map((condText, idx) => ({
        title: (
          <div className='flex items-center gap-2'>
            {/* Đối tượng */}
            <ConditionNodeContent>{condText.conditionLabel}</ConditionNodeContent>
            {/* Toán tử */}
            <ConditionNodeContent>{condText.operatorLabel}</ConditionNodeContent>
            {/* Giá trị */}
            <ConditionNodeContent>{condText.valueText}</ConditionNodeContent>
          </div>
        ),
        key: `${conditionIndex}-${idx}`
      }))
    }
  }, [])

  return {
    renderConditionText,
    convertToTreeData
  }
}
