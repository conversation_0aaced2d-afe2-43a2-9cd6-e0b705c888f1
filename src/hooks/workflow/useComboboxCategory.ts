'use client'

import { useQuery } from '@tanstack/react-query'

import { stateTransitionConfig } from '@/models/workflow/stateTransitionConfig'

/**
 * Custom hook để lấy danh sách danh mục combobox
 * Chỉ được gọi khi cần thiết (lazy loading)
 * @param shouldFetch - Cờ để kiểm soát việc gọi API
 */
export const useComboboxCategory = () => {
  // State để kiểm soát việc gọi API

  const { data: comboboxCategory } = useQuery({
    queryKey: ['combobox-category'],
    queryFn: () => stateTransitionConfig.getComboboxCategory(),
    // Chỉ gọi API khi shouldFetch = true
    // Sử dụng cache trong 10 phút, tránh gọi lại API khi đã có dữ liệu
    staleTime: 10 * 60 * 1000,
    // Giữ dữ liệu trong cache trong 15 phút
    gcTime: 15 * 60 * 1000
  })

  return { comboboxCategory }
}
