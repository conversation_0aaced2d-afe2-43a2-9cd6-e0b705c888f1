'use client'

import { useQuery } from '@tanstack/react-query'

import { processManagement } from '@/models/workflow/processManagement'

interface MailTemplateItem {
  id: number
  code: string
  name: string
  titleDefault?: string
  contentHtml?: string
  emailType?: number
}

export const useTemplateEmailProcess = (enabled: boolean, params?: { emailType?: number; actionType?: string }) => {
  return useQuery<MailTemplateItem[], Error>({
    queryKey: ['getListTemplateEmail', params?.emailType, params?.actionType],
    queryFn: async () => {
      const data = await processManagement.getListTemplateEmail({
        emailType: params?.emailType || -1,
        actionType: params?.actionType || '',
        lstObjectApply: 'WORKFLOW'
      })

      return Array.isArray(data) ? (data as MailTemplateItem[]) : data?.data || []
    },
    enabled
  })
}

export default useTemplateEmailProcess
