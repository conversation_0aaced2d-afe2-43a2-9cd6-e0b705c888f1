import type { ITrigger } from '@/types/workflow/stateTransition'
import {
  SCHEDULE_TYPE_VALUES,
  SCHEDULE_FREQUENCY_VALUES,
  SCHEDULE_IN_WEEK_VALUES,
  DAY_IN_MONTH,
  SCHEDULE_WEEK_OF_MONTH_VALUES,
  RULES_ENGINE_CONDITIONS,
  RULES_ENGINE_CONDITION_VALUES,
  OPERATORS,
  OPERAND_CODE
} from '@/constants/workflow/trigger'
import { useComboboxCategory } from './useComboboxCategory'

// region render condition by trigger
export const useRenderConditionByTrigger = () => {
  const { comboboxCategory } = useComboboxCategory()

  const renderConditionByTrigger = (trigger?: ITrigger): string => {
    if (!trigger || !trigger?.type) return ''

    switch (trigger.type) {
      case 'MANUAL':
        return '' // bỏ trống

      case 'API':
        // L<PERSON>y từ trường "Điều kiện" của response, các điều kiện ngăn cách nhau bằng dấu ","
        if (trigger.api && trigger.api.conditions) {
          return Array.isArray(trigger.api.conditions) ? trigger.api.conditions.join(', ') : trigger.api.conditions
        }

        // Lấy từ cột "Trường đối tác" và "Giá trị" của tab response (trường outputMapping)
        if (trigger.api && trigger.api.outputMapping && Array.isArray(trigger.api.outputMapping)) {
          const mappingGroups: Record<string, string[]> = {}

          // Nhóm các giá trị theo trường đích (dest)
          trigger.api.outputMapping.forEach((mapping: { dest: string | number; value: any }) => {
            if (mapping.dest && mapping.value) {
              if (!mappingGroups[mapping.dest]) {
                mappingGroups[mapping.dest] = []
              }

              // Nếu giá trị là string thì thêm dấu nháy kép, nếu là số thì không cần
              const formattedValue = typeof mapping.value === 'string' ? `"${mapping.value}"` : mapping.value

              mappingGroups[mapping.dest].push(formattedValue)
            }
          })

          // Tạo chuỗi kết quả theo định dạng yêu cầu
          const result = Object.entries(mappingGroups)
            .map(([field, values]) => {
              return `"${field}": [${values.join(', ')}]`
            })
            .join('; ')

          return result
        }

        return '' // không có thì bỏ trống

      case 'WEBHOOK':
        return '' // bỏ trống

      case 'SCHEDULE':
        // Format: [Thời điểm động bộ] - [Tần suất] - từ [Thời điểm bắt đầu] - [Thời gian] - [Các ngày]
        if (trigger.schedule) {
          const {
            type, // 'MONTHLY', 'DAILY', 'WEEKLY', 'ONCE'
            intervalType, // 'EVERY_2_HOURS', 'EVERY_1_HOUR', etc.
            startTime, // '17:12'
            endTime, // null hoặc thời gian kết thúc
            startDate, // '18/07/2025'
            endDate, // null hoặc ngày kết thúc
            dayOfWeek, // ['MON', 'SAT']
            dayOfMonth, // [1, 2, 3]
            weekOfMonth // [1]
          } = trigger.schedule

          let scheduleText = ''

          // 1. Thời điểm động bộ (type)
          const scheduleTypeLabel = SCHEDULE_TYPE_VALUES.find(item => item.value === type)?.label || type

          if (scheduleTypeLabel) {
            scheduleText += scheduleTypeLabel
          }

          // 2. Tần suất (intervalType)
          const frequencyLabel =
            SCHEDULE_FREQUENCY_VALUES.find(item => item.value === intervalType)?.label || intervalType

          if (frequencyLabel) {
            scheduleText += ` - ${frequencyLabel}`
          }

          // 3. Thời gian bắt đầu và kết thúc
          if (startDate) {
            scheduleText += ` - từ ${startDate}`

            if (endDate) {
              scheduleText += ` đến ${endDate}`
            }
          }

          // 4. Thời gian trong ngày (startTime và endTime)
          if (startTime) {
            scheduleText += ` - ${startTime}`

            if (endTime) {
              scheduleText += ` đến ${endTime}`
            }
          }

          // 5. Các ngày trong tuần (dayOfWeek) - cho WEEKLY
          if (dayOfWeek && Array.isArray(dayOfWeek) && dayOfWeek.length > 0) {
            const dayLabels = dayOfWeek.map(day => {
              const dayLabel = SCHEDULE_IN_WEEK_VALUES.find(item => item.value === day)?.label

              return dayLabel || day
            })

            scheduleText += ` - ${dayLabels.join(', ')}`
          }

          // 6. Các ngày trong tháng (dayOfMonth) - cho MONTHLY với ngày cố định
          if (dayOfMonth && Array.isArray(dayOfMonth) && dayOfMonth.length > 0) {
            const dayLabels = dayOfMonth.map(day => {
              const dayLabel = DAY_IN_MONTH.find(item => item.value === day)?.label

              return dayLabel || `Ngày ${day}`
            })

            scheduleText += ` - ${dayLabels.join(', ')}`
          }

          // 7. Tuần trong tháng (weekOfMonth) - cho MONTHLY với thứ trong tuần cố định
          if (weekOfMonth && Array.isArray(weekOfMonth) && weekOfMonth.length > 0) {
            const weekLabels = weekOfMonth.map(week => {
              const weekLabel = SCHEDULE_WEEK_OF_MONTH_VALUES.find(item => item.value === week)?.label

              return `Tuần ${weekLabel || week}`
            })

            scheduleText += ` - ${weekLabels.join(', ')}`
          }

          return scheduleText.trim()
        }

        return ''

      case 'RULE_ENGINE':
        if (trigger.ruleEngine && trigger.ruleEngine.conditions) {
          const conditionTexts: string[] = []

          trigger.ruleEngine.conditions.forEach(condition => {
            if (condition.ifconds && Array.isArray(condition.ifconds)) {
              condition.ifconds.forEach(ifcond => {
                // Tìm tên điều kiện dựa trên operandId
                const operandKey = Object.keys(OPERAND_CODE).find(
                  key => OPERAND_CODE[key as keyof typeof OPERAND_CODE] === ifcond.operandId
                )

                if (operandKey) {
                  // Tìm label của điều kiện
                  const conditionItem = RULES_ENGINE_CONDITIONS.find(item => item.value === operandKey)
                  const conditionLabel = conditionItem?.label || `Operand ${ifcond.operandId}`

                  // Tìm operator text
                  const operatorItem = OPERATORS.find(op => op.value === ifcond.operator)
                  const operatorLabel = operatorItem?.label || '='

                  // Xử lý giá trị từ ifcond.data.value
                  let valueText = ''

                  if (ifcond.data?.value && Array.isArray(ifcond.data.value)) {
                    // Xử lý đặc biệt cho OPERAND_CODE.ID (5004) - Danh mục
                    if (ifcond.operandId === OPERAND_CODE.ID) {
                      // Kích hoạt việc gọi API lấy danh sách danh mục khi cần
                      if (comboboxCategory) {
                        const valueLabels = ifcond.data.value.map(val => {
                          const categoryItem = comboboxCategory.find((item: any) => item.id === val)

                          return categoryItem?.name || val.toString()
                        })

                        valueText = valueLabels.join(', ')
                      } else {
                        // Nếu chưa có dữ liệu comboboxCategory, hiển thị giá trị raw
                        valueText = ifcond.data.value.join(', ')
                      }
                    } else {
                      const conditionValues =
                        RULES_ENGINE_CONDITION_VALUES[operandKey as keyof typeof RULES_ENGINE_CONDITION_VALUES]

                      if (conditionValues && conditionValues.length > 0) {
                        const valueLabels = ifcond.data.value.map(val => {
                          const valueItem = conditionValues.find(item => item.value === val)

                          return valueItem?.label || val.toString()
                        })

                        valueText = valueLabels.join(', ')
                      } else {
                        // Nếu không có mapping, hiển thị giá trị raw
                        valueText = ifcond.data.value.join(', ')
                      }
                    }
                  }

                  const conditionText = `${conditionLabel} ${operatorLabel} ${valueText}`

                  conditionTexts.push(conditionText)
                }
              })
            }
          })

          return conditionTexts.join(', ')
        }

        return ''

      default:
        return ''
    }
  }

  return { renderConditionByTrigger }
}
