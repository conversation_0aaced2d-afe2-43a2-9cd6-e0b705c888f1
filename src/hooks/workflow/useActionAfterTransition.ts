import { useState, useMemo } from 'react'

import { NOTIFICATION_TYPE_MAP, POST_ACTION_TYPE, NOTIFICATION_TYPE } from '@/constants/workflow/stateTransition'
import type { PostAction } from '@/types/workflow/postAction'

export const useActionAfterTransition = (postActions: PostAction[]) => {
  const [openDataMappingModal, setOpenDataMappingModal] = useState(false)
  const [openEmailDetailModal, setOpenEmailDetailModal] = useState(false)
  const [openUrlVariablesModal, setOpenUrlVariablesModal] = useState(false)

  const hasNotificationWebhook = useMemo(
    () => postActions?.some((action: PostAction) => action.postActionType === POST_ACTION_TYPE.WEBHOOK),
    [postActions]
  )

  const getWebhookAction = useMemo(
    () => postActions?.find((action: PostAction) => action.postActionType === POST_ACTION_TYPE.WEBHOOK),
    [postActions]
  )

  const webhookData = useMemo(
    () => (hasNotificationWebhook ? getWebhookAction?.webhook?.apiConfig : null),
    [hasNotificationWebhook, getWebhookAction]
  )

  const getNotificationMethods = useMemo(() => {
    const methods = postActions
      ?.filter((action: PostAction) => action.postActionType === POST_ACTION_TYPE.NOTIFICATION)
      .map((action: PostAction) => NOTIFICATION_TYPE_MAP[action.notifications?.notificationType || ''])

    return () => methods?.join(', ')
  }, [postActions])

  const emailNotification = useMemo(
    () =>
      postActions?.find(
        (action: PostAction) =>
          action.postActionType === POST_ACTION_TYPE.NOTIFICATION &&
          action.notifications?.notificationType === NOTIFICATION_TYPE.EMAIL
      )?.notifications,
    [postActions]
  )

  const appNotification = useMemo(
    () =>
      postActions?.find(
        (action: PostAction) =>
          action.postActionType === POST_ACTION_TYPE.NOTIFICATION &&
          action.notifications?.notificationType === NOTIFICATION_TYPE.NOTIFICATION
      )?.notifications,
    [postActions]
  )

  const modals = {
    openDataMappingModal: () => setOpenDataMappingModal(true),
    closeDataMappingModal: () => setOpenDataMappingModal(false),
    isDataMappingModalOpen: openDataMappingModal,

    openEmailDetailModal: () => setOpenEmailDetailModal(true),
    closeEmailDetailModal: () => setOpenEmailDetailModal(false),
    isEmailDetailModalOpen: openEmailDetailModal,

    openUrlVariablesModal: () => setOpenUrlVariablesModal(true),
    closeUrlVariablesModal: () => setOpenUrlVariablesModal(false),
    isUrlVariablesModalOpen: openUrlVariablesModal
  }

  return {
    modals,
    hasNotificationWebhook,
    webhookData,
    emailNotification,
    appNotification,
    getNotificationMethods,
    postActions
  }
}
