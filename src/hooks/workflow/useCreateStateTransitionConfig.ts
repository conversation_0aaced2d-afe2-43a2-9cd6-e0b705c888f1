import { useMutation } from '@tanstack/react-query'
import { message } from 'antd'

import { stateTransitionAdmin } from '@/models/workflow/stateTransition'

interface CreateStateTransitionConfigRequest {
  name: string
  objectType: string
  description: string
  status: number
  items: any[]
}

export const useCreateStateTransitionConfig = () => {
  return useMutation({
    mutationFn: (data: CreateStateTransitionConfigRequest) => stateTransitionAdmin.createStateTransitionConfig(data),
    onSuccess: response => {
      message.success('Tạo cấu hình chuyển đổi trạng thái thành công')

      return response
    },
    onError: (error: any) => {
      const errorMessage = error?.response?.data?.message || 'Có lỗi xảy ra khi tạo cấu hình chuyển đổi trạng thái'

      message.error(errorMessage)
      throw error
    }
  })
}
