import React, { useState } from 'react'

import { useRouter } from 'next/navigation'

import { useMutation } from '@tanstack/react-query'
import { setCookie, getCookie } from 'cookies-next'
import type { FormInstance } from 'antd'

import Users from '@/models/User'
import { API_ROOT } from '@/models/Base'
import type { NotificationInfoType } from '@/types/notificationModalTypes'
import { ACTION_TYPE, PARAM_SEND_OTP_COOKIE_NAME } from '@/views/auth/utils/constants'
import { FORGOT_PASSWORD_PATH } from '@/components/partner-management/common/constants'

export type InfoModal = {
  title: string | React.ReactNode
  btnText: string
  btnCancel?: string
  description: string | React.ReactNode
  redirectPage: () => void
}

interface UseForgotPasswordProps {
  form: FormInstance
  portalType: 'SME' | 'IOT' | 'DEV'
  customerType?: string
}

interface UseForgotPasswordReturn {
  // States
  visibleModal: boolean
  setVisibleModal: (visible: boolean) => void
  visibleModalNotification: boolean
  setVisibleModalNotification: (visible: boolean) => void
  visibleModalContact: boolean
  setVisibleModalContact: (visible: boolean) => void
  infoModal: InfoModal
  infoNotification: NotificationInfoType

  // Mutations
  mutation: ReturnType<typeof useMutation>
  mutationSendOTP: ReturnType<typeof useMutation>
  mutationSendLinkActivate: ReturnType<typeof useMutation>

  // Handlers
  handleSubmit: (values: any) => void
  handleConfirm: (value: { methodSendOTP: any }) => void
}

export const useForgotPassword = ({
  form,
  portalType,
  customerType
}: UseForgotPasswordProps): UseForgotPasswordReturn => {
  const router = useRouter()

  // States
  const [visibleModal, setVisibleModal] = useState(false)
  const [visibleModalNotification, setVisibleModalNotification] = useState(false)
  const [visibleModalContact, setVisibleModalContact] = useState(false)

  const [infoModal, setInfoModal] = useState<InfoModal>({
    title: '',
    btnText: '',
    description: '',
    redirectPage: () => {}
  })

  const [infoNotification, setInfoNotification] = useState<NotificationInfoType>({
    iconType: 'WARNING',
    title: '',
    textButton: '',
    subTitle: '',
    handleConfirm: {}
  })

  // Modal configurations
  const getSuccessModal = (): InfoModal => ({
    title: (
      <div className='flex items-center justify-start gap-4'>
        <div className='flex size-10 items-center justify-center rounded-full bg-sme-blue-2'>
          <i className='onedx-mail-icons size-6'></i>
        </div>
        <div className='headline-16-semibold'>Hệ thống gửi email hướng dẫn thành công</div>
      </div>
    ),
    btnText: 'Đóng',
    description: (
      <>
        <div className='body-14-regular mt-2'>Vui lòng kiểm tra email đăng ký của bạn và làm theo hướng dẫn</div>
      </>
    ),
    redirectPage: () => setVisibleModal(false)
  })

  const getErrorModal = (): InfoModal => ({
    title: 'Gửi token tạm thời tới email lỗi',
    btnText: 'Đóng',
    description: 'Đã có lỗi xảy ra. Vui lòng thử lại sau một vài phút.',
    redirectPage: () => {
      const loginPath =
        portalType === 'IOT'
          ? '/iot-portal/login'
          : portalType === 'DEV'
            ? '/partner-portal/login'
            : '/sme-portal/login'

      router.push(loginPath)
    }
  })

  const getErrorModalDisable = (): InfoModal => ({
    title: (
      <div className='flex items-center justify-start gap-4'>
        <div className='flex size-10 items-center justify-center rounded-full bg-red-1'>
          <i className='onedx-warning-less-icon size-6'></i>
        </div>
        <div className='headline-16-semibold'>Tài khoản đã bị vô hiệu hóa</div>
      </div>
    ),
    btnText: 'Liên hệ',
    btnCancel: 'Đóng',
    description: (
      <>
        <div className='body-14-regular mt-2'>Liên hệ với quản trị viên để được hỗ trợ</div>
      </>
    ),
    redirectPage: () => {
      setVisibleModal(false)
      setVisibleModalContact(true)
    }
  })

  // Mutations
  const mutationSendOTP = useMutation({
    mutationFn: Users.sendOTP,
    onSuccess: () => {
      const verificationPath =
        portalType === 'IOT'
          ? '/iot-portal/verification-code?actionType=CHANGE_PASSWORD'
          : portalType === 'DEV'
            ? '/dev-portal/verification-code?actionType=CHANGE_PASSWORD'
            : '/sme-portal/verification-code?actionType=CHANGE_PASSWORD'

      router.push(verificationPath)
    },
    onError: () => {
      setVisibleModal(true)
      setInfoModal(getErrorModal())
    }
  })

  const mutationSendLinkActivate = useMutation({
    mutationFn: Users.resendActivationMail,
    onSuccess: () => {
      const forgotPasswordPath = FORGOT_PASSWORD_PATH[portalType]

      router.push(forgotPasswordPath)
    },
    onError: () => {
      setVisibleModal(true)
      setInfoModal(getErrorModal())
    }
  })

  // Handler for OTP confirmation
  const handleConfirm = (value: { methodSendOTP: any }) => {
    const paramSendOTP = getCookie(PARAM_SEND_OTP_COOKIE_NAME)

    if (paramSendOTP && JSON.parse(paramSendOTP).hash) {
      const bodySendOTP = {
        ...JSON.parse(paramSendOTP),
        sendType: value.methodSendOTP
      }

      setCookie(PARAM_SEND_OTP_COOKIE_NAME, JSON.stringify(bodySendOTP))
      mutationSendOTP.mutate(bodySendOTP)
    } else {
      const bodySendLinkActivate = {
        username: form.getFieldValue('username'),
        redirectUrl: `${API_ROOT}/${portalType.toLowerCase()}-portal/forgot-password${customerType ? `?customerType=${customerType}` : ''}`
      }

      mutationSendLinkActivate.mutate(bodySendLinkActivate)
    }
  }

  // Main mutation
  const mutation = useMutation({
    mutationFn: Users.forgotPassword,
    onSuccess: (res: any) => {
      if (res.needActivate) {
        const paramSendOTP = {
          username: form.getFieldValue('username'),
          hash: res.hashOTP,
          actionType: ACTION_TYPE.ACTIVATE
        }

        setCookie(PARAM_SEND_OTP_COOKIE_NAME, paramSendOTP)

        // Check configuration for password reset
        if (res.hashOTP) {
          setVisibleModalNotification(true)
          setInfoNotification({
            iconType: 'WARNING',
            title: 'Tài khoản của quý khách chưa được kích hoạt',
            textButton: 'Xác nhận',
            subTitle: 'Chúng tôi sẽ gửi mã kích hoạt đến email hoặc số điện thoại của quý khách đã đăng ký',
            methodOTP: { emailMask: res.emailMask, phoneMask: res.phoneMask },
            isCloseModal: true,
            handleConfirm
          })
        } else {
          setVisibleModalNotification(true)
          setInfoNotification({
            iconType: 'WARNING',
            title: 'Tài khoản của quý khách chưa được kích hoạt',
            subTitle: `Chúng tôi sẽ gửi mã kích hoạt đến email ${res.emailMask} của quý khách`,
            textButton: 'Xác nhận',
            isCloseModal: true,
            handleConfirm
          })
        }
      } else if (res.hashOTP) {
        const paramSendOTP = {
          username: form.getFieldValue('username'),
          hash: res.hashOTP,
          actionType: ACTION_TYPE.CHANGE_PASSWORD
        }

        setCookie(PARAM_SEND_OTP_COOKIE_NAME, JSON.stringify(paramSendOTP))

        setVisibleModalNotification(true)
        setInfoNotification({
          iconType: 'WARNING',
          title: 'Xác thực mã OTP',
          textButton: 'Xác nhận',
          subTitle: 'Vui lòng chọn hình thức gửi mã OTP xác thực',
          methodOTP: { emailMask: res.emailMask, phoneMask: res.phoneMask },
          isCloseModal: true,
          handleConfirm
        })
      } else {
        // For DEV portal, just show simple success notification
        if (portalType === 'DEV') {
          setVisibleModalNotification(true)
          setInfoNotification({
            iconType: 'ACTIVE',
            title: 'Hệ thống gửi email hướng dẫn thành công',
            subTitle: 'Vui lòng kiểm tra email đăng ký của bạn và làm theo hướng dẫn.',
            textButton: 'Đồng ý',
            isCloseModal: false,
            handleConfirm: () => {
              setVisibleModalNotification(false)
              window.location.href = '/partner-portal/reset-password'
            },
            width: 608
          })
        } else {
          setVisibleModal(true)
          setInfoModal(getSuccessModal())
        }
      }
    },
    onError: (error: any) => {
      if (error?.field === 'users' && ['error.object.not.found', 'error.object.not.exist'].includes(error?.errorCode)) {
        form.setFields([
          {
            name: 'username',
            errors: ['Tên đăng nhập không tồn tại']
          }
        ])
      } else if (error.field === 'status' && error.errorCode === 'error.user.disable') {
        setVisibleModal(true)
        setInfoModal(getErrorModalDisable())
      }
    }
  })

  // Submit handler
  const handleSubmit = (values: any) => {
    const portalMapping = {
      IOT: 'IOT_PORTAL',
      SME: 'SME',
      DEV: 'DEV'
    }

    const submitData = {
      ...values,
      portal: portalMapping[portalType],
      ...(customerType && { customerType })
    }

    mutation.mutate(submitData)
  }

  return {
    // States
    visibleModal,
    setVisibleModal,
    visibleModalNotification,
    setVisibleModalNotification,
    visibleModalContact,
    setVisibleModalContact,
    infoModal,
    infoNotification,

    // Mutations
    mutation,
    mutationSendOTP,
    mutationSendLinkActivate,

    // Handlers
    handleSubmit,
    handleConfirm
  }
}
