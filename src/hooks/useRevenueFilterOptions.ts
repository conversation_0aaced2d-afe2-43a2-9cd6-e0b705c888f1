import { useQuery } from '@tanstack/react-query'

import DashboardManagement from '@/models/DashboardManagement'

interface OptionType {
  label: string
  value: string | number
}

export const useRevenueFiltersOptions = () => {
  const { data: provinceList, ...provinceRest } = useQuery<OptionType[]>({
    queryKey: ['provinceList'],
    queryFn: async () => {
      const res = await DashboardManagement.getProviceList({})

      return (
        res?.map((item: any) => ({
          label: item.provinceName,
          value: item.provinceId
        })) || []
      )
    }
  })

  const { data: serviceList, ...serviceRest } = useQuery<OptionType[]>({
    queryKey: ['serviceList'],
    queryFn: async () => {
      const res = await DashboardManagement.getServiceList({})

      return (
        res?.map((item: any) => ({
          label: item.productName,
          value: item.uniqueId
        })) || []
      )
    }
  })

  return {
    provinceList,
    serviceList,
    provinceQuery: provinceRest,
    serviceQuery: serviceRest
  }
}
