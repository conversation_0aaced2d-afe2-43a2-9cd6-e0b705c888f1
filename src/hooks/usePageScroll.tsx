import { useCallback, useEffect, useMemo, useState } from 'react'

import { useQuery } from '@tanstack/react-query'

import { formatPaginationData } from '@/hooks/util'

/** Function xử lý gọi API phân trang theo dạng scroll vô tận để sử dụng với Antd Table
 * * keyQuery: Key Query của API truyền vào
 * * callFn: Function gọi API
 * * defaultParams: Các Params mặc định của API
 * * queryConfig Các: cài đặt khi gọi API (pa
 * */
export const usePageScroll = (
  // Key Query của API truyền vào
  keyQuery: any[],

  // Function gọi API
  callFn: (params: { [key: string]: any; size: number; page: number }, signal?: any) => any,

  // Các Params mặc định của API
  defaultParams?: { [key: string]: any },

  // Các cài đặt khi gọi API
  queryConfig?: { [key: string]: any; pageSize?: number; threshold?: number }
) => {
  /** Trang hiện tại của API */
  const [page, setPage] = useState<number>(1)

  /** Kích cỡ trang hiện tại */
  const pageSize = useMemo(() => queryConfig?.pageSize || 10, [queryConfig])

  /** Thứ tự sắp xếp hiện tại của bảng */
  const [sort, setSort] = useState<string>(defaultParams?.sort || null)

  /** Quay lại trang đầu tiên mỗi khi giá trị defaultParams truyền vào thay đổi */
  useEffect(() => {
    // Quay lại trang đầu tiên khi các tham số mặc định của API bị thay đổi
    setPage(1)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [JSON.stringify(defaultParams)])

  /** Gọi API lấy danh sách phân trang */
  const { data, error, isLoading, isFetching, refetch } = useQuery({
    placeholderData: prev => prev,
    queryKey: [...keyQuery, defaultParams, page, pageSize, sort, 'usePageScroll'],
    queryFn: () =>
      callFn({
        ...defaultParams,
        page: 0,
        size: page * pageSize,
        sort
      })
        // Xử lý và đặt lại giá trị data dựa theo dữ liệu trả về của API
        .then(formatPaginationData),
    ...queryConfig
  })

  /** Xử lý load thêm dữ liệu của bảng */
  const loadMore: () => void = useCallback(() => {
    if (!isFetching && data?.total > page * pageSize) setPage(page + 1)
  }, [isFetching, data, page, pageSize])

  /** Xử lý load thêm dữ liệu của bảng khi scroll đến cuối trang */
  const onScroll: (event: any) => void = useCallback(
    event => {
      const scrollTop =
        event?.target?.scrollTop !== undefined ? event.target.scrollTop : document.documentElement.scrollTop

      const scrollHeight =
        event?.target?.scrollHeight !== undefined ? event.target.scrollHeight : document.documentElement.scrollHeight

      const clientHeight =
        event?.target?.clientHeight !== undefined ? event.target.clientHeight : document.documentElement.clientHeight

      // Chiều cao tối đa của thanh scroll
      const maxScroll = scrollHeight - clientHeight

      // Trạng thái khi đã scroll được ít nhất 99% chiều cao của thanh scroll
      const inTheEnd = queryConfig?.threshold
        ? queryConfig.threshold > 1
          ? maxScroll - scrollTop <= queryConfig.threshold
          : scrollTop / maxScroll >= queryConfig.threshold
        : scrollTop / maxScroll >= 0.99

      // Gọi API khi đã scroll tới gần cuối trang, VÀ API hiện tại đang không được gọi + có thể gọi (kích cỡ 1 trang x tổng số trang < kích cỡ tối đa của API)
      if (inTheEnd && !isFetching && data?.total > page * pageSize) loadMore()
    },
    [loadMore, isFetching, data, page, pageSize, queryConfig]
  )

  /** Xử lý khi bấm nút trên bảng */
  const onChange: (
    pagination: { current: number; pageSize: number },
    filters: any,
    sorter: { order: string; field: string },
    { action }: { action: string }
  ) => void = useCallback((pagination, filters, sorter, { action }) => {
    if (action === 'sort') {
      if (!sorter.order) {
        setSort('')
      } else {
        setSort(`${sorter.field},${sorter.order.slice(0, -3).toUpperCase()}`)
      }
    }
  }, [])

  return {
    data,
    error,
    isLoading,
    isFetching,
    page,
    pageSize,
    content: (data?.content || []) as any[],
    total: data?.total,
    refetch,
    loadMore,
    onChange,
    onScroll
  }
}
