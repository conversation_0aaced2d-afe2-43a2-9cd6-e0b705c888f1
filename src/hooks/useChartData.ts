// hooks/useChartData.ts
import { useRef, useState, useEffect } from 'react'

import { useParams } from 'next/navigation'

import { useQuery } from '@tanstack/react-query'

import DashboardManagement from '@/models/DashboardManagement'
import { useUser } from '@/hooks'
import { DATE_FORMAT, DEFAULT_END_TIME, DEFAULT_START_TIME } from '@/components/partner-management/common/constants'

export interface ChartParams {
  reportCycle: number
  chartType: number
  lstServiceProductUniqueId?: number[]
  startTime: string
  endTime: string
  lstCustomerType?: string[]
  serviceType: number
  lstProvinceId: number[]
  labelTime?: string
  top?: number
  id: string | number
}

export const useChartData = (chartName: string, apiMethod: 'overviewServiceRevenue' | 'overviewTopPricing') => {
  const { user } = useUser()
  const { id } = useParams<{ id: string }>()

  const [activeTab, setActiveTab] = useState('GROWTH')
  const [reportCycle, setReportCycle] = useState(1)
  const [topFilter, setTopFilter] = useState(5)

  // Base params
  const baseParams: Omit<ChartParams, 'chartType'> = {
    reportCycle: 1,
    startTime: DEFAULT_START_TIME.format(DATE_FORMAT),
    endTime: DEFAULT_END_TIME.format(DATE_FORMAT),
    serviceType: -1,
    lstProvinceId: [-1],
    id: id || user.id,
    ...(apiMethod === 'overviewServiceRevenue' && {
      lstServiceProductUniqueId: [-1],
      lstCustomerType: ['ALL']
    }),
    ...(apiMethod === 'overviewTopPricing' && {
      top: 5
    })
  }

  // Params for different chart types
  const cumulativeParams = useRef<ChartParams>({
    ...baseParams,
    chartType: 2
  })

  const growthParams = useRef<ChartParams>({
    ...baseParams,
    chartType: 1
  })

  // Update params when filters change
  useEffect(() => {
    const updates = {
      reportCycle,
      ...(apiMethod === 'overviewTopPricing' && { top: topFilter })
    }

    Object.assign(cumulativeParams.current, updates)
    Object.assign(growthParams.current, updates)
  }, [reportCycle, topFilter, apiMethod])

  // API queries
  const growthQuery = useQuery({
    queryKey: ['growth', chartName, growthParams.current, reportCycle, topFilter],
    queryFn: () => DashboardManagement[apiMethod](growthParams.current),
    enabled: !!chartName && activeTab === 'GROWTH',
    staleTime: 5 * 60 * 1000,
    retry: 2
  })

  const cumulativeQuery = useQuery({
    queryKey: ['cumulative', chartName, cumulativeParams.current, reportCycle, topFilter],
    queryFn: () => DashboardManagement[apiMethod](cumulativeParams.current),
    enabled: !!chartName && activeTab === 'CUMULATIVE',
    staleTime: 5 * 60 * 1000,
    retry: 2
  })

  // Tab change handler
  const handleTabChange = (key: string) => {
    setActiveTab(key)
    setReportCycle(1)

    if (apiMethod === 'overviewTopPricing') {
      setTopFilter(5)
    }
  }

  return {
    activeTab,
    reportCycle,
    topFilter,
    setReportCycle,
    setTopFilter,
    handleTabChange,
    growthData: growthQuery.data,
    cumulativeData: cumulativeQuery.data,
    isLoadingGrowth: growthQuery.isFetching,
    isLoadingCumulative: cumulativeQuery.isFetching,
    growthParams: growthParams.current,
    cumulativeParams: cumulativeParams.current
  }
}
