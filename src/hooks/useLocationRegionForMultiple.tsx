import { useState, useCallback } from 'react'

import { useQuery } from '@tanstack/react-query'

import NewAddress from '@/models/NewAddress'

import Address from '@/models/Address'

export const useLocationRegionForMultiple = (key = '') => {
  const [address, setAddress] = useState({
    regionId: null,
    provinceId: [],
    wardId: [],
    streetId: []
  })

  const [provinceSearch, setProvinceSearch] = useState('')
  const [wardSearch, setWardSearch] = useState('')
  const [streetSearch, setStreetSearch] = useState('')

  const lastIdValue = (id: string) => {
    return id.substring(id.lastIndexOf('_') + 1)
  }

  const updateAddress = useCallback(
    (field: any, newValue: any, updateProvinceId: any = null, updateWardId: any = null, updateStreetId: any = null) => {
      setAddress(prev => {
        const newAddress = { ...prev, [field]: newValue }

        if (field === 'regionId') {
          newAddress.provinceId = updateProvinceId
          newAddress.wardId = updateWardId
          newAddress.streetId = updateStreetId
        } else if (field === 'provinceId') {
          newAddress.wardId = updateWardId
          newAddress.streetId = updateStreetId
        } else if (field === 'wardId') {
          newAddress.streetId = updateStreetId
        }

        return newAddress
      })
    },
    []
  )

  const { data: regionList, isFetching: loadingCountry } = useQuery({
    queryKey: ['getAllNation', key],
    queryFn: async () => {
      const res = await Address.getAllRegion({})

      return res.map((item: any) => ({ value: item.id, label: item.name }))
    },
    initialData: []
  })

  const { data: provinceList, isFetching: loadingProvince } = useQuery({
    queryKey: ['getProvinceById', address.regionId, key, provinceSearch],
    queryFn: async () => {
      const res = await NewAddress.getProvinces({ name: provinceSearch })

      return res?.content?.map((item: any) => ({
        label: item.name,
        value: item.id,
        code: item.code,
        id: item.id
      }))
    },
    initialData: []
  })

  const { data: wardList, isFetching: loadingWard } = useQuery({
    queryKey: ['getWardById', address.provinceId, key, wardSearch],
    queryFn: async () => {
      if (!address.provinceId) return []

      const res = await NewAddress.getWardsByParams({
        // @ts-ignore
        provinceIds: (address.provinceId || []).join(','),
        name: wardSearch
      })

      return res?.content?.map((e: any) => {
        return { value: `${e.id}`, label: e.name, code: e.code }
      })
    },
    initialData: [],
    enabled: !!address.provinceId
  })

  const { data: streetList, isFetching: loadingStreet } = useQuery({
    queryKey: ['getStreetById', address.wardId, address.provinceId, key, streetSearch],
    queryFn: async () => {
      if (!address.provinceId || !address.wardId) return []

      const res = await NewAddress.getStreetsByParams({
        provinceIds: (address.provinceId || []).join(','),
        wardIds: (address.wardId || []).join(','),
        name: streetSearch
      })

      return res.content.map((e: any) => {
        const renderNewValue = `${e.id}`

        return { value: renderNewValue, label: e.name }
      })
    },
    initialData: [],
    enabled: !!address.wardId && address.wardId?.length > 0
  })

  return {
    address,
    updateAddress,
    regionList,
    provinceList,
    wardList,
    streetList,
    loadingCountry,
    loadingProvince,
    loadingWard,
    loadingStreet,
    setProvinceSearch,
    setWardSearch,
    setStreetSearch,
    lastIdValue
  }
}
