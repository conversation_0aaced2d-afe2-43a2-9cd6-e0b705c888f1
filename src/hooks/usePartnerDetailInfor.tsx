import { useQuery } from '@tanstack/react-query'

import PartnerManagement from '@/models/PartnerManagement'

export const usePartnerDetailInfo = (id: string | number, isDashboard?: boolean) => {
  return useQuery({
    queryKey: ['getPartnerDetailInfor', id],
    queryFn: async () => {
      const res = await PartnerManagement.getPartnerDetailInfo(id)

      return res
    },
    enabled: !!id && !isDashboard
  })
}
