import { useMemo } from 'react'

import { convertCapabilities } from '@/utils/partnerUtils'

import { CUSTOMER_TYPES, GENDER_LABELS } from '@/components/partner-management/common/constants'

export const usePartnerDataTransform = (data: any, onLocationClick: () => void) => {
  const generalInfo = useMemo(() => {
    if (!data) return []

    if (data.customerType === CUSTOMER_TYPES.PERSONAL) {
      return [
        { label: 'Loại hình đối tác', value: 'Cá nhân' },
        { label: 'Họ và tên', value: data.name },
        { label: 'Loại giấy chứng thực', value: data.repPersonalCertType },
        { label: 'Số giấy chứng thực', value: data.repPersonalCertNumber }
      ]
    }

    return [
      { label: 'Loại hình đối tác', value: '<PERSON><PERSON><PERSON> nghiệp' },
      { label: 'Tê<PERSON> do<PERSON>h nghiệp', value: data.name },
      { label: 'Mã số thuế', value: data.taxCode },
      { label: 'Mã BHXH', value: data.socialInsuranceNumber },
      { label: 'Website', value: data.website }
    ]
  }, [data])

  const addressInfo = useMemo(() => {
    if (!data) return []

    return [
      { label: 'Quốc gia', value: 'Việt Nam' },
      { label: 'Tỉnh/Thành', value: data.provinceName },
      { label: 'Phường/Xã', value: data.wardName },
      { label: 'Phố/Đường', value: data.streetName },
      { label: 'Địa chỉ đăng ký kinh doanh', value: data.businessRegistrationAddress }
    ]
  }, [data])

  const contactInfo = useMemo(() => {
    if (!data) return []

    return [
      { label: 'Số điện thoại chính', value: data.phoneNumber },
      { label: 'Email chính', value: data.primaryEmail },
      {
        label: 'Liên hệ phụ',
        value: `${data.secondaryContacts?.length || 0} liên hệ`,
        type: 'contact-list',
        data: data.secondaryContacts
      },
      { label: 'Địa chỉ văn phòng làm việc', value: data.officeAddress }
    ]
  }, [data])

  const representativeInfo = useMemo(() => {
    if (!data?.representative) return []

    const rep = data.representative

    return [
      { label: 'Người đại diện pháp luật', value: rep.name },
      { label: 'Chức danh', value: rep.position },
      { label: 'Giới tính', value: GENDER_LABELS[rep.gender as keyof typeof GENDER_LABELS] },
      { label: 'Ngày sinh', value: rep.dateOfBirth },
      { label: 'Loại giấy chứng thực', value: rep.certType },
      { label: 'Số giấy chứng thực', value: rep.certNo },
      { label: 'Ngày cấp', value: rep.certIssueDate },
      { label: 'Nơi cấp', value: rep.certIssuePlace },
      { label: 'Nơi đăng ký hộ khẩu', value: rep.registeredResidence },
      { label: 'Chỗ ở hiện tại', value: rep.currentAddress }
    ]
  }, [data])

  const capacityInfo = useMemo(() => {
    if (!data) return []

    return [
      {
        label: 'Năng lực cung cấp',
        value: convertCapabilities(data.capabilities).join(', ')
      },
      {
        label: 'Địa bàn cung cấp',
        value: `${data.operatingAreas?.length || 0} địa bàn`,
        onClick: onLocationClick,
        clickable: true
      }
    ]
  }, [data, onLocationClick])

  const financeInfo = useMemo(() => {
    if (!data?.bankAccount) return []

    return [
      { label: 'Tên chủ tài khoản', value: data.bankAccount.name },
      { label: 'Tên ngân hàng', value: data.bankAccount.bank },
      { label: 'Số tài khoản', value: data.bankAccount.number }
    ]
  }, [data])

  return {
    generalInfo,
    addressInfo,
    contactInfo,
    representativeInfo,
    capacityInfo,
    financeInfo
  }
}
