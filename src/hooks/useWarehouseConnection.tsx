import { message } from 'antd'

import { useMutation, useQuery } from '@tanstack/react-query'

import warehouseInstance from '@/models/Warehouse'

export const useWarehouseConnection = ({ code }: { code: any }) => {
  const convertAPI = useMutation({
    mutationKey: ['data'],
    mutationFn: warehouseInstance.uploadJSON,
    onSuccess: () => {
      message.success('Lấy dữ liệu thành công')
    },
    onError: () => {
      message.error('Có lỗi xảy ra')
    }
  })

  const onSendFormatAPI = async (data: any) => {
    try {
      const response = await convertAPI.mutateAsync(data)

      return response // Return the response so it can be used by the caller
    } catch (error) {
      console.error('Error sending data:', error)
      throw error // Re-throw so the caller can handle it
    }
  }

  const {
    data: lstDataTemplate,
    refetch: refetchDataTemplate,
    isLoading: loadingDataTemplate
  } = useQuery({
    queryKey: ['dataTemplate', code],
    queryFn: async () => {
      const res = await warehouseInstance.dataTemplate(code)

      return res
    },
    enabled: !!code
  })

  return {
    onSendFormatAPI,
    data: convertAPI.data,
    lstDataTemplate,
    refetchDataTemplate,
    loadingDataTemplate,
    isLoadingFormatAPI: convertAPI.isPending
  }
}
