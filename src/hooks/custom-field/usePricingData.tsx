import { useCallback, useReducer } from 'react'

import { useParams } from 'next/navigation'

import { useQuery } from '@tanstack/react-query'

import { useSelector } from 'react-redux'

import DevService from '@/models/DevService'
import { useUser } from '../useUser'
import { selectActiveKey } from '@/redux-store/slices/customFieldSlice'

export interface PricingItem {
  icon: string
  externalLink: string | null
  isSold: string | null
  approve: string
  approveProcessing: string
  modifiedAt: string
  createdAt: number
  recommendedStatus: string
  pricingOrder: number
  id: number
  serviceName: string
  pricingName: string
  pricingCode: string
  price: number | null
  status: string
  pricingStrategies: [
    {
      id: number
      planName: string | null
      paymentCycle: number
      cycleType: string
      numberOfCycles: number
      pricingPlan: string
      numberOfTrial: number | null
      trialType: string
      unitId: number | null
      unitName: string | null
      freeQuantity: number | null
      defaultCircle: string
      price: number
      cycleCode: string
      unitLimitedList: []
      addonList: []
      customerTypeCode: string[]
      displayStatus: number
      referenceId: number | null
      pricingId: number | null
      approveStatus: string
      priority: number | null
      exchangedPricingStrategy: []
      createdAt: number
      minimumQuantity: number | null
      maximumQuantity: number | null
    }
  ]
  priority: number | null
  creationLayoutId: number
  isOneTime: number
  isDefault: boolean
  approveValue: number
}

export const INITIAL_DATA: PricingItem[] = [
  {
    icon: '/resources/upload/file/services/images/02082023/2023080213485cb84e12-2742-45c8-9c9b-04f463a1df4c.JPG',
    externalLink: null,
    isSold: 'NOT_SOLD_YET',
    approve: 'APPROVED',
    approveProcessing: 'APPROVED',
    modifiedAt: '26/04/2025 03:04:22',
    createdAt: 1690959611370,
    recommendedStatus: 'UN_RECOMMEND',
    pricingOrder: 1,
    id: 2570,
    serviceName: 'trùng biến thể',
    pricingName: 'ôiiiewr',
    pricingCode: '847',
    price: null,
    status: 'VISIBLE',
    pricingStrategies: [
      {
        id: 6184,
        planName: null,
        paymentCycle: -1,
        cycleType: 'MONTHLY',
        numberOfCycles: -1,
        pricingPlan: 'FLAT_RATE',
        numberOfTrial: null,
        trialType: 'DAILY',
        unitId: null,
        unitName: null,
        freeQuantity: null,
        defaultCircle: 'YES',
        price: 10000,
        cycleCode: '',
        unitLimitedList: [],
        addonList: [],
        customerTypeCode: ['ENTERPRISE', 'PERSONAL', 'HOUSE_HOLD'],
        displayStatus: 1,
        referenceId: null,
        pricingId: 3123,
        approveStatus: 'APPROVED',
        priority: null,
        exchangedPricingStrategy: [],
        createdAt: 1690959611097,
        minimumQuantity: null,
        maximumQuantity: null
      }
    ],
    priority: null,
    creationLayoutId: 10,
    isOneTime: 0,
    isDefault: true,
    approveValue: 1
  },
  {
    icon: '/resources/upload/file/services/images/02082023/2023080213485cb84e12-2742-45c8-9c9b-04f463a1df4c.JPG',
    externalLink: null,
    isSold: null,
    approve: 'UNAPPROVED',
    approveProcessing: 'UNAPPROVED',
    modifiedAt: '26/04/2025 03:04:22',
    createdAt: 1745634933899,
    recommendedStatus: 'UN_RECOMMEND',
    pricingOrder: 2,
    id: 5321,
    serviceName: 'trùng biến thể',
    pricingName: 'Gói chưa duyệt',
    pricingCode: 'GOI12121212',
    price: null,
    status: 'INVISIBLE',
    pricingStrategies: [
      {
        id: 14944,
        planName: null,
        paymentCycle: -1,
        cycleType: 'MONTHLY',
        numberOfCycles: -1,
        pricingPlan: 'FLAT_RATE',
        numberOfTrial: null,
        trialType: 'DAILY',
        unitId: null,
        unitName: null,
        freeQuantity: null,
        defaultCircle: 'YES',
        price: 70000,
        cycleCode: '',
        unitLimitedList: [],
        addonList: [],
        customerTypeCode: ['ENTERPRISE', 'PERSONAL', 'HOUSE_HOLD'],
        displayStatus: 1,
        referenceId: null,
        pricingId: null,
        approveStatus: 'UNAPPROVED',
        priority: null,
        exchangedPricingStrategy: [],
        createdAt: 1745634933915,
        minimumQuantity: null,
        maximumQuantity: null
      }
    ],
    priority: null,
    creationLayoutId: 10,
    isOneTime: 0,
    isDefault: false,
    approveValue: 0
  }
]

export type PricingActionType =
  | { type: 'TOGGLE_EXPAND'; payload: string }
  | { type: 'SET_EXPANDED'; payload: string[] }
  | { type: 'TOGGLE_VISIBILITY'; payload: { key: string; value: boolean } }
  | { type: 'UPDATE_ITEM'; payload: { key: string; data: Partial<PricingItem> } }
  | { type: 'SET_DATA'; payload: PricingItem[] }
  | { type: 'LOADING'; payload: boolean }

export interface PricingState {
  data: PricingItem[]
  expandedRowKeys: string[]
  loading: boolean
  error: string | null
}

// components/PricingTable/usePricingData.ts - Cải tiến hook

// Reducer function để quản lý state
const pricingReducer = (state: PricingState, action: PricingActionType): PricingState => {
  switch (action.type) {
    case 'TOGGLE_EXPAND':
      return {
        ...state,
        expandedRowKeys: state.expandedRowKeys.includes(action.payload)
          ? state.expandedRowKeys.filter(key => key !== action.payload)
          : [...state.expandedRowKeys, action.payload]
      }
    case 'SET_EXPANDED':
      return {
        ...state,
        expandedRowKeys: action.payload
      }

    case 'SET_DATA':
      return {
        ...state,
        data: action.payload,
        loading: false,
        error: null
      }
    case 'LOADING':
      return {
        ...state,
        loading: action.payload
      }
    default:
      return state
  }
}

export const usePricingData = (initialData = INITIAL_DATA) => {
  const { user } = useUser()

  const { id } = useParams<{ id: string }>()

  const activeKey = useSelector(selectActiveKey)

  // Khởi tạo state với useReducer
  const [state, dispatch] = useReducer(pricingReducer, {
    data: initialData,
    expandedRowKeys: ['2'], // Default expanded row
    loading: false,
    error: null
  })

  // Callback cho việc mở rộng/thu gọn hàng
  const handleExpand = useCallback((expanded: boolean, record: PricingItem) => {
    dispatch({ type: 'TOGGLE_EXPAND', payload: String(record.id) })
  }, [])

  // Có thể thêm fetch từ API
  const fetchData = useCallback(async () => {
    dispatch({ type: 'LOADING', payload: true })

    try {
      // Đây là giả lập fetch, trong thực tế sẽ gọi API
      // const response = await fetch('/api/pricing');
      // const data = await response.json();
      // dispatch({ type: 'SET_DATA', payload: data });

      // Giả lập delay để demo loading state
      setTimeout(() => {
        dispatch({ type: 'SET_DATA', payload: initialData })
      }, 500)
    } catch (error) {
      console.error('Error fetching pricing data:', error)
      // setState({ ...state, error: error.message, loading: false });
    }
  }, [initialData])

  const { data: dataPriceList } = useQuery<PricingItem[]>({
    queryKey: ['getProcessingPriceList', id],
    queryFn: async () => {
      const res: PricingItem[] = await DevService.getPricingList(
        !user.portalType ? 'admin' : user.portalType === 'ADMIN' ? 'admin' : 'dev'
      )(id, { tab: activeKey })

      return res
    },
    enabled: !!id && !!user
  })

  return {
    ...state,
    data: dataPriceList,
    handleExpand,
    refreshData: fetchData
  }
}
