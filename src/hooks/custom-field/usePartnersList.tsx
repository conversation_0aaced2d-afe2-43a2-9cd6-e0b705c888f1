'use client'

import type { ReactNode } from 'react'
import { useCallback, useState } from 'react'

import { useRouter } from 'next/navigation'

import { useMutation, useQuery } from '@tanstack/react-query'

import type { TableProps } from 'antd'
import { Tooltip, Tag } from 'antd'

import { debounce } from 'lodash'

import type { CheckboxOption } from '@/components/filter/SettingInput'
import { message } from '@/components/notification'

import { DATE_FORMATS } from '@/constants/constantEnum'
import { usePage } from '@/hooks'
import type { NotificationInfoType } from '@/types/notificationModalTypes'
import type { CheckedState } from '@components/filter'
import {
  statusToUI,
  activeStatusToUI,
  partnerCheckboxOptions,
  CUSTOMER_TYPES
} from '@components/partner-management/common/constants'
import { pageSizeOptions } from '@views/product-catalog/constants/constants'
import type { IPartnerParams } from '@/components/inventory/types/inventory'
import AccountDev from '@/models/AccountDev'
import Common from '@/models/Common'
import { convertCapabilities } from '@/utils/partnerUtils'

const checkboxOptions = [
  { label: 'Tên đối tác', key: 'isName' },
  { label: 'Mã đối tác', key: 'isCode' }
]

export const usePartnersList = () => {
  const router = useRouter()

  const [checked, setChecked] = useState<CheckedState>({ isCode: true, isName: true })

  const [openOperatingAreaLst, setOpenOperatingAreaLst] = useState({
    open: false,
    data: [] as any[]
  })

  const checkBoxOptions: CheckboxOption[] = [
    { label: 'Tên đối tác', key: 'isName' },
    { label: 'Mã đối tác', key: 'isCode' },
    { label: 'Email đối tác', key: 'isEmail' },
    { label: 'Số điện thoại đối tác', key: 'isPhoneNumber' }
  ]

  const defaultSearchParams: IPartnerParams = {
    page: 0,
    size: 10,
    isEmail: 1,
    isName: 1,
    isPhoneNumber: 1,
    customerType: null,
    capabilities: null,
    provinceIds: null,
    status: -1,
    state: null,
    taxCodes: null,
    repPersonalCertNumbers: null
  }

  const [filterParams, setFilterParams] = useState<any>(defaultSearchParams)

  const [selectedColumns, setSelectedColumns] = useState<string[]>([
    'code',
    'name',
    'customerType',
    'capabilityLst',
    'operatingAreaLst',
    'status',
    'state',
    'provinceName'
  ])

  const CapabilityTags = ({ capabilities }: { capabilities: string[] }) => {
    const convertedCaps = convertCapabilities(capabilities)

    if (!convertedCaps.length) {
      return <span>-</span>
    }

    if (convertedCaps.length === 1) {
      return (
        <Tag bordered={false} className='bg-gray-1 text-gray-8'>
          {convertedCaps[0]}
        </Tag>
      )
    }

    const [firstCap, ...remainingCaps] = convertedCaps

    const tooltipContent = (
      <div className='flex max-w-[520px] flex-wrap gap-2'>
        {remainingCaps.map((cap: string, idx: number) => (
          <Tag key={`${cap}-${idx}`} bordered={false} className='bg-gray-1 text-gray-8'>
            {cap}
          </Tag>
        ))}
      </div>
    )

    return (
      <div className='flex items-center gap-2'>
        <Tag bordered={false} className='bg-gray-1 text-gray-8'>
          {firstCap}
        </Tag>
        <Tooltip color='white' title={tooltipContent} placement='topLeft' mouseEnterDelay={0.15}>
          <Tag className='cursor-pointer text-xs text-gray-11'>+{remainingCaps.length}</Tag>
        </Tooltip>
      </div>
    )
  }

  const mainColumns: TableProps<any>['columns'] = [
    {
      title: 'Mã đối tác',
      dataIndex: 'code',
      key: 'code',
      width: 200,
      align: 'left',
      sorter: (a, b) => a.code?.localeCompare(b.code),
      render: (value: string) => <div className='text-color-blue text-underline font-medium'>{value}</div>
    },
    {
      title: 'Tên đối tác',
      dataIndex: 'name',
      key: 'name',
      width: 350,
      align: 'left',
      render: (value: string, record: any) => (
        <div className='flex cursor-pointer items-center gap-x-2 truncate'>
          <div className='flex items-center gap-x-1'>
            {record.customerType === CUSTOMER_TYPES.ENTERPRISE ? (
              <i className='onedx-business size-6 text-text-primary-default' />
            ) : (
              <i className='onedx-user1 size-6 text-text-primary-default' />
            )}
            <Tooltip title={value} placement='topLeft'>
              <div
                className='text-color-blue max-w-60 truncate hover:cursor-pointer'
                onClick={() => router.push(`/partner-management/${record.id}`)}
              >
                {value}
              </div>
            </Tooltip>
          </div>
        </div>
      )
    },
    {
      title: 'Loại hình đối tác',
      dataIndex: 'customerType',
      key: 'customerType',
      width: 200,
      align: 'left',
      render: (customerType: string) => {
        const typeInfo = customerType === CUSTOMER_TYPES.ENTERPRISE ? 'Doanh nghiệp' : 'Cá nhân'

        return <div className='text-color-blue'>{typeInfo}</div>
      }
    },
    {
      title: 'Năng lực cung cấp',
      dataIndex: 'capabilityLst',
      key: 'capabilityLst',
      width: 200,
      align: 'left',
      render: (capabilities: string[]): ReactNode => <CapabilityTags capabilities={capabilities} />
    },
    {
      title: 'Địa bàn hoạt động',
      dataIndex: 'operatingAreaLst',
      key: 'operatingAreaLst',
      width: 200,
      align: 'center',
      render: (operatingAreaLst: object[]) => (
        <div
          onClick={() =>
            setOpenOperatingAreaLst({
              open: true,
              data: operatingAreaLst || []
            })
          }
          className='text-color-blue cursor-pointer hover:underline'
        >
          {operatingAreaLst?.length ?? 0}
        </div>
      )
    },
    {
      title: 'Trạng thái phê duyệt',
      dataIndex: 'state',
      key: 'state',
      width: 200,
      align: 'left',
      render: (state: string) => {
        const statusInfo = statusToUI[state]

        if (!statusInfo) return <span>-</span>

        return (
          <Tag color={statusInfo?.color} bordered={false} className='w-[120px] text-center'>
            <div style={{ color: statusInfo?.textColor }}>{statusInfo?.text}</div>
          </Tag>
        )
      }
    },
    {
      title: 'Trạng thái hoạt động',
      dataIndex: 'status',
      key: 'status',
      width: 200,
      align: 'left',
      render: (status: '0' | '1') => {
        const statusInfo = activeStatusToUI[status === '1' ? 'true' : 'false']

        return (
          <Tag color={statusInfo.color} bordered={false} className='w-[120px] text-center'>
            <div style={{ color: statusInfo.textColor }}>{statusInfo.text}</div>
          </Tag>
        )
      }
    },
    {
      title: 'Tỉnh thành',
      dataIndex: 'provinceName',
      key: 'provinceName',
      ellipsis: true,
      width: 200,
      align: 'left'
    },
    {
      title: 'Số chứng thực',
      dataIndex: 'insuranceNumber',
      key: 'insuranceNumber',
      width: 200,
      align: 'left',
      render: (insuranceNumber: string) => {
        // const insuranceNumber = record.type === 'company' ? record.taxCode : record.identityNumber
        return <div className='text-color-blue'>{insuranceNumber}</div>
      }
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
      width: 250,
      align: 'left',
      render: (email: string) => (
        <Tooltip title={email} placement='topLeft'>
          <div className='text-color-blue max-w-[200px] truncate'>{email}</div>
        </Tooltip>
      )
    },
    {
      title: 'Số điện thoại',
      dataIndex: 'phoneNumber',
      key: 'phoneNumber',
      width: 200,
      align: 'left',
      render: (phone: string) => {
        return <div className='text-color-blue'>{phone}</div>
      }
    },
    {
      title: 'Địa chỉ',
      dataIndex: 'address',
      key: 'address',
      width: 250,
      align: 'left',
      render: (address: string) => (
        <Tooltip title={address} placement='topLeft'>
          <div className='text-color-blue max-w-[250px] truncate'>{address}</div>
        </Tooltip>
      )
    },
    {
      title: 'Người tạo',
      dataIndex: 'createdBy',
      key: 'createdBy',
      width: 200,
      align: 'left',
      render: (createdBy: string) => {
        // const isAdmin = record.creatorEmail === '<EMAIL>'
        // const displayText = isAdmin ? `Admin - ${record.creatorEmail}` : record.creatorEmail

        return (
          <Tooltip title={createdBy} placement='topLeft'>
            <div className='text-color-blue max-w-[200px] truncate'>{createdBy}</div>
          </Tooltip>
        )
      }
    },
    {
      title: 'Thời gian cập nhật',
      dataIndex: 'modifiedAt',
      key: 'modifiedAt',
      width: 200,
      align: 'left',
      render: (value: string) => (
        <div className='text-color-blue font-inter align-middle text-sm font-normal not-italic leading-5 tracking-[0.005em] text-gray-11'>
          {value}
        </div>
      )
    }
  ]

  const filteredColumns = mainColumns.filter(
    (col: any) => !col.dataIndex || selectedColumns.includes(col.dataIndex.toString())
  )

  const debouncedSetFilterParams = debounce(params => {
    setFilterParams(params)
  }, 200)

  const handleSearch = useCallback(
    (value: string) => {
      debouncedSetFilterParams({
        ...filterParams,
        value: value || '',
        isName: Number(Boolean(checked.isName)),
        isCode: Number(Boolean(checked.isCode))
      })
    },
    [debouncedSetFilterParams, filterParams, checked]
  )

  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([])

  const onSelectChange = useCallback((record: any, selected: boolean) => {
    setSelectedRowKeys(prev => (selected ? [...prev, record] : prev.filter(i => i.id !== record.id)))
  }, [])

  const onSelectAll = useCallback((selected: boolean, _: any, changeRows: any) => {
    setSelectedRowKeys(prev => {
      if (selected) return [...prev, ...changeRows]
      const changeRowKeys = changeRows.map((row: any) => row.id)

      return prev.filter(item => !changeRowKeys.includes(item.id))
    })
  }, [])

  const rowSelection = {
    selectedRowKeys: selectedRowKeys?.map(item => item.id),
    onSelect: onSelectChange,
    onSelectAll,
    getCheckboxProps: (record: any) => ({ name: record.name })
  }

  const { page, pageSize, content, pagination, setPageSize, refetch } = usePage(
    ['getList', 'table', filterParams],
    async () => {
      const params = { ...filterParams, type: 0, page: page - 1, pageSize, size: pageSize }

      return AccountDev.getList(params)
    },
    { sort: 'createdAt,desc' }
  )

  const { data: taxCodeList } = useQuery({
    queryKey: ['getTaxCodeList'],
    queryFn: async () => {
      const res = await Common.getTaxCodeList()

      return res.content.map((e: any) => ({
        label: e.value,
        value: e.value
      }))
    },
    initialData: []
  })

  const { data: repPersonalCertNumberList } = useQuery({
    queryKey: ['getRepPersonalCertNumberList'],
    queryFn: async () => {
      const res = await Common.getRepPersonalCertNumberList()

      const uniqueValues = Array.from(new Set(res.content.map((e: any) => e.value)))

      return uniqueValues.map((value: any) => ({
        label: value,
        value: value
      }))
    },
    initialData: []
  })

  const [infoModal, setInfoModal] = useState<NotificationInfoType>({})
  const [visibleModal, setVisibleModal] = useState(false)
  const [datePickerOpen, setDatePickerOpen] = useState(false)

  const deletePartners = useMutation({
    mutationKey: ['deletePartnersByIds'],
    mutationFn: (ids: number[]) => AccountDev.deleteByIds(ids),
    onSuccess: () => {
      message.success('Xóa đối tác thành công')
      setSelectedRowKeys([])
      refetch()
    },
    onError: error => {
      message.error('Xóa đối tác thất bại')
      console.error(error)
    }
  })

  const handleRemoveService = (ids: number[]) => {
    setVisibleModal(true)
    setInfoModal({
      iconType: 'WARNING',
      title: `Bạn có chắc chắn muốn xóa đối tác đang chọn?`,
      textButton: 'Xác nhận',
      isCloseModal: true,
      handleConfirm: () => deletePartners.mutate(ids),
      handleCancel: () => setVisibleModal(false)
    })
  }

  const handleFilterChange = (key: string, value: any) => {
    const formattedValue = Array.isArray(value) ? value.join(',') : value

    setFilterParams((prev: any) => ({ ...prev, [key]: formattedValue }))
  }

  const handleUpdateFilterChange = (params: any) => {
    setFilterParams({ ...filterParams, ...params })
  }

  const formatCount = (count: number) => (count > 0 ? String(count).padStart(2, '0') : '0')

  const handleChangeDate = (date: any) => {
    if (date?.length === 2) {
      handleFilterChange('startTime', date[0]?.format(DATE_FORMATS.TIMESTAMP))
      handleFilterChange('endTime', date[1]?.format(DATE_FORMATS.TIMESTAMP))
    } else {
      handleFilterChange('startTime', '')
      handleFilterChange('endTime', '')
    }
  }

  return {
    checked,
    setChecked,
    checkBoxOptions,
    handleSearch,
    handleFilterChange,
    handleUpdateFilterChange,
    handleChangeDate,
    selectedColumns,
    setSelectedColumns,
    filteredColumns,
    rowSelection,
    selectedRowKeys,
    formatCount,
    handleRemoveService,
    pageSizeOptions,
    pageSize,
    setPageSize,
    content,
    pagination,
    visibleModal,
    setVisibleModal,
    infoModal,
    datePickerOpen,
    setDatePickerOpen,
    partnerCheckboxOptions,
    checkboxOptions,
    openOperatingAreaLst,
    setOpenOperatingAreaLst,
    taxCodeList,
    repPersonalCertNumberList
  }
}
