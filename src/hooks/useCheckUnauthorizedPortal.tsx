import { useRouter, usePathname } from 'next/navigation'

import { useUser } from '@/hooks'
import NotAuthorized from '@views/error/NotAuthorized'
import { ADMIN_PORTAL_ROLE, DEV_PORTAL_ADMIN_ROLE } from '@/constants/common'

export const useCheckUnauthorizedPortal = () => {
  const { user } = useUser()
  const router = useRouter()
  const pathname = usePathname()

  const isAdminLogin = user?.id && user?.roles?.some(role => role === ADMIN_PORTAL_ROLE)
  const isDevLogin = user?.id && user?.roles?.some(role => role === DEV_PORTAL_ADMIN_ROLE)

  const portals = [
    { path: '/admin-portal', isLoggedIn: isAdminLogin },
    { path: '/partner-portal', isLoggedIn: isDevLogin }
  ]

  // Danh sách đường dẫn ngo<PERSON><PERSON> lệ không cần kiểm tra
  const excludedPaths = ['/payment-callback']

  // Trạng thái đường dẫn hiện tại nằm trong danh sách ngoại lệ
  const isExcluded = excludedPaths.some(path => pathname.includes(path))

  const unauthorizedPortal = portals.find(
    ({ path, isLoggedIn }) => !pathname.includes('/payment-callback') && !pathname.startsWith(path) && isLoggedIn
  )

  if (!isExcluded && unauthorizedPortal) {
    return <NotAuthorized onClick={() => router.push(unauthorizedPortal.path)} />
  }

  return null
}
