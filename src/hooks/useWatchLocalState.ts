import { useContext, useEffect, useRef, useState } from 'react'

import { isEqual } from 'lodash'

import { LocalStateContext } from '@/context/LocalStateContext'

/**
 * useWatch với selector pattern - giám sát một phần của state
 * @param selector Function để chọn phần state cần theo dõi
 * @returns Giá trị được chọn từ state
 *
 * @example
 * ```tsx
 * const userData = useWatch((state) => state.user)
 * const userEmail = useWatch((state) => state.user?.email)
 * ```
 */
export const useWatchLocal = <T>(selector: (state: any) => T): T => {
  const context = useContext(LocalStateContext)

  if (!context) {
    throw new Error('useWatch must be used within a LocalStateProvider')
  }

  const { state, _internal } = context
  const [selectedState, setSelectedState] = useState<T>(() => selector(state))
  const previousSelected = useRef<T>(selectedState)

  useEffect(() => {
    const handleStateChange = (newState: any) => {
      const newSelectedState = selector(newState)

      if (previousSelected.current !== newSelectedState) {
        setSelectedState(newSelectedState)
        previousSelected.current = newSelectedState
      }
    }

    _internal.addSubscriber(handleStateChange)

    return () => {
      _internal.removeSubscriber(handleStateChange)
    }
  }, [selector, _internal])

  // Update initial state if selector changes
  useEffect(() => {
    const newSelectedState = selector(state)

    if (!isEqual(previousSelected.current, newSelectedState)) {
      setSelectedState(newSelectedState)
      previousSelected.current = newSelectedState
    }
  }, [selector, state])

  return selectedState
}

/**
 * useWatchCallback - thực thi callback khi state thay đổi
 * @param callback Function được gọi khi state thay đổi
 *
 * @example
 * ```tsx
 * useWatchCallback((newState, oldState) => {
 *   console.log('State changed:', { newState, oldState })
 *   // Thực hiện side effects khác
 * })
 * ```
 */
export const useWatchCallback = (callback: (newState: any, oldState: any) => void): void => {
  const context = useContext(LocalStateContext)

  if (!context) {
    throw new Error('useWatchCallback must be used within a LocalStateProvider')
  }

  const { _internal } = context

  useEffect(() => {
    _internal.addSubscriber(callback)

    return () => {
      _internal.removeSubscriber(callback)
    }
  }, [callback, _internal])
}

/**
 * useWatchField - giám sát một field cụ thể trong state
 * @param fieldPath Đường dẫn đến field (string hoặc string[])
 * @returns Giá trị của field được theo dõi
 *
 * @example
 * ```tsx
 * const userEmail = useWatchField('user.email')
 * const userName = useWatchField(['user', 'name'])
 * ```
 */
export const useWatchField = <T = any>(fieldPath: string | string[]): T | undefined => {
  return useWatchLocal(state => {
    if (Array.isArray(fieldPath)) {
      return fieldPath.reduce((obj, key) => obj?.[key], state)
    }

    return fieldPath.split('.').reduce((obj, key) => obj?.[key], state)
  })
}
