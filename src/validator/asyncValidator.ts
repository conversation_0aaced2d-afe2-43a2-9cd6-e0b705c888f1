import isNil from 'lodash/isNil'

import { toBoolean } from '@/utils/string'

interface AsyncValidatorConfig {
  validatorFn: (value: string, ...args: any) => Promise<boolean>
  resultPassed?: boolean
  debounceMs?: number
  errorMessage?: string
  validationCondition?: (value: string) => boolean
  onSuccess?: (value: boolean) => void
  onFailure?: (value: boolean) => void
  onFinally?: (value: boolean) => void
}

/**
 * Tạo validator bất đồng bộ với debounce
 * @note chỉ dùng với các api validate trả về dạng true / false
 * @note Dùng lodash debounce không phù hợp vì nó không thể hủy promise trước đó được => lỗi trả về result cũ
 * @param config
 * @param config.resultPassed mặc định true, tức là nếu api trả về true thì hợp lệ, false thì không hợ<PERSON> lệ, báo lỗi errorMessage
 *                            nếu đặt false thì ngượ<PERSON> lại, api trả về false thì hợp lệ, true thì không hợp lệ, báo lỗi errorMessage
 * @param config.validationCondition điều kiện để thực hiện validate, nếu không thỏa điều kiện này thì bỏ qua validate
 * @returns { validator: (rule, value) => Promise<void> }
 */
export const createAsyncValidator = (config: AsyncValidatorConfig) => {
  const {
    validatorFn,
    debounceMs = 500,
    errorMessage = 'Có lỗi xảy ra',
    resultPassed = true,
    validationCondition,
    onSuccess,
    onFailure,
    onFinally
  } = config

  let timeoutId: NodeJS.Timeout | null = null
  let pendingPromise: {
    resolve: (value?: any) => void
    reject: (reason?: any) => void
  } | null = null

  const validator = (_: any, value: string) => {
    // Clear timeout trước đó nếu có
    if (timeoutId) {
      clearTimeout(timeoutId)
    }

    // Reject promise trước đó nếu có
    if (pendingPromise) {
      pendingPromise.resolve() // Resolve thay vì reject để không gây lỗi
      pendingPromise = null
    }

    return new Promise<void>((resolve, reject) => {
      // Lưu promise handlers
      pendingPromise = { resolve, reject }

      // Tạo timeout mới
      timeoutId = setTimeout(async () => {
        try {
          const response = await validatorFn(value)

          // Kiểm tra xem promise này có còn là pending promise hiện tại không
          if (pendingPromise?.resolve !== resolve) {
            return // Ignore nếu đã có validation mới hơn
          }

          if (isNil(response)) {
            resolve()

            return
          }

          // Nếu có điều kiện validate phụ, và không thỏa điều kiện này thì bỏ qua validate
          if (validationCondition && !validationCondition(value)) {
            resolve()

            return
          }

          const result = resultPassed ? toBoolean(response) : !toBoolean(response)

          if (!result) {
            onFailure && onFailure(response)
            reject(new Error(errorMessage))
          } else {
            onSuccess && onSuccess(response)
            resolve()
          }

          // Clear pending promise
          onFinally && onFinally(response)
          pendingPromise = null
        } catch (error: any) {
          // Kiểm tra xem promise này có còn là pending promise hiện tại không
          if (pendingPromise?.reject !== reject) {
            return // Ignore nếu đã có validation mới hơn
          }

          console.warn('Async validation error:', error)
          reject(new Error(error?.message || 'Có lỗi xảy ra'))

          // Clear pending promise
          onFinally && onFinally(false)
          pendingPromise = null
        }
      }, debounceMs)
    })
  }

  return { validator }
}

// Thêm function wrapper để truyền thêm các tham số khác cho validatorFn nếu cần
// example: const validateEmailDuplicate = createAsyncValidatorWithParams(config)
//          validateEmailDuplicate(param1, param2, ...)
export const createAsyncValidatorWithParams =
  (config: AsyncValidatorConfig) =>
  (...params: any) => {
    const isObjectValidatorFn = params.isObjectValidatorFn || false

    if (isObjectValidatorFn) {
      const validatorFn = (value: string) => config.validatorFn({ value, ...params })

      return createAsyncValidator({ ...config, validatorFn })
    }

    const validatorFn = (value: string) => config.validatorFn(value, ...params)

    return createAsyncValidator({ ...config, validatorFn })
  }

export default createAsyncValidator
