import type { ClipboardEvent, KeyboardEvent } from 'react'

import type { FormInstance } from 'antd'
import type { RuleObject } from 'antd/es/form'
import type { StoreValue } from 'antd/es/form/interface'

import { isEmpty, trim } from 'lodash'

import AdminTicket from '@/models/AdminTicket'
import { isValidVideoUrl } from '@/utils/videoUtils'

// Validate trường bắt buộc nhập
export function validateRequire(message?: any, required: boolean = true) {
  return {
    required,
    message: message || 'Vui lòng không bỏ trống mục này'
  }
}

// region Bắt buộc
/** Validate Bắt buộc nhập */
export const validateRequireInput = (message?: any, required = true) => {
  return {
    required,
    validator: (_: any, value: string) => {
      if (!required) return Promise.resolve()

      if (!!value && (typeof value === 'string' ? value.trim() !== '' : true)) {
        return Promise.resolve()
      }

      return Promise.reject(message || 'Vui lòng không bỏ trống mục này')
    }
  }
}

// Validate truong nhap toi da bao nhieu ky tu
export function validateMaxLengthStr(maxLength: number, message?: string) {
  return {
    max: maxLength,
    message: message || 'Vui lòng nhập tối đa ký tự cho phép'
  }
}

// endregion

// region Độ dài
/** Validate Độ dài */
export const validateStrLength = (max1: number, max2: number, message: string) => {
  return {
    validator: (_: any, value: string | any[]) => {
      value = value || ''

      if ((value && (value.length === max1 || value.length === max2)) || value.length === 0) {
        return Promise.resolve()
      }

      return Promise.reject(message || 'Vui lòng chỉ nhập tối đa số ký tự cho phép')
    }
  }
}

// endregion

// region Code
/** Validate các mã code */
export const validateCode = (message?: string, regex?: RegExp) => {
  const rg = regex || /^[a-zA-Z0-9\s\-_/|]+$/

  return {
    validator: (_: any, value: string) => {
      // Kiểm tra nếu value là undefined hoặc null
      if (value === undefined || value === null || value === '') {
        return Promise.resolve()
      }

      const valueTrim = value.trim()

      // Kiểm tra nếu giá trị sau khi trim là rỗng
      if (!valueTrim) {
        return Promise.resolve()
      }

      // Kiểm tra regex chỉ khi có giá trị
      if (!rg.test(valueTrim)) {
        return Promise.reject(message || 'Vui lòng nhập đúng định dạng')
      }

      return Promise.resolve()
    }
  }
}

// Validate trường phải là số
export function validateNumberInput(message: string) {
  const regex = /^[0-9]+$/g

  return {
    validator: (_: any, value: any) => {
      if (trim(value)) {
        if (new RegExp(regex).test(value) || !value) {
          return Promise.resolve()
        }

        return Promise.reject(message || 'Vui lòng nhập số')
      }

      return Promise.resolve()
    }
  }
}

// endregion

// region Mật khẩu
/** Validate Mật khẩu */
export function validatePassword(message?: string) {
  return {
    validator: (_: any, value: any) => {
      if (value && value.length) {
        const isASCII = /^[\x20-\x7E]+$/.test(value)

        if (!isASCII) {
          return Promise.reject('Mật khẩu chỉ được chứa ký tự a-z, A-Z và không được chứa các ký tự biểu tượng')
        }

        // Kiểm tra độ dài mật khẩu
        if (value.length < 8 || value.length > 16) {
          return Promise.reject(message || 'Mật khẩu phải có độ dài từ 8 đến 16 ký tự')
        }

        // Kiểm tra không có dấu cách
        if (/\s/.test(value)) {
          return Promise.reject(message)
        }

        // Kiểm tra chứa ít nhất một chữ thường
        const hasLowerCase = /[a-z]/.test(value)

        if (!hasLowerCase) {
          return Promise.reject(message)
        }

        // Kiểm tra chứa ít nhất một chữ hoa
        const hasUpperCase = /[A-Z]/.test(value)

        if (!hasUpperCase) {
          return Promise.reject(message)
        }

        // Kiểm tra chứa ít nhất một ký tự số
        const hasNumber = /[0-9]/.test(value)

        if (!hasNumber) {
          return Promise.reject(message)
        }

        // Kiểm tra chứa ít nhất một ký tự đặc biệt trong danh sách !@#$%^&+=
        const hasSpecialChar = /[!@#$%^&+=]/.test(value)

        if (!hasSpecialChar) {
          return Promise.reject(message)
        }

        // Nếu tất cả các điều kiện đều thỏa mãn
        return Promise.resolve()
      }

      // Nếu không có giá trị nhập vào
      return Promise.resolve()
    }
  }
}

// Validate mật khẩu cũ và mật khẩu mới
export function validateCompareOldPassword(oldPassword: string | any[], messageCompare: any, messagePattern: any) {
  return {
    validator: (_: any, value: any[]) => {
      if (value && value.length) {
        if (oldPassword === value) {
          return Promise.reject(messageCompare) // Mật khẩu mới không được trùng mật khẩu cũ || newPassCanNotSameAsOldPass
        }

        const regex = []

        regex.push('[A-Z]') // Uppercase
        regex.push('[a-z]') // Lowercase
        regex.push('[0-9]') // Digit
        regex.push("[\\s\\`\\~\\@\\#\\%\\&\\(\\)\\[\\]\\{\\}\\\\^\\$\\:\\;\\'\\/\\,\\|\\?\\*\\+\\.\\<\\>\\-\\=\\!\\_]")
        let passed = 0

        for (let i = 0; i < regex.length; i++) {
          if (new RegExp(regex[i]).test(value.toString())) {
            passed++
          }
        }

        if (passed > 3 && value.length >= 8 && value.length <= 16) {
          return Promise.resolve()
        }

        return Promise.reject(messagePattern) // Mật khẩu phải có từ 8-16 ký tự, bao gồm ít nhất 1 chữ viết hoa, 1 chữ viết thường, 1 chữ số và 1 ký tự đặc biệt || registerPassNotValid
      }

      return Promise.resolve()
    }
  }
}

// endregion

// region Số điện thoại
/** Validate Số điện thoại */
export function validatePhoneNumber(message: any) {
  return {
    validator: (_: any, number1: string | null | undefined) => {
      if (number1 === null || number1 === undefined) return Promise.resolve()
      const number = trim(number1)
      const DOT = '.'
      const SPACE = ' '
      const HYPHEN = '-'

      const isExitsCharacterPhone = (numberSplit: string | string[]) => {
        let count = 0

        if (numberSplit.includes(DOT)) {
          count++
        }

        if (numberSplit.includes(SPACE)) {
          count++
        }

        if (numberSplit.includes(HYPHEN)) {
          count++
        }

        return count > 1
      }

      if (number && trim(number)) {
        let isRegex = false
        let hasAreaCode = false
        let phoneNumberIgnoreAreaCode: string[] = []
        const AREA_CODE = '84'
        const AREA_CODE_HAS_PLUS = '+84'
        const AREA_CODE_HAS_BRACKET = '(84)'
        const AREA_CODE_HAS_ALL = '(+84)'
        const REGEX_IS_NUMERIC = /^[0-9]*$/g

        const REGEX_FORMAT_PHONE =
          /(^([-.\s]{0,1})([0-9]{3})([-.\s]{1})([0-9]{3})([-.\s]{1})([0-9]{4}))|(^([-.\s]{0,1})([0-9]{4})([-.\s]{1})([0-9]{3})([-.\s]{1})([0-9]{3}))|(^([-.\s]{0,1})([0-9]{4})([-.\s]{1})([0-9]{6}))|(^([-.\s]{0,1})([0-9]{2})([-.\s]{1})([0-9]{2})([-.\s]{1})([0-9]{2})([-.\s]{1})([0-9]{2})([-.\s]{1})([0-9]{2}))|(^[0-9]{4,12})/g

        const REGEX_FORMAT_PHONE_IGNORE_BEGIN_ZERO =
          /(^([-.\s]{0,1})([1-9]{3})([-.\s]{1})([0-9]{6}))|(^([-.\s]{0,1})([1-9]{3})([-.\s]{1})([0-9]{3})([-.\s]{1})([0-9]{3}))|(^[0-9]{4,12})/g

        const isNumber = REGEX_IS_NUMERIC.test(number)
        const phoneReplaceCharacter = number.replaceAll(/[^0-9]/g, '')

        // Check hasCode
        if (number.startsWith(AREA_CODE)) {
          hasAreaCode = true
          phoneNumberIgnoreAreaCode = number.split(AREA_CODE)
        } else if (number.startsWith(AREA_CODE_HAS_PLUS)) {
          hasAreaCode = true
          phoneNumberIgnoreAreaCode = number.split(AREA_CODE_HAS_PLUS)
        } else if (number.startsWith(AREA_CODE_HAS_BRACKET)) {
          hasAreaCode = true
          phoneNumberIgnoreAreaCode = number.split(AREA_CODE_HAS_BRACKET)
        } else if (number.startsWith(AREA_CODE_HAS_ALL)) {
          hasAreaCode = true
          phoneNumberIgnoreAreaCode = number.split(AREA_CODE_HAS_ALL)
        }

        // có mã vùng
        if (hasAreaCode) {
          // có nhiều ký tự đặc biệt
          if (isExitsCharacterPhone(phoneNumberIgnoreAreaCode[1])) {
            isRegex = true
          } else if (
            phoneReplaceCharacter.substring(2).length < 10 &&
            !REGEX_FORMAT_PHONE_IGNORE_BEGIN_ZERO.test(phoneNumberIgnoreAreaCode[1])
          ) {
            // < 10, !== 0
            isRegex = true
          } else if (
            phoneReplaceCharacter.substring(2).length >= 10 &&
            !REGEX_FORMAT_PHONE.test(phoneNumberIgnoreAreaCode[1])
          ) {
            // full sđt
            isRegex = true
          } else if (
            !REGEX_IS_NUMERIC.test(
              phoneNumberIgnoreAreaCode[1].replaceAll('-', '').replaceAll('.', '').replaceAll(' ', '')
            )
          ) {
            isRegex = true
          }
        } else if (isExitsCharacterPhone(number)) {
          // Có nhiều ký tự đặc biệt > 1
          isRegex = true
        } else if (!REGEX_FORMAT_PHONE.test(number)) {
          isRegex = true
        } else if (!hasAreaCode && phoneReplaceCharacter.length >= 4 && phoneReplaceCharacter.length <= 9) {
          return Promise.reject(message || 'Sai định dạng số điện thoại')
        } else if (
          !isNumber &&
          !REGEX_IS_NUMERIC.test(number.replaceAll('-', '').replaceAll('.', '').replaceAll(' ', ''))
        ) {
          isRegex = true
        }

        if (isNumber && number.length > 12) {
          return Promise.reject('Vui lòng nhập tối đa 12 ký tự')
        }

        if (!isNumber && number.length > 18) {
          return Promise.reject('Sai định dạng số điện thoại')
        }

        if (
          (phoneReplaceCharacter.startsWith('1800') || phoneReplaceCharacter.startsWith('1900')) &&
          phoneReplaceCharacter.length !== 8 &&
          phoneReplaceCharacter.length < 10
        ) {
          return Promise.reject(message || 'Sai định dạng số điện thoại')
        }

        if (
          !phoneReplaceCharacter.startsWith('1800') &&
          !phoneReplaceCharacter.startsWith('1900') &&
          ((!phoneReplaceCharacter.startsWith('0') && phoneReplaceCharacter.length < 3) ||
            (phoneReplaceCharacter.startsWith('0') && phoneReplaceCharacter.length < 10))
        ) {
          return Promise.reject(message || 'Sai định dạng số điện thoại')
        }

        if (isRegex) return Promise.reject(message || 'Giá trị nhập vào không hợp lệ')

        return Promise.resolve()
      }

      return Promise.resolve()
    }
  }
}

/** Validate định dạng số điện thoại (Chỉ nhập 0-9) */
export function validatePhoneNumber1(message?: string, minLength?: number) {
  return {
    validator: (_: any, number1: string | null | undefined) => {
      if (number1 === null || number1 === undefined) return Promise.resolve()
      const number = number1.trim()

      if (!number) return Promise.resolve()

      // Kiểm tra có ký tự đặc biệt không
      const hasSpecialCharacters = /[^0-9]/.test(number)

      if (hasSpecialCharacters) {
        return Promise.reject(message || 'Sai định dạng SĐT')
      }

      // Kiểm tra độ dài số điện thoại
      // if (number.length > 11) {
      //   return Promise.reject('Vui lòng nhập tối đa 11 ký tự')
      // }

      // Kiểm tra độ dài tối thiểu
      if (minLength && number.length < minLength) {
        return Promise.reject(message || 'Sai định dạng SĐT')
      }

      // Kiểm tra số điện thoại bắt đầu bằng 1800 hoặc 1900
      if (number.startsWith('1800') || number.startsWith('1900')) {
        if (number.length !== 8 && number.length !== 10) {
          return Promise.reject(message || 'Sai định dạng SĐT')
        }
      }
      // Kiểm tra các số điện thoại khác
      else {
        if (!number.startsWith('0') || number.length < 10) {
          return Promise.reject(message || 'Sai định dạng SĐT')
        }
      }

      return Promise.resolve()
    }
  }
}

export function validatePhoneNumber2(message?: string, minLength?: number) {
  return {
    validator: (_: any, number1: string | null | undefined) => {
      if (number1 === null || number1 === undefined) return Promise.resolve()
      const number = number1.trim()

      if (!number) return Promise.resolve()

      // Kiểm tra có ký tự đặc biệt không
      const hasSpecialCharacters = /[^0-9]/.test(number)

      if (hasSpecialCharacters) {
        return Promise.reject(message || 'Sai định dạng SĐT')
      }

      // Kiểm tra độ dài tối thiểu
      if (minLength && number.length < minLength) {
        return Promise.reject(message || 'Sai định dạng SĐT')
      }

      // Kiểm tra số điện thoại bắt đầu bằng 1800 hoặc 1900
      if (number.startsWith('1800') || number.startsWith('1900')) {
        if (number.length !== 8 && number.length !== 10) {
          return Promise.reject(message || 'Sai định dạng SĐT')
        }
      }

      return Promise.resolve()
    }
  }
}

// endregion

// region Email
/** Validate email */
export function validateEmail(message: string, regex?: any) {
  return {
    validator: (_: any, value: string) => {
      if (value && value.length) {
        let baseRegex =
          /^[a-zA-Z0-9]+(?:[.][a-zA-Z0-9]+)*@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9]{2,}(?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+$/

        if (regex) {
          baseRegex = regex
        }

        if (!baseRegex.test(value)) {
          return Promise.reject(message || 'Vui lòng nhập đúng định dạng')
        }

        const domain = value.split('@')[1]
        const tld = domain?.split('.').pop() || ''

        if (tld.length < 2 || tld.length > 10) {
          return Promise.reject(message || 'Vui lòng nhập đúng định dạng')
        }

        return Promise.resolve()
      }

      return Promise.resolve()
    }
  }
}

// region pattern
/** Validate định dạng taxcode  */

export const validateCustomPattern = (regex: RegExp | string, message?: string) => {
  return {
    validator: (_: any, value: string) => {
      if (value && trim(value) !== '') {
        if (new RegExp(regex).test(value)) {
          Promise.resolve()
        } else return Promise.reject(message || 'Vui lòng nhập đúng định dạng')

        return Promise.resolve()
      }

      return Promise.resolve()
    }
  }
}

export const validateCodeWithoutTrim = (message: string, regex: any) => {
  const rg = regex || /^[a-zA-Z0-9#\-_/:|]+$/

  return {
    validator: (_: any, value: any) => {
      if (value === null || !value) {
        return Promise.resolve()
      }

      if (value && !rg.test(value)) {
        return Promise.reject(message || 'Vui lòng nhập đúng định dạng')
      }

      return Promise.resolve()
    }
  }
}

// endregion

export const validatePattern = (regex: RegExp, message?: string) => {
  return {
    validator: (_: any, value: string) => {
      if (regex.test(value)) {
        return Promise.reject(message || 'Vui lòng nhập đúng định dạng')
      }

      return Promise.resolve()
    }
  }
}

// region Trùng lặp

export const validateDupTicketTitle = (portalType: string, message?: string) => {
  return {
    required: true,
    validator: async (_: any, value = '') => {
      if (!value || value.trim() === '') {
        return Promise.reject('Tiêu đề phiếu hỗ trợ không được bỏ trống')
      }

      try {
        const res = await AdminTicket.ticketTitleValidate(portalType, value)

        // Nếu res = true thì báo lỗi
        if (res === true) {
          return Promise.reject(message || 'Tiêu đề đã tồn tại')
        }

        return Promise.resolve()
      } catch {
        return Promise.reject('Lỗi kiểm tra tiêu đề')
      }
    }
  }
}

// kiểm tra trùng giá trị
export const validateDuplicateOnEdit = (editingValue: any, isLabel: boolean, array: any, message: string) => {
  return {
    validator: (_: any, value: any) => {
      if (!value) return Promise.resolve()

      const arrayWithoutEditingValue = isLabel
        ? array.filter((el: any) => el !== editingValue?.label)
        : array.filter((el: any) => el !== editingValue?.key)

      if (arrayWithoutEditingValue.find((el: any) => el.trim() === value.trim())) {
        return Promise.reject(message || 'Giá trị đã tồn tại')
      }

      return Promise.resolve()
    }
  }
}

/** Validate dữ liệu đã nhập vào có bị trùng so với 1 mảng dữ liệu cho trước hay không */
export function validateDuplicate(array: any[], message?: any, validateValue?: any) {
  return {
    validator: (_: any, value: any) => {
      // Nếu có dữ liệu truyền vào để validate thì validate dữ liệu đó thay vì dữ liệu mặc định của Form
      const valueToValidate = validateValue || value

      if (array?.length > 0 && !!valueToValidate) {
        if (array.find(item => String(item) === String(valueToValidate))) {
          return Promise.reject(message || 'Dữ liệu đã tồn tại')
        }

        return Promise.resolve()
      }

      return Promise.resolve()
    }
  }
}

// endregion

// region Checkbox required

export function validateRequireCheckbox(message: string, dataLength = 0) {
  return {
    validator: (_: any, value: boolean) => {
      if (dataLength > 0 || value) {
        return Promise.resolve()
      }

      return Promise.reject(message || 'Vui lòng không bỏ trống mục này')
    }
  }
}

// endregion

// region Website, url

/** Validate slugname */
export function validateSlugName(message: string) {
  return {
    message: message || 'Slugname phải bắt đầu bằng "/"',
    validator: (_: any, slugName: string) => {
      const check = (slugName || '').startsWith('/')

      if (check) return Promise.resolve()

      return Promise.reject()
    }
  }
}

// validate đường dẫn URL
export function validateUrl(message?: string) {
  return {
    validator: (_: any, value: string) => {
      if (!!value && value.trim() !== '') {
        const trimmedValue = value.trim()

        // Tạo một biểu thức chính quy để kiểm tra liên kết trong chuỗi
        const url = new RegExp(
          '^(https?:\\/\\/)?((([-a-z0-9]{1,63}\\.)*?[a-z0-9]([-a-z0-9]{0,253}[a-z0-9])?\\.[a-z]{2,63})|((\\d{1,3}\\.){3}\\d{1,3}))(:\\d{1,5})?((\\/|\\?)((%[0-9a-f]{2})|[-\\w\\+\\.\\?\\/@~#&=])*)?$',
          'i'
        )

        if (!url.test(trimmedValue)) {
          return Promise.reject(message || 'Link URL không hợp lệ')
        }
      }

      return Promise.resolve()
    }
  }
}

// validate đường dẫn URL
export function validateLink(message?: string) {
  return {
    validator: (_: any, value: string) => {
      if (!!value && value.trim() !== '') {
        const trimmedValue = value.trim()

        /**
         * ^(https?:\/\/)                      # Bắt đầu với http:// hoặc https://
         * [-a-zA-Z0-9@:%._\+~#=]{1,256}       # Domain name cho phép Các ký tự từ a-z, A-Z, 0-9, Các ký tự đặc biệt: @, :, %, ., _, +, ~, #, =, Dài 1-256 ký tự
         * \.[a-zA-Z0-9()]{1,6}                # Đuôi domain: Bắt đầu bằng đấu chấm (.), chứa chữ, số, dấu ngoặc đơn, dài 1-6 ký tự
         * \b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)   # Query string với các ký tự đặc biệt, Dấu * nghĩa là có thể có hoặc không có phần này
         * i                                   # Query string với các ký tự đặc biệt, Dấu * nghĩa là có thể có hoặc không có phần này
         * */
        const url = new RegExp(
          '^(https?:\\/\\/)[-a-zA-Z0-9@:%._\\+~#=]{1,256}\\.[a-zA-Z0-9()]{1,6}\\b([-a-zA-Z0-9()@:%_\\+.~#?&//=]*)',
          'i'
        )

        if (!url.test(trimmedValue)) {
          return Promise.reject(message || 'Đường dẫn không đúng định dạng')
        }
      }

      return Promise.resolve()
    }
  }
}

export const validateImageUrl = () => ({
  validator(_: any, value: any) {
    if (!value) {
      return Promise.resolve()
    }

    // chỉ nhận webp, jpeg
    const imagePattern = /\.(jpeg|webp)(\?.*)?$/i
    const urlPattern = /^(https?:\/\/.*\.(?:png|jpg|jpeg|gif|webp|bmp|tiff))(\?.*)?$/i

    if (!urlPattern.test(value)) {
      return Promise.reject(new Error('Link URL không phải định dạng link ảnh'))
    }

    if (imagePattern.test(value)) {
      return Promise.resolve()
    }

    return Promise.reject(new Error('Định dạng ảnh không được hỗ trợ'))
  }
})

/** Validate URL ảnh cho Page Builder - chấp nhận tất cả định dạng ảnh phổ biến */
export const validatePageBuilderImageUrl = () => ({
  validator(_: any, value: any) {
    if (!value) {
      return Promise.resolve()
    }

    // Chấp nhận các định dạng ảnh phổ biến cho Page Builder
    const urlPattern = /^(https?:\/\/.*\.(?:png|jpg|jpeg|gif|webp|bmp|tiff))(\?.*)?$/i

    if (!urlPattern.test(value)) {
      return Promise.reject(new Error('Link URL không phải định dạng link ảnh'))
    }

    return Promise.resolve()
  }
})

/**
 * Validate định dạng ảnh theo nhu cầu
 */
export function validateAllFormatsByUrl(formats?: string[]) {
  return {
    validator: async (_: any, url: string) => {
      // Chỉ kiểm tra khi có URL nhập vào
      if (!!trim(url)) {
        // Lấy định dạng file
        const extension = url.slice(url.lastIndexOf('.')).toLowerCase()

        // Xử lý tiếp nếu có định dạng file hợp lệ
        if (
          extension.match(/\.(jpeg|jpg|png|webp|jfif|ico)$/) &&
          (!formats?.length || formats.some(item => String(item).toLowerCase().includes(extension)))
        ) {
          // Xử lý load ảnh từ URL đã nhập
          const img = new Image()

          img.src = url

          const imageLoaded = await new Promise(resolve => {
            img.onerror = () => resolve(false)
            img.onload = () => resolve(true)
          })

          // Nếu ảnh load thành công thì pass
          if (imageLoaded) {
            return Promise.resolve()
          }

          return Promise.reject('URL ảnh không hợp lệ')
        }

        return Promise.reject('Định dạng ảnh không hợp lệ')
      }

      return Promise.resolve()
    }
  }
}

/** Validate định dạng input theo nhu cầu */
export function validateInputFormats(formats?: string[], validateType?: 'OR' | 'AND', message?: string) {
  return {
    validator: (_: any, value: any) => {
      // check pattern
      if (!formats?.length) return Promise.resolve()
      let totalValidLetters = 0

      const regexFormatResult = formats.map(item => {
        const reg = new RegExp(item, 'g')
        const match = (value?.match(reg) || []).filter((itemMatch?: string) => itemMatch !== ' ')

        totalValidLetters += match?.length ?? 0

        return !isEmpty(match)
      })

      if (totalValidLetters !== value.length) return Promise.reject(message || 'Giá trị đã nhập không đúng định dạng')
      const result = validateType === 'OR' ? regexFormatResult.some(reg => reg) : regexFormatResult.every(reg => reg)

      if (!result && value.trim()) return Promise.reject(message || 'Giá trị đã nhập không đúng định dạng')

      return Promise.resolve()
    }
  }
}

export const validateVideoUrl = () => ({
  validator(_: any, value: any) {
    if (!value) return Promise.resolve()

    if (!isValidVideoUrl(value)) {
      return Promise.reject(new Error('Vui lòng tải lên video từ các nguồn Google Drive, Youtube'))
    }

    return Promise.resolve()
  }
})
// endregion

// region validator chỉ nhập được số

/** Hàm dùng để chặn ký tự đặc biệt, ký tự chữ, chỉ cho nhập số */
export const formatNormalizeNumber = (value: string) => {
  if (trim(value)) {
    return value.toString().replaceAll(/\D/g, '')
  }

  return null
}

/** Hàm dùng để chuyển đổi giá trị boolean sang nhị phân */
export const normalizeBooleanValue = (value: boolean) => {
  return value ? 1 : 0
}

/** Hàm dùng để chặn ký tự đặc biệt, ký tự chữ, chỉ cho nhập số, và trả về giá trị thực của số đã nhập */
export const normalizeNumberValue = (value: string) => {
  if (trim(value)) {
    return Number(String(value).replaceAll(/\D/g, ''))
  }

  return null
}

/** Hàm dùng để chặn ký tự đặc biệt, ký tự chữ, chỉ cho nhập số trong khoảng xác định, và trả về giá trị thực của số đã nhập */
export const normalizeNumberRange =
  (min: number = 0, max: number = 9999999999) =>
  (value: string) => {
    if (trim(value)) {
      // Số lượng đầu vào
      const quantity = Number(String(value).replaceAll(/\D/g, ''))

      // Số lượng tối thiểu
      const minQuantity = min > 0 ? min : 0

      // Số lượng tối đa
      const maxQuantity = max > 0 ? max : 9999999999

      return Math.max(Math.min(Number(quantity), maxQuantity), minQuantity)
    }

    return null
  }

/**
 * Validate số thập phân với số chữ số sau dấu phẩy tùy chỉnh
 * @param decimalPlaces Số chữ số sau dấu phẩy cho phép
 * @param message Thông báo lỗi tùy chỉnh
 */
export const validateDecimalNumber = (decimalPlaces: number, message?: string) => {
  return {
    validator: (_: any, value: any) => {
      if (!value && value !== 0) {
        return Promise.resolve()
      }

      const pattern = new RegExp(`^\\d+(\\.\\d{0,${decimalPlaces}})?$`)

      if (!pattern.test(value)) {
        return Promise.reject(
          message || `Cho phép nhập số thập phân sau dấu phẩy hiển thị tối đa ${decimalPlaces} chữ số`
        )
      }

      return Promise.resolve()
    }
  }
}

//hàm check trùng
export const createDuplicateValidator =
  (form: FormInstance, listName: string, keyField: string, errorMessage: string) =>
  async (_: RuleObject, value: StoreValue) => {
    if (!value) return Promise.resolve()

    const list = form.getFieldValue(listName) || []
    const duplicate = list.filter((item: any) => item?.[keyField] === value)

    if (duplicate.length > 1) {
      return Promise.reject(new Error(errorMessage))
    }

    return Promise.resolve()
  }

//hàm validate min max
export const minMaxValidator =
  (form: FormInstance, errorMessage: string, minPath?: any, maxPath?: any) =>
  async (_: RuleObject, value: StoreValue) => {
    const minValue = form.getFieldValue(minPath)
    const maxValue = form.getFieldValue(maxPath)

    if (value && minValue && Number(value) < Number(minValue)) {
      return Promise.reject(new Error(errorMessage))
    }

    if (value && maxValue && Number(value) > Number(maxValue)) {
      return Promise.reject(new Error(errorMessage))
    }

    return Promise.resolve()
  }

export function formatNormalizeFloatNumber(value: string | undefined, prevValue: any) {
  if (!!value && trim(value)) {
    value = trim(value.toString()).replaceAll(/[^,\d]/g, '')
    const valueDot = value.replaceAll(',', '.')
    const dot = valueDot.indexOf('.')

    if (dot === valueDot.length - 1) {
      return value
    }

    if (Number.isNaN(parseFloat(valueDot))) {
      return prevValue || null
    }

    const str = valueDot.slice(dot + 1)

    if (dot > 0 && str.length > 2) {
      return prevValue
    }

    if (valueDot === '0.00') return '0'

    if (dot > 0 && (str === '0' || str === '00')) {
      return value
    }

    return valueDot.toString().replaceAll('.', ',')
  }

  return null
}

export function validateMinInputNumber(min: number, message?: string) {
  return {
    validator: (_: any, value: any) => {
      const number = parseInt(value, 10)

      if (trim(value) && !Number.isNaN(number)) {
        if (number >= min) return Promise.resolve()

        return Promise.reject(message || 'Giá trị nhập vào không hợp lệ')
      }

      return Promise.resolve()
    }
  }
}

export function validateMaxInputNumber(max: number, message?: string, type?: string) {
  return {
    validator: (_: any, value: any) => {
      if (!max) return Promise.resolve()
      const number = parseInt(value, 10)

      if (trim(value) && !Number.isNaN(number)) {
        if (type === 'not_equal') {
          if (number < max) return Promise.resolve()

          return Promise.reject(message)
        }

        if (number <= max) return Promise.resolve()

        return Promise.reject(message || 'Giá trị nhập vào không hợp lệ')
      }

      return Promise.resolve()
    }
  }
}

// endregion

export function validateLengthTinNoRequire(max1: number, max2: number, message?: string) {
  const defaultMessage = 'Vui lòng chỉ nhập tối đa số ký tự cho phép'

  return {
    validator: (_: any, value: string | undefined | null) => {
      if (value == null) return Promise.resolve()

      const strValue = String(value)
      const length = strValue.length

      if (/[a-zA-Z]/.test(strValue)) {
        return Promise.reject(message || defaultMessage)
      }

      if (isNaN(Number(value)) || strValue.includes('-') || strValue.includes('.')) {
        return Promise.reject(message || defaultMessage)
      }

      if (length === 0 || length === max1 || length === max2) {
        return Promise.resolve()
      }

      return Promise.reject(message || defaultMessage)
    }
  }
}

export function validateInputValueRange(min: number, max: number, message?: string) {
  return {
    validator: (_: any, value: any) => {
      const regexNumber = /^[0-9]+(\.?[0-9]+)?$/g
      const isNumber = regexNumber.test(value)

      if (!isNumber) return Promise.resolve()

      if (((!!value && trim(value) !== '') || value === 0) && min <= Number(value) && Number(value) <= max) {
        return Promise.resolve()
      }

      return Promise.reject(message || `Giá trị phải trong khoảng từ ${min} đến ${max}`)
    }
  }
}

// #region Hàm chỉ cho phép nhập số từ bàn phím
/** Hàm chỉ cho phép nhập số từ bàn phím */
// Khai báo cho handleKeyPress
export const handleKeyPress = (e: KeyboardEvent<HTMLInputElement>): void => {
  const charCode: number = e.which ? e.which : e.keyCode
  const charStr: string = String.fromCharCode(charCode)

  if (!/^\d$/.test(charStr)) {
    e.preventDefault()
  }
}

// Khai báo cho handlePaste
export const handlePaste = (e: ClipboardEvent<HTMLInputElement>): void => {
  const pasteData: string = e.clipboardData?.getData('Text')

  if (!/^\d+$/.test(pasteData)) {
    e.preventDefault()
  }
}
// #endregion

/** Validate mảng yêu cầu ít nhất một giá trị */
export function validateArrayRequired<T = any>(message?: string) {
  return {
    type: 'array' as const,
    validator: (_: any, value: T[]) => {
      if (!Array.isArray(value) || value.length === 0) {
        return Promise.reject(message || 'Vui lòng chọn ít nhất một giá trị')
      }

      return Promise.resolve()
    }
  }
}

// #region
// Kiểm tra giá trị đã tồn tại trong mảng hay chưa -- Custom Field
export function validateValueExistsInArray<T>(array: T[] | undefined, message?: string) {
  return {
    validator: (_: any, value: T) => {
      const occurrences = (array || []).filter(a => a === value).length

      if (occurrences > 1) {
        return Promise.reject(message || 'Giá trị nhập vào không hợp lệ')
      }

      return Promise.resolve()
    }
  }
}
// #endregion

// #region
/** Validate giá trị nhập vào phải thuộc bảng chữ cái Latin */
export const validateVietnamese = (message?: string) => {
  return {
    validator: (_: any, value: string) => {
      if (!value) return Promise.resolve()

      // This regex allows ASCII characters and Vietnamese characters
      // It includes letters with diacritics used in Vietnamese
      const vietnameseAndASCIIRegex =
        /^[a-zA-Z0-9\s\!\@\#\$\%\^\&\*\(\)\_\+\-\=\[\]\{\}\;\:\'\"\,\.\/\<\>\?\`\~áàảãạăắằẳẵặâấầẩẫậéèẻẽẹêếềểễệíìỉĩịóòỏõọôốồổỗộơớờởỡợúùủũụưứừửữựýỳỷỹỵđÁÀẢÃẠĂẮẰẲẴẶÂẤẦẨẪẬÉÈẺẼẸÊẾỀỂỄỆÍÌỈĨỊÓÒỎÕỌÔỐỒỔỖỘƠỚỜỞỠỢÚÙỦŨỤƯỨỪỬỮỰÝỲỶỸỴĐ]*$/

      if (vietnameseAndASCIIRegex.test(value)) {
        return Promise.resolve()
      }

      return Promise.reject(message || 'Không được phép nhập ký tự đặc biệt')
    }
  }
}

/** Hàm dùng để chặn ký tự đặc biệt và ký tự chữ, số âm , số 0, ký tự space
 * Giới hạn số lượng  ký tự
 * */
export const formatAndNormalizeMax3DigitsInput = (value: string, max: number) => {
  if (value.trim()) {
    const numericValue = value.replace(/\D/g, '')

    if (!numericValue || Number(numericValue) === 0) return null

    return numericValue.slice(0, max)
  }

  return null
}

export function formatNormalizeNumberOtherZero(value: string, type: string) {
  if (trim(value)) {
    value = value.toString().replaceAll(/\D/g, '')

    if (value !== '' && value !== '0') {
      return type === 'normal' ? parseInt(value, 10) : parseInt(value, 10).toLocaleString('fr')
    }

    return ''
  }

  return ''
}

/** Hàm dùng để chặn ký tự đặc biệt, ký tự chữ, chỉ cho phép nhập số type number */
export const normalizeInputNumber = (value: string): number | null => {
  if (trim(value)) {
    const numericValue = value.toString().replaceAll(/\D/g, '')

    if (numericValue !== '') {
      return parseInt(numericValue, 10)
    }

    return null
  }

  return null
}

// #endregion

// #region
/**
 * Kiểm tra giá trị không chứa ký tự tiếng Việt.
 */
export const validateNonVietnamese = (message?: string) => {
  return {
    validator: (_: any, value: string) => {
      if (!value) return Promise.resolve()

      const vietnameseRegex = /[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ\s]/i

      if (vietnameseRegex.test(value)) {
        return Promise.reject(new Error(message ?? 'Giá trị không hợp lệ'))
      }

      return Promise.resolve()
    }
  }
}

/**
 * Hàm chặn nhập kí tự đặc biệt, kí tự chữ, dấu cách , số âm, số 0, nhập > 100 trả về 100
 */
// #endregion

export function normalizeInput(value: string) {
  const valueString = String(value).trim()

  // Chặn dấu cách, ký tự chữ, ký tự đặc biệt, dấu âm, thập phân
  if (!/^\d+$/.test(valueString)) return null

  const num = Number(valueString)

  // Chặn số 0
  if (num === 0) return null

  // Giới hạn tối đa là 100
  if (num > 100) return 100

  return num
}

/** Validate giá trị giá không được bỏ trống */
export const validatePriceRequired = (message?: string) => {
  return {
    validator: async (_: any, value: any) => {
      if (value === null || value === undefined) {
        return Promise.reject(message || 'Đơn giá không được bỏ trống')
      }

      return Promise.resolve()
    }
  }
}

export const validateKeyboardCharacters = (message?: string) => {
  return {
    validator: async (_: any, value: any) => {
      const keyboardCharactersRegex =
        /[^ a-z0-9A-Z_ÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠàáâãèéêìíòóôõùúăđĩũơƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂưăạảấầẩẫậắằẳẵặẹẻẽềềểỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪễếệỉịọỏốồổỗộớờởỡợụủứừỬỮỰỲỴÝỶỸửữựỳýỵỷỹ\-+=`~_.!?,@#$%^&*()\\/<>\[\]{}|:;"']/u

      if (value && keyboardCharactersRegex.test(value)) {
        return Promise.reject(new Error(message ?? 'Không được phép nhập ký tự đặc biệt'))
      }

      return Promise.resolve()
    }
  }
}

export const validateSpecialCharacters = (message?: string) => {
  return {
    validator: async (_: any, value: any) => {
      const keyboardCharactersRegex = /[\-+=`~_.!?,@#$%^&*()]/u

      if (value && keyboardCharactersRegex.test(value)) {
        return Promise.reject(new Error(message ?? 'Không được phép nhập ký tự đặc biệt'))
      }

      return Promise.resolve()
    }
  }
}

export const validateTechniqueName = () => ({
  validator(_: any, value: string) {
    if (!value) {
      return Promise.resolve()
    }

    // ^[A-Za-z] → bắt đầu bằng chữ cái
    // [A-Za-z0-9_.]*$ → các ký tự tiếp theo là chữ, số, "_" hoặc ".", không có khoảng trắng
    const regex = /^[A-Za-z][A-Za-z0-9_.]*$/

    if (!regex.test(value)) {
      return Promise.reject('Tên kỹ thuật sai định dạng')
    }

    return Promise.resolve()
  }
})

// Hàm validate ký tự ngăn cách | , , . ;
export const validateSeparator = () => ({
  validator(_: any, value: any) {
    if (!value) {
      return Promise.reject(new Error('Vui lòng nhập ký tự ngăn cách "|" , "," , ".", ";"'))
    }

    if (!['|', ',', '.', ';'].includes(value)) {
      return Promise.reject(new Error('Vui lòng nhập ký tự ngăn cách "|" , "," , ".", ";"'))
    }

    return Promise.resolve()
  }
})
