import React from 'react'

export type IconType = 'ERROR' | 'WARNING' | 'SUCCESS' | 'DELETE' | 'NOTIFY' | 'ACTIVE' | 'DELETE_INFO'

interface IconProps {
  iconType: IconType
  size: number
}

const Icon: React.FC<IconProps> = ({ iconType, size }) => {
  const iconClasses = {
    ERROR: {
      background: 'bg-bg-error-lighter',
      icon: 'onedx-warning text-text-error-default'
    },
    WARNING: {
      background: 'bg-bg-warning-light',
      icon: 'onedx-information text-text-warning-default'
    },
    SUCCESS: {
      background: 'bg-bg-success-light',
      icon: 'onedx-success-icon text-text-success-default'
    },
    DELETE: {
      background: 'bg-bg-error-lighter',
      icon: 'onedx-delete text-text-error-default'
    },
    NOTIFY: {
      background: 'bg-bg-primary-lighter',
      icon: 'onedx-circle-warning text-text-primary-default'
    },
    ACTIVE: {
      background: 'bg-bg-primary-lighter',
      icon: 'onedx-email-system text-text-primary-default'
    },
    DELETE_INFO: {
      background: 'bg-bg-error-lighter',
      icon: 'onedx-circle-warning text-text-error-default'
    }
  }

  const { background, icon } = iconClasses[iconType]

  return (
    <div className={`bg flex size-10 items-center justify-center ${background} p-2`} style={{ borderRadius: '50%' }}>
      <i className={`${icon} size-[${size}px] inline-block`} />
    </div>
  )
}

interface IconWrapperProps {
  iconType: IconType
  size?: number
}

const IconWrapper: React.FC<IconWrapperProps> = ({ iconType, size = 48 }) => {
  return <Icon iconType={iconType} size={size} />
}

export default IconWrapper
