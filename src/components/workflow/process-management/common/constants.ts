'use client'

export const quickSearchOptionsList = [
  {
    label: 'Tên tiến trình',
    key: 'isName'
  },
  {
    label: 'Mã định danh',
    key: 'isCode'
  }
]

export const statusOptions = [
  {
    label: 'Hoạt động',
    value: 'ACTIVE'
  },
  {
    label: 'Không hoạt động',
    value: 'INACTIVE'
  },
  {
    label: 'Bản nháp',
    value: 'DRAFT'
  }
]

export const pageSizeOptions = [
  { label: '10 / trang', value: 10 },
  { label: '20 / trang', value: 20 },
  { label: '50 / trang', value: 50 }
]

export const quickSearchOptions = [
  { label: 'Mã đơn hàng', key: 'isCartCode' },
  { label: 'Tên sản phẩm dịch vụ', key: 'isServiceName' },
  { label: 'Tên khách hàng', key: 'isCustomerName' },
  { label: 'Nhà cung cấp', key: 'isProviderName' }
]

export const PROGRESS = {
  RECEIVED: 'RECEIVED',
  PROCESSING: 'PROCESSING',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED'
}

export const progressOptions = [
  { label: 'Tiếp nhận đơn hàng', value: PROGRESS.RECEIVED },
  { label: 'Đang xử lý', value: PROGRESS.PROCESSING },
  { label: 'Đã hoàn tất', value: PROGRESS.COMPLETED },
  { label: 'Đã hủy', value: PROGRESS.CANCELLED }
]

export const tagStatusProcess = {
  ACTIVE: {
    bgColor: '#D2F9E2',
    color: '#07945F',
    text: 'Đang hoạt động',
    value: 'ACTIVE'
  },
  INACTIVE: {
    bgColor: '#F2F4F9',
    color: '#394867',
    text: 'Không hoạt động',
    value: 'INACTIVE'
  },
  DRAFT: {
    bgColor: '#FFF6B3',
    color: '#E28800',
    text: 'Bản nháp',
    value: 'DRAFT'
  }
}

export const convertStatusProcess = (status: string) => {
  if (status === 'ACTIVE') {
    return 'Đang hoạt động'
  } else if (status === 'INACTIVE') {
    return 'Không hoạt động'
  }

  return ''
}

export const quickSearchActivityHistory = [
  {
    label: 'Mô tả',
    key: 'isDescription'
  },
  {
    label: 'Người thực hiện',
    key: 'isModifiedBy'
  }
]

export const versionOptions = [
  { value: 'ALL', label: 'Tất cả' },
  { value: 'LATEST', label: 'Mới nhất' },
  { value: 'OLDEST', label: 'Cũ nhất' }
]

export const getStatusOrder = (status: any) => {
  switch (status) {
    case 'RECEIVED':
      return { statusText: 'Tiếp nhận đơn hàng', backgroundColor: '#CDE4FE', color: '#2A6AEB' }
    case 'PROCESSING':
      return { statusText: 'Đang xử lý', backgroundColor: '#CDE4FE', color: '#2A6AEB' }
    case 'COMPLETED':
      return { statusText: 'Đã hoàn tất', backgroundColor: '#D2F9E2', color: '#07945F' }
    case 'CANCELLED':
      return { statusText: 'Đã hủy', backgroundColor: '#FFE1E0', color: '#D82D2A' }
    default:
      return { statusText: 'Tiếp nhận đơn hàng', backgroundColor: '#CDE4FE', color: '#2A6AEB' }
  }
}

export const TYPE_VALUES = [
  { label: 'Một lần', value: 'ONCE' },
  { label: 'Hàng ngày', value: 'DAILY' },
  { label: 'Ngày trong tuần', value: 'WEEKLY' },
  { label: 'Ngày trong tháng', value: 'MONTHLY' }
]

export const FREQUENCY_VALUES = [
  { label: 'Một lần', value: 'ONCE' },
  { label: '15 phút một lần', value: 'EVERY_15_MIN' },
  { label: '30 phút một lần', value: 'EVERY_30_MIN' },
  { label: '1 giờ một lần', value: 'EVERY_1_HOUR' },
  { label: '2 giờ một lần', value: 'EVERY_2_HOURS' },
  { label: '3 giờ một lần', value: 'EVERY_3_HOURS' }
]

export const WEEK_VALUES = [
  { label: 'Thứ 2', value: 'MON' },
  { label: 'Thứ 3', value: 'TUE' },
  { label: 'Thứ 4', value: 'WED' },
  { label: 'Thứ 5', value: 'THU' },
  { label: 'Thứ 6', value: 'FRI' },
  { label: 'Thứ 7', value: 'SAT' },
  { label: 'Chủ nhật', value: 'SUN' }
]

export const FREQUENCY_TYPE_MONTH_VALUES = [
  { label: 'Ngày cố định hàng tháng', value: 'MONTHLY_FIXED_DATE' },
  { label: 'Thứ trong tuần cố định hàng tháng', value: 'MONTHLY_FIXED_WEEKDAY' }
]

export const DAY_IN_MONTH = [
  { value: 1, label: 'Ngày 1' },
  { value: 2, label: 'Ngày 2' },
  { value: 3, label: 'Ngày 3' },
  { value: 4, label: 'Ngày 4' },
  { value: 5, label: 'Ngày 5' },
  { value: 6, label: 'Ngày 6' },
  { value: 7, label: 'Ngày 7' },
  { value: 8, label: 'Ngày 8' },
  { value: 9, label: 'Ngày 9' },
  { value: 10, label: 'Ngày 10' },
  { value: 11, label: 'Ngày 11' },
  { value: 12, label: 'Ngày 12' },
  { value: 13, label: 'Ngày 13' },
  { value: 14, label: 'Ngày 14' },
  { value: 15, label: 'Ngày 15' },
  { value: 16, label: 'Ngày 16' },
  { value: 17, label: 'Ngày 17' },
  { value: 18, label: 'Ngày 18' },
  { value: 19, label: 'Ngày 19' },
  { value: 20, label: 'Ngày 20' },
  { value: 21, label: 'Ngày 21' },
  { value: 22, label: 'Ngày 22' },
  { value: 23, label: 'Ngày 23' },
  { value: 24, label: 'Ngày 24' },
  { value: 25, label: 'Ngày 25' },
  { value: 26, label: 'Ngày 26' },
  { value: 27, label: 'Ngày 27' },
  { value: 28, label: 'Ngày 28' },
  { value: 29, label: 'Ngày 29' },
  { value: 30, label: 'Ngày 30' },
  { value: 31, label: 'Ngày 31' }
]

export const WEEK_OF_MONTH_VALUES = [
  { label: 'Đầu tiên', value: 1 },
  { label: 'Thứ 2', value: 2 },
  { label: 'Thứ 3', value: 3 },
  { label: 'Thứ 4', value: 4 },
  { label: 'Cuối cùng', value: 5 }
]

export const ACTION_OPTIONS = [{ label: 'Gửi thông báo', value: 'NOTIFICATION' }]

export const DATE_UNIT = [
  { label: 'Ngày', value: 0 },
  { label: 'Tuần', value: 1 },
  { label: 'Tháng', value: 2 },
  { label: 'Năm', value: 3 }
]

export const TYPE_LABELS = {
  ONCE: 'Một lần',
  DAILY: 'Hàng ngày',
  WEEKLY: 'Ngày trong tuần',
  MONTHLY: 'Ngày trong tháng'
}

export const FIELD_LABELS = {
  intervalType: 'Tần suất',
  daysOfWeek: 'Ngày trong tuần',
  daysOfMonth: 'Ngày trong tháng',
  weekOfMonth: 'Tuần trong tháng',
  startTime: 'Thời gian đồng bộ',
  startDate: 'Thời điểm bắt đầu'
}

export const OPERAND_ID = {
  ORDER_ADDRESS: 1062,
  CREATION_TIME: 1018,
  CURRENT_STATE_TIME: 1064
}

export const RULES_ENGINE_CONDITIONS = [
  { value: 'ORDER_ADDRESS', label: 'Địa chỉ đơn hàng' },
  { value: 'CREATION_TIME', label: 'Thời gian tạo' },
  { value: 'CURRENT_STATE_TIME', label: 'Thời gian ở trạng thái hiện tại' }
]

export const OPERATORS = [
  { label: '=', value: 1 },
  { label: '>', value: 12 },
  { label: 'Trong khoảng', value: 15 }
]
