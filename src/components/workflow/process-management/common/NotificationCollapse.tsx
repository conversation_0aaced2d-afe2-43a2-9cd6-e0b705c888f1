import { Collapse } from 'antd'
import classNames from 'classnames'
import { styled } from 'styled-components'

const { Panel } = Collapse

const StyledCollapse = styled(Collapse)`
  .ant-collapse-content-box {
    padding: 0 12px 12px !important;
  }

  .ant-collapse-header {
    padding: 12px !important;
  }

  background-color: white;
`

interface NotificationCollapseProps {
  children?: React.ReactNode
  className?: string
  label?: string | React.ReactNode
}

export const NotificationCollapse = ({ children, className, label }: NotificationCollapseProps) => {
  return (
    <StyledCollapse
      className={classNames('rounded-lg', className)}
      collapsible='icon'
      expandIconPosition='end'
      ghost
      defaultActiveKey={['1']}
      expandIcon={({ isActive }: { isActive?: boolean }) => (
        <i className={`onedx-chevron-down size-5 text-icon-neutral-light ${isActive ? 'rotate-180' : ''}`} />
      )}
    >
      <Panel header={<div className='text-body-14 font-medium text-text-neutral-strong'>{label}</div>} key='1'>
        <div className='border-t border-solid border-slate-200 pt-[10px]'>{children}</div>
      </Panel>
    </StyledCollapse>
  )
}
