'use client'

import { Collapse, Tabs, Table, Form } from 'antd'
import { styled } from 'styled-components'

import { baseColorLight } from '@/utils/colors'

export const StyledCollapse = styled(Collapse)`
  .ant-collapse-content-box {
    padding-block-start: 0 !important;
    padding-block-end: 16px !important;
  }

  .ant-collapse-header {
    padding: 16px 16px 12px !important;
  }

  background-color: white;
`

export const StyledTabs = styled(Tabs)`
  .ant-tabs-nav {
    margin: 0 !important;
  }

  .ant-tabs-tab {
    padding: 8px 12px !important;
    margin: 0 !important;
  }
`

export const CustomTable = styled(Table)`
  // Table header
  thead > tr > th {
    &:before {
      display: none !important;
    }
    padding-block: 12px !important;
    padding-inline: 16px !important;
    background-color: ${baseColorLight['gray-1']} !important;
    color: ${baseColorLight['gray-6']}!important;
    font-weight: 600 !important;
    font-size: 12px !important;
    border-block-end: 1px solid ${baseColorLight['gray-2']} !important;
  }

  // <PERSON><PERSON><PERSON> bảo checkbox ở header và body thẳng hàng
  th.ant-table-selection-column,
  td.ant-table-selection-column {
    padding-inline: 8px !important;
    text-align: center !important;
    width: 40px !important;
    vertical-align: middle !important;
  }

  & .ant-pagination {
    gap: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
  }

  & .ant-pagination .ant-pagination-options {
    position: absolute;
    inset-inline-end: 0;
    margin: 0;
  }

  // Pagination
  & .ant-pagination .ant-pagination-item-active {
    background-color: #f2f4f9 !important;
    border: none !important;
  }

  & .ant-pagination .ant-pagination-item-active a,
  .ant-pagination .ant-pagination-item-active a {
    color: #0f131a !important;
    font-weight: 400;
    background-color: #cff1fc !important;
    font-size: 14px;
    border-radius: 9999px;
  }

  & .ant-pagination .ant-pagination-item {
    border-radius: 50%;
  }

  & .ant-pagination.ant-pagination-mini .ant-pagination-item {
    min-inline-size: 28px;
    block-size: 28px;
    line-height: 25px;
  }

  & .ant-table-tbody .ant-table-row-level-0 .ant-table-cell-row-hover {
    .cursor-pointer {
      color: #0070c4 !important;
    }
    background-color: #f2fafc !important;

    // Thay đổi màu text khi hover, trừ cột status
    color: #2a6aeb !important;

    // Giữ nguyên màu cho cột status
    .ant-table-cell[data-column-key='status'] {
      color: inherit !important;
    }
  }

  & .ant-pagination-item a {
    background-color: #f8f7fa !important;
    border-color: #f8f7fa !important;
  }

  & .ant-pagination .ant-pagination-item {
    background-color: #f8f7fa !important;
    border: none !important;
  }
`

export const CellWrapper = styled.div`
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
`

export const Ribbon = styled.span`
  position: absolute;
  top: -16px;
  right: 0px;
  display: inline-block;
  background: #cde4fe;
  color: #1e43af;
  font-size: 10px;
  font-weight: 500;
  border-radius: 0px 0px 8px 8px;
  padding: 2px 8px;
  height: 20px;
  z-index: 1;
`

export const CustomFormItem = styled(Form.Item)`
  &.ant-form-item {
    margin-block-end: 0px !important;
  }
`
