import type { ReactNode } from 'react'

import { Collapse } from 'antd'

import { ContentRender } from '@/views/order/detail/tabs/common/components'
import { StyledCollapse } from './styled'

const { Panel } = Collapse

type Field = {
  label: string
  content: ReactNode
  hidden?: boolean
}

type CollapseProps = {
  header: ReactNode
  items: Field[]
  defaultActiveKey?: string[]
}

export const CustomCollapse = ({ header, items, defaultActiveKey = ['1'] }: CollapseProps) => {
  return (
    <StyledCollapse
      className='rounded-none bg-white'
      collapsible='icon'
      expandIconPosition='end'
      ghost
      defaultActiveKey={defaultActiveKey}
      expandIcon={({ isActive }: { isActive?: boolean }) => (
        <i className={`onedx-chevron-down size-5 text-icon-neutral-light ${isActive ? 'rotate-180' : ''}`} />
      )}
    >
      <Panel header={header} key='1'>
        <div className='border-t border-solid border-slate-200 pt-4'>
          <div className='grid grid-cols-4 gap-4'>
            {items
              ?.filter(item => !item.hidden)
              .map((field, index) => (
                <ContentRender key={`${field.label}-${index}`} label={field.label} content={field.content} />
              ))}
          </div>
        </div>
      </Panel>
    </StyledCollapse>
  )
}
