import React from 'react'

import { <PERSON><PERSON>, <PERSON>er, Form } from 'antd'

import {
  FIELD_LABELS,
  NotificationCollapse,
  OPERAND_ID,
  TYPE_LABELS,
  OPERATORS,
  RULES_ENGINE_CONDITIONS,
  DATE_UNIT,
  CustomTag
} from '@/components/workflow/process-management/common'
import { convertProductTypeName, getConditionTypeLabel, getNotificationType } from '@/utils/workflow'
import { ContentRender } from '@/views/order/detail/tabs/common/components'
import { usePageScroll } from '@/hooks/usePageScroll'
import AddressCategory from '@/models/AddressCategory'

interface TriggerType {
  api?: any | null
  webhook?: any | null
  manual?: any | null
  ruleEngine?: any | null
  schedule?: any | null
  type?: string | 'STATE' | 'RULE_ENGINE' | 'SCHEDULE' | 'MANUAL'
  state?: any
}

export const ConditionDetail = ({
  open,
  setOpen,
  conditionDetail
}: {
  open: boolean
  setOpen: (open: boolean) => void
  conditionDetail: any
}) => {
  const onClose = () => setOpen(false)
  const { content: addressList } = usePageScroll([], AddressCategory.getAddressList, { status: 1 })

  const getStateField = (item: any) => {
    const displayNames = item?.state?.details.map((stateDetail: any) => stateDetail.stateDisplayName).join(',')

    return [
      {
        label: 'Loại điều kiện',
        content: <div>{getConditionTypeLabel(item?.type)}</div>
      },
      {
        label: 'Thành phần cần kiểm tra',
        content: convertProductTypeName(item?.state) || 'Chưa có thông tin'
      },
      { label: 'Trạng thái yêu cầu', content: displayNames || 'Chưa có thông tin' }
    ]
  }

  const getManualField = (item: any) => {
    const roleName = item?.manual?.roles?.join(',')
    const agentName = item?.manual?.agentTypes?.join(',')

    return [
      {
        label: 'Loại điều kiện',
        content: <div>{getConditionTypeLabel(item?.type)}</div>
      },
      { label: 'Tác nhân thực hiện', content: agentName || 'Chưa có thông tin' },
      { label: 'Vai trò', content: roleName || 'Chưa có thông tin' }
    ]
  }

  const getScheduleField = (item: any, isCondition?: boolean) => {
    const schedule = item?.schedule
    const scheduleType = schedule?.type

    const createField = (label: string, value: any) => ({
      label,
      content: <div>{value || ''}</div>
    })

    const fieldConfigs = {
      ONCE: ['startTime', 'startDate'],
      DAILY: ['intervalType', 'startTime', 'startDate'],
      WEEKLY: ['intervalType', 'daysOfWeek', 'startTime', 'startDate'],
      MONTHLY:
        schedule?.intervalType === 'MONTHLY_FIXED_WEEKDAY'
          ? ['intervalType', 'weekOfMonth', 'daysOfWeek', 'startTime', 'startDate']
          : ['intervalType', 'daysOfMonth', 'startTime', 'startDate']
    }

    const conditionFields = [
      createField('Loại điều kiện', getConditionTypeLabel(item?.type)),
      createField('Thời điểm quét', TYPE_LABELS[scheduleType as keyof typeof TYPE_LABELS] || '')
    ]

    const notificationFields = [
      createField('Thời điểm đồng bộ', TYPE_LABELS[scheduleType as keyof typeof TYPE_LABELS] || '')
    ]

    fieldConfigs[scheduleType as keyof typeof fieldConfigs]?.forEach(key => {
      const value = key.includes('days') ? schedule?.[key]?.join(', ') : schedule?.[key]

      conditionFields.push(createField(FIELD_LABELS[key as keyof typeof FIELD_LABELS], value))
      notificationFields.push(createField(FIELD_LABELS[key as keyof typeof FIELD_LABELS], value))
    })

    return isCondition ? conditionFields : notificationFields
  }

  const getNotificationField = (item: any) => {
    const isCustom = item?.contentType === 'CUSTOM'

    return [
      ...(isCustom
        ? [
            { label: 'Notification', content: item?.title || 'Chưa có thông tin' },
            { label: 'Nội dung', content: item?.content || 'Chưa có thông tin' }
          ]
        : [{ label: 'Email', content: item?.templateCode || 'Chưa có thông tin' }])
    ]
  }

  const renderNotificationHeader = () => (
    <div className='flex items-center gap-2'>
      <div className='size-2 rounded-full bg-bg-success-default' />
      <div className='text-sm font-normal'>Gửi thông báo</div>
    </div>
  )

  // Điều kiện trạng thái
  const renderStateConditionDetail = (trigger: TriggerType) => {
    const stateField = getStateField(trigger)

    return (
      <div className='grid grid-cols-2 gap-4'>
        {stateField?.map(field => (
          <>
            <ContentRender label={field.label} content={field.content} />
          </>
        ))}
      </div>
    )
  }

  const renderConditionText = (condition: any) => {
    if (condition.ifconds && Array.isArray(condition.ifconds)) {
      const conditionTexts: { conditionLabel: string; operatorLabel: string; valueText: string }[] = []

      condition.ifconds.forEach((ifcond: any) => {
        // Tìm tên điều kiện dựa trên operandId
        const operandKey = Object.keys(OPERAND_ID).find(
          key => OPERAND_ID[key as keyof typeof OPERAND_ID] === ifcond.operandId
        )

        if (operandKey) {
          // Tìm label của điều kiện
          const conditionItem = RULES_ENGINE_CONDITIONS.find(item => item.value === operandKey)
          const conditionLabel = conditionItem?.label || `Operand ${ifcond.operandId}`

          // Tìm operator text
          const operatorItem = OPERATORS.find(op => op.value === ifcond.operator)
          const operatorLabel = operatorItem?.label || '='

          if (ifcond.operandId === OPERAND_ID.ORDER_ADDRESS) {
            if (Array.isArray(ifcond?.data?.value)) {
              ifcond.data.value.forEach((val: any) => {
                const addressItem = addressList?.find((item: any) => item?.id === val)

                const valueText = addressItem?.name || val.toString()

                conditionTexts.push({ conditionLabel, operatorLabel, valueText })
              })
            }
          } else {
            // Xử lý theo cấu trúc dữ liệu mới dựa trên toán tử
            const operatorItem = OPERATORS.find(op => op.value === ifcond.operator)
            const isRangeOperator = operatorItem?.label === 'Trong khoảng'

            if (Array.isArray(ifcond?.data?.value)) {
              ifcond.data.value.forEach((val: any) => {
                let valueText = ''

                if (isRangeOperator) {
                  // Toán tử "Trong khoảng"
                  const startUnit = val.start?.unit
                  const startValue = val.start?.value
                  const endUnit = val.end?.unit
                  const endValue = val.end?.value

                  const startUnitLabel = DATE_UNIT.find(unit => unit.value === startUnit)?.label || 'Ngày'
                  const endUnitLabel = DATE_UNIT.find(unit => unit.value === endUnit)?.label || 'Ngày'

                  valueText = `${startValue} ${startUnitLabel} - ${endValue} ${endUnitLabel}`
                } else {
                  // Toán tử "=" hoặc ">"
                  const unit = val.unit
                  const value = val.value

                  const unitLabel = DATE_UNIT.find(dateUnit => dateUnit.value === unit)?.label || 'Ngày'

                  valueText = `${value} ${unitLabel}`
                }

                conditionTexts.push({ conditionLabel, operatorLabel, valueText })
              })
            }

            if (ifcond?.data?.value !== null && ifcond?.data?.value !== undefined) {
              // Xử lý trường hợp value là object đơn lẻ (không phải array)
              const val = ifcond.data.value
              let valueText = ''

              if (isRangeOperator) {
                // Toán tử "Trong khoảng"
                const startUnit = val.start?.unit
                const startValue = val.start?.value
                const endUnit = val.end?.unit
                const endValue = val.end?.value

                const startUnitLabel = DATE_UNIT.find(unit => unit.value === startUnit)?.label || 'Ngày'
                const endUnitLabel = DATE_UNIT.find(unit => unit.value === endUnit)?.label || 'Ngày'

                valueText = `${startValue} ${startUnitLabel} - ${endValue} ${endUnitLabel}`
              } else {
                // Toán tử "=" hoặc ">"
                const unit = ifcond.data.unit
                const value = ifcond.data.value

                const unitLabel = DATE_UNIT.find(dateUnit => dateUnit.value === unit)?.label || 'Ngày'

                valueText = `${value} ${unitLabel}`
              }

              conditionTexts.push({ conditionLabel, operatorLabel, valueText })
            }
          }
        }
      })

      return conditionTexts
    }

    return []
  }

  // Điều kiện dữ liệu
  const renderRuleEngineDetail = (trigger: TriggerType) => (
    <div>
      <ContentRender label='Loại điều kiện' content={getConditionTypeLabel(trigger?.type as string)} />
      <div className='mt-4 space-y-4'>
        <div>
          {trigger?.ruleEngine?.conditions?.map((condition: any, index: number) => {
            const conditionTexts = renderConditionText(condition)

            return (
              <div key={index}>
                <div className='mb-2 text-sm font-normal text-text-neutral-medium'>Điều kiện {index + 1}</div>
                <div className='w-full rounded-lg border border-solid border-border-neutral-medium bg-white p-2'>
                  {conditionTexts?.length > 0 ? (
                    <div className='flex flex-col gap-[14px]'>
                      {conditionTexts.map((condText, condIndex) => (
                        <div key={condIndex} className='flex items-center gap-2'>
                          <CustomTag>{condText.conditionLabel}</CustomTag>
                          <CustomTag>{condText.operatorLabel}</CustomTag>
                          <CustomTag>{condText.valueText}</CustomTag>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className='py-1'>
                      <div>Không có điều kiện</div>
                    </div>
                  )}
                </div>
              </div>
            )
          })}
        </div>
      </div>
    </div>
  )

  // Điều kiện thời gian
  const renderScheduleConditionDetail = (trigger: TriggerType) => {
    const isCondition = true
    const scheduleField = getScheduleField(trigger, isCondition)

    return (
      <>
        <div className='grid grid-cols-2 gap-4'>
          {scheduleField?.map(field => (
            <>
              <ContentRender label={field.label} content={field.content} />
            </>
          ))}
        </div>
        {trigger?.schedule?.conditions?.length && (
          <div className='mt-4 space-y-4'>
            <div>
              {trigger?.schedule?.conditions?.map((condition: any, index: number) => {
                const conditionTexts = renderConditionText(condition)

                return (
                  <div key={index}>
                    <div className='mb-2 text-sm font-normal text-text-neutral-medium'>Điều kiện {index + 1}</div>
                    <div className='w-full rounded-lg border border-solid border-border-neutral-medium bg-white p-2'>
                      {conditionTexts?.length > 0 ? (
                        <div className='flex flex-col gap-[14px]'>
                          {conditionTexts.map((condText, condIndex) => (
                            <div key={condIndex} className='flex items-center gap-2'>
                              <CustomTag>{condText.conditionLabel}</CustomTag>
                              <CustomTag>{condText.operatorLabel}</CustomTag>
                              <CustomTag>{condText.valueText}</CustomTag>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className='py-1'>
                          <div>Không có điều kiện</div>
                        </div>
                      )}
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        )}
      </>
    )
  }

  const renderScheduleDetail = (trigger: TriggerType) => {
    const scheduleField = getScheduleField(trigger)

    return (
      <div className='grid grid-cols-2 gap-4'>
        {scheduleField?.map(field => (
          <>
            <ContentRender label={field.label} content={field.content} />
          </>
        ))}
      </div>
    )
  }

  // Điều kiện thủ công
  const renderManualDetail = (trigger: TriggerType) => {
    const manualField = getManualField(trigger)

    return (
      <div className='grid grid-cols-2 gap-4'>
        {manualField?.map(field => (
          <>
            <ContentRender label={field.label} content={field.content} />
          </>
        ))}
      </div>
    )
  }

  const renderSendNotificationDetail = (action: any, notificationType?: string) => {
    const sendNotificationField = getNotificationField(action)

    return (
      <div className='grid grid-cols-2 gap-4'>
        {notificationType && (
          <ContentRender label='Phương thức thông báo' content={<div>{getNotificationType(notificationType)}</div>} />
        )}
        {sendNotificationField?.map(field => (
          <>
            <ContentRender label={field.label} content={field.content} />
          </>
        ))}
      </div>
    )
  }

  return (
    <Drawer
      title={
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-4'>
            <div className='flex size-10 items-center justify-center rounded-full bg-[#CDE4FE]'>
              <i className='onedx-progress size-6 p-2 text-primary' />
            </div>
            <div>
              <div className='text-headline-16 font-semibold text-text-neutral-strong'>
                Chi tiết điều kiện chuyển tiếp
              </div>
              <Form.Item shouldUpdate noStyle>
                {({ prefixName, getFieldValue }: any) =>
                  getFieldValue(prefixName)?.map(
                    (item: any, index: any) =>
                      item?.code === conditionDetail?.code && (
                        <div key={item?.code} className='mt-2 text-sm font-normal text-gray-8'>
                          Từ: <strong>{getFieldValue(prefixName)?.[index - 1]?.name}</strong> sang{' '}
                          <strong>{item?.name}</strong>
                        </div>
                      )
                  )
                }
              </Form.Item>
            </div>
          </div>
          <div className='hover:cursor-pointer' onClick={onClose}>
            <i className='onedx-close-icon size-6' />
          </div>
        </div>
      }
      width={590}
      closable={false}
      open={open}
      footer={
        <div className='flex justify-end'>
          <Button className='border border-solid border-primary text-primary' onClick={onClose}>
            Đóng
          </Button>
        </div>
      }
    >
      {/* Logic condition */}
      <div className='text-body-14'>
        <div className='mb-2 font-normal text-text-neutral-medium'>Logic kết hợp các điều kiện</div>
        {conditionDetail?.triggerLogic === 'AND' && <div className='font-medium'>Tất cả phải đúng (AND)</div>}
        {conditionDetail?.triggerLogic === 'OR' && <div className='font-medium'>Ít nhất 1 điều kiện đúng (OR)</div>}
      </div>

      {/* Condition Detail */}
      {conditionDetail?.trigger?.map((item: TriggerType, index: number) => (
        <>
          <NotificationCollapse
            key={index}
            label={<div>{`Điều kiện ${index + 1}`}</div>}
            className='mt-5 bg-bg-neutral-lightest'
          >
            {item.type === 'STATE' && renderStateConditionDetail(item)}
            {item.type === 'RULE_ENGINE' && renderRuleEngineDetail(item)}
            {item.type === 'SCHEDULE' && renderScheduleConditionDetail(item)}
            {item.type === 'MANUAL' && renderManualDetail(item)}
          </NotificationCollapse>
        </>
      ))}

      {/* Hành động nếu không đạt */}
      {!!conditionDetail?.postActions && (
        <NotificationCollapse
          label={
            <div className='flex justify-between border-l-4 border-solid border-yellow-6'>
              <div className='pl-3 text-base font-medium'>Hành động nếu không đạt</div>
            </div>
          }
          className='mt-5 bg-bg-neutral-lightest'
        >
          <div className='grid grid-cols-2 gap-2 text-sm'>
            <div className='font-normal text-text-neutral-medium'>Hành động</div>
            {conditionDetail?.postActions?.postActionType === 'NOTIFICATION' && (
              <div className='font-medium'>Gửi thông báo</div>
            )}
          </div>

          {conditionDetail?.postActions?.EMAIL && (
            <NotificationCollapse
              label={renderNotificationHeader()}
              className='mt-5 rounded-lg border border-solid border-border-neutral-medium bg-white'
            >
              {renderSendNotificationDetail(conditionDetail?.postActions.EMAIL, 'EMAIL')}
            </NotificationCollapse>
          )}

          {conditionDetail?.postActions?.NOTIFICATION && (
            <NotificationCollapse
              label={renderNotificationHeader()}
              className='mt-5 rounded-lg border border-solid border-border-neutral-medium bg-white'
            >
              {renderSendNotificationDetail(conditionDetail?.postActions.NOTIFICATION, 'NOTIFICATION')}
            </NotificationCollapse>
          )}

          {conditionDetail?.postActions?.schedule && (
            <NotificationCollapse
              label={renderNotificationHeader()}
              className='mt-5 rounded-lg border border-solid border-border-neutral-medium bg-white'
            >
              {renderScheduleDetail(conditionDetail?.postActions)}
            </NotificationCollapse>
          )}
          {/* schedule */}
        </NotificationCollapse>
      )}
    </Drawer>
  )
}
