'use client'

import { useCallback, useMemo, useState } from 'react'

import { useRouter } from 'next/navigation'

import type { TableColumnType } from 'antd'
import { Button, DatePicker, Pagination, Popover, Select, Spin, Tag, Tooltip } from 'antd'

import dayjs from 'dayjs'

import { useMutation, useQuery } from '@tanstack/react-query'

import { debounce } from 'lodash'

import SettingInput from '@/components/filter/SettingInput'
import { filterOptionTrim } from '@/utils/string'
import { EmptyTableOrder } from '@/views/order-management/common'
import { pageSizeOptions, quickSearchOptionsList, statusOptions, tagStatusProcess } from '../common/constants'
import { CellWrapper, CustomTable, Ribbon } from '../common'
import { processManagement } from '@/models/workflow/processManagement'
import type { ProcessListType } from '@/types/workflow/processManagement'
import { ConfirmModal } from '@/views/custom-field/editor/sections/create-service/variant/modal/ConfirmModal'
import { message } from '@/components/notification'

const { RangePicker } = DatePicker

export const ProcessList = () => {
  const router = useRouter()

  // region state
  const [checkedFilter, setCheckedFilter] = useState<any>({
    isName: true,
    isCode: true
  })

  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([])
  const [hoveredRow, setHoveredRow] = useState(null)

  const [openConfirmModal, setOpenConfirmModal] = useState(false)
  const [infoModal, setInfoModal] = useState({})

  // TODO: ghep api
  /* eslint-disable @typescript-eslint/no-unused-vars */
  const [page, setPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [rangeValue, setRangeValue] = useState<any>(null)

  const [filterParams, setFilterParams] = useState<any>({
    isLatest: 1
  })

  // region call api
  const {
    data: listProcess,
    isFetching,
    refetch
  } = useQuery({
    queryKey: ['listProcess', page, pageSize, filterParams, checkedFilter],
    queryFn: () =>
      processManagement.getListProcess({
        ...filterParams,
        page: page - 1,
        size: pageSize,
        sort: 'createdAt,desc',
        isName: checkedFilter.isName ? 1 : 0,
        isCode: checkedFilter.isCode ? 1 : 0
      })
  })

  const deleteProcessMutation = useMutation({
    mutationKey: ['deleteProcess'],
    mutationFn: async (body: ProcessListType) => await processManagement.deleteProcessById(body),
    onSuccess: () => {
      refetch()
      message.success('Xóa tiến trình thành công!')
    },
    onError: () => {
      message.error('Xóa tiến trình thất bại!')
    }
  })

  const deleteListProcessMutation = useMutation({
    mutationKey: ['deleteProcess'],
    mutationFn: async (body: any) => await processManagement.deleteListProcessByIds(body),
    onSuccess: () => {
      refetch()
      message.success('Xóa tiến trình thành công!')
    },
    onError: () => {
      message.error('Xóa tiến trình thất bại!')
    }
  })

  // region handler

  const disabledDate = (current: any) => {
    const today = dayjs().endOf('day')

    // Nếu chưa chọn ngày Từ, chỉ disable ngày lớn hơn hôm nay
    if (!rangeValue?.[0]) {
      return current && current > today
    }

    // Nếu đã chọn ngày Từ, disable ngày < ngày Từ hoặc > hôm nay
    return (current && current < dayjs(rangeValue[0]).startOf('day')) || (current && current > today)
  }

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const handleSearch = useCallback(
    debounce((value: string) => {
      setFilterParams({ ...filterParams, search: value?.replace(/%/g, '\\%').replace(/_/g, '\\_') || '' })
    }, 400),
    []
  )

  const handleCalendarChange = (dateChange: any) => {
    setRangeValue(dateChange)

    if (!dateChange?.[0] && !dateChange?.[1]) {
      setFilterParams({ ...filterParams, startTime: undefined, endTime: undefined })
    } else {
      if (dateChange?.[0]) {
        setFilterParams({ ...filterParams, startTime: dateChange[0].format('DD/MM/YYYY') })
      }

      if (dateChange?.[1]) {
        setFilterParams({ ...filterParams, endTime: dateChange[1].format('DD/MM/YYYY') })
      }
    }
  }

  // region columns
  const columns: TableColumnType<ProcessListType>[] = [
    {
      title: <div className='text-caption-12 font-semibold text-text-neutral-medium'>Tên tiến trình</div>,
      dataIndex: 'name',
      key: 'name',
      width: 513,
      sorter: (a: any, b: any) => a.name?.localeCompare(b.name),
      render: (text: string, record: any) => (
        <CellWrapper>
          <div
            className='cursor-pointer'
            onClick={() => router.push(`/workflow/process-management/detail/${record.id}?tab=GENERAL`)}
          >
            {text}
          </div>
          {record.isDefault && <Ribbon>Mẫu</Ribbon>}
        </CellWrapper>
      )
    },
    {
      title: <div className='text-caption-12 font-semibold text-text-neutral-medium'>Mã định danh</div>,
      dataIndex: 'code',
      key: 'code',
      width: 132
    },
    {
      title: <div className='text-caption-12 font-semibold text-text-neutral-medium'>Phiên bản</div>,
      dataIndex: 'version',
      key: 'version',
      width: 150,
      render: () => <div>v.1.0</div>
    },
    {
      title: <div className='text-caption-12 font-semibold text-text-neutral-medium'>Trạng thái</div>,
      dataIndex: 'status',
      key: 'status',
      width: 153,
      align: 'center',
      render: (status: keyof typeof tagStatusProcess) => {
        const tagInfo = tagStatusProcess[status]

        if (!tagInfo) return null

        return (
          <Tag className='text-center' color={tagInfo.bgColor} style={{ minWidth: '8.75rem', padding: '2px 8px' }}>
            <div className='text-caption-12 font-medium' style={{ color: tagInfo.color }}>
              {tagInfo.text}
            </div>
          </Tag>
        )
      }
    },
    {
      title: <div className='text-caption-12 font-semibold text-text-neutral-medium'>Thời gian cập nhật</div>,
      dataIndex: 'modifiedAt',
      key: 'modifiedAt',
      width: 153
    },
    {
      key: 'action',
      fixed: 'right',
      width: 52,
      render: (_: any, record: any) => {
        return (
          hoveredRow === record.id && (
            <div className='flex items-center justify-center'>
              <Popover
                overlayInnerStyle={{ padding: 0 }}
                trigger='click'
                content={renderContentPopover(record)}
                placement='bottomRight'
                arrow={false}
              >
                <i className='onedx-menu-dot-vertical size-4 cursor-pointer' />
              </Popover>
            </div>
          )
        )
      }
    }
  ]

  const rowSelection: any = {
    type: 'checkbox',
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(newSelectedRowKeys)
    }
  }

  // region render
  const renderContentPopover = (record: ProcessListType) => (
    <div className='flex w-[157px] flex-col text-body-14 font-medium'>
      {record?.status === 'ACTIVE' && (
        <Tooltip title='Kích hoạt mẫu tiến trình'>
          <Button
            className='h-11 w-full justify-start rounded-b-none border-x-0 border-t-0 border-border-neutral-light text-text-neutral-strong hover:text-primary-blue'
            onClick={() => {
              setInfoModal({
                iconType: 'NOTIFY',
                title: 'Xác nhận kích hoạt mẫu',
                description: (
                  <span className='py-4 text-body-14 font-medium'>
                    Bạn có chắc chắn muốn kích hoạt mẫu tiến trình ?
                  </span>
                ),
                confirmButtonText: 'Xác nhận',
                onClickConfirm: () => {
                  console.log(record?.id)
                }, // call api kích hoạt mẫu
                onClickCancel: () => setOpenConfirmModal(false)
              })
              setOpenConfirmModal(true)
            }}
          >
            <i className='onedx-kanban size-5' />
            <div>Kích hoạt mẫu</div>
          </Button>
        </Tooltip>
      )}
      <Button
        className='h-11 w-full justify-start rounded-b-none border-x-0 border-t-0 border-border-neutral-light text-text-neutral-strong hover:text-primary-blue'
        onClick={() => router.push(`/workflow/process-management/duplicate/${record?.id}`)}
      >
        <i className='onedx-copy size-5' />
        <div>Nhân bản</div>
      </Button>

      <Button
        className='h-11 w-full justify-start border-none border-border-neutral-light text-text-error-default'
        disabled={record?.isDefault}
        onClick={() => {
          setInfoModal({
            iconType: 'DELETE',
            title: 'Xác nhận xóa tiến trình',
            description: <span className='py-4 text-body-14 font-medium'>Bạn có chắc chắn muốn xóa tiến trình ?</span>,
            confirmButtonText: 'Xác nhận',
            onClickConfirm: () => {
              deleteProcessMutation.mutate(record)
              setOpenConfirmModal(false)
            },
            onClickCancel: () => setOpenConfirmModal(false)
          })
          setOpenConfirmModal(true)
        }}
      >
        <i className='onedx-delete size-5' />
        <div>Xóa</div>
      </Button>
    </div>
  )

  const renderTable = () => (
    <div className='w-full overflow-hidden'>
      <Spin spinning={isFetching}>
        <CustomTable
          rowSelection={rowSelection}
          columns={columns as any}
          dataSource={listProcess?.content}
          rowKey='id'
          pagination={false}
          scroll={{ x: '1200', y: 'calc(100vh - 300px)' }}
          locale={{
            emptyText: <EmptyTableOrder emptyText='Không có dữ liệu tiến trình' />
          }}
          tableLayout='fixed'
          onRow={(record: any) => ({
            onMouseEnter: () => setHoveredRow(record.id),
            onMouseLeave: () => setHoveredRow(null)
          })}
        />
      </Spin>
    </div>
  )

  const pagination = useMemo(
    () => ({
      position: ['none', 'bottomCenter'] as any,
      current: page,
      pageSize: pageSize,
      total: listProcess?.totalElements,
      onChange: (page: number, pageSize: number) => {
        setPage(page)
        setPageSize(pageSize)
      }
    }),
    [listProcess?.totalElements, page, pageSize]
  )

  const renderPagination = () => (
    <div className='mt-4 flex w-full justify-between'>
      <div className='flex gap-4'>
        <div className='pt-2'>Đã chọn: {selectedRowKeys?.length}</div>
        <Tooltip title='Xóa tiến trình'>
          <Button
            onClick={() => {
              setInfoModal({
                iconType: 'DELETE',
                title: 'Xác nhận xóa tiến trình',
                description: (
                  <span className='py-4 text-body-14 font-medium'>Bạn có chắc chắn muốn xóa tiến trình đã chọn ?</span>
                ),
                confirmButtonText: 'Xác nhận',
                onClickConfirm: () => {
                  deleteListProcessMutation.mutate({ ids: selectedRowKeys })
                  setOpenConfirmModal(false)
                }, // call api xóa
                onClickCancel: () => setOpenConfirmModal(false)
              })
              setOpenConfirmModal(true)
            }}
            className='border-primary-blue bg-white text-primary-blue'
            disabled={selectedRowKeys?.length === 0}
          >
            <i className='onedx-delete-traffic size-4 ' /> Xóa
          </Button>
        </Tooltip>
      </div>
      <Pagination {...pagination} showSizeChanger={false} />
      <Select defaultValue={10} options={pageSizeOptions} onChange={value => setPageSize(value)} />
    </div>
  )

  // region return
  return (
    <div className='size-full bg-bg-surface-secondary'>
      {/* Header */}
      <div className='flex items-center justify-between bg-bg-surface p-4'>
        <div className='flex items-center gap-3'>
          <div className='h-5 w-1 rounded-sm bg-icon-neutral-lighter' />
          <div className='text-headline-16 font-semibold text-text-neutral-strong'>
            Danh sách tiến trình đơn hàng IoT
          </div>
        </div>
        <Button
          type='primary'
          icon={<i className='onedx-add size-4' />}
          onClick={() => {
            router.push('/workflow/process-management/create')
          }}
          className='h-9 bg-primary-blue'
        >
          Tạo tiến trình mới
        </Button>
      </div>
      {/* Filter */}
      <div className='mt-2 bg-bg-surface p-4'>
        <div className='mb-4 flex items-center gap-4'>
          <SettingInput
            placeholder='Tìm kiếm theo tên tiến trình, mã định danh'
            styles={{ width: '100%' }}
            checked={checkedFilter}
            setChecked={setCheckedFilter}
            onKeyDown={event => {
              if (event.key === 'Enter') {
                event.preventDefault()
                handleSearch(event.currentTarget.value)
                setPage(1)
              }
            }}
            checkBoxOptions={quickSearchOptionsList}
            size='small'
          />
          <Select
            mode='multiple'
            placeholder={<div className='text-text-neutral-light'>Phiên bản</div>}
            options={[]}
            filterOption={filterOptionTrim}
            maxTagCount='responsive'
            maxTagPlaceholder={omittedValues => (
              <Tooltip
                styles={{ root: { pointerEvents: 'none' } }}
                title={omittedValues.map(({ label }) => label).join(', ')}
              >
                <span>+{omittedValues.length}</span>
              </Tooltip>
            )}
            onChange={() => {}}
            notFoundContent='Không có dữ liệu tìm kiếm'
            className='w-[123px] shrink-0'
            style={{ height: 36 }}
          />
          <RangePicker
            format='DD-MM-YYYY'
            size='small'
            placeholder={['Từ', 'Đến']}
            onCalendarChange={handleCalendarChange}
            allowEmpty={[true, true]}
            value={rangeValue}
            disabledDate={disabledDate}
            className='w-[170px]'
            style={{ height: 36 }}
          />
          <Select
            mode='multiple'
            placeholder={<div className='text-text-neutral-light'>Trạng thái</div>}
            options={statusOptions}
            filterOption={filterOptionTrim}
            maxTagCount='responsive'
            maxTagPlaceholder={omittedValues => (
              <Tooltip
                styles={{ root: { pointerEvents: 'none' } }}
                title={omittedValues.map(({ label }) => label).join(', ')}
              >
                <span>+{omittedValues.length}</span>
              </Tooltip>
            )}
            onChange={values => {
              setFilterParams({ ...filterParams, lstStatus: values.join(',') })
            }}
            notFoundContent='Không có dữ liệu'
            className='w-[150px] shrink-0'
            style={{ height: 36 }}
          />
        </div>
        {renderTable()}
        {renderPagination()}
      </div>

      <ConfirmModal openModal={openConfirmModal} setOpenModal={setOpenConfirmModal} infoModal={infoModal} />
    </div>
  )
}
