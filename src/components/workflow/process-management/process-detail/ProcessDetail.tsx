'use client'

import { useCallback, useState } from 'react'

import { useParams, usePathname, useRouter, useSearchParams } from 'next/navigation'

import { Button } from 'antd'

import { useQuery } from '@tanstack/react-query'

import { GeneralInfoTab, OrderApplyTab, ProcessStepTab, ActivityHistory } from './process-tabs'
import { CustomTabs } from '../common/CustomTabs'
import { processManagement } from '@/models/workflow/processManagement'
import { CONVERT_PROCESS_DATA } from '@/views/workflow/process-management/create/convert'

export const ProcessDetail = () => {
  const searchParams = useSearchParams()
  const pathname = usePathname()

  const { id } = useParams()

  const tabParam = searchParams.get('tab')
  const router = useRouter()
  const [activeKey, setActiveKey] = useState(tabParam || 'OVERVIEW')

  const { data: processDetail } = useQuery({
    queryKey: ['processDetail'],
    queryFn: () => processManagement.getDetailProcessById(id).then(CONVERT_PROCESS_DATA),
    enabled: !!id
  })

  const handleChangeActiveKey = useCallback(
    (key: string) => {
      setActiveKey(key)
      // Cập nhật URL khi thay đổi tab
      const params = new URLSearchParams(searchParams.toString())

      params.set('tab', key)

      router.push(`${pathname}?${params.toString()}`)
    },
    [pathname, router, searchParams]
  )

  const processDetailTabs = [
    {
      key: 'GENERAL',
      label: 'Thông tin chung',
      children: <GeneralInfoTab processInfo={processDetail} />
    },
    {
      key: 'PROCESS_STEP',
      label: 'Chi tiết các bước tiến trình',
      children: <ProcessStepTab listStep={processDetail?.workflowStepsDTO} />
    },
    {
      key: 'ORDER_APPLY',
      label: 'Đơn hàng áp dụng',
      children: <OrderApplyTab id={id} tab={tabParam} />
    },
    {
      key: 'HISTORY',
      label: 'Lịch sử hoạt động',
      children: <ActivityHistory draftId={processDetail?.draftId} />
    }
  ]

  return (
    <div className='w-full bg-bg-surface-secondary'>
      {/* Header */}
      <div className='flex items-center gap-3 bg-white p-4'>
        <Button
          type='text'
          icon={<i className='onedx-chevron-left size-5' />}
          onClick={() => {
            router.push('/workflow/process-management/list')
          }}
        />
        <div className='text-headline-20 font-semibold text-text-neutral-strong'>{`Chi tiết trạng thái ${processDetail?.name}`}</div>
      </div>

      {/* Tab*/}
      <CustomTabs activeKey={activeKey} onChange={handleChangeActiveKey} items={processDetailTabs} />
    </div>
  )
}
