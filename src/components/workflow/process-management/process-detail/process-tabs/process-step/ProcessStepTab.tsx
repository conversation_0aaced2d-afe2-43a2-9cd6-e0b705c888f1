'use client'

import { useEffect } from 'react'

import { Form } from 'antd'

import { StepList } from '@views/workflow/process-management/create/step/step-list/StepList'

import '@xyflow/react/dist/style.css'

export const ProcessStepTab = ({ listStep }: any) => {
  const [form] = Form.useForm()

  useEffect(() => {
    if (listStep?.length) {
      form.setFieldsValue({ workflowStepsDTO: listStep })
    }
  }, [form, listStep])

  return (
    <div className='bg-white'>
      <Form form={form}>
        <StepList />
      </Form>
    </div>
  )
}
