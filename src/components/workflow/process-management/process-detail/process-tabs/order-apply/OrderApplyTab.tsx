import { useState, useEffect, useRef, useMemo, useCallback } from 'react'

import { Collapse, DatePicker, Pagination, Select, Tag } from 'antd'

import dayjs from 'dayjs'

import { useQuery } from '@tanstack/react-query'

import { debounce } from 'lodash'

import SettingInput from '@/components/filter/SettingInput'
import { MultiCheckboxSelect } from '@/components/filter/MultiCheckboxSelect'
import { progressOptions } from '@/views/order-management/constants'
import { CustomTable, getStatusOrder, pageSizeOptions, quickSearchOptions, StyledCollapse } from '../../../common'
import { CustomPopover, EmptyTableOrder, OrderProductList } from '@/views/order-management/common'
import { OrderSummaryDetail } from './components/OrderSummaryDetail'
import { processManagement } from '@/models/workflow/processManagement'

const { Panel } = Collapse
const { RangePicker } = DatePicker

export const OrderApplyTab = ({ id, tab }: any) => {
  const [checkedFilter, setCheckedFilter] = useState<any>({
    isCartCode: true, // Mã đơn hàng
    isServiceName: true, // Tên sp dịch vụ
    isCustomerName: true, // Tên khách hàng
    isProviderName: true // Nhà cung cấp
  })

  const scrollRef = useRef<HTMLDivElement>(null)
  const [canScrollLeft, setCanScrollLeft] = useState(false)
  const [canScrollRight, setCanScrollRight] = useState(false)
  const [openOrderDetail, setOpenOrderDetail] = useState(false)
  const [selectedProgress, setSelectedProgress] = useState<any>()
  const [page, setPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [rangeValue, setRangeValue] = useState()
  const [filterParams, setFilterParams] = useState({})
  const [orderCode, setOrderCode] = useState(null)
  const [subCode, setSubCode] = useState(null)

  // data thống kê đơn hàng theo step
  const { data: orderStatisticData } = useQuery({
    queryKey: ['order-statistic-data'],
    queryFn: async () => await processManagement.getStatisticOrderByStep(id),
    enabled: !!id && tab === 'ORDER_APPLY'
  })

  // data danh sách đơn hàng áp dụng
  const { data: orderApplyData } = useQuery({
    queryKey: ['order-apply-data', filterParams, page, pageSize],
    queryFn: async () => {
      const res = await processManagement.getListOrderApply(id, {
        page: page - 1,
        size: pageSize,
        sort: 'createdAt,desc',
        isCartCode: checkedFilter?.isCartCode ? 1 : 0,
        isCustomerName: checkedFilter?.isCustomerName ? 1 : 0,
        isServiceName: checkedFilter?.isServiceName ? 1 : 0,
        isProviderName: checkedFilter?.isProviderName ? 1 : 0,
        ...filterParams
      })

      return res
    },
    enabled: !!id && tab === 'ORDER_APPLY'
  })

  const columns: any = [
    {
      title: <div className='text-caption-12 font-semibold text-text-neutral-medium'>STT</div>,
      key: 1,
      width: 56,
      render: (_: any, _record: any, index: number) => <div>{(page - 1) * pageSize + index + 1}</div>
    },
    {
      title: <div className='text-caption-12 font-semibold text-text-neutral-medium'>Mã đơn hàng</div>,
      dataIndex: 'subCode',
      key: 'subCode',
      width: 129,
      render: (value: any, record: any) => (
        <div
          className='cursor-pointer'
          onClick={() => {
            setOrderCode(record?.isCart ? record?.subCode : record?.subId)
            setSubCode(record?.subCode)
            setOpenOrderDetail(true)
          }}
        >
          {value}
        </div>
      )
    },
    {
      title: <div className='text-caption-12 font-semibold text-text-neutral-medium'>Sản phẩm</div>,
      dataIndex: 'orderItems',
      key: 'orderItems',
      width: 200,
      render: (value: any, record: any) => {
        // Nội dung hiển thị trong Popover luôn là orderItems
        const popoverContent = <OrderProductList orderItems={value} />

        // Ưu tiên hiển thị theo thứ tự: solutionName -> packageName -> orderItems
        const displayName = record.packageName || value?.[0]?.serviceName

        if (!displayName) return null

        let displayContent = (
          <div className='inline-block max-w-[165px] cursor-pointer truncate rounded-md bg-bg-neutral-lighter px-2 py-[2px]'>
            <span className='text-caption-12 font-medium text-text-neutral-medium'>{displayName}</span>
          </div>
        )

        if (!record.packageName && value?.length > 1) {
          displayContent = (
            <div className='flex cursor-pointer items-center gap-1'>
              <div className='inline-block max-w-[123px] truncate rounded-md bg-bg-neutral-lighter px-2 py-[2px]'>
                <span className='text-caption-12 font-medium text-text-neutral-medium'>{value[0].serviceName}</span>
              </div>
              <div className='inline-block rounded-md bg-bg-neutral-lighter px-2 py-[2px]'>
                <span className='text-caption-12 font-medium text-text-neutral-medium'>+{value.length - 1}</span>
              </div>
            </div>
          )
        }

        return (
          <CustomPopover content={popoverContent} placement='right' trigger='hover' arrow={false}>
            {displayContent}
          </CustomPopover>
        )
      }
    },
    {
      title: <div className='text-caption-12 font-semibold text-text-neutral-medium'>Nhà cung cấp</div>,
      dataIndex: 'providerName',
      key: 'providerName',
      width: 133,
      ellipsis: true,
      render: (value: any) => <div className='truncate'>{value}</div>
    },
    {
      title: <div className='text-caption-12 font-semibold text-text-neutral-medium'>Khách hàng</div>,
      dataIndex: 'userName',
      key: 'userName',
      width: 122
    },
    {
      title: <div className='text-caption-12 font-semibold text-text-neutral-medium'>Tiến trình đơn hàng</div>,
      dataIndex: 'totalProgressStatusEnum',
      key: 'totalProgressStatusEnum',
      width: 170,
      render: (value: any) => {
        return (
          <Tag
            style={{
              backgroundColor: getStatusOrder(value)?.backgroundColor,
              color: getStatusOrder(value)?.color,
              border: 'none',
              padding: '2px 8px',
              width: '100%',
              textAlign: 'center'
            }}
          >
            {getStatusOrder(value)?.statusText}
          </Tag>
        )
      }
    },
    {
      title: <div className='text-caption-12 font-semibold text-text-neutral-medium'>Ngày đặt hàng</div>,
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 165
    },
    {
      title: <div className='text-caption-12 font-semibold text-text-neutral-medium'>Người phụ trách</div>,
      dataIndex: 'assigneeName',
      key: 'assigneeName',
      width: 195
    }
  ]

  const renderOrderStatus = ({ label, quantity }: { label: string; quantity: number }) => (
    <div className='w-[212px] rounded-xl border border-solid border-border-neutral-light bg-white p-4'>
      <div className='text-body-14 font-normal text-text-neutral-medium'>{label}</div>
      <div className='text-title-36 font-semibold text-text-neutral-strong'>{quantity}</div>
    </div>
  )

  useEffect(() => {
    const checkScroll = () => {
      const el = scrollRef.current

      if (!el) return
      setCanScrollLeft(el.scrollLeft > 0)
      setCanScrollRight(el.scrollLeft + el.clientWidth < el.scrollWidth)
    }

    checkScroll()
    const el = scrollRef.current

    if (el) {
      el.addEventListener('scroll', checkScroll)
      window.addEventListener('resize', checkScroll)
    }

    return () => {
      if (el) el.removeEventListener('scroll', checkScroll)
      window.removeEventListener('resize', checkScroll)
    }
  }, [])

  const handleScroll = (direction: 'left' | 'right') => {
    const el = scrollRef.current

    if (!el) return
    const scrollAmount = 220 // width of item + gap

    el.scrollBy({ left: direction === 'left' ? -scrollAmount : scrollAmount, behavior: 'smooth' })
  }

  const disabledDate = (current: any) => {
    const today = dayjs().endOf('day')

    // Nếu chưa chọn ngày Từ, chỉ disable ngày lớn hơn hôm nay
    if (!rangeValue?.[0]) {
      return current && current > today
    }

    // Nếu đã chọn ngày Từ, disable ngày < ngày Từ hoặc > hôm nay
    return (current && current < dayjs(rangeValue[0]).startOf('day')) || (current && current > today)
  }

  const handleCalendarChange = (dateChange: any) => {
    setRangeValue(dateChange)

    if (!dateChange?.[0] && !dateChange?.[1]) {
      setFilterParams({ ...filterParams, startDate: undefined, endDate: undefined })
    } else {
      if (dateChange?.[0]) {
        setFilterParams({ ...filterParams, startDate: dateChange[0].format('DD/MM/YYYY') })
      }

      if (dateChange?.[1]) {
        setFilterParams({ ...filterParams, endDate: dateChange[1].format('DD/MM/YYYY') })
      }
    }
  }

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const handleSearch = useCallback(
    debounce((value: string) => {
      setFilterParams({ ...filterParams, search: value?.replace(/%/g, '\\%').replace(/_/g, '\\_') || '' })
    }, 400),
    []
  )

  const pagination = useMemo(
    () => ({
      position: ['none', 'bottomCenter'] as any,
      current: page,
      pageSize: pageSize,
      total: orderApplyData?.totalElements,
      onChange: (page: number, pageSize: number) => {
        setPage(page)
        setPageSize(pageSize)
      }
    }),
    [orderApplyData, page, pageSize]
  )

  const renderPagination = () => (
    <div className='mt-4 flex w-full justify-between'>
      <div />
      <Pagination {...pagination} showSizeChanger={false} />
      <Select defaultValue={pageSize} options={pageSizeOptions} onChange={value => setPageSize(value)} />
    </div>
  )

  return (
    <div className='space-y-2'>
      {/* Thống kê đơn hàng */}
      <div className='relative bg-white p-4'>
        <div className='flex justify-between border-l-4 border-solid border-yellow-6'>
          <div className='pl-2 font-semibold'>Thống kê đơn hàng</div>
        </div>
        <div className='relative mt-4'>
          {canScrollLeft && (
            <div
              className='absolute left-0 top-1/2 z-10 flex size-10 -translate-y-1/2 items-center justify-center rounded-full border border-border-neutral-lighter bg-white shadow'
              onClick={() => handleScroll('left')}
            >
              <i className='onedx-chevron-left size-5 text-primary' />
            </div>
          )}
          <div
            ref={scrollRef}
            className='scrollbar-hide flex items-center gap-x-7 overflow-x-auto '
            style={{ scrollBehavior: 'smooth' }}
          >
            {orderStatisticData?.map((item: any, index: any) => (
              <div key={index}>{renderOrderStatus({ label: item.stepName, quantity: item.countAppliedItems })}</div>
            ))}
          </div>
          {canScrollRight && (
            <div
              className='absolute right-0 top-1/2 z-10 flex size-10 -translate-y-1/2 items-center justify-center rounded-full border border-border-neutral-lighter bg-white shadow'
              onClick={() => handleScroll('right')}
            >
              <i className='onedx-chevron-right size-5 text-primary' />
            </div>
          )}
        </div>
      </div>
      {/* Danh sách đơn hàng */}
      <StyledCollapse
        className='rounded-none bg-white'
        collapsible='icon'
        expandIconPosition='end'
        ghost
        defaultActiveKey={['1']}
        expandIcon={({ isActive }) => (
          <i className={`onedx-chevron-down size-5 text-icon-neutral-light ${isActive && 'rotate-180'}`} />
        )}
      >
        <Panel
          header={
            <div className='flex justify-between border-l-4 border-solid border-yellow-6'>
              <div className='pl-2 font-semibold'>Danh sách đơn hàng đã áp dụng tiến trình</div>
            </div>
          }
          key='1'
        >
          <div className='border-t border-solid border-border-neutral-light pt-4'>
            {/* Filter */}
            <div className='mb-4 flex items-center gap-x-3'>
              <SettingInput
                placeholder='Tìm kiếm theo mã, tên khách hàng'
                styles={{ width: '100%' }}
                checked={checkedFilter}
                setChecked={setCheckedFilter}
                onKeyDown={event => {
                  if (event.key === 'Enter') {
                    event.preventDefault()
                    handleSearch(event.currentTarget.value)
                    setPage(1)
                  }
                }}
                checkBoxOptions={quickSearchOptions}
                size='small'
              />
              <MultiCheckboxSelect
                optionFilter={progressOptions}
                placeholder='Tiến trình đơn hàng'
                onChange={values => {
                  setSelectedProgress(values)
                  setFilterParams({ ...filterParams, workflowStepId: values.length > 0 ? values : ['NONE'] })
                  setPage(1)
                }}
                value={selectedProgress}
              />

              <RangePicker
                format='DD/MM/YYYY'
                placeholder={['Từ', 'Đến']}
                onCalendarChange={handleCalendarChange}
                allowEmpty={[true, true]}
                value={rangeValue}
                disabledDate={disabledDate}
                className='w-[170px]'
                style={{ height: 36 }}
              />
            </div>

            {/* Table */}
            <CustomTable
              columns={columns}
              locale={{
                emptyText: <EmptyTableOrder emptyText='Không có dữ liệu tiến trình' />
              }}
              scroll={{ x: '1200' }}
              dataSource={orderApplyData?.content}
              pagination={false}
            />

            {renderPagination()}
          </div>
        </Panel>
      </StyledCollapse>

      {openOrderDetail && (
        <OrderSummaryDetail
          openSummaryDetail={openOrderDetail}
          setOpenSummaryDetail={setOpenOrderDetail}
          portalType={'admin'}
          orderCode={orderCode}
          subCode={subCode}
        />
      )}
    </div>
  )
}
