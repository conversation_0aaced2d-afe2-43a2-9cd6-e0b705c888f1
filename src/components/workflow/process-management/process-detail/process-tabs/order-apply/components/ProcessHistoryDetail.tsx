import { Button, Modal } from 'antd'

import { CustomTable } from '@/views/order-management/common'

interface ProcessHistoryDetailProps {
  openModal: boolean
  setOpenModal: any
}

const mockData = [
  {
    key: 1,
    service: 'Dịch vụ A',
    startState: 'Kiểm tra hoạt động thành công',
    endState: 'Kiểm tra hoạt động thành công',
    result: 'Thành công',
    trigger: 'Manual',
    role: 'Admin',
    time: '2024/06/01 10:00'
  },
  {
    key: 2,
    service: 'Dịch vụ B',
    startState: 'Kiểm tra hoạt động thành công',
    endState: 'Kiểm tra hoạt động thành công',
    result: 'Thất bại',
    trigger: 'Manual',
    role: 'Đối tác',
    time: '2024/06/02 14:30'
  }
]

export const ProcessHistoryDetail = ({ openModal, setOpenModal }: ProcessHistoryDetailProps) => {
  const handleClose = () => setOpenModal(false)

  const columns: any = [
    {
      title: <div className='text-caption-12 font-semibold text-text-neutral-medium'>Sản phẩm dịch vụ</div>,
      dataIndex: 'service',
      key: 'service',
      width: 143
    },
    {
      title: <div className='text-caption-12 font-semibold text-text-neutral-medium'>Trạng thái bắt đầu</div>,
      dataIndex: 'startState',
      key: 'startState',
      width: 259
    },
    {
      title: <div className='text-caption-12 font-semibold text-text-neutral-medium'>Trạng thái kết thúc</div>,
      dataIndex: 'endState',
      key: 'endState',
      width: 259
    },
    {
      title: <div className='text-caption-12 font-semibold text-text-neutral-medium'>Kết quả</div>,
      dataIndex: 'result',
      key: 'result',
      width: 153
    },
    {
      title: <div className='text-caption-12 font-semibold text-text-neutral-medium'>Trigger</div>,
      dataIndex: 'trigger',
      key: 'trigger',
      width: 95
    },
    {
      title: <div className='text-caption-12 font-semibold text-text-neutral-medium'>Vai trò</div>,
      dataIndex: 'role',
      key: 'role',
      width: 79
    },
    {
      title: <div className='text-caption-12 font-semibold text-text-neutral-medium'>Thời gian</div>,
      dataIndex: 'time',
      key: 'time',
      width: 164
    }
  ]

  const renderTable = () => (
    <div className='py-6'>
      <CustomTable columns={columns} dataSource={mockData} pagination={false} />
    </div>
  )

  return (
    <Modal
      centered
      onCancel={() => {
        setOpenModal(false)
      }}
      title={
        <div className='flex w-full justify-between gap-4 self-stretch'>
          <div className='flex items-center gap-4'>
            <div className='flex size-10 items-center justify-center rounded-full bg-bg-primary-light'>
              <i className='onedx-progress size-6 text-icon-primary-default' />
            </div>
            <span className='text-headline-16 font-semibold text-text-neutral-strong'>
              Xem chi tiết lịch sử thay đổi tiến trình
            </span>
          </div>
          <i className='onedx-close-icon size-6 cursor-pointer text-icon-neutral-medium' onClick={handleClose} />
        </div>
      }
      width={1200}
      open={openModal}
      footer={
        <div className='flex w-full justify-end gap-3'>
          <Button
            block
            className='h-9 w-[68px] border-text-info-default font-medium text-text-info-default'
            onClick={handleClose}
          >
            Đóng
          </Button>
        </div>
      }
      closable={false}
      className='rounded-2xl'
    >
      {renderTable()}
    </Modal>
  )
}
