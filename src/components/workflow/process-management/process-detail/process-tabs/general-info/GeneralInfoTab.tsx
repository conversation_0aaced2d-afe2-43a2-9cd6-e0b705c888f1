import { useMemo } from 'react'

import { useParams, useRouter } from 'next/navigation'

import { Divider, Tag } from 'antd'

import { generateClassificationFields } from '@/utils/workflow'
import type { ProcessDetail } from '@/types/workflow/processManagement'
import { CustomCollapse, tagStatusProcess } from '../../../common'

export const GeneralInfoTab = ({ processInfo }: ProcessDetail) => {
  const router = useRouter()

  const { id } = useParams()

  const generalField = useMemo(() => {
    return [
      { label: 'Tên tiến trình', content: processInfo?.name, hidden: false },
      { label: 'Mã định danh', content: processInfo?.code, hidden: false },

      {
        label: 'Trạng thái hoạt động',
        content: (
          <Tag
            className='text-center'
            color={tagStatusProcess[processInfo?.status as keyof typeof tagStatusProcess]?.bgColor}
            style={{ minWidth: '8.75rem', padding: '2px 8px' }}
          >
            <div
              className='text-caption-12 font-medium'
              style={{ color: tagStatusProcess[processInfo?.status as keyof typeof tagStatusProcess]?.color }}
            >
              {tagStatusProcess[processInfo?.status as keyof typeof tagStatusProcess]?.text}
            </div>
          </Tag>
        ),
        hidden: false
      },
      { label: 'Mô tả', content: processInfo?.description, hidden: false }
    ]
  }, [processInfo])

  const classifyField = useMemo(() => {
    // Sử dụng dữ liệu từ API nếu có, nếu không thì dùng dữ liệu mặc định
    const classificationData = {
      tags: processInfo?.tags || [],
      productType: processInfo?.productTypes || [],
      ObjectType: processInfo?.objectTypes || []
    }

    return generateClassificationFields(classificationData)
  }, [processInfo])

  const classifyObjectField = useMemo(() => {
    return [
      {
        label: 'Đối tượng vật lý',
        content: (
          <div
            className='cursor-pointer truncate text-primary underline'
            title={processInfo?.physicalStateTransitionName as string}
          >
            {processInfo?.physicalStateTransitionName}
          </div>
        ),
        hidden: false
      },
      {
        label: 'Đối tượng kỹ thuật số',
        content: (
          <div
            className='cursor-pointer truncate text-primary underline'
            title={processInfo?.digitalStateTransitionName as string}
          >
            {processInfo?.digitalStateTransitionName}
          </div>
        ),
        hidden: false
      },
      {
        label: 'Sản phẩm dịch vụ',
        content: (
          <div
            className='cursor-pointer truncate text-primary underline'
            title={processInfo?.serviceStateTransitionName as string}
          >
            {processInfo?.serviceStateTransitionName}
          </div>
        ),
        hidden: false
      }
    ]
  }, [processInfo])

  const handleClickEdit = () => router.push(`/workflow/process-management/edit/${id}`)

  const renderHeader = (label: string, onClick?: () => void) => (
    <div className='flex justify-between border-l-4 border-solid border-yellow-6'>
      <div className='pl-2 font-semibold'>{label}</div>
      <div className='flex items-center justify-between'>
        <div className='flex gap-1 pl-4 text-primary hover:cursor-pointer' onClick={onClick}>
          <div className='pt-1'>
            <i className='onedx-edit size-4' />
          </div>
          Chỉnh sửa
        </div>
        <Divider type='vertical' className='ml-3 mr-0 h-3' />
      </div>
    </div>
  )

  return (
    <div className='flex flex-col space-y-2'>
      {/* Thông tin chung */}
      <CustomCollapse header={renderHeader('Thông tin chung', handleClickEdit)} items={generalField} />
      <CustomCollapse header={renderHeader('Thông tin phân loại', handleClickEdit)} items={classifyField} />
      <CustomCollapse header={renderHeader('Thông tin phân loại', handleClickEdit)} items={classifyObjectField} />
    </div>
  )
}
