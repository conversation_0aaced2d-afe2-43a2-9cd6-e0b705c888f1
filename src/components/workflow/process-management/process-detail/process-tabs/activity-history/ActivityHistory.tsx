import { useCallback, useMemo, useState } from 'react'

import { useRouter, useSearchParams } from 'next/navigation'

import { useQuery } from '@tanstack/react-query'

import moment from 'moment'

import { DatePicker, Pagination, Select } from 'antd'

import { debounce } from 'lodash'

import dayjs from 'dayjs'

import { EmptyTableOrder } from '@/views/order-management/common'
import { CustomTable, pageSizeOptions, quickSearchActivityHistory, versionOptions } from '../../../common'
import { processManagement } from '@/models/workflow/processManagement'
import SettingInput from '@/components/filter/SettingInput'

const { RangePicker } = DatePicker

export const ActivityHistory = ({ draftId }: any) => {
  const searchParams = useSearchParams()

  const tabParam = searchParams.get('tab')

  const router = useRouter()

  const [page, setPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)

  const [checkedFilter, setCheckedFilter] = useState<any>({
    isModifiedBy: true,
    isDescription: true
  })

  const [rangeValue, setRangeValue] = useState()

  const [filterParams, setFilterParams] = useState<any>({})

  const { data: dataActivityHistory } = useQuery({
    queryKey: ['getProcessActivityHistory', filterParams, page, pageSize],
    queryFn: async () => {
      const res = await processManagement.getProcessActivityHistory(draftId, {
        page: page - 1,
        size: pageSize,
        sort: 'modifiedAt,desc',
        isModifiedBy: checkedFilter?.isModifiedBy ? 1 : 0,
        isDescription: checkedFilter?.isDescription ? 1 : 0,
        ...filterParams
      })

      return res?.content
    },
    enabled: !!draftId && tabParam === 'HISTORY'
  })

  const columns = [
    {
      title: <div className='text-caption-12 font-semibold text-text-neutral-medium'>STT</div>,
      key: 1,
      width: 56,
      render: (_: any, _record: any, index: number) => <div>{(page - 1) * pageSize + index + 1}</div>
    },
    {
      title: <div className='text-caption-12 font-semibold text-text-neutral-medium'>Tên tiến trình</div>,
      dataIndex: 'name',
      key: 'name',
      width: 313,
      render: (value: string) => <div className='truncate'>{value}</div>
    },
    {
      title: <div className='text-caption-12 font-semibold text-text-neutral-medium'>Phiên bản</div>,
      dataIndex: 'version',
      key: 'version',
      width: 133,
      render: (value: any, record: any) => (
        <div
          className='cursor-pointer'
          onClick={() => router.push(`/workflow/process-management/detail/${record.id}?tab=GENERAL`)}
        >
          Ver 1.{value}
        </div>
      )
    },
    {
      title: <div className='text-caption-12 font-semibold text-text-neutral-medium'>Mô tả hoạt động</div>,
      dataIndex: 'changeSummary',
      key: 'changeSummary',
      width: 313,
      render: (value: any) => <div className='truncate'>{value}</div>
    },
    {
      title: <div className='text-caption-12 font-semibold text-text-neutral-medium'>Người thực hiện</div>,
      dataIndex: 'modifiedByInfo',
      key: 'modifiedByInfo',
      width: 127,
      render: (value: any) => <div className='truncate'>{value}</div>
    },
    {
      title: <div className='text-caption-12 font-semibold text-text-neutral-medium'>Thời gian đặt hàng</div>,
      dataIndex: 'modifiedAt',
      key: 'modifiedAt',
      width: 230,
      render: (value: any) => <div>{moment(value).format('DD/MM/YYYY HH:mm')}</div>
    }
  ]

  const pagination = useMemo(
    () => ({
      position: ['none', 'bottomCenter'] as any,
      current: page,
      pageSize: pageSize,
      total: dataActivityHistory?.totalElements,
      onChange: (page: number, pageSize: number) => {
        setPage(page)
        setPageSize(pageSize)
      }
    }),
    [dataActivityHistory, page, pageSize]
  )

  const renderPagination = () => (
    <div className='mt-4 flex w-full justify-between'>
      <div />
      <Pagination {...pagination} showSizeChanger={false} />
      <Select defaultValue={pageSize} options={pageSizeOptions} onChange={value => setPageSize(value)} />
    </div>
  )

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const handleSearch = useCallback(
    debounce((value: string) => {
      setFilterParams({ ...filterParams, search: value?.replace(/%/g, '\\%').replace(/_/g, '\\_') || '' })
    }, 400),
    []
  )

  const disabledDate = (current: any) => {
    const today = dayjs().endOf('day')

    // Nếu chưa chọn ngày Từ, chỉ disable ngày lớn hơn hôm nay
    if (!rangeValue?.[0]) {
      return current && current > today
    }

    // Nếu đã chọn ngày Từ, disable ngày < ngày Từ hoặc > hôm nay
    return (current && current < dayjs(rangeValue[0]).startOf('day')) || (current && current > today)
  }

  const handleCalendarChange = (dateChange: any) => {
    setRangeValue(dateChange)

    if (!dateChange?.[0] && !dateChange?.[1]) {
      setFilterParams({ ...filterParams, startTime: undefined, endTime: undefined })
    } else {
      if (dateChange?.[0]) {
        setFilterParams({ ...filterParams, startTime: dateChange[0].format('DD/MM/YYYY') })
      }

      if (dateChange?.[1]) {
        setFilterParams({ ...filterParams, endTime: dateChange[1].format('DD/MM/YYYY') })
      }
    }
  }

  return (
    <div className='bg-white p-4'>
      <div className='mb-4 flex items-center gap-3'>
        <SettingInput
          placeholder='Tìm kiếm theo tên tiến trình, mã định danh'
          styles={{ width: '100%' }}
          checked={checkedFilter}
          setChecked={setCheckedFilter}
          onKeyDown={event => {
            if (event.key === 'Enter') {
              event.preventDefault()
              handleSearch(event.currentTarget.value)
              setPage(1)
            }
          }}
          checkBoxOptions={quickSearchActivityHistory}
          size='small'
        />
        <Select
          placeholder={<div className='text-text-neutral-light'>Phiên bản</div>}
          options={versionOptions}
          onChange={value => setFilterParams({ ...filterParams, version: value })}
          notFoundContent='Không có dữ liệu tìm kiếm'
          className='h-9 w-[123px] shrink-0'
          style={{ height: 36 }}
        />
        <RangePicker
          format='DD-MM-YYYY'
          size='small'
          placeholder={['Từ', 'Đến']}
          onCalendarChange={handleCalendarChange}
          allowEmpty={[true, true]}
          value={rangeValue}
          disabledDate={disabledDate}
          className='w-[170px]'
          style={{ height: 36 }}
        />
      </div>
      <CustomTable
        columns={columns}
        dataSource={dataActivityHistory}
        locale={{
          emptyText: <EmptyTableOrder emptyText='Không có dữ liệu tiến trình' />
        }}
        scroll={{ x: '1200' }}
        pagination={false}
      />
      {renderPagination()}
    </div>
  )
}
