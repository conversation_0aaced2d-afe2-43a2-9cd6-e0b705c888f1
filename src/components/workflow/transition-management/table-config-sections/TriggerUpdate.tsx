'use client'

import React, { useState } from 'react'

import { PlusOutlined } from '@ant-design/icons'
import { Button, Form, Input, Select } from 'antd'

import { RequiredDotLabel } from '@/components/label/RequiredDotLabel'
import { SectionHeader, NestedSectionHeader } from '../../shared/section-header'

export const TriggerUpdate: React.FC = () => {
  const [mainCollapsed, setMainCollapsed] = useState(false)
  const [manualCollapsed, setManualCollapsed] = useState(false)
  const [apiCollapsed, setApiCollapsed] = useState(false)

  return (
    <div className='mb-5 rounded bg-gray-alpha-1'>
      <SectionHeader
        title='Trigger cập nhật'
        collapsed={mainCollapsed}
        onToggle={() => setMainCollapsed(!mainCollapsed)}
      />

      {!mainCollapsed && (
        <div className='p-4'>
          {/* Section con - Manual */}
          <div className='mb-4 overflow-hidden rounded-lg'>
            <NestedSectionHeader
              title='Manual'
              collapsed={manualCollapsed}
              onToggle={() => setManualCollapsed(!manualCollapsed)}
              showDelete={true}
              onDelete={() => console.log('Delete manual section')}
              isChecked={true}
              onCheckChange={checked => console.log('Manual checked:', checked)}
            />

            {!manualCollapsed && (
              <div className='bg-gray-alpha-1 p-4'>
                <Form.Item
                  name='roles'
                  label={<RequiredDotLabel label='Vai trò/Tác nhân được cập nhật trạng thái' />}
                  rules={[{ required: true, message: 'Vai trò/Tác nhân được cập nhật trạng thái không được bỏ trống' }]}
                >
                  <Select
                    mode='multiple'
                    placeholder='Chọn vai trò/tác nhân'
                    options={[
                      { value: 'admin', label: 'Admin' },
                      { value: 'partner', label: 'Đối tác' }
                    ]}
                  />
                </Form.Item>
              </div>
            )}
          </div>

          {/* Section con - API_call */}
          <div className='mb-4 overflow-hidden rounded-lg'>
            <NestedSectionHeader
              title='API'
              collapsed={apiCollapsed}
              onToggle={() => setApiCollapsed(!apiCollapsed)}
              showDelete={true}
              onDelete={() => console.log('Delete API section')}
              isChecked={true}
              onCheckChange={checked => console.log('API checked:', checked)}
            />

            {!apiCollapsed && (
              <div className='bg-gray-alpha-1 p-4'>
                <Form.Item
                  name='apiCall'
                  label={<RequiredDotLabel label='API Call' />}
                  rules={[{ required: true, message: 'API Call không được bỏ trống' }]}
                >
                  <Select
                    placeholder='Chọn API đã tích hợp'
                    options={[
                      { value: 'shipping_api', label: 'API Vận chuyển' },
                      { value: 'payment_api', label: 'API Thanh toán' },
                      { value: 'inventory_api', label: 'API Kho hàng' }
                    ]}
                  />
                </Form.Item>
                <Form.Item name='apiEndpoint' label={<RequiredDotLabel label='URL Endpoint' />}>
                  <Input placeholder='https://api.example.com/webhook' disabled />
                </Form.Item>
              </div>
            )}
          </div>

          {/* Nút thêm trigger */}
          <Button type='link' icon={<PlusOutlined />}>
            Thêm trigger cập nhật
          </Button>
        </div>
      )}
    </div>
  )
}
