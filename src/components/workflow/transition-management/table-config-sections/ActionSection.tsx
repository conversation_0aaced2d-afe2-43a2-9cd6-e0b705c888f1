'use client'

import { Form, Input, Select, Radio, Button } from 'antd'

import { RequiredDotLabel } from '@/components/label/RequiredDotLabel'
import { SectionHeader } from '../../shared/section-header/SectionHeader'

const { TextArea } = Input

interface ActionSectionProps {
  collapsed: boolean
  onToggle: () => void
  actionType: string
  authType: string
  handleActionTypeChange: (value: string) => void
  handleAuthTypeChange: (value: string) => void
}

export const ActionSection: React.FC<ActionSectionProps> = ({
  collapsed,
  onToggle,
  actionType,
  authType,
  handleActionTypeChange,
  handleAuthTypeChange
}) => {
  return (
    <div className='mb-5 rounded bg-gray-alpha-1'>
      <SectionHeader title='Hành động sau khi chuyển đổi trạng thái' collapsed={collapsed} onToggle={onToggle} />

      {!collapsed && (
        <div className='p-4'>
          <Form.Item name='actionType' label='Chọn hành động' initialValue='webhook'>
            <Select
              placeholder='Chọn hành động'
              onChange={handleActionTypeChange}
              options={[
                { value: 'webhook', label: 'Gửi webhook đến hệ thống khác' },
                { value: 'notification', label: 'Gửi thông báo' },
                { value: 'task', label: 'Tạo task mới' }
              ]}
            />
          </Form.Item>

          {actionType === 'webhook' && (
            <>
              <Form.Item
                name='webhookLink'
                label={<RequiredDotLabel label='Link webhook' />}
                rules={[
                  { required: true, message: 'Tên webhook không được bỏ trống' },
                  { max: 200, message: 'Độ dài tối đa là 200 ký tự' }
                ]}
              >
                <Input placeholder='Nhập link webhook nhận dữ liệu khi có event' />
              </Form.Item>

              <Form.Item name='authType' label={<RequiredDotLabel label='Kiểu Auth' />} initialValue='key'>
                <Select
                  placeholder='Chọn kiểu xác thực'
                  onChange={handleAuthTypeChange}
                  options={[
                    { value: 'key', label: 'Key' },
                    { value: 'basic', label: 'Basic Auth' }
                  ]}
                />
              </Form.Item>

              {authType === 'key' && (
                <Form.Item name='secretKey' label='Secret key'>
                  <Input.Password placeholder='********' disabled addonAfter={<Button size='small'>Reset</Button>} />
                </Form.Item>
              )}

              {authType === 'basic' && (
                <Form.Item
                  name='basicAuth'
                  label={<RequiredDotLabel label='username:password' />}
                  rules={[{ required: true, message: 'Trường dữ liệu không được bỏ trống' }]}
                >
                  <Input placeholder='username:password' />
                </Form.Item>
              )}
            </>
          )}

          {actionType === 'notification' && (
            <>
              <Form.Item
                name='notificationType'
                label={<RequiredDotLabel label='Loại thông báo' />}
                rules={[{ required: true, message: 'Vui lòng chọn loại thông báo' }]}
              >
                <Radio.Group>
                  <Radio value='email'>Email</Radio>
                  <Radio value='push'>Push Notification</Radio>
                </Radio.Group>
              </Form.Item>

              <Form.Item
                name='notificationTemplate'
                label={<RequiredDotLabel label='Mẫu thông báo' />}
                rules={[{ required: true, message: 'Vui lòng chọn mẫu thông báo' }]}
              >
                <Select
                  placeholder='Chọn mẫu thông báo'
                  options={[
                    { value: 'order_confirm', label: 'Xác nhận đơn hàng' },
                    { value: 'shipping_notice', label: 'Thông báo giao hàng' },
                    { value: 'custom', label: 'Tùy chỉnh' }
                  ]}
                />
              </Form.Item>

              <Form.Item name='notificationContent' label='Nội dung thông báo'>
                <TextArea rows={4} placeholder='Nhập nội dung thông báo' />
              </Form.Item>
            </>
          )}

          {actionType === 'task' && (
            <>
              <Form.Item
                name='taskName'
                label={<RequiredDotLabel label='Tên task' />}
                rules={[{ required: true, message: 'Vui lòng nhập tên task' }]}
              >
                <Input placeholder='Nhập tên task' />
              </Form.Item>

              <Form.Item
                name='taskAssignee'
                label={<RequiredDotLabel label='Người thực hiện' />}
                rules={[{ required: true, message: 'Vui lòng chọn người thực hiện' }]}
              >
                <Select
                  placeholder='Chọn người thực hiện'
                  options={[
                    { value: 'user1', label: 'Người dùng 1' },
                    { value: 'user2', label: 'Người dùng 2' },
                    { value: 'user3', label: 'Người dùng 3' }
                  ]}
                />
              </Form.Item>

              <Form.Item name='taskDescription' label='Mô tả task'>
                <TextArea rows={4} placeholder='Nhập mô tả task' />
              </Form.Item>
            </>
          )}
        </div>
      )}
    </div>
  )
}
