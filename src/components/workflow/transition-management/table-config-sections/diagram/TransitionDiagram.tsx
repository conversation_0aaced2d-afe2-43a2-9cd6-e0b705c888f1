'use client'
import { useCallback, useRef, useState, useEffect, useMemo } from 'react'

import { useParams } from 'next/navigation'

import type { Edge, Node } from '@xyflow/react'
import {
  addEdge,
  Background,
  BackgroundVariant,
  Controls,
  ReactFlow,
  ReactFlowProvider,
  useEdgesState,
  useNodesState,
  useReactFlow
} from '@xyflow/react'
import '@xyflow/react/dist/style.css'

import { toPng } from 'html-to-image'

import type { TransitionDiagramProps } from '@/types/workflow/transition'
import { CustomNode, CustomEdge, DiagramActions, DiagramToolbar } from './component'
import { calculateGridLayout, calculateNodePositions, convertTableDataToDiagram } from '@/utils/workflow'

const nodeTypes = { custom: CustomNode }
const edgeTypes = { custom: CustomEdge }

// Loại bỏ fake data - sử dụng data động từ props

const Flow = ({
  nodes: customNodes,
  edges: customEdges,
  stateTableData,
  onNodeClick,
  onEdgeClick,
  onCreateState,
  onCreateTransition,
  isCollapsed,
  layoutType = 'horizontal',
  gridColumns = 3
}: TransitionDiagramProps) => {
  // Lấy id trạng thái
  const { id } = useParams()
  // const pathname = usePathname()
  const containerRef = useRef<HTMLDivElement>(null)
  // Lấy instance React Flow để điều chỉnh viewport
  const { fitView, getViewport, setViewport } = useReactFlow()

  // Kiểm tra xem có phải trang detail không
  // const isDetailPage = pathname?.includes('/detail/')
  const [containerSize, setContainerSize] = useState({ width: 1200, height: 500 })

  // Tính toán vị trí nodes dựa vào layout type
  const calculatePositions = useCallback(
    (nodes: Node[]) => {
      if (layoutType === 'grid') {
        return calculateGridLayout(nodes, containerSize.width, gridColumns || 3)
      }

      return calculateNodePositions(nodes, containerSize.width)
    },
    [layoutType, containerSize.width, gridColumns]
  )

  // Chuyển đổi dữ liệu từ API hoặc sử dụng dữ liệu từ props
  const getDiagramData = useCallback(() => {
    if (stateTableData && stateTableData.length > 0) {
      return convertTableDataToDiagram(stateTableData, layoutType || 'horizontal', containerSize.width, gridColumns)
    }

    // Sử dụng data từ props thay vì fake data
    return {
      nodes: customNodes || [],
      edges: customEdges || []
    }
  }, [stateTableData, layoutType, containerSize.width, gridColumns, customNodes, customEdges])

  // Khởi tạo với dữ liệu từ props
  const initialDiagramData = getDiagramData()

  const [nodes, setNodes, onNodesChange] = useNodesState(calculatePositions(initialDiagramData.nodes))
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialDiagramData.edges)

  // Cập nhật kích thước container
  useEffect(() => {
    const updateSize = () => {
      if (containerRef.current) {
        setContainerSize({
          width: containerRef.current.offsetWidth,
          height: containerRef.current.offsetHeight
        })
      }
    }

    updateSize()
    window.addEventListener('resize', updateSize)

    return () => window.removeEventListener('resize', updateSize)
  }, [])

  // Cập nhật nodes và edges khi dữ liệu thay đổi
  useEffect(() => {
    const newDiagramData = getDiagramData()

    setNodes(calculatePositions(newDiagramData.nodes))
    setEdges(newDiagramData.edges)
  }, [getDiagramData, calculatePositions, setNodes, setEdges])

  const onConnect = useCallback((params: Edge | any) => setEdges(eds => addEdge(params, eds)), [setEdges])

  // Hàm tải sơ đồ ảnh, tự động phóng to để hiển thị toàn bộ nodes trước khi chụp
  const handleDownload = async () => {
    if (!containerRef.current) return

    // Lưu viewport hiện tại
    const prevViewport = getViewport()

    // Lưu display của các phần tử nền để khôi phục sau
    let prevDisplay: string[] = []

    try {
      // Ẩn nền chấm và phóng to toàn bộ sơ đồ
      // Ẩn phần tử background chấm
      const bgEls: HTMLElement[] = Array.from(
        containerRef.current.querySelectorAll('.react-flow__background')
      ) as HTMLElement[]

      prevDisplay = bgEls.map(el => el.style.display)

      bgEls.forEach(el => {
        el.style.display = 'none'
      })

      // Phóng to để bao quát toàn bộ sơ đồ
      fitView({ padding: 0.1 })

      // Đợi 2 frame để React Flow render xong
      await new Promise(resolve => requestAnimationFrame(() => requestAnimationFrame(resolve)))

      const dataUrl = await toPng(containerRef.current, { cacheBust: true, pixelRatio: 2 })
      const link = document.createElement('a')

      link.download = 'transition-diagram.png'
      link.href = dataUrl
      link.click()
    } catch (err) {
      console.error('Failed to download diagram:', err)
    } finally {
      // Khôi phục viewport cũ
      // Khôi phục background chấm
      const bgElsRestore: HTMLElement[] = Array.from(
        containerRef.current.querySelectorAll('.react-flow__background')
      ) as HTMLElement[]

      bgElsRestore.forEach((el, idx) => {
        el.style.display = prevDisplay[idx] || ''
      })

      if (prevViewport) {
        setViewport(prevViewport)
      }
    }
  }

  const [showTransitions, setShowTransitions] = useState(true)

  // Cập nhật edges với state checkbox Ẩn/Hiển thị chuyển đổi
  const edgesWithTransitionState = useMemo(() => {
    return edges.map(edge => ({
      ...edge,
      data: {
        ...edge.data,
        showTransitions
      }
    }))
  }, [edges, showTransitions])

  return (
    <div
      ref={containerRef}
      className={`relative ${isCollapsed ? 'h-[calc(100vh-200px)]' : 'h-[500px]'} w-full rounded-lg border bg-gray-50`}
    >
      <div className='absolute left-4 top-4 z-10 flex items-center gap-4'>
        <DiagramToolbar
          onDownload={handleDownload}
          showTransitions={showTransitions}
          onShowTransitionsChange={setShowTransitions}
        />
      </div>
      {/* Button Tạo Trạng thái và Tạo chuyển đổi hiển thị khi ở màn hình Tạo */}
      {!id && onCreateState && onCreateTransition && (
        <DiagramActions onCreateState={onCreateState} onCreateTransition={onCreateTransition} />
      )}
      <ReactFlow
        nodes={nodes}
        edges={edgesWithTransitionState}
        nodeTypes={nodeTypes}
        edgeTypes={edgeTypes}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onNodeClick={onNodeClick ? (_, node) => onNodeClick(node) : undefined}
        onEdgeClick={onEdgeClick ? (_, edge) => onEdgeClick(edge) : undefined}
        defaultViewport={{ x: 0, y: 0, zoom: 1 }}
        minZoom={0.1} // min 10%
        maxZoom={1} // max 100%
        proOptions={{ hideAttribution: true }}
      >
        <Controls showInteractive={false} />
        <Background variant={BackgroundVariant.Dots} gap={12} size={1} />
      </ReactFlow>
    </div>
  )
}

export const TransitionDiagram = (props: TransitionDiagramProps) => {
  return (
    <ReactFlowProvider>
      <Flow {...props} />
    </ReactFlowProvider>
  )
}
