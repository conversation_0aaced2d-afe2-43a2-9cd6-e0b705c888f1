'use client'

import { useState } from 'react'

import type { EdgeProps } from '@xyflow/react'
import { getBezierPath } from '@xyflow/react'
import classNames from 'classnames'

// Định nghĩa interface cho transition item
interface TransitionItem {
  key: string
  label?: string
  type?: string
  color?: string
  [key: string]: any
}

// Định nghĩa interface cho edge data
interface CustomEdgeData {
  label?: string
  color?: string
  transitions?: TransitionItem[]
  [key: string]: any
}

interface CustomEdgeProps extends Omit<EdgeProps, 'data'> {
  data?: CustomEdgeData & {
    showTransitions?: boolean
  }
}

export const CustomEdge = ({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  data
}: CustomEdgeProps) => {
  const [visible, setVisible] = useState(false)

  const [edgePath, labelX, labelY] = getBezierPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition
  })

  const handleToggle = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setVisible(prev => !prev)
  }

  return (
    <>
      <path
        id={id}
        className='react-flow__edge-path'
        d={edgePath}
        strokeWidth={2}
        stroke='#6B7280'
        fill='none'
        markerEnd='url(#react-flow__arrowclosed)'
      />
      {data?.showTransitions && (
        <foreignObject
          width={130}
          height={150}
          x={labelX - 65}
          y={labelY - 15}
          className='overflow-visible'
          style={{ pointerEvents: 'none' }}
        >
          <div style={{ pointerEvents: 'auto' }}>
            <div className='relative rounded-md border border-solid border-border-neutral-light bg-white'>
              <div className='flex items-center justify-center gap-2 py-2 pl-3 pr-2'>
                <div className='caption-12-medium text-text-neutral-light'>{data?.label || ''}</div>
                <div
                  style={{ backgroundColor: data?.color || '#2A6AEB' }}
                  className={classNames('size-4 rounded-full flex items-center justify-center text-white')}
                >
                  <i className='onedx-add size-3 text-white' />
                </div>
              </div>
              {visible && (
                <div className='caption-12-regular mb-1 space-y-1.5 border-t border-solid border-border-neutral-light px-3 py-2 text-text-neutral-light'>
                  {data?.transitions?.map((item: TransitionItem) => (
                    <div
                      key={item.key}
                      className={classNames('rounded-lg px-2 py-0.5 text-white')}
                      style={{ backgroundColor: data?.color || '#2A6AEB' }}
                    >
                      {item.label}
                    </div>
                  ))}
                </div>
              )}
            </div>
            <div className='relative z-10 mt-[-8px] flex items-center justify-center'>
              <div
                onClick={handleToggle}
                className='flex size-4 cursor-pointer items-center justify-center rounded-full border border-solid border-border-neutral-light bg-white transition-colors hover:bg-gray-50'
              >
                <i
                  className={classNames(
                    visible ? 'onedx-chevron-up' : 'onedx-chevron-down',
                    'size-4 text-icon-neutral-medium'
                  )}
                />
              </div>
            </div>
          </div>
        </foreignObject>
      )}
    </>
  )
}
