'use client'

import { memo } from 'react'

import { <PERSON><PERSON>, Position } from '@xyflow/react'
import classNames from 'classnames'

interface CustomNodeProps {
  data: {
    label: string
    icon?: React.ReactNode
    color?: string
  }
}

export const CustomNode = memo(({ data }: CustomNodeProps) => {
  return (
    <div
      style={{ backgroundColor: data?.color || '#2A6AEB' }}
      className={classNames('flex w-[180px] items-center justify-center gap-2 rounded-lg px-4 py-2 shadow-sm')}
    >
      <Handle type='target' position={Position.Left} className='!bg-gray-8' />

      <div className='truncate text-center text-sm font-medium text-white'>{data.label}</div>

      <Handle type='source' position={Position.Right} className='!bg-gray-8' />
    </div>
  )
})
