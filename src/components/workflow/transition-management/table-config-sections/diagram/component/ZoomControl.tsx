'use client'

import { useRef, useState, useEffect } from 'react'

import { Button, InputNumber } from 'antd'
import { useReactFlow, useStore } from '@xyflow/react'

export const ZoomControl = () => {
  const { zoomIn, zoomOut, getZoom, setViewport } = useReactFlow()
  const [zoomLevel, setZoomLevel] = useState(100)
  const inputRef = useRef<any>(null)

  // Đồng bộ zoomLevel khi thay đổi transform
  const currentZoom = useStore(state => state.transform[2])

  useEffect(() => {
    setZoomLevel(Math.round(currentZoom * 100))
  }, [currentZoom])

  const handleZoomIn = () => {
    zoomIn()
    setTimeout(() => setZoomLevel(Math.round(getZoom() * 100)), 0)
  }

  const handleZoomOut = () => {
    zoomOut()
    setTimeout(() => setZoomLevel(Math.round(getZoom() * 100)), 0)
  }

  const handleZoomChange = (value: number) => {
    setZoomLevel(value)

    // Lấy viewport hiện tại để giữ nguyên x/y
    const svg = document.querySelector('.react-flow__viewport') as SVGSVGElement | null
    let x = 0,
      y = 0

    if (svg) {
      const transform = svg.getAttribute('transform')

      if (transform) {
        const match = /translate\(([-\d.]+),\s*([\-\d.]+)\)/.exec(transform)

        if (match) {
          x = parseFloat(match[1])
          y = parseFloat(match[2])
        }
      }
    }

    setViewport({ x, y, zoom: value / 100 })
  }

  // Chặn xóa text % và chỉ cho nhập số
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === '%' || e.key === 'e' || e.key === 'E' || e.key === '+' || e.key === '-') {
      e.preventDefault()
    }

    // Không cho xóa hết text
    if ((e.key === 'Backspace' || e.key === 'Delete') && String(zoomLevel).length <= 1) {
      e.preventDefault()
    }
  }

  // Luôn giữ ký tự % khi blur
  const handleBlur = () => {
    if (inputRef.current) {
      const val = inputRef.current.value

      if (!val.endsWith('%')) {
        inputRef.current.value = val.replace(/[^0-9]/g, '') + '%'
      }
    }
  }

  return (
    <div className='flex items-center'>
      <Button
        type='default'
        className='flex size-8 items-center justify-center rounded-r-none border-r-0 !bg-white !p-0'
        onClick={handleZoomIn}
        icon={<i className='onedx-plus size-4' style={{ color: '#5E7699' }} />}
      />
      <InputNumber
        min={10}
        max={100}
        value={zoomLevel}
        formatter={value => `${value}%`}
        parser={value => Number(value?.replace('%', ''))}
        onChange={value => handleZoomChange(Number(value))}
        controls={false}
        className='flex h-8 w-[60px] items-center justify-center rounded-none border-x-0 !bg-white !p-0 text-center'
        style={{
          textAlign: 'center',
          height: 28,
          color: '#5E7699',
          fontWeight: 500,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}
        ref={inputRef}
        onKeyDown={handleKeyDown}
        onBlur={handleBlur}
      />
      <Button
        type='default'
        className='flex size-8 items-center justify-center rounded-l-none border-l-0 !bg-white !p-0'
        onClick={handleZoomOut}
        icon={<i className='onedx-minus size-4' style={{ color: '#5E7699' }} />}
      />
    </div>
  )
}
