'use client'

import React, { useState, useRef } from 'react'

import { Form, message, Tabs } from 'antd'
import { styled } from 'styled-components'

import type { TableConfigDrawerProps, TableConfigFormData, TransitionStateTableItem } from '@/types/workflow/transition'
import { CustomDrawer } from '../../../shared/drawer'
import type { TriggerUpdateRef } from './component'
import { ActionSection, GeneralSection, PreviousStateSection, TriggerUpdate } from './component'
import { mapDataConfigCreate, mapApiToDataTable } from '@/utils/workflow/transition'

const CustomTabs = styled(Tabs)`
  &.ant-tabs-card.ant-tabs-top .ant-tabs-nav .ant-tabs-tab-active {
    border-block-end-color: #2a6aeb !important;
  }
  &.ant-tabs .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
    color: #2a6aeb !important;
  }
`

export const TableConfigDrawer: React.FC<TableConfigDrawerProps> = ({
  open,
  onClose,
  maskClosable = true,
  selectedObject,
  tableStates = []
}) => {
  const [form] = Form.useForm<TableConfigFormData>()
  const [loading, setLoading] = useState(false)
  const [isNewState, setIsNewState] = useState<boolean>(false)
  const [actionType, setActionType] = useState<string>('')
  const [authType, setAuthType] = useState<string>('key')
  const [messageApi, contextHolder] = message.useMessage()
  // Theo dõi thay đổi của stateId để truyền xuống PreviousStateSection
  const currentStateId = Form.useWatch('stateId', form)

  // States để quản lý collapse của từng section
  const [generalCollapsed, setGeneralCollapsed] = useState<boolean>(false)
  const [previousStateCollapsed, setPreviousStateCollapsed] = useState<boolean>(false)
  const [actionCollapsed, setActionCollapsed] = useState<boolean>(false)
  const [selectedPreviousStates, setSelectedPreviousStates] = useState<{ id: string; name: string }[]>([])

  // State để quản lý collapse cho từng trạng thái tiền nhiệm
  const [stateCollapseMap, setStateCollapseMap] = useState<Record<string, boolean>>({})

  // Refs cho các component TriggerUpdate
  const singleTriggerRef = useRef<TriggerUpdateRef>(null)
  const tabTriggerRefs = useRef<Record<string, any>>({})

  // Khởi tạo refs cho các tab
  const initTabTriggerRef = (stateId: string) => {
    if (!tabTriggerRefs.current[stateId]) {
      const newRef = React.createRef<TriggerUpdateRef>()

      tabTriggerRefs.current[stateId] = newRef
    }

    return tabTriggerRefs.current[stateId]
  }

  // Hàm reset toàn bộ form/state khi đóng
  const resetAll = () => {
    try {
      singleTriggerRef.current?.reset?.()
      Object.values(tabTriggerRefs.current).forEach(ref => ref?.current?.reset?.())
    } catch {}

    try {
      form.resetFields()
    } catch {}

    setSelectedPreviousStates([])
    setGeneralCollapsed(false)
    setPreviousStateCollapsed(false)
    setActionCollapsed(false)
    setIsNewState(false)
    setActionType('')
    setAuthType('key')
    setStateCollapseMap({})
    tabTriggerRefs.current = {}
  }

  // Hàm xử lý toggle collapse cho một trạng thái cụ thể
  const handleStateToggle = (stateId: string) => {
    setStateCollapseMap(prev => ({
      ...prev,
      [stateId]: !prev[stateId]
    }))
  }

  // Xử lý khi thay đổi loại hành động
  const handleActionTypeChange = (value: string) => {
    setActionType(value)

    // Khi clear, reset các field cấu hình liên quan để tránh giữ dữ liệu cũ
    if (!value) {
      try {
        ;(form as any).setFieldsValue({ notifications: undefined, webhook: undefined })
      } catch {}
    }
  }

  // Xử lý khi thay đổi loại xác thực
  const handleAuthTypeChange = (value: string) => {
    setAuthType(value)
  }

  // Xử lý khi thay đổi tên trạng thái
  const handleStateNameChange = (value: string) => {
    // Phân biệt giữa trạng thái có sẵn và trạng thái mới

    if (value === 'existing_state') {
      setIsNewState(false) // Trạng thái có sẵn
    } else if (value === 'new_state') {
      setIsNewState(true) // Trạng thái mới
    } else if (value === '') {
      // Khi xóa tên trạng thái, reset stateId về null
      form.setFieldValue('stateId', null)
      setIsNewState(true)
    } else {
      // Nếu là tên thực tế, kiểm tra xem có phải trạng thái mới không
      const formValues = form.getFieldsValue()

      // Kiểm tra xem có phải trạng thái mới không (dựa vào việc có stateId hay không)
      const hasStateId = formValues.stateId !== undefined && formValues.stateId !== null && formValues.stateId !== ''

      setIsNewState(!hasStateId)
    }
  }

  // Hàm để lấy tất cả giá trị mapping từ các component ScheduleSync
  const getAllScheduleSyncValues = () => {
    // Lấy giá trị từ TriggerUpdate đơn lẻ nếu chỉ có 1 trạng thái
    if (selectedPreviousStates.length <= 1) {
      return singleTriggerRef.current?.getScheduleSyncValues()
    }

    // Lấy giá trị từ tất cả các tab nếu có nhiều trạng thái
    const allValues: Record<string, any> = {}

    Object.keys(tabTriggerRefs.current).forEach(stateId => {
      const ref = tabTriggerRefs.current[stateId]

      if (ref.current) {
        allValues[stateId] = ref.current.getScheduleSyncValues()
      }
    })

    return allValues
  }

  // Hàm xử lý khi submit form - đã được tối ưu hóa
  const handleSubmit = async () => {
    try {
      setLoading(true)

      // Lấy giá trị form
      const formValues = await form.validateFields()

      // Lấy giá trị mapping từ ScheduleSync
      const scheduleSyncValues = getAllScheduleSyncValues()

      // Gọi hàm mapping dữ liệu từ utils
      const mappedData = mapDataConfigCreate(
        formValues,
        scheduleSyncValues,
        selectedPreviousStates,
        singleTriggerRef as React.RefObject<TriggerUpdateRef>,
        tabTriggerRefs
      )

      // Chuẩn hóa theo format dataTable chuẩn
      const rows: TransitionStateTableItem[] = mapApiToDataTable(mappedData, formValues)

      messageApi.success('Tạo cấu hình thành công!')

      // Reset trước khi đóng
      resetAll()

      // Trả dữ liệu ra ngoài để hiển thị bảng và đóng Drawer
      onClose(true, rows, mappedData)
    } catch (error) {
      console.error('Validation failed:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    resetAll()
    onClose()
  }

  return (
    <>
      {contextHolder}
      <CustomDrawer
        title='Tạo cấu hình dạng bảng'
        icon={<i className='onedx-table size-6 text-icon-info-strong' />}
        tooltip={!selectedObject ? 'Không thể tạo cấu hình khi chưa chọn đối tượng' : undefined}
        width={980}
        open={open}
        onCancel={handleCancel}
        onSubmit={handleSubmit}
        maskClosable={maskClosable}
        loading={loading}
        disabled={false}
        cancelText='Đóng'
        submitText='Xác nhận'
      >
        <Form
          form={form}
          layout='vertical'
          requiredMark={false}
          className='transition-table-config-form'
          onFinish={values => {
            console.log('Tất cả giá trị:', values)
          }}
        >
          {/* Phần Thiết lập thông tin chung */}
          <GeneralSection
            collapsed={generalCollapsed}
            onToggle={() => setGeneralCollapsed(!generalCollapsed)}
            handleStateNameChange={handleStateNameChange}
            messageApi={messageApi}
            isNewState={isNewState}
            tableStates={tableStates}
            disableNameValidation={true}
          />

          {/* Phần Trạng thái tiền nhiệm chuyển đến */}
          <PreviousStateSection
            collapsed={previousStateCollapsed}
            onToggle={() => setPreviousStateCollapsed(!previousStateCollapsed)}
            onStateChange={setSelectedPreviousStates}
            idsIgnore={currentStateId ? [String(currentStateId)] : []}
          />

          {/* Hiển thị tabs nếu có nhiều hơn 1 trạng thái tiền nhiệm */}
          {selectedPreviousStates.length > 1 ? (
            <CustomTabs
              type='card'
              items={selectedPreviousStates?.map(state => ({
                key: state.id,
                label: state.name,
                children: (
                  <>
                    {/* Component Trigger cập nhật */}
                    <TriggerUpdate ref={initTabTriggerRef(state.id)} selectedStates={[state]} />

                    {/* Phần Hành động sau khi chuyển đổi trạng thái */}
                    <ActionSection
                      collapsed={stateCollapseMap[state.id] ?? false}
                      onToggle={() => handleStateToggle(state.id)}
                      actionType={actionType}
                      authType={authType}
                      handleActionTypeChange={handleActionTypeChange}
                      handleAuthTypeChange={handleAuthTypeChange}
                    />
                  </>
                )
              }))}
            />
          ) : (
            // Hiển thị trực tiếp nếu chỉ có 1 trạng thái
            <>
              {/* Component Trigger cập nhật */}
              <TriggerUpdate ref={singleTriggerRef} selectedStates={selectedPreviousStates} />

              {/* Phần Hành động sau khi chuyển đổi trạng thái */}
              <ActionSection
                collapsed={actionCollapsed}
                onToggle={() => setActionCollapsed(!actionCollapsed)}
                actionType={actionType}
                authType={authType}
                handleActionTypeChange={handleActionTypeChange}
                handleAuthTypeChange={handleAuthTypeChange}
              />
            </>
          )}
        </Form>
      </CustomDrawer>
    </>
  )
}
