'use client'

import React, { useState, useEffect } from 'react'

import { PlusOutlined } from '@ant-design/icons'
import { Button, Select, Tooltip } from 'antd'

import { OPERATORS } from '@/constants/workflow/trigger'
import { useStateTransitionManagement } from '@/hooks/workflow/useStateTransitionManagement'

const EQUALS_ONLY = OPERATORS.filter(op => op.value === 1)

const getOperatorOptions = (field?: string) => {
  if (field === 'category' || field === 'ID') {
    return OPERATORS
  }

  return EQUALS_ONLY
}

interface ConditionItem {
  id: string
  field: string
  operator: number
  value: string | number | (string | number)[]
}

export interface ConditionGroup {
  id: string
  conditions: ConditionItem[]
}

interface ConditionConfigProps {
  conditions: Array<{ value: string; label: string }>
  conditionValues: Record<string, Array<{ value: string | number; label: string }>>
  value?: ConditionGroup[]
  onChange?: (groups: ConditionGroup[]) => void
}

export const ConditionConfig: React.FC<ConditionConfigProps> = ({ conditions, conditionValues, value, onChange }) => {
  const { comboboxCategory } = useStateTransitionManagement()

  // Cập nhật conditionValues khi có dữ liệu danh mục
  useEffect(() => {
    if (comboboxCategory?.length > 0) {
      // Cập nhật cả 'category' và 'ID' để đảm bảo tương thích
      conditionValues.ID = comboboxCategory.map((cat: { id: number; name: string }) => ({
        value: cat.id,
        label: cat.name
      }))
    }
  }, [comboboxCategory, conditionValues])

  const [groups, setGroups] = useState<ConditionGroup[]>(
    value || [{ id: '1', conditions: [{ id: '1', field: '', operator: 1, value: [] }] }]
  )

  // Cập nhật state khi value prop thay đổi
  useEffect(() => {
    if (value) {
      setGroups(value)
    }
  }, [value])

  // Gọi onChange khi groups thay đổi
  useEffect(() => {
    onChange?.(groups)
  }, [groups, onChange])

  // Thêm một điều kiện mới vào nhóm
  const addCondition = (groupId: string) => {
    const newId = Date.now().toString()

    const newCondition: ConditionItem = {
      id: newId,
      field: '',
      operator: 1,
      value: []
    }

    setGroups(prevGroups =>
      prevGroups.map(group =>
        group.id === groupId ? { ...group, conditions: [...group.conditions, newCondition] } : group
      )
    )
  }

  // Thêm một nhóm điều kiện mới (OR)
  const addConditionGroup = () => {
    const newId = Date.now().toString()

    const newGroup = {
      id: newId,
      conditions: [{ id: newId + '-1', field: '', operator: 1, value: [] }]
    }

    setGroups(prevGroups => [...prevGroups, newGroup])
  }

  // Xóa một điều kiện
  const removeCondition = (groupId: string, conditionId: string) => {
    setGroups(
      prevGroups =>
        prevGroups
          .map(group => {
            if (group.id === groupId) {
              // Nếu đây là điều kiện cuối cùng trong nhóm, xóa cả nhóm
              if (group.conditions.length === 1) {
                return null
              }

              // Ngược lại, chỉ xóa điều kiện đó
              return {
                ...group,
                conditions: group.conditions.filter(c => c.id !== conditionId)
              }
            }

            return group
          })
          .filter(Boolean) as { id: string; conditions: ConditionItem[] }[]
    )
  }

  // Cập nhật giá trị của một điều kiện
  const updateCondition = (
    groupId: string,
    conditionId: string,
    field: keyof ConditionItem,
    value: string | number | (string | number)[]
  ) => {
    setGroups(prevGroups =>
      prevGroups.map(group => {
        if (group.id === groupId) {
          return {
            ...group,
            conditions: group.conditions.map(condition => {
              if (condition.id === conditionId) {
                // Nếu thay đổi field, reset value
                if (field === 'field') {
                  const nextField = value as string
                  const isCategory = nextField === 'category' || nextField === 'ID'

                  return {
                    ...condition,
                    field: nextField,
                    // Nếu đổi sang field không phải 'category'/'ID' mà operator đang là 'not_equals' thì ép về 'equals'
                    operator: !isCategory && condition.operator !== 1 ? 1 : condition.operator,
                    value: [] // Reset value thành array rỗng
                  }
                }

                // Xử lý đúng kiểu dữ liệu cho từng trường
                if (field === 'operator') {
                  return { ...condition, [field]: value as number }
                }

                return { ...condition, [field]: value }
              }

              return condition
            })
          }
        }

        return group
      })
    )
  }

  return (
    <div>
      {/* Header với tiêu đề và nút thêm */}
      <div className='mb-2.5 flex items-center justify-between'>
        <h3 className='caption-12-medium text-gray-900'>Điều kiện</h3>
        <Button
          type='text'
          onClick={addConditionGroup}
          className='flex items-center text-primary-blue'
          icon={<PlusOutlined />}
        >
          Thêm điều kiện
        </Button>
      </div>

      {groups.map(group => (
        <div key={group.id} className='mb-6 flex flex-col rounded-lg bg-gray-100 p-4'>
          <div className='relative'>
            {/* Đường dọc liền mạch cho toàn bộ nhóm, chỉ render nếu có nhiều hơn 1 item */}
            {group.conditions.length > 1 && (
              <div
                className='absolute left-4 top-8 z-0 w-px border-l border-dashed border-primary-blue'
                style={{ height: 'calc(100% - 2rem - 1rem)' }}
              />
            )}
            {group.conditions.map((condition, condIndex) => (
              <div key={condition.id} className={`flex items-center ${condIndex > 0 ? 'mt-4' : ''} relative z-10`}>
                {condIndex === 0 ? (
                  // Item đầu tiên - có button add và đường ngang
                  <div className='z-10 flex w-12 items-center'>
                    <Button
                      type='default'
                      shape='default'
                      size='middle'
                      icon={<PlusOutlined />}
                      className='size-8 rounded-lg border-primary-blue bg-[#E6F1FE] text-primary-blue'
                      onClick={() => addCondition(group.id)}
                    />
                    <div className='flex-1'>
                      <div className='h-px border-t border-dashed border-primary-blue'></div>
                    </div>
                  </div>
                ) : (
                  // Các item tiếp theo - chỉ có đường ngang từ đường dọc chính
                  <div className='z-10 flex w-12 items-center'>
                    <div className='relative flex size-8 items-center justify-center'>
                      {/* Đường ngang từ đường dọc chính */}
                      <div className='absolute left-1/2 top-1/2 h-px w-full border-t border-dashed border-primary-blue'></div>
                    </div>
                  </div>
                )}
                <div className='flex flex-1 items-center gap-4'>
                  <div className='w-1/3'>
                    <Select
                      className='w-full'
                      value={condition.field}
                      onChange={value => updateCondition(group.id, condition.id, 'field', value)}
                      options={conditions}
                      placeholder='Nhập trường giá trị'
                    />
                  </div>
                  <div className='w-1/3'>
                    {condition.field && (
                      <Select
                        className='w-full'
                        value={condition.operator}
                        onChange={value => updateCondition(group.id, condition.id, 'operator', value)}
                        options={getOperatorOptions(condition.field)}
                      />
                    )}
                  </div>
                  <div className='w-1/3'>
                    {condition.field && (
                      <Select
                        className='w-full'
                        mode='multiple'
                        value={Array.isArray(condition.value) ? condition.value : []}
                        onChange={value => updateCondition(group.id, condition.id, 'value', value)}
                        options={conditionValues[condition.field] || []}
                        placeholder='Chọn giá trị'
                        showSearch
                        filterOption={(input, option) =>
                          String(option?.label ?? '')
                            .toLowerCase()
                            .includes(input.toLowerCase())
                        }
                        maxTagCount={1}
                        maxTagTextLength={12}
                      />
                    )}
                  </div>
                  <Tooltip title='Xóa điều kiện' className='flex items-center justify-center bg-transparent'>
                    <Button
                      type='default'
                      shape='default'
                      size='middle'
                      icon={<i className='onedx-delete size-4 text-[#5e7699]' />}
                      onClick={() => removeCondition(group.id, condition.id)}
                      className='flex size-8 items-center justify-center'
                    />
                  </Tooltip>
                </div>
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  )
}
