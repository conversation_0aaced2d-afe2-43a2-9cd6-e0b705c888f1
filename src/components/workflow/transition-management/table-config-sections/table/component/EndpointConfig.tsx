'use client'

import React, { useState, useEffect } from 'react'

import { Form, Input, Select, Empty, Space } from 'antd'

import { RequiredDotLabel } from '@/components/label/RequiredDotLabel'
import { GreenDotPanel } from '@/components/label/GreenDotPanel'
import { generateRawUrl } from '@/utils/workflow'
import { CONNECTION_METHODS, AUTH_METHODS } from '@/constants/workflow/transition'
import type { EndpointConfigProps } from '@/types/workflow/transition'

// Định nghĩa interface cho initialData
interface EndpointInitialData {
  url?: {
    raw: string
    variables?: Array<{
      key: string
      value: string
    }>
  }
  rawUrl?: string
  connectionMethod?: string
  auth?: {
    type: string
    bearer?: {
      key: string
      value: string
    }
    apikey?: Array<{
      key: string
      value: string
      type: string
    }>
    basic?: {
      username: string
      password: string
    }
  }
  tokenKey?: string
  tokenValue?: string
  apiKeyValue?: string
  keyValue?: string
  keyPlacement?: string
}

// Component EmptyFormItem để hiển thị form item không có label
const EmptyFormItem: React.FC<any> = ({ children, ...props }) => {
  return (
    <Form.Item {...props} noStyle>
      {children}
    </Form.Item>
  )
}

export const EndpointConfig: React.FC<EndpointConfigProps & { initialData?: EndpointInitialData }> = ({
  backgroundColor,
  name,
  initialData
}) => {
  const form = Form.useFormInstance()

  // State cho URL endpoint
  const [urlEndpoint, setUrlEndpoint] = useState<string>(initialData?.url?.raw || '')
  const [rawUrl, setRawUrl] = useState<string>(initialData?.rawUrl || '') // URL đã được format

  // State cho phương thức xác thực và các tham số
  const [authMethod, setAuthMethod] = useState<string>(initialData?.auth?.type || 'bearer_token')

  // Khởi tạo params từ variables trong initialData nếu có
  const [params, setParams] = useState<Array<{ key: string; value: string }>>(() => {
    if (initialData?.url?.variables && Array.isArray(initialData.url.variables)) {
      return initialData.url.variables.map(variable => ({
        key: variable.key || '',
        value: variable.value || ''
      }))
    }

    return []
  })

  const [pathVariables, setPathVariables] = useState<Array<{ key: string; value: string }>>([])

  // State cho vị trí đặt API key
  const [keyPlacement, setKeyPlacement] = useState<string>(initialData?.keyPlacement || 'header')

  // State cho việc đóng/mở panel
  const [endpointCollapsed, setEndpointCollapsed] = useState<boolean>(false)

  // State cho phương thức HTTP
  const [httpMethod, setHttpMethod] = useState<string>(initialData?.connectionMethod || 'GET')

  // Xử lý thay đổi phương thức kết nối
  const handleConnectionMethodChange = (value: string) => {
    setHttpMethod(value)
    form.setFieldValue([name, 'connectionMethod'], value)
  }

  // Xử lý thay đổi URL endpoint
  const handleUrlEndpointChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const url = e.target.value

    setUrlEndpoint(url)
    form.setFieldValue([name, 'urlEndpoint'], url)
    parseUrlEndpoint(url)

    // Tạo raw URL
    const raw = generateRawUrl(url, params)

    setRawUrl(raw)
    form.setFieldValue([name, 'rawUrl'], raw) // Lưu raw URL vào form
  }

  // Phân tích URL để trích xuất path variables và params
  const parseUrlEndpoint = (url: string) => {
    try {
      // Lấy phần path trước query string
      const getPathBeforeQuery = (url: string) => url.split('?')[0]

      // Tìm path variables (định dạng /:name)
      const pathMatches = Array.from(getPathBeforeQuery(url).matchAll(/\/:([^/\?&]+)/g)).map(m => m[1])

      // Tìm query params (định dạng ?name=value hoặc &name=value)
      const queryMatches = Array.from(url.matchAll(/[?&]([^=&]+)=([^&]*)/g)).map(m => ({
        key: m[1],
        value: m[2]
      }))

      // Tạo mảng path variables mới
      const newPathVariables = pathMatches.map(key => {
        const existingParam = pathVariables.find(p => p && p.key === key)

        return {
          key,
          value: existingParam?.value || ''
        }
      })

      // Tạo mảng params mới
      const newParams = queryMatches.map(({ key, value }) => {
        return {
          key,
          value: value
        }
      })

      // Cập nhật state và form
      setPathVariables(newPathVariables)
      setParams(newParams)

      form.setFieldsValue({
        pathVariables: newPathVariables,
        params: newParams
      })
    } catch (error) {
      console.error('Error parsing URL:', error)
    }
  }

  // Xử lý thay đổi phương thức xác thực
  const handleAuthMethodChange = (value: string) => {
    setAuthMethod(value)
    form.setFieldValue([name, 'authMethod'], value)

    // Clear các fields của authentication methods khác khi chuyển đổi
    if (value === 'bearer_token') {
      // Clear API key fields
      form.setFieldsValue({
        [name]: {
          ...form.getFieldValue(name),
          apiKeyValue: undefined,
          keyValue: undefined,
          keyPlacement: 'header' // reset to default
        }
      })
    } else if (value === 'api_key') {
      // Clear bearer token fields
      form.setFieldsValue({
        [name]: {
          ...form.getFieldValue(name),
          tokenKey: undefined,
          tokenValue: undefined
        }
      })

      // Reset key placement to default if not set
      if (!form.getFieldValue([name, 'keyPlacement'])) {
        form.setFieldValue([name, 'keyPlacement'], 'header')
        setKeyPlacement('header')
      }
    } else if (value === 'no_auth') {
      // Clear all auth fields
      form.setFieldsValue({
        [name]: {
          ...form.getFieldValue(name),
          tokenKey: undefined,
          tokenValue: undefined,
          apiKeyValue: undefined,
          keyValue: undefined,
          keyPlacement: undefined
        }
      })
    }
  }

  // Xử lý thay đổi key placement
  const handleKeyPlacementChange = (value: string) => {
    setKeyPlacement(value)
    form.setFieldValue([name, 'keyPlacement'], value)
  }

  // Xử lý thay đổi param value
  const handleParamValueChange = (index: number, value: string) => {
    const newParams = [...params]

    newParams[index].value = value
    setParams(newParams)

    const formParams = form.getFieldValue('params') || []

    formParams[index] = { ...formParams[index], value }
    form.setFieldValue('params', formParams)

    // Cập nhật raw URL
    const raw = generateRawUrl(urlEndpoint, newParams)

    setRawUrl(raw)
    form.setFieldValue([name, 'rawUrl'], raw)
  }

  // Xử lý thay đổi param key
  const handleParamKeyChange = (index: number, value: string) => {
    const newParams = [...params]

    newParams[index].key = value
    setParams(newParams)

    const formParams = form.getFieldValue('params') || []

    formParams[index] = { ...formParams[index], key: value }
    form.setFieldValue('params', formParams)

    // Cập nhật raw URL
    const raw = generateRawUrl(urlEndpoint, newParams)

    setRawUrl(raw)
    form.setFieldValue([name, 'rawUrl'], raw)
  }

  // Xử lý thay đổi path variable value
  const handlePathVariableValueChange = (index: number, value: string) => {
    const newVariables = [...pathVariables]

    newVariables[index].value = value
    setPathVariables(newVariables)

    const formPathVariables = form.getFieldValue('pathVariables') || []

    formPathVariables[index] = { ...formPathVariables[index], value }
    form.setFieldValue('pathVariables', formPathVariables)

    // Cập nhật raw URL
    const raw = generateRawUrl(urlEndpoint, params)

    setRawUrl(raw)
    form.setFieldValue([name, 'rawUrl'], raw)
  }

  // Xử lý thay đổi path variable key
  const handlePathVariableKeyChange = (index: number, value: string) => {
    const newVariables = [...pathVariables]

    newVariables[index].key = value
    setPathVariables(newVariables)

    const formPathVariables = form.getFieldValue('pathVariables') || []

    formPathVariables[index] = { ...formPathVariables[index], key: value }
    form.setFieldValue('pathVariables', formPathVariables)

    // Cập nhật raw URL
    const raw = generateRawUrl(urlEndpoint, params)

    setRawUrl(raw)
    form.setFieldValue([name, 'rawUrl'], raw)
  }

  // Kiểm tra xem có nên hiển thị params và path variables không
  const shouldShowParamsAndPathVariables = () => {
    return params.length > 0 || pathVariables.length > 0
  }

  // Validate URL không có key trùng lặp
  const validateUrlNoDuplicates = (_: any, url: string) => {
    if (!url) return Promise.resolve()

    const urlRegex =
      /https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)/gi

    if (!urlRegex.test(url)) {
      return Promise.reject(new Error('Đường dẫn không đúng định dạng'))
    }

    // Tìm tất cả path variables
    const pathMatches = Array.from(url.matchAll(/:([^/\?&]+)/g)).map(m => m[1])
    // Tìm tất cả query params
    const queryMatches = Array.from(url.matchAll(/[?&]([^=&]+)=/g)).map(m => m[1])

    const dupPath = pathMatches.find((k, i) => pathMatches.indexOf(k) !== i)

    if (dupPath) {
      return Promise.reject(new Error(`Path variable "${dupPath}" bị trùng lặp`))
    }

    const dupQuery = queryMatches.find((k, i) => queryMatches.indexOf(k) !== i)

    if (dupQuery) {
      return Promise.reject(new Error(`Query parameter "${dupQuery}" bị trùng lặp`))
    }

    return Promise.resolve()
  }

  // Validate key không trùng lặp
  const validateUniqueKey = (value: string, index: number, type: 'pathVariables' | 'params') => {
    const pathVariables = form.getFieldValue('pathVariables') || []
    const params = form.getFieldValue('params') || []

    const currentList = type === 'pathVariables' ? pathVariables : params

    const duplicate = currentList.some((item: any, idx: number) => idx !== index && item?.key === value)

    if (duplicate) {
      return Promise.reject(new Error('Key không được trùng lặp'))
    }

    return Promise.resolve()
  }

  // Khởi tạo form fields
  useEffect(() => {
    form.setFieldsValue({
      [name]: {
        connectionMethod: httpMethod,
        urlEndpoint: urlEndpoint,
        authMethod: authMethod,
        keyPlacement: keyPlacement,
        // Thêm các giá trị xác thực từ initialData
        tokenKey: initialData?.tokenKey || 'Authorization',
        tokenValue: initialData?.auth?.bearer?.value || '',
        apiKeyValue: initialData?.auth?.apikey?.[0]?.value || '',
        keyValue: initialData?.auth?.apikey?.[0]?.key || ''
      },
      pathVariables: pathVariables,
      params: params
    })
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [initialData])

  return (
    <div className='mb-4 rounded-lg border border-gray-3'>
      <GreenDotPanel
        label='Cấu hình endpoint'
        collapsed={endpointCollapsed}
        onToggle={() => setEndpointCollapsed(!endpointCollapsed)}
        backgroundColor={backgroundColor}
      >
        {/* Phương thức kết nối */}
        <div className='mb-4 grid grid-cols-2 gap-4'>
          <Form.Item
            name={[name, 'connectionMethod']}
            label={<RequiredDotLabel label='Phương thức kết nối' />}
            className='mb-0'
            required={false}
            rules={[{ required: true, message: 'Phương thức HTTP không được bỏ trống' }]}
          >
            <Select
              placeholder='Chọn phương thức kết nối'
              options={CONNECTION_METHODS}
              defaultValue='GET'
              onChange={handleConnectionMethodChange}
              notFoundContent={<Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description='Không có dữ liệu' />}
            />
          </Form.Item>

          {/* URL Endpoint */}
          <Form.Item
            name={[name, 'urlEndpoint']}
            label={<RequiredDotLabel label='URL Endpoint API' />}
            className='mb-0'
            required={false}
            rules={[
              { required: true, message: 'Endpoint URL không được bỏ trống' },
              { validator: validateUrlNoDuplicates }
            ]}
          >
            <Input placeholder='Nhập URL' onChange={handleUrlEndpointChange} />
          </Form.Item>
        </div>

        {/* Hiển thị Raw URL */}
        {rawUrl && (
          <div className='mb-4 hidden'>
            <Form.Item name={[name, 'rawUrl']} label='Raw URL (Formatted)' className='mb-0'>
              <Input value={rawUrl} readOnly className='bg-gray-50' placeholder='Raw URL' />
            </Form.Item>
          </div>
        )}

        {/* Params và Path Variables - Hiển thị khi có params hoặc path variables */}
        {shouldShowParamsAndPathVariables() && (
          <div className='mb-4 flex flex-col gap-3 rounded-xl border-2 border-dashed border-[#B7BDC7] bg-gray-12 p-3'>
            {/* Params */}
            {params.length > 0 && (
              <Form.Item label={<RequiredDotLabel label='Params' />} className='mb-0' required={false}>
                <div className='flex w-full flex-col gap-3'>
                  {params.map((param, idx) => (
                    <Space.Compact key={`query-${param.key}-${idx}`} block>
                      <EmptyFormItem
                        name={['params', idx, 'key']}
                        className='w-full'
                        rules={[
                          { required: true, message: 'Key không được bỏ trống' },
                          {
                            validator: (_: any, value: string) => validateUniqueKey(value, idx, 'params')
                          }
                        ]}
                      >
                        <Input
                          className='border-r-0'
                          prefix='Key:'
                          value={param.key}
                          onChange={e => handleParamKeyChange(idx, e.target.value)}
                          maxLength={50}
                        />
                      </EmptyFormItem>
                      <EmptyFormItem name={['params', idx, 'value']} className='w-full'>
                        <Input
                          prefix='Value:'
                          value={param.value}
                          onChange={e => handleParamValueChange(idx, e.target.value)}
                          maxLength={50}
                        />
                      </EmptyFormItem>
                    </Space.Compact>
                  ))}
                </div>
              </Form.Item>
            )}

            {/* Path Variables */}
            {pathVariables.length > 0 && (
              <Form.Item label={<RequiredDotLabel label='Path Variables' />} className='mb-0' required={false}>
                <div className='grid grid-cols-3 gap-4'>
                  {pathVariables.map((variable, idx) => (
                    <Space.Compact key={idx} block>
                      <EmptyFormItem
                        name={['pathVariables', idx, 'key']}
                        rules={[
                          {
                            validator: (_: any, value: string) => validateUniqueKey(value, idx, 'pathVariables')
                          }
                        ]}
                      >
                        <Input
                          className='border-r-0'
                          prefix='Key:'
                          value={variable.key}
                          onChange={e => handlePathVariableKeyChange(idx, e.target.value)}
                          maxLength={50}
                        />
                      </EmptyFormItem>
                      <EmptyFormItem
                        name={['pathVariables', idx, 'value']}
                        rules={[{ required: true, message: 'Value không được bỏ trống' }]}
                      >
                        <Input
                          prefix='Value:'
                          value={variable.value}
                          onChange={e => handlePathVariableValueChange(idx, e.target.value)}
                          maxLength={50}
                        />
                      </EmptyFormItem>
                    </Space.Compact>
                  ))}
                </div>
              </Form.Item>
            )}
          </div>
        )}

        {/* Phương thức xác thực API */}
        <div className='mb-4'>
          {/* Bearer Token - 3 cột ngang hàng */}
          {authMethod === 'bearer_token' && (
            <div className='grid grid-cols-3 gap-4'>
              <Form.Item
                name={[name, 'authMethod']}
                label={<RequiredDotLabel label='Phương thức xác thực API' />}
                className='mb-0'
              >
                <Select
                  placeholder='Chọn phương thức'
                  options={AUTH_METHODS}
                  value={authMethod}
                  onChange={handleAuthMethodChange}
                  notFoundContent={<Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description='Không có dữ liệu' />}
                />
              </Form.Item>

              <Form.Item
                name={[name, 'tokenKey']}
                label={<RequiredDotLabel label='Token Key' />}
                required={false}
                className='mb-0'
                rules={[{ required: true, message: 'Token Key không được bỏ trống' }]}
              >
                <Input placeholder='Authorization' autoComplete='new-password' defaultValue='Authorization' disabled />
              </Form.Item>

              <Form.Item
                name={[name, 'tokenValue']}
                label={<RequiredDotLabel label='Token Value' />}
                required={false}
                className='mb-0'
                rules={[{ required: true, message: 'Token value không được bỏ trống' }]}
              >
                <Input.Password placeholder='Nhập token value' autoComplete='new-password' />
              </Form.Item>
            </div>
          )}

          {/* API Key - 4 cột */}
          {authMethod === 'api_key' && (
            <div className='grid grid-cols-4 gap-4'>
              <Form.Item
                name={[name, 'authMethod']}
                label={<RequiredDotLabel label='Phương thức xác thực API' />}
                className='mb-0'
                required={false}
                rules={[{ required: true, message: 'Phương thức xác thực không được bỏ trống' }]}
              >
                <Select
                  placeholder='Chọn phương thức'
                  options={AUTH_METHODS}
                  value={authMethod}
                  onChange={handleAuthMethodChange}
                  notFoundContent={<Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description='Không có dữ liệu' />}
                />
              </Form.Item>

              <Form.Item
                name={[name, 'apiKeyValue']}
                label={<RequiredDotLabel label='API Key' />}
                className='mb-0'
                required={false}
                rules={[
                  { required: true, message: 'API Key không được bỏ trống' },
                  {
                    validator: (_: any, value: string) => {
                      if (value && value.trim().length === 0) {
                        return Promise.reject('API Key không được bỏ trống')
                      }

                      return Promise.resolve()
                    }
                  }
                ]}
              >
                <Input.Password placeholder='Nhập API Key' />
              </Form.Item>

              <Form.Item
                name={[name, 'keyValue']}
                label={<RequiredDotLabel label='Key Value' />}
                className='mb-0'
                required={false}
                rules={[
                  { required: true, message: 'Key value không được bỏ trống' },
                  {
                    validator: (_: any, value: string) => {
                      if (value && value.trim().length === 0) {
                        return Promise.reject('Key value không được bỏ trống')
                      }

                      return Promise.resolve()
                    }
                  }
                ]}
              >
                <Input placeholder='Nhập key value' />
              </Form.Item>

              <Form.Item
                name={[name, 'keyPlacement']}
                label={<RequiredDotLabel label='Key Placement' />}
                className='mb-0'
                required={false}
                rules={[{ required: true, message: 'Key Placement không được bỏ trống' }]}
              >
                <Select
                  placeholder='Chọn vị trí đặt key'
                  options={[
                    { value: 'header', label: 'Header' },
                    { value: 'query', label: 'Query Param' }
                  ]}
                  defaultValue='header'
                  onChange={handleKeyPlacementChange}
                />
              </Form.Item>
            </div>
          )}

          {/* No Auth - 1 cột */}
          {authMethod === 'no_auth' && (
            <Form.Item
              name={[name, 'authMethod']}
              label={<RequiredDotLabel label='Phương thức xác thực API' />}
              className='mb-0'
              required={false}
              rules={[{ required: true, message: 'Phương thức xác thực không được bỏ trống' }]}
            >
              <Select
                placeholder='Chọn phương thức'
                options={AUTH_METHODS}
                value={authMethod}
                onChange={handleAuthMethodChange}
                notFoundContent={<Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description='Không có dữ liệu' />}
              />
            </Form.Item>
          )}
        </div>
      </GreenDotPanel>
    </div>
  )
}
