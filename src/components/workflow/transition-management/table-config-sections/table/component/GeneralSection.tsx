'use client'

import { useState, useRef, useLayoutEffect, useEffect } from 'react'

import { Button, Checkbox, Form, Input, Popover, Select, Radio, message, Dropdown, Empty } from 'antd'
import { CheckOutlined, DownOutlined, SearchOutlined, CloseOutlined } from '@ant-design/icons'
import classNames from 'classnames'

import { RequiredDotLabel } from '@/components/label/RequiredDotLabel'
import { DEFAULT_STATE_COLOR, SUGGESTED_COLORS } from '@/constants/workflow/stateTransition'
import { SectionHeader } from '../../../../shared/section-header/SectionHeader'
import { validateCustomPattern, validateMaxLengthStr, validateRequireInput, validateVietnamese } from '@/validator'
import { useStateManagement } from '@/hooks/workflow/useStateManagement'
import { generateCode } from '@/utils/workflow'
import { stateTransitionAdmin } from '@/models/workflow/stateTransition'
import { useNameValidation } from '@/hooks/workflow'
import type { TransitionStateTableItem } from '@/types/workflow/transition'
import IconDropDown from '@/components/device/device-profile/common/IconDropDown'

// Thêm interface StateResponse vì không có trong types
interface StateResponse {
  content: any[]
  totalElements: number
  pageable: {
    pageNumber: number
    pageSize: number
  }
}

const { TextArea } = Input

interface StateItem {
  id: string
  name: string
  code: string
  displayName?: string
  colorCode?: string
  stateType?: string
  description?: string
  status?: string
  typeId?: string
  applyAll?: boolean
}

// Tạo ID mới cho trạng thái mới
const generateNewId = () => {
  return `${Math.floor(Math.random() * 1000)}`
}

interface GeneralSectionProps {
  collapsed: boolean
  onToggle: () => void
  isNewState: boolean
  handleStateNameChange: (value: string) => void
  selectedObject?: any
  messageApi: any
  tableStates?: TransitionStateTableItem[] // Danh sách trạng thái đã có trong bảng
  isEditMode?: boolean // Thêm prop để xác định đang ở chế độ chỉnh sửa
  disableNameValidation?: boolean // Thêm prop để tắt validation tên trong modal chỉnh sửa nhanh
}

export const GeneralSection: React.FC<GeneralSectionProps> = ({
  collapsed,
  onToggle,
  isNewState,
  handleStateNameChange,
  messageApi,
  tableStates = [],
  isEditMode = false,
  disableNameValidation = false
}) => {
  const form = Form.useFormInstance()
  // State cho Popover chọn màu
  const [colorPopoverOpen, setColorPopoverOpen] = useState(false)
  const stateColor = Form.useWatch('colorCode', form) || DEFAULT_STATE_COLOR

  // State cho danh sách trạng thái
  const [existingStates, setExistingStates] = useState<StateItem[]>([])
  const [loading, setLoading] = useState(false)
  const [stateTypeOptions, setStateTypeOptions] = useState<any[]>([])
  const [stateTypeLoading, setStateTypeLoading] = useState(false)
  const [stateTypeHasMore, setStateTypeHasMore] = useState(true)
  const [stateTypePage, setStateTypePage] = useState(0)
  const [stateTypeSearch, setStateTypeSearch] = useState('')

  // State cho chọn trạng thái
  const [stateDropdownOpen, setStateDropdownOpen] = useState(false)
  const [searchText, setSearchText] = useState('')
  const [pendingSelected, setPendingSelected] = useState<string | null>(null)
  const buttonRef = useRef<HTMLButtonElement>(null)
  const [buttonWidth, setButtonWidth] = useState<number>(0)

  // Lấy giá trị stateName từ form
  const stateName = Form.useWatch('stateName', form)
  const iconValue = Form.useWatch('icon', form)

  // Sử dụng hook để lấy danh sách trạng thái
  const stateManagement = useStateManagement({
    page: 0,
    size: 100,
    search: '',
    isName: 1,
    isCode: 1
  })

  const generateNewCode = () => {
    return generateCode(() => stateTransitionAdmin.generateStateCode())
  }

  // Hàm fetch dữ liệu combobox loại trạng thái
  const fetchStateTypeOptions = async (page = 0, search = '') => {
    setStateTypeLoading(true)

    try {
      const res = await stateTransitionAdmin.getStateTypeCombobox({ page, size: 10, search })

      const newOptions =
        res?.content?.map((item: any) => ({
          label: item.name,
          value: item.id
        })) || []

      setStateTypeOptions(prev => (page === 0 ? newOptions : [...prev, ...newOptions]))
      setStateTypeHasMore(!(res?.last ?? newOptions.length < 10))
    } finally {
      setStateTypeLoading(false)
    }
  }

  // Lấy danh sách trạng thái khi component mount
  useEffect(() => {
    const fetchStates = async () => {
      setLoading(true)

      try {
        const response = (await stateManagement.getStateList) as unknown as StateResponse

        if (response && response.content) {
          const states = response.content.map((state: any) => ({
            id: state.id,
            name: state.name,
            code: state.code,
            displayName: state.displayName,
            colorCode: state.colorCode,
            stateType: state.typeName,
            description: state.description,
            status: state.status,
            typeId: state.typeId,
            applyAll: state.applyAll
          }))

          setExistingStates(states)
        }
      } catch (error) {
        messageApi.error('Không thể lấy danh sách trạng thái')
        console.error('Error fetching states:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchStates()
  }, [stateManagement.getStateList, messageApi])

  useLayoutEffect(() => {
    if (stateDropdownOpen && buttonRef.current) {
      setButtonWidth(buttonRef.current.offsetWidth)
    }
  }, [stateDropdownOpen])

  // Lọc danh sách trạng thái theo từ khóa tìm kiếm và loại bỏ những trạng thái đã có trong bảng
  const filteredStates = existingStates
    .filter(state => state.name.toLowerCase().includes(searchText.toLowerCase()))
    .filter(state => !tableStates.some(tableState => tableState.name === state.name))

  // Render lưới màu preset
  const renderColorGrid = () => (
    <div className='grid grid-cols-3 gap-4 p-3'>
      {SUGGESTED_COLORS.map(color => {
        const isSelected = stateColor === color

        return (
          <div
            key={color}
            className={`flex size-10 cursor-pointer items-center justify-center rounded-full border-2 transition-all ${isSelected ? 'border-bright-blue-9' : 'border-transparent'}`}
            style={{ backgroundColor: color }}
            onClick={() => {
              form.setFieldValue('colorCode', color)
              setColorPopoverOpen(false)
            }}
          >
            {isSelected && <CheckOutlined className='text-lg text-white' />}
          </div>
        )
      })}
    </div>
  )

  // Xử lý khi nhập tìm kiếm
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchText(e.target.value)
  }

  // Xử lý khi chọn trạng thái
  const handleSelectState = (stateId: string) => {
    setPendingSelected(stateId)
  }

  // Xử lý khi xác nhận chọn trạng thái
  const handleConfirmSelection = async () => {
    if (pendingSelected) {
      const selectedStateObj = existingStates.find(state => state.id === pendingSelected)

      if (selectedStateObj) {
        // Tìm kiếm thông tin chi tiết của trạng thái theo mã
        setLoading(true)

        try {
          // Sử dụng stateManagement để tìm kiếm chi tiết trạng thái
          // Thay vì tạo một instance mới, chúng ta sẽ lọc từ danh sách đã có
          const stateDetail = existingStates.find(state => state.id === selectedStateObj.id)

          if (stateDetail) {
            // Điền thông tin vào form
            form.setFieldsValue({
              stateName: stateDetail.name,
              stateCode: stateDetail.code,
              displayName: stateDetail.displayName,
              colorCode: stateDetail.colorCode || DEFAULT_STATE_COLOR,
              stateType: stateDetail.stateType,
              description: stateDetail.description,
              stateId: stateDetail.id,
              status: stateDetail.status,
              code: stateDetail.code,
              typeId: stateDetail.typeId,
              applyAll: stateDetail.applyAll
            })

            handleStateNameChange('existing_state') // Đánh dấu là trạng thái có sẵn
          } else {
            // Nếu không tìm thấy chi tiết, sử dụng thông tin cơ bản
            form.setFieldsValue({
              stateName: selectedStateObj.name,
              stateCode: selectedStateObj.code,
              displayName: selectedStateObj.displayName || '',
              colorCode: selectedStateObj.colorCode || DEFAULT_STATE_COLOR,
              description: selectedStateObj.description || '',
              stateId: null,
              status: selectedStateObj.status,
              code: selectedStateObj.code,
              typeId: selectedStateObj.typeId,
              applyAll: selectedStateObj.applyAll
            })
            handleStateNameChange('existing_state') // Đánh dấu là trạng thái có sẵn
          }
        } catch (error) {
          messageApi.error('Không thể lấy thông tin chi tiết trạng thái')
          console.error('Error fetching state details:', error)

          // Fallback: Sử dụng thông tin cơ bản nếu API thất bại
          form.setFieldsValue({
            stateName: selectedStateObj.name,
            stateCode: selectedStateObj.code,
            stateId: selectedStateObj.id // Có stateId khi chọn trạng thái có sẵn
          })
          handleStateNameChange('existing_state') // Đánh dấu là trạng thái có sẵn
        } finally {
          setLoading(false)
        }

        setStateDropdownOpen(false)
        setPendingSelected(null)
        setSearchText('')
      }
    }
  }

  // Xử lý khi hủy chọn trạng thái
  const handleCancelSelection = () => {
    setPendingSelected(null)
  }

  // Debounce function để validate tên loại trạng thái
  const validateStateName = useNameValidation(undefined, {
    validateFn: stateTransitionAdmin.validateStateName,
    existsErrorMessage: 'Tên trạng thái đã tồn tại trong hệ thống'
  })

  // Custom validator cho tên trạng thái
  const validateStateNameField = async (_: any, value: string) => {
    if (!value || value.trim() === '') return Promise.resolve()

    // Nếu tắt validation tên (dùng trong modal chỉnh sửa nhanh), bỏ qua validation
    if (disableNameValidation) {
      return Promise.resolve()
    }

    // Lấy stateId từ form để kiểm tra có phải trạng thái có sẵn không
    const stateId = form.getFieldValue('stateId')

    // Nếu đang chỉnh sửa trạng thái có sẵn (có stateId), bỏ qua validation
    if (isEditMode && stateId) {
      return Promise.resolve()
    }

    // Sử dụng debounce để validate tên loại trạng thái
    return new Promise((resolve, reject) => {
      validateStateName(value.trim(), error => {
        if (error) {
          reject(new Error(error))
        } else {
          resolve(undefined)
        }
      })
    })
  }

  // Xử lý khi nhấn Thêm mới
  const handleAddNewClick = async () => {
    // Tạo trạng thái mới với tên từ ô tìm kiếm
    const trimmedName = searchText.trim()

    if (trimmedName) {
      try {
        // Kiểm tra trùng lặp bằng API validateStateName
        await stateTransitionAdmin.validateStateName({
          name: trimmedName
        })

        // Nếu không trùng, tiếp tục tạo trạng thái mới
        try {
          // Đợi code được sinh ra
          const code = await generateNewCode()

          // Tạo trạng thái mới
          const newState = {
            id: generateNewId(),
            name: trimmedName, // Sử dụng tên đã được trim
            code: code
          }

          // Kiểm tra xem trạng thái mới có trùng với trạng thái trong bảng không
          const isDuplicateInTable = tableStates.some(tableState => tableState.name === trimmedName)

          if (isDuplicateInTable) {
            messageApi.warning(`Trạng thái "${trimmedName}" đã có trong bảng. Vui lòng chọn trạng thái khác.`)

            return
          }

          // Thêm trạng thái mới vào danh sách
          setExistingStates(prevStates => [...prevStates, newState])

          // Cập nhật form với thông tin trạng thái mới
          // form.setFieldsValue({
          //   stateName: newState.name,
          //   stateCode: newState.code,
          //   displayName: newState.name,
          //   colorCode: DEFAULT_STATE_COLOR,
          //   stateId: undefined, // Đánh dấu là trạng thái mới
          //   description: '',
          //   stateType: undefined
          // })

          // Đánh dấu là trạng thái mới
          handleStateNameChange('new_state')

          // Giả lập thêm mới thành công
          message.success(`Đã thêm trạng thái mới: ${newState.name}`)
        } catch (error) {
          messageApi.error('Không thể tạo mã trạng thái mới')
          console.error('Error generating code:', error)
        }

        // Chỉ reset ô tìm kiếm và giữ dropdown mở để người dùng có thể chọn
        setSearchText('')
        setPendingSelected(null)
      } catch (error: any) {
        // Nếu API trả về lỗi trùng tên
        if (error?.errorCode === 'error.data.exists') {
          messageApi.error('Tên trạng thái đã tồn tại trong hệ thống')
        } else {
          messageApi.error('Đã có lỗi xảy ra khi kiểm tra tên trạng thái')
          console.error('Error validating state name:', error)
        }
      }
    } else {
      message.info('Vui lòng nhập tên trạng thái mới')
    }
  }

  // Khi mở dropdown lần đầu thì load dữ liệu
  const handleDropdownVisibleChange = (open: boolean) => {
    if (open) {
      // Nếu options chỉ có 1 item (option đang chọn), fetch lại toàn bộ danh sách
      if (stateTypeOptions.length <= 1) {
        setStateTypePage(0)
        fetchStateTypeOptions(0, '')
      }
    }
  }

  // Khi scroll tới cuối thì load thêm
  const handlePopupScroll = (e: React.UIEvent<HTMLDivElement, UIEvent>) => {
    const target = e.target as HTMLDivElement

    if (target.scrollTop + target.offsetHeight >= target.scrollHeight - 8 && stateTypeHasMore && !stateTypeLoading) {
      const nextPage = stateTypePage + 1

      setStateTypePage(nextPage)
      fetchStateTypeOptions(nextPage, stateTypeSearch)
    }
  }

  // Khi search
  const handleSearch = (value: string) => {
    // Trim chuỗi tìm kiếm trước khi gửi đến API
    const trimmedValue = value.trim()

    setStateTypeSearch(trimmedValue)
    setStateTypePage(0)
    setStateTypeHasMore(true)
    fetchStateTypeOptions(0, trimmedValue)
  }

  // Render dropdown chọn trạng thái
  const stateDropdownContent = (
    <div
      className='rounded-lg border bg-white px-2 py-1 shadow-lg'
      style={{ minWidth: buttonWidth, maxWidth: buttonWidth }}
    >
      <Input
        prefix={<SearchOutlined className='text-gray-6' />}
        value={searchText}
        onChange={handleSearchChange}
        placeholder='Tìm kiếm'
        maxLength={100}
        className='mb-3'
        suffix={
          <Button
            size='small'
            className='border-none bg-bg-primary-lighter text-bright-blue-9'
            onClick={handleAddNewClick}
            disabled={!searchText.trim()}
          >
            Thêm mới
          </Button>
        }
        onPressEnter={e => {
          e.stopPropagation()
          handleAddNewClick()
        }}
      />
      {/* Scrollable radio list */}
      {loading ? (
        <div className='flex h-[160px] items-center justify-center'>
          <span>Đang tải...</span>
        </div>
      ) : !filteredStates.length ? (
        <Empty
          description={<div className='text-xs font-semibold text-gray-6'>Chưa có dữ liệu</div>}
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          className='m-0 flex h-[160px] flex-col items-center justify-center rounded-[8px] border border-solid border-gray-3'
        />
      ) : (
        <div className='max-h-[260px] overflow-y-auto rounded-[8px] border border-solid border-gray-3'>
          <Radio.Group
            className='flex w-full flex-col gap-2'
            value={pendingSelected ?? form.getFieldValue('stateName')}
            onChange={e => handleSelectState(e.target.value)}
          >
            {filteredStates.map((state, index) => (
              <Radio
                key={state.id}
                value={state.id}
                className={classNames(
                  'flex w-full items-center gap-2 p-4 pl-3',
                  index !== filteredStates.length - 1 && 'border-b border-solid border-gray-3'
                )}
              >
                <div className='flex items-center gap-2'>
                  <div className='max-w-[120px] truncate text-xs font-medium' title={state.name}>
                    {state.name}
                  </div>
                </div>
              </Radio>
            ))}
          </Radio.Group>
        </div>
      )}
      {pendingSelected && (
        <div className='mt-3 flex items-center justify-end gap-3'>
          <Button onClick={handleCancelSelection}>Hủy</Button>
          <Button type='primary' onClick={handleConfirmSelection} loading={loading}>
            Xác nhận
          </Button>
        </div>
      )}
    </div>
  )

  return (
    <div className='mb-5 rounded bg-gray-alpha-1'>
      <SectionHeader
        title='Thông tin trạng thái'
        collapsed={collapsed}
        onToggle={onToggle}
        component={
          <div>
            {/* Checkbox chọn đối tượng áp dụng */}
            <Form.Item className='mb-0' name='isSharedForAllObjects' valuePropName='checked'>
              <Checkbox className='border-r border-solid border-border-neutral-medium pr-2'>
                Dùng chung cho tất cả đối tượng
              </Checkbox>
            </Form.Item>
          </div>
        }
      />

      {!collapsed && (
        <div className='p-4'>
          {/* Hàng 1: Trạng thái, Color Picker, Tên hiển thị, Icon */}
          <div className='mb-4 flex items-start justify-start gap-3'>
            {/* Tên trạng thái */}
            <div className='w-[250px]'>
              <Form.Item name='stateId' initialValue={undefined} className='hidden'></Form.Item>
              <Form.Item name='status' initialValue={undefined} className='hidden'></Form.Item>
              <Form.Item name='typeId' initialValue={undefined} className='hidden'></Form.Item>
              <Form.Item
                name='stateName'
                label={<RequiredDotLabel label='Trạng thái' />}
                rules={[
                  validateRequireInput('Tên trạng thái không được bỏ trống'),
                  validateMaxLengthStr(50, 'Tên trạng thái không được vượt quá 50 ký tự'),
                  validateCustomPattern(/^[A-Za-z][A-Za-z0-9_.]*$/, 'Tên trạng thái sai định dạng'),
                  {
                    validator: validateStateNameField
                  }
                ]}
                className='mb-0'
              >
                <Dropdown
                  open={stateDropdownOpen}
                  onOpenChange={setStateDropdownOpen}
                  dropdownRender={() => stateDropdownContent}
                  trigger={['click']}
                  className='w-full'
                >
                  <Button
                    ref={buttonRef}
                    className='flex h-9 w-full items-center justify-between'
                    onClick={() => setStateDropdownOpen(true)}
                  >
                    {stateName ? (
                      <span className='flex items-center gap-2 rounded-[8px] bg-bright-blue-9 px-2 py-1 text-white'>
                        <span className='max-w-[120px] truncate text-xs font-medium' title={stateName}>
                          {stateName}
                        </span>
                        <CloseOutlined
                          className='ml-2 cursor-pointer transition-opacity hover:opacity-80'
                          onClick={e => {
                            e.stopPropagation()
                            form.setFields([
                              {
                                name: 'stateName',
                                value: undefined,
                                touched: true
                              },
                              {
                                name: 'stateCode',
                                value: undefined,
                                touched: true
                              },
                              {
                                name: 'displayName',
                                value: undefined,
                                touched: true
                              },
                              {
                                name: 'colorCode',
                                value: DEFAULT_STATE_COLOR,
                                touched: true
                              },
                              {
                                name: 'stateType',
                                value: undefined,
                                touched: true
                              },
                              {
                                name: 'description',
                                value: undefined,
                                touched: true
                              }
                            ])
                            handleStateNameChange('')
                          }}
                        />
                      </span>
                    ) : (
                      <span className='font-light text-gray-400'>Nhập tên trạng thái</span>
                    )}
                    <DownOutlined
                      className={classNames('ml-2 text-gray-400 transition-transform duration-200', {
                        'rotate-180': stateDropdownOpen
                      })}
                    />
                  </Button>
                </Dropdown>
              </Form.Item>
            </div>

            {/* Màu sắc */}
            <div className='w-[80px]'>
              <Form.Item name='colorCode' initialValue={DEFAULT_STATE_COLOR} className='mb-0' label>
                <Popover
                  trigger='click'
                  open={colorPopoverOpen}
                  onOpenChange={setColorPopoverOpen}
                  content={renderColorGrid()}
                  placement='bottomLeft'
                >
                  <div
                    tabIndex={0}
                    className='
              focus:outline-primary-6 flex h-9 w-full
              cursor-pointer items-center justify-start
              gap-1 rounded-lg
              bg-bg-surface px-2 outline
              outline-1
              outline-offset-[-1px]
              outline-border-neutral-light
              transition-all
            '
                    style={{ boxSizing: 'border-box' }}
                  >
                    <div
                      className='size-5 shrink-0 rounded-full border border-gray-3'
                      style={{ backgroundColor: stateColor }}
                    />
                    <DownOutlined className='size-5 shrink-0 text-gray-6' />
                  </div>
                </Popover>
              </Form.Item>
            </div>

            {/* Tên hiển thị */}
            <div className='w-[250px]'>
              <Form.Item
                name='displayName'
                label={
                  <span className='text-xs font-medium text-gray-11'>
                    <RequiredDotLabel>Tên hiển thị</RequiredDotLabel>
                  </span>
                }
                rules={[
                  validateRequireInput('Tên hiển thị không được bỏ trống'),
                  validateMaxLengthStr(250, 'Tên hiển thị không được vượt quá 250 ký tự'),
                  validateVietnamese('Không được phép nhập ký tự đặc biệt')
                ]}
                className='mb-0'
              >
                <Input
                  placeholder='Nhập tên hiển thị'
                  className='h-9 text-sm font-normal leading-5 tracking-tight-05 text-gray-11'
                  maxLength={250}
                />
              </Form.Item>
            </div>

            {/* Icon */}
            <div className='flex-1'>
              <Form.Item name='icon' label='Icon' className='mb-0'>
                <IconDropDown iconValue={iconValue} onIconChange={value => form.setFieldValue('icon', value)} />
              </Form.Item>
            </div>
          </div>

          {/* Hàng 2: Mã trạng thái, Loại trạng thái */}
          <div className='mb-4 flex items-start justify-start gap-3'>
            {/* Mã trạng thái */}
            <div className='flex-1'>
              <Form.Item name='stateCode' label={<RequiredDotLabel label='Mã trạng thái' />} className='mb-0'>
                <Input placeholder='Nhập mã trạng thái' className='h-9' disabled={!isNewState} />
              </Form.Item>
            </div>

            {/* Loại trạng thái */}
            <div className='flex-1'>
              <Form.Item
                name='stateType'
                label={<RequiredDotLabel label='Loại trạng thái' />}
                rules={[{ required: true, message: 'Loại trạng thái không được bỏ trống' }]}
                className='mb-0'
              >
                <Select
                  placeholder='Chọn loại trạng thái'
                  showSearch
                  optionFilterProp='label'
                  options={stateTypeOptions}
                  loading={stateTypeLoading}
                  onDropdownVisibleChange={handleDropdownVisibleChange}
                  onPopupScroll={handlePopupScroll}
                  onSearch={handleSearch}
                  filterOption={false}
                  notFoundContent={<Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description='Không có dữ liệu' />}
                  onChange={value => {
                    // Tìm option được chọn để lấy label (text)
                    const selectedOption = stateTypeOptions.find(option => option.value === value)

                    if (selectedOption) {
                      // Set stateType bằng label (text) để hiển thị đúng
                      form.setFieldValue('stateType', selectedOption.label)
                      // Set typeId bằng value (id) để lưu trữ
                      form.setFieldValue('typeId', value)
                    }
                  }}
                />
              </Form.Item>
            </div>
          </div>

          {/* Hàng 3: Mô tả (full width) */}
          <div className='w-full'>
            <Form.Item
              name='description'
              label='Mô tả'
              className='mb-0'
              rules={[{ required: true, message: 'Mô tả không được bỏ trống' }]}
            >
              <TextArea placeholder='Nhập mô tả' rows={3} maxLength={100} showCount />
            </Form.Item>
          </div>
        </div>
      )}
    </div>
  )
}
