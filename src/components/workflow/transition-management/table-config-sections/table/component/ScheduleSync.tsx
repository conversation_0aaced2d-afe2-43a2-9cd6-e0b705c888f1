'use client'

import React, { useState, useImperativeHandle, forwardRef, useCallback, useEffect } from 'react'

import { CloseOutlined } from '@ant-design/icons'
import { Form, Select, DatePicker, Tag } from 'antd'
import moment from 'moment'
import dayjs from 'dayjs'
import { styled } from 'styled-components'

import { RequiredDotLabel } from '@/components/label/RequiredDotLabel'
import {
  DAY_IN_MONTH,
  FREQUENCY_TYPE_MONTH_VALUES,
  SCHEDULE_FREQUENCY_VALUES,
  SCHEDULE_IN_WEEK_VALUES,
  SCHEDULE_TYPE_VALUES,
  SCHEDULE_WEEK_OF_MONTH_VALUES
} from '@/constants/workflow/trigger'

// Interface cho kết quả mapping
export interface ScheduleSyncMappingResult {
  type: string | null
  startTime: string | null
  endTime: string | null
  startDate: string | null
  intervalType: string | null
  dayOfWeek: string[] | number | null
  dayOfMonth: string | null
  dayOfWeekMonthly: string | null
  weekOfMonth: number | null
}

interface ScheduleSyncProps {
  id: string
  collapsed?: boolean
  setCollapsed?: (collapsed: boolean) => void
  initialData?: ScheduleSyncMappingResult | null
}

export interface ScheduleSyncRef {
  mapScheduleSyncValues: () => ScheduleSyncMappingResult
  form?: any // Expose form để có thể debug
}

export const ScheduleSync = forwardRef<ScheduleSyncRef, ScheduleSyncProps>(({ id, initialData }, ref) => {
  const [form] = Form.useForm()

  const [scheduleType, setScheduleType] = useState<string>(initialData?.type || 'ONCE')

  const [frequencyTypeDaily, setFrequencyTypeDaily] = useState<string>(initialData?.intervalType || 'ONCE')

  const [frequencyTypeWeek, setFrequencyTypeWeek] = useState<string>(initialData?.intervalType || 'ONCE')

  const [frequencyTypeMonth, setFrequencyTypeMonth] = useState<string>('FIXED_DATE')

  const [selectedWeekDays, setSelectedWeekDays] = useState<string[]>(
    Array.isArray(initialData?.dayOfWeek) ? initialData.dayOfWeek : ['MON', 'TUE', 'WED']
  )

  const [selectedDayOfWeek, setSelectedDayOfWeek] = useState<number>(
    typeof initialData?.dayOfWeek === 'number' ? initialData.dayOfWeek : 1
  )

  const [selectedWeekOfMonth, setSelectedWeekOfMonth] = useState<number>(initialData?.weekOfMonth || 1)

  // Khởi tạo dữ liệu form từ initialData
  useEffect(() => {
    if (initialData) {
      // Set scheduleType
      if (initialData.type) {
        setScheduleType(initialData.type)
        form.setFieldsValue({ [`${id}-scheduleType`]: initialData.type })
      }

      // Set các giá trị khác dựa vào loại schedule
      switch (initialData.type) {
        case 'ONCE':
          if (initialData.startTime) {
            // Chuyển đổi startTime string sang dayjs
            const [hour, minute] = initialData.startTime.split(':').map(Number)

            form.setFieldsValue({
              [`${id}-syncTimeOnce`]: dayjs()
                .hour(hour || 0)
                .minute(minute || 0)
            })
          }

          if (initialData.startDate) {
            // Chuyển đổi startDate string (DD/MM/YYYY) sang dayjs
            const [day, month, year] = initialData.startDate.split('/').map(Number)

            form.setFieldsValue({
              [`${id}-startDateOnce`]: dayjs()
                .date(day || 1)
                .month((month || 1) - 1)
                .year(year || 2023)
            })
          }

          break

        case 'DAILY':
          if (initialData.intervalType) {
            setFrequencyTypeDaily(initialData.intervalType)
            form.setFieldsValue({ [`${id}-frequencyDaily`]: initialData.intervalType })
          }

          if (initialData.startTime) {
            const [hour, minute] = initialData.startTime.split(':').map(Number)

            form.setFieldsValue({
              [`${id}-syncTimeDaily`]: dayjs()
                .hour(hour || 0)
                .minute(minute || 0)
            })
          }

          if (initialData.startDate) {
            const [day, month, year] = initialData.startDate.split('/').map(Number)

            form.setFieldsValue({
              [`${id}-startDateDaily`]: dayjs()
                .date(day || 1)
                .month((month || 1) - 1)
                .year(year || 2023)
            })
          }

          break

        case 'WEEKLY':
          if (initialData.intervalType) {
            setFrequencyTypeWeek(initialData.intervalType)
            form.setFieldsValue({ [`${id}-frequencyDayInWeek`]: initialData.intervalType })
          }

          if (Array.isArray(initialData.dayOfWeek)) {
            setSelectedWeekDays(initialData.dayOfWeek)
            form.setFieldsValue({ [`${id}-weekDays`]: initialData.dayOfWeek })
          }

          if (initialData.startTime) {
            const [hour, minute] = initialData.startTime.split(':').map(Number)

            form.setFieldsValue({
              [`${id}-syncTimeDayInWeek`]: dayjs()
                .hour(hour || 0)
                .minute(minute || 0)
            })
          }

          if (initialData.startDate) {
            const [day, month, year] = initialData.startDate.split('/').map(Number)

            form.setFieldsValue({
              [`${id}-startDateDayInWeek`]: dayjs()
                .date(day || 1)
                .month((month || 1) - 1)
                .year(year || 2023)
            })
          }

          break

        case 'MONTHLY':
          // Xác định loại frequency cho tháng
          if (initialData.dayOfMonth) {
            setFrequencyTypeMonth('FIXED_DATE')

            form.setFieldsValue({
              [`${id}-frequencyDayInMonth`]: 'FIXED_DATE',
              [`${id}-dayOfMonth`]: initialData.dayOfMonth
            })
          } else if (typeof initialData.dayOfWeek === 'number' && initialData.weekOfMonth) {
            setFrequencyTypeMonth('FIXED_WEEK_DAY')

            form.setFieldsValue({
              [`${id}-frequencyDayInMonth`]: 'FIXED_WEEK_DAY'
            })

            // Cập nhật selectedDayOfWeek và selectedWeekOfMonth
            setSelectedDayOfWeek(initialData.dayOfWeek)
            setSelectedWeekOfMonth(initialData.weekOfMonth)
          }

          if (initialData.startTime) {
            const [hour, minute] = initialData.startTime.split(':').map(Number)

            form.setFieldsValue({
              [`${id}-syncTime`]: dayjs()
                .hour(hour || 0)
                .minute(minute || 0)
            })
          }

          if (initialData.startDate) {
            const [day, month, year] = initialData.startDate.split('/').map(Number)

            form.setFieldsValue({
              [`${id}-startDateDayInMonth`]: dayjs()
                .date(day || 1)
                .month((month || 1) - 1)
                .year(year || 2023)
            })
          }

          break
      }
    }
  }, [initialData, id, form])

  // Generic handler creator
  const createFieldHandler = useCallback(
    (fieldName: string, setter?: (value: any) => void) => (value: any) => {
      form.setFieldsValue({ [`${id}-${fieldName}`]: value })
      if (setter) setter(value)
    },
    [form, id]
  )

  // Các handlers được tạo từ generic function
  const handleScheduleTypeChange = createFieldHandler('scheduleType', setScheduleType)
  const handleWeekDaysChange = createFieldHandler('weekDays', setSelectedWeekDays)
  const handleFrequencyDailyChange = useCallback((value: string) => setFrequencyTypeDaily(value), [])
  const handleFrequencyWeekChange = useCallback((value: string) => setFrequencyTypeWeek(value), [])
  const handleFrequencyMonthChange = useCallback((value: string) => setFrequencyTypeMonth(value), [])
  const handleDayOfWeekChange = useCallback((value: number) => setSelectedDayOfWeek(value), [])
  const handleWeekChange = useCallback((value: number) => setSelectedWeekOfMonth(value), [])

  // Time/Date handlers
  const handleSyncTimeOnceChange = createFieldHandler('syncTimeOnce')
  const handleStartDateOnceChange = createFieldHandler('startDateOnce')
  const handleSyncTimeDailyChange = createFieldHandler('syncTimeDaily')
  const handleSyncTimeDailyRangeChange = createFieldHandler('syncTimeDailyRange')
  const handleStartDateDailyChange = createFieldHandler('startDateDaily')
  const handleSyncTimeWeeklyChange = createFieldHandler('syncTimeDayInWeek')
  const handleSyncTimeWeeklyRangeChange = createFieldHandler('syncTimeDayInWeek')
  const handleStartDateWeeklyChange = createFieldHandler('startDateDayInWeek')
  const handleDayOfMonthChange = createFieldHandler('dayOfMonth')
  const handleSyncTimeMonthChange = createFieldHandler('syncTime')
  const handleStartDateMonthChange = createFieldHandler('startDateDayInMonth')

  useImperativeHandle(ref, () => ({
    mapScheduleSyncValues,
    form // Expose form để có thể debug
  }))

  // Mapping data để gửi xuống api
  const mapScheduleSyncValues = (): ScheduleSyncMappingResult => {
    try {
      const values = form.getFieldsValue(true)
      const type = values[`${id}-scheduleType`] || scheduleType

      const result: ScheduleSyncMappingResult = {
        type,
        startTime: null,
        endTime: null,
        startDate: null,
        intervalType: null,
        dayOfWeek: null,
        dayOfMonth: null,
        dayOfWeekMonthly: null,
        weekOfMonth: null
      }

      // Helper function để format time
      const formatTime = (timeValue: any) => {
        return timeValue?.format ? timeValue.format('HH:mm') : '17:30'
      }

      const formatDate = (dateValue: any) => {
        return dateValue?.format ? dateValue.format('DD/MM/YYYY') : moment().format('DD/MM/YYYY')
      }

      switch (type) {
        case 'ONCE':
          result.startTime = formatTime(values[`${id}-syncTimeOnce`])
          result.startDate = formatDate(values[`${id}-startDateOnce`])
          break

        case 'DAILY':
          result.intervalType = frequencyTypeDaily

          if (frequencyTypeDaily === 'ONCE') {
            result.startTime = formatTime(values[`${id}-syncTimeDaily`])
          } else {
            const rangeValue = values[`${id}-syncTimeDailyRange`]

            if (Array.isArray(rangeValue)) {
              result.startTime = formatTime(rangeValue[0])
              result.endTime = formatTime(rangeValue[1])
            } else {
              result.startTime = '01:00'
              result.endTime = '01:00'
            }
          }

          result.startDate = formatDate(values[`${id}-startDateDaily`])
          break

        case 'WEEKLY':
          result.intervalType = frequencyTypeWeek
          result.dayOfWeek = values[`${id}-weekDays`] || selectedWeekDays

          if (frequencyTypeWeek === 'ONCE') {
            result.startTime = formatTime(values[`${id}-syncTimeDayInWeek`])
          } else {
            const rangeValue = values[`${id}-syncTimeDayInWeek`]

            if (Array.isArray(rangeValue)) {
              result.startTime = formatTime(rangeValue[0])
              result.endTime = formatTime(rangeValue[1])
            } else {
              result.startTime = '01:00'
              result.endTime = '01:00'
            }
          }

          result.startDate = formatDate(values[`${id}-startDateDayInWeek`])
          break

        case 'MONTHLY':
          if (frequencyTypeMonth === 'FIXED_DATE') {
            result.dayOfMonth = values[`${id}-dayOfMonth`]
          } else {
            result.dayOfWeek = selectedDayOfWeek
            result.weekOfMonth = selectedWeekOfMonth
          }

          result.startTime = formatTime(values[`${id}-syncTime`])
          result.startDate = formatDate(values[`${id}-startDateDayInMonth`])
          break
      }

      return result
    } catch (error) {
      console.error('Error in mapScheduleSyncValues:', error)

      return {
        type: scheduleType,
        startTime: '17:30',
        endTime: null,
        startDate: moment().format('DD/MM/YYYY'),
        intervalType: scheduleType,
        dayOfWeek: scheduleType === 'WEEKLY' ? selectedWeekDays : null,
        dayOfMonth: scheduleType === 'MONTHLY' && frequencyTypeMonth === 'FIXED_DATE' ? '1' : null,
        dayOfWeekMonthly: scheduleType === 'MONTHLY' && frequencyTypeMonth === 'FIXED_WEEK_DAY' ? 'MON' : null,
        weekOfMonth: scheduleType === 'MONTHLY' && frequencyTypeMonth === 'FIXED_WEEK_DAY' ? 1 : null
      }
    }
  }

  // Reusable form item component
  const FormField = ({
    name,
    label,
    children,
    className = 'col-span-1 mb-0',
    initialValue = undefined
  }: {
    name: string
    label: string | React.ReactNode
    children: React.ReactNode
    className?: string
    initialValue?: any
  }) => (
    <Form.Item name={`${id}-${name}`} label={label} className={className} initialValue={initialValue}>
      {children}
    </Form.Item>
  )

  // Reusable time picker component
  const TimePicker = ({
    name,
    label,
    onChange,
    defaultHour = 17,
    defaultMinute = 30,
    className = 'col-span-1 mb-0'
  }: {
    name: string
    label: string
    onChange: (value: any) => void
    defaultHour?: number
    defaultMinute?: number
    className?: string
  }) => (
    <FormField
      name={name}
      label={<RequiredDotLabel label={label} />}
      className={className}
      initialValue={dayjs().hour(defaultHour).minute(defaultMinute)}
    >
      <DatePicker.TimePicker format='HH:mm' placeholder='Chọn giờ' className='w-full' onChange={onChange} />
    </FormField>
  )

  // Reusable date picker component
  const DateField = ({
    name,
    label,
    onChange,
    className = 'col-span-1 mb-0'
  }: {
    name: string
    label: string
    onChange: (value: any) => void
    className?: string
  }) => (
    <FormField name={name} label={label} className={className}>
      <DatePicker placeholder='Chọn ngày' format='DD/MM/YYYY' className='w-full' onChange={onChange} />
    </FormField>
  )

  // Một lần
  const renderOnceSchedule = () => (
    <div className='grid grid-cols-3 gap-4'>
      <FormField
        name='scheduleType'
        label={<RequiredDotLabel label={`${id === 'api-schedule' ? 'Thời điểm đồng bộ' : 'Thời điểm quét'}`} />}
      >
        <Select
          value={scheduleType}
          defaultValue='ONCE'
          onChange={handleScheduleTypeChange}
          options={SCHEDULE_TYPE_VALUES}
        />
      </FormField>

      <TimePicker name='syncTimeOnce' label='Thời gian đồng bộ' onChange={handleSyncTimeOnceChange} />

      <DateField name='startDateOnce' label='Thời điểm bắt đầu' onChange={handleStartDateOnceChange} />
    </div>
  )

  // Hàng ngày
  const renderDailySchedule = () => (
    <div className='grid grid-cols-4 gap-4'>
      <FormField name='scheduleType' label={<RequiredDotLabel label='Thời điểm đồng bộ' />}>
        <Select
          value={scheduleType}
          defaultValue='DAILY'
          onChange={handleScheduleTypeChange}
          options={SCHEDULE_TYPE_VALUES}
        />
      </FormField>

      <FormField name='frequencyDaily' label={<RequiredDotLabel label='Tần suất' />}>
        <Select
          defaultValue='ONCE'
          value={frequencyTypeDaily}
          onChange={handleFrequencyDailyChange}
          options={SCHEDULE_FREQUENCY_VALUES}
        />
      </FormField>

      {frequencyTypeDaily === 'ONCE' ? (
        <TimePicker name='syncTimeDaily' label='Thời gian đồng bộ' onChange={handleSyncTimeDailyChange} />
      ) : (
        <FormField
          name='syncTimeDailyRange'
          label={<RequiredDotLabel label='Thời gian đồng bộ' />}
          initialValue={[dayjs().hour(1).minute(0), dayjs().hour(1).minute(0)]}
        >
          <DatePicker.RangePicker
            picker='time'
            format='HH:mm'
            placeholder={['Từ', 'Đến']}
            className='w-full'
            showNow={false}
            onChange={handleSyncTimeDailyRangeChange}
          />
        </FormField>
      )}

      <DateField name='startDateDaily' label='Thời điểm bắt đầu' onChange={handleStartDateDailyChange} />
    </div>
  )

  // Ngày trong tuần
  const renderWeeklySchedule = () => (
    <div>
      <div className='grid grid-cols-3 gap-2'>
        <FormField name='scheduleType' label={<RequiredDotLabel label='Thời điểm đồng bộ' />}>
          <Select
            defaultValue='WEEKLY'
            value={scheduleType}
            onChange={handleScheduleTypeChange}
            options={SCHEDULE_TYPE_VALUES}
          />
        </FormField>

        <FormField name='weekDays' label={<RequiredDotLabel label='Chọn ngày' />}>
          <Select
            mode='multiple'
            value={selectedWeekDays}
            onChange={handleWeekDaysChange}
            maxTagCount={2}
            maxTagPlaceholder={omittedValues => `+${omittedValues.length}`}
            options={SCHEDULE_IN_WEEK_VALUES}
            tagRender={props => (
              <Tag
                className='rounded-md bg-primary-blue py-0.5 text-white'
                closable
                onClose={props.onClose}
                closeIcon={<CloseOutlined className='ml-1 text-white' />}
              >
                {props.label}
              </Tag>
            )}
          />
        </FormField>

        <FormField name='frequencyDayInWeek' label={<RequiredDotLabel label='Tần suất' />}>
          <Select
            defaultValue='ONCE'
            value={frequencyTypeWeek}
            onChange={handleFrequencyWeekChange}
            options={SCHEDULE_FREQUENCY_VALUES}
          />
        </FormField>
      </div>

      <div className='mt-4 grid grid-cols-2 gap-4'>
        {frequencyTypeWeek === 'ONCE' ? (
          <TimePicker name='syncTimeDayInWeek' label='Thời gian đồng bộ' onChange={handleSyncTimeWeeklyChange} />
        ) : (
          <FormField
            name='syncTimeDayInWeek'
            label={<RequiredDotLabel label='Thời gian đồng bộ' />}
            initialValue={[dayjs().hour(1).minute(0), dayjs().hour(1).minute(0)]}
          >
            <DatePicker.RangePicker
              picker='time'
              format='HH:mm'
              placeholder={['Từ', 'Đến']}
              className='w-full'
              showNow={false}
              onChange={handleSyncTimeWeeklyRangeChange}
            />
          </FormField>
        )}

        <DateField name='startDateDayInWeek' label='Thời điểm bắt đầu' onChange={handleStartDateWeeklyChange} />
      </div>
    </div>
  )

  // Ngày trong tháng
  const renderMonthlySchedule = () => (
    <div>
      <div className='grid grid-cols-3 gap-4'>
        <FormField name='scheduleType' label={<RequiredDotLabel label='Thời điểm đồng bộ' />}>
          <Select
            defaultValue='MONTHLY'
            value={scheduleType}
            onChange={handleScheduleTypeChange}
            options={SCHEDULE_TYPE_VALUES}
          />
        </FormField>

        <FormField name='frequencyDayInMonth' label={<RequiredDotLabel label='Tần suất' />}>
          <Select
            defaultValue='FIXED_DATE'
            value={frequencyTypeMonth}
            onChange={handleFrequencyMonthChange}
            options={FREQUENCY_TYPE_MONTH_VALUES}
          />
        </FormField>

        {frequencyTypeMonth === 'FIXED_DATE' ? (
          <FormField name='dayOfMonth' label={<RequiredDotLabel label='Chọn ngày' />}>
            <Select placeholder='Chọn ngày' options={DAY_IN_MONTH} onChange={handleDayOfMonthChange} />
          </FormField>
        ) : (
          <FormField
            name='weekOfMonth'
            label={<RequiredDotLabel label='Chọn thứ ... của ... tuần' />}
            className='mb-0 flex flex-row'
          >
            <Select
              className='w-1/2'
              placeholder='Chọn thứ'
              options={SCHEDULE_IN_WEEK_VALUES}
              onChange={handleDayOfWeekChange}
            />
            <Select
              className='w-1/2'
              placeholder='Chọn tuần'
              options={SCHEDULE_WEEK_OF_MONTH_VALUES}
              onChange={handleWeekChange}
            />
          </FormField>
        )}
      </div>

      <div className='mt-4 grid grid-cols-2 gap-4'>
        <TimePicker name='syncTime' label='Thời gian đồng bộ' onChange={handleSyncTimeMonthChange} />

        <DateField name='startDateDayInMonth' label='Thời điểm bắt đầu' onChange={handleStartDateMonthChange} />
      </div>
    </div>
  )

  const renderScheduleContent = () => {
    const scheduleRenderers = {
      ONCE: renderOnceSchedule,
      DAILY: renderDailySchedule,
      WEEKLY: renderWeeklySchedule,
      MONTHLY: renderMonthlySchedule
    }

    return (
      scheduleRenderers[scheduleType as keyof typeof scheduleRenderers]?.() || (
        <div className='grid grid-cols-4 gap-4'>
          <FormField name='scheduleType' label={<RequiredDotLabel label='Thời điểm đồng bộ' />}>
            <Select
              placeholder='Chọn loại lịch trình'
              onChange={handleScheduleTypeChange}
              value={scheduleType}
              options={SCHEDULE_TYPE_VALUES}
            />
          </FormField>
        </div>
      )
    )
  }

  return (
    <CustomScheduleSync>
      <div className='variant-container rounded-md'>{renderScheduleContent()}</div>
    </CustomScheduleSync>
  )
})

export const CustomScheduleSync = styled.div`
  .ant-form-item .ant-form-item-label > label {
    font-size: 12px !important;
    font-weight: 500 !important;
    color: #0f1319 !important;
  }
`
