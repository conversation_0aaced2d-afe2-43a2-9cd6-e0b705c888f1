'use client'

import { Empty, Form, Select } from 'antd'

import { SectionHeader } from '../../../../shared/section-header/SectionHeader'
import { NotificationConfig } from './NotificationConfig'
// import { RequiredDotLabel } from '@/components/label/RequiredDotLabel'
import { WebhookConfig } from './WebhookConfig'

interface ActionSectionProps {
  collapsed: boolean
  onToggle: () => void
  actionType: string
  authType: string
  handleActionTypeChange: (value: string) => void
  handleAuthTypeChange: (value: string) => void
}

export const ActionSection: React.FC<ActionSectionProps> = ({
  collapsed,
  onToggle,
  actionType,
  // authType,
  handleActionTypeChange
  // handleAuthTypeChange
}) => {
  return (
    <div className='mb-5 rounded bg-gray-alpha-1'>
      <SectionHeader title='Hành động sau khi chuyển đổi trạng thái' collapsed={collapsed} onToggle={onToggle} />

      {!collapsed && (
        <div className='p-4'>
          <Form.Item name='actionType' label='Hành động'>
            <Select
              placeholder='Chọn hành động'
              allowClear
              onChange={value => handleActionTypeChange(value as string)}
              onClear={() => handleActionTypeChange('')}
              options={[
                { value: 'webhook', label: 'Gửi webhook đến hệ thống khác' },
                { value: 'notification', label: 'Gửi thông báo' },
                { value: 'task', label: 'Tạo task mới' }
              ]}
              value={actionType || undefined}
              notFoundContent={<Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description='Không có dữ liệu' />}
            />
          </Form.Item>

          {actionType === 'webhook' && <WebhookConfig backgroundColor='bg-white' />}
          {actionType === 'notification' && <NotificationConfig />}
        </div>
      )}
    </div>
  )
}
