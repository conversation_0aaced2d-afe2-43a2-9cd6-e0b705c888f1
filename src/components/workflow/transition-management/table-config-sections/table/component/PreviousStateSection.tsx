'use client'

import { Empty, Form, Select } from 'antd'

import { useQuery } from '@tanstack/react-query'

import { SectionHeader } from '../../../../shared/section-header/SectionHeader'
import { stateTransitionConfig } from '@/models/workflow/stateTransitionConfig'
import { RequiredDotLabel } from '@/components/label/RequiredDotLabel'

interface PreviousStateSectionProps {
  collapsed: boolean
  onToggle: () => void
  onStateChange: (states: { id: string; name: string }[]) => void
  idsIgnore?: string[]
}

export const PreviousStateSection: React.FC<PreviousStateSectionProps> = ({
  collapsed,
  onToggle,
  onStateChange,
  idsIgnore = []
}) => {
  const { data: comboboxPreviousState, isLoading } = useQuery({
    queryKey: ['combobox-previous-state', idsIgnore],
    queryFn: () =>
      stateTransitionConfig.getComboboxPreviousState({
        page: 0,
        size: 100,
        search: '',
        idsIgnore: idsIgnore.length > 0 ? idsIgnore : undefined
      }),
    enabled: true // Luôn cho phép query chạy
  })

  // Loại bỏ refetch thủ công để tránh gọi API mỗi lần gõ ký tự ở nơi khác gây re-render

  const options = comboboxPreviousState?.content || []

  return (
    <div className='mb-5 rounded bg-gray-alpha-1'>
      <SectionHeader title='Trạng thái tiền nhiệm' collapsed={collapsed} onToggle={onToggle} />

      {!collapsed && (
        <div className='p-4'>
          <Form.Item
            name='previousStates'
            label={<RequiredDotLabel label='Trạng thái tiền nhiệm được phép chuyển đến' />}
            tooltip='Dạng ô lựa chọn đa giá trị (multi-select), cho phép chọn 1 hoặc nhiều trạng thái trước đó mà trạng thái hiện tại được phép chuyển từ đó đến.'
            rules={[
              {
                required: true,
                type: 'array',
                min: 1,
                message: 'Vui lòng chọn ít nhất 1 trạng thái tiền nhiệm'
              }
            ]}
          >
            <Select
              mode='multiple'
              placeholder='Chọn trạng thái tiền nhiệm'
              options={options.map((item: any) => ({
                value: item.id,
                label: item.name
              }))}
              loading={isLoading}
              onChange={(values: string[]) => {
                const selectedStates = values.map(value => ({
                  id: value,
                  name: options.find((opt: any) => opt.id === value)?.name || ''
                }))

                onStateChange(selectedStates)
              }}
              maxTagCount={4}
              className='w-full'
              notFoundContent={<Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description='Không có dữ liệu' />}
            />
          </Form.Item>
        </div>
      )}
    </div>
  )
}
