'use client'

import React, { useEffect, useMemo, useRef, useState } from 'react'

import { Empty, Input, Modal, Spin, Tabs } from 'antd'

import { useMailTemplates } from '@/hooks/workflow/useMailTemplates'

interface MailTemplatePickerModalProps {
  open: boolean
  onCancel: () => void
  onConfirm: (template: any | null) => void
}

interface MailTemplateItem {
  id: number
  code: string
  name: string
  titleDefault?: string
  contentHtml?: string
  emailType?: number // 1: Doanh nghiệp/HKD, 0: C<PERSON> nhân (theo thực tế payload)
}

export const MailTemplatePickerModal: React.FC<MailTemplatePickerModalProps> = ({ open, onCancel, onConfirm }) => {
  const [templates, setTemplates] = useState<MailTemplateItem[]>([])
  const [activeTab, setActiveTab] = useState<'ENTERPRISE' | 'PERSONAL'>('ENTERPRISE')
  const [searchText, setSearchText] = useState('')
  const [selected, setSelected] = useState<MailTemplateItem | null>(null)

  const emailType = activeTab === 'ENTERPRISE' ? 0 : 1
  const { data, isLoading, error } = useMailTemplates(open, { emailType })

  useEffect(() => {
    if (data && Array.isArray(data)) {
      setTemplates(data)
      setSelected(data[0] || null)
    }
  }, [data])

  const filtered = useMemo(() => {
    const typeFilter = activeTab === 'ENTERPRISE' ? 1 : 0
    const list = templates.filter(it => (it?.emailType ?? -1) === typeFilter || (it?.emailType ?? -1) === -1)

    if (!searchText) return list

    const q = searchText.toLowerCase().trim()

    return list.filter(it => `${it.name} ${it.code}`.toLowerCase().includes(q))
  }, [templates, activeTab, searchText])

  const tabItems = [
    { key: 'ENTERPRISE', label: <span className='text-sm'>Doanh nghiệp/ HKD</span> },
    { key: 'PERSONAL', label: <span className='text-sm'>Cá nhân</span> }
  ]

  const TemplateThumb: React.FC<{ html?: string }> = ({ html }) => {
    const wrapperRef = useRef<HTMLDivElement | null>(null)
    const contentRef = useRef<HTMLDivElement | null>(null)

    useEffect(() => {
      const adjust = () => {
        const wrapper = wrapperRef.current
        const content = contentRef.current

        if (!wrapper || !content) return

        const cw = content.scrollWidth
        const ch = content.scrollHeight

        if (cw === 0 || ch === 0) return
      }

      // Defer to next paint so DOM has size
      const id = requestAnimationFrame(adjust)

      return () => cancelAnimationFrame(id)
    }, [html])

    return (
      <div ref={wrapperRef} className='flex h-[400px] w-auto overflow-hidden rounded border bg-transparent'>
        <div
          ref={contentRef}
          className='ck-content'
          style={{
            transform: `scale(0.42)`,
            transformOrigin: 'top left',
            maxWidth: '100%'
            // width: scale ? `${100 / scale}%` : '100%'
          }}
          dangerouslySetInnerHTML={{ __html: html || '' }}
        />
      </div>
    )
  }

  return (
    <Modal
      open={open}
      onCancel={onCancel}
      onOk={() => onConfirm(selected)}
      title={<span>Chọn file mẫu</span>}
      width={1200}
      okText='Xác nhận'
      cancelText='Hủy'
    >
      <div className='flex h-[70vh] w-full gap-4'>
        {/* Preview bên trái */}
        <div className='w-1/2 overflow-hidden rounded border border-gray-3 bg-white'>
          <div className='size-full overflow-auto p-6'>
            {selected?.contentHtml ? (
              <div className='ck-content' dangerouslySetInnerHTML={{ __html: selected.contentHtml }} />
            ) : isLoading ? (
              <div className='flex h-full items-center justify-center'>
                <Spin />
              </div>
            ) : (
              <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description='Chưa chọn template' />
            )}
          </div>
        </div>

        {/* Sidebar bên phải */}
        <div className='w-1/2 overflow-hidden rounded border border-gray-3 bg-white'>
          <div className='px-4 pt-3'>
            <Tabs activeKey={activeTab} onChange={k => setActiveTab(k as any)} items={tabItems} />
            <Input.Search
              placeholder='Tìm kiếm'
              allowClear
              onSearch={setSearchText}
              onChange={e => setSearchText(e.target.value)}
              className='mb-3'
            />
          </div>
          <div className='h-[calc(70vh-110px)] overflow-auto p-3'>
            {isLoading ? (
              <div className='flex h-full items-center justify-center'>
                <Spin />
              </div>
            ) : error ? (
              <div className='p-4 text-red-500'>{error.message}</div>
            ) : filtered.length === 0 ? (
              <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description='Không có template' />
            ) : (
              <div className='grid grid-cols-2 gap-4'>
                {filtered.map(item => (
                  <button
                    key={item.id}
                    className={`cursor-pointer rounded-lg border p-3 text-left transition-colors ${
                      selected?.id === item.id
                        ? 'border-primary-blue ring-2 ring-primary-blue/20'
                        : 'border-gray-3 hover:border-gray-4'
                    }`}
                    onClick={() => setSelected(item)}
                  >
                    <div className='mb-2'>
                      <div className='truncate text-sm font-medium'>{item.name}</div>
                    </div>
                    <TemplateThumb html={item.contentHtml} />
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </Modal>
  )
}

export default MailTemplatePickerModal
