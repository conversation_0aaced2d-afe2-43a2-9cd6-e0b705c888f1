'use client'

import React, { useEffect, useState } from 'react'

import { Modal, Table, Select, Button, Empty, Spin } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import { CloseOutlined } from '@ant-design/icons'

import { StyledContent } from '@/components/workflow/shared/table'
import { STATUS_CONDITION_VALUES } from '@/constants/workflow/trigger'

interface DataMappingItem {
  key: string
  field: string
  operator: number
  value: string[] | null
}

interface PopupDataMappingWebhookProps {
  open: boolean
  onClose: () => void
  onConfirm: (data: DataMappingItem[]) => void
  initialData?: DataMappingItem[]
}

export const PopupDataMappingWebhook: React.FC<PopupDataMappingWebhookProps> = ({
  open,
  onClose,
  onConfirm,
  initialData = []
}) => {
  const [data, setData] = useState<DataMappingItem[]>(
    initialData.length > 0 ? initialData : [{ key: '1', field: 'status', operator: 1, value: [] }]
  )

  const [loading] = useState<boolean>(false)

  useEffect(() => {
    if (open && initialData.length > 0) {
      setData(initialData)
    }
  }, [open, initialData])

  const handleAddRow = () => {
    const newKey = (data.length + 1).toString()

    const newRow: DataMappingItem = {
      key: newKey,
      field: 'status',
      operator: 1,
      value: []
    }

    setData([...data, newRow])
  }

  const handleDeleteRow = (key: string) => {
    const newData = data.filter(item => item.key !== key)

    setData(newData)
  }

  const handleFieldChange = (key: string, field: string, value: any) => {
    const newData = data.map(item => {
      if (item.key === key) {
        return { ...item, [field]: value }
      }

      return item
    })

    setData(newData)
  }

  const handleConfirm = () => {
    onConfirm(data)
    onClose()
  }

  const columns: ColumnsType<DataMappingItem> = [
    {
      title: 'Trường',
      dataIndex: 'field',
      key: 'field',
      width: '30%',
      render: (_, record) => (
        <Select
          className='w-full'
          value={record.field}
          onChange={value => handleFieldChange(record.key, 'field', value)}
        >
          <Select.Option value='status'>Trạng thái cài đặt</Select.Option>
        </Select>
      )
    },
    {
      title: 'Hành động',
      dataIndex: 'operator',
      key: 'operator',
      width: '30%',
      render: (_, record) => (
        <Select
          className='w-full'
          value={record.operator}
          onChange={value => handleFieldChange(record.key, 'operator', value)}
        >
          <Select.Option value={1}>=</Select.Option>
        </Select>
      )
    },
    {
      title: 'Giá trị',
      dataIndex: 'value',
      key: 'value',
      width: '30%',
      render: (_, record) => (
        <Select
          className='w-full'
          mode='multiple'
          placeholder='Chọn giá trị'
          value={Array.isArray(record.value) ? record.value : []}
          onChange={value => handleFieldChange(record.key, 'value', value)}
          showSearch
          filterOption={(input, option) =>
            String(option?.label ?? '')
              .toLowerCase()
              .includes(input.toLowerCase())
          }
          maxTagCount={1}
        >
          {STATUS_CONDITION_VALUES.map(item => (
            <Select.Option key={item.value} value={item.value}>
              {item.label}
            </Select.Option>
          ))}
        </Select>
      )
    },
    {
      title: '',
      key: 'action',
      width: '10%',
      render: (_, record, index) => {
        const isLastRow = index === data.length - 1

        if (data.length === 1 || isLastRow) {
          return (
            <Button
              type='text'
              icon={<i className='onedx-plus size-4 text-gray-6' />}
              onClick={handleAddRow}
              className='flex items-center justify-center rounded-lg border border-solid border-icon-neutral-lighter'
            />
          )
        }

        return (
          <Button
            type='text'
            icon={<i className='onedx-delete size-4 text-gray-6' />}
            onClick={() => handleDeleteRow(record.key)}
            className='flex items-center justify-center rounded-lg border border-solid border-icon-neutral-lighter'
          />
        )
      }
    }
  ]

  return (
    <Modal
      title='Ánh xạ dữ liệu'
      open={open}
      onCancel={onClose}
      width={960}
      closeIcon={<CloseOutlined />}
      footer={[
        <div className='flex w-full justify-end gap-4' key='footer'>
          <Button key='cancel' onClick={onClose} className='border-primary-blue bg-white text-text-primary-blue'>
            Đóng
          </Button>
          <Button key='confirm' type='primary' onClick={handleConfirm} className='bg-primary-blue'>
            Xác nhận
          </Button>
        </div>
      ]}
    >
      <div className='mt-4'>
        <Spin spinning={loading}>
          <StyledContent>
            <Table
              columns={columns}
              dataSource={data}
              pagination={false}
              scroll={{ y: 300 }}
              locale={{
                emptyText: <Empty description='Không có dữ liệu.' />
              }}
            />
          </StyledContent>
        </Spin>
      </div>
    </Modal>
  )
}
