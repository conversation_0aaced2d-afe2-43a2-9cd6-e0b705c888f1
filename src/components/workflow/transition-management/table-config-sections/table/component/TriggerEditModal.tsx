'use client'

import React, { useState, useEffect, useRef } from 'react'

import { Modal, Button, Form, Select, Empty } from 'antd'

import { ConditionConfig, type ConditionGroup } from './ConditionConfig'
import { ScheduleSync, type ScheduleSyncRef, type ScheduleSyncMappingResult } from './ScheduleSync'
import { EndpointConfig } from './EndpointConfig'
import { WebhookCard } from './WebhookCard'
import { GreenDotPanel } from '@/components/label/GreenDotPanel'
import { PopupDataMapping } from './PopupDataMapping'
import { PopupDataMappingWebhook } from './PopupDataMappingWebhook'
import { RequiredDotLabel } from '@/components/label/RequiredDotLabel'
import { NestedSectionHeader } from '../../../../shared/section-header'

import {
  RULES_ENGINE_CONDITIONS,
  RULES_ENGINE_CONDITION_VALUES,
  SCHEDULE_CONDITIONS,
  SCHEDULE_CONDITION_VALUES,
  OPERAND_CODE,
  TRIGGER_OPTIONS_CREATE
} from '@/constants/workflow/trigger'

interface TriggerEditModalProps {
  open: boolean
  onClose: () => void
  onConfirm: (triggerData: any) => void
  triggerData: any // Dữ liệu trigger hiện tại
  preStateId?: string | number | null // ID của trạng thái tiền nhiệm
}

// Hàm để lấy operandId từ field
const getOperandIdByField = (field: string): number => {
  // Chuyển field thành key trong OPERAND_CODE
  const fieldUpperCase = field.toUpperCase()

  // Kiểm tra xem field có trong OPERAND_CODE không
  if (fieldUpperCase in OPERAND_CODE) {
    return OPERAND_CODE[fieldUpperCase as keyof typeof OPERAND_CODE]
  }

  // Nếu không tìm thấy, trả về giá trị mặc định
  return 0
}

// Không cần hàm này nữa vì chúng ta đang sử dụng field value trực tiếp

// Hàm để lấy key từ label
const getKeyByFieldLabel = (label: string): string => {
  // Sử dụng RULES_ENGINE_CONDITIONS và SCHEDULE_CONDITIONS để tìm key
  const allConditions = [...RULES_ENGINE_CONDITIONS, ...SCHEDULE_CONDITIONS]

  // Tìm condition có label trùng với label đầu vào
  const condition = allConditions.find(cond => cond.label === label)

  // Nếu tìm thấy, trả về value (key), ngược lại trả về label
  return condition ? condition.value : label
}

// Hàm để lấy field value từ operandId
const getFieldValueByOperandId = (operandId: number): string => {
  // Tìm key tương ứng với operandId
  for (const [fieldKey, id] of Object.entries(OPERAND_CODE)) {
    if (id === operandId) {
      // Trả về fieldKey làm giá trị field
      return fieldKey
    }
  }

  // Nếu không tìm thấy, trả về operandId dưới dạng string
  return operandId.toString()
}

export const TriggerEditModal: React.FC<TriggerEditModalProps> = ({ open, onClose, onConfirm, triggerData }) => {
  const [form] = Form.useForm()
  const [selectedTriggers, setSelectedTriggers] = useState<string[]>([])
  const [ruleEngineConditions, setRuleEngineConditions] = useState<ConditionGroup[]>([])
  const [scheduleConditions, setScheduleConditions] = useState<ConditionGroup[]>([])
  const [webhookData, setWebhookData] = useState<any[]>([])
  const [requestMappingData, setRequestMappingData] = useState<any[]>([])
  const [responseMappingData, setResponseMappingData] = useState<any[]>([])
  const [apiScheduleCollapsed, setApiScheduleCollapsed] = useState(false)
  const [dataMappingOpen, setDataMappingOpen] = useState(false)
  const [dataMappingWebhookOpen, setDataMappingWebhookOpen] = useState(false)
  const [scheduleData, setScheduleData] = useState<ScheduleSyncMappingResult | null>(null)
  const [apiScheduleData, setApiScheduleData] = useState<ScheduleSyncMappingResult | null>(null)

  // Collapsed states per section
  const [manualCollapsed, setManualCollapsed] = useState(false)
  const [apiCollapsed, setApiCollapsed] = useState(false)
  const [webhookCollapsed, setWebhookCollapsed] = useState(false)
  const [scheduleCollapsed, setScheduleCollapsed] = useState(false)
  const [ruleEngineCollapsed, setRuleEngineCollapsed] = useState(false)

  // Refs cho ScheduleSync components
  const scheduleRef = useRef<ScheduleSyncRef>(null)
  const apiScheduleRef = useRef<ScheduleSyncRef>(null)

  // Khởi tạo dữ liệu từ triggerData
  useEffect(() => {
    if (open && triggerData) {
      // Xác định loại trigger từ dữ liệu
      const triggerTypes: string[] = []

      if (Array.isArray(triggerData)) {
        triggerData.forEach((trigger: any) => {
          if (typeof trigger === 'string') {
            triggerTypes.push(trigger)
          } else if (trigger && trigger.type) {
            triggerTypes.push(trigger.type)
          }
        })
      }

      setSelectedTriggers(triggerTypes)

      // Khởi tạo các dữ liệu khác từ triggerData
      if (triggerData) {
        // Khởi tạo rule engine conditions
        const ruleEngineTrigger = Array.isArray(triggerData)
          ? triggerData.find((t: any) => t.type === 'RULE_ENGINE')
          : null

        if (ruleEngineTrigger && ruleEngineTrigger.ruleEngine && ruleEngineTrigger.ruleEngine.conditions) {
          // Lấy conditions từ ruleEngine
          const apiConditions = ruleEngineTrigger.ruleEngine.conditions

          if (Array.isArray(apiConditions)) {
            const conditionGroups: ConditionGroup[] = apiConditions.map(condition => {
              // Mỗi ifconds trong condition sẽ trở thành một condition trong ConditionGroup
              const conditions = Array.isArray(condition.ifconds)
                ? condition.ifconds.map((ifcond: any) => {
                    // Lấy field value từ operandId để khớp với options trong Select
                    const fieldValue = getFieldValueByOperandId(ifcond.operandId || parseInt(ifcond.key))

                    return {
                      id: ifcond.id?.toString() || '',
                      field: fieldValue, // Sử dụng field value từ RULES_ENGINE_CONDITIONS
                      operator: ifcond.operator, // Giữ nguyên giá trị số của operator
                      value: ifcond.data?.value || []
                    }
                  })
                : []

              return {
                id: condition.id?.toString() || '',
                conditions
              }
            })

            setRuleEngineConditions(conditionGroups)
          }
        }

        // Khởi tạo schedule conditions và schedule data
        const scheduleTrigger = Array.isArray(triggerData) ? triggerData.find((t: any) => t.type === 'SCHEDULE') : null

        if (scheduleTrigger && scheduleTrigger.schedule) {
          // Tách riêng phần schedule config (không bao gồm conditions)
          const scheduleConfigData = { ...scheduleTrigger.schedule }

          delete scheduleConfigData.conditions

          // Lưu trữ dữ liệu schedule để sử dụng sau này
          setScheduleData(scheduleConfigData)

          // Xử lý conditions nếu có
          if (scheduleTrigger.schedule.conditions && Array.isArray(scheduleTrigger.schedule.conditions)) {
            // Lấy conditions từ schedule
            const apiConditions = scheduleTrigger.schedule.conditions

            // Chuyển đổi từ định dạng API sang định dạng ConditionGroup
            // Tương tự như cách xử lý rule engine conditions
            const conditionGroups: ConditionGroup[] = apiConditions.map(
              (condition: { ifconds: any[]; id: { toString: () => any } }) => {
                // Mỗi ifconds trong condition sẽ trở thành một condition trong ConditionGroup
                const conditions = Array.isArray(condition.ifconds)
                  ? condition.ifconds.map((ifcond: any) => {
                      // Lấy field value từ operandId để khớp với options trong Select
                      const fieldValue = getFieldValueByOperandId(ifcond.operandId || parseInt(ifcond.key))

                      return {
                        id: ifcond.id?.toString() || '',
                        field: fieldValue,
                        operator: ifcond.operator,
                        value: ifcond.data?.value || []
                      }
                    })
                  : []

                return {
                  id: condition.id?.toString() || '',
                  conditions
                }
              }
            )

            setScheduleConditions(conditionGroups)
          }
        }

        // Khởi tạo webhook data
        const webhookTrigger = Array.isArray(triggerData) ? triggerData.find((t: any) => t.type === 'WEBHOOK') : null

        if (webhookTrigger && webhookTrigger.webhook) {
          setWebhookData([webhookTrigger.webhook])
        }

        // Khởi tạo Manual trigger data
        const manualTrigger = Array.isArray(triggerData) ? triggerData.find((t: any) => t.type === 'MANUAL') : null

        if (manualTrigger && manualTrigger.manual) {
          // Set giá trị cho form fields của Manual trigger
          form.setFieldsValue({
            actor: manualTrigger.manual.agentTypes?.[0] || 'ADMIN',
            role: manualTrigger.manual.roles?.[0] || 'ADMIN'
          })
        }

        // Khởi tạo API mapping data + schedule cho API
        const apiMappingTrigger = Array.isArray(triggerData) ? triggerData.find((t: any) => t.type === 'API') : null

        if (apiMappingTrigger) {
          if (apiMappingTrigger.requestMapping) {
            setRequestMappingData(apiMappingTrigger.requestMapping)
          }

          if (apiMappingTrigger.responseMapping) {
            setResponseMappingData(apiMappingTrigger.responseMapping)
          }

          if (apiMappingTrigger.schedule) {
            setApiScheduleData(apiMappingTrigger.schedule)
          }
        }
      }
    }
  }, [open, triggerData, form])

  // Xử lý khi xác nhận
  const handleConfirm = async () => {
    try {
      const values = await form.validateFields()

      // Tạo dữ liệu trigger mới dựa trên loại trigger đã chọn
      const newTriggers: any[] = []

      // Xử lý Manual trigger
      if (selectedTriggers.includes(TRIGGER_OPTIONS_CREATE.MANUAL)) {
        newTriggers.push({
          type: 'MANUAL',
          manual: {
            agentTypes: [values.actor],
            roles: [values.role]
          }
        })
      }

      // Xử lý API trigger
      if (selectedTriggers.includes(TRIGGER_OPTIONS_CREATE.API)) {
        // Lấy thông tin endpoint từ form
        const endpointValues = form.getFieldValue('trigger_endpoint') || {}

        // Tạo inputMapping từ request data
        const inputMapping = requestMappingData
          .filter((item: any) => item.defaultValue && item.defaultValue !== 'Chọn tham số')
          .map((item: any) => ({
            dest: item.field, // Trường đối tác
            source: item.defaultValue, // Tham số đầu vào
            isRequired: item.required, // Bắt buộc
            type: item.type // Kiểu dữ liệu
          }))

        // Tạo outputMapping từ response data
        const outputMapping = responseMappingData
          .filter((item: any) => item.targetField && item.value)
          .map((item: any) => ({
            source: item.targetField, // Trường đối tác
            dest: item.sourceField, // Trường nguồn
            value: item.value // Giá trị
          }))

        // Tạo constraints từ outputMapping
        const constraints = outputMapping
          .filter((item: any) => item.source && item.value)
          .map((item: any) => ({
            field: item.source,
            values: [item.value]
          }))

        const formatAuthConfig = () => {
          const baseAuth = {
            type: endpointValues.authMethod || 'bearer'
          }

          if (endpointValues.authMethod === 'api_key') {
            // Nếu là 'apikey' thì chỉ truyền apikey
            return {
              ...baseAuth,
              apikey: [
                {
                  type: endpointValues.keyPlacement,
                  key: endpointValues.keyValue,
                  value: endpointValues.apiKeyValue
                }
              ]
            }
          } else {
            // Nếu không phải 'apikey' thì chỉ truyền bearer
            return {
              ...baseAuth,
              bearer: {
                key: 'Authorization',
                value: endpointValues.tokenValue
              }
            }
          }
        }

        // Lấy dữ liệu schedule cho API từ ref
        const apiSchedule = apiScheduleRef.current?.mapScheduleSyncValues() || null

        newTriggers.push({
          type: TRIGGER_OPTIONS_CREATE.API,
          api: {
            constraints: constraints.length > 0 ? constraints : null,
            apiConfig: {
              method: endpointValues.connectionMethod,
              headers: endpointValues.headers,
              url: {
                raw: endpointValues.rawUrl,
                variables:
                  form.getFieldValue('params')?.map((param: any) => ({
                    key: param.key,
                    value: param.value
                  })) || []
              },
              auth: formatAuthConfig(),
              securities: {
                hmac: {
                  field: 'SECURE_CODE',
                  participants: ['code', 'data'],
                  encode: 'SHA256'
                }
              }
            },
            outputMapping: outputMapping.length > 0 ? outputMapping : null,
            inputMapping: inputMapping.length > 0 ? inputMapping : null
          },
          schedule: apiSchedule
        })
      }

      // Xử lý Webhook trigger
      if (selectedTriggers.includes(TRIGGER_OPTIONS_CREATE.WEBHOOK)) {
        newTriggers.push({
          type: TRIGGER_OPTIONS_CREATE.WEBHOOK,
          webhook: {
            action: 'aliqua',
            status: webhookData[0].status
          }
        })
      }

      // Xử lý Schedule trigger
      if (selectedTriggers.includes(TRIGGER_OPTIONS_CREATE.SCHEDULE)) {
        // Lấy dữ liệu schedule từ ScheduleSync component thông qua ref
        const scheduleData = scheduleRef.current?.mapScheduleSyncValues() || null

        // Chuyển đổi từ ConditionGroup sang cấu trúc API
        // Xử lý giống như rule engine
        const apiConditions = scheduleConditions.map(group => ({
          id: parseInt(group.id) || 0,
          key: group.id,
          ifconds: group.conditions.map(condition => {
            // Chuyển đổi từ label sang key
            const fieldKey = getKeyByFieldLabel(condition.field)
            const operandId = getOperandIdByField(fieldKey)

            return {
              id: parseInt(condition.id) || 0,
              key: operandId.toString(), // Sử dụng operandId làm key
              operator: condition.operator,
              operandId: operandId,
              data: {
                value: condition.value
              }
            }
          })
        }))

        newTriggers.push({
          type: 'SCHEDULE',
          schedule: {
            ...scheduleData,
            conditions: apiConditions.length > 0 ? apiConditions : null
          }
        })
      }

      // Xử lý Rule Engine trigger
      if (selectedTriggers.includes(TRIGGER_OPTIONS_CREATE.RULE_ENGINE)) {
        // Chuyển đổi từ ConditionGroup sang cấu trúc API
        const apiConditions = ruleEngineConditions.map(group => ({
          id: parseInt(group.id) || 0,
          key: group.id,
          ifconds: group.conditions.map(condition => {
            // Chuyển đổi từ label sang key
            const fieldKey = getKeyByFieldLabel(condition.field)
            const operandId = getOperandIdByField(fieldKey)

            return {
              id: parseInt(condition.id) || 0,
              key: operandId.toString(), // Sử dụng operandId làm key
              operator: condition.operator,
              operandId: operandId,
              data: {
                value: condition.value
              }
            }
          })
        }))

        newTriggers.push({
          type: TRIGGER_OPTIONS_CREATE.RULE_ENGINE,
          ruleEngine: {
            conditions: apiConditions
          }
        })
      }

      // Gọi hàm onConfirm với dữ liệu mới
      onConfirm(newTriggers)
      onClose()
    } catch (error) {
      console.error('Validation failed:', error)
    }
  }

  // Helper remove trigger
  const removeTrigger = (key: string) => {
    setSelectedTriggers(prev => prev.filter(item => item !== key))
  }

  return (
    <Modal
      title='Chỉnh sửa nhanh "Trigger"'
      open={open}
      onCancel={onClose}
      width={1000}
      footer={[
        <Button key='cancel' onClick={onClose}>
          Huỷ
        </Button>,
        <Button key='confirm' type='primary' onClick={handleConfirm} className='bg-primary-blue'>
          Xác nhận
        </Button>
      ]}
    >
      <Form form={form} layout='vertical'>
        {selectedTriggers.length === 0 && <Empty description='Không có trigger nào được chọn' />}

        {/* Manual */}
        {selectedTriggers.includes(TRIGGER_OPTIONS_CREATE.MANUAL) && (
          <div className='mb-4 overflow-hidden rounded-lg bg-[#F9F9FA]'>
            <NestedSectionHeader
              title='Manual'
              collapsed={manualCollapsed}
              onToggle={() => setManualCollapsed(!manualCollapsed)}
              showDelete={true}
              onDelete={() => removeTrigger(TRIGGER_OPTIONS_CREATE.MANUAL)}
            />
            {!manualCollapsed && (
              <div className='p-4'>
                <div className='grid grid-cols-2 gap-4'>
                  <div>
                    <Form.Item
                      name='actor'
                      label={<RequiredDotLabel label='Tác nhân được cập nhật trạng thái' />}
                      rules={[{ required: true, message: 'Tác nhân thực hiện không được bỏ trống' }]}
                    >
                      <Select
                        placeholder='Chọn tác nhân thực hiện'
                        options={[
                          { value: 'ADMIN', label: 'Admin' },
                          { value: 'PARTNER', label: 'Đối tác' }
                        ]}
                        notFoundContent={<Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description='Không có dữ liệu' />}
                      />
                    </Form.Item>
                  </div>
                  <div>
                    <Form.Item
                      name='role'
                      label={<RequiredDotLabel label='Vai trò' />}
                      rules={[{ required: true, message: 'Vai trò không được bỏ trống' }]}
                    >
                      <Select
                        placeholder='Chọn vai trò'
                        options={[
                          { value: 'ADMIN', label: 'Admin' },
                          { value: 'PARTNER', label: 'Đối tác' }
                        ]}
                        notFoundContent={<Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description='Không có dữ liệu' />}
                      />
                    </Form.Item>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* API */}
        {selectedTriggers.includes(TRIGGER_OPTIONS_CREATE.API) && (
          <div className='mb-4 overflow-hidden rounded-lg bg-[#F9F9FA]'>
            <NestedSectionHeader
              title='API Call'
              collapsed={apiCollapsed}
              onToggle={() => setApiCollapsed(!apiCollapsed)}
              showDelete={true}
              onDelete={() => removeTrigger(TRIGGER_OPTIONS_CREATE.API)}
            />
            <div className={`${apiCollapsed ? 'hidden' : 'p-4'}`}>
              <EndpointConfig
                name='trigger_endpoint'
                initialData={
                  Array.isArray(triggerData)
                    ? triggerData.find((t: any) => t.type === 'API')?.api?.apiConfig
                    : undefined
                }
                key={JSON.stringify(triggerData)}
              />

              <GreenDotPanel
                label='Lịch trình đồng bộ'
                collapsed={apiScheduleCollapsed}
                onToggle={() => setApiScheduleCollapsed(!apiScheduleCollapsed)}
                backgroundColor={'bg-[#F3F4F5]'}
              >
                <ScheduleSync id='api-schedule' ref={apiScheduleRef} initialData={apiScheduleData} />
              </GreenDotPanel>

              <div className='mt-6 flex items-center justify-between rounded-lg bg-[#F3F4F5] p-3'>
                <span className="body-14-regular relative items-center justify-center pl-4 text-gray-11 before:absolute before:left-0 before:top-1/2 before:size-2 before:-translate-y-1/2 before:rounded-full before:bg-green-6 before:content-['']">
                  Ánh xạ dữ liệu
                </span>
                <Button type='primary' className='bg-primary-blue' onClick={() => setDataMappingOpen(true)}>
                  Cài đặt file
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Webhook */}
        {selectedTriggers.includes(TRIGGER_OPTIONS_CREATE.WEBHOOK) && (
          <div className='mb-4 overflow-hidden rounded-lg bg-[#F9F9FA]'>
            <NestedSectionHeader
              title='Webhook'
              collapsed={webhookCollapsed}
              onToggle={() => setWebhookCollapsed(!webhookCollapsed)}
              showDelete={true}
              onDelete={() => removeTrigger(TRIGGER_OPTIONS_CREATE.WEBHOOK)}
            />
            <div className={`${webhookCollapsed ? 'hidden' : 'p-4'}`}>
              <WebhookCard initialData={webhookData} onDataMappingChange={data => setWebhookData(data)} />
            </div>
          </div>
        )}

        {/* Schedule */}
        {selectedTriggers.includes(TRIGGER_OPTIONS_CREATE.SCHEDULE) && (
          <div className='mb-4 overflow-hidden rounded-lg bg-[#F9F9FA]'>
            <NestedSectionHeader
              title='Lịch hẹn'
              collapsed={scheduleCollapsed}
              onToggle={() => setScheduleCollapsed(!scheduleCollapsed)}
              showDelete={true}
              onDelete={() => removeTrigger(TRIGGER_OPTIONS_CREATE.SCHEDULE)}
            />
            <div className={`${scheduleCollapsed ? 'hidden' : 'block'} p-4`}>
              <ScheduleSync id='schedule' ref={scheduleRef} initialData={scheduleData} />
              <div className='mt-4'>
                <ConditionConfig
                  conditions={SCHEDULE_CONDITIONS}
                  conditionValues={SCHEDULE_CONDITION_VALUES}
                  value={scheduleConditions}
                  onChange={setScheduleConditions}
                />
              </div>
            </div>
          </div>
        )}

        {/* Rule Engine */}
        {selectedTriggers.includes(TRIGGER_OPTIONS_CREATE.RULE_ENGINE) && (
          <div className='mb-4 overflow-hidden rounded-lg bg-[#F9F9FA]'>
            <NestedSectionHeader
              title='Rule Engine'
              collapsed={ruleEngineCollapsed}
              onToggle={() => setRuleEngineCollapsed(!ruleEngineCollapsed)}
              showDelete={true}
              onDelete={() => removeTrigger(TRIGGER_OPTIONS_CREATE.RULE_ENGINE)}
            />
            {!ruleEngineCollapsed && (
              <div className='p-4'>
                <ConditionConfig
                  conditions={RULES_ENGINE_CONDITIONS}
                  conditionValues={RULES_ENGINE_CONDITION_VALUES}
                  value={ruleEngineConditions}
                  onChange={setRuleEngineConditions}
                />
              </div>
            )}
          </div>
        )}
      </Form>

      {/* Cấu hình popup data mapping */}
      <PopupDataMapping
        open={dataMappingOpen}
        onClose={() => setDataMappingOpen(false)}
        onConfirm={data => {
          setRequestMappingData(data.request || [])
          setResponseMappingData(data.response || [])
          setDataMappingOpen(false)
        }}
        triggerData={triggerData.find((t: any) => t.type === 'API')?.api}
      />
      <PopupDataMappingWebhook
        open={dataMappingWebhookOpen}
        onClose={() => setDataMappingWebhookOpen(false)}
        onConfirm={() => {
          setDataMappingWebhookOpen(false)
        }}
      />
    </Modal>
  )
}
