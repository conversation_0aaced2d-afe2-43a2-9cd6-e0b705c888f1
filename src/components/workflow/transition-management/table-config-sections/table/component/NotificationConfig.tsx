'use client'

import React, { useEffect, useState } from 'react'

import { Form, Input, Radio, Checkbox } from 'antd'

import { RequiredDotLabel } from '@/components/label/RequiredDotLabel'
import { MailTemplatePickerModal } from './MailTemplatePickerModal'

interface NotificationConfigProps {
  backgroundColor?: string
}

export const NotificationConfig: React.FC<NotificationConfigProps> = ({ backgroundColor = 'bg-transparent' }) => {
  const [contentType, setContentType] = useState<'TEMPLATE' | 'CUSTOM'>('TEMPLATE')
  const [templateModalOpen, setTemplateModalOpen] = useState(false)

  const [selectedNotificationTypes, setSelectedNotificationTypes] = useState<{
    email: boolean
    notification: boolean
  }>({
    email: true,
    notification: true
  })

  const handleNotificationTypeChange = (e: any) => {
    const next = e.target.value

    setContentType(next)

    try {
      // ensure a blank line for linter separation
      if (next === 'CUSTOM') {
        form?.setFieldsValue?.({
          notifications: {
            email: {
              contentType: 'CUSTOM',
              templateCode: null
            }
          }
        })
      } else {
        form?.setFieldsValue?.({
          notifications: {
            email: {
              contentType: 'TEMPLATE'
            }
          }
        })
      }
    } catch {}
  }

  const handleNotificationTypeToggle = (type: 'email' | 'notification') => {
    setSelectedNotificationTypes(prev => {
      const next = { ...prev, [type]: !prev[type] }

      try {
        form?.setFieldsValue?.({
          notifications: {
            selectedTypes: next
          }
        })
      } catch {}

      return next
    })
  }

  const form = Form.useFormInstance?.() as any

  useEffect(() => {
    try {
      form?.setFieldsValue?.({
        notifications: {
          selectedTypes: selectedNotificationTypes
        }
      })
    } catch {}
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const handleTemplateConfirm = (tpl: any | null) => {
    setTemplateModalOpen(false)

    if (!tpl) return

    const templateCode = tpl?.code || ''

    try {
      form?.setFieldsValue?.({
        notifications: {
          email: {
            notificationType: selectedNotificationTypes.email ? 'EMAIL' : 'NOTIFICATION',
            templateCode,
            contentType,
            customContent: null,
            customTitle: null
          }
        }
      })
    } catch {}
  }

  return (
    <div className={`p-0 ${backgroundColor}`}>
      {/* Gửi thông báo */}
      <div className='mb-4 w-full'>
        <div className='caption-12-medium mb-2 text-text-neutral-strong'>Gửi thông báo</div>
        <div className='flex gap-6'>
          <Checkbox
            checked={selectedNotificationTypes.email}
            onChange={() => handleNotificationTypeToggle('email')}
            className='body-14-regular text-text-neutral-strong'
          >
            Email
          </Checkbox>
          <Checkbox
            checked={selectedNotificationTypes.notification}
            onChange={() => handleNotificationTypeToggle('notification')}
            className='body-14-regular text-text-neutral-strong'
          >
            Notification
          </Checkbox>
        </div>
      </div>

      {/* Nội dung thông báo */}
      {selectedNotificationTypes.email || selectedNotificationTypes.notification ? (
        <div className='mb-4'>
          <div className='mb-4'>
            <div className='caption-12-medium mb-2 text-text-neutral-strong'>Nội dung thông báo</div>
            <div
              className={`grid gap-4 ${selectedNotificationTypes.email && selectedNotificationTypes.notification ? 'grid-cols-2' : 'grid-cols-1'}`}
            >
              {/* Email */}
              {selectedNotificationTypes.email && (
                <div className='rounded-lg border border-solid border-gray-200 bg-white p-3'>
                  <div className='caption-14-medium mb-3 text-text-neutral-strong'>Email</div>
                  <div className='mb-4'>
                    <Radio.Group value={contentType} onChange={handleNotificationTypeChange} className='flex gap-6'>
                      <Radio value='TEMPLATE' className='text-gray-700 transition-colors hover:text-blue-600'>
                        <span className='font-medium'>File mẫu</span>
                      </Radio>
                      <Radio value='CUSTOM' className='text-gray-700 transition-colors hover:text-blue-600'>
                        <span className='font-medium'>Tùy chỉnh</span>
                      </Radio>
                    </Radio.Group>
                  </div>

                  {contentType === 'TEMPLATE' ? (
                    <div
                      className='flex h-[120px] w-full cursor-pointer items-center justify-center rounded-lg border-2 border-dashed border-blue-300 bg-blue-50 transition-colors hover:border-blue-400 hover:bg-blue-100'
                      onClick={() => setTemplateModalOpen(true)}
                    >
                      <div className='flex flex-col items-center justify-center'>
                        <div className='mb-3 flex size-12 items-center justify-center rounded-lg border border-blue-200 bg-white shadow-sm'>
                          <svg
                            width='24'
                            height='24'
                            viewBox='0 0 24 24'
                            fill='none'
                            xmlns='http://www.w3.org/2000/svg'
                          >
                            <path
                              d='M13 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V9L13 2Z'
                              stroke='#2563eb'
                              strokeWidth='2'
                              strokeLinecap='round'
                              strokeLinejoin='round'
                            />
                            <path
                              d='M13 2V9H20'
                              stroke='#2563eb'
                              strokeWidth='2'
                              strokeLinecap='round'
                              strokeLinejoin='round'
                            />
                          </svg>
                        </div>
                        <div className='text-sm font-medium text-blue-600'>Chọn file mẫu</div>
                        <div className='mt-1 text-xs text-gray-500'>Kéo thả hoặc click để chọn file</div>
                      </div>
                    </div>
                  ) : (
                    <div className='space-y-4'>
                      {/* Tiêu đề email */}
                      <div>
                        <Form.Item
                          label={<RequiredDotLabel label='Tiêu đề email' />}
                          name={['notifications', 'email', 'customTitle']}
                          required
                        >
                          <Input placeholder='Nhập tiêu đề email' className='w-full' />
                        </Form.Item>
                      </div>

                      {/* Nội dung */}
                      <div>
                        <div className='caption-12-medium mb-2 text-text-neutral-strong'>Nội dung</div>
                        <Form.Item name={['notifications', 'email', 'customContent']} noStyle>
                          <Input.TextArea
                            placeholder='Nhập nội dung'
                            className='min-h-[80px] w-full'
                            style={{ resize: 'vertical' }}
                          />
                        </Form.Item>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Notification */}
              {selectedNotificationTypes.notification && (
                <div className='rounded-lg border border-solid border-gray-200 bg-white p-3'>
                  <div className='caption-14-medium mb-2 text-text-neutral-strong'>Notification</div>

                  <Form.Item
                    name={['notifications', 'app', 'title']}
                    label={<RequiredDotLabel label='Tiêu đề notification' />}
                    className='mb-4'
                  >
                    <Input placeholder='Nhập tiêu đề notification' />
                  </Form.Item>

                  <div>
                    <div className='caption-12-medium mb-2 text-text-neutral-strong'>Nội dung</div>
                    <Form.Item name={['notifications', 'app', 'content']}>
                      <Input.TextArea placeholder='Nhập nội dung' className='min-h-[120px]' />
                    </Form.Item>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      ) : null}

      <MailTemplatePickerModal
        open={templateModalOpen}
        onCancel={() => setTemplateModalOpen(false)}
        onConfirm={handleTemplateConfirm}
      />
    </div>
  )
}
