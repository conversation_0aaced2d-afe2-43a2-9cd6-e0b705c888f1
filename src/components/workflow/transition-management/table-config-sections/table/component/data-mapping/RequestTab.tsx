import React, { useEffect, useState } from 'react'

import { Table, Select, Empty, Spin } from 'antd'
import type { ColumnsType } from 'antd/es/table'

import { StyledContent } from '@/components/workflow/shared/table'
import { INPUT_PARAMETERS } from '@/constants/workflow'

interface RequestField {
  field: string
  description: string
  type: string
  required: boolean
  defaultValue: string
}

interface RequestTabProps {
  loading: boolean
  fields: RequestField[]
  onChange: (data: any[]) => void
  initialData?: RequestField[] // Thêm prop initialData
}

export const RequestTab: React.FC<RequestTabProps> = ({ loading, fields, onChange, initialData = [] }) => {
  const [data, setData] = useState<RequestField[]>(initialData.length > 0 ? initialData : [])

  useEffect(() => {
    // Nếu có initialData, sử dụng initialData
    if (initialData && initialData.length > 0) {
      setData(initialData)
      onChange(initialData)
    }
    // N<PERSON><PERSON> không có initialData nhưng có fields, sử dụng fields
    else if (fields && fields.length > 0) {
      setData(fields)
      onChange(fields)
    }
  }, [fields, initialData, onChange])

  const columns: ColumnsType<RequestField> = [
    {
      title: 'Trường đối tác',
      dataIndex: 'field',
      key: 'field',
      width: '40%',
      render: text => <span className='text-gray-11'>{text}</span>
    },
    {
      title: 'Kiểu dữ liệu',
      dataIndex: 'type',
      key: 'type',
      width: '15%',
      render: text => <span className='text-gray-11'>{text}</span>
    },
    {
      title: 'Trạng thái',
      dataIndex: 'required',
      key: 'required',
      width: '20%',
      render: required => (
        <div
          className={`caption-12-medium flex w-30 items-center justify-center rounded-md px-2 py-1 ${required ? 'bg-[#FFE1E0] text-red-7' : 'bg-[#CDE4FE] text-bg-primary-default'}`}
        >
          {required ? 'Bắt buộc' : 'Không bắt buộc'}
        </div>
      )
    },
    {
      title: 'Tham số đầu vào',
      dataIndex: 'defaultValue',
      key: 'defaultValue',
      width: '25%',
      render: (_, record, index) => (
        <Select
          className='w-full'
          placeholder='Chọn tham số'
          value={record.defaultValue || undefined}
          onChange={value => {
            const newData = [...data]

            newData[index].defaultValue = value
            setData(newData)
            onChange(newData)
          }}
        >
          {INPUT_PARAMETERS.map(item => (
            <Select.Option key={item.value} value={item.value}>
              {item.label}
            </Select.Option>
          ))}
        </Select>
      )
    }
  ]

  return (
    <div className='mt-4'>
      <Spin spinning={loading}>
        {data && data.length > 0 ? (
          <StyledContent>
            <Table
              columns={columns}
              dataSource={data}
              rowKey={(record, index) => record.field || `row-${index}`}
              pagination={false}
              scroll={{ y: 300 }}
            />
          </StyledContent>
        ) : (
          <Empty description='Không có dữ liệu. Vui lòng tải lên file JSON để xem thông tin.' className='py-8' />
        )}
      </Spin>
    </div>
  )
}
