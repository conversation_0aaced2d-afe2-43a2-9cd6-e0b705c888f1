import React, { useState, useEffect, useRef } from 'react'

import { <PERSON><PERSON>, Ta<PERSON>, But<PERSON> } from 'antd'

import { CloseOutlined } from '@ant-design/icons'

import { useUploadJSON } from '@/hooks/workflow/useUploadJSON'
import { RequestTab } from './RequestTab'
import { UploadJSONSection } from './UploadJSONSection'
import { ResponseTab } from './ResponseTab'

interface DataMappingModalProps {
  open: boolean
  onClose: () => void
  onConfirm: (requestData: any, responseData: any) => void
  initialRequestData?: any[] // Dữ liệu ban đầu cho tab Request
  initialResponseData?: any[] // Dữ liệu ban đầu cho tab Response
  isEditing?: boolean // Thêm prop isEditing để xác định dữ liệu đang được chỉnh sửa hay là mới tải lên
}

export const DataMappingModal: React.FC<DataMappingModalProps> = ({
  open,
  onClose,
  onConfirm,
  initialRequestData = [],
  initialResponseData = []
}) => {
  const [activeTab, setActiveTab] = useState<string>('request')
  const [requestData, setRequestData] = useState<any[]>(initialRequestData)
  const [responseData, setResponseData] = useState<any[]>(initialResponseData)

  // Trạng thái tải file cho từng tab
  const [requestFileUploaded, setRequestFileUploaded] = useState<boolean>(initialRequestData.length > 0)
  const [responseFileUploaded, setResponseFileUploaded] = useState<boolean>(initialResponseData.length > 0)

  // Lưu thông tin file đã tải cho từng tab
  const [requestFile, setRequestFile] = useState<File | null>(null)
  const [responseFile, setResponseFile] = useState<File | null>(null)

  const { loading, requestFields, responseFields, uploadJSON } = useUploadJSON()

  const prevInitialReqRef = useRef<string>('')
  const prevInitialResRef = useRef<string>('')
  const prevReqFieldsRef = useRef<string>('')
  const prevResFieldsRef = useRef<string>('')

  // Cập nhật dữ liệu khi initialRequestData hoặc initialResponseData thay đổi (có guard so sánh)
  useEffect(() => {
    const reqKey = JSON.stringify(initialRequestData ?? [])
    const resKey = JSON.stringify(initialResponseData ?? [])

    if (reqKey !== prevInitialReqRef.current && initialRequestData && initialRequestData.length > 0) {
      prevInitialReqRef.current = reqKey
      setRequestData(initialRequestData)
      setRequestFileUploaded(true)
    }

    if (resKey !== prevInitialResRef.current && initialResponseData && initialResponseData.length > 0) {
      prevInitialResRef.current = resKey
      setResponseData(initialResponseData)
      setResponseFileUploaded(true)
    }
  }, [initialRequestData, initialResponseData])

  // Theo dõi thay đổi từ requestFields (khi tải file mới) có guard so sánh
  useEffect(() => {
    const key = JSON.stringify(requestFields ?? [])

    if (key !== prevReqFieldsRef.current && requestFields && requestFields.length > 0) {
      prevReqFieldsRef.current = key
      setRequestData(requestFields)
      setRequestFileUploaded(true)
    }
  }, [requestFields])

  useEffect(() => {
    const key = JSON.stringify(responseFields ?? [])

    if (key !== prevResFieldsRef.current && responseFields && responseFields.length > 0) {
      prevResFieldsRef.current = key
      setResponseData(responseFields)
      setResponseFileUploaded(true)
    }
  }, [responseFields])

  const handleTabChange = (key: string) => {
    setActiveTab(key)
  }

  const handleConfirm = () => {
    onConfirm(requestData, responseData)
    onClose()
  }

  const handleCancel = () => {
    onClose()
  }

  const handleRequestDataChange = (data: any[]) => {
    setRequestData(data)
  }

  const handleResponseDataChange = (data: any[]) => {
    setResponseData(data)
  }

  const handleRequestUploadSuccess = async (file: File) => {
    await uploadJSON(file, 'request')
    setRequestFile(file)

    // Cập nhật requestData từ requestFields và chuyển trạng thái sang đã tải file
    if (requestFields && requestFields.length > 0) {
      setRequestData(requestFields)
      setRequestFileUploaded(true)
    }
  }

  const handleResponseUploadSuccess = async (file: File) => {
    await uploadJSON(file, 'response')
    setResponseFile(file)

    // Cập nhật responseData từ responseFields và chuyển trạng thái sang đã tải file
    if (responseFields && responseFields.length > 0) {
      setResponseData(responseFields)
      setResponseFileUploaded(true)
    }
  }

  const handleUpdateRequestFile = () => {
    setRequestFileUploaded(false)
  }

  const handleUpdateResponseFile = () => {
    setResponseFileUploaded(false)
  }

  return (
    <Modal
      title='Ánh xạ dữ liệu'
      open={open}
      onCancel={handleCancel}
      width={960}
      closeIcon={<CloseOutlined />}
      footer={[
        <div className='flex w-full justify-between' key='footer'>
          {activeTab === 'request' && requestFileUploaded ? (
            <Button
              key='update-request'
              className='flex items-center justify-center border-primary-blue text-text-primary-blue'
              type='default'
              icon={<i className='onedx-edit size-4' />}
              onClick={handleUpdateRequestFile}
            >
              Cập nhật file
            </Button>
          ) : activeTab === 'response' && responseFileUploaded ? (
            <Button
              key='update-response'
              className='flex items-center justify-center border-primary-blue text-text-primary-blue'
              type='default'
              icon={<i className='onedx-edit size-4' />}
              onClick={handleUpdateResponseFile}
            >
              Cập nhật file
            </Button>
          ) : (
            <div className='flex-1'></div>
          )}
          <div className='flex gap-4'>
            <Button key='cancel' onClick={onClose} className='border-primary-blue bg-white text-text-primary-blue'>
              Đóng
            </Button>
            <Button key='confirm' type='primary' onClick={handleConfirm} className='bg-primary-blue'>
              Xác nhận
            </Button>
          </div>
        </div>
      ]}
    >
      <Tabs
        activeKey={activeTab}
        onChange={handleTabChange}
        items={[
          {
            key: 'request',
            label: 'Request',
            children: requestFileUploaded ? (
              <RequestTab
                loading={loading}
                fields={requestFields || requestData}
                onChange={handleRequestDataChange}
                initialData={requestData}
              />
            ) : (
              <UploadJSONSection
                loading={loading}
                onUpload={handleRequestUploadSuccess}
                lastUploadedFile={requestFile}
                tabType='request'
              />
            )
          },
          {
            key: 'response',
            label: 'Response',
            children: responseFileUploaded ? (
              <ResponseTab
                loading={loading}
                fields={responseFields || []}
                onChange={handleResponseDataChange}
                initialData={responseData}
              />
            ) : (
              <UploadJSONSection
                loading={loading}
                onUpload={handleResponseUploadSuccess}
                lastUploadedFile={responseFile}
                tabType='response'
              />
            )
          }
        ]}
      />
    </Modal>
  )
}
