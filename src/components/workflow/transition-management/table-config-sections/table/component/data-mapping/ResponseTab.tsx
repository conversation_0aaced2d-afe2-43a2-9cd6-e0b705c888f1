import React, { useEffect, useState } from 'react'

import { Table, Select, Button, Empty, Spin } from 'antd'
import type { ColumnsType } from 'antd/es/table'

import { StyledContent } from '@/components/workflow/shared/table'

interface ResponseMapping {
  key: string
  sourceField: string
  targetField: string | null
  value: string | null
}

interface ResponseTabProps {
  loading: boolean
  fields: Array<{
    path: string
    enums: string[] | null
  }>
  onChange: (data: any[]) => void
  initialData?: ResponseMapping[]
}

export const ResponseTab: React.FC<ResponseTabProps> = ({ loading, fields, onChange, initialData }) => {
  // Sử dụng giá trị mặc định chỉ khi không có initialData hoặc initialData rỗng
  const defaultData = [{ key: '1', sourceField: 'order_status', targetField: null, value: null }]

  const [data, setData] = useState<ResponseMapping[]>(initialData && initialData.length > 0 ? initialData : defaultData)

  // Lưu trữ các gi<PERSON> trị enum cho trường được chọn
  const [selectedFieldEnums, setSelectedFieldEnums] = useState<string[] | null>(null)

  // Cập nhật data khi initialData thay đổi
  useEffect(() => {
    if (initialData && initialData.length > 0) {
      setData(initialData)
    }
  }, [initialData])

  // Cập nhật data khi fields thay đổi (khi tải file mới)
  useEffect(() => {
    if (fields && fields.length > 0) {
      // Khi có fields mới (từ việc tải file), luôn cập nhật data
      if (fields.some(field => field.path)) {
        const newData = [{ key: '1', sourceField: 'order_status', targetField: null, value: null }]

        setData(newData)
      }
    }
  }, [fields])

  useEffect(() => {
    onChange(data)
  }, [data, onChange])

  const handleAddRow = () => {
    const newKey = (data.length + 1).toString()

    const newRow: ResponseMapping = {
      key: newKey,
      sourceField: 'order_status',
      targetField: null,
      value: null
    }

    setData([...data, newRow])
  }

  const handleDeleteRow = (key: string) => {
    const newData = data.filter(item => item.key !== key)

    setData(newData)
  }

  const handleFieldChange = (key: string, field: string, value: any) => {
    const newData = data.map(item => {
      if (item.key === key) {
        // Nếu đang thay đổi targetField, cập nhật selectedFieldEnums và reset value
        if (field === 'targetField') {
          const selectedField = fields.find(f => f.path === value)

          setSelectedFieldEnums(selectedField?.enums || null)

          return { ...item, [field]: value, value: null }
        }

        return { ...item, [field]: value }
      }

      return item
    })

    setData(newData)
  }

  const columns: ColumnsType<ResponseMapping> = [
    {
      title: 'Trường nguồn',
      dataIndex: 'sourceField',
      key: 'sourceField',
      width: '30%',
      render: (_, record) => (
        <Select
          className='w-full'
          value={record.sourceField}
          onChange={value => handleFieldChange(record.key, 'sourceField', value)}
        >
          <Select.Option value='order_status'>order_status</Select.Option>
        </Select>
      )
    },
    {
      title: 'Trường đối tác',
      dataIndex: 'targetField',
      key: 'targetField',
      width: '30%',
      render: (_, record) => (
        <Select
          className='w-full'
          placeholder='Chọn trường'
          value={record.targetField || undefined}
          onChange={value => handleFieldChange(record.key, 'targetField', value)}
        >
          {fields.map((field, idx) => (
            <Select.Option key={`resp-field-${field.path || idx}-${idx}`} value={field.path}>
              {field.path}
            </Select.Option>
          ))}
        </Select>
      )
    },
    {
      title: 'Giá trị',
      dataIndex: 'value',
      key: 'value',
      width: '30%',
      render: (_, record) => (
        <Select
          className='w-full'
          placeholder='Chọn giá trị'
          value={record.value || undefined}
          onChange={value => handleFieldChange(record.key, 'value', value)}
          disabled={!record.targetField} // Vô hiệu hóa nếu chưa chọn trường đối tác
        >
          {selectedFieldEnums &&
            selectedFieldEnums.map((enumValue, idx) => (
              <Select.Option key={`enum-${enumValue}-${idx}`} value={enumValue}>
                {enumValue}
              </Select.Option>
            ))}
        </Select>
      )
    },
    {
      title: '',
      key: 'action',
      width: '10%',
      render: (_, record, index) => {
        // Kiểm tra nếu chỉ có 1 hàng hoặc đây là hàng cuối cùng
        const isLastRow = index === data.length - 1

        if (data.length === 1 || isLastRow) {
          return (
            <Button
              type='text'
              icon={<i className='onedx-plus size-4 text-gray-6' />}
              onClick={handleAddRow}
              className='flex items-center justify-center rounded-lg border border-solid border-icon-neutral-lighter'
            />
          )
        }

        return (
          <Button
            type='text'
            icon={<i className='onedx-delete size-4 text-gray-6' />}
            onClick={() => handleDeleteRow(record.key)}
            className='flex items-center justify-center rounded-lg border border-solid border-icon-neutral-lighter'
          />
        )
      }
    }
  ]

  return (
    <div className='mt-4'>
      <Spin spinning={loading}>
        <StyledContent>
          <Table
            columns={columns}
            dataSource={data}
            pagination={false}
            scroll={{ y: 300 }}
            locale={{
              emptyText: <Empty description='Không có dữ liệu. Vui lòng tải lên file JSON để xem thông tin.' />
            }}
          />
        </StyledContent>
      </Spin>
    </div>
  )
}
