'use client'

import React, { useState, useRef, forwardRef, useImperativeHandle } from 'react'

import { PlusOutlined } from '@ant-design/icons'
import { Empty, Form, Select, Dropdown, Checkbox, Button } from 'antd'

import { RequiredDotLabel } from '@/components/label/RequiredDotLabel'
import { SectionHeader, NestedSectionHeader } from '../../../../shared/section-header'
import { EndpointConfig } from './EndpointConfig'
import { ScheduleSync, type ScheduleSyncRef } from './ScheduleSync'
import { ConditionConfig, type ConditionGroup } from './ConditionConfig'

import {
  TRIGGER_OPTIONS,
  RULES_ENGINE_CONDITIONS,
  RULES_ENGINE_CONDITION_VALUES,
  SCHEDULE_CONDITIONS,
  SCHEDULE_CONDITION_VALUES,
  TRIGGER_OPTIONS_CREATE
} from '@/constants/workflow/trigger'
import { PopupDataMapping } from './PopupDataMapping'
import { GreenDotPanel } from '@/components/label/GreenDotPanel'
import { PopupDataMappingWebhook } from './PopupDataMappingWebhook'
import { WebhookCard } from './WebhookCard'

interface TriggerUpdateProps {
  selectedStates: { id: string; name: string }[]
}

export interface TriggerUpdateRef {
  getScheduleSyncValues: () => {
    apiSchedule: any
    schedule: any
  }
  ruleEngineConditions: ConditionGroup[]
  scheduleConditions: ConditionGroup[]
  selectedTriggers: string[]
  webhookData?: any[]
  reset: () => void
}

export const TriggerUpdate = forwardRef<TriggerUpdateRef, TriggerUpdateProps>(({ selectedStates }, ref) => {
  const [mainCollapsed, setMainCollapsed] = useState(false)
  const [manualCollapsed, setManualCollapsed] = useState(false)
  const [apiCollapsed, setApiCollapsed] = useState(false)
  const [webhookCollapsed, setWebhookCollapsed] = useState(false)
  const [scheduleCollapsed, setScheduleCollapsed] = useState(false)
  const [ruleEngineCollapsed, setRuleEngineCollapsed] = useState(false)
  const [selectedTriggers, setSelectedTriggers] = useState<string[]>([])
  const [ruleEngineConditions, setRuleEngineConditions] = useState<ConditionGroup[]>([])
  const [scheduleConditions, setScheduleConditions] = useState<ConditionGroup[]>([])
  const [webhookData, setWebhookData] = useState<any[]>([])
  const [requestMappingData, setRequestMappingData] = useState<any[]>([])
  const [responseMappingData, setResponseMappingData] = useState<any[]>([])

  // Refs cho các component ScheduleSync
  const apiScheduleRef = useRef<ScheduleSyncRef>(null)
  const scheduleRef = useRef<ScheduleSyncRef>(null)

  // State cho ScheduleSync
  const [apiScheduleCollapsed, setApiScheduleCollapsed] = useState(false)

  // State cho PopupDataMapping
  const [dataMappingOpen, setDataMappingOpen] = useState(false)
  const [dataMappingWebhookOpen, setDataMappingWebhookOpen] = useState(false)

  // State để control dropdown
  const [dropdownOpen, setDropdownOpen] = useState(false)

  // Xử lý khi chọn một loại trigger
  const handleTriggerSelect = (key: string, checked: boolean) => {
    if (checked) {
      // Cho phép chọn nhiều trigger
      const newTriggers = [...selectedTriggers, key]

      setSelectedTriggers(newTriggers)
    } else {
      const newTriggers = selectedTriggers.filter(item => item !== key)

      setSelectedTriggers(newTriggers)
    }
  }

  // Xử lý khi xác nhận chọn trigger
  const handleConfirm = () => {
    setDropdownOpen(false)
  }

  // Hàm để lấy giá trị mapping từ các component ScheduleSync
  const getScheduleSyncValues = () => {
    const scheduleValues = scheduleRef.current?.mapScheduleSyncValues()
    const apiScheduleValues = apiScheduleRef.current?.mapScheduleSyncValues()

    const values = {
      apiSchedule: apiScheduleValues,
      schedule: scheduleValues
    }

    return values
  }

  // Hàm reset toàn bộ state nội bộ
  const reset = () => {
    setMainCollapsed(false)
    setManualCollapsed(false)
    setApiCollapsed(false)
    setWebhookCollapsed(false)
    setScheduleCollapsed(false)
    setRuleEngineCollapsed(false)
    setSelectedTriggers([])
    setRuleEngineConditions([])
    setScheduleConditions([])
    setWebhookData([])
    setRequestMappingData([])
    setResponseMappingData([])
    setApiScheduleCollapsed(false)
    setDataMappingOpen(false)
    setDataMappingWebhookOpen(false)
    setDropdownOpen(false)
  }

  // Expose APIs via ref
  useImperativeHandle(ref, () => {
    const refData = {
      getScheduleSyncValues,
      ruleEngineConditions,
      scheduleConditions,
      webhookData,
      selectedTriggers,
      requestMappingData,
      responseMappingData,
      reset
    }

    return refData
  })

  // Custom dropdown menu với checkbox
  const dropdownContent = (
    <div className='ml-2 w-40 rounded-md bg-white py-1 shadow-md'>
      {TRIGGER_OPTIONS.map(option =>
        option.type === 'divider' ? (
          <div key={option.key} className='my-1 border-t border-gray-3'></div>
        ) : (
          <div
            key={option.key}
            className='flex cursor-pointer items-center p-2 pl-6 hover:bg-gray-1'
            onClick={() => {
              const isChecked = !selectedTriggers.includes(option.key)

              handleTriggerSelect(option.key, isChecked)
            }}
          >
            <Checkbox className='mr-2' checked={selectedTriggers.includes(option.key)} />
            <span>{option.label}</span>
          </div>
        )
      )}
      <div className='my-1 border-t border-gray-3 text-center'>
        <Button type='primary' size='middle' onClick={handleConfirm} className='w-32 rounded bg-primary-blue'>
          Xác nhận
        </Button>
      </div>
    </div>
  )

  const addTriggerButton = (
    <Dropdown
      dropdownRender={() => dropdownContent}
      trigger={['click']}
      placement='bottomRight'
      open={dropdownOpen}
      onOpenChange={setDropdownOpen}
    >
      <div className='flex cursor-pointer items-center text-primary-blue'>
        <PlusOutlined />
        <span className='ml-1'>Thêm trigger cập nhật</span>
      </div>
    </Dropdown>
  )

  return (
    <div className='mb-5 rounded bg-gray-alpha-1'>
      <SectionHeader
        title='Trigger cập nhật'
        collapsed={mainCollapsed}
        onToggle={() => setMainCollapsed(!mainCollapsed)}
        component={addTriggerButton}
      />

      {!mainCollapsed && (
        <div className='p-4'>
          {/* Section con - Manual */}
          {selectedTriggers.includes(TRIGGER_OPTIONS_CREATE.MANUAL) && (
            <div className='mb-4 overflow-hidden rounded-lg bg-white'>
              <NestedSectionHeader
                title='Manual'
                collapsed={manualCollapsed}
                onToggle={() => setManualCollapsed(!manualCollapsed)}
                showDelete={true}
                onDelete={() =>
                  setSelectedTriggers(prev => prev.filter(item => item !== TRIGGER_OPTIONS_CREATE.MANUAL))
                }
              />

              {!manualCollapsed && (
                <div className='p-4'>
                  <div className='grid grid-cols-2 gap-4'>
                    <div>
                      <Form.Item
                        name={[selectedStates[0]?.id || 'default', 'actor']}
                        label={<RequiredDotLabel label='Tác nhân được cập nhật trạng thái' />}
                        rules={[{ required: true, message: 'Tác nhân thực hiện không được bỏ trống' }]}
                      >
                        <Select
                          placeholder='Chọn tác nhân thực hiện'
                          options={[
                            { value: 'ADMIN', label: 'Admin' },
                            { value: 'PARTNER', label: 'Đối tác' }
                          ]}
                          notFoundContent={
                            <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description='Không có dữ liệu' />
                          }
                        />
                      </Form.Item>
                    </div>

                    <div>
                      <Form.Item
                        name={[selectedStates[0]?.id || 'default', 'role']}
                        label={<RequiredDotLabel label='Vai trò' />}
                        rules={[{ required: true, message: 'Vai trò không được bỏ trống' }]}
                      >
                        <Select
                          placeholder='Chọn vai trò'
                          options={[
                            { value: 'ADMIN', label: 'Admin' },
                            { value: 'PARTNER', label: 'Đối tác' }
                          ]}
                          notFoundContent={
                            <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description='Không có dữ liệu' />
                          }
                        />
                      </Form.Item>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Section con - API_call */}
          {selectedTriggers.includes(TRIGGER_OPTIONS_CREATE.API) && (
            <div className='mb-4 overflow-hidden rounded-lg bg-white'>
              <NestedSectionHeader
                title='API Call'
                collapsed={apiCollapsed}
                onToggle={() => setApiCollapsed(!apiCollapsed)}
                showDelete={true}
                onDelete={() => setSelectedTriggers(prev => prev.filter(item => item !== TRIGGER_OPTIONS_CREATE.API))}
              />

              <div className={`${apiCollapsed ? 'hidden' : 'p-4'}`}>
                {/* Cấu hình endpoint */}
                <EndpointConfig name={`${selectedStates[0]?.id || 'default'}_trigger_endpoint`} />

                {/* Lịch trình đồng bộ */}
                <GreenDotPanel
                  label='Lịch trình đồng bộ'
                  collapsed={apiScheduleCollapsed}
                  onToggle={() => setApiScheduleCollapsed(!apiScheduleCollapsed)}
                  backgroundColor={'bg-[#F3F4F5]'}
                >
                  <ScheduleSync ref={apiScheduleRef} id={`${selectedStates[0]?.id || 'default'}_api-schedule`} />
                </GreenDotPanel>

                {/* Ánh xạ dữ liệu*/}
                <div className='mt-6 flex items-center justify-between rounded-lg bg-[#F3F4F5] p-3'>
                  <span className="body-14-regular relative items-center justify-center pl-4 text-gray-11 before:absolute before:left-0 before:top-1/2 before:size-2 before:-translate-y-1/2 before:rounded-full before:bg-green-6 before:content-['']">
                    Ánh xạ dữ liệu
                  </span>
                  <Button type='primary' className='bg-primary-blue' onClick={() => setDataMappingOpen(true)}>
                    Cài đặt file
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Section con - Webhook */}
          {selectedTriggers.includes(TRIGGER_OPTIONS_CREATE.WEBHOOK) && (
            <div className='mb-4 overflow-hidden rounded-lg bg-white'>
              <NestedSectionHeader
                title='Webhook'
                collapsed={webhookCollapsed}
                onToggle={() => setWebhookCollapsed(!webhookCollapsed)}
                showDelete={true}
                onDelete={() =>
                  setSelectedTriggers(prev => prev.filter(item => item !== TRIGGER_OPTIONS_CREATE.WEBHOOK))
                }
              />

              <div className={`${webhookCollapsed ? 'hidden' : 'p-4'}`}>
                <WebhookCard initialData={webhookData} onDataMappingChange={data => setWebhookData(data)} />
              </div>
            </div>
          )}

          {/* Section con - Lịch hẹn */}
          {selectedTriggers.includes(TRIGGER_OPTIONS_CREATE.SCHEDULE) && (
            <div className='mb-4 overflow-hidden rounded-lg bg-white'>
              <NestedSectionHeader
                title='Lịch hẹn'
                collapsed={scheduleCollapsed}
                onToggle={() => setScheduleCollapsed(!scheduleCollapsed)}
                showDelete={true}
                onDelete={() =>
                  setSelectedTriggers(prev => prev.filter(item => item !== TRIGGER_OPTIONS_CREATE.SCHEDULE))
                }
              />
              <div className={`${scheduleCollapsed ? 'hidden' : 'block'} p-4`}>
                {/* Lịch trình */}
                <ScheduleSync ref={scheduleRef} id={`${selectedStates[0]?.id || 'default'}_schedule`} />
                {/* Cấu hình điều kiện lịch trình*/}
                <div className='mt-4'>
                  <ConditionConfig
                    conditions={SCHEDULE_CONDITIONS}
                    conditionValues={SCHEDULE_CONDITION_VALUES}
                    value={scheduleConditions}
                    onChange={setScheduleConditions}
                  />
                </div>
              </div>
            </div>
          )}

          {/* Section con - Rule engine*/}
          {selectedTriggers.includes(TRIGGER_OPTIONS_CREATE.RULE_ENGINE) && (
            <div className='mb-4 overflow-hidden rounded-lg bg-white'>
              <NestedSectionHeader
                title='Rule Engine'
                collapsed={ruleEngineCollapsed}
                onToggle={() => setRuleEngineCollapsed(!ruleEngineCollapsed)}
                showDelete={true}
                onDelete={() =>
                  setSelectedTriggers(prev => prev.filter(item => item !== TRIGGER_OPTIONS_CREATE.RULE_ENGINE))
                }
              />

              {!ruleEngineCollapsed && (
                <div className='p-4'>
                  {/* Rule engine */}
                  <ConditionConfig
                    conditions={RULES_ENGINE_CONDITIONS}
                    conditionValues={RULES_ENGINE_CONDITION_VALUES}
                    value={ruleEngineConditions}
                    onChange={setRuleEngineConditions}
                  />
                </div>
              )}
            </div>
          )}

          {/* Cấu hình popup data mapping */}
          <PopupDataMapping
            open={dataMappingOpen}
            onClose={() => setDataMappingOpen(false)}
            onConfirm={data => {
              setRequestMappingData(data.request || [])
              setResponseMappingData(data.response || [])
              setDataMappingOpen(false)
            }}
          />
          <PopupDataMappingWebhook
            open={dataMappingWebhookOpen}
            onClose={() => setDataMappingWebhookOpen(false)}
            onConfirm={() => {
              setDataMappingWebhookOpen(false)
            }}
          />
        </div>
      )}
    </div>
  )
})
