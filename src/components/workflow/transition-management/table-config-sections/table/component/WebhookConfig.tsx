'use client'

import React, { useState } from 'react'

import { Button, Form } from 'antd'

import { EndpointConfig } from './EndpointConfig'
import { PopupDataMapping } from './PopupDataMapping'

interface WebhookConfigProps {
  backgroundColor?: string
}

export const WebhookConfig: React.FC<WebhookConfigProps> = ({ backgroundColor = 'bg-white' }) => {
  const form = Form.useFormInstance()
  const [dataMappingOpen, setDataMappingOpen] = useState(false)

  return (
    <>
      <EndpointConfig backgroundColor={backgroundColor} name='action_endpoint' />
      <div className='mt-6 flex items-center justify-between rounded-lg bg-[#F3F4F5] p-3'>
        <span className="body-14-regular relative items-center justify-center pl-4 text-gray-11 before:absolute before:left-0 before:top-1/2 before:size-2 before:-translate-y-1/2 before:rounded-full before:bg-green-6 before:content-['']">
          Ánh xạ dữ liệu
        </span>
        <Button type='primary' className='bg-primary-blue' onClick={() => setDataMappingOpen(true)}>
          Cài đặt file
        </Button>
      </div>
      <PopupDataMapping
        open={dataMappingOpen}
        onClose={() => setDataMappingOpen(false)}
        onConfirm={data => {
          // Đồng bộ vào form để mapDataConfigCreate có thể build postActions WEBHOOK
          form.setFieldValue('actionRequestMappingData', data.request || [])
          form.setFieldValue('actionResponseMappingData', data.response || [])
          setDataMappingOpen(false)
        }}
      />
    </>
  )
}
