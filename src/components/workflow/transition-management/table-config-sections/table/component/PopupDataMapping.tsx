'use client'

import React, { useEffect, useState } from 'react'

import { DataMappingModal } from './data-mapping'

interface PopupDataMappingProps {
  open: boolean
  onClose: () => void
  onConfirm: (data: any) => void
  triggerData?: any // Thêm prop triggerData để nhận dữ liệu từ component cha
}

export const PopupDataMapping: React.FC<PopupDataMappingProps> = ({ open, onClose, onConfirm, triggerData }) => {
  const [requestData, setRequestData] = useState<any[]>([])
  const [responseData, setResponseData] = useState<any[]>([])

  // Xử lý dữ liệu triggerData khi component được mount hoặc triggerData thay đổi
  useEffect(() => {
    if (triggerData) {
      // Xử lý inputMapping (Request)
      if (triggerData.inputMapping && Array.isArray(triggerData.inputMapping)) {
        const formattedRequestData = triggerData.inputMapping.map((item: any) => {
          const source = item.source || ''
          const dest = item.dest || ''
          const isRequired = item.isRequired || false
          const type = item.type || 'string'

          return {
            field: dest, // Trường đối tác
            description: source, // Mô tả (có thể hiển thị source path)
            type: type, // Kiểu dữ liệu
            required: isRequired, // Trạng thái bắt buộc
            defaultValue: source // Giá trị mặc định (source path)
          }
        })

        setRequestData(formattedRequestData)
      }

      // Xử lý outputMapping (Response)
      if (triggerData.outputMapping && Array.isArray(triggerData.outputMapping)) {
        // Kiểm tra xem outputMapping có dữ liệu không
        if (triggerData.outputMapping.length > 0) {
          const formattedResponseData = triggerData.outputMapping
            .map((item: any, index: number) => {
              // Xử lý các trường hợp khác nhau của dữ liệu
              let source = ''
              let dest = ''
              let value = null

              // Kiểm tra cấu trúc dữ liệu để trích xuất đúng thông tin
              if (item.source) {
                source = item.source
              }

              if (item.dest) {
                dest = item.dest
              }

              if (item.value) {
                value = item.value
              }

              // Trường hợp đặc biệt khi dữ liệu có cấu trúc khác
              if (!source && !dest && item[0] && typeof item[0] === 'object') {
                if (item[0].source) source = item[0].source
                if (item[0].dest) dest = item[0].dest
                if (item[0].value) value = item[0].value
              }

              return {
                key: (index + 1).toString(),
                sourceField: dest || '', // Trường nguồn
                targetField: source || '', // Trường đối tác
                value: value // Giá trị
              }
            })
            .filter((item: { sourceField: string; targetField: string }) => item.sourceField || item.targetField) // Lọc bỏ các item không có dữ liệu

          // Đã xóa log debug
          setResponseData(formattedResponseData)
        } else {
          // Nếu outputMapping là mảng rỗng, đặt responseData là mảng rỗng
          setResponseData([])
        }
      } else {
        // Nếu không có outputMapping, đặt responseData là mảng rỗng
        setResponseData([])
      }
    }
  }, [triggerData])

  const handleConfirm = (requestData: any, responseData: any) => {
    // Kết hợp dữ liệu từ cả hai tab để truyền về component cha
    const combinedData = {
      request: requestData,
      response: responseData
    }

    onConfirm(combinedData)
  }

  return (
    <DataMappingModal
      open={open}
      onClose={onClose}
      onConfirm={handleConfirm}
      initialRequestData={requestData}
      initialResponseData={responseData}
    />
  )
}
