'use client'

import { useState } from 'react'

import { useParams } from 'next/navigation'

import { CalendarOutlined, FilterOutlined } from '@ant-design/icons'
import { DatePicker, Space, Table, Select, Pagination } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import { useQuery } from '@tanstack/react-query'

import { stateTransitionAdmin } from '@/models/workflow/stateTransition'
import type { HistoryRecord } from '@/types/workflow/transition'
import type { CheckedState } from '@/components/filter/SettingInput'
import SettingInput from '@/components/filter/SettingInput'
import { HISTORY_FILTER, VERSION_FILTER } from '@/constants/workflow'
import { DATE_FORMATS } from '@/utils/date'
import type { IStateTransitionHistoryParams } from '@/types/workflow/stateTransition'

const { RangePicker } = DatePicker

export const HistorySection: React.FC = () => {
  const { id: draftId } = useParams<{ id: string }>()
  const [searchValue, setSearchValue] = useState('')
  const [dateRange, setDateRange] = useState<[string, string] | null>(null)
  const [versionFilter, setVersionFilter] = useState<string>()

  // region state filter
  const [checkedFilter, setCheckedFilter] = useState<CheckedState>({
    isName: true,
    isChangeSummary: true,
    isActor: true
  })

  const [page, setPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)

  // Sử dụng useQuery để gọi API lấy lịch sử
  const { data: historyData, isLoading } = useQuery({
    queryKey: ['stateTransitionHistory', draftId, searchValue, dateRange, versionFilter, page, pageSize],
    queryFn: () => {
      // Thiết lập các giá trị mặc định và thêm các tham số tùy chọn khi có giá trị
      const params: IStateTransitionHistoryParams = {
        ...(searchValue && { value: searchValue }),
        ...(dateRange?.[0] && { startDate: dateRange[0] }),
        ...(dateRange?.[1] && { endDate: dateRange[1] }),
        page: page - 1,
        size: pageSize,
        isName: checkedFilter.isName ? 1 : 0,
        isChangeSummary: checkedFilter.isChangeSummary ? 1 : 0,
        isActor: checkedFilter.isActor ? 1 : 0,
        versionFilter: versionFilter,
        sort: 'modifiedAt,desc'
      }

      return stateTransitionAdmin.getHistory(draftId, params)
    },
    enabled: !!draftId
  })

  // region handle filter
  // Xử lý tìm kiếm
  const handleSearch = (value: string) => {
    setSearchValue(value)
  }

  // Xử lý chọn ngày
  const handleDateChange = (dates: any, dateStrings: [string, string]) => {
    setDateRange(dateStrings[0] && dateStrings[1] ? dateStrings : null)
  }

  // Xử lý chọn phiên bản
  const handleVersionFilterChange = (value: string) => {
    setVersionFilter(value)
  }
  // endregion filter

  // region Columns
  const columns: ColumnsType<HistoryRecord> = [
    {
      title: 'STT',
      dataIndex: 'index',
      key: 'index',
      width: 56,
      render: (_: any, _record: any, index: number) => (
        <div className='line-clamp-1 text-sm text-gray-11 group-hover:text-primary-blue'>
          {(page - 1) * pageSize + index + 1}
        </div>
      )
    },
    {
      title: 'Tên cấu hình',
      dataIndex: 'name',
      key: 'name',
      width: 313,
      render: text => <div className='line-clamp-1 text-sm text-gray-11 group-hover:text-primary-blue'>{text}</div>
    },
    {
      title: 'Phiên bản',
      dataIndex: 'version',
      key: 'version',
      width: 133,
      render: text => <div className='line-clamp-1 text-sm text-gray-11 group-hover:text-primary-blue'>{text}</div>
    },
    {
      title: 'Mô tả hoạt động',
      dataIndex: 'changeSummary',
      key: 'changeSummary',
      width: 313,
      render: text => <div className='line-clamp-1 text-sm text-gray-11 group-hover:text-primary-blue'>{text}</div>
    },
    {
      title: 'Người thực hiện',
      dataIndex: 'actorInfo',
      key: 'actorInfo',
      width: 230,
      render: text => <div className='line-clamp-1 text-sm text-gray-11 group-hover:text-primary-blue'>{text}</div>
    },
    {
      title: 'Thời gian',
      dataIndex: 'modifiedAt',
      key: 'modifiedAt',
      width: 127,
      render: text => <div className='line-clamp-1 text-sm text-gray-11 group-hover:text-primary-blue'>{text}</div>
    }
  ]
  // endregion Columns

  // region render
  return (
    <div>
      <div className='mb-2 overflow-hidden rounded-md bg-white p-4'>
        {/* Bộ lọc */}
        <div className='mb-4 flex items-center gap-4'>
          <SettingInput
            placeholder='Tìm kiếm theo tên cấu hình/ mô tả/ người thực hiện'
            styles={{ width: '100%' }}
            checked={checkedFilter}
            setChecked={setCheckedFilter}
            onKeyDown={event => {
              if (event.key === 'Enter') {
                event.preventDefault()
                handleSearch(event.currentTarget.value)
                setPage(1)
              }
            }}
            checkBoxOptions={HISTORY_FILTER}
            size='small'
          />
          <Space>
            <Select
              placeholder='Phiên bản'
              style={{ width: 120 }}
              value={versionFilter}
              onChange={handleVersionFilterChange}
              options={VERSION_FILTER}
              suffixIcon={<FilterOutlined />}
            />
          </Space>
          <Space>
            <RangePicker
              placeholder={['Từ ngày', 'Đến ngày']}
              className='w-[280px]'
              format={DATE_FORMATS.DD_MM_YYYY_SLASH}
              size='middle'
              suffixIcon={<CalendarOutlined />}
              onChange={handleDateChange}
            />
          </Space>
        </div>

        {/* Bảng lịch sử sử dụng Ant Design Table */}
        <div className='px-4 pb-4'>
          <Table
            columns={columns}
            dataSource={historyData?.content}
            rowKey='key'
            pagination={false}
            className='rounded-md border border-gray-200 [&_tr:hover_td]:!text-primary-blue'
            size='middle'
            loading={isLoading}
            onRow={() => ({
              className: 'border-b border-gray-alpha-3 transition-colors duration-200 group hover:font-medium'
            })}
          />
        </div>

        {/* Phân trang */}
        <div className='flex items-center justify-between'>
          <div className='flex w-1/3 justify-center' />
          <div className='flex w-1/3 justify-center'>
            <Pagination
              current={page}
              pageSize={pageSize}
              total={historyData?.totalElements}
              showSizeChanger={false}
              onChange={p => setPage(p)}
            />
          </div>
          <div className='flex w-1/3 min-w-[120px] items-center justify-end gap-2'>
            <Select
              value={`${pageSize} / Trang`}
              onChange={val => {
                setPageSize(Number(val))
                setPage(1)
              }}
              options={[
                { value: 10, label: '10 / Trang' },
                { value: 20, label: '20 / Trang' },
                { value: 50, label: '50 / Trang' }
              ]}
              style={{ width: 120 }}
            />
          </div>
        </div>
      </div>
    </div>
  )
}
// endregion render
