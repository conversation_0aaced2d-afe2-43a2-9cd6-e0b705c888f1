'use client'

//#region Imports
import React, { useEffect } from 'react'

import { TableOutlined } from '@ant-design/icons'
import { Button, Form, Select, Space } from 'antd'

import { useQuery } from '@tanstack/react-query'

import TableCheckboxEdit from '@/views/product-catalog/common/TableCheckboxEdit'
import type { CheckedState } from '@/components/filter/SettingInput'
import SettingInput from '@/components/filter/SettingInput'
import { stateTransitionAdmin } from '@/models/workflow/stateTransition'
import { filterOptionTrim } from '@/utils/string'
// Thêm import SelectFilterPopup
import SelectFilterPopup from '@/views/order-management/common/SelectFilterPopup'
import {
  FILTER_OPTIONS,
  STATE_SEARCH_CHECKBOX_OPTIONS,
  TABLE_CHECKBOX_OPTIONS,
  TRIGGER_ROLE_OPTIONS
} from '@/constants/workflow'
import type { ISearchFilterBarProps } from '@/types/workflow/stateTransition'
//#endregion

export const SearchFilterBar: React.FC<ISearchFilterBarProps> = ({
  readOnly,
  selectedColumns,
  setSelectedColumns,
  handleAddState,
  searchFilters,
  updateSearchFilter
}) => {
  //#region Hooks & State
  // Khởi tạo form instance
  const [form] = Form.useForm()

  // API call để lấy danh sách loại trạng thái từ model có sẵn
  const { data: stateTypes, isLoading: loadingStateTypes } = useQuery({
    queryKey: ['state-types-combobox'],
    queryFn: () => stateTransitionAdmin.getStateTypeCombobox({ search: '', page: 0, size: 1000 }),
    select: data =>
      data?.content?.map((item: any) => ({
        label: item.name,
        value: item.id
      })) || []
  })

  // State để lưu filter params với tên mới
  // Thay thế state filterParams hiện tại
  const [filterParams, setFilterParams] = React.useState({
    lstTriggerType: searchFilters.lstTriggerType || [],
    postActionTypes: searchFilters.postActionTypes || [],
    timeRange:
      searchFilters.startTime && searchFilters.endTime ? [searchFilters.startTime, searchFilters.endTime] : undefined
  })
  //#endregion

  //#region Effects
  // Thêm useEffect để đồng bộ filterParams với searchFilters
  React.useEffect(() => {
    setFilterParams({
      lstTriggerType: searchFilters.lstTriggerType || [],
      postActionTypes: searchFilters.postActionTypes || [],
      timeRange:
        searchFilters.startTime && searchFilters.endTime ? [searchFilters.startTime, searchFilters.endTime] : undefined
    })
  }, [searchFilters.lstTriggerType, searchFilters.postActionTypes, searchFilters.startTime, searchFilters.endTime])

  // Đồng bộ form values với searchFilters
  useEffect(() => {
    form.setFieldsValue({
      searchInput: searchFilters.searchValue,
      lstTypeId: searchFilters.lstTypeId,
      lstTriggerRole: searchFilters.lstTriggerRole,
      lstTriggerType: searchFilters.lstTriggerType || [],
      postActionTypes: searchFilters.postActionTypes || [],
      startTime: searchFilters.startTime,
      endTime: searchFilters.endTime
    })
  }, [searchFilters, form])
  //#endregion

  //#region Event Handlers
  // Hàm xử lý khi filter params thay đổi
  const handleFilterParamsChange = (newParams: any) => {
    setFilterParams(newParams)

    // Xử lý tất cả các filter trực tiếp
    Object.keys(newParams).forEach(key => {
      if (newParams[key] !== undefined && newParams[key] !== null) {
        updateSearchFilter(key, newParams[key])
      } else {
        // Reset giá trị khi clear filter
        const defaultValue = Array.isArray(searchFilters[key as keyof typeof searchFilters]) ? [] : ''

        updateSearchFilter(key, defaultValue)
      }
    })
  }

  /* Xử lý thay đổi trạng thái checkbox filter */
  const handleCheckedChange = (newCheckedState: CheckedState | ((prev: CheckedState) => CheckedState)) => {
    const newValue =
      typeof newCheckedState === 'function' ? newCheckedState(searchFilters.checkedFilter) : newCheckedState

    updateSearchFilter('checkedFilter', newValue)
  }

  /* Xử lý sự kiện nhấn phím trong ô tìm kiếm */
  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter') {
      event.preventDefault()
      // Chỉ khi nhấn Enter mới cập nhật searchFilter và gọi API
      updateSearchFilter('searchValue', event.currentTarget.value)
    }
  }

  // Xử lý khi form values thay đổi
  const handleValuesChange = (changedValues: any) => {
    // Cập nhật searchFilters cho từng field thay đổi
    Object.keys(changedValues).forEach(key => {
      updateSearchFilter(key, changedValues[key])
    })
  }
  //#endregion

  //#region Render
  return (
    <div className='mb-4 flex items-center gap-2'>
      <SettingInput
        placeholder='Tìm kiếm theo mã trạng thái/tên trạng thái'
        styles={{ width: '100%' }}
        checked={searchFilters.checkedFilter}
        setChecked={handleCheckedChange}
        onKeyDown={handleKeyDown}
        checkBoxOptions={STATE_SEARCH_CHECKBOX_OPTIONS}
        size='small'
      />
      <Form
        form={form}
        layout='inline'
        className='flex-1'
        onValuesChange={handleValuesChange}
        initialValues={{
          searchInput: searchFilters.searchValue,
          lstTypeId: searchFilters.lstTypeId,
          lstTriggerRole: searchFilters.lstTriggerRole,
          lstTriggerType: searchFilters.lstTriggerType || [],
          postActionTypes: searchFilters.postActionTypes || [],
          startTime: searchFilters.startTime,
          endTime: searchFilters.endTime
        }}
      >
        <Space>
          <Form.Item name='lstTypeId'>
            <Select
              placeholder='Loại trạng thái'
              className='w-[250px] [&_.ant-select-selection-item]:!mr-1 [&_.ant-select-selection-overflow-item-suffix]:!ml-1 [&_.ant-select-selection-overflow-item]:!max-w-[150px] [&_.ant-select-selection-overflow]:!flex-nowrap [&_.ant-select-selection-overflow]:!gap-1 [&_.ant-select-selection-overflow]:!overflow-hidden [&_.ant-select-selector]:!h-[32px] [&_.ant-select-selector]:!min-h-[32px]'
              suffixIcon={<i className='onedx-chevron-down size-5' />}
              showSearch
              size='middle'
              mode='multiple'
              allowClear
              loading={loadingStateTypes}
              options={stateTypes}
              filterOption={filterOptionTrim}
              notFoundContent='Không có dữ liệu'
              maxTagCount={1}
            />
          </Form.Item>

          <Form.Item name='lstTriggerRole'>
            <Select
              placeholder='Vai trò cập nhật'
              className='w-56'
              suffixIcon={<i className='onedx-chevron-down size-5' />}
              size='middle'
              allowClear
              mode='multiple'
              options={TRIGGER_ROLE_OPTIONS}
            />
          </Form.Item>

          {/* Popup Bộ lọc */}
          <SelectFilterPopup
            filterOptions={FILTER_OPTIONS}
            filterParams={filterParams}
            setFilterParams={handleFilterParamsChange}
            customStyle='w-[100px]'
          />
        </Space>
      </Form>

      {!readOnly && (
        <Button type='primary' icon={<TableOutlined />} className='ml-auto' ghost onClick={handleAddState}>
          Thêm trạng thái
        </Button>
      )}

      <TableCheckboxEdit
        selectedColumns={selectedColumns}
        setSelectedColumns={setSelectedColumns}
        isAdmin={true}
        checkboxOptions={TABLE_CHECKBOX_OPTIONS}
        height={'40px'}
      />
    </div>
  )
  //#endregion
}
