'use client'

import React from 'react'

import { ValueDetail } from '@/components/workflow/shared/value-detail/ValueDetail'

interface GeneralInfoSectionProps {
  selectedState: any // Edge
}

export const TransitionGeneralInfoSection: React.FC<GeneralInfoSectionProps> = ({ selectedState }) => {
  return (
    <div className='rounded-lg bg-bg-neutral-lightest'>
      <div className='grid grid-cols-2 gap-x-4 gap-y-5 p-4'>
        <ValueDetail label='Trạng thái tiền nhiệm' value={selectedState.data.sourceName} />
        <ValueDetail label='Trạng thái đích' value={selectedState.data.targetName} />
      </div>
    </div>
  )
}
