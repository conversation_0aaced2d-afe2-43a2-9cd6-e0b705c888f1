'use client'

import React from 'react'

import { SectionHeader } from '@/components/workflow/shared/section-header/SectionHeader'
import { ValueDetail } from '@/components/workflow/shared/value-detail/ValueDetail'
import {
  DAY_IN_MONTH,
  SCHEDULE_FREQUENCY_VALUES,
  SCHEDULE_IN_WEEK_VALUES,
  SCHEDULE_TYPE_VALUES,
  SCHEDULE_WEEK_OF_MONTH_VALUES
} from '@/constants/workflow/trigger'
import { useConditionRenderer } from '@/hooks/workflow'
import { ConditionDetail } from './ConditionDetail'

interface ScheduleSectionProps {
  triggerData: any
  collapsedSection: boolean
  toggleSection: (section: 'schedule') => void
}

// Kiểm tra object có rỗng hay không
const isObjectEmpty = (obj: any) => {
  return !obj || Object.keys(obj).length === 0
}

export const ScheduleSection: React.FC<ScheduleSectionProps> = ({ triggerData, collapsedSection, toggleSection }) => {
  const { renderConditionText, convertToTreeData } = useConditionRenderer()

  if (isObjectEmpty(triggerData)) {
    return null
  }

  return (
    <div className='rounded-lg border border-solid border-border-neutral-medium bg-white'>
      <SectionHeader
        title='Lịch trình'
        collapsed={collapsedSection}
        onToggle={() => toggleSection('schedule')}
        isDetail
      />
      {!collapsedSection && (
        <div className='space-y-4 p-3'>
          <div className='grid grid-cols-4 gap-x-4 gap-y-5 text-sm text-gray-600'>
            <ValueDetail
              label='Thời điểm quét'
              value={
                SCHEDULE_TYPE_VALUES.find(item => item.value === triggerData?.type)?.label ||
                triggerData?.frequency ||
                'Hàng ngày'
              }
            />
            <ValueDetail
              label='Tần suất'
              value={
                SCHEDULE_FREQUENCY_VALUES.find(item => item.value === triggerData?.intervalType)?.label ||
                `${triggerData?.interval || 1} lần`
              }
            />
            <ValueDetail label='Thời gian bắt đầu' value={triggerData?.startTime || '09:00'} />
            <ValueDetail
              label='Ngày bắt đầu - kết thúc'
              value={`${triggerData?.startDate} - ${triggerData?.endDate}`}
            />
            {/* Thêm hiển thị các ngày trong tuần nếu có */}
            {triggerData?.dayOfWeek && triggerData.dayOfWeek.length > 0 && (
              <ValueDetail
                label='Các ngày trong tuần'
                value={triggerData.dayOfWeek
                  .map((day: string) => SCHEDULE_IN_WEEK_VALUES.find(item => item.value === day)?.label || day)
                  .join(', ')}
              />
            )}
            {/* Thêm hiển thị các ngày trong tháng nếu có */}
            {triggerData?.dayOfMonth && triggerData.dayOfMonth.length > 0 && (
              <ValueDetail
                label='Các ngày trong tháng'
                value={triggerData.dayOfMonth
                  .map((day: number) => DAY_IN_MONTH.find(item => item.value === day)?.label || `Ngày ${day}`)
                  .join(', ')}
              />
            )}
            {/* Thêm hiển thị tuần trong tháng nếu có */}
            {triggerData?.weekOfMonth && triggerData.weekOfMonth.length > 0 && (
              <ValueDetail
                label='Tuần trong tháng'
                value={triggerData.weekOfMonth
                  .map(
                    (week: number) =>
                      SCHEDULE_WEEK_OF_MONTH_VALUES.find(item => item.value === week)?.label || `Tuần ${week}`
                  )
                  .join(', ')}
              />
            )}
          </div>
          {/* Render điều kiện sử dụng hook chung */}
          {triggerData?.conditions && triggerData.conditions.length > 0 && (
            <div className='space-y-4'>
              {triggerData.conditions.map((condition: any, index: number) => {
                const conditionTexts = renderConditionText(condition)

                return (
                  <div key={index}>
                    <div className='body-14-regular mb-2 text-text-neutral-medium'>Điều kiện {index + 1}</div>
                    <ConditionDetail treeData={convertToTreeData(conditionTexts, index)} />
                  </div>
                )
              })}
            </div>
          )}
        </div>
      )}
    </div>
  )
}
