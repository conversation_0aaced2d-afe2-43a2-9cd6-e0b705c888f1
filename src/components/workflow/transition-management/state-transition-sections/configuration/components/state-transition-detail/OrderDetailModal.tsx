// pages/order-detail.tsx
import React from 'react'

import { Table, Button, Modal, Spin } from 'antd'
import type { ColumnsType, TableProps } from 'antd/es/table'
import { styled } from 'styled-components'
import { useQuery } from '@tanstack/react-query'

import { TRIGGER_TYPE_MAP } from '@/constants/workflow'
import { stateTransitionConfig } from '@/models/workflow/stateTransitionConfig'
import type { OrderRecord, ProductData } from '@/types/workflow/transition'

// Sử dụng OrderRecord từ types/workflow/transition

const columns: ColumnsType<OrderRecord> = [
  {
    title: 'Trạng thái bắt đầu',
    dataIndex: 'preStateName',
    key: 'preStateName'
  },
  {
    title: 'Trạng thái kết thúc',
    dataIndex: 'stateName',
    key: 'stateName'
  },
  {
    title: 'Kết quả',
    dataIndex: 'result',
    key: 'result',
    render: text => {
      const bgColor = text === 'Thành công' ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'

      return <div className={`rounded-md px-2 py-1 text-xs font-medium ${bgColor} w-30 text-center`}>{text}</div>
    }
  },
  {
    title: 'Trigger',
    dataIndex: 'triggerType',
    key: 'triggerType',
    render: text => TRIGGER_TYPE_MAP[text]
  },
  {
    title: 'Vai trò',
    dataIndex: 'role',
    key: 'role'
  },
  {
    title: 'Thời gian',
    dataIndex: 'createdAt',
    key: 'createdAt'
  }
]

export const OrderDetailModal = (props: { open: boolean; onCancel: () => void; subscription?: ProductData }) => {
  const { open, onCancel, subscription } = props

  const subscriptionId = subscription?.key

  //  API danh sách lịch sử chuyển đổi trạng thái của đơn hàng
  const { data: orderHistoryData, isLoading } = useQuery({
    queryKey: ['order-history', subscriptionId],
    queryFn: () => stateTransitionConfig.getOrderHistory(subscriptionId as number),
    enabled: !!subscriptionId,
    select: data => {
      // Xử lý dữ liệu trả về từ API
      return (
        data?.content?.map((item: any) => ({
          ...item,
          key: item.id || `${item.preStateName}-${item.stateName}-${item.createdAt}`
        })) || []
      )
    }
  })

  return (
    <Modal
      open={open}
      footer={
        <div className='text-right'>
          <Button onClick={onCancel} className='rounded px-6'>
            Đóng
          </Button>
        </div>
      }
      onCancel={onCancel}
      title={<div className='text-lg font-semibold'>Xem chi tiết đơn hàng &quot;{subscription?.name}&quot;</div>}
      width={1200}
      className='rounded-lg'
    >
      {isLoading ? (
        <div className='flex items-center justify-center py-8'>
          <Spin size='large' />
        </div>
      ) : (
        <CustomTable
          columns={columns}
          dataSource={orderHistoryData}
          pagination={false}
          rowKey='key'
          className='rounded-md border border-gray-200'
        />
      )}
    </Modal>
  )
}

const CustomTable = styled(Table)<TableProps<any>>`
  .ant-table-cell-with-append {
    display: flex;
  }
`
