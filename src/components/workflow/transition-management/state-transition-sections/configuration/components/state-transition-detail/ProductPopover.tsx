'use client'

import { useState } from 'react'

import { Button, Popover, Table } from 'antd'
import type { ColumnsType } from 'antd/es/table'

import { handleSrcImg } from '@/utils/string'
import { pricingUtils } from '@/utils/products/pricing'
import { OrderDetailModal } from './OrderDetailModal'
import type { ProductData, ProductPopoverProps } from '@/types/workflow/transition'
import { CYCLE_FREQUENCY_MAPPING } from '@/views/product-catalog/constants/constants'

const ProductPopover: React.FC<ProductPopoverProps> = ({ children, orderItems = [] }) => {
  const [openOrderDetailModal, setOpenOrderDetailModal] = useState(false)
  const [selectedSubscription, setSelectedSubscription] = useState<ProductData>()

  const dataSource: ProductData[] = orderItems.map(item => {
    // Xử lý hiển thị thông tin paymentCycle, cycleType, numberOfCycles
    const cycle = pricingUtils.cycle({
      paymentCycle: item?.paymentCycle,
      cycleType: CYCLE_FREQUENCY_MAPPING[item?.cycleType as keyof typeof CYCLE_FREQUENCY_MAPPING],
      numberOfCycle: item?.numberOfCycles
    })

    return {
      key: item.subId,
      image: item.serviceIconUrl || '',
      name: item.serviceName || '',
      variant: item.variantName || '',
      package: item.pricingName || '',
      cycle: item.isOneTime ? cycle.isRender && <span>({cycle.cycleDescription})</span> : '(Gói cước áp dụng 1 lần)',
      sku: item.sku || '',
      price: item.unitPrice || 0,
      quantity: item.quantity || 1,
      status: item?.stateDisplayName || ''
    }
  })

  const handleViewSubscriptionDetail = (record: ProductData) => {
    setSelectedSubscription(record)
    setOpenOrderDetailModal(true)
  }

  const productName = `Sản phẩm (${orderItems?.length || 0})`

  const columns: ColumnsType<ProductData> = [
    {
      title: `${productName}`,
      dataIndex: 'productInfo',
      key: 'productInfo',
      width: 200,
      render: (_, record) => (
        <div className='flex gap-2'>
          <img src={handleSrcImg(record.image)} alt={record.name} className='size-10 rounded-md object-cover' />
          <div className='space-y-1'>
            <div className='body-14-medium'>{record.name}</div>
            <div className='caption-12-medium text-text-neutral-medium'>
              {record.package} {record.cycle}
            </div>
            {record.variant && <div className='caption-12-medium text-text-neutral-medium'>{record.variant}</div>}
          </div>
        </div>
      )
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      align: 'center',
      render: text => (
        <div className='caption-12-medium rounded-md bg-[#CDE4FE] px-2 py-1 text-text-primary-default'>{text}</div>
      )
    },
    {
      title: 'Hành động',
      dataIndex: 'action',
      key: 'action',
      width: 80,
      align: 'center',
      render: (_, record) => (
        <Button type='link' onClick={() => handleViewSubscriptionDetail(record)}>
          Xem chi tiết
        </Button>
      )
    }
  ]

  return (
    <>
      {/* Popover hiển thị danh sách sản phẩm */}
      <Popover
        content={
          <div className='w-[900px]'>
            <Table size='small' pagination={false} columns={columns} dataSource={dataSource} />
          </div>
        }
        trigger='hover'
        placement='right'
      >
        {children}
      </Popover>

      {/* Modal chi tiết đơn hàng */}
      {openOrderDetailModal && (
        <OrderDetailModal
          subscription={selectedSubscription}
          open={openOrderDetailModal}
          onCancel={() => setOpenOrderDetailModal(false)}
        />
      )}
    </>
  )
}

export default ProductPopover
