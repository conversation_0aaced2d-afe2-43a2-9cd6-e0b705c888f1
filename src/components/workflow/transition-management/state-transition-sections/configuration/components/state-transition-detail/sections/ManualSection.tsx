'use client'

import React from 'react'

import { SectionHeader } from '@/components/workflow/shared/section-header/SectionHeader'
import { ValueDetail } from '@/components/workflow/shared/value-detail/ValueDetail'

interface ManualSectionProps {
  triggerData: any
  collapsedSection: boolean
  toggleSection: (section: 'manual') => void
}

// Hàm để convert tên agent/role thành Admin/Đối tác
const convertAgentTypeDisplay = (agentTypes: string[]): string => {
  if (agentTypes && agentTypes.some(type => type?.includes('ADMIN'))) {
    return 'Admin'
  }

  return 'Đối tác'
}

// Kiểm tra object có rỗng hay không
const isObjectEmpty = (obj: any) => {
  return !obj || Object.keys(obj).length === 0
}

export const ManualSection: React.FC<ManualSectionProps> = ({ triggerData, collapsedSection, toggleSection }) => {
  if (isObjectEmpty(triggerData)) {
    return null
  }

  return (
    <div className='rounded-lg border border-solid border-border-neutral-medium bg-white'>
      <SectionHeader title='Manual' collapsed={collapsedSection} onToggle={() => toggleSection('manual')} isDetail />
      {!collapsedSection && (
        <div className='p-3'>
          <div className='grid grid-cols-2 gap-x-4 gap-y-3'>
            <ValueDetail
              label='Tác nhân thực hiện'
              value={convertAgentTypeDisplay(triggerData.agentTypes) || 'Không có'}
            />
            <ValueDetail label='Vai trò' value={convertAgentTypeDisplay(triggerData.roles) || 'Không có'} />
          </div>
        </div>
      )}
    </div>
  )
}
