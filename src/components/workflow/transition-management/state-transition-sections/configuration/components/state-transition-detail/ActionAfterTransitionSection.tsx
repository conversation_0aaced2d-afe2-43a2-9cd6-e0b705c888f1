'use client'

import React from 'react'

import { SectionHeader } from '@/components/workflow/shared/section-header/SectionHeader'
import type { TransitionStateTableItem } from '@/types/workflow/transition'
import type { PostAction } from '@/types/workflow/postAction'
import { useActionAfterTransition } from '@/hooks/workflow/useActionAfterTransition'
import { WebhookSection } from './WebhookSection'
import { NotificationSection } from './NotificationSection'
import { ActionAfterTransitionModals } from './ActionAfterTransitionModals'

interface ActionAfterTransitionSectionProps {
  selectedState: TransitionStateTableItem
  collapsed: boolean
  onToggle: () => void
}

export const ActionAfterTransitionSection: React.FC<ActionAfterTransitionSectionProps> = ({
  selectedState,
  collapsed,
  onToggle
}) => {
  const postActions: PostAction[] = selectedState.postActions || []

  const { webhookData, emailNotification, appNotification, getNotificationMethods, modals } =
    useActionAfterTransition(postActions)

  return (
    <div className='rounded-lg bg-bg-neutral-lightest'>
      <SectionHeader
        title='Hành động sau khi chuyển đổi trạng thái'
        collapsed={collapsed}
        onToggle={onToggle}
        component={
          <div className='flex items-center gap-2'>
            <i className='onedx-edit size-4 text-icon-primary-default' />
            <div className='body-14-medium text-text-primary-default'>Chỉnh sửa</div>
          </div>
        }
      />
      {!collapsed && (
        <div className='space-y-4 p-3'>
          {!postActions || postActions.length === 0 ? (
            <div className='rounded-lg border border-solid border-border-neutral-medium bg-white p-3'>
              <div className='text-center text-gray-500'>Chưa có hành động nào được cấu hình</div>
            </div>
          ) : (
            <>
              {/* Webhook Section */}
              {webhookData && (
                <WebhookSection
                  webhookData={webhookData}
                  onOpenDataMapping={modals.openDataMappingModal}
                  onOpenUrlVariables={modals.openUrlVariablesModal}
                />
              )}

              {/* Notification Section */}
              {(emailNotification || appNotification) && (
                <NotificationSection
                  getNotificationMethods={getNotificationMethods}
                  emailNotification={emailNotification}
                  appNotification={appNotification}
                  onOpenEmailDetail={modals.openEmailDetailModal}
                />
              )}
            </>
          )}
        </div>
      )}

      {/* Modals */}
      <ActionAfterTransitionModals
        modals={modals}
        webhookData={webhookData}
        emailNotification={emailNotification}
        postActions={postActions}
      />
    </div>
  )
}
