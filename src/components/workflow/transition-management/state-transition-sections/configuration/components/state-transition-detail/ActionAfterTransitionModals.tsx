import React from 'react'

import { <PERSON><PERSON>, Modal } from 'antd'

import { DataMappingModal } from './DataMappingModal'
import { POST_ACTION_TYPE } from '@/constants/workflow/stateTransition'
import type { PostAction } from '@/types/workflow/postAction'

interface ActionAfterTransitionModalsProps {
  modals: {
    isDataMappingModalOpen: boolean
    closeDataMappingModal: () => void
    isEmailDetailModalOpen: boolean
    closeEmailDetailModal: () => void
    isUrlVariablesModalOpen: boolean
    closeUrlVariablesModal: () => void
  }
  webhookData: any
  emailNotification: any
  postActions: PostAction[]
}

export const ActionAfterTransitionModals: React.FC<ActionAfterTransitionModalsProps> = ({
  modals,
  webhookData,
  emailNotification,
  postActions
}) => {
  return (
    <>
      {/* Ánh xạ dữ liệu */}
      {modals.isDataMappingModalOpen && (
        <DataMappingModal
          open={modals.isDataMappingModalOpen}
          onCancel={modals.closeDataMappingModal}
          type='webhook'
          triggerData={postActions?.find((action: PostAction) => action.postActionType === POST_ACTION_TYPE.WEBHOOK)}
        />
      )}

      {/* Modal Email Detail */}
      {modals.isEmailDetailModalOpen && emailNotification && (
        <Modal
          title='Email'
          open={modals.isEmailDetailModalOpen}
          onCancel={modals.closeEmailDetailModal}
          footer={[
            <Button key='close' type='default' onClick={modals.closeEmailDetailModal}>
              Đóng
            </Button>
          ]}
          width={680}
          styles={{
            header: {
              borderBottom: '1px solid #f0f0f0',
              paddingBottom: '12px'
            },
            footer: {
              borderTop: '1px solid #f0f0f0',
              paddingTop: '12px'
            }
          }}
        >
          {emailNotification.contentType === 'TEMPLATE' ? (
            <div>
              {emailNotification.templateHtmlBody && (
                <div
                  className='rounded-lg border bg-gray-50 p-4'
                  dangerouslySetInnerHTML={{
                    __html: emailNotification.templateHtmlBody
                      .replace('$HEADER', emailNotification.templateHtmlHeader || '')
                      .replace('$FOOTER', emailNotification.templateHtmlFooter || '')
                  }}
                />
              )}
            </div>
          ) : (
            <div>
              <div className='body-14-medium mb-1'>{emailNotification.customTitle}</div>
              <div className='body-14-regular'>{emailNotification.customContent}</div>
            </div>
          )}
        </Modal>
      )}

      {/* Modal hiển thị URL Variables */}
      <Modal
        title='Xem chi tiết Params/ Path variable'
        open={modals.isUrlVariablesModalOpen}
        onCancel={modals.closeUrlVariablesModal}
        footer={[
          <Button key='close' onClick={modals.closeUrlVariablesModal}>
            Đóng
          </Button>
        ]}
        width={800}
      >
        <div className='rounded border bg-gray-50 p-3'>
          <pre className='whitespace-pre-wrap text-sm'>
            {JSON.stringify(
              {
                url: {
                  raw: webhookData?.url?.raw?.trim(),
                  variables: webhookData?.url?.variables || []
                }
              },
              null,
              2
            )}
          </pre>
        </div>
      </Modal>
    </>
  )
}