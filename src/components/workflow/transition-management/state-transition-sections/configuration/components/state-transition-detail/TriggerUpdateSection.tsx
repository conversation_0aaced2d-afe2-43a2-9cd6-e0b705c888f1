'use client'

import React from 'react'

import { SectionHeader } from '@/components/workflow/shared/section-header/SectionHeader'
import type { TransitionStateTableItem } from '@/types/workflow/transition'
import { ManualSection, ApiCallSection, WebhookSection, ScheduleSection, ConditionSection } from './sections'

interface TriggerUpdateSectionProps {
  selectedState: TransitionStateTableItem
  collapsedSections: {
    trigger: boolean
    manual: boolean
    apiCall: boolean
    webhook: boolean
    schedule: boolean
    condition: boolean
  }
  toggleSection: (section: 'trigger' | 'manual' | 'apiCall' | 'webhook' | 'schedule' | 'condition') => void
  triggerData: any // Dữ liệu trigger để hiển thị
}

// Kiểm tra object có rỗng hay không
const isObjectEmpty = (obj: any) => {
  return !obj || Object.keys(obj).length === 0
}

export const TriggerUpdateSection: React.FC<TriggerUpdateSectionProps> = ({
  collapsedSections,
  toggleSection,
  triggerData
}) => {
  return (
    <div className='rounded-lg bg-bg-neutral-lightest'>
      <SectionHeader
        title='Trigger cập nhật'
        collapsed={collapsedSections.trigger}
        onToggle={() => toggleSection('trigger')}
        component={
          <div className='flex items-center gap-2'>
            <i className='onedx-edit size-4 text-icon-primary-default' />
            <div className='body-14-medium text-text-primary-default'>Chỉnh sửa</div>
          </div>
        }
      />
      {!collapsedSections.trigger && (
        <div className='space-y-4 p-4'>
          {/* Manual */}
          {!isObjectEmpty(triggerData?.manual) && (
            <ManualSection
              collapsedSection={collapsedSections.manual}
              toggleSection={() => toggleSection('manual')}
              triggerData={triggerData.manual}
            />
          )}

          {/* API Call */}
          {!isObjectEmpty(triggerData?.apiCall) && (
            <ApiCallSection
              collapsedSection={collapsedSections.apiCall}
              toggleSection={() => toggleSection('apiCall')}
              triggerData={triggerData}
            />
          )}

          {/* Webhook */}
          {!isObjectEmpty(triggerData?.webhook) && (
            <WebhookSection
              collapsedSection={collapsedSections.webhook}
              toggleSection={() => toggleSection('webhook')}
              triggerData={triggerData.webhook}
            />
          )}

          {/* Lịch trình */}
          {!isObjectEmpty(triggerData?.schedule) && (
            <ScheduleSection
              collapsedSection={collapsedSections.schedule}
              toggleSection={() => toggleSection('schedule')}
              triggerData={triggerData.schedule}
            />
          )}

          {/* Điều kiện */}
          {!isObjectEmpty(triggerData?.condition) && (
            <ConditionSection
              collapsedSection={collapsedSections.condition}
              toggleSection={() => toggleSection('condition')}
              triggerData={triggerData.condition}
            />
          )}
        </div>
      )}
    </div>
  )
}
