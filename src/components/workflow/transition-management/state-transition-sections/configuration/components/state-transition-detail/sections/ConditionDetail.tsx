import React from 'react'

import { Tree } from 'antd'
import type { TreeDataNode } from 'antd'
import { styled } from 'styled-components'

const StyledConditionTree = styled(Tree)`
  .ant-tree-list {
    background-color: rgba(2, 23, 60, 0.02);
    padding: 8px;
    border-radius: 8px;
  }

  /* bỏ indent mặc định của antd */
  .ant-tree-indent,
  .ant-tree-switcher {
    display: none !important;
  }

  .ant-tree-treenode {
    position: relative;
    display: flex;
    align-items: center;
    margin: 6px;
    padding-inline-start: 20px; /* cột trái cho line/dot */
  }

  .ant-tree-node-content-wrapper {
    margin-inline-start: 0 !important; /* đ<PERSON><PERSON> bảo chữ thẳng */
  }

  /* line dọc cho node con */
  .ant-tree-treenode::before {
    content: '';
    position: absolute;
    inset-block: 0;
    inset-inline-start: 6px;
    border-inline-start: 1px dashed #1890ff;
    block-size: 36px;
  }

  /* line ngang */
  .ant-tree-treenode::after {
    content: '';
    position: absolute;
    inset-block-start: 14px;
    inset-inline-start: 8px;
    inline-size: 12px;
    border-block-start: 1px dashed #1890ff;
  }

  /* chặn line kéo quá ở node lá */
  .ant-tree-treenode:last-child::before {
    inset-block-end: auto;
    block-size: 14px;
  }

  /* dot xanh cho root */
  .ant-tree-treenode:first-child::before {
    content: '';
    position: absolute;
    inset-block-start: 10px;
    inset-inline-start: 4px;
    inline-size: 8px;
    block-size: 8px;
    border-radius: 50%;
    background: #1890ff;
  }

  /* nhánh ngang cho root */
  .ant-tree-treenode:first-child::after {
    content: '';
    position: absolute;
    inset-block-start: 14px;
    inline-size: 14px;
    inset-inline-start: 6px;
    border-inline-start: 1px dashed #1890ff;
    block-size: 22px;
  }

  /* chặn line kéo quá ở node lá */
  .ant-tree-treenode:last-child::after {
    border-inline-start: none;
  }
`

// Example tree data
// const treeDataMock: TreeDataNode[] = [
//   {
//     title: 'Title 1',
//     key: '0-0'
//   },
//   { title: 'Title 2', key: '0-1' },
//   { title: 'Title 3', key: '0-2' }
// ]

export const ConditionDetail = ({ treeData }: { treeData?: TreeDataNode[] }) => {
  return <StyledConditionTree treeData={treeData} defaultExpandAll selectable={false} showLine={false} />
}
