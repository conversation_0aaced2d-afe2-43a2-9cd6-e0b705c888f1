'use client'

import React, { useState } from 'react'

import { But<PERSON> } from 'antd'

import { SectionHeader } from '@/components/workflow/shared/section-header/SectionHeader'
import { ValueDetail } from '@/components/workflow/shared/value-detail/ValueDetail'
import type { MappingDataType } from '@/types/workflow/transition'
import { DataMappingModal } from '../DataMappingModal'

interface WebhookSectionProps {
  triggerData: any
  collapsedSection: boolean
  toggleSection: (section: 'webhook') => void
}

// Kiểm tra object có rỗng hay không
const isObjectEmpty = (obj: any) => {
  return !obj || Object.keys(obj).length === 0
}

export const WebhookSection: React.FC<WebhookSectionProps> = ({ triggerData, collapsedSection, toggleSection }) => {
  const [openDataMappingModal, setOpenDataMappingModal] = useState(false)
  const [typeMappingData, setTypeMappingData] = useState<MappingDataType>('webhook')

  if (isObjectEmpty(triggerData)) {
    return null
  }

  return (
    <div className='rounded-lg border border-solid border-border-neutral-medium bg-white'>
      <SectionHeader title='Webhook' collapsed={collapsedSection} onToggle={() => toggleSection('webhook')} isDetail />
      {!collapsedSection && (
        <div className='space-y-4 p-3'>
          <ValueDetail
            label='Sử dụng webhook mặc định của hệ thống'
            value={triggerData.url || 'URL://httpvnpt-technology.vn'}
          />
          {/* JSON Data */}
          <div className='overflow-auto rounded-md bg-gray-100 px-3 font-mono text-sm font-medium text-text-neutral-light shadow-inner'>
            {/* TODO: Mapping data để hiển thị JSON Data */}
            {/* <pre>{JSON.stringify(triggerData??.jsonData, null, 2)}</pre> */}
          </div>
          {/* Ánh xạ dữ liệu */}
          <div className='flex items-center justify-between rounded-lg bg-bg-neutral-lightest p-3'>
            <div className='body-14-regular'>
              <i className='onedx-dot mr-1.5 size-2 text-icon-success-default' /> Ánh xạ dữ liệu
            </div>
            <div className='text-right'>
              <Button
                type='default'
                className='border-text-primary-default text-text-primary-default'
                onClick={() => {
                  setOpenDataMappingModal(true)
                  setTypeMappingData('webhook')
                }}
              >
                Xem chi tiết
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Modal Ánh xạ dữ liệu */}
      {openDataMappingModal && (
        <DataMappingModal
          open={openDataMappingModal}
          onCancel={() => setOpenDataMappingModal(false)}
          type={typeMappingData}
          triggerData={triggerData}
        />
      )}
    </div>
  )
}
