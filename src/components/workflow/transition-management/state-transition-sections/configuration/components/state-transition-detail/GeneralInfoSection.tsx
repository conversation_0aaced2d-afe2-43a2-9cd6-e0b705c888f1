'use client'

import React from 'react'

import * as antdIcons from '@ant-design/icons'

import { SectionHeader } from '@/components/workflow/shared/section-header/SectionHeader'
import { ValueDetail } from '@/components/workflow/shared/value-detail/ValueDetail'
import type { TransitionStateTableItem } from '@/types/workflow/transition'
import { CLASSIFICATION_NAME } from '@/constants/workflow'
import * as customIcons from '@/assets/builder-icons'

interface GeneralInfoSectionProps {
  selectedState: TransitionStateTableItem
  collapsed: boolean
  onToggle: () => void
}

const renderIconValue = (value: any) => {
  if (!value || value === '') return <i className='onedx-document size-5' />

  // @ts-ignore
  // eslint-disable-next-line import/namespace
  const IconComponent = antdIcons[value as string] || customIcons[value as string]

  return (
    <div className='flex items-center gap-x-2 text-text-neutral-strong'>
      {IconComponent && <IconComponent style={{ fontSize: '14px' }} />}
      <div className='max-w-[120px] truncate'>{value}</div>
    </div>
  )
}

export const GeneralInfoSection: React.FC<GeneralInfoSectionProps> = ({ selectedState, collapsed, onToggle }) => {
  return (
    <div className='rounded-lg bg-bg-neutral-lightest'>
      <SectionHeader
        title='Thông tin chung'
        collapsed={collapsed}
        onToggle={onToggle}
        component={
          <div className='flex items-center gap-2'>
            <i className='onedx-edit size-4 text-icon-primary-default' />
            <div className='body-14-medium text-text-primary-default'>Chỉnh sửa</div>
          </div>
        }
      />
      {!collapsed && (
        <div className='grid grid-cols-4 gap-x-4 gap-y-5 p-4'>
          {/* Tên trạng thái */}
          <div>
            <div className='mb-2 text-sm font-normal leading-5 tracking-tight-05 text-gray-8'>Tên trạng thái</div>
            <div className='flex items-center gap-2'>
              <div className='line-clamp-1 text-sm font-medium leading-5 tracking-tight-05 text-gray-11'>
                {selectedState.name}
              </div>
              <div className='size-4 min-w-4 rounded-full' style={{ backgroundColor: selectedState.color }} />
            </div>
          </div>
          <ValueDetail label='Tên hiển thị' value={selectedState.displayName} />
          <div>
            <div className='mb-2 text-sm font-normal leading-5 tracking-tight-05 text-gray-8'>Icon</div>
            {renderIconValue(selectedState.icon)}
          </div>
          <ValueDetail label='Mã trạng thái' value={selectedState.code} />
          <ValueDetail label='Loại trạng thái' value={selectedState.type} />
          <ValueDetail
            label='Đối tượng'
            value={CLASSIFICATION_NAME[selectedState.objectType as keyof typeof CLASSIFICATION_NAME]}
          />
          <ValueDetail label='Mô tả' value={selectedState.description} />
        </div>
      )}
    </div>
  )
}
