'use client'

import React from 'react'

import { SectionHeader } from '@/components/workflow/shared/section-header/SectionHeader'
import { ConditionDetail } from './ConditionDetail'
import { useConditionRenderer } from '@/hooks/workflow/useConditionRenderer'

interface ConditionSectionProps {
  triggerData: any
  collapsedSection: boolean
  toggleSection: (section: 'condition') => void
}

// Kiểm tra object có rỗng hay không
const isObjectEmpty = (obj: any) => {
  return !obj || Object.keys(obj).length === 0
}

export const ConditionSection: React.FC<ConditionSectionProps> = ({ triggerData, collapsedSection, toggleSection }) => {
  const { renderConditionText, convertToTreeData } = useConditionRenderer()

  if (isObjectEmpty(triggerData)) {
    return null
  }

  return (
    <div className='rounded-lg border border-solid border-border-neutral-medium bg-white'>
      <SectionHeader
        title='Điều kiện'
        collapsed={collapsedSection}
        onToggle={() => toggleSection('condition')}
        isDetail
      />
      {!collapsedSection && (
        <div className='space-y-4 p-3'>
          {triggerData.conditions?.map((condition: any, index: number) => {
            const conditionTexts = renderConditionText(condition)

            return (
              <div key={index}>
                <div className='body-14-regular mb-2 text-text-neutral-medium'>Điều kiện {index + 1}</div>
                <ConditionDetail treeData={convertToTreeData(conditionTexts, index)} />
              </div>
            )
          })}
        </div>
      )}
    </div>
  )
}
