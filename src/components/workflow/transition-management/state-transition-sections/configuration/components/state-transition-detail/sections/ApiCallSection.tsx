'use client'

import React, { useState } from 'react'

import { But<PERSON> } from 'antd'

import { SectionHeader } from '@/components/workflow/shared/section-header/SectionHeader'
import { ValueDetail } from '@/components/workflow/shared/value-detail/ValueDetail'
import type { MappingDataType } from '@/types/workflow/transition'
import { DataMappingModal } from '../DataMappingModal'
import {
  DAY_IN_MONTH,
  SCHEDULE_FREQUENCY_VALUES,
  SCHEDULE_IN_WEEK_VALUES,
  SCHEDULE_TYPE_VALUES,
  SCHEDULE_WEEK_OF_MONTH_VALUES
} from '@/constants/workflow/trigger'

interface ApiCallSectionProps {
  triggerData: any
  collapsedSection: boolean
  toggleSection: (section: 'apiCall') => void
}

// Kiểm tra object có rỗng hay không
const isObjectEmpty = (obj: any) => {
  return !obj || Object.keys(obj).length === 0
}

export const ApiCallSection: React.FC<ApiCallSectionProps> = ({ triggerData, collapsedSection, toggleSection }) => {
  const [openDataMappingModal, setOpenDataMappingModal] = useState(false)
  const [typeMappingData, setTypeMappingData] = useState<MappingDataType>('api')

  if (isObjectEmpty(triggerData)) {
    return null
  }

  return (
    <div className='rounded-lg border border-solid border-border-neutral-medium bg-white'>
      <SectionHeader title='API Call' collapsed={collapsedSection} onToggle={() => toggleSection('apiCall')} isDetail />
      {!collapsedSection && (
        <div className='space-y-4 p-3'>
          {/* Cấu hình endpoint */}
          <div className='rounded-lg bg-bg-neutral-lightest p-3'>
            <div className='body-14-regular mb-5'>
              <i className='onedx-dot mr-1.5 size-2 text-icon-success-default' /> Cấu hình endpoint
            </div>
            <div className='grid grid-cols-4 gap-x-4 gap-y-5'>
              <ValueDetail label='Phương thức kết nối' value={triggerData?.apiCall?.method || 'API Call'} />
              <ValueDetail label='URL Endpoint' value={triggerData?.apiCall?.url || 'Không có'} />
              <ValueDetail label='Params/ Parth variable' value='Xem chi tiết' />
              <ValueDetail label='Phương thức xác thực API' value={triggerData?.apiCall?.authMethod || 'API'} />
              <ValueDetail label='Header' value={JSON.stringify(triggerData?.apiCall?.headers) || 'Không có'} />
              <ValueDetail label='Token Value' value={triggerData?.apiCall?.tokenValue || 'Không có'} />
            </div>
          </div>

          {/* Lịch trình đồng bộ */}
          <div className='rounded-lg bg-bg-neutral-lightest p-3'>
            <div className='body-14-regular mb-5'>
              <i className='onedx-dot mr-1.5 size-2 text-icon-success-default' /> Lịch trình đồng bộ
            </div>
            <div className='grid grid-cols-4 gap-x-4 gap-y-5 text-sm text-gray-600'>
              <ValueDetail
                label='Thời điểm đồng bộ'
                value={
                  SCHEDULE_TYPE_VALUES.find(item => item.value === triggerData.apiSchedule?.type)?.label ||
                  triggerData.apiSchedule?.frequency ||
                  '_'
                }
              />
              <ValueDetail
                label='Tần suất'
                value={
                  SCHEDULE_FREQUENCY_VALUES.find(item => item.value === triggerData.apiSchedule?.intervalType)?.label ||
                  `${triggerData.apiSchedule?.interval || 0} lần`
                }
              />
              <ValueDetail label='Thời gian' value={triggerData.apiSchedule?.startTime || '-'} />
              <ValueDetail
                label='Ngày bắt đầu - kết thúc'
                value={`${triggerData.apiSchedule?.startDate || '-'} - ${triggerData.apiSchedule?.endDate || '-'}`}
              />
              {/* Hiển thị thêm thông tin nếu có */}
              {triggerData.apiSchedule?.dayOfWeek && triggerData.apiSchedule.dayOfWeek.length > 0 && (
                <ValueDetail
                  label='Các ngày trong tuần'
                  value={triggerData.apiSchedule.dayOfWeek
                    .map((day: string) => SCHEDULE_IN_WEEK_VALUES.find(item => item.value === day)?.label || day)
                    .join(', ')}
                />
              )}
              {triggerData.apiSchedule?.dayOfMonth && triggerData.apiSchedule.dayOfMonth.length > 0 && (
                <ValueDetail
                  label='Các ngày trong tháng'
                  value={triggerData.apiSchedule.dayOfMonth
                    .map((day: number) => DAY_IN_MONTH.find(item => item.value === day)?.label || `Ngày ${day}`)
                    .join(', ')}
                />
              )}
              {triggerData.apiSchedule?.weekOfMonth && triggerData.apiSchedule.weekOfMonth.length > 0 && (
                <ValueDetail
                  label='Tuần trong tháng'
                  value={triggerData.apiSchedule.weekOfMonth
                    .map(
                      (week: number) =>
                        SCHEDULE_WEEK_OF_MONTH_VALUES.find(item => item.value === week)?.label || `Tuần ${week}`
                    )
                    .join(', ')}
                />
              )}
            </div>
          </div>

          {/* Ánh xạ dữ liệu */}
          <div className='flex items-center justify-between rounded-lg bg-bg-neutral-lightest p-3'>
            <div className='body-14-regular'>
              <i className='onedx-dot mr-1.5 size-2 text-icon-success-default' /> Ánh xạ dữ liệu
            </div>
            <div className='text-right'>
              <Button
                type='default'
                className='border-text-primary-default text-text-primary-default'
                onClick={() => {
                  setOpenDataMappingModal(true)
                  setTypeMappingData('api')
                }}
              >
                Xem chi tiết
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Modal Ánh xạ dữ liệu */}
      {openDataMappingModal && (
        <DataMappingModal
          open={openDataMappingModal}
          onCancel={() => setOpenDataMappingModal(false)}
          type={typeMappingData}
          triggerData={triggerData}
        />
      )}
    </div>
  )
}
