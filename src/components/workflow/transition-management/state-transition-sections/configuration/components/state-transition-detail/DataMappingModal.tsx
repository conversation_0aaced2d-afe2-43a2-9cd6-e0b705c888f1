'use client'

import React from 'react'

import type { TabsProps } from 'antd'
import { Modal, Button, Table, Tabs } from 'antd'
import type { ColumnsType } from 'antd/es/table'

import { styled } from 'styled-components'

import type { MappingDataType } from '@/types/workflow/transition'

// region columnsTab1
const columnsTab1: ColumnsType<any> = [
  {
    title: 'Trường đối tác',
    dataIndex: 'field',
    width: '25%',
    key: 'field',
    render: text => <div className='body-14-medium text-text-neutral-strong'>{text}</div>
  },
  {
    title: 'Kiểu dữ liệu',
    dataIndex: 'dataType',
    key: 'dataType',
    width: '25%'
  },
  {
    title: 'Trạng thái',
    dataIndex: 'status',
    key: 'status',
    width: '25%',
    render: text => {
      const bgColor = text === 'Bắt buộc' ? 'bg-red-100 text-red-600' : 'bg-blue-100 text-blue-600'

      return <div className={`w-36 rounded-md px-2 py-1 text-center text-xs font-medium ${bgColor}`}>{text}</div>
    }
  },
  {
    title: 'Tham số đầu vào',
    dataIndex: 'param',
    key: 'param'
  }
]
// endregion

// region columnsTab2
const columnsTab2: ColumnsType<any> = [
  {
    title: 'Trường nguồn',
    dataIndex: 'source',
    key: 'source'
  },
  {
    title: 'Trường đối tác',
    dataIndex: 'targetField',
    key: 'targetField',
    render: text => <div className='body-14-medium text-text-neutral-strong'>{text}</div>
  },
  {
    title: 'Giá trị',
    dataIndex: 'value',
    key: 'value'
  }
]
// endregion

// region columnsAction
const columnsAction: ColumnsType<any> = [
  {
    title: <span className='text-sm font-semibold text-[#475467]'>Trường nguồn</span>,
    dataIndex: 'source',
    key: 'source',
    render: (text: string) => <span className='text-sm text-[#101828]'>{text}</span>
  },
  {
    title: <span className='text-sm font-semibold text-[#475467]'>Hành động</span>,
    dataIndex: 'action',
    key: 'action',
    render: (text: string) => <span className='text-sm text-[#101828]'>{text}</span>
  },
  {
    title: <span className='text-sm font-semibold text-[#475467]'>Giá trị</span>,
    dataIndex: 'value',
    key: 'value',
    render: (text: string) => <span className='text-sm text-[#101828]'>{text}</span>
  }
]
// endregion

// region dataAction
const dataAction = [
  { key: 1, source: 'Trạng thái', action: 'Bằng', value: 'Đã giao hàng' },
  { key: 2, source: 'Trạng thái', action: 'Bằng', value: 'Chưa giao hàng' },
  { key: 3, source: 'Trạng thái', action: 'Bằng', value: 'Chờ giao' },
  { key: 4, source: 'Trạng thái', action: 'Bằng', value: 'Đã giao hàng' }
]
// endregion

// region component
export const DataMappingModal = (props: {
  open: boolean
  onCancel: () => void
  type: MappingDataType
  triggerData?: any
}) => {
  const { open, onCancel, type, triggerData } = props

  // Tạo dữ liệu động từ triggerData
  const generateRequestData = () => {
    // Xử lý dữ liệu cho API
    if (type === 'api' && triggerData?.apiCall?.inputMapping) {
      return triggerData.apiCall.inputMapping.map((item: any, index: number) => ({
        key: index.toString(),
        field: item.dest, // Trường đối tác
        displayName: item.displayName, // Tên hiển thị của trường đối tác
        dataType: item.type || 'String', // Kiểu dữ liệu
        status: item.isRequired ? 'Bắt buộc' : 'Không bắt buộc', // Trạng thái
        param: item.source // Tham số đầu vào
      }))
    }

    // Xử lý dữ liệu cho Webhook
    if (type === 'webhook' && triggerData?.webhook?.inputMapping) {
      return triggerData.webhook.inputMapping.map((item: any, index: number) => ({
        key: index.toString(),
        field: item.dest, // Trường đối tác
        displayName: item.displayName, // Tên hiển thị của trường đối tác
        dataType: item.type || '', // Kiểu dữ liệu
        status: item.isRequired ? 'Bắt buộc' : 'Không bắt buộc', // Trạng thái
        param: item.source // Tham số đầu vào
      }))
    }

    return []
  }

  const generateResponseData = () => {
    // Xử lý dữ liệu cho API
    if (type === 'api' && triggerData?.apiCall?.outputMapping) {
      return triggerData.apiCall.outputMapping.map((item: any, index: number) => ({
        key: index.toString(),
        source: item.source, // Trường nguồn
        targetField: item.dest, // Trường đối tác
        value: item.value || '-' // Giá trị
      }))
    }

    // Xử lý dữ liệu cho Webhook
    if (type === 'webhook' && triggerData?.webhook?.outputMapping) {
      return triggerData.webhook.outputMapping.map((item: any, index: number) => ({
        key: index.toString(),
        source: item.source, // Trường nguồn
        targetField: item.dest, // Trường đối tác
        value: item.value || '-' // Giá trị
      }))
    }

    return []
  }

  // Dữ liệu động cho các tab
  const requestData = generateRequestData()
  const responseData = generateResponseData()

  const items: TabsProps['items'] = [
    {
      key: '1',
      label: <div className='text-center font-medium text-blue-600'>Request</div>,
      children: (
        <div>
          <Table columns={columnsTab1} dataSource={requestData.length > 0 ? requestData : []} pagination={false} />
        </div>
      )
    },
    {
      key: '2',
      label: <div className='text-center font-medium text-black'>Response</div>,
      children: (
        <div>
          <Table columns={columnsTab2} dataSource={responseData.length > 0 ? responseData : []} pagination={false} />
        </div>
      )
    }
  ]

  // Tạo dữ liệu động cho action
  const generateActionData = () => {
    if (type === 'action' && triggerData?.action?.conditions) {
      return triggerData.action.conditions.map((item: any, index: number) => ({
        key: index.toString(),
        source: item.source,
        action: item.operator,
        value: item.value
      }))
    }

    return []
  }

  const actionData = generateActionData()

  // Render content based on type
  const renderContent = () => {
    // Nếu type là action, chỉ hiển thị bảng giá trị
    if (type === 'action') {
      return (
        <div>
          <Table
            columns={columnsAction}
            dataSource={actionData.length > 0 ? actionData : dataAction}
            pagination={false}
          />
        </div>
      )
    }

    // Nếu type là webhook, chỉ hiển thị tab Request
    if (type === 'webhook') {
      // Only show Request tab
      const webhookItems = [items[0]]

      return <Tabs centered className='sm:w-full' defaultActiveKey='1' items={webhookItems} />
    }

    // Còn lại type là api, hiển thị cả 2 tabs Request và Response
    return <CustomTab className='sm:w-full' defaultActiveKey='1' items={items} />
  }

  // region return
  return (
    <Modal
      title='Ánh xạ dữ liệu'
      open={open}
      onCancel={onCancel}
      footer={
        <div className='text-right'>
          <Button type='default' onClick={onCancel}>
            Đóng
          </Button>
        </div>
      }
      width={960}
    >
      {renderContent()}
    </Modal>
  )
}
// endregion

// region styled
export const CustomTab = styled(Tabs)`
  .ant-tabs-nav-list {
    inline-size: 100%;

    .ant-tabs-tab {
      inline-size: ${100 / 2}%;
    }

    .ant-tabs-ink-bar {
      inline-size: ${100 / 2}%;
    }
  }

  .ant-tabs-tab-btn {
    margin-block: 0;
    margin-inline: auto;
    font-weight: 500;
  }

  .ant-tabs-nav .ant-tabs-nav-operations {
    display: none !important;
  }

  .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
    font-weight: 500;
  }
`
// endregion
