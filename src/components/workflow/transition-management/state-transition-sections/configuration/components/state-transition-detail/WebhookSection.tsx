import React from 'react'

import { Button } from 'antd'

import { ValueDetail } from '@/components/workflow/shared/value-detail/ValueDetail'
import { getAuthTypeDisplay } from '@/utils/workflow'

interface WebhookSectionProps {
  webhookData: any
  onOpenDataMapping: () => void
  onOpenUrlVariables: () => void
}

export const WebhookSection: React.FC<WebhookSectionProps> = ({
  webhookData,
  onOpenDataMapping,
  onOpenUrlVariables
}) => {
  return (
    <>
      <div className='grid grid-cols-2'>
        <div className='text-sm font-normal leading-5 tracking-tight-05 text-gray-8'>Hành động</div>
        <div className='text-sm font-medium leading-5 tracking-tight-05 text-gray-11'>
          Gửi Webhook đến hệ thống khác
        </div>
      </div>
      <div className='rounded-lg border border-solid border-border-neutral-medium bg-white p-3'>
        <div className='grid grid-cols-4 gap-x-4 gap-y-5 py-3'>
          <ValueDetail label='Phương thức kết nối' value={webhookData?.method} />
          <ValueDetail label='URL Endpoint API' value={webhookData?.url?.raw?.trim()} />
          {webhookData?.method === 'GET' && (
            <ValueDetail
              label='Params/ Path variable'
              value={
                webhookData?.url?.variables?.length > 0 && (
                  <div
                    className='cursor-pointer text-text-primary-default underline'
                    onClick={e => {
                      e.preventDefault()
                      onOpenUrlVariables()
                    }}
                  >
                    Xem chi tiết
                  </div>
                )
              }
            />
          )}
          <ValueDetail
            label='Phương thức xác thực API'
            value={webhookData?.auth?.type ? getAuthTypeDisplay(webhookData.auth.type) : null}
          />
        </div>
        <div className='flex items-center justify-between rounded-lg border border-solid border-border-neutral-medium p-3'>
          <div className='body-14-regular'>
            <i className='onedx-dot mr-1.5 size-2 text-icon-success-default' /> Ánh xạ dữ liệu
          </div>
          <div className='text-right'>
            <Button
              onClick={onOpenDataMapping}
              type='default'
              className='border-text-primary-default text-text-primary-default'
            >
              Xem chi tiết
            </Button>
          </div>
        </div>
      </div>
    </>
  )
}
