import React from 'react'

import { ValueDetail } from '@/components/workflow/shared/value-detail/ValueDetail'

interface NotificationSectionProps {
  getNotificationMethods: () => string | undefined
  emailNotification: any
  appNotification: any
  onOpenEmailDetail: () => void
}

export const NotificationSection: React.FC<NotificationSectionProps> = ({
  getNotificationMethods,
  emailNotification,
  appNotification,
  onOpenEmailDetail
}) => {
  return (
    <>
      <div className='grid grid-cols-2'>
        <div className='text-sm font-normal leading-5 tracking-tight-05 text-gray-8'>Hành động</div>
        <div className='text-sm font-medium leading-5 tracking-tight-05 text-gray-11'>G<PERSON>i thông báo</div>
      </div>
      <div className='rounded-lg border border-solid border-border-neutral-medium bg-white p-3'>
        <div className='grid grid-cols-4 gap-x-4 gap-y-5 py-3'>
          <ValueDetail label='Phương thức thông báo' value={getNotificationMethods() || 'Chưa cấu hình'} />
          <ValueDetail
            label='Email'
            value={
              emailNotification ? (
                <div
                  className='cursor-pointer text-text-primary-default underline hover:text-text-primary-default-hover'
                  onClick={onOpenEmailDetail}
                >
                  Xem chi tiết
                </div>
              ) : (
                <span className='text-text-neutral-default'>Chưa cấu hình</span>
              )
            }
          />
          <ValueDetail label='Notification' value={appNotification?.customTitle || 'Chưa cấu hình'} />
          <ValueDetail label='Nội dung' value={appNotification?.customContent || 'Chưa cấu hình'} />
        </div>
      </div>
    </>
  )
}
