'use client'

import React, { useState } from 'react'

import { Button, Empty, Form, Modal, Select, message } from 'antd'

import { stateTransitionConfig } from '@/models/workflow/stateTransitionConfig'
import type { TransitionStateTableItem } from '@/types/workflow/transition'

interface PreviousStateModalProps {
  open: boolean
  editingItem: TransitionStateTableItem | null
  currentPreStateId?: string | number | null
  onClose: () => void
  onConfirm: (values: { previousState: { id: string | number; name: string } }) => void
}

export const PreviousStateModal: React.FC<PreviousStateModalProps> = ({
  open,
  editingItem,
  currentPreStateId,
  onClose,
  onConfirm
}) => {
  const [form] = Form.useForm()
  const [previousStateOptions, setPreviousStateOptions] = useState<any[]>([])
  const [loading, setLoading] = useState(false)

  // Load dữ liệu khi modal mở
  React.useEffect(() => {
    if (open && editingItem) {
      loadPreviousStates()
      mapDataToForm()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open, editingItem])

  const loadPreviousStates = async () => {
    if (!editingItem) return

    setLoading(true)

    try {
      const response = await stateTransitionConfig.getComboboxPreviousState({
        page: 0,
        size: 100,
        search: '',
        idsIgnore: [String(editingItem.stateId)] // Loại bỏ trạng thái hiện tại
      })

      setPreviousStateOptions(response?.content || [])
    } catch (error) {
      console.error('Error loading previous states:', error)
      message.error('Không thể tải danh sách trạng thái tiền nhiệm')
    } finally {
      setLoading(false)
    }
  }

  const mapDataToForm = () => {
    if (!editingItem) return

    form.setFieldsValue({
      previousState: currentPreStateId ?? undefined
    })
  }

  const handleConfirm = async () => {
    try {
      const values = await form.validateFields()
      const selectedId: string | number | undefined = values?.previousState

      const idToName = new Map(previousStateOptions.map((opt: any) => [opt.id, opt.name]))
      const name = selectedId !== undefined ? idToName.get(selectedId) || String(selectedId) : ''

      onConfirm({ previousState: { id: selectedId as any, name } })
    } catch (error) {
      console.error('Validation failed:', error)
    }
  }

  const handleClose = () => {
    form.resetFields()
    onClose()
  }

  return (
    <Modal
      title='Chỉnh sửa nhanh "Trạng thái tiền nhiệm"'
      open={open}
      onCancel={handleClose}
      footer={[
        <Button key='cancel' onClick={handleClose}>
          Huỷ
        </Button>,
        <Button key='confirm' type='primary' onClick={handleConfirm}>
          Xác nhận
        </Button>
      ]}
      width={600}
      destroyOnClose
    >
      <Form form={form} layout='vertical'>
        <div className='mb-5 rounded bg-gray-alpha-1'>
          <div className='p-4'>
            <Form.Item
              name='previousState'
              label='Trạng thái tiền nhiệm được phép chuyển đến'
              tooltip='Chọn đúng trạng thái tiền nhiệm cho dòng đang chỉnh sửa.'
              rules={[{ required: true, message: 'Vui lòng chọn trạng thái tiền nhiệm' }]}
            >
              <Select
                placeholder='Chọn trạng thái tiền nhiệm'
                options={previousStateOptions.map((item: any) => ({ value: item.id, label: item.name }))}
                loading={loading}
                className='w-full'
                notFoundContent={<Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description='Không có dữ liệu' />}
              />
            </Form.Item>
          </div>
        </div>
      </Form>
    </Modal>
  )
}
