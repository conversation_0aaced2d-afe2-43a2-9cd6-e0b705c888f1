'use client'

import React, { useMemo, useState } from 'react'

import { Button, Checkbox, Modal, Pagination, Select, Table, Tooltip, message, Form } from 'antd'
import type { ColumnsType } from 'antd/es/table'

import ModalActionDelete from '@/components/workflow/shared/popover/ModalActionDelete'
import type { TransitionStateTableItem } from '@/types/workflow/transition'
import { formatSelectedCount } from '@/utils/workflow'
// import { useRenderConditionByTrigger } from '@/hooks/workflow'
import { TRIGGER_TYPE_MAP } from '@/constants/workflow'
// constants used inside utils now
import { renderConditionByTriggerCreate } from '@/utils/workflow/transition'
// import type { ITrigger } from '@/types/workflow/stateTransition'
import { GeneralSection } from '@/components/workflow/transition-management/table-config-sections/table/component/GeneralSection'
import { PreviousStateModal } from './PreviousStateModal'
import { TriggerEditModal } from '@/components/workflow/transition-management/table-config-sections/table/component/TriggerEditModal'

interface ConfigurationTableProps {
  data: TransitionStateTableItem[]
  setTableData: (data: TransitionStateTableItem[]) => void
  readOnly?: boolean
  selectedColumns?: string[]
  onAfterDelete?: (deletedKeys: string[]) => void
  setDataConfig?: (updater: (prev: any) => any) => void
}

export const ConfigurationTableCreate: React.FC<ConfigurationTableProps> = ({
  data,
  setTableData,
  selectedColumns,
  onAfterDelete,
  setDataConfig
}) => {
  // region State
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([])
  const [page, setPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [hoveredRowKey, setHoveredRowKey] = useState<string | null>(null)
  const [deleteModalOpen, setDeleteModalOpen] = useState(false)
  const [deleteTarget, setDeleteTarget] = useState<TransitionStateTableItem | null>(null)
  const [deleteMultiple, setDeleteMultiple] = useState(false)
  const [conditionModalOpen, setConditionModalOpen] = useState(false)
  const [selectedConditions] = useState<{ triggerType: string; condition: string }[]>([])
  const [editModalOpen, setEditModalOpen] = useState(false)
  const [editingItem, setEditingItem] = useState<TransitionStateTableItem | null>(null)
  const [editForm] = Form.useForm()
  const [previousStateModalOpen, setPreviousStateModalOpen] = useState(false)
  const [editingPreviousStateItem, setEditingPreviousStateItem] = useState<TransitionStateTableItem | null>(null)
  const [editingPreStateId, setEditingPreStateId] = useState<string | number | null>(null)
  const [triggerEditModalOpen, setTriggerEditModalOpen] = useState(false)
  const [editingTriggerItem, setEditingTriggerItem] = useState<TransitionStateTableItem | null>(null)

  // region Helpers
  // Dữ liệu hiển thị flatted theo từng previous state
  const flattenedData = useMemo(() => {
    const rows: (TransitionStateTableItem & { __viewId: string; originKey: string })[] = []

    data.forEach((item, idx) => {
      const triggerConds: any[] = Array.isArray(item?.triggerConditions) ? (item as any).triggerConditions : []

      if (triggerConds.length > 1) {
        triggerConds.forEach((tc: any, tcIdx: number) => {
          const viewRow: any = {
            ...item,
            previousState: tc?.preStateName || item.previousState,
            triggers: Array.isArray(tc?.triggers) && tc.triggers.length > 0 ? tc.triggers : item.triggers,
            preStateId: tc?.preStateId,
            __viewId: `${item.key}__${tc?.preStateId ?? tcIdx}`,
            originKey: item.key
          }

          rows.push(viewRow)
        })
      } else {
        rows.push({ ...(item as any), __viewId: `${item.key}__base_${idx}`, originKey: item.key })
      }
    })

    return rows.map((r, i) => ({ ...r, index: i + 1 }))
  }, [data])

  // region Event Handlers
  const handleOpenDeleteModal = (item: TransitionStateTableItem) => {
    setDeleteTarget(item)
    setDeleteMultiple(false)
    setDeleteModalOpen(true)
  }

  const handleOpenDeleteMultipleModal = () => {
    setDeleteMultiple(true)
    setDeleteModalOpen(true)
  }

  const handleConfirmDelete = () => {
    const mapViewKeyToOriginKey = (viewKey: string) => {
      const found = flattenedData.find(r => (r as any).__viewId === viewKey)

      return (found as any)?.originKey || viewKey
    }

    if (deleteMultiple) {
      const originKeys = Array.from(new Set(selectedRowKeys.map(mapViewKeyToOriginKey)))

      setTableData(data.filter(item => !originKeys.includes(item.key)))
      message.success(`Đã xóa ${originKeys.length} trạng thái`)
      setSelectedRowKeys([])
      onAfterDelete?.(originKeys)
    } else if (deleteTarget) {
      const originKey = (deleteTarget as any).originKey || (deleteTarget as any).key

      setTableData(data.filter(item => item.key !== originKey))
      message.success('Đã xóa 1 trạng thái')

      const viewKey = (deleteTarget as any).__viewId || originKey

      if (selectedRowKeys.includes(viewKey)) {
        setSelectedRowKeys(selectedRowKeys.filter(k => k !== viewKey))
      }

      onAfterDelete?.([originKey])
    }

    setDeleteModalOpen(false)
    setDeleteTarget(null)
  }

  const handleCloseDeleteModal = () => {
    setDeleteModalOpen(false)
    setDeleteTarget(null)
    setDeleteMultiple(false)
  }

  const handleOpenEditModal = (item: TransitionStateTableItem) => {
    setEditingItem(item)
    // Mapping data từ item sang form
    editForm.setFieldsValue({
      stateId: item.id, // Thêm stateId để xác định đây là trạng thái có sẵn
      stateName: item.name,
      displayName: item.displayName,
      colorCode: item.color,
      icon: item.icon,
      stateCode: item.code,
      stateType: item.type,
      description: item.description,
      isSharedForAllObjects: item.applyAll || false
    })
    setEditModalOpen(true)
  }

  const handleCloseEditModal = () => {
    setEditModalOpen(false)
    setEditingItem(null)
    editForm.resetFields()
  }

  const handleConfirmEdit = async () => {
    try {
      const values = await editForm.validateFields()

      if (editingItem) {
        // Cập nhật data trong table
        const updatedData = data.map(item =>
          item.key === editingItem.key
            ? {
                ...item,
                name: values.stateName,
                displayName: values.displayName,
                color: values.colorCode,
                icon: values.icon,
                code: values.stateCode,
                type: values.stateType,
                description: values.description,
                applyAll: values.isSharedForAllObjects
              }
            : item
        )

        setTableData(updatedData)
        // Đồng bộ trực tiếp vào dataConfigTable (items)
        setDataConfig?.((prev: any) => {
          if (!prev || !Array.isArray(prev.items)) return prev

          const newItems = prev.items.map((it: any) => {
            const key = it?.code || it?.stateCode

            if (key === editingItem.key) {
              return {
                ...it,
                name: values.stateName,
                displayName: values.displayName,
                colorCode: values.colorCode,
                code: values.stateCode,
                typeId: values.stateType,
                description: values.description,
                icon: values.icon,
                applyAll: values.isSharedForAllObjects
              }
            }

            return it
          })

          return { ...prev, items: newItems }
        })
        message.success('Đã cập nhật thông tin trạng thái')
        handleCloseEditModal()
      }
    } catch (error) {
      console.error('Validation failed:', error)
    }
  }

  const handleOpenPreviousStateModal = (item: TransitionStateTableItem) => {
    setEditingPreviousStateItem(item)
    setEditingPreStateId((item as any)?.preStateId ?? null)
    setPreviousStateModalOpen(true)
  }

  const handleClosePreviousStateModal = () => {
    setPreviousStateModalOpen(false)
    setEditingPreviousStateItem(null)
    setEditingPreStateId(null)
  }

  const handleConfirmPreviousStateEdit = (values: { previousState: { id: string | number; name: string } }) => {
    if (editingPreviousStateItem) {
      // Cập nhật đúng triggerConditions phần tử tương ứng theo preStateId cho data hiển thị
      const updatedData = data.map(item => {
        if (item.key !== editingPreviousStateItem.key) {
          return item
        }

        const tcList = Array.isArray((item as any).triggerConditions) ? (item as any).triggerConditions : []
        const indexToUpdate = tcList.findIndex((tc: any) => tc?.preStateId === editingPreStateId)

        if (indexToUpdate >= 0) {
          const newTc = tcList.map((tc: any, idx: number) =>
            idx === indexToUpdate
              ? { ...tc, preStateId: values.previousState.id, preStateName: values.previousState.name }
              : tc
          )

          return { ...(item as any), triggerConditions: newTc }
        }

        // Nếu chưa có, thêm mới một phần tử
        const newTc = [
          ...tcList,
          { preStateId: values.previousState.id, preStateName: values.previousState.name, triggers: [] }
        ]

        return { ...(item as any), triggerConditions: newTc }
      })

      setTableData(updatedData)

      // Đồng bộ trực tiếp vào dataConfigTable.triggerConditions chỉ mục tương ứng
      setDataConfig?.((prev: any) => {
        if (!prev || !Array.isArray(prev.items)) {
          return prev
        }

        const newItems = prev.items.map((it: any) => {
          const key = it?.code || it?.stateCode

          if (key === editingPreviousStateItem.key) {
            const list = Array.isArray(it?.triggerConditions) ? it.triggerConditions : []
            const idx = list.findIndex((tc: any) => tc?.preStateId === editingPreStateId)

            if (idx >= 0) {
              const updatedList = list.map((tc: any, i: number) =>
                i === idx
                  ? {
                      ...tc,
                      preStateId: values.previousState.id,
                      preStateName: values.previousState.name
                    }
                  : tc
              )

              return { ...it, triggerConditions: updatedList }
            }

            const appended = [
              ...list,
              { preStateId: values.previousState.id, preStateName: values.previousState.name, triggers: [] }
            ]

            return { ...it, triggerConditions: appended }
          }

          return it
        })

        return { ...prev, items: newItems }
      })
      message.success('Đã cập nhật trạng thái tiền nhiệm')
      handleClosePreviousStateModal()
    }
  }

  // Hàm mở modal chỉnh sửa trigger
  const handleOpenTriggerEditModal = (item: TransitionStateTableItem) => {
    setEditingTriggerItem(item)
    setTriggerEditModalOpen(true)
  }

  // Hàm đóng modal chỉnh sửa trigger
  const handleCloseTriggerEditModal = () => {
    setTriggerEditModalOpen(false)
    setEditingTriggerItem(null)
  }

  // Hàm xác nhận chỉnh sửa trigger
  const handleConfirmTriggerEdit = (newTriggers: any[]) => {
    if (editingTriggerItem) {
      // Tìm triggerCondition phù hợp dựa vào preStateId
      const preStateId = (editingTriggerItem as any).previousStateId

      // Cập nhật data trong table
      const updatedData = data.map(item => {
        if (item.key !== editingTriggerItem.key) {
          return item
        }

        // Nếu có triggerConditions (cấu trúc mới)
        if (Array.isArray((item as any).triggerConditions)) {
          const tcList = [...(item as any).triggerConditions]

          // Nếu có preStateId, cập nhật triggers trong triggerCondition tương ứng
          if (preStateId) {
            const indexToUpdate = tcList.findIndex((tc: any) => tc?.preStateId === preStateId)

            if (indexToUpdate >= 0) {
              tcList[indexToUpdate] = {
                ...tcList[indexToUpdate],
                triggers: newTriggers
              }

              return { ...item, triggerConditions: tcList }
            }
          }

          // Nếu không tìm thấy preStateId tương ứng, không cập nhật gì
          return item
        }

        // Nếu không có triggerConditions (cấu trúc cũ), cập nhật triggers trực tiếp
        return { ...item, triggers: newTriggers }
      })

      setTableData(updatedData)

      // Đồng bộ trực tiếp vào dataConfigTable
      setDataConfig?.((prev: any) => {
        if (!prev || !Array.isArray(prev.items)) {
          return prev
        }

        const newItems = prev.items.map((it: any) => {
          const key = it?.code || it?.stateCode

          if (key === editingTriggerItem.key) {
            // Nếu có triggerConditions (cấu trúc mới)
            if (Array.isArray(it?.triggerConditions)) {
              const tcList = [...it.triggerConditions]

              // Nếu có preStateId, cập nhật triggers trong triggerCondition tương ứng
              if (preStateId) {
                const indexToUpdate = tcList.findIndex((tc: any) => tc?.preStateId === preStateId)

                if (indexToUpdate >= 0) {
                  tcList[indexToUpdate] = {
                    ...tcList[indexToUpdate],
                    triggers: newTriggers
                  }

                  return { ...it, triggerConditions: tcList }
                }
              }

              // Nếu không tìm thấy preStateId tương ứng, không cập nhật gì
              return it
            }

            // Nếu không có triggerConditions (cấu trúc cũ), cập nhật triggers trực tiếp
            return { ...it, triggers: newTriggers }
          }

          return it
        })

        return { ...prev, items: newItems }
      })

      message.success('Đã cập nhật trigger')
      handleCloseTriggerEditModal()
    }
  }

  // Phân trang dữ liệu đã flatten
  const startIndex = (page - 1) * pageSize
  const paginatedData = flattenedData.slice(startIndex, startIndex + pageSize)
  const allSelected = paginatedData.length > 0 && selectedRowKeys.length === paginatedData.length
  // endregion Data Processing

  // region Columns
  const columns: ColumnsType<any> = [
    {
      title: 'STT',
      dataIndex: 'index',
      key: 'index',
      width: 60
    },
    {
      title: 'Tên trạng thái',
      dataIndex: 'name',
      key: 'name',
      width: 180,
      render: (text: string, record: any) => (
        <div className='flex items-center'>
          <div className='mr-2 size-4 rounded-sm' style={{ backgroundColor: record.color }} />
          <div className='flex items-center gap-2'>
            <span className='cursor-pointer text-icon-primary-blue hover:text-blue-800'>{text}</span>
            <div className='flex size-6 items-center justify-center'>
              {hoveredRowKey === (record as any).__viewId && (
                <i
                  className='onedx-edit hover:text-blue-6 size-6 cursor-pointer text-icon-primary-blue'
                  onClick={() => handleOpenEditModal(record)}
                />
              )}
            </div>
          </div>
        </div>
      )
    },
    {
      title: 'Tên hiển thị',
      dataIndex: 'displayName',
      key: 'displayName',
      width: 130
    },
    {
      title: 'Mã trạng thái',
      dataIndex: 'code',
      key: 'code',
      width: 150
    },
    {
      title: 'Loại trạng thái',
      dataIndex: 'type',
      key: 'type',
      width: 137
    },
    {
      title: 'Trạng thái tiền nhiệm',
      dataIndex: 'previousState',
      key: 'previousState',
      width: 150,
      render: (text: string, record: TransitionStateTableItem) => {
        const isHovered = hoveredRowKey === (record as any).__viewId

        return (
          <div className='relative pr-6'>
            <span className='text-icon-primary-blue hover:text-blue-800'>{text || '-'}</span>
            <i
              className={`onedx-edit absolute right-0 top-1/2 size-5 -translate-y-1/2 cursor-pointer text-icon-primary-blue transition-opacity ${
                isHovered ? 'opacity-100' : 'pointer-events-none opacity-0'
              }`}
              onClick={() => handleOpenPreviousStateModal(record)}
            />
          </div>
        )
      }
    },
    {
      title: 'Trigger',
      dataIndex: 'triggers',
      key: 'triggers',
      width: 124,
      render: (_, record: TransitionStateTableItem) => {
        // Kiểm tra nếu triggers là mảng string (format cũ) hoặc mảng object (format mới)
        const triggerNames = Array.isArray(record?.triggers)
          ? record.triggers
              .map((trigger: any) => {
                // Nếu trigger là string (format cũ)
                if (typeof trigger === 'string') {
                  return trigger
                }

                // Nếu trigger là object (format mới từ API)
                if (trigger && typeof trigger === 'object' && trigger.type) {
                  return TRIGGER_TYPE_MAP[trigger.type as string] || trigger.type
                }

                return null
              })
              .filter((name: any) => name) // Loại bỏ các giá trị undefined
              .join(', ')
          : ''

        const isHovered = hoveredRowKey === (record as any).__viewId

        return (
          <div className='relative pr-6'>
            <span className='text-icon-primary-blue hover:text-blue-800'>
              {(record?.triggers?.length as number) > 2 ? (
                <Tooltip title={triggerNames}>
                  <div className='line-clamp-1'>{triggerNames}</div>
                </Tooltip>
              ) : (
                triggerNames || '-'
              )}
            </span>
            <i
              className={`onedx-edit absolute right-0 top-1/2 size-5 -translate-y-1/2 cursor-pointer text-icon-primary-blue transition-opacity ${
                isHovered ? 'opacity-100' : 'pointer-events-none opacity-0'
              }`}
              onClick={() => handleOpenTriggerEditModal(record)}
            />
          </div>
        )
      }
    },
    {
      title: 'Điều kiện chuyển trạng thái',
      dataIndex: 'condition',
      key: 'condition',
      width: 360,
      render: (_, record: TransitionStateTableItem) => {
        // Luôn ưu tiên render theo triggers (V2). Chỉ fallback về record.condition khi không build được.
        if (!record?.triggers || record.triggers.length === 0) {
          return record.condition?.trim() || ''
        }

        // Hiển thị theo từng trigger như yêu cầu (dùng V2)
        const items = record.triggers
          .filter((t: any) => t && typeof t === 'object' && t.type)
          .map((trigger: any, idx: number) => {
            const label = TRIGGER_TYPE_MAP[trigger?.type as string] || trigger?.type || ''
            const text = renderConditionByTriggerCreate(trigger)

            return (
              <div key={idx} className='mb-1 last:mb-0'>
                <span className='font-semibold text-gray-11'>{label}:</span>{' '}
                <span className='text-gray-11'>{text || ''}</span>
              </div>
            )
          })

        // Nếu không có item hợp lệ, fallback về record.condition
        if (!items || items.length === 0) {
          return record.condition?.trim() || ''
        }

        return <div className='space-y-0.5'>{items}</div>
      }
    },
    {
      title: 'Vai trò cập nhật',
      dataIndex: 'updateRole',
      key: 'updateRole',
      width: 120
    },
    {
      title: 'Hành động hệ thống',
      dataIndex: 'systemAction',
      key: 'systemAction',
      width: 150
    },
    {
      title: '',
      key: 'action',
      width: 48,
      render: (_, record) => (
        <div className='flex h-full items-center justify-center'>
          {hoveredRowKey === (record as any).__viewId ? (
            <div
              className='flex cursor-pointer items-center justify-center'
              onClick={() => handleOpenDeleteModal(record)}
            >
              <i className='onedx-delete size-5 text-gray-8 hover:text-red-7' />
            </div>
          ) : (
            <div className='invisible flex items-center justify-center'>
              <i className='onedx-delete size-5' />
            </div>
          )}
        </div>
      )
    }
  ]

  // Lọc các cột dựa trên selectedColumns
  const filteredColumns = selectedColumns ? columns.filter(col => selectedColumns.includes(col.key as string)) : columns

  // region render component
  return (
    <div>
      <Table
        columns={filteredColumns}
        dataSource={paginatedData}
        rowKey='__viewId'
        pagination={false}
        className='overflow-x-auto rounded-md border border-gray-200'
        rowClassName={record => (hoveredRowKey === (record as any).__viewId ? 'bg-gray-1' : 'bg-white')}
        onRow={record => ({
          onMouseEnter: () => setHoveredRowKey((record as any).__viewId),
          onMouseLeave: () => setHoveredRowKey(null),
          className: 'border-b border-gray-alpha-3'
        })}
        rowSelection={{
          selectedRowKeys,
          onChange: selectedKeys => setSelectedRowKeys(selectedKeys as string[]),
          columnWidth: 64,
          selections: false,
          columnTitle: (
            <Checkbox
              checked={allSelected}
              disabled={paginatedData.length === 0}
              onChange={e => {
                if (e.target.checked) {
                  setSelectedRowKeys(paginatedData.map(item => item.key))
                } else {
                  setSelectedRowKeys([])
                }
              }}
            />
          )
        }}
      />

      {/* Footer: phần đã chọn + pagination */}
      <div className='mt-4 flex w-full items-center justify-between'>
        <div className='w-1/3'>
          {selectedRowKeys.length > 0 && (
            <div className='flex items-center gap-4'>
              <span>
                Đã chọn: <b>{formatSelectedCount(selectedRowKeys.length)}</b>
              </span>
              <Button
                icon={<i className='onedx-delete size-4' />}
                className='bg-blue-1 flex h-7 flex-row-reverse items-center rounded-xl border-icon-primary-default px-4 text-icon-primary-default'
                onClick={handleOpenDeleteMultipleModal}
              >
                Xoá
              </Button>
            </div>
          )}
        </div>
        <div className='flex w-1/3 justify-center'>
          <Pagination
            current={page}
            pageSize={pageSize}
            total={data.length}
            showSizeChanger={false}
            onChange={p => setPage(p)}
          />
        </div>
        <div className='flex w-1/3 min-w-[120px] items-center justify-end gap-2'>
          <Select
            value={`${pageSize} / Trang`}
            onChange={val => {
              setPageSize(Number(val))
              setPage(1)
            }}
            options={[
              { value: 10, label: '10 / Trang' },
              { value: 20, label: '20 / Trang' },
              { value: 50, label: '50 / Trang' }
            ]}
            style={{ width: 120 }}
          />
        </div>
      </div>

      {/* Modal xác nhận xóa */}
      <ModalActionDelete
        open={deleteModalOpen}
        title={
          <div className='mb-4 flex w-full items-center'>
            <div className='mr-4 flex size-12 items-center justify-center rounded-full bg-red-50'>
              <i className='onedx-delete size-6 text-icon-error-strong' />
            </div>
            <span className='text-lg font-semibold text-gray-11'>
              {deleteMultiple ? 'Xóa nhiều trạng thái' : 'Xóa trạng thái'}
            </span>
          </div>
        }
        description={
          <span className='body-14-regular text-black'>
            {deleteMultiple
              ? `Bạn có chắc chắn muốn xóa ${selectedRowKeys.length} trạng thái đã chọn?`
              : 'Bạn có chắc chắn muốn xóa trạng thái này?'}
          </span>
        }
        onCancel={handleCloseDeleteModal}
        onClose={handleCloseDeleteModal}
        onConfirm={handleConfirmDelete}
      />

      {/* Modal hiển thị điều kiện chuyển trạng thái */}
      <Modal
        title='Điều kiện chuyển trạng thái'
        open={conditionModalOpen}
        onCancel={() => setConditionModalOpen(false)}
        footer={[
          <Button key='close' type='default' onClick={() => setConditionModalOpen(false)}>
            Đóng
          </Button>
        ]}
        width={600}
      >
        <div className='space-y-2'>
          {selectedConditions.map((item, index) => (
            <div key={index} className='p-3'>
              <div className='mb-2 font-bold text-black'>{item.triggerType}</div>
              <div className='text-gray-700'>{item.condition}</div>
            </div>
          ))}
        </div>
      </Modal>

      {/* Modal chỉnh sửa trạng thái */}
      <Modal
        title='Chỉnh sửa nhanh "Trạng thái"'
        open={editModalOpen}
        onCancel={handleCloseEditModal}
        footer={[
          <Button key='cancel' onClick={handleCloseEditModal}>
            Huỷ
          </Button>,
          <Button key='confirm' type='primary' onClick={handleConfirmEdit}>
            Xác nhận
          </Button>
        ]}
        width={800}
        destroyOnClose
      >
        <Form form={editForm} layout='vertical'>
          <GeneralSection
            collapsed={false}
            onToggle={() => {}}
            isNewState={false}
            handleStateNameChange={() => {}}
            messageApi={message}
            tableStates={data}
            isEditMode={true}
            disableNameValidation={true}
          />
        </Form>
      </Modal>

      {/* Modal chỉnh sửa trạng thái tiền nhiệm */}
      <PreviousStateModal
        open={previousStateModalOpen}
        editingItem={editingPreviousStateItem}
        currentPreStateId={editingPreStateId as any}
        onClose={handleClosePreviousStateModal}
        onConfirm={handleConfirmPreviousStateEdit}
      />

      {/* Modal chỉnh sửa trigger */}
      <TriggerEditModal
        open={triggerEditModalOpen}
        onClose={handleCloseTriggerEditModal}
        onConfirm={handleConfirmTriggerEdit}
        triggerData={
          Array.isArray(editingTriggerItem?.triggerConditions)
            ? editingTriggerItem?.triggerConditions.find(
                (tc: any) => tc?.preStateId === (editingTriggerItem as any)?.previousStateId
              )?.triggers ||
              editingTriggerItem?.triggers ||
              []
            : editingTriggerItem?.triggers || []
        }
        preStateId={(editingTriggerItem as any)?.preStateId}
      />
    </div>
  )
  // endregion Render
}
