'use client'

import { useMemo, useState } from 'react'

import { DownOutlined, UpOutlined } from '@ant-design/icons'
import type { TabsProps } from 'antd'
import type { Node, Edge } from '@xyflow/react'

import type { ConfigurationProps, TransitionStateTableItem } from '@/types/workflow/transition'
import type { CheckedState } from '@/components/filter/SettingInput'
import { WorkflowTabs } from '../../../../../shared/tabs/WorkflowTabs'
import { TableConfigDrawer } from '../../../../table-config-sections/table/TableConfigDrawer'
import { StateGeneralInfoDrawer, StateDetailDrawer } from '../..'
import { SearchFilterBarCreate } from './SearchFilterBarCreate'
import { TransitionDiagram } from '../../../../table-config-sections/diagram/TransitionDiagram'
import { mapApiToDataTable } from '@/utils/workflow/transition'
import { ConfigurationTableCreate } from './ConfigurationTableCreate'
import { StyledContent } from '@/components/workflow/shared/table'

export const ConfigurationSectionCreate: React.FC<ConfigurationProps> = ({
  configCollapsed,
  setConfigCollapsed,
  setConfigType,
  stateTableData = [],
  readOnly = false,
  showTabs = false,
  setDataConfig,
  onDeleteItems
}) => {
  const [drawerOpen, setDrawerOpen] = useState(false)
  const [hasConfigurations, setHasConfigurations] = useState(readOnly || stateTableData.length > 0)
  const [activeTab, setActiveTab] = useState('list')

  // State cho search filters
  const [searchFilters, setSearchFilters] = useState({
    searchValue: '',
    checkedFilter: {} as CheckedState,
    lstTypeId: [] as number[],
    lstTriggerRole: [] as string[],
    lstTriggerType: [] as string[],
    postActionTypes: [] as string[],
    startTime: '',
    endTime: ''
  })

  // Function để update search filter
  const updateSearchFilter = (key: string, value: any) => {
    setSearchFilters(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const [tableData, setTableData] = useState<TransitionStateTableItem[]>(
    stateTableData.length > 0 ? (stateTableData as unknown as TransitionStateTableItem[]) : []
  )

  // Xử lý lọc dữ liệu chay trong bảng - không call api
  const filteredTableData = useMemo(() => {
    const {
      searchValue,
      checkedFilter,
      lstTypeId,
      lstTriggerRole,
      lstTriggerType,
      postActionTypes,
      startTime,
      endTime,
      isStateApplyAll
    } = searchFilters as any

    return tableData.filter((item: TransitionStateTableItem) => {
      // Search by code/name
      if (searchValue && (checkedFilter?.isCode || checkedFilter?.isName)) {
        const sv = searchValue.toLowerCase()
        const byCode = checkedFilter?.isCode ? (item.code || '').toLowerCase().includes(sv) : false
        const byName = checkedFilter?.isName ? (item.name || '').toLowerCase().includes(sv) : false

        if (!byCode && !byName) {
          return false
        }
      }

      // Filter by typeId (if available)
      if (Array.isArray(lstTypeId) && lstTypeId.length > 0) {
        const typeId = (item as any).typeId

        if (!lstTypeId.includes(typeId as any)) {
          return false
        }
      }

      // Filter by trigger role (Manual trigger roles)
      if (Array.isArray(lstTriggerRole) && lstTriggerRole.length > 0) {
        const hasRole = Array.isArray(item.triggers)
          ? item.triggers.some((t: any) => {
              if (!t || typeof t !== 'object') return false
              if (t.type !== 'MANUAL') return false
              const roles: string[] = (t.manual?.roles || []).map((r: any) => (r + '').toUpperCase())

              return roles.some(r => lstTriggerRole.includes(r))
            })
          : false

        if (!hasRole) {
          return false
        }
      }

      // Filter by trigger types
      if (Array.isArray(lstTriggerType) && lstTriggerType.length > 0) {
        const hasType = Array.isArray(item.triggers)
          ? item.triggers.some((t: any) => lstTriggerType.includes(t?.type))
          : false

        if (!hasType) {
          return false
        }
      }

      // Filter by post action types
      if (Array.isArray(postActionTypes) && postActionTypes.length > 0) {
        const actions: any[] = Array.isArray(item.postActions) ? item.postActions : []
        const hasAction = actions.some(a => a?.type && postActionTypes.includes(a.type))

        if (!hasAction) {
          return false
        }
      }

      // Filter by apply all (dùng chung)
      if (isStateApplyAll === 'YES' && item.applyAll !== true) return false
      if (isStateApplyAll === 'NO' && item.applyAll === true) return false

      // Time range placeholder: nếu item có createdAt/modifiedAt thì có thể so sánh (định dạng DD/MM/YYYY)
      if (startTime && endTime && (item as any).modifiedAt) {
        const toDate = (d: string) => {
          const [dd, mm, yyyy] = d.split('/').map((x: string) => Number(x))

          return new Date(yyyy, mm - 1, dd).getTime()
        }

        const fromTs = toDate(startTime)
        const toTs = toDate(endTime)
        const itemTs = toDate(((item as any).modifiedAt as string) || '')

        if (!(itemTs >= fromTs && itemTs <= toTs)) {
          return false
        }
      }

      return true
    })
  }, [tableData, searchFilters])

  const [stateDetailDrawerOpen, setStateDetailDrawerOpen] = useState(false)
  const [transitionDetailDrawerOpen, setTransitionDetailDrawerOpen] = useState(false)
  const [selectedNodeData, setSelectedNodeData] = useState<TransitionStateTableItem | null>(null)
  const [selectedEdgeData, setSelectedEdgeData] = useState<any>(null)

  const renumberIndexes = (items: TransitionStateTableItem[]) => items.map((it, idx) => ({ ...it, index: idx + 1 }))

  // State để quản lý các cột được chọn hiển thị
  const [selectedColumns, setSelectedColumns] = useState<string[]>([
    'index',
    'name',
    'displayName',
    'type',
    'previousState',
    'previousStateId',
    'triggers'
    // 'condition'
  ])

  const handleOpenTableDrawer = () => {
    setDrawerOpen(true)

    setConfigType?.('table')
  }

  const handleCloseDrawer = (shouldRefresh = false, newRows?: TransitionStateTableItem[], rawMappedData?: any) => {
    setDrawerOpen(false)

    if (setDataConfig) {
      setDataConfig((prev: any) => {
        // Nếu chưa có dữ liệu cũ thì trả về dữ liệu mới luôn
        if (!prev || !Array.isArray(prev?.items)) return rawMappedData

        const prevItems = Array.isArray(prev.items) ? prev.items : []
        const newItems = Array.isArray(rawMappedData?.items) ? rawMappedData.items : []

        // Merge theo khoá ưu tiên: code -> stateCode -> stateId
        // Ưu tiên dữ liệu mới nếu trùng khoá
        const seen = new Set<string>()

        const mergedItems = [...prevItems, ...newItems].reduce((acc: any[], it: any) => {
          const key = (it?.code ?? it?.stateCode ?? it?.stateId ?? Math.random().toString()) + ''

          if (!seen.has(key)) {
            seen.add(key)
            acc.push(it)
          }

          return acc
        }, [])

        return {
          ...prev,
          ...rawMappedData,
          items: mergedItems
        }
      })
    }

    if (shouldRefresh) {
      setHasConfigurations(true)

      // Ưu tiên dùng newRows; nếu không có thì format từ rawMappedData
      const incomingRows: TransitionStateTableItem[] =
        Array.isArray(newRows) && newRows.length > 0 ? newRows : mapApiToDataTable(rawMappedData, {})

      if (incomingRows && incomingRows.length > 0) {
        const updated = renumberIndexes([...tableData, ...incomingRows])

        setTableData(updated)
      }
    }
  }

  const handleCreateDiagramConfig = () => {
    setConfigType?.('diagram')
  }

  const handleNodeClick = (node: Node) => {
    // Tạo dữ liệu state từ node data theo cấu trúc TransitionStateTableItem
    const selectedNode: TransitionStateTableItem = {
      key: node.id,
      index: 1,
      name: (node.data?.label as string) || node.id,
      displayName: (node.data?.label as string) || node.id,
      code: node.data?.stateCode as string,
      type: 'workflow_state',
      previousState: '',
      previousStateId: '',
      preStateId: '',
      triggers: [],
      condition: '',
      color: (node.data?.color as string) || '#2A6AEB',
      description: node.data?.description as string,
      objectType: node.data?.objectType as string
    }

    setSelectedNodeData(selectedNode)
    setStateDetailDrawerOpen(true)
  }

  const handleEdgeClick = (edge: Edge) => {
    // Tạo dữ liệu state từ edge data theo cấu trúc TransitionStateTableItem
    setSelectedEdgeData(edge)
    setTransitionDetailDrawerOpen(true)
  }

  const handleCloseStateDetailDrawer = () => {
    setStateDetailDrawerOpen(false)
    setSelectedNodeData(null)
  }

  const handleCloseTransitionDetailDrawer = () => {
    setTransitionDetailDrawerOpen(false)
    setSelectedNodeData(null)
  }

  const tabItems: TabsProps['items'] = [
    {
      key: 'list',
      label: <span className='text-sm'>Danh sách</span>
    },
    {
      key: 'diagram',
      label: <span className='text-sm'>Sơ đồ</span>
    }
  ]

  return (
    <div className='overflow-hidden rounded-md bg-white' style={{ margin: 0 }}>
      {/* Header */}
      <div className='mb-3 border-b border-solid border-gray-alpha-13 pb-3'>
        <div className='flex items-center justify-between px-4 py-3 pb-0'>
          <div className='flex items-center'>
            <div className='mr-3 h-4 w-1 bg-yellow-400'></div>
            <h2 className='text-base font-medium'>Cấu hình trạng thái</h2>
          </div>

          <div className='flex items-center'>
            {(hasConfigurations || showTabs) && (
              <WorkflowTabs activeKey={activeTab} onChange={setActiveTab} items={tabItems} />
            )}

            <button
              onClick={() => setConfigCollapsed(!configCollapsed)}
              className='flex size-8 max-h-[32px] min-h-[32px] min-w-[32px] max-w-[32px] items-center justify-center rounded-lg bg-white hover:bg-gray-100'
              style={{ width: '32px', height: '32px', padding: 0 }}
            >
              {configCollapsed ? <DownOutlined className='text-gray-8' /> : <UpOutlined className='text-gray-8' />}
            </button>
          </div>
        </div>
      </div>

      {!configCollapsed && (
        <div className='p-5'>
          {!hasConfigurations ? (
            // Hiển thị các nút tạo cấu hình khi chưa có dữ liệu
            <div className='flex justify-center gap-4'>
              <button
                className='flex h-10 items-center border border-primary-blue px-4 text-primary-blue'
                onClick={handleOpenTableDrawer}
              >
                <i className='onedx-table mr-2 flex size-4 items-center' />
                Tạo cấu hình dạng bảng
              </button>
              <button
                className='flex h-10 items-center border border-primary-blue px-4 text-primary-blue'
                onClick={handleCreateDiagramConfig}
              >
                <i className='onedx-map-icon mr-2 flex size-4 items-center' />
                Tạo cấu hình dạng sơ đồ
              </button>
            </div>
          ) : (
            // Hiển thị nội dung dựa trên tab đang active
            <div>
              {activeTab === 'list' && (
                <>
                  <StyledContent>
                    <SearchFilterBarCreate
                      readOnly={readOnly}
                      selectedColumns={selectedColumns}
                      setSelectedColumns={setSelectedColumns}
                      handleAddState={handleOpenTableDrawer}
                      searchFilters={searchFilters}
                      updateSearchFilter={updateSearchFilter}
                    />
                    <ConfigurationTableCreate
                      data={filteredTableData}
                      setTableData={setTableData}
                      readOnly={readOnly}
                      selectedColumns={selectedColumns}
                      onAfterDelete={(deletedKeys: string[]) => onDeleteItems?.(deletedKeys)}
                      setDataConfig={setDataConfig}
                    />
                  </StyledContent>
                </>
              )}

              {activeTab === 'diagram' && (
                <div className='w-full rounded-lg border border-gray-3'>
                  <TransitionDiagram
                    stateTableData={stateTableData}
                    isCollapsed={configCollapsed}
                    onCreateState={handleOpenTableDrawer}
                    onCreateTransition={handleOpenTableDrawer}
                    onNodeClick={handleNodeClick}
                    onEdgeClick={handleEdgeClick}
                  />
                </div>
              )}
            </div>
          )}
        </div>
      )}

      {/* Drawer tạo cấu hình dạng bảng */}
      {!readOnly && (
        <TableConfigDrawer open={drawerOpen} onClose={handleCloseDrawer} maskClosable={true} tableStates={tableData} />
      )}

      {/* Drawer hiển thị chi tiết state */}
      {stateDetailDrawerOpen && (
        <StateGeneralInfoDrawer
          open={stateDetailDrawerOpen}
          onClose={handleCloseStateDetailDrawer}
          selectedState={selectedNodeData}
        />
      )}

      {/* Drawer hiển thị chi tiết transition */}
      {transitionDetailDrawerOpen && (
        <StateDetailDrawer
          open={transitionDetailDrawerOpen}
          onClose={handleCloseTransitionDetailDrawer}
          selectedState={selectedEdgeData}
          type='transition'
        />
      )}
    </div>
  )
}
