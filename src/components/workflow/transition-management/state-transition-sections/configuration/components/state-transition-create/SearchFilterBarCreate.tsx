'use client'

//#region Imports
import React, { useEffect } from 'react'

import { TableOutlined } from '@ant-design/icons'
import { Button, Form, Select, Space } from 'antd'

import { useQuery } from '@tanstack/react-query'

import TableCheckboxEdit from '@/views/product-catalog/common/TableCheckboxEdit'
import type { CheckedState } from '@/components/filter/SettingInput'
import SettingInput from '@/components/filter/SettingInput'
import { stateTransitionAdmin } from '@/models/workflow/stateTransition'
import { filterOptionTrim } from '@/utils/string'
import SelectFilterPopup from '@/views/order-management/common/SelectFilterPopup'
import {
  FILTER_OPTIONS,
  STATE_SEARCH_CHECKBOX_OPTIONS,
  TABLE_CHECKBOX_OPTIONS,
  TRIGGER_ROLE_OPTIONS
} from '@/constants/workflow'
import type { ISearchFilterBarProps } from '@/types/workflow/stateTransition'
//#endregion

export const SearchFilterBarCreate: React.FC<ISearchFilterBarProps> = ({
  readOnly,
  selectedColumns,
  setSelectedColumns,
  handleAddState,
  searchFilters,
  updateSearchFilter
}) => {
  //#region Hooks & State
  const [form] = Form.useForm()

  const { data: stateTypes, isLoading: loadingStateTypes } = useQuery({
    queryKey: ['state-types-combobox'],
    queryFn: () => stateTransitionAdmin.getStateTypeCombobox({ search: '', page: 0, size: 1000 }),
    select: data =>
      data?.content?.map((item: any) => ({
        label: item.name,
        value: item.id
      })) || []
  })

  const [filterParams, setFilterParams] = React.useState({
    lstTriggerType: searchFilters.lstTriggerType || [],
    postActionTypes: searchFilters.postActionTypes || [],
    timeRange:
      searchFilters.startTime && searchFilters.endTime ? [searchFilters.startTime, searchFilters.endTime] : undefined
  })
  //#endregion

  //#region Effects
  React.useEffect(() => {
    setFilterParams({
      lstTriggerType: searchFilters.lstTriggerType || [],
      postActionTypes: searchFilters.postActionTypes || [],
      timeRange:
        searchFilters.startTime && searchFilters.endTime ? [searchFilters.startTime, searchFilters.endTime] : undefined
    })
  }, [searchFilters.lstTriggerType, searchFilters.postActionTypes, searchFilters.startTime, searchFilters.endTime])

  useEffect(() => {
    form.setFieldsValue({
      searchInput: searchFilters.searchValue,
      lstTypeId: searchFilters.lstTypeId,
      lstTriggerRole: searchFilters.lstTriggerRole,
      lstTriggerType: searchFilters.lstTriggerType || [],
      postActionTypes: searchFilters.postActionTypes || [],
      startTime: searchFilters.startTime,
      endTime: searchFilters.endTime
    })
  }, [searchFilters, form])
  //#endregion

  //#region Event Handlers
  const handleFilterParamsChange = (newParams: any) => {
    setFilterParams(newParams)

    Object.keys(newParams).forEach(key => {
      if (newParams[key] !== undefined && newParams[key] !== null) {
        updateSearchFilter(key, newParams[key])
      } else {
        const defaultValue = Array.isArray(searchFilters[key as keyof typeof searchFilters]) ? [] : ''

        updateSearchFilter(key, defaultValue)
      }
    })
  }

  const handleCheckedChange = (newCheckedState: CheckedState | ((prev: CheckedState) => CheckedState)) => {
    const newValue =
      typeof newCheckedState === 'function' ? newCheckedState(searchFilters.checkedFilter) : newCheckedState

    updateSearchFilter('checkedFilter', newValue)
  }

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter') {
      event.preventDefault()
      updateSearchFilter('searchValue', event.currentTarget.value)
    }
  }

  const handleValuesChange = (changedValues: any) => {
    Object.keys(changedValues).forEach(key => {
      updateSearchFilter(key, changedValues[key])
    })
  }
  //#endregion

  //#region Render
  return (
    <div className='mb-4 flex items-center gap-2'>
      <SettingInput
        placeholder='Tìm kiếm theo mã trạng thái/tên trạng thái'
        styles={{ width: '100%' }}
        checked={searchFilters.checkedFilter}
        setChecked={handleCheckedChange}
        onKeyDown={handleKeyDown}
        checkBoxOptions={STATE_SEARCH_CHECKBOX_OPTIONS}
        size='small'
      />
      <Form
        form={form}
        layout='inline'
        className='flex-1'
        onValuesChange={handleValuesChange}
        initialValues={{
          searchInput: searchFilters.searchValue,
          lstTypeId: searchFilters.lstTypeId,
          lstTriggerRole: searchFilters.lstTriggerRole,
          lstTriggerType: searchFilters.lstTriggerType || [],
          postActionTypes: searchFilters.postActionTypes || [],
          startTime: searchFilters.startTime,
          endTime: searchFilters.endTime
        }}
      >
        <Space>
          <Form.Item name='lstTypeId'>
            <Select
              placeholder='Loại trạng thái'
              className='w-[250px] [&_.ant-select-selection-item]:!mr-1 [&_.ant-select-selection-overflow-item-suffix]:!ml-1 [&_.ant-select-selection-overflow-item]:!max-w-[150px] [&_.ant-select-selection-overflow]:!flex-nowrap [&_.ant-select-selection-overflow]:!gap-1 [&_.ant-select-selection-overflow]:!overflow-hidden [&_.ant-select-selector]:!h-[32px] [&_.ant-select-selector]:!min-h-[32px]'
              suffixIcon={<i className='onedx-chevron-down size-5' />}
              showSearch
              size='middle'
              mode='multiple'
              allowClear
              loading={loadingStateTypes}
              options={stateTypes}
              filterOption={filterOptionTrim}
              notFoundContent='Không có dữ liệu'
              maxTagCount={1}
            />
          </Form.Item>

          <Form.Item name='lstTriggerRole'>
            <Select
              placeholder='Vai trò cập nhật'
              className='w-56'
              suffixIcon={<i className='onedx-chevron-down size-5' />}
              size='middle'
              allowClear
              mode='multiple'
              options={TRIGGER_ROLE_OPTIONS}
            />
          </Form.Item>

          <SelectFilterPopup
            filterOptions={FILTER_OPTIONS}
            filterParams={filterParams}
            setFilterParams={handleFilterParamsChange}
            customStyle='w-[100px]'
          />
        </Space>
      </Form>

      {!readOnly && (
        <Button type='primary' icon={<TableOutlined />} className='ml-auto' ghost onClick={handleAddState}>
          Thêm cấu hình
        </Button>
      )}

      <TableCheckboxEdit
        selectedColumns={selectedColumns}
        setSelectedColumns={setSelectedColumns}
        isAdmin={true}
        checkboxOptions={TABLE_CHECKBOX_OPTIONS}
        height={'40px'}
      />
    </div>
  )
  //#endregion
}
