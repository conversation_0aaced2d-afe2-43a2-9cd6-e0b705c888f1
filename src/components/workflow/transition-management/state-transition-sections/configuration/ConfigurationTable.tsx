'use client'

import React, { useState } from 'react'

import { Button, Modal, Pagination, Select, Table, Tooltip, message } from 'antd'
import type { ColumnsType } from 'antd/es/table'

import ModalActionDelete from '@/components/workflow/shared/popover/ModalActionDelete'
import type { TransitionStateTableItem } from '@/types/workflow/transition'
import { formatSelectedCount } from '@/utils/workflow'
import { StateDetailDrawer } from './StateDetailDrawer'
import { useRenderConditionByTrigger } from '@/hooks/workflow'
import { TRIGGER_TYPE_MAP } from '@/constants/workflow'
import type { ITrigger } from '@/types/workflow/stateTransition'

interface ConfigurationTableProps {
  data?: TransitionStateTableItem[]
  setTableData: (data: TransitionStateTableItem[]) => void
  readOnly?: boolean
  selectedColumns?: string[]
  onAfterDelete?: (deletedKeys: string[]) => void
}

export const ConfigurationTable: React.FC<ConfigurationTableProps> = ({
  data,
  setTableData,
  selectedColumns,
  onAfterDelete
}) => {
  // region State
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([])
  const [page, setPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [hoveredRowKey, setHoveredRowKey] = useState<string | null>(null)
  const [deleteModalOpen, setDeleteModalOpen] = useState(false)
  const [deleteTarget, setDeleteTarget] = useState<TransitionStateTableItem | null>(null)
  const [deleteMultiple, setDeleteMultiple] = useState(false)
  const [detailDrawerOpen, setDetailDrawerOpen] = useState(false)
  const [selectedState, setSelectedState] = useState<TransitionStateTableItem | null>(null)
  const [conditionModalOpen, setConditionModalOpen] = useState(false)
  const [selectedConditions, setSelectedConditions] = useState<{ triggerType: string; condition: string }[]>([])
  // endregion State

  // region Hooks
  // Hook Render điều kiện theo loại trigger
  const { renderConditionByTrigger } = useRenderConditionByTrigger()
  // endregion Hooks

  // region Event Handlers
  const handleOpenDeleteModal = (item: TransitionStateTableItem) => {
    setDeleteTarget(item)
    setDeleteMultiple(false)
    setDeleteModalOpen(true)
  }

  const handleOpenDeleteMultipleModal = () => {
    setDeleteMultiple(true)
    setDeleteModalOpen(true)
  }

  const handleConfirmDelete = () => {
    if (deleteMultiple) {
      // Xóa nhiều
      setTableData(data?.filter(item => !selectedRowKeys.includes(item.key)) || [])
      message.success(`Đã xóa ${selectedRowKeys.length} trạng thái`)
      setSelectedRowKeys([])
      // Xóa bản ghi gửi xuống api
      onAfterDelete?.(selectedRowKeys)
    } else if (deleteTarget) {
      // Xóa một
      setTableData(data?.filter(item => item.key !== deleteTarget.key) || [])
      message.success('Đã xóa 1 trạng thái')

      if (selectedRowKeys.includes(deleteTarget.key)) {
        setSelectedRowKeys(selectedRowKeys.filter(k => k !== deleteTarget.key))
      }

      // Xóa bản ghi gửi xuống api
      onAfterDelete?.([deleteTarget.key])
    }

    setDeleteModalOpen(false)
    setDeleteTarget(null)
  }

  const handleCloseDeleteModal = () => {
    setDeleteModalOpen(false)
    setDeleteTarget(null)
    setDeleteMultiple(false)
  }

  const handleOpenDetailDrawer = (state: TransitionStateTableItem) => {
    setSelectedState(state)
    setDetailDrawerOpen(true)
  }

  const handleCloseDetailDrawer = () => {
    setDetailDrawerOpen(false)
    setSelectedState(null)
  }
  // endregion Event Handlers

  // region Data Processing
  // Phân trang dữ liệu
  const startIndex = (page - 1) * pageSize
  const paginatedData = data?.slice(startIndex, startIndex + pageSize) || []
  // Số bản ghi đã chọn (dùng khi ở màn Tạo/Chỉnh sửa)
  // const allSelected = paginatedData.length > 0 && selectedRowKeys.length === paginatedData.length
  // endregion Data Processing

  // region Columns
  const columns: ColumnsType<TransitionStateTableItem> = [
    {
      title: 'Tên trạng thái',
      dataIndex: 'name',
      key: 'name',
      width: 180,
      render: (text: string, record: TransitionStateTableItem) => (
        <div className='flex items-center'>
          <div className='mr-2 size-4 rounded-sm' style={{ backgroundColor: record.color }} />
          <span
            className='cursor-pointer text-blue-600 hover:text-blue-800 hover:underline'
            onClick={() => handleOpenDetailDrawer(record)}
          >
            {text}
          </span>
        </div>
      )
    },
    {
      title: 'Tên hiển thị',
      dataIndex: 'displayName',
      key: 'displayName',
      width: 130,
      render: (text: string) => <div className='line-clamp-1'>{text}</div>
    },
    {
      title: 'Mã trạng thái',
      dataIndex: 'code',
      key: 'code',
      width: 150
    },
    {
      title: 'Loại trạng thái',
      dataIndex: 'type',
      key: 'type',
      width: 137
    },
    {
      title: 'Trạng thái tiền nhiệm',
      dataIndex: 'previousState',
      key: 'previousState',
      width: 150
    },
    {
      title: 'Trigger',
      dataIndex: 'triggers',
      key: 'triggers',
      width: 124,
      render: (_, record: TransitionStateTableItem) => {
        const triggerNames = record?.triggers
          ?.map((trigger: ITrigger) => TRIGGER_TYPE_MAP[trigger?.type as string])
          ?.filter((name: any) => name) // Loại bỏ các giá trị undefined
          ?.join(', ')

        return (record?.triggers?.length as number) > 2 ? (
          <Tooltip title={triggerNames}>
            <div className='line-clamp-1'>{triggerNames}</div>
          </Tooltip>
        ) : (
          triggerNames
        )
      }
    },
    {
      title: 'Điều kiện chuyển trạng thái',
      dataIndex: 'condition',
      key: 'condition',
      width: 245,
      render: (_, record: TransitionStateTableItem) => {
        if (!record?.triggers || record.triggers.length === 0) {
          return ''
        }

        const firstTriggerCondition = renderConditionByTrigger(record.triggers[0])

        if (record.triggers.length === 1) {
          return firstTriggerCondition
        }

        const handleShowAllConditions = () => {
          const conditions = record.triggers.map((trigger: ITrigger) => ({
            triggerType: TRIGGER_TYPE_MAP[trigger?.type as string] || trigger?.type || '',
            condition: renderConditionByTrigger(trigger) || 'Chưa có dữ liệu'
          }))

          setSelectedConditions(conditions)
          setConditionModalOpen(true)
        }

        return (
          <div>
            {firstTriggerCondition}{' '}
            {record.triggers.length > 1 && (
              <span
                className='ml-1 cursor-pointer text-blue-600 hover:text-blue-800 '
                onClick={handleShowAllConditions}
              >
                ...Xem thêm
              </span>
            )}
          </div>
        )
      }
    },
    {
      title: 'Vai trò cập nhật',
      dataIndex: 'updateRole',
      key: 'updateRole',
      width: 120
    },
    {
      title: 'Hành động hệ thống',
      dataIndex: 'systemAction',
      key: 'systemAction',
      width: 150
    },
    {
      title: '',
      key: 'action',
      width: 48,
      render: (_, record) => (
        <div className='flex h-full items-center justify-center'>
          {hoveredRowKey === record.key ? (
            <div
              className='flex cursor-pointer items-center justify-center'
              onClick={() => handleOpenDeleteModal(record)}
            >
              <i className='onedx-delete size-5 text-gray-8 hover:text-red-7' />
            </div>
          ) : (
            <div className='invisible flex items-center justify-center'>
              <i className='onedx-delete size-5' />
            </div>
          )}
        </div>
      )
    }
  ]

  // Lọc các cột dựa trên selectedColumns
  const filteredColumns = selectedColumns ? columns.filter(col => selectedColumns.includes(col.key as string)) : columns
  // endregion Columns

  // region render component
  return (
    <div>
      <Table
        columns={filteredColumns}
        dataSource={paginatedData}
        rowKey='key'
        pagination={false}
        className='overflow-x-auto rounded-md border border-gray-200'
        rowClassName={record => (hoveredRowKey === record.key ? 'bg-gray-1' : 'bg-white')}
        onRow={record => ({
          onMouseEnter: () => setHoveredRowKey(record.key),
          onMouseLeave: () => setHoveredRowKey(null),
          className: 'border-b border-gray-alpha-3'
        })}
        // Màn edit/Tạo thì bật comment lên
        // rowSelection={{
        //   selectedRowKeys,
        //   onChange: selectedKeys => setSelectedRowKeys(selectedKeys as string[]),
        //   columnWidth: 64,
        //   selections: false,
        //   columnTitle: (
        //     <Checkbox
        //       checked={allSelected}
        //       disabled={paginatedData.length === 0}
        //       onChange={e => {
        //         if (e.target.checked) {
        //           setSelectedRowKeys(paginatedData.map(item => item.key))
        //         } else {
        //           setSelectedRowKeys([])
        //         }
        //       }}
        //     />
        //   )
        // }}
      />

      {/* Footer: phần đã chọn + pagination */}
      <div className='mt-4 flex w-full items-center justify-between'>
        <div className='w-1/3'>
          {selectedRowKeys.length > 0 && (
            <div className='flex items-center gap-4'>
              <span>
                Đã chọn: <b>{formatSelectedCount(selectedRowKeys.length)}</b>
              </span>
              <Button
                icon={<i className='onedx-delete size-4' />}
                className='bg-blue-1 flex h-7 flex-row-reverse items-center rounded-xl border-icon-primary-default px-4 text-icon-primary-default'
                onClick={handleOpenDeleteMultipleModal}
              >
                Xoá
              </Button>
            </div>
          )}
        </div>
        <div className='flex w-1/3 justify-center'>
          <Pagination
            current={page}
            pageSize={pageSize}
            total={data?.length || 0}
            showSizeChanger={false}
            onChange={p => setPage(p)}
          />
        </div>
        <div className='flex w-1/3 min-w-[120px] items-center justify-end gap-2'>
          <Select
            value={`${pageSize} / Trang`}
            onChange={val => {
              setPageSize(Number(val))
              setPage(1)
            }}
            options={[
              { value: 10, label: '10 / Trang' },
              { value: 20, label: '20 / Trang' },
              { value: 50, label: '50 / Trang' }
            ]}
            style={{ width: 120 }}
          />
        </div>
      </div>

      {/* Modal xác nhận xóa */}
      <ModalActionDelete
        open={deleteModalOpen}
        title={
          <div className='mb-4 flex w-full items-center'>
            <div className='mr-4 flex size-12 items-center justify-center rounded-full bg-red-50'>
              <i className='onedx-delete size-6 text-icon-error-strong' />
            </div>
            <span className='text-lg font-semibold text-gray-11'>
              {deleteMultiple ? 'Xóa nhiều trạng thái' : 'Xóa trạng thái'}
            </span>
          </div>
        }
        description={
          <span className='body-14-regular text-black'>
            {deleteMultiple
              ? `Bạn có chắc chắn muốn xóa ${selectedRowKeys.length} trạng thái đã chọn?`
              : 'Bạn có chắc chắn muốn xóa trạng thái này?'}
          </span>
        }
        onCancel={handleCloseDeleteModal}
        onClose={handleCloseDeleteModal}
        onConfirm={handleConfirmDelete}
      />

      {/* Modal hiển thị điều kiện chuyển trạng thái */}
      <Modal
        title='Điều kiện chuyển trạng thái'
        open={conditionModalOpen}
        onCancel={() => setConditionModalOpen(false)}
        footer={[
          <Button key='close' type='default' onClick={() => setConditionModalOpen(false)}>
            Đóng
          </Button>
        ]}
        width={600}
      >
        <div className='space-y-2'>
          {selectedConditions.map((item, index) => (
            <div key={index} className='p-3'>
              <div className='mb-2 font-bold text-black'>{item.triggerType}</div>
              <div className='text-gray-700'>{item.condition}</div>
            </div>
          ))}
        </div>
      </Modal>

      {/* Drawer chi tiết trạng thái */}
      {detailDrawerOpen && (
        <StateDetailDrawer open={detailDrawerOpen} onClose={handleCloseDetailDrawer} selectedState={selectedState} />
      )}
    </div>
  )
  // endregion Render
}
