'use client'

import React, { useState, useMemo } from 'react'

import type { TransitionStateTableItem } from '@/types/workflow/transition'
import { CustomDrawer } from '@/components/workflow/shared/drawer'

// Import các component con
import {
  GeneralInfoSection,
  TriggerUpdateSection,
  ActionAfterTransitionSection,
  PredecessorStateSection
} from './components/state-transition-detail'
import { TransitionGeneralInfoSection } from './components/transition-detail'
import { mapTriggerData } from '@/utils/workflow'

interface StateDetailDrawerProps {
  open: boolean
  onClose: () => void
  selectedState: TransitionStateTableItem | null
  type?: 'state' | 'transition'
}

export const StateDetailDrawer: React.FC<StateDetailDrawerProps> = ({
  open,
  onClose,
  selectedState,
  type = 'state'
}) => {
  // State để quản lý trạng thái đóng/mở của các section trong drawer
  const [collapsedSections, setCollapsedSections] = useState({
    general: false, // Thông tin chung
    predecessor: false, // Trạng thái tiền nhiệm
    trigger: false, // Trigger cập nhật
    manual: false, // Cấu hình manual trigger
    apiCall: false, // Cấu hình API Call
    webhook: false, // Cấu hình Webhook
    schedule: false, // Cấu hình lịch trình
    condition: false, // Cấu hình điều kiện
    action: false // Hành động sau khi chuyển đổi trạng thái
  })

  // Lấy trigger data từ selectedState
  const triggerData = useMemo(() => {
    if (!selectedState) return null

    return mapTriggerData(type === 'transition' ? selectedState?.data?.triggers : selectedState?.triggers)
  }, [selectedState, type])

  // Hàm để cập nhật một section cụ thể
  const toggleSection = (section: keyof typeof collapsedSections) => {
    setCollapsedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }

  if (!selectedState) return null

  return (
    <CustomDrawer
      title={type === 'transition' ? 'Chi tiết chuyển đổi' : `Chi tiết trạng thái "${selectedState.name}"`}
      icon={<i className='onedx-open-new-tab size-6 text-icon-info-strong' />}
      width={981}
      open={open}
      onCancel={onClose}
      cancelText='Đóng'
    >
      <div className='flex flex-col gap-5'>
        {/* Thông tin chung */}
        {type === 'transition' ? (
          <TransitionGeneralInfoSection selectedState={selectedState} />
        ) : (
          <>
            <GeneralInfoSection
              selectedState={selectedState}
              collapsed={collapsedSections.general}
              onToggle={() => toggleSection('general')}
            />
            {/* Trạng thái tiền nhiệm */}
            <PredecessorStateSection
              selectedState={selectedState}
              collapsed={collapsedSections.predecessor}
              onToggle={() => toggleSection('predecessor')}
            />
          </>
        )}

        {/* Trigger cập nhật */}
        <TriggerUpdateSection
          selectedState={selectedState}
          collapsedSections={{
            trigger: collapsedSections.trigger,
            manual: collapsedSections.manual,
            apiCall: collapsedSections.apiCall,
            webhook: collapsedSections.webhook,
            schedule: collapsedSections.schedule,
            condition: collapsedSections.condition
          }}
          toggleSection={toggleSection}
          triggerData={triggerData}
        />

        {/* Hành động sau khi chuyển đổi trạng thái */}
        <ActionAfterTransitionSection
          selectedState={selectedState}
          collapsed={collapsedSections.action}
          onToggle={() => toggleSection('action')}
        />
      </div>
    </CustomDrawer>
  )
}
