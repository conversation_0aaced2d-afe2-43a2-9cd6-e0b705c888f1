'use client'

// #region IMPORTS
import { useState } from 'react'

import { useParams } from 'next/navigation'

import { CalendarOutlined, FilterOutlined } from '@ant-design/icons'
import { DatePicker, Pagination, Select, Space, Table, Tooltip } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import { useQuery } from '@tanstack/react-query'

import { DATE_FORMATS } from '@/utils/date'

import { ORDER_SEARCH_CHECKBOX_OPTIONS } from '@/constants/workflow/transition'

import ProductPopover from './configuration/components/state-transition-detail/ProductPopover'
import type { CheckedState } from '@/components/filter/SettingInput'
import SettingInput from '@/components/filter/SettingInput'
import { processManagement } from '@/models/workflow/processManagement'
import type { OrderApplyRecord } from '@/types/workflow/transition'
import { stateTransitionConfig } from '@/models/workflow/stateTransitionConfig'
// #endregion IMPORTS

const { RangePicker } = DatePicker

export const OrderApplyConfiguration: React.FC = () => {
  // #region states
  const { id: stateTransitionId } = useParams<{ id: string }>()
  const [page, setPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [searchValue, setSearchValue] = useState('')
  const [dateRange, setDateRange] = useState<[string, string] | null>(null)
  const [selectedStateIds, setSelectedStateIds] = useState<number[]>([])

  const [checkedFilter, setCheckedFilter] = useState<CheckedState>({
    isCartCode: true,
    isServiceName: true,
    isCustomerName: true,
    isProviderName: true
  })
  // #endregion STATE DECLARATIONS

  // #region API
  // Sử dụng useQuery để gọi API lấy danh sách đơn hàng áp dụng
  const { data: orderData, isLoading } = useQuery({
    queryKey: ['stateTransitionOrders', stateTransitionId, searchValue, dateRange, selectedStateIds, page, pageSize],
    queryFn: () => {
      // Thiết lập các tham số cho API
      const params: Record<string, any> = {
        page: page - 1,
        size: pageSize,
        isCartCode: checkedFilter.isCartCode ? 1 : 0,
        isCustomerName: checkedFilter.isCustomerName ? 1 : 0,
        isProviderName: checkedFilter.isProviderName ? 1 : 0,
        isServiceName: checkedFilter.isServiceName ? 1 : 0,
        ...(searchValue && { value: searchValue }),
        ...(dateRange?.[0] && { startDate: dateRange[0] }),
        ...(dateRange?.[1] && { endDate: dateRange[1] }),
        ...(selectedStateIds?.length > 0 && { stateIds: selectedStateIds.join(',') })
      }

      return stateTransitionConfig.getOrderApplyList(stateTransitionId, params)
    },
    enabled: !!stateTransitionId
  })

  //  API lấy danh sách trạng thái
  const { data: steps, isLoading: isLoadingSteps } = useQuery({
    queryKey: ['steps-combobox'],
    queryFn: () => processManagement.getStepsCombobox(),
    // Sử dụng cache trong 10 phút, tránh gọi lại API khi đã có dữ liệu
    staleTime: 10 * 60 * 1000,
    // Giữ dữ liệu trong cache trong 15 phút
    gcTime: 15 * 60 * 1000,
    // Sử dụng select để xử lý dữ liệu trả về
    select: data =>
      data?.content?.map((step: any) => ({
        label: step.name,
        value: step.id
      })) || []
  })
  // #endregion API

  // #region table columns
  const columns: ColumnsType<OrderApplyRecord> = [
    {
      title: 'STT',
      dataIndex: 'index',
      key: 'index',
      width: 56,
      align: 'center',
      render: (_: any, _record: any, index: number) => (
        <div className='text-sm text-gray-11 group-hover:text-primary-blue'>{(page - 1) * pageSize + index + 1}</div>
      )
    },
    {
      title: 'Mã đơn hàng',
      dataIndex: 'code',
      key: 'code',
      width: 150,
      render: text => <div className='cursor-pointer text-sm text-gray-11 group-hover:text-primary-blue'>{text}</div>
    },
    {
      title: 'Sản phẩm',
      dataIndex: 'orderItems',
      key: 'orderItems',
      width: 300,
      render: orderItems => (
        <div className='caption-12-medium flex items-center gap-2 text-sm text-gray-11 group-hover:text-primary-blue'>
          {orderItems.length > 1 && (
            <ProductPopover orderItems={orderItems}>
              <div className='flex cursor-pointer items-center gap-2 '>
                <div className='line-clamp-1 rounded-lg bg-bg-neutral-lighter px-2 py-0.5 text-text-neutral-medium'>
                  {orderItems[0].serviceName || orderItems[0]?.pricingName}
                </div>
                <div className='min-w-10 rounded-lg border border-solid border-border-neutral-medium px-2 py-0.5 text-text-neutral-strong'>
                  +{orderItems.length - 1}
                </div>
              </div>
            </ProductPopover>
          )}
        </div>
      )
    },
    {
      title: 'Nhà cung cấp',
      dataIndex: 'providerName',
      key: 'providerName',
      width: 250,
      render: text => (
        <Tooltip title={text}>
          <div className='line-clamp-1 text-sm text-gray-11 group-hover:text-primary-blue'>{text}</div>
        </Tooltip>
      )
    },
    {
      title: 'Khách hàng',
      dataIndex: 'userName',
      key: 'userName',
      width: 150,
      render: text => (
        <Tooltip title={text}>
          <div className='line-clamp-1 text-sm text-gray-11 group-hover:text-primary-blue'>{text}</div>
        </Tooltip>
      )
    },
    {
      title: 'Ngày đặt hàng',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 230,
      render: text => <div className='text-sm text-gray-11 group-hover:text-primary-blue'>{text}</div>
    },
    {
      title: 'Người phụ trách',
      dataIndex: 'assigneeName',
      key: 'assigneeName',
      width: 230,
      render: text => (
        <Tooltip title={text}>
          <div className='line-clamp-1 text-sm text-gray-11 group-hover:text-primary-blue'>{text}</div>
        </Tooltip>
      )
    }
  ]
  // #endregion table columns

  // region render component
  return (
    <div>
      <div className='overflow-hidden rounded-md bg-white p-4'>
        {/* Bộ lọc */}
        <div className='mb-4 flex items-center gap-4'>
          <SettingInput
            placeholder='Tìm kiếm theo mã đơn hàng/tên sản phẩm dịch vụ/tên khách hàng/nhà cung cấp'
            styles={{ width: '100%' }}
            checked={checkedFilter}
            setChecked={setCheckedFilter}
            onKeyDown={event => {
              if (event.key === 'Enter') {
                event.preventDefault()
                setSearchValue(event.currentTarget.value)
                setPage(1)
              }
            }}
            checkBoxOptions={ORDER_SEARCH_CHECKBOX_OPTIONS}
            size='small'
          />
          <Space>
            <Select
              placeholder='Trạng thái'
              style={{ width: 150 }}
              mode='multiple'
              value={selectedStateIds}
              onChange={values => {
                setSelectedStateIds(values)
                setPage(1)
              }}
              loading={isLoadingSteps}
              options={steps}
              suffixIcon={<FilterOutlined />}
            />
          </Space>
          <Space>
            <RangePicker
              placeholder={['Từ ngày', 'Đến ngày']}
              className='w-[280px]'
              format={DATE_FORMATS.DD_MM_YYYY_SLASH}
              size='middle'
              suffixIcon={<CalendarOutlined />}
              onChange={(dates, dateStrings) => {
                setDateRange(dateStrings[0] && dateStrings[1] ? dateStrings : null)
                setPage(1)
              }}
            />
          </Space>
        </div>

        {/* Bảng Đơn hàng áp dụng cấu hình */}
        <div className='pb-4'>
          <Table
            columns={columns}
            dataSource={orderData?.content}
            rowKey='key'
            pagination={false}
            className='rounded-md border border-gray-200 [&_tr:hover_td]:!text-primary-blue'
            size='middle'
            loading={isLoading}
            onRow={() => ({
              className: 'border-b border-gray-alpha-3 transition-colors duration-200 group hover:font-medium'
            })}
          />
        </div>

        {/* Phân trang */}
        <div className='flex items-center justify-between'>
          <div className='flex w-1/3 justify-center' />
          <div className='flex w-1/3 justify-center'>
            <Pagination
              current={page}
              pageSize={pageSize}
              total={orderData?.totalElements}
              showSizeChanger={false}
              onChange={p => setPage(p)}
            />
          </div>
          <div className='flex w-1/3 min-w-[120px] items-center justify-end gap-2'>
            <Select
              value={`${pageSize} / Trang`}
              onChange={val => {
                setPageSize(Number(val))
                setPage(1)
              }}
              options={[
                { value: 10, label: '10 / Trang' },
                { value: 20, label: '20 / Trang' },
                { value: 50, label: '50 / Trang' }
              ]}
              style={{ width: 120 }}
            />
          </div>
        </div>
      </div>
    </div>
  )
}
// endregion
