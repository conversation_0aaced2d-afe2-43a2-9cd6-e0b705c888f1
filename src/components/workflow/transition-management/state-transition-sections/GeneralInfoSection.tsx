'use client'

import { DownOutlined, InfoCircleOutlined, UpOutlined } from '@ant-design/icons'
import { Empty, Form, Input, Select, Tooltip } from 'antd'

import { RequiredDotLabel } from '@/components/label/RequiredDotLabel'
import { SwitchWithStatus } from '@/components/swtich/SwitchWithStatus'
import { TARGET_OBJECTS } from '@/constants/workflow/transition'
import type { GeneralInfoProps } from '@/types/workflow/transition'

export const GeneralInfoSection: React.FC<GeneralInfoProps> = ({
  form,
  generalCollapsed,
  setGeneralCollapsed,
  setSelectedObject
}) => {
  // Xử lý khi đối tượng thay đổi
  const handleObjectChange = (value: string) => {
    setSelectedObject(value)
    form.setFieldsValue({ categories: [] }) // Reset danh mục khi đổi đối tượng
  }

  return (
    <div className='mb-2 overflow-hidden rounded-md bg-white'>
      <div className='mb-3 border-b border-solid border-gray-alpha-13 pb-3'>
        <div className='flex items-center px-4 py-3 pb-0'>
          <div className='mr-3 h-4 w-1 bg-yellow-400'></div>
          <h2 className='text-base font-medium'>Thông tin chung</h2>
          <div className='ml-auto flex items-center'>
            <div className='mr-2 whitespace-nowrap text-sm font-medium leading-5 tracking-tight-05 text-primary-blue'>
              Trạng thái hoạt động
            </div>
            <Form.Item name='isActive' valuePropName='checked' initialValue={true} noStyle>
              <SwitchWithStatus className='!h-auto !bg-transparent !p-0' />
            </Form.Item>
            <div className='mx-4 h-4 w-px bg-gray-200'></div>
            <button
              onClick={() => setGeneralCollapsed(!generalCollapsed)}
              className='flex size-8 max-h-[32px] min-h-[32px] min-w-[32px] max-w-[32px] items-center justify-center rounded-lg bg-white hover:bg-gray-100'
              style={{ width: '32px', height: '32px', padding: 0 }}
            >
              {generalCollapsed ? <DownOutlined className='text-gray-8' /> : <UpOutlined className='text-gray-8' />}
            </button>
          </div>
        </div>
      </div>

      {!generalCollapsed && (
        <div className='p-4'>
          <div className='grid grid-cols-2 gap-4'>
            <Form.Item
              name='configName'
              label={<RequiredDotLabel>Tên cấu hình</RequiredDotLabel>}
              rules={[
                { required: true, message: 'Tên cấu hình không được bỏ trống' },
                { max: 250, message: 'Tên cấu hình không được vượt quá 250 ký tự' }
              ]}
            >
              <Input placeholder='Nhập tên cấu hình' className='h-9' />
            </Form.Item>

            <Form.Item
              name='targetObject'
              label={
                <div className='flex items-center'>
                  <RequiredDotLabel>Đối tượng</RequiredDotLabel>
                  <Tooltip title='Chọn các đối tượng trong cấu hình chuyển đổi'>
                    <InfoCircleOutlined className='ml-1 text-gray-500' />
                  </Tooltip>
                </div>
              }
              rules={[{ required: true, message: 'Đối tượng không được bỏ trống' }]}
            >
              <Select
                placeholder='Chọn đối tượng'
                onChange={handleObjectChange}
                options={TARGET_OBJECTS}
                showSearch
                optionFilterProp='label'
                className='h-9'
                notFoundContent={<Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description='Không có dữ liệu' />}
              />
            </Form.Item>
          </div>

          <Form.Item
            name='description'
            label='Mô tả'
            rules={[{ max: 100, message: 'Mô tả không được vượt quá 100 ký tự' }]}
            className='pb-4'
          >
            <Input.TextArea placeholder='Nhập mô tả' rows={3} maxLength={100} showCount className='resize-y' />
          </Form.Item>
        </div>
      )}
    </div>
  )
}
