'use client'

import { useCallback, useState, useMemo } from 'react'

import { useParams } from 'next/navigation'

import { useQuery } from '@tanstack/react-query'

import type { TabsProps } from 'antd'
import { Spin } from 'antd'
import type { Node, Edge } from '@xyflow/react'

import type { ConfigurationProps, TransitionStateTableItem } from '@/types/workflow/transition'
import type {
  IStateTransitionConfigResponse,
  IStateTransitionTableResponse,
  HeaderProps,
  ContentProps
} from '@/types/workflow/stateTransition'
import { WorkflowTabs } from '../../shared/tabs/WorkflowTabs'
import { ConfigurationTable, StateGeneralInfoDrawer, SearchFilterBar, StateDetailDrawer } from './configuration'
import { TransitionDiagram } from '../table-config-sections/diagram/TransitionDiagram'
import { transformApiDataToTableFormat } from '@/utils/workflow'
import { stateTransitionConfig } from '@/models/workflow/stateTransitionConfig'
import { DEFAULT_QUERY_PARAMS, DEFAULT_SEARCH_FILTERS, DEFAULT_SELECTED_COLUMNS } from '@/constants/workflow'
import { formatSpecialDigits } from '@/constants/common'

// region Constants
const TAB_ITEMS: TabsProps['items'] = [
  {
    key: 'list',
    label: <span className='text-sm font-medium'>Danh sách</span>
  },
  {
    key: 'diagram',
    label: <span className='text-sm font-medium'>Sơ đồ</span>
  }
]
// endregion

// region Custom hooks
const useDrawerState = () => {
  const [stateDetailDrawerOpen, setStateDetailDrawerOpen] = useState(false)
  const [transitionDetailDrawerOpen, setTransitionDetailDrawerOpen] = useState(false)
  const [selectedNodeData, setSelectedNodeData] = useState<TransitionStateTableItem | null>(null)
  const [selectedEdgeData, setSelectedEdgeData] = useState<any>(null)

  const handleCloseStateDetailDrawer = useCallback(() => {
    setStateDetailDrawerOpen(false)
    setSelectedNodeData(null)
  }, [])

  const handleCloseTransitionDetailDrawer = useCallback(() => {
    setTransitionDetailDrawerOpen(false)
    setSelectedNodeData(null)
  }, [])

  return {
    stateDetailDrawerOpen,
    setStateDetailDrawerOpen,
    transitionDetailDrawerOpen,
    setTransitionDetailDrawerOpen,
    selectedNodeData,
    setSelectedNodeData,
    selectedEdgeData,
    setSelectedEdgeData,
    handleCloseStateDetailDrawer,
    handleCloseTransitionDetailDrawer
  }
}

const useSearchFilters = () => {
  const [searchFilters, setSearchFilters] = useState(DEFAULT_SEARCH_FILTERS)

  const updateSearchFilter = useCallback((key: string, value: any) => {
    if (key === 'checkedFilter') {
      setSearchFilters(prev => ({
        ...prev,
        checkedFilter: { ...value }
      }))
    } else {
      setSearchFilters(prev => ({
        ...prev,
        [key]: value
      }))
    }
  }, [])

  const searchParams = useMemo(
    () => ({
      ...DEFAULT_QUERY_PARAMS,
      search: formatSpecialDigits(searchFilters.searchValue),
      isCode: searchFilters.checkedFilter.isCode ? 1 : 0,
      isName: searchFilters.checkedFilter.isName ? 1 : 0,
      lstTypeId: searchFilters.lstTypeId.join(','),
      lstTriggerRole: searchFilters.lstTriggerRole.join(','),
      lstTriggerType: searchFilters.lstTriggerType.join(','),
      postActionTypes: searchFilters.postActionTypes.join(','),
      isStateApplyAll: searchFilters.isStateApplyAll,
      startTime: searchFilters.startTime,
      endTime: searchFilters.endTime
    }),
    [searchFilters]
  )

  return {
    searchFilters,
    updateSearchFilter,
    searchParams
  }
}

// Utility functions
const createSelectedNodeFromNode = (node: Node): TransitionStateTableItem => ({
  key: node.id,
  index: 1,
  name: node.data?.name as string,
  displayName: (node.data?.label as string) || node.id,
  icon: node.data?.icon as string,
  code: node.data?.stateCode as string,
  type: 'workflow_state',
  previousState: '',
  triggers: [],
  condition: '',
  color: (node.data?.color as string) || '#2A6AEB',
  description: node.data?.description as string,
  objectType: node.data?.objectType as string
})
// endregion

// region Sub-components

// Header cấu hình trạng thái
const ConfigurationHeader: React.FC<HeaderProps> = ({
  showTabs,
  activeTab,
  setActiveTab,
  configCollapsed,
  setConfigCollapsed
}) => (
  <div className='mb-3 border-b border-solid border-gray-alpha-13 pb-3'>
    <div className='flex items-center justify-between px-4 py-3 pb-0'>
      <div className='flex items-center'>
        <div className='mr-3 h-4 w-1 bg-yellow-400'></div>
        <h2 className='text-base font-medium'>Cấu hình trạng thái</h2>
      </div>

      <div className='flex items-center'>
        {showTabs && <WorkflowTabs activeKey={activeTab} onChange={setActiveTab} items={TAB_ITEMS} />}

        <button
          onClick={() => setConfigCollapsed(!configCollapsed)}
          className='flex size-8 max-h-[32px] min-h-[32px] min-w-[32px] max-w-[32px] items-center justify-center rounded-lg bg-white hover:bg-gray-100'
          style={{ width: '32px', height: '32px', padding: 0 }}
        >
          {configCollapsed ? (
            <i className='onedx-chevron-down size-5 text-gray-8' />
          ) : (
            <i className='onedx-chevron-up size-5 text-gray-8' />
          )}
        </button>
      </div>
    </div>
  </div>
)

// Nội dung cấu hình trạng thái
const ConfigurationContent: React.FC<ContentProps> = ({
  activeTab,
  readOnly,
  selectedColumns,
  setSelectedColumns,
  searchFilters,
  updateSearchFilter,
  isLoading,
  configData,
  configCollapsed,
  handleNodeClick,
  handleEdgeClick,
  onDeleteItems
}) => (
  <div className='p-5'>
    <div>
      {activeTab === 'list' && (
        <>
          <SearchFilterBar
            readOnly={readOnly}
            selectedColumns={selectedColumns}
            setSelectedColumns={setSelectedColumns}
            searchFilters={searchFilters}
            updateSearchFilter={updateSearchFilter}
          />
          <Spin spinning={isLoading}>
            <ConfigurationTable
              data={configData}
              setTableData={() => {}}
              readOnly={readOnly}
              selectedColumns={selectedColumns}
              onAfterDelete={(deletedKeys: string[]) => onDeleteItems?.(deletedKeys)}
            />
          </Spin>
        </>
      )}

      {activeTab === 'diagram' && (
        <div className='w-full rounded-lg border border-gray-3'>
          <TransitionDiagram
            stateTableData={configData}
            isCollapsed={configCollapsed}
            onNodeClick={handleNodeClick}
            onEdgeClick={handleEdgeClick}
          />
        </div>
      )}
    </div>
  </div>
)
// endregion

// region Render
export const ConfigurationSection: React.FC<ConfigurationProps> = ({
  configCollapsed,
  setConfigCollapsed,
  readOnly = false,
  showTabs = false,
  onDeleteItems
}) => {
  const params = useParams()
  const stateTransitionId = params?.id as string

  const [activeTab, setActiveTab] = useState('list')
  const [selectedColumns, setSelectedColumns] = useState<string[]>(DEFAULT_SELECTED_COLUMNS)

  const {
    stateDetailDrawerOpen,
    setStateDetailDrawerOpen,
    transitionDetailDrawerOpen,
    setTransitionDetailDrawerOpen,
    selectedNodeData,
    setSelectedNodeData,
    selectedEdgeData,
    setSelectedEdgeData,
    handleCloseStateDetailDrawer,
    handleCloseTransitionDetailDrawer
  } = useDrawerState()

  const { searchFilters, updateSearchFilter, searchParams } = useSearchFilters()

  // API Dữ liệu bảng danh sách cấu hình trạng thái
  const { data: configData, isLoading } = useQuery<IStateTransitionTableResponse>({
    queryKey: ['stateTransitionConfig', stateTransitionId, { ...searchParams }],
    queryFn: async (): Promise<IStateTransitionTableResponse> => {
      if (!stateTransitionId) return []

      const response: IStateTransitionConfigResponse = await stateTransitionConfig.getStateTransitionConfig(
        stateTransitionId,
        searchParams
      )

      return transformApiDataToTableFormat(response)
    },
    enabled: !!stateTransitionId,
    initialData: []
  })

  // Event handlers
  const handleNodeClick = useCallback(
    (node: Node) => {
      const selectedNode = createSelectedNodeFromNode(node)

      setSelectedNodeData(selectedNode)
      setStateDetailDrawerOpen(true)
    },
    [setSelectedNodeData, setStateDetailDrawerOpen]
  )

  const handleEdgeClick = useCallback(
    (edge: Edge) => {
      setSelectedEdgeData(edge)
      setTransitionDetailDrawerOpen(true)
    },
    [setSelectedEdgeData, setTransitionDetailDrawerOpen]
  )

  return (
    <div className='overflow-hidden rounded-md bg-white' style={{ margin: 0 }}>
      {/* Header */}
      <ConfigurationHeader
        showTabs={showTabs}
        activeTab={activeTab}
        setActiveTab={setActiveTab}
        configCollapsed={configCollapsed}
        setConfigCollapsed={setConfigCollapsed}
      />

      {/* Content */}
      {!configCollapsed && (
        <ConfigurationContent
          activeTab={activeTab}
          readOnly={readOnly}
          selectedColumns={selectedColumns}
          setSelectedColumns={setSelectedColumns}
          searchFilters={searchFilters}
          updateSearchFilter={updateSearchFilter}
          isLoading={isLoading}
          configData={configData}
          configCollapsed={configCollapsed}
          handleNodeClick={handleNodeClick}
          handleEdgeClick={handleEdgeClick}
          onDeleteItems={onDeleteItems}
        />
      )}

      {/* Drawers */}
      {stateDetailDrawerOpen && (
        <StateGeneralInfoDrawer
          open={stateDetailDrawerOpen}
          onClose={handleCloseStateDetailDrawer}
          selectedState={selectedNodeData}
        />
      )}

      {/* Drawer hiển thị chi tiết transition */}
      {transitionDetailDrawerOpen && (
        <StateDetailDrawer
          open={transitionDetailDrawerOpen}
          onClose={handleCloseTransitionDetailDrawer}
          selectedState={selectedEdgeData}
          type='transition'
        />
      )}
    </div>
  )
}
// endregion
