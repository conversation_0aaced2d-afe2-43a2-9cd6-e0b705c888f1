import type { TabsProps } from 'antd'
import { Tabs } from 'antd'
import { styled } from 'styled-components'

const StyledTabs = styled(Tabs)`
  .ant-tabs-tab {
    padding-block: 2px 12px;
    padding-inline: 12px;
    font-size: 14px;
    line-height: 1.4285714286em;
  }

  .ant-tabs-tab-active .ant-tabs-tab-btn {
    color: #2a6aeb !important;
    font-weight: 500;
  }

  .ant-tabs-tab:not(.ant-tabs-tab-active) .ant-tabs-tab-btn {
    color: #0f1319;
    font-weight: 400;
  }

  .ant-tabs-ink-bar {
    background-color: #2a6aeb;
    block-size: 1px;
  }
`

export interface WorkflowTabsProps extends TabsProps {
  className?: string
}

export const WorkflowTabs = ({
  className = 'mb-0 mr-4',
  tabBarGutter = 24,
  size = 'small',
  tabBarStyle = { marginBottom: 0 },
  ...props
}: WorkflowTabsProps) => {
  return (
    <StyledTabs className={className} tabBarGutter={tabBarGutter} size={size} tabBarStyle={tabBarStyle} {...props} />
  )
}
