/**
 * ListStateView - Component hiển thị danh sách trạng thái dưới dạng bảng
 * Hỗ trợ tìm kiếm, lọ<PERSON>, phân trang và tùy chỉnh cột
 */
'use client'
import React, { useState, useEffect, useMemo } from 'react'

import { Select, DatePicker, Tooltip } from 'antd'
import type { ColumnsType } from 'antd/es/table'

import { CreateStateType } from './CreateStateType'
import { DetailStateType } from './DetailStateType'
import SettingInput, { type CheckedState } from '@/components/filter/SettingInput'
import { SEARCH_TYPE_CHECKBOX_OPTIONS } from '@/constants/workflow/stateTypes'
import ModalActionDelete from '../shared/popover/ModalActionDelete'

import { useStateTypeManagement } from '@/hooks/workflow/useStateTypeManagement'
import { WorkflowHeader } from '../shared/header'
import { WorkflowFilterBar } from '../shared/filter'
import { WorkflowTable } from '../shared/table'

// Định nghĩa kiểu dữ liệu cho selected item
interface SelectedItem {
  id: string
  code: string
  name: string
}

/**
 * Danh sách loại trạng thái
 */
export const ListStateType = () => {
  // State cho phân trang
  const [page, setPage] = useState(0)
  const [pageSize, setPageSize] = useState(10)

  // State cho drawer chi tiết
  const [detailDrawerOpen, setDetailDrawerOpen] = useState(false)
  const [selectedStateId, setSelectedStateId] = useState<string | null>(null)

  // State cho modal xác nhận xóa
  const [deleteModalOpen, setDeleteModalOpen] = useState(false)
  const [deleteTarget, setDeleteTarget] = useState<any>(null)

  // State cho filter trạng thái và khoảng thời gian
  const [statusFilter, setStatusFilter] = useState<string | undefined>(undefined)
  const [dateRange, setDateRange] = useState<[any, any] | null>(null)

  // State cho setting input
  const [checkedFilter, setCheckedFilter] = useState<CheckedState>({ isName: true, isCode: true })
  const [searchInput, setSearchInput] = useState('')
  const [search, setSearch] = useState('')

  const { deleteStateType, messageApi, contextHolder } = useStateTypeManagement()

  // State cho chọn nhiều bản ghi
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([])
  const [selectedItems, setSelectedItems] = useState<SelectedItem[]>([])

  // Debounce search: sau 300ms không gõ nữa mới cập nhật search
  useEffect(() => {
    const handler = setTimeout(() => {
      setSearch(searchInput)
    }, 300)

    return () => clearTimeout(handler)
  }, [searchInput])

  // Xây dựng params cho API
  const params = useMemo(() => {
    const isName = checkedFilter.isName ? 1 : 0
    const isCode = checkedFilter.isCode ? 1 : 0
    const status = statusFilter
    let startTime, endTime

    if (dateRange && dateRange[0] && dateRange[1]) {
      startTime = dateRange[0] ? dateRange[0].format('DD/MM/YYYY') : undefined
      endTime = dateRange[1] ? dateRange[1].format('DD/MM/YYYY') : undefined
    } else {
      startTime = dateRange?.[0] ? dateRange[0].format('DD/MM/YYYY') : undefined
      endTime = dateRange?.[1] ? dateRange[1].format('DD/MM/YYYY') : undefined
    }

    return {
      page: page ?? 0,
      size: pageSize ?? 10,
      search,
      isName,
      isCode,
      status,
      startTime,
      endTime
    }
  }, [page, pageSize, search, statusFilter, dateRange, checkedFilter])

  const { listData, listLoading } = useStateTypeManagement(params)

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const dataSource = listData?.content || []
  const total = listData?.totalElements || 0
  const current = listData?.pageable ? listData.pageable.pageNumber + 1 : 1
  const pageSz = listData?.pageable ? listData.pageable.pageSize : pageSize

  // Lấy danh sách ID của các items trong trang hiện tại
  const currentPageIds = useMemo(() => {
    return dataSource.map(item => item.id)
  }, [dataSource])

  // Hàm xử lý khi chọn/bỏ chọn items
  const handleSelectionChange = (newSelectedRowKeys: React.Key[], selectedRows: any[]) => {
    // Tạo map từ selectedItems hiện tại để dễ dàng tìm kiếm
    const selectedItemsMap = new Map(selectedItems.map(item => [item.id, item]))

    // Xác định các items đã bỏ chọn trong trang hiện tại
    const deselectedIds = currentPageIds.filter(
      id => selectedItemsMap.has(id as string) && !newSelectedRowKeys.includes(id)
    )

    // Xóa các items đã bỏ chọn khỏi map
    deselectedIds.forEach(id => selectedItemsMap.delete(id as string))

    // Thêm hoặc cập nhật các items được chọn trong trang hiện tại
    selectedRows.forEach(row => {
      selectedItemsMap.set(row.id, {
        id: row.id,
        code: row.code,
        name: row.name
      })
    })

    // Chuyển map thành array và cập nhật state
    const updatedSelectedItems = Array.from(selectedItemsMap.values())
    const updatedSelectedKeys = updatedSelectedItems.map(item => item.id)

    setSelectedItems(updatedSelectedItems)
    setSelectedRowKeys(updatedSelectedKeys)
  }

  // Hàm mở modal xóa
  const handleOpenDeleteModal = (record: any) => {
    if (record.isUsed) return
    setDeleteTarget(record)
    setDeleteModalOpen(true)
  }

  // Hàm đóng modal
  const handleCloseDeleteModal = () => {
    setDeleteModalOpen(false)
    setDeleteTarget(null)
  }

  // Xác nhận xóa (1 hoặc nhiều)
  const handleConfirmDelete = async () => {
    if (selectedRowKeys.length > 0) {
      // Xóa nhiều
      await deleteStateType.mutate(selectedRowKeys as string[])
      setSelectedRowKeys([])
      setSelectedItems([])
    } else if (deleteTarget) {
      // Xóa 1
      await deleteStateType.mutate([deleteTarget.id])
    }

    setDeleteModalOpen(false)
    setDeleteTarget(null)
  }

  // Xử lý drawer xem chi tiết
  const showDetailDrawer = (stateId: string) => {
    setSelectedStateId(stateId)
    setDetailDrawerOpen(true)
  }

  const closeDetailDrawer = () => {
    setDetailDrawerOpen(false)
  }

  // Xác định các hàng không thể chọn (isUsed = true)
  const getCheckboxProps = (record: any) => ({
    disabled: record.isUsed,
    name: record.name,
    title: record.isUsed ? 'Không thể xóa loại trạng thái đang được gán với trạng thái cụ thể' : ''
  })

  // Trả về nội dung tooltip cho các hàng có isUsed = true
  const getRowTooltip = () => {
    return ''
  }

  const columns: ColumnsType<any> = [
    {
      title: 'Mã loại trạng thái',
      dataIndex: 'code',
      key: 'code',
      render: (text: string) => (
        <span className={`body-14-regular text-gray-11 group-hover:font-medium group-hover:text-primary-blue`}>
          {text}
        </span>
      ),
      width: 180
    },
    {
      title: 'Tên loại trạng thái',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: any) => {
        const content = (
          <span
            style={{
              display: 'inline-block',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              maxWidth: '100%',
              verticalAlign: 'middle'
            }}
            className='body-14-medium cursor-pointer text-gray-11 hover:underline group-hover:font-medium group-hover:text-primary-blue'
            onClick={() => showDetailDrawer(record.id)}
          >
            {text}
          </span>
        )

        return text?.length > 40 ? <Tooltip title={text}>{content}</Tooltip> : content
      },
      width: 300
    },
    {
      title: 'Mô tả',
      dataIndex: 'description',
      key: 'description',
      width: 500,
      ellipsis: { showTitle: false },
      render: (text: string) => {
        const content = (
          <span
            className='body-14-regular text-gray-11 group-hover:font-medium group-hover:text-primary-blue'
            style={{
              display: 'inline-block',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              maxWidth: '100%',
              verticalAlign: 'middle'
            }}
          >
            {text}
          </span>
        )

        return text?.length > 60 ? (
          <Tooltip title={text} placement='topLeft'>
            {content}
          </Tooltip>
        ) : (
          content
        )
      }
    },
    {
      title: 'Người tạo',
      dataIndex: 'creator',
      key: 'creator',
      render: (text: string) => (
        <span className='body-14-regular text-gray-11 group-hover:font-medium group-hover:text-primary-blue'>
          {text}
        </span>
      ),
      width: 200
    },
    {
      title: 'Trạng thái hoạt động',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <span
          className={`flex w-32 items-center justify-center rounded-lg px-3 py-1 text-xs font-semibold ${
            status === 'ACTIVE'
              ? 'border border-green-3 bg-green-2 text-green-7 '
              : 'border border-gray-3 bg-gray-2 text-gray-8'
          }`}
        >
          {status === 'ACTIVE' ? 'Hoạt động' : 'Không hoạt động'}
        </span>
      ),
      width: 180
    },
    {
      title: 'Thời gian cập nhật',
      dataIndex: 'modifiedAt',
      key: 'modifiedAt',
      render: (text: string) => (
        <span className='body-14-regular text-gray-11 group-hover:font-medium group-hover:text-primary-blue'>
          {text}
        </span>
      ),
      width: 200
    },
    {
      title: '',
      key: 'column-setting',
      fixed: 'right',
      width: 48,
      align: 'center',
      render: (_: any, record: any) => {
        const deleteIcon = (
          <div
            className='flex cursor-pointer items-center justify-center opacity-0 group-hover:opacity-100'
            onClick={() => {
              if (!record.isUsed) handleOpenDeleteModal(record)
            }}
          >
            <i
              className={`onedx-delete size-4 ${!record.isUsed ? 'cursor-pointer hover:text-red-6' : 'cursor-not-allowed'}`}
            />
          </div>
        )

        return record.isUsed ? (
          <Tooltip title='Không thể xóa loại trạng thái đang được gán với trạng thái cụ thể'>{deleteIcon}</Tooltip>
        ) : (
          deleteIcon
        )
      }
    }
  ]

  // Nội dung bên trái của filter bar
  const filterBarLeftContent = (
    <SettingInput
      checked={checkedFilter}
      setChecked={setCheckedFilter}
      value={searchInput}
      onChange={setSearchInput}
      placeholder='Tìm kiếm theo mã loại trạng thái/ tên loại trạng thái'
      checkBoxOptions={SEARCH_TYPE_CHECKBOX_OPTIONS}
    />
  )

  // Nội dung bên phải của filter bar
  const filterBarRightContent = (
    <>
      <Select
        allowClear
        size='large'
        placeholder='Trạng thái'
        className='flex h-full w-2/5 items-center rounded-xl border border-gray-3 bg-white text-gray-7'
        value={statusFilter}
        onChange={val => setStatusFilter(val)}
        options={[
          { value: 'ACTIVE', label: 'Hiển thị' },
          { value: 'INACTIVE', label: 'Ẩn' }
        ]}
      />
      <DatePicker.RangePicker
        allowClear
        size='large'
        format='DD/MM/YYYY'
        className='flex h-full w-3/5 items-center rounded-xl border border-gray-3 bg-white text-gray-7'
        placeholder={['Từ ngày', 'Đến ngày']}
        value={dateRange}
        onChange={val => setDateRange(val)}
      />
    </>
  )

  return (
    <div className='flex h-full flex-col bg-white'>
      {contextHolder}
      {/* Header */}
      <WorkflowHeader title='Danh sách loại trạng thái' actions={<CreateStateType />} />

      {/* Nội dung chính */}
      <div className='flex-1 bg-white p-4'>
        {/* Filter bar */}
        <WorkflowFilterBar leftContent={filterBarLeftContent} rightContent={filterBarRightContent} />

        {/* Table */}
        <WorkflowTable
          typeOfTable='STATE_TYPE'
          dataSource={dataSource}
          columns={columns}
          total={total}
          current={current}
          pageSize={pageSz}
          loading={listLoading}
          selectedRowKeys={selectedRowKeys}
          onChangePage={setPage}
          onChangePageSize={setPageSize}
          onChangeSelectedRows={handleSelectionChange}
          onDelete={handleOpenDeleteModal}
          getCheckboxProps={getCheckboxProps}
          rowTooltip={getRowTooltip}
        />

        {/* Drawer xem chi tiết */}
        {selectedStateId && (
          <DetailStateType
            messageApi={messageApi}
            stateTypeId={selectedStateId}
            onClose={closeDetailDrawer}
            open={detailDrawerOpen}
          />
        )}

        {/* Modal xác nhận xóa */}
        <ModalActionDelete
          open={deleteModalOpen}
          title={
            <div className='mb-4 flex w-full items-center'>
              <div className='mr-4 flex size-12 items-center justify-center rounded-full bg-red-50'>
                <i className='onedx-delete size-6 text-icon-error-strong' />
              </div>
              <span className='text-lg font-semibold text-gray-11'>Xóa loại trạng thái</span>
            </div>
          }
          description={
            <span className='body-14-regular text-black'>Bạn có chắc chắn muốn xóa loại trạng thái đang chọn?</span>
          }
          onCancel={handleCloseDeleteModal}
          onClose={handleCloseDeleteModal}
          onConfirm={handleConfirmDelete}
        />
      </div>
    </div>
  )
}
