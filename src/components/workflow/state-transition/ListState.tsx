/**
 * ListState - Component hiển thị danh sách trạng thái dưới dạng bảng
 * Hỗ trợ tìm kiếm, lọ<PERSON>, phân trang và tùy chỉnh cột
 */
'use client'
import React, { useState, useEffect, useMemo } from 'react'

import { Select, Tooltip, Popover, Input, Checkbox, Button } from 'antd'
import type { ColumnsType } from 'antd/es/table'

import { CreateState } from './CreateState'
import { DetailState } from './DetailState'
import SettingInput, { type CheckedState, type CheckboxOption } from '@/components/filter/SettingInput'
import { MultiCheckboxSelect } from '@/components/filter/MultiCheckboxSelect'
import { SEARCH_STATE_CHECKBOX_OPTIONS } from '@/constants/workflow/transition'
import {
  CUSTOM_COLUMNS_CONFIG,
  DEFAULT_COLUMNS_CONFIG,
  DEFAULT_FILTER_PARAM,
  LIST_FILTER,
  STATE_OBJECT_CHECKBOX_OPTIONS
} from '@/constants/workflow/stateTransition'
import ModalActionDelete from '../shared/popover/ModalActionDelete'

import { useStateManagement } from '@/hooks/workflow/useStateManagement'
import type { StateFilterParams } from '@/types/workflow/stateTransition'
import { WorkflowHeader } from '../shared/header'
import { WorkflowFilterBar } from '../shared/filter'
import { WorkflowTable } from '../shared/table'
import { FilterModal } from '@/components/filter'

// Định nghĩa kiểu dữ liệu cho response API
interface StateResponse {
  content: any[]
  totalElements: number
  pageable: {
    pageNumber: number
    pageSize: number
  }
}

// Định nghĩa kiểu dữ liệu cho selected item
interface SelectedItem {
  id: string
  code: string
  name: string
}

// Hàm chuyển đổi từ định dạng { label, value } sang { label, key }
const convertToCheckboxOptions = (options: { label: string; value: string }[]): CheckboxOption[] => {
  return options.map(option => ({
    label: option.label,
    key: option.value
  }))
}

// Hàm map objectType sang label tiếng Việt
const getObjectTypeLabel = (value: string) => {
  const found = STATE_OBJECT_CHECKBOX_OPTIONS.find((opt: { value: string; label: string }) => opt.value === value)

  return found ? found.label : value
}

/**
 * Danh sách trạng thái
 */
export const ListState = () => {
  // State cho phân trang
  const [page, setPage] = useState(0)
  const [pageSize, setPageSize] = useState(10)

  const [popoverOpen, setPopoverOpen] = useState(false)
  const [columnSearch, setColumnSearch] = useState('')

  // State cho cột tùy chỉnh
  const [showColumns, setShowColumns] = useState<{ [key: string]: boolean }>({
    description: true,
    status: true,
    modifiedAt: true,
    creator: true
  })

  // State cho drawer chi tiết
  const [detailDrawerOpen, setDetailDrawerOpen] = useState(false)
  const [selectedStateId, setSelectedStateId] = useState<string | null>(null)

  // State cho modal xác nhận xóa
  const [deleteModalOpen, setDeleteModalOpen] = useState(false)
  const [deleteTarget, setDeleteTarget] = useState<any>(null)

  // State cho filter dùng chung và đối tượng
  const [applyAllFilter, setApplyAllFilter] = useState<boolean | undefined>(undefined)
  const [objectTypeFilter, setObjectTypeFilter] = useState<string[]>([])

  // State cho setting input
  const [checkedFilter, setCheckedFilter] = useState<CheckedState>({ isName: true, isCode: true, isType: true })
  const [searchInput, setSearchInput] = useState('')
  const [search, setSearch] = useState('')

  // State cho chọn nhiều bản ghi
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([])
  const [selectedItems, setSelectedItems] = useState<SelectedItem[]>([])

  // Debounce search: sau 300ms không gõ nữa mới cập nhật search
  useEffect(() => {
    const handler = setTimeout(() => {
      setSearch(searchInput)
    }, 300)

    return () => clearTimeout(handler)
  }, [searchInput])

  // State cho FilterModal
  const [filterParam, setFilterParam] = useState(DEFAULT_FILTER_PARAM)

  // Xây dựng params cho API
  const params = useMemo<StateFilterParams>(() => {
    const isName = checkedFilter.isName ? 1 : 0
    const isCode = checkedFilter.isCode ? 1 : 0
    const objectTypes = objectTypeFilter.length > 0 ? objectTypeFilter : undefined

    return {
      page: page ?? 0,
      size: pageSize ?? 10,
      search,
      isName,
      isCode,
      applyAll: applyAllFilter,
      objectTypes,
      startTime: filterParam.startTime,
      endTime: filterParam.endTime,
      status: filterParam.status
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [page, pageSize, search, applyAllFilter, objectTypeFilter, checkedFilter, filterParam])

  const {
    deleteState,
    messageApi,
    contextHolder,
    getStateList: listData,
    getStateLoading: listLoading
  } = useStateManagement(params)

  const stateData = listData as unknown as StateResponse
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const dataSource = stateData?.content || []
  const total = stateData?.totalElements || 0
  const current = stateData?.pageable ? stateData.pageable.pageNumber + 1 : 1
  const pageSz = stateData?.pageable ? stateData.pageable.pageSize : pageSize

  // Lấy danh sách ID của các items trong trang hiện tại
  const currentPageIds = useMemo(() => {
    return dataSource.map(item => item.id)
  }, [dataSource])

  // Hàm xử lý khi chọn/bỏ chọn items
  const handleSelectionChange = (newSelectedRowKeys: React.Key[], selectedRows: any[]) => {
    // Tạo map từ selectedItems hiện tại để dễ dàng tìm kiếm
    const selectedItemsMap = new Map(selectedItems.map(item => [item.id, item]))

    // Xác định các items đã bỏ chọn trong trang hiện tại
    const deselectedIds = currentPageIds.filter(
      id => selectedItemsMap.has(id as string) && !newSelectedRowKeys.includes(id)
    )

    // Xóa các items đã bỏ chọn khỏi map
    deselectedIds.forEach(id => selectedItemsMap.delete(id as string))

    // Thêm hoặc cập nhật các items được chọn trong trang hiện tại
    selectedRows.forEach(row => {
      selectedItemsMap.set(row.id, {
        id: row.id,
        code: row.code,
        name: row.name
      })
    })

    // Chuyển map thành array và cập nhật state
    const updatedSelectedItems = Array.from(selectedItemsMap.values())
    const updatedSelectedKeys = updatedSelectedItems.map(item => item.id)

    setSelectedItems(updatedSelectedItems)
    setSelectedRowKeys(updatedSelectedKeys)
  }

  // Hàm mở modal xóa
  const handleOpenDeleteModal = (record: any) => {
    setDeleteTarget(record)
    setDeleteModalOpen(true)
  }

  // Hàm đóng modal
  const handleCloseDeleteModal = () => {
    setDeleteModalOpen(false)
    setDeleteTarget(null)
  }

  // Xác nhận xóa (1 hoặc nhiều)
  const handleConfirmDelete = async () => {
    if (selectedRowKeys.length > 0) {
      // Xóa nhiều
      await deleteState.mutate(selectedRowKeys as string[])
      setSelectedRowKeys([])
      setSelectedItems([])
    } else if (deleteTarget) {
      // Xóa 1
      await deleteState.mutate([deleteTarget.id])
    }

    setDeleteModalOpen(false)
    setDeleteTarget(null)
  }

  // Xử lý drawer xem chi tiết
  const showDetailDrawer = (stateId: string) => {
    setSelectedStateId(stateId)
    setDetailDrawerOpen(true)
  }

  const closeDetailDrawer = () => {
    setDetailDrawerOpen(false)
  }

  // Xác định các hàng không thể chọn (isUsed = true) và thêm tooltip cho checkbox
  const getCheckboxProps = (record: any) => ({
    disabled: record.isUsed,
    name: record.name,
    title: record.isUsed ? 'Không thể chọn trạng thái đang được cấu hình chuyển đổi' : ''
  })

  // Không cần tooltip cho cả hàng nữa
  const getRowTooltip = () => {
    return ''
  }

  const columns: ColumnsType<any> = [
    {
      title: 'Mã trạng thái',
      dataIndex: 'code',
      key: 'code',
      render: (text: string) => (
        <span
          className={`body-14-regular group-hover:body-14-medium text-gray-11 group-hover:font-medium group-hover:text-primary-blue`}
        >
          {text}
        </span>
      ),
      width: 180
    },
    {
      title: 'Tên trạng thái',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: any) => {
        const content = (
          <span className='body-14-regular flex w-full cursor-pointer items-center gap-2'>
            <span
              style={{
                display: 'inline-block',
                width: 20,
                height: 20,
                borderRadius: '25%',
                backgroundColor: record.colorCode,
                marginRight: 8,
                flexShrink: 0
              }}
            />
            <span
              style={{
                display: 'inline-block',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                maxWidth: '100%',
                verticalAlign: 'middle'
              }}
              className='body-14-medium cursor-pointer text-gray-11 hover:underline group-hover:font-medium group-hover:text-primary-blue'
              onClick={() => showDetailDrawer(record.id)}
            >
              {text}
            </span>
          </span>
        )

        return text?.length > 25 ? (
          <Tooltip title={text}>
            <div onClick={() => showDetailDrawer(record.id)}>{content}</div>
          </Tooltip>
        ) : (
          <div onClick={() => showDetailDrawer(record.id)}>{content}</div>
        )
      },
      width: 250
    },
    {
      title: 'Tên hiển thị',
      dataIndex: 'displayName',
      key: 'displayName',
      render: (text: string) => {
        const content = (
          <span
            style={{
              display: 'inline-block',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              maxWidth: 150,
              verticalAlign: 'middle'
            }}
            className='body-14-regular text-gray-11 group-hover:font-medium group-hover:text-primary-blue'
          >
            {text}
          </span>
        )

        return text?.length > 15 ? <Tooltip title={text}>{content}</Tooltip> : content
      },
      width: 200
    },
    {
      title: 'Loại trạng thái',
      dataIndex: 'typeName',
      key: 'typeName',
      render: (text: string) => {
        const content = (
          <span
            style={{
              display: 'inline-block',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              maxWidth: '100%',
              verticalAlign: 'middle'
            }}
            className='body-14-regular text-gray-11 group-hover:font-medium group-hover:text-primary-blue'
          >
            {text}
          </span>
        )

        return text?.length > 30 ? <Tooltip title={text}>{content}</Tooltip> : content
      },
      width: 250
    },
    {
      title: 'Đối tượng',
      dataIndex: 'objectType',
      key: 'objectType',
      render: (text: string, record: any) => (
        <span className='body-14-regular flex items-center gap-2 text-gray-11 group-hover:font-medium group-hover:text-primary-blue'>
          {record.applyAll && <i className='onedx-double-checked size-5' />}
          {getObjectTypeLabel(text)}
        </span>
      ),
      width: 220
    },
    // Cột tuỳ chỉnh
    ...(showColumns.description
      ? [
          {
            title: 'Mô tả',
            dataIndex: 'description',
            key: 'description',
            width: 320,
            ellipsis: { showTitle: false },
            render: (text: string) => {
              const content = (
                <span
                  className='body-14-regular text-gray-11 group-hover:font-medium group-hover:text-primary-blue'
                  style={{
                    display: 'inline-block',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    maxWidth: '100%',
                    verticalAlign: 'middle'
                  }}
                >
                  {text}
                </span>
              )

              return text?.length > 40 ? (
                <Tooltip title={text} placement='topLeft'>
                  {content}
                </Tooltip>
              ) : (
                content
              )
            }
          }
        ]
      : []),
    ...(showColumns.status
      ? [
          {
            title: 'Trạng thái',
            dataIndex: 'status',
            key: 'status',
            render: (status: string) => (
              <span
                className={`flex w-32 items-center justify-center rounded-lg px-3 py-1 text-xs font-semibold ${
                  status === 'ACTIVE'
                    ? 'border border-green-3 bg-green-2 text-green-7'
                    : 'border border-gray-3 bg-gray-2 text-gray-8'
                }`}
              >
                {status === 'ACTIVE' ? 'Hoạt động' : 'Không hoạt động'}
              </span>
            ),
            width: 180
          }
        ]
      : []),
    ...(showColumns.creator
      ? [
          {
            title: 'Người tạo',
            dataIndex: 'creator',
            key: 'creator',
            render: (text: string) => (
              <span className='body-14-regular text-gray-11 group-hover:font-medium group-hover:text-primary-blue'>
                {text}
              </span>
            ),
            width: 200
          }
        ]
      : []),
    ...(showColumns.modifiedAt
      ? [
          {
            title: 'Thời gian cập nhật',
            dataIndex: 'modifiedAt',
            key: 'modifiedAt',
            render: (text: string) => (
              <span className='body-14-regular text-gray-11 group-hover:font-medium group-hover:text-primary-blue'>
                {text}
              </span>
            ),
            width: 200
          }
        ]
      : []),
    // Cột xóa
    {
      title: '',
      key: 'column-setting',
      fixed: 'right',
      width: 48,
      align: 'center',
      render: (_: any, record: any) => {
        const deleteIcon = (
          <div
            className={`flex cursor-pointer items-center justify-center opacity-0 group-hover:opacity-100 ${
              record.isUsed ? 'cursor-not-allowed' : ''
            }`}
            onClick={record.isUsed ? undefined : () => handleOpenDeleteModal(record)}
          >
            <i
              className={`onedx-delete size-4 ${!record.isUsed ? 'cursor-pointer hover:text-red-6' : 'cursor-not-allowed'}`}
            />
          </div>
        )

        return record.isUsed ? (
          <Tooltip title='Không thể xóa trạng thái đang được cấu hình chuyển đổi'>{deleteIcon}</Tooltip>
        ) : (
          deleteIcon
        )
      }
    }
  ]

  // Chuyển đổi options cho checkbox
  const searchCheckboxOptions = convertToCheckboxOptions(SEARCH_STATE_CHECKBOX_OPTIONS)

  // Nội dung bên trái của filter bar
  const filterBarLeftContent = (
    <SettingInput
      checked={checkedFilter}
      setChecked={setCheckedFilter}
      value={searchInput}
      onChange={setSearchInput}
      placeholder='Tìm kiếm theo mã trạng thái/ tên trạng thái/ loại trạng thái'
      checkBoxOptions={searchCheckboxOptions}
    />
  )

  // Nội dung bên phải của filter bar
  const filterBarRightContent = (
    <div className='flex w-full gap-4'>
      <div className='w-[300px]'>
        <Select
          allowClear
          placeholder='Dùng chung'
          className='flex size-full items-center rounded-xl border border-gray-3 bg-white text-gray-7'
          value={applyAllFilter}
          onChange={val => setApplyAllFilter(val)}
          options={[
            { label: 'Dùng chung', value: 1 },
            { label: 'Không dùng chung', value: 0 }
          ]}
        />
      </div>
      <div className='w-[200px] min-w-[200px]'>
        <MultiCheckboxSelect
          placeholder='Đối tượng'
          optionFilter={STATE_OBJECT_CHECKBOX_OPTIONS}
          value={objectTypeFilter}
          onChange={val => setObjectTypeFilter(val)}
          height={40}
          width='100%'
        />
      </div>
      <div className='w-[100px]'>
        <FilterModal
          listFilter={LIST_FILTER}
          filterParam={filterParam}
          changeFilterParam={setFilterParam}
          disableAutofill
          method='post'
          icon='onedx-filter-traffic'
        />
      </div>
      <div className='w-[300px] max-w-[400px]'>
        <Popover
          open={popoverOpen}
          onOpenChange={setPopoverOpen}
          placement='bottomRight'
          trigger='click'
          content={
            <div className='max-w-96'>
              <Input
                placeholder='Tìm giá trị'
                className='mb-2'
                value={columnSearch}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setColumnSearch(e.target.value)}
              />
              <div className='flex max-h-60 flex-col gap-1 overflow-auto'>
                {/* Cột mặc định */}
                {DEFAULT_COLUMNS_CONFIG.filter(col => col.title.toLowerCase().includes(columnSearch.toLowerCase())).map(
                  col => (
                    <Checkbox key={col.key} checked disabled className='px-3 py-2 !font-medium !text-gray-7'>
                      {col.title}
                    </Checkbox>
                  )
                )}
                {/* Cột tuỳ chỉnh */}
                {CUSTOM_COLUMNS_CONFIG.filter(col => col.title.toLowerCase().includes(columnSearch.toLowerCase())).map(
                  col => (
                    <Checkbox
                      key={col.key}
                      checked={showColumns[col.key]}
                      onChange={e => setShowColumns(sc => ({ ...sc, [col.key]: e.target.checked }))}
                      className='rounded-xl px-3 py-2 !font-medium hover:bg-[#E6F1FE] hover:text-icon-info-default'
                    >
                      {col.title}
                    </Checkbox>
                  )
                )}
              </div>
            </div>
          }
        >
          <Button className='flex size-full cursor-pointer items-center justify-center gap-2 rounded-xl border border-gray-3 px-2 text-gray-500'>
            <span className='text-sm font-medium'>Chỉnh sửa bảng</span>
            <i className='onedx-table-edit size-5' />
          </Button>
        </Popover>
      </div>
    </div>
  )

  return (
    <div className='flex h-full flex-col bg-white'>
      {contextHolder}
      {/* Header */}
      <WorkflowHeader title='Danh sách trạng thái' actions={<CreateState />} />

      {/* Nội dung chính */}
      <div className='flex-1 bg-white p-4'>
        {/* Filter bar */}
        <WorkflowFilterBar
          leftWidth='w-1/2'
          rightWidth='w-1/2'
          leftContent={filterBarLeftContent}
          rightContent={filterBarRightContent}
        />

        {/* Table */}
        <WorkflowTable
          typeOfTable='STATE'
          dataSource={dataSource}
          columns={columns}
          total={total}
          current={current}
          pageSize={pageSz}
          loading={listLoading}
          selectedRowKeys={selectedRowKeys}
          onChangePage={setPage}
          onChangePageSize={setPageSize}
          onChangeSelectedRows={handleSelectionChange}
          onDelete={handleOpenDeleteModal}
          getCheckboxProps={getCheckboxProps}
          rowTooltip={getRowTooltip}
        />

        {/* Drawer xem chi tiết */}
        {selectedStateId && (
          <DetailState
            messageApi={messageApi}
            stateId={selectedStateId}
            onClose={closeDetailDrawer}
            open={detailDrawerOpen}
          />
        )}

        {/* Modal xác nhận xóa */}
        <ModalActionDelete
          open={deleteModalOpen}
          title={
            <div className='mb-4 flex w-full items-center'>
              <div className='mr-4 flex size-12 items-center justify-center rounded-full bg-red-50'>
                <i className='onedx-delete size-6 text-icon-error-strong' />
              </div>
              <span className='text-lg font-semibold text-gray-11'>Xóa trạng thái</span>
            </div>
          }
          description={
            <span className='body-14-regular text-black'>Bạn có chắc chắn muốn xóa trạng thái đã chọn?</span>
          }
          onCancel={handleCloseDeleteModal}
          onClose={handleCloseDeleteModal}
          onConfirm={handleConfirmDelete}
        />
      </div>
    </div>
  )
}
