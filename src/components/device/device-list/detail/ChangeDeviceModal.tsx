'use client'

import React, { useCallback, useEffect, useMemo, useState } from 'react'

import { useRouter } from 'next/navigation'

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'

import { debounce } from 'lodash'

import { CloseOutlined } from '@ant-design/icons'

import type { TableProps } from 'antd'
import { Spin, Modal, Button, Collapse, Tag } from 'antd'

import { styled } from 'styled-components'

import { handleSrcImg } from '@/utils/string'

import { message } from '@components/notification'

import Device from '@/models/Device'

import { usePage } from '@/hooks'

import { lstConnectStatus, tagStatus } from '@components/device/device-list/common/constant'

import SettingInput, { type CheckboxOption, type CheckedState } from '@components/filter/SettingInput'

import { ConfirmModal } from '@components/device/device-list/detail/ConfirmModal'

import type { DeviceDetailInfo, ReplaceDevice } from '@/types/device/device-list/deviceList'

import { EmptySearch } from '@components/inventory/common/EmptySearch'
import { TableRow } from '@views/device/device-list/detail/ActivityAndEvent'
import { StyledTable, StyledTableWrapper } from '@views/inventory/common/StyledTableWrapper'
import SelectFilter from '@views/product-catalog/common/SelectFilter'

const { Panel } = Collapse

const StyledCollapse = styled(Collapse)`
  .ant-collapse-content-box {
    padding-top: 0 !important;
  }
  .ant-collapse-header-text {
    font-size: 16px;
  }
  .ant-collapse-header.ant-collapse-collapsible-icon {
    display: flex;
    align-items: center;
  }
  background-color: white;
`

interface ChangeDeviceModalProps {
  open: boolean
  onClose: () => void
  onSubmit?: () => void
  deviceInfo: DeviceDetailInfo
}

type TagStatusKey = keyof typeof tagStatus

const renderTagStatus = (value: TagStatusKey) => {
  const tagInfo = tagStatus[value]

  return (
    <Tag color={tagInfo?.color}>
      <div className='w-[100px] text-center' style={{ color: `${tagInfo.textColor}` }}>
        {tagInfo?.text}
      </div>
    </Tag>
  )
}

export const ChangeDeviceModal: React.FC<ChangeDeviceModalProps> = ({ open, onClose, onSubmit, deviceInfo }) => {
  const router = useRouter()

  const [showConfirm, setShowConfirm] = useState(false)

  const [confirmData, setConfirmData] = useState<any>(null)

  const [checked, setChecked] = useState<CheckedState>({
    isSerial: true,
    isCode: true
  })

  const checkBoxOptions: CheckboxOption[] = [
    { label: 'Serial number', key: 'isSerial' },
    { label: 'Mã đơn hàng', key: 'isCode' }
  ]

  const defaultSearchParams = useMemo(
    () => ({
      page: 0,
      size: 10,
      status: '',
      phoneNumber: ''
    }),
    []
  )

  const [filterParams, setFilterParams] = useState(defaultSearchParams)

  const queryClient = useQueryClient()

  const model = useMemo(() => {
    return deviceInfo?.model
  }, [deviceInfo])

  const orderCode = useMemo(() => {
    return deviceInfo?.businessStatus?.orderCode
  }, [deviceInfo])

  const {
    content,
    page,
    pageSize,
    isFetching: isLoadingReplaceDevices
  } = usePage(['replace-device', 'table', model, orderCode, filterParams], async () => {
    const params = {
      ...filterParams,
      page: page - 1,
      pageSize,
      size: pageSize,
      sort: 'id,desc'
    }

    const res = await Device.getChangeDevice(model, orderCode, { ...params })

    return res
  })

  const updateDevice = useMutation({
    mutationFn: (data: any) => Device.updateDevice(data),
    onSuccess: () => {
      message.success('Thay đổi thiết bị thành công!')
      queryClient.invalidateQueries({ queryKey: ['deviceDetail'] })
      router.back()
    },
    onError: () => {
      message.error('Có lỗi khi thay đổi')
    }
  })

  const { data: lstCustomerPhone = [] } = useQuery({
    queryKey: ['getListCustomerPhone'],
    queryFn: async () => {
      const data = { page: 0, size: 1000 }
      const res = await Device.searchCustomerPhone(data)

      return (
        res?.map((item: any) => ({
          label: item,
          value: item
        })) || []
      )
    }
  })

  const listFilter = [
    {
      name: 'phoneNumber',
      label: 'SĐT Khách hàng',
      options: lstCustomerPhone,
      placeHolder: 'Chọn SĐT Khách hàng',
      canSearch: true,
      notSelectFirstValue: true
    },
    {
      name: 'status',
      label: 'Trạng thái kết nối',
      options: lstConnectStatus,
      placeHolder: 'Chọn Trạng thái kết nối',
      notSelectFirstValue: true
    }
  ]

  // Tạo hàm debounce bên ngoài useCallback
  const debouncedSetFilterParams = debounce(params => {
    setFilterParams(params)
  }, 200)

  const handleSearch = useCallback(
    (value: string) => {
      debouncedSetFilterParams({
        ...filterParams,
        value: value || '',
        isSerial: Number(Boolean(checked.isSerial)),
        isCode: Number(Boolean(checked.isCode))
      })
    },
    [filterParams, checked, debouncedSetFilterParams]
  )

  const mainColumns: TableProps<ReplaceDevice>['columns'] = [
    {
      title: 'Serial number',
      dataIndex: 'serialNumber',
      key: 'serialNumber',
      align: 'center',
      width: 200,
      sorter: (a: any, b: any) => a.serialNumber?.localeCompare(b.serialNumber)
    },
    {
      title: 'Mã đơn hàng',
      dataIndex: 'orderCode',
      key: 'orderCode',
      align: 'center',
      width: 200,
      render: (value: string, record: ReplaceDevice) => (
        <div className='hover:cursor-pointer' onClick={() => router.push(`/order/detail/${record.orderCode}`)}>
          {value}
        </div>
      )
    },
    {
      title: 'Tên khách hàng',
      dataIndex: 'customerName',
      key: 'customerName',
      align: 'center',
      width: 200
    },
    {
      title: 'Trạng thái kết nối',
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      width: 200,
      render: (value: any) => renderTagStatus(value)
    }
  ]

  const [selectedDevice, setSelectedDevice] = useState<ReplaceDevice | null>(null)

  const rowSelection = {
    type: 'radio' as const,
    selectedRowKeys: selectedDevice ? [selectedDevice.id] : [],
    onChange: (_: React.Key[], selectedRows: ReplaceDevice[]) => {
      setSelectedDevice(selectedRows[0] || null)
    }
  }

  useEffect(() => {
    if (!open) {
      setSelectedDevice(null)
      setFilterParams(defaultSearchParams)
    }
  }, [defaultSearchParams, open])

  return (
    <Modal
      title={
        <div className='flex gap-4 border-b-2 border-solid border-gray-100 pb-3 pt-1'>
          <div className='size-10 rounded-full bg-sme-blue-2 p-2 text-sme-blue-7'>
            <i className='onedx-cart size-6' />
          </div>
          <div className='flex flex-1 items-center text-base font-semibold'>Thay đổi thiết bị</div>
        </div>
      }
      open={open}
      onCancel={onClose}
      width={960}
      className='rounded-2xl'
      footer={null}
      closeIcon={<CloseOutlined className='text-neutral-80' />}
    >
      {/* Thiết bị đã chọn */}
      <StyledCollapse
        className='bg-white'
        collapsible='icon'
        expandIconPosition='end'
        expandIcon={() => null}
        ghost
        defaultActiveKey={['1']}
      >
        <Panel
          key='1'
          header={
            <div className='mt-1 flex justify-between border-l-4 border-solid border-yellow-6'>
              <div className='pl-2 font-semibold'>Thiết bị đã chọn</div>
            </div>
          }
        >
          <div className=' w-full gap-3 bg-white pt-4'>
            <div className='flex items-center justify-between rounded-xl bg-neutral-100 px-4 py-3'>
              {/* Bên trái: icon + tên + thuộc tính */}
              <div className='flex items-center gap-3'>
                <img src={handleSrcImg(deviceInfo?.serviceIconUrl)} alt='device' className='size-12 rounded-md' />
                <div>
                  <div className='text-base font-medium text-neutral-900'>{deviceInfo?.deviceName}</div>
                  <div className='text-sm text-neutral-500'>{deviceInfo?.variantName}</div>
                </div>
              </div>

              {/* Bên phải: serial number */}
              <div className='text-sm font-medium text-neutral-700'>{deviceInfo?.serialNumber}</div>
            </div>
          </div>
        </Panel>
      </StyledCollapse>

      {/* Thiết bị thay thế */}
      <StyledCollapse className='bg-white' collapsible='icon' expandIconPosition='end' ghost defaultActiveKey={['1']}>
        <Panel
          key='1'
          header={
            <div className='mt-1 flex justify-between border-l-4 border-solid border-yellow-6'>
              <div className='pl-2 font-semibold'>Thiết bị thay thế</div>
            </div>
          }
        >
          <div className=' w-full gap-3 bg-white pt-4'>
            <div className='mb-4 flex items-center gap-x-2'>
              {/* Tìm kiếm */}
              <SettingInput
                placeholder='Tìm kiếm thiết bị'
                styles={{ flex: 1, minWidth: 200 }}
                size='small'
                checked={checked}
                setChecked={setChecked}
                checkBoxOptions={checkBoxOptions}
                onChange={e => handleSearch(e)}
              />

              {/* Bộ lọc */}
              <SelectFilter
                setFilterParams={setFilterParams}
                filterOptions={listFilter}
                filterParams={filterParams}
                defaultFilter={defaultSearchParams}
                customStyle={'h-[36px]'}
              />
            </div>
            <Spin spinning={isLoadingReplaceDevices}>
              <StyledTableWrapper className='w-full'>
                <StyledTable<ReplaceDevice>
                  rowSelection={rowSelection}
                  className='custom-pricing-table custom-z-index-table'
                  columns={mainColumns}
                  dataSource={content || []}
                  rowKey='id'
                  pagination={false}
                  components={{
                    body: {
                      row: TableRow
                    }
                  }}
                  locale={{
                    emptyText: (
                      <div className='flex items-center justify-center py-20'>
                        <EmptySearch title='Danh sách trống' description='Không có dữ liệu thiết bị thay thế' />
                      </div>
                    )
                  }}
                />
              </StyledTableWrapper>
            </Spin>
          </div>
        </Panel>
      </StyledCollapse>

      <div className='flex gap-4 border-b-2 border-solid border-gray-100 pb-3 pt-1'></div>

      <div className='grid grid-cols-12 items-center gap-3 border-t border-gray-200 pb-1 pt-6'>
        <div className='col-span-6 flex items-center justify-start gap-2'></div>
        <div className='col-span-6 flex items-center justify-end gap-3'>
          <Button
            color='primary'
            variant='outlined'
            className='rounded-lg px-4 py-2'
            style={{ height: '40px' }}
            onClick={onClose}
          >
            Huỷ
          </Button>
          <Button
            type='primary'
            size='large'
            className='rounded-lg px-4 py-2'
            disabled={!selectedDevice}
            onClick={() => {
              if (selectedDevice) {
                setConfirmData({
                  oldDevice: {
                    id: deviceInfo?.deviceId,
                    name: deviceInfo?.deviceName,
                    serial: deviceInfo?.serialNumber
                  },
                  newDevice: {
                    id: selectedDevice?.id as number,
                    name: selectedDevice?.deviceName || '',
                    serial: selectedDevice?.serialNumber || ''
                  }
                })

                setShowConfirm(true)
              }
            }}
          >
            Xác nhận
          </Button>
        </div>
      </div>

      {showConfirm && confirmData && (
        <ConfirmModal
          open={showConfirm}
          onClose={() => setShowConfirm(false)}
          onSubmit={(data: any) => {
            updateDevice.mutate(data)
            setShowConfirm(false)
            setTimeout(() => {
              onSubmit?.()
            }, 200)
          }}
          confirmData={confirmData}
        />
      )}
    </Modal>
  )
}
