'use client'

import React from 'react'

import { Mo<PERSON>, Button } from 'antd'
import { CloseOutlined } from '@ant-design/icons'

interface ConfirmModalProps {
  open: boolean
  onClose: () => void
  onSubmit: (data: { sourceDeviceId: number; targetDeviceId: number }) => void
  confirmData: {
    oldDevice: {
      id: number
      name?: string
      serial?: string
    }
    newDevice: {
      id: number
      name: string
      serial: string
    }
  }
}

export const ConfirmModal: React.FC<ConfirmModalProps> = ({ open, onClose, onSubmit, confirmData }) => {
  const { oldDevice, newDevice } = confirmData

  const handleConfirm = () => {
    onSubmit({
      sourceDeviceId: oldDevice.id,
      targetDeviceId: newDevice.id
    })
  }

  return (
    <Modal
      title={
        <div className='flex gap-4 pb-3'>
          <div className='size-10 rounded-full bg-sme-blue-2 p-2 text-sme-blue-7'>
            <i className={'onedx-check size-6'} />
          </div>
          <div className='flex flex-1 items-center text-base font-semibold'>
            <PERSON><PERSON><PERSON> n<PERSON>n thay đổi <PERSON>hiết bị gán với Đơn hàng
          </div>
        </div>
      }
      open={open}
      onCancel={onClose}
      width={380}
      centered
      closeIcon={<CloseOutlined className='text-neutral-80' />}
      className='rounded-xl'
      footer={
        <div className='flex w-full gap-3'>
          <Button
            onClick={onClose}
            className='flex-1 rounded-lg'
            style={{ height: 40 }}
            color='primary'
            variant='outlined'
          >
            Hủy
          </Button>
          <Button type='primary' onClick={handleConfirm} className='flex-1 rounded-lg' style={{ height: 40 }}>
            Xác nhận
          </Button>
        </div>
      }
    >
      <div className='mb-6 text-center text-sm text-neutral-700'>
        Xác nhận dùng thiết bị{' '}
        <b>
          {newDevice.name} (S/N: {newDevice.serial})
        </b>{' '}
        để thay thế cho thiết bị{' '}
        <b>
          {oldDevice.name} (S/N: {oldDevice.serial})
        </b>
        ?
      </div>
    </Modal>
  )
}
