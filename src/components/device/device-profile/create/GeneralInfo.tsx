import React from 'react'

import { Form, Input } from 'antd'

import { SelectPartner } from '@views/inventory/common/SelectPartner'

import { CustomHeaderCollapse } from '@components/device/device-profile/common/CustomHeaderCollapse'

export default function GeneralInfo() {
  return (
    <CustomHeaderCollapse title='Thông tin chung'>
      <Form.Item label='Tên hồ sơ' name='name' rules={[{ required: true, message: 'Tên hồ sơ không được bỏ trống' }]}>
        <Input placeholder='Nhập tên hồ sơ' maxLength={100}></Input>
      </Form.Item>
      <SelectPartner objectName='partnerUuid' />
      <div className='grid grid-cols-2 gap-4'>
        <Form.Item
          label='Mã model thiết bị'
          name='model'
          rules={[{ required: true, message: 'Mã thiết bị không được bỏ trống' }]}
        >
          <Input placeholder='Nhập mã thiết bị' maxLength={100}></Input>
        </Form.Item>
        <Form.Item label='Phiên bản' name='version'>
          <Input placeholder='Nhập phiên bản'></Input>
        </Form.Item>
      </div>
      <Form.Item label='Mô tả thiết bị' name='description'>
        <Input.TextArea rows={4} maxLength={500} showCount placeholder='Nhập mô tả'></Input.TextArea>
      </Form.Item>
    </CustomHeaderCollapse>
  )
}
