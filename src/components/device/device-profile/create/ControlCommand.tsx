import React from 'react'

import { Button, Form, Input, Space, Dropdown } from 'antd'

import type { MenuProps } from 'antd'

import { createDuplicateValidator } from '@/validator'

import IconDropDown from '@components/device/device-profile/common/IconDropDown'

import AddTypeModal from '@components/device/device-profile/create/AddTypeModal'

import { CustomHeaderCollapse } from '@components/device/device-profile/common/CustomHeaderCollapse'

export default function ControlCommand({ form }: any) {
  const [visibleModal, setVisibleModal] = React.useState(false)

  // lưu chỉ số command và index parameter đang mở modal để cập nhật đúng chỗ
  const [modalContext, setModalContext] = React.useState<{ commandIndex: number; paramIndex: number } | null>(null)

  const handleAddTypeSave = (formAddValues: any[]) => {
    if (!modalContext) return

    const { commandIndex, paramIndex } = modalContext
    // lấy toàn bộ controlCommands hiện tại
    const controlCommands = form.getFieldValue('controlCommands') || []

    // đảm bảo tồn tại structure để gán
    controlCommands[commandIndex] = controlCommands[commandIndex] || {}
    controlCommands[commandIndex].commandParams = controlCommands[commandIndex].commandParams || []
    controlCommands[commandIndex].commandParams[paramIndex] =
      controlCommands[commandIndex].commandParams[paramIndex] || {}

    controlCommands[commandIndex].commandParams[paramIndex].validations =
      controlCommands[commandIndex].commandParams[paramIndex].validations || {}

    const existingValues =
      controlCommands[commandIndex].commandParams[paramIndex].validations.values &&
      Array.isArray(controlCommands[commandIndex].commandParams[paramIndex].validations.values)
        ? controlCommands[commandIndex].commandParams[paramIndex].validations.values
        : []

    // 1) Ghép phần tử mới vào (append)
    const appended = [...existingValues, ...formAddValues]

    // 2) (Tùy chọn) loại bỏ phần tử có name rỗng hoặc trùng nhau theo 'name' để tránh duplicate
    const filtered = appended.filter(
      (it: any) => it && (it.name ?? it.displayName ?? it.icon ?? it.description ?? '') !== ''
    )

    // loại bỏ trùng theo 'name' (nếu ko có name thì giữ)
    const dedupedByName: any[] = []
    const seen = new Set<string>()

    for (const item of filtered) {
      const key = (item.name ?? '').toString()

      if (!key) {
        dedupedByName.push(item)
      } else if (!seen.has(key)) {
        seen.add(key)
        dedupedByName.push(item)
      }
    }

    controlCommands[commandIndex].commandParams[paramIndex].validations.values = dedupedByName

    form.setFieldsValue({ controlCommands })

    // reset context + đóng modal
    setModalContext(null)
    setVisibleModal(false)
  }

  const typeOptions: MenuProps['items'] = [
    {
      label: 'Lệnh nhanh',
      key: '1'
    },
    {
      label: 'Lệnh nâng cao',
      key: '2'
    }
  ]

  const handleMenuClick = (add: (defaultValue?: any) => void) => {
    return (e: any) => {
      if (e.key === '1') {
        add({ type: 'BUILT_IN' })
      } else if (e.key === '2') {
        add({ type: 'ADVANCE' })
      }
    }
  }

  return (
    <>
      <Form.List name='controlCommands'>
        {(fields, { add, remove }) => (
          <>
            {fields.length === 0 && (
              <div className='flex items-center justify-between rounded-lg bg-white px-5 py-4'>
                <div className='border-l-4 border-solid border-yellow-6'>
                  <div className='pl-2 text-base font-semibold'>Lệnh điều khiển</div>
                </div>
                <Dropdown menu={{ items: typeOptions, onClick: handleMenuClick(add) }} trigger={['click']}>
                  <Button type='primary'>
                    <Space>Thêm lệnh điều khiển</Space>
                  </Button>
                </Dropdown>
              </div>
            )}

            {fields.length !== 0 && (
              <CustomHeaderCollapse
                title='Lệnh điều khiển'
                surfixHeaderContent={
                  <div onClick={e => e.stopPropagation()}>
                    <Dropdown menu={{ items: typeOptions, onClick: handleMenuClick(add) }} trigger={['click']}>
                      <Button>
                        <Space>Thêm lệnh điều khiển</Space>
                      </Button>
                    </Dropdown>
                  </div>
                }
              >
                {fields.map(({ key, name, ...restField }) => {
                  const icon = form.getFieldValue(['controlCommands', name, 'labelIcon'])

                  return (
                    <div key={key} className='relative mt-4 rounded-lg bg-gray-12 p-6'>
                      <div className='absolute right-0 top-0 cursor-pointer'>
                        <Button type='text' onClick={() => remove(name)}>
                          X
                        </Button>
                      </div>

                      <Form.Item {...restField} name={[name, 'id']} noStyle></Form.Item>

                      <Form.Item {...restField} name={[name, 'type']} noStyle></Form.Item>

                      {/* Thông tin command */}
                      <div className='grid grid-cols-2 gap-4'>
                        <Form.Item
                          {...restField}
                          name={[name, 'name']}
                          label='Tên lệnh'
                          rules={[
                            { required: true, message: 'Tên lệnh không được bỏ trống' },
                            {
                              validator: createDuplicateValidator(
                                form,
                                'controlCommands',
                                'name',
                                'Tên lệnh đã tồn tại'
                              )
                            }
                          ]}
                        >
                          <Input placeholder='Nhập tên lệnh' />
                        </Form.Item>
                        <Form.Item label='Tên hiển thị'>
                          <Space.Compact block>
                            <Form.Item
                              {...restField}
                              name={[name, 'displayName']}
                              noStyle
                              rules={[{ required: true, message: 'Tên hiển thị không được bỏ trống' }]}
                            >
                              <Input placeholder='Nhập tên hiển thị' />
                            </Form.Item>

                            <Form.Item
                              {...restField}
                              name={[name, 'labelIcon']}
                              noStyle
                              rules={[{ required: true, message: 'Vui lòng chọn icon' }]}
                            >
                              <div style={{ width: 80, textAlign: 'center' }}>
                                <IconDropDown
                                  iconValue={icon}
                                  onIconChange={newIcon => {
                                    form.setFieldsValue({
                                      controlCommands: {
                                        [name]: {
                                          labelIcon: newIcon
                                        }
                                      }
                                    })
                                  }}
                                ></IconDropDown>
                              </div>
                            </Form.Item>
                          </Space.Compact>
                        </Form.Item>
                      </div>

                      <div className='grid grid-cols-2 gap-4'>
                        <Form.Item {...restField} name={[name, 'shortDescription']} label='Mô tả lệnh'>
                          <Input.TextArea placeholder='Nhập mô tả' />
                        </Form.Item>
                      </div>
                    </div>
                  )
                })}
              </CustomHeaderCollapse>
            )}
          </>
        )}
      </Form.List>

      <AddTypeModal visible={visibleModal} setVisible={setVisibleModal} onSave={handleAddTypeSave} />
    </>
  )
}
