import React from 'react'

import { Button, Form, Input, Space, Dropdown, Collapse, Select, Typography, Flex, Tooltip, Tag } from 'antd'

import type { MenuProps } from 'antd'
import { EditOutlined, CloseOutlined } from '@ant-design/icons'

import { createDuplicateValidator } from '@/validator'

import IconDropDown from '@components/device/device-profile/common/IconDropDown'

import AddTypeModal from '@components/device/device-profile/create/AddTypeModal'

import { CustomHeaderCollapse } from '@components/device/device-profile/common/CustomHeaderCollapse'
import './ParameterConfiguration.css'

const { Panel } = Collapse;
const { Text } = Typography;
const { Option } = Select;

// Dữ liệu giả lập cho giao diện
const initialParameters = [
  {
    key: '1',
    name: 'Data',
    displayName: 'Dữ liệu',
    type: 'Object',
    required: true,
    visibleToUser: true,
  },
  {
    key: '2',
    name: 'Device<PERSON>',
    displayName: 'Mã thiết bị',
    type: 'String',
    defaultValue: '0x9035EAFFFE53B278',
    minValue: '-',
    maxValue: '-',
    required: true,
  },
];

const ParameterConfiguration = () => {
  // Custom header cho Collapse Panel để thêm icon xoá
  const getPanelHeader = (title: string, showCloseIcon = false) => (
    <Flex justify="space-between" align="center" style={{ width: '100%' }}>
      <Text>
        Tham số: <Text strong>{title}</Text>
      </Text>
      {showCloseIcon && (
        <Tooltip title="Xoá tham số">
          <Button
            type="text"
            icon={<CloseOutlined />}
            size="small"
            onClick={(event) => {
              // Ngăn việc click vào nút xoá làm expand/collapse panel
              event.stopPropagation();
              console.log(`Xoá tham số ${title}`);
              // Logic xoá item ở đây...
            }}
          />
        </Tooltip>
      )}
    </Flex>
  );

  return (
    <div style={{ maxWidth: 800, margin: '24px auto' }}>
      <Collapse defaultActiveKey={['2']} accordion style={{ background: '#fff' }}>
        {initialParameters.map((param, index) => (
          <Panel header={getPanelHeader(param.name, index > 0)} key={param.key}>
            <Form
              layout="horizontal"
              labelCol={{ span: 6 }}
              wrapperCol={{ span: 18 }}
              labelAlign="left"
            >
              <Form.Item label="Tên hiển thị">
                <Input defaultValue={param.displayName} />
              </Form.Item>
              <Form.Item label="Phân loại">
                <Select defaultValue={param.type}>
                  <Option value="String">String</Option>
                  <Option value="Object">Object</Option>
                  <Option value="Number">Number</Option>
                  <Option value="Boolean">Boolean</Option>
                </Select>
              </Form.Item>
              {param.defaultValue !== undefined && (
                <Form.Item label="Giá trị mặc định">
                  <Input
                    defaultValue={param.defaultValue}
                    suffix={
                      <Tooltip title="Chỉnh sửa">
                        <EditOutlined style={{ color: 'rgba(0,0,0,.45)' }} />
                      </Tooltip>
                    }
                  />
                </Form.Item>
              )}
               {param.minValue !== undefined && (
                 <Form.Item label="Giá trị tối thiểu">
                  <Input placeholder="-" disabled />
                </Form.Item>
               )}
               {param.maxValue !== undefined && (
                 <Form.Item label="Giá trị tối đa">
                  <Input placeholder="-" disabled />
                </Form.Item>
               )}
              <Form.Item label="Tham số bắt buộc">
                <Select defaultValue={param.required} className="custom-tag-select">
                  <Option value={true}>Bật</Option>
                  <Option value={false}>Tắt</Option>
                </Select>
              </Form.Item>
              {param.visibleToUser !== undefined && (
                <Form.Item label="Hiển thị cho người dùng">
                   <Select defaultValue={param.visibleToUser} className="custom-tag-select">
                    <Option value={true}>Bật</Option>
                    <Option value={false}>Tắt</Option>
                  </Select>
                </Form.Item>
              )}
            </Form>
          </Panel>
        ))}
      </Collapse>
    </div>
  );
};

export default function ControlCommand({ form }: any) {
  const [visibleModal, setVisibleModal] = React.useState(false)

  // lưu chỉ số command và index parameter đang mở modal để cập nhật đúng chỗ
  const [modalContext, setModalContext] = React.useState<{ commandIndex: number; paramIndex: number } | null>(null)

  const handleAddTypeSave = (formAddValues: any[]) => {
    if (!modalContext) return

    const { commandIndex, paramIndex } = modalContext
    // lấy toàn bộ controlCommands hiện tại
    const controlCommands = form.getFieldValue('controlCommands') || []

    // đảm bảo tồn tại structure để gán
    controlCommands[commandIndex] = controlCommands[commandIndex] || {}
    controlCommands[commandIndex].commandParams = controlCommands[commandIndex].commandParams || []
    controlCommands[commandIndex].commandParams[paramIndex] =
      controlCommands[commandIndex].commandParams[paramIndex] || {}

    controlCommands[commandIndex].commandParams[paramIndex].validations =
      controlCommands[commandIndex].commandParams[paramIndex].validations || {}

    const existingValues =
      controlCommands[commandIndex].commandParams[paramIndex].validations.values &&
      Array.isArray(controlCommands[commandIndex].commandParams[paramIndex].validations.values)
        ? controlCommands[commandIndex].commandParams[paramIndex].validations.values
        : []

    // 1) Ghép phần tử mới vào (append)
    const appended = [...existingValues, ...formAddValues]

    // 2) (Tùy chọn) loại bỏ phần tử có name rỗng hoặc trùng nhau theo 'name' để tránh duplicate
    const filtered = appended.filter(
      (it: any) => it && (it.name ?? it.displayName ?? it.icon ?? it.description ?? '') !== ''
    )

    // loại bỏ trùng theo 'name' (nếu ko có name thì giữ)
    const dedupedByName: any[] = []
    const seen = new Set<string>()

    for (const item of filtered) {
      const key = (item.name ?? '').toString()

      if (!key) {
        dedupedByName.push(item)
      } else if (!seen.has(key)) {
        seen.add(key)
        dedupedByName.push(item)
      }
    }

    controlCommands[commandIndex].commandParams[paramIndex].validations.values = dedupedByName

    form.setFieldsValue({ controlCommands })

    // reset context + đóng modal
    setModalContext(null)
    setVisibleModal(false)
  }

  const typeOptions: MenuProps['items'] = [
    {
      label: 'Lệnh nhanh',
      key: '1'
    },
    {
      label: 'Lệnh nâng cao',
      key: '2'
    }
  ]

  const handleMenuClick = (add: (defaultValue?: any) => void) => {
    return (e: any) => {
      if (e.key === '1') {
        add({ type: 'BUILT_IN' })
      } else if (e.key === '2') {
        add({ type: 'ADVANCE' })
      }
    }
  }

  return (
    <>
      <Form.List name='controlCommands'>
        {(fields, { add, remove }) => (
          <>
            {fields.length === 0 && (
              <div className='flex items-center justify-between rounded-lg bg-white px-5 py-4'>
                <div className='border-l-4 border-solid border-yellow-6'>
                  <div className='pl-2 text-base font-semibold'>Lệnh điều khiển</div>
                </div>
                <Dropdown menu={{ items: typeOptions, onClick: handleMenuClick(add) }} trigger={['click']}>
                  <Button type='primary'>
                    <Space>Thêm lệnh điều khiển</Space>
                  </Button>
                </Dropdown>
              </div>
            )}

            {fields.length !== 0 && (
              <CustomHeaderCollapse
                title='Lệnh điều khiển'
                surfixHeaderContent={
                  <div onClick={e => e.stopPropagation()}>
                    <Dropdown menu={{ items: typeOptions, onClick: handleMenuClick(add) }} trigger={['click']}>
                      <Button>
                        <Space>Thêm lệnh điều khiển</Space>
                      </Button>
                    </Dropdown>
                  </div>
                }
              >
                {fields.map(({ key, name, ...restField }) => {
                  const icon = form.getFieldValue(['controlCommands', name, 'labelIcon'])

                  return (
                    <div key={key} className='relative mt-4 rounded-lg bg-gray-12 p-6'>
                      <div className='absolute right-0 top-0 cursor-pointer'>
                        <Button type='text' onClick={() => remove(name)}>
                          X
                        </Button>
                      </div>

                      <Form.Item {...restField} name={[name, 'id']} noStyle></Form.Item>

                      <Form.Item {...restField} name={[name, 'type']} noStyle></Form.Item>

                      {/* Thông tin command */}
                      <div className='grid grid-cols-2 gap-4'>
                        <Form.Item
                          {...restField}
                          name={[name, 'name']}
                          label='Tên lệnh'
                          rules={[
                            { required: true, message: 'Tên lệnh không được bỏ trống' },
                            {
                              validator: createDuplicateValidator(
                                form,
                                'controlCommands',
                                'name',
                                'Tên lệnh đã tồn tại'
                              )
                            }
                          ]}
                        >
                          <Input placeholder='Nhập tên lệnh' />
                        </Form.Item>
                        <Form.Item label='Tên hiển thị'>
                          <Space.Compact block>
                            <Form.Item
                              {...restField}
                              name={[name, 'displayName']}
                              noStyle
                              rules={[{ required: true, message: 'Tên hiển thị không được bỏ trống' }]}
                            >
                              <Input placeholder='Nhập tên hiển thị' />
                            </Form.Item>

                            <Form.Item
                              {...restField}
                              name={[name, 'labelIcon']}
                              noStyle
                              rules={[{ required: true, message: 'Vui lòng chọn icon' }]}
                            >
                              <div style={{ width: 80, textAlign: 'center' }}>
                                <IconDropDown
                                  iconValue={icon}
                                  onIconChange={newIcon => {
                                    form.setFieldsValue({
                                      controlCommands: {
                                        [name]: {
                                          labelIcon: newIcon
                                        }
                                      }
                                    })
                                  }}
                                ></IconDropDown>
                              </div>
                            </Form.Item>
                          </Space.Compact>
                        </Form.Item>
                      </div>

                      <div className='grid grid-cols-2 gap-4'>
                        <Form.Item {...restField} name={[name, 'shortDescription']} label='Mô tả lệnh'>
                          <Input.TextArea placeholder='Nhập mô tả' />
                        </Form.Item>
                      </div>
                      <div className='grid grid-cols-2 gap-4'>
                        <ParameterConfiguration />
                      </div>
                    </div>
                  )
                })}
              </CustomHeaderCollapse>
            )}
          </>
        )}
      </Form.List>

      <AddTypeModal visible={visibleModal} setVisible={setVisibleModal} onSave={handleAddTypeSave} />
    </>
  )
}
