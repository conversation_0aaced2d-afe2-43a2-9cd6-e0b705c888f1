/* Style cho Collapse component để giống với thi<PERSON><PERSON> kế hơn,
  ví dụ như màu nền khi active.
*/
.ant-collapse-item-active {
    background-color: #f7f7f7; /* <PERSON><PERSON><PERSON> nền xám nhạt khi panel đượ<PERSON> chọn */
}

/* Style tuỳ chỉnh cho Select component để trông giống như một cái Tag.
  Chúng ta target vào Select có class 'custom-tag-select' và khi giá trị của nó là 'true'.
*/
.custom-tag-select .ant-select-selector {
  border: none !important;
  background-color: transparent !important;
  padding: 0 8px !important;
  box-shadow: none !important;
}

.custom-tag-select.ant-select-in-form-item {
    width: fit-content;
}

/* Khi option "Bật" được chọn (value={true}) */
.custom-tag-select.ant-select-single.ant-select-show-arrow[data-value="true"] .ant-select-selector {
  background-color: #FFF7E6 !important; /* <PERSON><PERSON><PERSON> nền cam nhạt */
  border-radius: 4px;
}

.custom-tag-select.ant-select-single.ant-select-show-arrow[data-value="true"] .ant-select-selection-item {
  color: #D46B08; /* Màu chữ cam đậm */
  font-weight: 500;
}

/* Khi option "Tắt" được chọn (value={false}) */
.custom-tag-select.ant-select-single.ant-select-show-arrow[data-value="false"] .ant-select-selector {
    background-color: #f5f5f5 !important;
    border-radius: 4px;
}
.custom-tag-select.ant-select-single.ant-select-show-arrow[data-value="false"] .ant-select-selection-item {
    color: rgba(0, 0, 0, 0.65);
}
