import React from 'react'

import { Button, Form, Input } from 'antd'

import { createDuplicateValidator } from '@/validator'

import deviceInstance from '@/models/Device'

import { SelectCustom } from '@views/inventory/common/SelectCustom'

import { CustomHeaderCollapse } from '@components/device/device-profile/common/CustomHeaderCollapse'

export default function DeviceProperties({ form }: any) {
  return (
    <Form.List name='deviceAttributes'>
      {(fields, { add, remove }) => (
        <>
          {fields.length === 0 && (
            <div className='flex items-center justify-between rounded-lg bg-white px-5 py-4'>
              <div className='border-l-4 border-solid border-yellow-6'>
                <div className='pl-2 text-base font-semibold'>Thuộc tính thiết bị</div>
              </div>
              <Button type='primary' onClick={() => add()}>
                Thêm thuộc tính
              </Button>
            </div>
          )}

          {fields.length !== 0 && (
            <CustomHeaderCollapse
              title='Thuộc tính thiết bị'
              surfixHeaderContent={
                <Button
                  type='text'
                  onClick={e => {
                    e.stopPropagation()
                    add()
                  }}
                >
                  Thêm thuộc tính
                </Button>
              }
            >
              {fields.map(({ key, name, ...restField }) => (
                <div key={key} className='relative mt-4 rounded-lg bg-gray-12 p-6'>
                  <div className='absolute right-0 top-0 cursor-pointer'>
                    <Button type='text' onClick={() => remove(name)}>
                      X
                    </Button>
                  </div>

                  <Form.Item {...restField} name={[name, 'id']} noStyle></Form.Item>
                  <Form.Item
                    {...restField}
                    name={[name, 'name']}
                    label='Tên kỹ thuật'
                    rules={[
                      { required: true, message: 'Tên kỹ thuật không được bỏ trống' },
                      {
                        validator: createDuplicateValidator(
                          form,
                          'deviceAttributes',
                          'name',
                          'Tên kỹ thuật không được trùng'
                        )
                      }
                    ]}
                  >
                    <SelectCustom
                      searchFn={deviceInstance.getAttributesList}
                      searchKey='name'
                      queryKey={`attributes-${key}-${form.getFieldValue(['deviceAttributes', name, 'name'])}`}
                      additionalReturnValue={['displayName']}
                      optionLabel='name'
                      optionValue='name'
                      onChange={(value, option: any) => {
                        form.setFieldValue(['deviceAttributes', name, 'displayName'], option?.displayName)
                      }}
                      isUseLocalSearch={true}
                    ></SelectCustom>
                  </Form.Item>
                  <Form.Item
                    {...restField}
                    name={[name, 'displayName']}
                    label='Tên hiển thị'
                    rules={[{ required: true, message: 'Tên hiển thị không được bỏ trống' }]}
                  >
                    <Input placeholder='Nhập tên hiển thị' maxLength={100} />
                  </Form.Item>
                  <Form.Item {...restField} name={[name, 'description']} label='Mô tả lệnh'>
                    <Input placeholder='Nhập mô tả' maxLength={100} />
                  </Form.Item>
                </div>
              ))}
            </CustomHeaderCollapse>
          )}
        </>
      )}
    </Form.List>
  )
}
