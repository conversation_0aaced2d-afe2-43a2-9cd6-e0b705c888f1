import React from 'react'

import { useSearchParams } from 'next/navigation'

import { Tooltip } from 'antd'

import { defaults } from 'lodash'

import { useResponsive } from '@/hooks'

import SmeSubscription from '@/models/SmeSubscription'

import { formatCurrency } from '@/constants/commonFunction'

import { isCheckPricingPlan } from '@views/enterprise/convert'
import { IN_ADDON, PRICING_PLAN } from '@views/payment/constant/PaymentConstant'
import InputPayment, { getMinPaymentScreen } from '@components/input-payment/InputPayment'
import CalculateAddon from '@components/addon/calculateAddon'
import Voucher from '@views/payment/components/voucher/Voucher'
import type { ICoupon } from '@views/payment/types/payment'

interface IAddon {
  name: string
  price: number
  freeQuantity: number | null
  quantity: number
  preAmountTax: number
  handleChangeQuantityAddon: (value: any) => void
  id: number
  pricingPlan: string
  hideUnitPriceIcon?: boolean
  maximumQuantity: number
  minimumQuantity: number
  isRequired: string
  unitLimitedList: any
  style?: string
  screenUsed?: string
  couponList: ICoupon[]
  coupons: ICoupon[]
  disabledState?: {
    increase?: boolean
    decrease?: boolean
    input?: boolean
  }
  isOpenModalVoucherAddon?: boolean
  totalAmountAfterTaxFinal?: any //gia thanh tien
}

const Addon: React.FC<IAddon> = ({
  name,
  price,
  freeQuantity,
  quantity,
  preAmountTax,
  id,
  handleChangeQuantityAddon,
  pricingPlan,
  hideUnitPriceIcon,
  maximumQuantity,
  minimumQuantity,
  isRequired,
  unitLimitedList,
  style = 'font-bold text-dark-charcoal',
  screenUsed = 'payment',
  couponList = [],
  isOpenModalVoucherAddon = false,
  disabledState,
  totalAmountAfterTaxFinal,
  coupons = []
}) => {
  const { isMobile } = useResponsive()

  // Nếu là màn thanh toán bundling thì sẽ có packageId
  const searchParams = useSearchParams()
  const packageId = searchParams.get('packageId')

  if (isMobile && screenUsed === 'payment') {
    return (
      <div className='flex flex-col gap-2'>
        <div style={{ fontWeight: 600, fontSize: '14px', lineHeight: '20px' }} className='truncate' title={name}>
          {name}
        </div>
        <div className='flex items-center justify-between'>
          <div className='flex items-center justify-center'>
            <div style={{ color: '#07945F', fontWeight: 600, fontSize: '14px', lineHeight: '20px' }}>₫</div>
            <div style={{ color: '#07945F', fontWeight: 600, fontSize: '14px', lineHeight: '20px' }}>
              {formatCurrency(price)}
            </div>
          </div>
          <div className='inline-flex flex-col items-center justify-center gap-1'>
            <div className=' inline-flex items-start justify-start rounded border'>
              <InputPayment
                type='PRICING'
                value={quantity}
                isRequired={isRequired === 'YES' || screenUsed === 'payment'}
                onChange={value => handleChangeQuantityAddon({ addonId: id, value })}
                disabled={pricingPlan === PRICING_PLAN.FLAT_RATE && quantity > 0 && isRequired === 'YES'}
                disabledState={defaults(disabledState, {
                  increase: pricingPlan === PRICING_PLAN.FLAT_RATE && quantity >= 1
                })}
                max={pricingPlan === PRICING_PLAN.FLAT_RATE ? 1 : maximumQuantity}
                min={screenUsed === 'payment' ? getMinPaymentScreen(minimumQuantity) : minimumQuantity}
              />
            </div>
            {!!freeQuantity && (
              <div className='inline-flex items-center justify-start gap-1 rounded bg-green-2 px-2 py-0.5'>
                <i className='onedx-gift-box size-5 text-text-success-strong' />
                <div className='text-center text-xs font-medium leading-none  tracking-tight text-text-success-strong'>
                  Miễn phí {freeQuantity}
                </div>
              </div>
            )}
          </div>
        </div>
        <Voucher
          type={IN_ADDON}
          promotionFn={SmeSubscription.getCouponAddon}
          addonId={id}
          isOpenModalVoucher={isOpenModalVoucherAddon}
          couponUsed={[...couponList, ...coupons]}
        />
      </div>
    )
  }

  return (
    <>
      <div className='flex gap-8 py-4'>
        <div className='inline-flex shrink grow basis-0 items-center text-sm  font-semibold leading-tight tracking-tight text-dark-charcoal'>
          {name}
        </div>

        <div className='flex items-center justify-end gap-1'>
          <div className='flex items-center justify-center'>
            <div className='text-xs font-normal leading-none  tracking-tight text-dark-charcoal'>₫</div>
            <div className='text-sm font-normal leading-tight  tracking-tight text-dark-charcoal'>
              {formatCurrency(price)}
            </div>
          </div>

          {!isCheckPricingPlan(pricingPlan) && (
            <Tooltip
              overlayInnerStyle={{
                minWidth: isMobile ? '343px' : '510px',
                width: 'fit-content',
                background: '#ffffff'
              }}
              title={
                <CalculateAddon
                  unitLimitedList={unitLimitedList}
                  quantity={quantity}
                  pricingPlan={pricingPlan}
                  totalPrice={quantity === 0 ? 0 : preAmountTax}
                />
              }
              placement='bottom'
            >
              {!hideUnitPriceIcon && <i className='onedx-information size-4' />}
            </Tooltip>
          )}
        </div>

        <div className='inline-flex flex-col items-center justify-center gap-1'>
          <div className=' inline-flex items-start justify-start rounded border'>
            <InputPayment
              type='PRICING'
              value={quantity}
              isRequired={isRequired === 'YES' || screenUsed === 'payment'}
              onChange={value => handleChangeQuantityAddon({ addonId: id, value, minimumQuantity, maximumQuantity })}
              disabled={pricingPlan === PRICING_PLAN.FLAT_RATE && quantity > 0 && isRequired === 'YES'}
              disabledState={defaults(disabledState, {
                increase: pricingPlan === PRICING_PLAN.FLAT_RATE && quantity >= 1
              })}
              max={pricingPlan === PRICING_PLAN.FLAT_RATE ? 1 : maximumQuantity}
              min={screenUsed === 'payment' || screenUsed === 'detail' ? minimumQuantity : 1}
              screenUsed={screenUsed}
            />
          </div>
          {!!freeQuantity && (
            <div className='inline-flex w-[136px] items-center justify-center gap-1 rounded bg-green-2 px-2 py-0.5'>
              <i className='onedx-gift-box size-5 text-text-success-strong' />
              <div className='text-center text-xs font-medium leading-none  tracking-tight text-green-6'>
                Miễn phí {freeQuantity}
              </div>
            </div>
          )}
        </div>

        <div className='flex min-w-16 items-center justify-end'>
          <div className={`text-xs leading-none tracking-tight ${style}`}>₫</div>
          <div className={`text-sm leading-tight  tracking-tight ${style}`}>
            {formatCurrency(quantity === 0 ? 0 : preAmountTax)}
          </div>
        </div>
      </div>

      {/* Hiển thị dành riêng cho màn thanh toán */}
      {/* Nếu là màn thanh toán bundling (có packageId) thì không hiển thị voucher */}
      {screenUsed === 'payment' && !packageId && (
        <Voucher
          type={IN_ADDON}
          promotionFn={SmeSubscription.getCouponAddon}
          addonId={id}
          isOpenModalVoucher={isOpenModalVoucherAddon}
          couponUsed={[...couponList, ...coupons]}
          totalAmountAfterTaxFinal={totalAmountAfterTaxFinal}
        />
      )}
    </>
  )
}

export default Addon
