import React, { createContext, useContext } from 'react'

interface ColumnContextType {
  contentAlign?: string
}

const ColumnContext = createContext<ColumnContextType>({})

export const ColumnProvider: React.FC<{ contentAlign?: string; children: React.ReactNode }> = ({
  contentAlign,
  children
}) => {
  return <ColumnContext.Provider value={{ contentAlign }}>{children}</ColumnContext.Provider>
}

export const useColumnContext = () => {
  return useContext(ColumnContext)
}
