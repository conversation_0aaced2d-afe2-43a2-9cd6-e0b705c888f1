// components/Column.tsx
import React, { useMemo } from 'react'

import { PlusOutlined } from '@ant-design/icons'

import { useEditor, useNode, type UserComponent } from '@craftjs/core'

import { useSelector } from 'react-redux'

import { DEFAULT_BASE_PROPS, SCREEN_TYPE } from '@/constants/page-builder'

import { defaultResponseColumnProps, IMG_BACKGROUND_ALIGN } from '@/constants/page-builder/row'
import { selectScreenType } from '@/redux-store/slices/builderSlice'
import { mapContentAlignToAlignItems } from '@/utils/page-builder/contentAlignUtils'

import type { ResponsiveBaseCustomizeProps } from '@/types/page-builder'

import type { RowCustomizeProps } from '@/types/page-builder/rowTypes'

import { ColumnSettings } from '@/components/page-builder/selectors/Column/ColumnSettings'
import { ColumnProvider } from './ColumnContext'
import { useForceSyncChildrenTextAlign } from '@/hooks/page-builder/useForceSyncChildrenTextAlign'

// Truyền kiểu cho type ResponsiveBaseCustomizeProps có 3 trường desktop, tablet, mobile giống nhau: TextCustomizeProps
type ColumnComponentProps = ResponsiveBaseCustomizeProps<RowCustomizeProps> & {
  children?: React.ReactNode
}

type ColumnComponent = React.FC<ColumnComponentProps> & UserComponent<ColumnComponentProps>

export const Column: ColumnComponent = ({ children, ...props }) => {
  const screenType = useSelector(selectScreenType)
  const isMobile = screenType === SCREEN_TYPE.MOBILE

  const {
    padding = DEFAULT_BASE_PROPS.padding,
    margin = DEFAULT_BASE_PROPS.margin,
    backgroundColor,
    imgSrc,
    imgSrcMobile,
    externalLink,
    externalLinkMobile,
    imageFit,
    color,
    shadow,
    border,
    align: imageAlign,
    contentAlign = 'start',
    justifyContent = 'center',
    hide,
    backgroundType
  } = props[screenType]

  const {
    connectors: { connect, drag },
    id
  } = useNode()

  const { enabled } = useEditor(editor => ({
    enabled: editor.options.enabled
  }))

  const backgroundStyles = useMemo(() => {
    if (backgroundType === 'IMAGE') {
      const imageUrl = isMobile ? imgSrcMobile || externalLinkMobile : imgSrc || externalLink

      return {
        backgroundImage: `url(${imageUrl})`,
        backgroundPosition: imageAlign ? IMG_BACKGROUND_ALIGN[imageAlign] : 'center',
        backgroundSize: imageFit === 'fit' ? 'contain' : 'cover',
        backgroundRepeat: 'no-repeat',
        backgroundColor: 'transparent'
      }
    }

    if (backgroundType === 'COLOR') {
      return {
        backgroundImage: 'none',
        backgroundColor: backgroundColor || 'transparent',
        backgroundPosition: 'initial',
        backgroundSize: 'initial',
        backgroundRepeat: 'initial'
      }
    }

    // không background
    return {
      backgroundImage: 'none',
      backgroundColor: 'transparent',
      backgroundPosition: 'initial',
      backgroundSize: 'initial',
      backgroundRepeat: 'initial'
    }
  }, [
    backgroundColor,
    backgroundType,
    externalLink,
    externalLinkMobile,
    imageAlign,
    imageFit,
    imgSrc,
    imgSrcMobile,
    isMobile
  ])

  // Force-sync children Text align theo Column khi contentAlign thay đổi
  useForceSyncChildrenTextAlign(id, props[screenType]?.contentAlign, screenType)

  return (
    <div
      ref={(ref: HTMLDivElement | null) => {
        if (ref) {
          connect(drag(ref))
        }
      }}
      style={{
        ...backgroundStyles,
        color: color,
        padding: `${padding?.top}px ${padding?.right}px ${padding?.bottom}px ${padding?.left}px`,
        margin: `${margin?.top}px ${margin?.right}px ${margin?.bottom}px ${margin?.left}px`,
        boxShadow: shadow === 0 ? 'none' : `0px 3px 100px ${shadow}px rgba(0, 0, 0, 0.13)`,
        borderRadius: `${border?.radius}px`,
        border: `${border?.thickness}px ${border?.style} ${border?.color}`,
        display: hide ? 'none' : 'flex',
        flexDirection: 'column',
        alignItems: mapContentAlignToAlignItems(contentAlign || 'start'),
        justifyContent: justifyContent
        // Force text alignment with higher specificity
      }}
    >
      <ColumnProvider contentAlign={contentAlign}>
        {children ||
          (enabled && (
            <div className='col-span-12 flex size-full flex-col items-center justify-center gap-2 overflow-hidden py-[10px]'>
              <PlusOutlined className='text-2xl text-primary' />
              <div className='line-clamp-2 text-center text-primary'>Kéo thả từ thư viện để thêm bố cục</div>
            </div>
          ))}
      </ColumnProvider>
    </div>
  )
}

Column.craft = {
  displayName: 'Column',
  props: defaultResponseColumnProps,
  rules: {
    // canDrag: () => true
  },
  related: {
    toolbar: ColumnSettings
  }
}
