'use client'
import React from 'react'

import type { UserComponent } from '@craftjs/core'
import { useEditor, useNode } from '@craftjs/core'
import type { ContentEditableEvent } from 'react-contenteditable'
import ContentEditable from 'react-contenteditable'

import { useSelector } from 'react-redux'

import classNames from 'classnames'

import { DEFAULT_BASE_PROPS } from '@/constants/page-builder'
import { defaultResponseTextProps } from '@/constants/page-builder/text'
import { selectScreenType } from '@/redux-store/slices/builderSlice'
import type { ResponsiveBaseCustomizeProps, TextCustomizeProps } from '@/types/page-builder'
import { getEffectiveTextAlign } from '@/utils/page-builder/contentAlignUtils'
import { TextSetting } from './TextSettings'
import { IconFromName } from '../../editor/CustomComponents/IconFromName'
import { useColumnContext } from '../Column/ColumnContext'
import { useMigrateLegacyTextAlign } from '@/hooks/page-builder/useMigrateLegacyTextAlign'

// Truyền kiểu cho type ResponsiveBaseCustomizeProps có 3 trường desktop, tablet, mobile giống nhau: TextCustomizeProps
type TextComponent = React.FC<ResponsiveBaseCustomizeProps<TextCustomizeProps>> &
  UserComponent<ResponsiveBaseCustomizeProps<TextCustomizeProps>>

export const Text: TextComponent = props => {
  const screenType = useSelector(selectScreenType)
  const { contentAlign: columnContentAlign } = useColumnContext()

  const {
    text,
    fontSize,
    fontWeight,
    border,
    color,
    textAlign,
    link,
    padding = DEFAULT_BASE_PROPS.padding,
    margin = DEFAULT_BASE_PROPS.margin,
    radius,
    icon,
    iconAlign,
    iconColor,
    iconSize,
    hasIcon,
    width,
    hide,
    className
  } = props[screenType]

  const {
    connectors: { connect, drag },
    actions: { setProp }
  } = useNode()

  const { isEnabled } = useEditor(state => ({
    isEnabled: state.options.enabled
  }))

  // const router = useRouter()

  const handleClick = () => {
    if (link) {
      // Kiểm tra xem có đang trong iframe không
      if (window.parent !== window) {
        // Nếu trong iframe, mở link ở parent window
        window.parent.open(link, '_blank', 'noopener,noreferrer')
      } else {
        // Nếu không trong iframe, mở bình thường
        window.open(link, '_blank', 'noopener,noreferrer')
      }
    }
  }

  const handleChange = (event: ContentEditableEvent) => {
    setProp((prop: ResponsiveBaseCustomizeProps<TextCustomizeProps>) => {
      prop.desktop.text = event.target.value
      prop.tablet.text = event.target.value
      prop.mobile.text = event.target.value
    }, 500)
  }

  // Use column alignment if available, otherwise use text's own alignment
  const effectiveTextAlign = getEffectiveTextAlign(columnContentAlign, textAlign)

  // One-time migration legacy align để đảm bảo thừa kế đúng từ Column
  useMigrateLegacyTextAlign({ isEnabled, textAlign, columnContentAlign, setProp })

  return (
    <div
      className={classNames(
        'flex w-full items-center',
        hasIcon && icon ? 'gap-2' : '',
        hide && '!hidden !w-0',
        `${iconAlign === 'end' && 'flex-row-reverse'}`
      )}
      ref={(ref: HTMLDivElement | null) => {
        if (ref) {
          connect(drag(ref))
        }
      }}
      style={{
        borderRadius: `${radius}px`,
        padding: `${padding.top}px ${padding.right}px ${padding.bottom}px ${padding.left}px`,
        margin: `${margin.top}px 0 ${margin.bottom}px 0`,
        minHeight: 'fit-content',
        width
      }}
    >
      {hasIcon && icon && (
        <IconFromName icon={icon} iconColor={iconColor?.toString()} iconSize={iconSize} className={`shrink-0 `} />
      )}
      <ContentEditable
        disabled={!isEnabled}
        html={text || `Text field`}
        onChange={handleChange}
        // Nếu đang ở màn Editor thì sẽ không kích hoạt sự kiện
        onClick={link && !isEnabled ? handleClick : undefined}
        tagName='p'
        className={className}
        style={{
          fontSize,
          border,
          fontWeight,
          color,
          textAlign: effectiveTextAlign,
          width: '100%',
          height: 'auto',
          minHeight: 'inherit',
          display: 'block',
          lineHeight: '1.5',
          margin: '0',
          wordBreak: 'break-word',
          cursor: link && !isEnabled && 'pointer',
          outline: 'none'
        }}
      />
    </div>
  )
}

Text.craft = {
  displayName: 'Văn bản',
  props: defaultResponseTextProps,
  rules: {
    canDrag: () => true,
    canMoveIn: () => false
  },
  related: {
    toolbar: TextSetting
  }
}
