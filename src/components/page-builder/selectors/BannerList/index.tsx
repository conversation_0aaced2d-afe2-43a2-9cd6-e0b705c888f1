import React from 'react'

import { useRouter } from 'next/navigation'

import { useEditor, useNode } from '@craftjs/core'
import { Carousel, Button as AntButton } from 'antd'
import { useSelector } from 'react-redux'
import type { ContentEditableEvent } from 'react-contenteditable'
import ContentEditable from 'react-contenteditable'
import { styled } from 'styled-components'

import { selectScreenType } from '@/redux-store/slices/builderSlice'
import {
  getImageContainerStylesBanner,
  getImageStylesBannerItem,
  getImageStylesBannerList
} from '@/utils/page-builder/helper'
import { BannerListSettings } from './BannerListSettings'
import { mapTextAlignToFlex, SCREEN_TYPE, defaultResponsiveBannerListProps } from '@/constants/page-builder'
import { IconFromName } from '../../editor/CustomComponents/IconFromName'
import { baseColorLight } from '@/utils/colors'
import { API_ROOT } from '@/models/Base'

export const BannerList = ({ ...props }: any) => {
  const screenType = useSelector(selectScreenType)
  const isMobile = screenType === SCREEN_TYPE.MOBILE

  const {
    connectors: { connect, drag },
    actions: { setProp }
  } = useNode()

  const { isEnabled } = useEditor(state => ({
    isEnabled: state.options.enabled
  }))

  const router = useRouter()

  // const handleRedirectPage = (href: string, event: any) => {
  //   if (isEnabled) return

  //   const parentPath = window.parent.location.href

  //   if (href && event === 1) {
  //     if (href === parentPath) {
  //       window.location.reload()
  //     } else {
  //       Object.assign(document.createElement('a'), {
  //         target: '_blank',
  //         rel: 'noopener noreferrer',
  //         href: href
  //       }).click()
  //     }
  //   }

  //   if (href && event === 0) {
  //     if (href === parentPath) {
  //       window.location.reload()
  //     } else {
  //       router.push(href)
  //     }
  //   }
  // }
  const handleRedirectPage = (href: string, newTab = false) => {
    if (isEnabled) return

    if (newTab) {
      // Open in new tab
      window.open(href, '_blank', 'noopener,noreferrer')
    } else if (href.startsWith(API_ROOT) && !href.startsWith('https://onesme.vn/blog/')) {
      // Internal navigation
      router.replace(href.replaceAll(API_ROOT, ''))
    } else {
      // External navigation with parent context
      router.push(href)
    }
  }

  const { images = [], slidesPerSection = 1, autoPlaySpeed, border } = props[screenType] || {}

  const paddingStyle = (paddingButton: any) => {
    return `${paddingButton?.top}px ${paddingButton?.right}px ${paddingButton?.bottom}px ${paddingButton?.left}px`
  }

  const marginStyle = (marginButton: any) => {
    return `${marginButton?.top}px 0px ${marginButton?.bottom}px 0px`
  }

  const actualSlidesToShow = Math.min(slidesPerSection, images.length)

  const carouselSettings = {
    dots: true,
    infinite: true,
    speed: 500,
    slidesToShow: actualSlidesToShow,
    slidesToScroll: 1,
    adaptiveHeight: false,
    arrows: false,
    draggable: true,
    swipeToSlide: true,
    autoplay: autoPlaySpeed > 0,
    autoplaySpeed: autoPlaySpeed
  }

  const handleSlideClick = (index: number) => {
    if (carouselRef.current) {
      setProp((props: any) => {
        if (!props[screenType]) props[screenType] = {}
        props[screenType].currentSlide = index
      })
    }
  }

  const handleChangeTextTitle = (event: ContentEditableEvent, index: number) => {
    setProp((prop: any) => {
      prop.desktop.images[index].titleText = event.target.value
      prop.tablet.images[index].titleText = event.target.value
      prop.mobile.images[index].titleText = event.target.value
    }, 500)
  }

  const handleChangeTextDescription = (event: ContentEditableEvent, index: number) => {
    setProp((prop: any) => {
      prop.desktop.images[index].descriptionText = event.target.value
      prop.tablet.images[index].descriptionText = event.target.value
      prop.mobile.images[index].descriptionText = event.target.value
    }, 500)
  }

  const carouselRef = React.useRef<any>(null)

  if (!Array.isArray(images) || images.length === 0) {
    return (
      <div
        className='relative flex h-[480px] w-full items-center justify-center bg-gray-100'
        ref={(ref: HTMLDivElement | null) => {
          if (ref) {
            connect(drag(ref))
          }
        }}
      >
        <div className='p-4 text-center'>
          <p>No images available</p>
        </div>
      </div>
    )
  }

  const visibleImages = images.filter(item => item.isShow)

  if (visibleImages.length === 0) {
    return null
  }

  const getImageUrl = (item: any) => {
    if (item?.imageSelectType === 'library') {
      return isMobile ? item?.urlLibraryMobile || item?.urlLibrary : item?.urlLibrary
    }

    return isMobile ? item?.urlMobile || item?.url : item?.url
  }

  return (
    <div
      className='group relative w-full'
      // style={{ maxHeight: isEnabled ? '380px' : '' }}
      ref={(ref: HTMLDivElement | null) => {
        if (ref) {
          connect(drag(ref))
        }
      }}
    >
      <div className='h-full'>
        <CustomCarousel
          ref={carouselRef}
          {...carouselSettings}
          className='h-full'
          key={`${autoPlaySpeed}-${actualSlidesToShow}`}
        >
          {images
            .filter(item => item.isShow[screenType])
            .map((item: any, index: number) => (
              <div
                key={item?.id}
                className={`${isMobile ? '' : 'aspect-[3.79/1]'} ${item?.imgSrc ? '' : 'cursor-pointer'} ${actualSlidesToShow > 1 ? 'p-2' : ''}`}
                onClick={() => {
                  if (!item?.imgSrc?.length) {
                    handleRedirectPage(item?.href)
                  }
                }}
                onMouseDown={e => {
                  if (e.button === 1) {
                    e.preventDefault()
                    handleRedirectPage(item?.href, true)
                  }
                }}
              >
                <div
                  className={`relative flex w-full items-center justify-center ${actualSlidesToShow > 1 ? 'h-[calc(480px-16px)] rounded-lg' : 'h-full'}`}
                  style={{
                    backgroundColor: item?.backgroundColor || '#f0f0f0'
                  }}
                  onClick={() => handleSlideClick(index)}
                >
                  {/* Text Overlay */}
                  <div
                    className={`absolute z-10 flex w-full p-6 text-white ${isMobile ? 'flex-col-reverse' : 'inset-0'}`}
                  >
                    <div
                      className={`flex flex-col justify-center ${
                        isMobile
                          ? 'w-full space-y-2 p-2'
                          : `${item?.imgSrc?.length > 0 ? 'w-1/2' : 'w-full'} space-y-6 p-16`
                      }`}
                    >
                      {item?.titleText && item?.showTitle[screenType] && (
                        <div className='flex' style={{ justifyContent: mapTextAlignToFlex(item.textAlignTitle) }}>
                          <div
                            ref={(ref: HTMLDivElement | null) => {
                              if (ref) {
                                connect(drag(ref))
                              }
                            }}
                          >
                            <ContentEditable
                              disabled={!isEnabled}
                              html={item?.titleText || `Text field`}
                              onChange={(event: ContentEditableEvent) => handleChangeTextTitle(event, index)}
                              tagName='p'
                              style={{
                                fontSize: isMobile ? 24 : item?.fontSizeTitle,
                                border,
                                fontWeight: item?.fontWeightTitle,
                                color: item?.colorTitle,
                                width: '100%',
                                height: 'auto',
                                minHeight: 'inherit',
                                display: 'block',
                                lineHeight: '1.5',
                                margin: '0',
                                outline: 'none'
                              }}
                            />
                          </div>
                        </div>
                      )}
                      {item?.descriptionText && item?.showDescription[screenType] && (
                        <div className='flex' style={{ justifyContent: mapTextAlignToFlex(item.textAlignDescription) }}>
                          <div
                            ref={(ref: HTMLDivElement | null) => {
                              if (ref) {
                                connect(drag(ref))
                              }
                            }}
                          >
                            <ContentEditable
                              disabled={!isEnabled}
                              html={item?.descriptionText || `Text field`}
                              onChange={(event: ContentEditableEvent) => handleChangeTextDescription(event, index)}
                              tagName='p'
                              style={{
                                fontSize: item?.fontSizeDescription,
                                border,
                                fontWeight: item?.fontWeightDescription,
                                color: item?.colorDescription,
                                textAlign: item?.textAlignDescription,
                                width: '100%',
                                height: 'auto',
                                minHeight: 'inherit',
                                display: 'block',
                                lineHeight: '1.5',
                                margin: '0',
                                outline: 'none'
                              }}
                            />
                          </div>
                        </div>
                      )}
                      <div
                        className={`flex w-full ${isMobile ? 'justify-center' : 'justify-start'}`}
                        style={{ justifyContent: mapTextAlignToFlex(item?.contentAlign) }}
                      >
                        {item?.buttonText && item?.showButton[screenType] && (
                          <AntButton
                            type={item?.buttonStyle}
                            size={item?.buttonSize}
                            style={{
                              backgroundColor: item?.buttonStyle === 'primary' ? item?.buttonBackgroundColor : '',
                              color: item?.buttonTextColor,
                              borderRadius: item?.buttonRadius,
                              margin: marginStyle(item?.marginButton),
                              padding: paddingStyle(item?.paddingButton),
                              fontSize: item?.buttonFontSize,
                              fontWeight: item?.buttonFontWeight,
                              borderColor: item?.buttonStyle === 'default' ? item?.buttonBackgroundColor : ''
                            }}
                            className={`${item?.hasIcon && 'gap-2'} flex items-center`}
                            onClick={e => {
                              e.stopPropagation()
                              handleRedirectPage(item?.href)
                            }}
                            onMouseDown={e => {
                              if (e.button === 1) {
                                e.preventDefault()
                                handleRedirectPage(item?.href, true)
                              }
                            }}
                            icon={item?.hasIcon && <IconFromName icon={item?.icon} iconColor={item?.iconColor} />}
                            iconPosition={item?.iconAlign}
                          >
                            {item?.buttonText}
                          </AntButton>
                        )}
                      </div>
                    </div>
                    {item?.imgSrc ? (
                      <div className={`flex w-1/2 items-center ${isMobile ? 'w-full' : ''}`}>
                        <div className=''>
                          <img
                            src={item?.imgSrc}
                            alt={item?.seo || ''}
                            style={getImageStylesBannerList(item?.imageFit)}
                          />
                        </div>
                      </div>
                    ) : null}
                  </div>

                  {/* Banner Image */}
                  {getImageUrl(item) ? (
                    <div
                      className='flex size-full items-center'
                      style={getImageContainerStylesBanner(item?.imageAlign)}
                    >
                      <img
                        src={getImageUrl(item)}
                        alt={item?.seo || 'Carousel slide'}
                        style={{
                          ...getImageStylesBannerItem(item?.imageFit),
                          maxHeight: isEnabled ? '480px' : '',
                          height: isMobile ? '480px' : '100%'
                        }}
                        className={`${actualSlidesToShow > 1 ? 'rounded-lg' : ''}`}
                      />
                    </div>
                  ) : (
                    <div
                      className='absolute inset-0 flex items-center justify-center'
                      style={{
                        backgroundColor: item?.backgroundColor,
                        border: '2px dashed #d9d9d9'
                      }}
                    />
                  )}
                </div>
              </div>
            ))}
        </CustomCarousel>
      </div>
    </div>
  )
}

BannerList.craft = {
  displayName: 'Danh sách banner',
  props: defaultResponsiveBannerListProps,
  rules: {
    canDrag: () => true,
    canMoveIn: () => false
  },
  related: {
    toolbar: BannerListSettings
  }
}

const CustomCarousel = styled(Carousel)`
  .slick-dots > li > button {
    background-color: ${baseColorLight['bright-blue-8']} !important;
  }
`
