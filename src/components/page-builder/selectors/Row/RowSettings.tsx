import React from 'react'

import { useNode } from '@craftjs/core'

import { Checkbox, Collapse } from 'antd'
import { useSelector } from 'react-redux'

import { CustomizeBackgroundType } from '@/components/page-builder/editor/ElementCustomize/CustomizeBackgroundType'
import { selectScreenType } from '@/redux-store/slices/builderSlice'

import { CustomLayoutRow, CustomizeBorder, CustomizeMargin, CustomizePadding } from '../../editor/ElementCustomize'
import { CustomizeColumnGap } from '../../editor/ElementCustomize/CustomizeColumnGap'

export const RowSettings: React.FC = () => {
  /** Hiển thị CustomLayoutRow và CustomizeColumnGap khi Row có nhiều hơn 1 column */
  const {
    values,
    actions: { setProp }
  } = useNode(node => ({
    values: node.data.props
  }))

  const screenType = useSelector(selectScreenType)
  
  // Kiểm tra số lượng columns thực tế thay vì child nodes
  const colWidths = values[screenType]?.colWidths || []
  const hasMultipleColumns = colWidths.length > 1

  return (
    <div>
      <Collapse bordered={false} defaultActiveKey={['1']}>
        {/* Bố cục -  Padding - Margin settings */}
        <Collapse.Panel key='1' header={<span className='text-base font-bold text-primary'>Row</span>}>
          <div className='flex flex-col gap-6'>
            {hasMultipleColumns && <CustomLayoutRow />}
            {hasMultipleColumns && <CustomizeColumnGap />}

            <div>Dãn dòng khi ở màn hình</div>
            <div className='flex items-center gap-4'>
              <Checkbox
                checked={values['tablet']?.isBreakLine}
                onChange={e => {
                  setProp((props: any) => (props['tablet'].isBreakLine = e.target.checked))
                }}
              >
                Tablet
              </Checkbox>
              <Checkbox
                checked={values['mobile']?.isBreakLine}
                onChange={e => {
                  setProp((props: any) => (props['mobile'].isBreakLine = e.target.checked))
                }}
              >
                Mobile
              </Checkbox>
            </div>
            <CustomizePadding />
            <CustomizeMargin />
          </div>
        </Collapse.Panel>

        {/* Background settings */}
        <Collapse.Panel key='2' header={<span className='text-base font-bold text-primary'>Layout background</span>}>
          <CustomizeBackgroundType />
        </Collapse.Panel>

        {/* Border settings */}
        <Collapse.Panel key='3' header={<span className='text-base font-bold text-primary'>Layout border</span>}>
          <CustomizeBorder />
        </Collapse.Panel>
      </Collapse>
    </div>
  )
}
