import {
  CustomizeBorder,
  CustomizeFontSize,
  CustomizeFontWeight,
  CustomizeInput,
  CustomizeLink,
  CustomizeMargin,
  CustomizePadding,
  CustomizeTextAlign,
  CustomizeTextColor
} from '../../editor/ElementCustomize'
import { CustomizeBackgroundType } from '../../editor/ElementCustomize/CustomizeBackgroundType'
import { CustomizeIconLibrary } from '../../editor/ElementCustomize/CustomizeIconLibrary'
import { CustomizeVisibleContent } from '../../editor/ElementCustomize/CustomizeVisibleContent'
import { CustomCollapse } from '../SocialMedia/SocialMediaSettings'

const items = [
  {
    key: '1',
    label: (
      <div className='flex items-center justify-between'>
        <span className='text-base font-bold'>Icon</span>
        <CustomizeVisibleContent propertyName='showIcon' label='' />
      </div>
    ),
    children: (
      <div className='flex flex-col gap-4'>
        <CustomizeIconLibrary />
      </div>
    )
  },
  {
    key: '2',
    label: (
      <div className='flex items-center justify-between'>
        <span className='text-base font-bold'>Title</span>
        <CustomizeVisibleContent propertyName='showTitle' label='' />
      </div>
    ),
    children: (
      <div className='my-4 flex flex-col gap-4'>
        <CustomizeInput propertyName='title' defaultValue='Lorem ipsum dolor....' />
        <CustomizeFontSize propertyName='titleFontSize' />
        <CustomizeFontWeight propertyName='titleFontWeight' />
        <CustomizeTextColor propertyName='titleColor' />
        <CustomizeTextAlign propertyName='titleAlign' />
      </div>
    )
  },
  {
    key: '3',
    label: (
      <div className='flex items-center justify-between'>
        <span className='text-base font-bold'>Description</span>
        <CustomizeVisibleContent propertyName='showDesc' label='' />
      </div>
    ),
    children: (
      <div className='my-4 flex flex-col gap-4'>
        <CustomizeInput propertyName='desc' defaultValue='Lorem ipsum dolor....' />
        <CustomizeFontSize propertyName='descFontSize' />
        <CustomizeFontWeight propertyName='descFontWeight' />
        <CustomizeTextColor propertyName='descColor' />
        <CustomizeTextAlign propertyName='descAlign' />
      </div>
    )
  },
  {
    key: '4',
    label: <span className='text-base font-bold'>Background</span>,
    children: (
      <div className='my-4 flex flex-col gap-4'>
        <CustomizeBackgroundType />
      </div>
    )
  },
  {
    key: '5',
    label: <span className='text-base font-bold'>Border</span>,
    children: (
      <div className='my-4 flex flex-col gap-4'>
        <CustomizeBorder />
      </div>
    )
  }
]

export const IconLibrarySettings = () => {
  return (
    <div>
      <div className='mb-4'>
        <CustomizeLink />
      </div>
      <CustomizePadding propertyName='contentPadding' />
      <CustomizeMargin />
      <CustomCollapse ghost bordered={false} items={items} />
    </div>
  )
}
