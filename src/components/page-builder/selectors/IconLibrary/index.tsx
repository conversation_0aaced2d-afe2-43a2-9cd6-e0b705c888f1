import { useCallback, useMemo } from 'react'

import { useRouter } from 'next/navigation'

import type { UserComponent } from '@craftjs/core'
import { useEditor, useNode } from '@craftjs/core'

import { useSelector } from 'react-redux'

import type { ResponsiveBaseCustomizeProps } from '@/types/page-builder'
import type { IconLibraryCustomizeProps } from '@/types/page-builder/iconLibTypes'
import { selectScreenType } from '@/redux-store/slices/builderSlice'
import { DEFAULT_MARGIN, DEFAULT_PADDING, SCREEN_TYPE } from '@/constants/page-builder'
import { defaultResponseIconLibraryProps } from '@/constants/page-builder/iconLibs'
import { IconLibrarySettings } from './IconLibrarySettings'
import { IconFromName } from '../../editor/CustomComponents/IconFromName'
import { IMG_BACKGROUND_ALIGN } from '@/constants/page-builder/row'
import { API_ROOT } from '@/models/Base'

type IconLibraryComponent = React.FC<ResponsiveBaseCustomizeProps<IconLibraryCustomizeProps>> &
  UserComponent<ResponsiveBaseCustomizeProps<IconLibraryCustomizeProps>>

export const IconLibrary: IconLibraryComponent = props => {
  const screenType = useSelector(selectScreenType)

  const isMobile = screenType === SCREEN_TYPE.MOBILE

  const route = useRouter()

  const {
    connectors: { connect, drag }
  } = useNode()

  const { enabled } = useEditor(state => ({
    enabled: state.options.enabled // Trạng thái cho biết liệu editor có được bật hay không
  }))

  const {
    padding = DEFAULT_PADDING,
    margin = DEFAULT_MARGIN,
    paddingIcon,
    contentPadding,
    icon,
    iconColor,
    iconAlign,
    size,
    borderIcon,
    bgIconColor,
    title,
    titleFontSize,
    titleFontWeight,
    titleColor,
    titleAlign,
    titleLineHeight,
    desc,
    descFontSize,
    descFontWeight,
    descColor,
    descAlign,
    descLineHeight,
    backgroundColor,
    border,
    showIcon,
    showTitle,
    showDesc,
    width,
    height,
    link,
    imgSrc,
    imgSrcMobile,
    externalLink,
    externalLinkMobile,
    imageFit,
    align,
    backgroundType
  } = props[screenType]

  const backgroundStyles = useMemo(() => {
    if (backgroundType === 'IMAGE') {
      const imageUrl = isMobile ? imgSrcMobile || externalLinkMobile : imgSrc || externalLink

      return {
        backgroundImage: `url(${imageUrl})`,
        backgroundPosition: align ? IMG_BACKGROUND_ALIGN[align] : 'center',
        backgroundSize: imageFit === 'fit' ? 'contain' : 'cover',
        backgroundRepeat: 'no-repeat',
        backgroundColor: 'transparent'
      }
    }

    if (backgroundType === 'COLOR') {
      return {
        backgroundImage: 'none',
        backgroundColor: backgroundColor || 'transparent',
        backgroundPosition: 'initial',
        backgroundSize: 'initial',
        backgroundRepeat: 'initial'
      }
    }

    // không background
    return {
      backgroundImage: 'none',
      backgroundColor: 'transparent',
      backgroundPosition: 'initial',
      backgroundSize: 'initial',
      backgroundRepeat: 'initial'
    }
  }, [
    backgroundColor,
    backgroundType,
    externalLink,
    externalLinkMobile,
    align,
    imageFit,
    imgSrc,
    imgSrcMobile,
    isMobile
  ])

  const paddingStyle = (padding: any) => `${padding?.top}px ${padding?.right}px ${padding?.bottom}px ${padding?.left}px`
  const marginStyle = `${margin?.top}px ${margin?.right}px ${margin?.bottom}px ${margin?.left}px`

  const contentStyles = {
    ...backgroundStyles,
    borderWidth: border?.thickness,
    borderColor: border?.color,
    borderStyle: border?.style,
    borderRadius: `${border?.radius}px`,
    padding: paddingStyle(contentPadding),
    width,
    height
  }

  const contentClassName = `flex ${isMobile && 'flex-col items-center justify-center gap-1'} ${!showTitle?.[screenType] && !showDesc?.[screenType] ? '' : 'items-center gap-x-4'} ${iconAlign === 'end' && 'flex-row-reverse'}`

  const renderContent = () => (
    <>
      {showIcon?.[screenType] && (
        <div
          className='flex items-center justify-center'
          style={{
            backgroundColor: bgIconColor,
            borderWidth: borderIcon?.thickness,
            borderColor: borderIcon?.color,
            borderStyle: borderIcon?.style,
            borderRadius: `${borderIcon?.radius}px`,
            padding: paddingStyle(paddingIcon)
          }}
        >
          <IconFromName icon={icon} iconColor={iconColor} iconSize={size} />
        </div>
      )}
      <div className={`${isMobile && 'flex flex-col items-center'}`}>
        {showTitle?.[screenType] && (
          <div
            style={{
              fontSize: titleFontSize,
              color: titleColor,
              textAlign: titleAlign,
              fontWeight: titleFontWeight,
              lineHeight: titleLineHeight
            }}
          >
            {title || ''}
          </div>
        )}
        {showDesc?.[screenType] && (
          <div
            style={{
              fontSize: descFontSize,
              color: descColor,
              textAlign: descAlign,
              fontWeight: descFontWeight,
              lineHeight: descLineHeight
            }}
          >
            {desc || ''}
          </div>
        )}
      </div>
    </>
  )

  const containerProps = {
    ref: (ref: HTMLDivElement | null) => {
      if (ref) {
        connect(drag(ref))
      }
    },
    style: {
      padding: paddingStyle(padding),
      margin: marginStyle,
      width,
      overflow: 'hidden'
    }
  }

  // Xử lý click link trong icon
  const handleIconClick = useCallback(() => {
    if (enabled || !link) return

    if (link.startsWith(API_ROOT!) && !link.startsWith('https://onesme.vn/blog/')) {
      route.push(link.replaceAll(API_ROOT!, ''))
    } else {
      handleExternalLink(link)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [enabled, link, route])

  // Xử lý external link
  const handleExternalLink = useCallback((url: string) => {
    // Kiểm tra xem có đang trong iframe không
    if (window.parent !== window) {
      // Nếu trong iframe, mở link ở parent window
      window.parent.open(url, '_blank', 'noopener,noreferrer')
    } else {
      // Nếu không trong iframe, mở bình thường
      window.open(url, '_blank', 'noopener,noreferrer')
    }
  }, [])

  return (
    <div {...containerProps}>
      <div onClick={handleIconClick} className={contentClassName} style={contentStyles}>
        {renderContent()}
      </div>
    </div>
  )
}

IconLibrary.craft = {
  displayName: 'Icon',
  props: { ...defaultResponseIconLibraryProps },
  rules: {
    canDrag: () => true,
    canMoveIn: () => true
  },
  related: {
    toolbar: IconLibrarySettings
  }
}
