import { FooterIconsLink } from '@/components/layout/vertical/FooterContent'
import { PreventNavigation } from '@/utils/page-builder/PreventNavigation'
import { EnterprisePageBuilder } from '@/views/page-builder/editor/pages/EnterprisePageBuilder'
import { HomePageBuilder } from '@/views/page-builder/editor/pages/HomePageBuilder'
import { HouseholdPageBuilder } from '@/views/page-builder/editor/pages/HouseholdPageBuilder'
import { PersonalPageBuilder } from '@/views/page-builder/editor/pages/PersonalPageBuilder'
import { HomeAffiliateIoTPageBuilder } from '@/views/page-builder/editor/pages/templates/HomeAffiliateIoTPageBuilder'
import { HomeAffiliateOneSMEPageBuilder } from '@/views/page-builder/editor/pages/templates/HomeAffiliateOneSMEPageBuilder'
import { HomeAffiliateGenzPageBuilder } from '@/views/page-builder/editor/pages/templates/HomeAffiliateGenzPageBuilder'
import { HomeAffiliateTeenPageBuilder } from '@/views/page-builder/editor/pages/templates/HomeAffiliateTeenPageBuilder'

import {
  ContactCustomerBuilder,
  ContactFormEnterpriseBuilder,
  ContactFormSection,
  NavigationServiceBuilder
} from '@/views/page-builder/editor/sections/enterprise'
import {
  ListPopularProductBuilder,
  ListSimNumberBuilder,
  ListSimPackageBuilder,
  NavigationServiceBuilder as NavigationPersonalBuilder,
  PopularInternetTVProductsBuilder,
  SimPackageBanner,
  BannerCategoryBuilder
} from '@/views/page-builder/editor/sections/personal'

import {
  AffiliateBuilder,
  BannerBottomBuilder,
  DigitalSolutionBuilder,
  MobileReasonsBuilder,
  NewsBuilder,
  PartnerBuilder,
  SupplyPartnerBuilder,
  WhyChooseUsBuilder
} from '@/views/page-builder/editor/sections/home'

import {
  HeaderBuilder as IoTHeaderBuilder,
  HeroBuilder as IoTHeroBuilder,
  ExpertIntroBuilder,
  WhyChooseUsBuilder as IoTWhyChooseUsBuilder,
  BestSellingProductsBuilder,
  ClientsPartnersBuilder,
  TestimonialsBuilder,
  FamilySolutionsBuilder,
  EnterpriseSolutionsBuilder,
  IoTSimDataBuilder,
  FooterBuilder as IoTFooterBuilder
} from '@/views/page-builder/editor/sections/templates/home-affiliate-iot'
import {
  OneSMEHeaderBuilder,
  BannerCarouselBuilder,
  ExpertIntroBuilder as OneSMEExpertIntroBuilder,
  FlashSaleBuilder,
  PromotionsBuilder,
  BestSellingProductsBuilder as OneSMEBestSellingProductsBuilder,
  ProductCategoriesBuilder,
  WhyChooseMeBuilder,
  OneSMEFooterBuilder
} from '@/views/page-builder/editor/sections/templates/home-affiliate-onesme'
import {
  HeaderGenzBuilder,
  HeroGenzBuilder,
  IntroGenzBuilder,
  MobileDealsGenzBuilder,
  InternetGamingGenzBuilder,
  WhyChooseGenzBuilder,
  FooterGenzBuilder
} from '@/views/page-builder/editor/sections/templates/home-affiliate-genz'
import {
  HeaderTeenBuilder,
  BannerCarouselTeenBuilder,
  IntroExpertTeenBuilder,
  MonthlyPromotionsTeenBuilder,
  BestSellingProductsTeenBuilder,
  ServicesGridTeenBuilder,
  FooterTeenBuilder,
  FlashSaleTeenBuilder,
  ProductCatalogTeenBuilder,
  WhyChooseMeTeenBuilder
} from '@/views/page-builder/editor/sections/templates/home-affiliate-teen'
import {
  NavigationServiceBuilder as HouseHoldNavigationServiceBuilder,
  SoftwareProductsBuilder,
  TelecomProductsBuilder,
  ContactFormHouseHoldBuilder
} from '@/views/page-builder/editor/sections/house-hold'

import Schemas from '@/utils/SEO/Schemas'

import { FooterBuilder } from '../editor/CustomComponents/FooterBuilder'

import { Header } from '../editor/CustomComponents/HeaderBuilder'
import { BannerList } from './BannerList'
import { Button } from './Button'
import { Column } from './Column'
import { Container } from './Container'
import { DropdownMenu } from './DropdownMenu'
import { Filter } from './Filter'
import { IconLibrary } from './IconLibrary'
import { Image } from './Image'
import { ImageList } from './ImageList'
import { InformationCard } from './InformationCard'
import { InputForm } from './InputForm'
import { MenuBar } from './MenuBar'
import { ProductGrid } from './ProductGrid'
import { ProductServiceCategory } from './ProductServiceCategory'
import { ReasonForChoosingBuilder } from './ReasonForChoosingBuilder'
import { Row } from './Row'
import { SocialMedia } from './SocialMedia'
import { Spacer } from './Spacer'
import { Survey } from './Survey'
import { Text } from './Text'
import { Video } from './Video'
import { ListProducts } from './ListProducts'

import EnterpriseListCategoryProduct from '@/views/enterprise/category-product/ListCategoryProduct'
import ContactCustomer from '@/views/enterprise/contact-customer/ContactCustomer'
import EnterpriseNavigationService from '@/views/enterprise/navigation-service/NavigationService'
import EnterpriseSellingProducts from '@/views/enterprise/selling-products/SellingProducts'
import News from '@/views/sme-portal/sections/News'
import ServiceProvider from '@/views/sme-portal/sections/ServiceProvider'
import Partner from '@/views/sme-portal/sections/Partner'

import { BestSeller, Hardware, Software, Support, Telecom } from '@/views/house-hold'

import ComboSection from '@/components/combo/ComboSection'
import SolutionConsulting from '@/views/enterprise/solution-consulting/SolutionConsulting'
import AffiliatePartner from '@/views/sme-portal/sections/AffiliatePartner'
import ListCategoryProduct from '@/views/personal/category-product/ListCategoryProduct'
import SellingProducts from '@/views/personal/selling-products/SellingProducts'
import QuickBuy from '@/views/sme-portal/sections/QuickBuy'
import NavigationService from '@views/personal/navigation-service/NavigationService'
import FooterBuilderMobile from '../editor/CustomComponents/FooterBuilder/FooterBuilderMobile'
import { CategoryBanner } from '@/views/page-builder/editor/sections/house-hold/product-category/CategoryBanner'
import { CategoryContent } from '@/views/page-builder/editor/sections/house-hold/product-category/CategoryContent'
import { DeviceProductsBuilder } from '@/views/page-builder/editor/sections/house-hold/DeviceProductsBuilder'
import { ContactButtonLink } from '../editor/CustomComponents'
import ContactForm from '@/components/contact/ContactForm'
import { ContactFormBuilder } from './ContactForm'
import { BlankPageBuilder } from '@/views/page-builder/editor/pages/BlankPageBuilder'
import {
  IconText,
  ProductCard,
  FlexContainer,
  RatingText,
  SectionTitle,
  BannerSection,
  IconLink,
  CartoonCard
} from '../editor/CustomComponents/IoTTemplate'
import { HomeAffiliateHouseholdPageBuilder } from '@/views/page-builder/editor/pages/templates/HomeAffiliateHouseholdPageBuilder'
import {
  BestSellingProductsHouseholdBuilder,
  ExpertIntroHouseholdBuilder,
  PromotionsHouseholdBuilder,
  AllSolutionsHouseholdBuilder,
  WhyChooseMeHouseholdBuilder,
  HeaderBuilderHousehold,
  InternetAndTVSection,
  FamilySecuritySection,
  FamilyServiceSection,
  SmartDevicesSection
} from '@/views/page-builder/editor/sections/templates/home-affiliate-household'
import { BannerListIot } from '../../../views/page-builder/editor/sections/templates/home-affiliate-household/BannerListHousehold'
import { FlashSaleHouseholdBuilder } from '@/views/page-builder/editor/sections/templates/home-affiliate-household/FlashSaleBuilder'

export const resolver = {
  // region Selector
  Container,
  Text,
  Button,
  Image,
  MenuBar,
  SocialMedia,
  Row,
  Spacer,
  DropdownMenu,
  InputForm,
  ProductGrid,
  Column,
  Video,
  Filter,
  ImageList,
  Survey,
  InformationCard,
  ReasonForChoosingBuilder,
  BannerList,
  ProductServiceCategory,
  ContactFormBuilder,
  IconLibrary,
  Header,
  FooterBuilder,
  ListProducts,
  BannerListIot,
  // endregion

  // region Pages
  HomePageBuilder,
  HomeAffiliateIoTPageBuilder,
  HomeAffiliateOneSMEPageBuilder,
  HomeAffiliateHouseholdPageBuilder,
  HomeAffiliateGenzPageBuilder,
  HomeAffiliateTeenPageBuilder,
  EnterprisePageBuilder,
  HouseholdPageBuilder,
  PersonalPageBuilder,
  BlankPageBuilder,
  // endregion

  // region Section
  DigitalSolutionBuilder,
  NewsBuilder,
  AffiliateBuilder,
  SupplyPartnerBuilder,
  PartnerBuilder,
  BannerBottomBuilder,
  WhyChooseUsBuilder,

  // IoT Affiliate Template Components
  IoTHeaderBuilder,
  IoTHeroBuilder,
  ExpertIntroBuilder,
  IoTWhyChooseUsBuilder,
  BestSellingProductsBuilder,
  ClientsPartnersBuilder,
  TestimonialsBuilder,
  FamilySolutionsBuilder,
  EnterpriseSolutionsBuilder,
  IoTSimDataBuilder,
  IoTFooterBuilder,

  // OneSME Affiliate Template Components
  OneSMEHeaderBuilder,
  BannerCarouselBuilder,
  OneSMEExpertIntroBuilder,
  FlashSaleBuilder,
  PromotionsBuilder,
  OneSMEBestSellingProductsBuilder,
  ProductCategoriesBuilder,
  WhyChooseMeBuilder,
  OneSMEFooterBuilder,

  // Household Affiliate Template Components
  HeaderBuilderHousehold,
  ExpertIntroHouseholdBuilder,
  FlashSaleHouseholdBuilder,
  PromotionsHouseholdBuilder,
  BestSellingProductsHouseholdBuilder,
  AllSolutionsHouseholdBuilder,
  WhyChooseMeHouseholdBuilder,
  InternetAndTVSection,
  FamilySecuritySection,
  FamilyServiceSection,
  SmartDevicesSection,
  // GenZ Affiliate Template Components
  HeaderGenzBuilder,
  HeroGenzBuilder,
  IntroGenzBuilder,
  MobileDealsGenzBuilder,
  InternetGamingGenzBuilder,
  WhyChooseGenzBuilder,
  FooterGenzBuilder,

  // Teen Affiliate Template Components
  HeaderTeenBuilder,
  BannerCarouselTeenBuilder,
  IntroExpertTeenBuilder,
  FlashSaleTeenBuilder,
  MonthlyPromotionsTeenBuilder,
  BestSellingProductsTeenBuilder,
  ServicesGridTeenBuilder,
  ProductCatalogTeenBuilder,
  WhyChooseMeTeenBuilder,
  FooterTeenBuilder,

  NavigationServiceBuilder,
  FooterIconsLink,
  ContactFormEnterpriseBuilder,
  ContactFormSection,
  ContactCustomerBuilder,
  NavigationPersonalBuilder,
  MobileReasonsBuilder,
  ListSimNumberBuilder,
  ListSimPackageBuilder,
  ListPopularProductBuilder,
  PopularInternetTVProductsBuilder,
  SimPackageBanner,
  FooterBuilderMobile,
  ContactButtonLink,
  ContactForm,

  // IoT Template Components
  IconText,
  IconLink,
  ProductCard,
  FlexContainer,
  RatingText,
  SectionTitle,
  BannerSection,
  CartoonCard,

  // region Section fix cứng - bỏ dần khi chuyển thành page builder
  // Enterprise
  EnterpriseNavigationService,
  EnterpriseSellingProducts,
  EnterpriseListCategoryProduct,
  Partner,
  ServiceProvider,
  News,
  ContactCustomer,

  //house-hold
  HouseHoldNavigationServiceBuilder,
  BestSeller,
  Telecom,
  QuickBuy,
  Software,
  Support,
  Hardware,
  ComboSection,
  AffiliatePartner,
  SoftwareProductsBuilder,
  DeviceProductsBuilder,
  TelecomProductsBuilder,
  CategoryBanner,
  CategoryContent,
  ContactFormHouseHoldBuilder,

  // personal
  NavigationService,
  SellingProducts,
  ListCategoryProduct,
  SolutionConsulting,
  BannerCategoryBuilder,

  //utils
  PreventNavigation,
  Schemas
}
