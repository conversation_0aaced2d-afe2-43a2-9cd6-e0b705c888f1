'use client'
import React from 'react'

import { useRouter } from 'next/navigation'

import type { UserComponent } from '@craftjs/core'
import { useEditor, useNode } from '@craftjs/core'

import classNames from 'classnames'

import { useSelector } from 'react-redux'

import type { SocialMediaCustomizeProps } from '@/types/page-builder/socialMediaTypes'
import { defaultResponseSocialMediaProps } from '@/constants/page-builder/socialMedia'
import { SocialMediaSettings } from './SocialMediaSettings'
import { selectScreenType } from '@/redux-store/slices/builderSlice'
import type { ResponsiveBaseCustomizeProps } from '@/types/page-builder'
import { IconFromName } from '../../editor/CustomComponents/IconFromName'
import { UPLOAD_TYPE } from '@/constants/custom-field/modalCreateVariant'

type SocialMediaComponent = React.FC<ResponsiveBaseCustomizeProps<SocialMediaCustomizeProps>> &
  UserComponent<ResponsiveBaseCustomizeProps<SocialMediaCustomizeProps>>

export const SocialMedia: SocialMediaComponent = props => {
  const screenType = useSelector(selectScreenType)

  const { size, padding, margin, iconAlign, socialList } = props[screenType]

  const {
    connectors: { connect, drag }
  } = useNode()

  const { isEnabled } = useEditor(state => ({
    isEnabled: state.options.enabled
  }))

  const paddingStyle = `${padding?.top}px ${padding?.right}px ${padding?.bottom}px ${padding?.left}px`
  const marginStyle = `${margin?.top}px ${margin?.right}px ${margin?.bottom}px ${margin?.left}px`

  const router = useRouter()

  const handleRedirectPage = (link: string) => {
    if (isEnabled) return

    if (link) {
      router.push(link)
    }
  }

  return (
    <>
      <div
        style={{
          margin: marginStyle,
          padding: paddingStyle
        }}
        ref={(ref: HTMLDivElement | null) => {
          if (ref) {
            connect(drag(ref))
          }
        }}
        className={classNames('flex gap-6')}
      >
        {socialList?.map((social, index) => {
          const { icon, name, url, displayType, iconColor, textColor } = social

          return (
            <div
              key={index}
              className={`flex cursor-pointer items-center gap-2 ${iconAlign === 'end' && 'flex-row-reverse'}`}
              style={{ fontSize: size, color: textColor }}
              onClick={e => {
                e.stopPropagation()
                handleRedirectPage(url ?? '')
              }}
            >
              <IconFromName icon={icon} iconColor={iconColor?.toString()} iconSize={size} />
              {/* <i className={`${icon} size-${formatSize(size ?? 16)}`} style={{ color: iconColor?.toString() }} /> */}
              {displayType === 'NAME' && name}
              {displayType === UPLOAD_TYPE.URL && url}
            </div>
          )
        })}
      </div>
    </>
  )
}

SocialMedia.craft = {
  displayName: 'Mạng xã hội',
  props: defaultResponseSocialMediaProps,
  rules: {
    canDrag: () => true,
    canMoveIn: () => false
  },
  related: {
    toolbar: SocialMediaSettings
  }
}
