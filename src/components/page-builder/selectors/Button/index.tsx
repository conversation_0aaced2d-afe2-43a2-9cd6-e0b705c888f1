'use client'
import React, { useState } from 'react'

import { useRouter } from 'next/navigation'

import Cookies from 'js-cookie'

import type { UserComponent } from '@craftjs/core'
import { useEditor, useNode } from '@craftjs/core'
import { Button as AntButton } from 'antd'

import { useSelector } from 'react-redux'

import { useUser } from '@/hooks'

import { API_ROOT, TOKEN_PARAM } from '@/models/Base'

import { defaultResponseButtonProps, RESET_MARGIN, RESET_PADDING } from '@/constants/page-builder'
import type { ButtonCustomizeProps, ResponsiveBaseCustomizeProps } from '@/types/page-builder'
import { ButtonSettings } from './ButtonSettings'
import { selectScreenType } from '@/redux-store/slices/builderSlice'
import WorkPlaceModal from '../ButtonWorkspace/WorkPlaceModal'
import { IconFromName } from '../../editor/CustomComponents/IconFromName'

type ButtonComponent = React.FC<ResponsiveBaseCustomizeProps<ButtonCustomizeProps>> &
  UserComponent<ResponsiveBaseCustomizeProps<ButtonCustomizeProps>>

export const Button: ButtonComponent = props => {
  const { user } = useUser()

  const screenType = useSelector(selectScreenType)

  const {
    text,
    size,
    buttonType,
    border,
    radius,
    color,
    padding = RESET_PADDING,
    margin = RESET_MARGIN,
    href,
    fontSize,
    fontWeight,
    buttonBackgroundColor,
    hasIcon,
    icon,
    iconAlign,
    iconColor,
    isButtonWorkplace,
    width,
    hide,
    className,
    boxShadow,
    containerClassName
  } = props[screenType]

  const {
    connectors: { connect, drag }
  } = useNode()

  const { isEnabled } = useEditor(state => ({
    isEnabled: state.options.enabled
  }))

  const paddingStyle = `${padding?.top}px ${padding?.right}px ${padding?.bottom}px ${padding?.left}px`
  const marginStyle = `${margin?.top}px ${margin?.right}px ${margin?.bottom}px ${margin?.left}px`

  const [visibleWorkPlaceModal, setVisibleWorkPlaceModal] = useState(false)
  const router = useRouter()

  const handleRedirectPage = (href: string) => {
    if (isEnabled) return

    router.push(href)
  }

  return (
    <div
      style={{ width }}
      className={containerClassName || ''}
      ref={(ref: HTMLDivElement | null) => {
        if (ref) {
          connect(drag(ref))
        }
      }}
    >
      <AntButton
        type={buttonType as any}
        size={size}
        href={href && !isEnabled ? href : undefined}
        style={{
          border,
          background: buttonType === 'primary' ? buttonBackgroundColor : '',
          color: color,
          borderRadius: radius,
          margin: marginStyle,
          padding: paddingStyle,
          fontSize,
          fontWeight,
          width,
          display: hide ? 'none' : 'flex',
          borderColor: buttonType === 'default' ? buttonBackgroundColor : '',
          boxShadow: boxShadow || 'none'
        }}
        className={`${hasIcon && 'gap-2'} ${className}`}
        onClick={e => {
          if (isEnabled) return
          e.stopPropagation()

          if (isButtonWorkplace) {
            setVisibleWorkPlaceModal(true)
          } else if (href) {
            handleRedirectPage(href)
          }
        }}
        icon={hasIcon && <IconFromName icon={icon} iconColor={iconColor} iconSize={20} />}
        iconPosition={iconAlign}
      >
        {text}
      </AntButton>
      {visibleWorkPlaceModal && (
        <WorkPlaceModal
          showModal={visibleWorkPlaceModal}
          setShowModal={setVisibleWorkPlaceModal}
          onSubmit={() => {
            let url
            const accessCookie = Cookies.get(TOKEN_PARAM)
            const accessInfo = accessCookie && JSON.parse(Cookies.get(TOKEN_PARAM) as string)

            if (API_ROOT!.includes('https://stagingonedx.vnpt-technology.vn:6443')) {
              url =
                accessInfo && user.isLoggedWP
                  ? `https://workplace-stagingonedx.vnpt-technology.vn:6443/sso/onesme?oneSmeToken=${accessInfo.access_token}&oneSmeUUID=${user.smeUUID}`
                  : `https://workplace-stagingonedx.vnpt-technology.vn:6443/login/workplace-directing`
            } else if (API_ROOT!.includes('https://staging.onesme.vn')) {
              url =
                accessInfo && user.isLoggedWP
                  ? `https://workplace-staging.onesme.vn/sso/onesme?oneSmeToken=${accessInfo.access_token}&oneSmeUUID=${user.smeUUID}`
                  : 'https://workplace-staging.onesme.vn/login/workplace-directing'
            } else {
              url =
                accessInfo && user.isLoggedWP
                  ? `https://workplace.onesme.vn/sso/onesme?oneSmeToken=${accessInfo.access_token}&oneSmeUUID=${user.smeUUID}`
                  : 'https://workplace.onesme.vn/login/workplace-directing'
            }

            window.open(url)
          }}
        />
      )}
    </div>
  )
}

Button.craft = {
  displayName: 'Button',
  props: defaultResponseButtonProps,
  rules: {
    canDrag: () => true,
    canMoveIn: () => false
  },
  related: {
    toolbar: ButtonSettings
  }
}
