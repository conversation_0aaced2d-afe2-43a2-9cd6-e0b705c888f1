'use client'
import React from 'react'

import type { UserComponent } from '@craftjs/core'
import { useEditor, useNode } from '@craftjs/core'

import { useSelector } from 'react-redux'

import { defaultResponseImageProps, RESET_MARGIN, RESET_PADDING, SCREEN_TYPE } from '@/constants/page-builder'
import type { ImageCustomizeProps, ResponsiveBaseCustomizeProps } from '@/types/page-builder'
import { ImageSettings } from './ImageSettings'
import { selectScreenType } from '@/redux-store/slices/builderSlice'

type ImageComponent = React.FC<ResponsiveBaseCustomizeProps<ImageCustomizeProps>> &
  UserComponent<ResponsiveBaseCustomizeProps<ImageCustomizeProps>>

export const Image: ImageComponent = props => {
  const screenType = useSelector(selectScreenType)
  const isMobile = screenType === SCREEN_TYPE.MOBILE

  const {
    margin = RESET_MARGIN,
    padding = RESET_PADDING,
    imgSrc,
    alt,
    align,
    border,
    externalLink,
    imgSrcMobile,
    externalLinkMobile,
    // typeUpload,
    width,
    height,
    hide,
    link,
    imgSrcFallBack
  } = props[screenType]

  // Đảm bảo imageFit được áp dụng đúng cho từng screen type
  const currentImageFit = props[screenType]?.imageFit || 'fit'

  const {
    id,
    connectors: { connect, drag }
  } = useNode()

  const { isActive } = useEditor((_, query) => ({
    isActive: query.getEvent('selected').contains(id)
  }))

  const paddingStyle = `${padding?.top}px ${padding?.right}px ${padding?.bottom}px ${padding?.left}px`
  const marginStyle = `${margin?.top}px ${margin?.right}px ${margin?.bottom}px ${margin?.left}px`

  // const router = useRouter()

  const handleClick = () => {
    // Nếu không ở màn editor thì mới được redirect
    if (link && !isActive) {
      // Kiểm tra xem có đang trong iframe không
      if (window.parent !== window) {
        // Nếu trong iframe, mở link ở parent window
        window.parent.open(link, '_blank', 'noopener,noreferrer')
      } else {
        // Nếu không trong iframe, mở bình thường
        window.open(link, '_blank', 'noopener,noreferrer')
      }
    }
  }

  // Tính toán kích thước container dựa trên imageFit
  let containerStyle: React.CSSProperties =
    currentImageFit === 'fill'
      ? {
          height: height || (isMobile ? '200px' : '300px'),
          width: '100%',
          minHeight: height || (isMobile ? '200px' : '300px')
        }
      : {
          height: 'auto',
          width: '100%',
          maxWidth: '100%'
        }

  // Tính toán style cho img (sẽ điều chỉnh tiếp dựa vào isFallback)
  let imgStyle: React.CSSProperties =
    currentImageFit === 'fill'
      ? {
          width: '100%',
          height: '100%',
          objectFit: 'cover' as const
        }
      : {
          width: width === 'auto' ? 'auto' : width || 'auto',
          height: height === 'auto' ? 'auto' : height || 'auto',
          maxWidth: '100%',
          objectFit: 'contain' as const
        }

  // Xác định src ảnh theo screen, ưu tiên URL đã chọn với fallback
  const resolvedSrc = (isMobile ? imgSrcMobile || externalLinkMobile || imgSrc || externalLink : imgSrc || externalLink)?.trim() || ''

  const isFallback = !resolvedSrc

  // Đồng bộ fallback cũ -> mới
  const normalizedFallback =
    imgSrcFallBack && imgSrcFallBack.includes('noImage.svg') ? '/assets/images/noImage.svg' : imgSrcFallBack

  // Luôn có ảnh để hiển thị: ưu tiên resolvedSrc, sau đó fallback, cuối cùng là placeholder mặc định
  const finalSrc = (resolvedSrc || normalizedFallback || '/assets/images/noImage.svg').trim()

  // Khi là fallback: hiển thị ảnh dạng contain để thấy rõ placeholder
  if (isFallback) {
    imgStyle = {
      ...imgStyle,
      width: '100%',
      height: '100%',
      objectFit: 'cover' as const
    }

    // Với chế độ 'fit', đảm bảo có chiều cao tối thiểu để hiển thị placeholder
    if (currentImageFit !== 'fill') {
      containerStyle = {
        ...containerStyle,
        minHeight: isMobile ? '180px' : '240px',
        alignItems: 'center'
      }
    }
  }

  // Render thẻ img
  return (
    <div
      className={`justify-${align} flex ${currentImageFit === 'fill' ? 'size-full' : ''}`}
      style={{
        margin: marginStyle,
        padding: paddingStyle,
        display: hide ? 'none' : 'flex',
        ...containerStyle,
        // Background nhẹ giúp empty-state rõ ràng hơn khi dùng fallback
        backgroundColor: isFallback ? '#F2F4F7' : (containerStyle as any).backgroundColor
      }}
      ref={(ref: HTMLDivElement | null) => {
        if (ref) {
          connect(drag(ref))
        }
      }}
    >
      <img
        src={finalSrc}
        alt={alt}
        style={{
          borderWidth: border?.thickness ? `${border.thickness}px` : '0px',
          borderColor: border?.color || 'transparent',
          borderStyle: border?.style || 'none',
          borderRadius: `${border?.radius || 0}px`,
          cursor: link ? 'pointer' : 'default',
          ...imgStyle
        }}
        onClick={handleClick}
        onError={e => {
          const target = e.currentTarget

          if (target.src.endsWith('/assets/images/noImage.svg')) {
            return
          }

          target.src = '/assets/images/noImage.svg'
        }}
      />
    </div>
  )
}

Image.craft = {
  displayName: 'Ảnh',
  props: defaultResponseImageProps,
  rules: {
    canDrag: () => true,
    canMoveIn: () => false
  },
  related: {
    toolbar: ImageSettings
  }
}
