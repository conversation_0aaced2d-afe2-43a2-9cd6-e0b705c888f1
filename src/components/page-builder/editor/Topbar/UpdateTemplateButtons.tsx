import { useState } from 'react'

import { Button, Form, Input, Modal } from 'antd'

import axios from 'axios'

import { message } from '@/components/notification'

interface Props {
  query: any
  pageId: string | string[] | undefined
}

export default function UpdateTemplateButtons({ query, pageId }: Props) {
  const [form] = Form.useForm()
  const [openModalLogin, setOpenModalLogin] = useState(false)
  const [loading, setLoading] = useState(false)
  const [stagingToken, setStagingToken] = useState<string | null>(null)
  const [internalStagingToken, setInternalStagingToken] = useState<string | null>(null)

  // Function to update template file only
  const updateTemplateFileOnly = async () => {
    try {
      const currentPageId = typeof pageId === 'string' ? pageId : pageId?.[0]
      const currentContent = query.serialize()

      await axios.post('/page-builder/update-template', {
        templateName: currentPageId,
        content: currentContent
      })

      message.success(`Cập nhật file JSON template ${currentPageId} thành công!`)
    } catch (error) {
      console.error('Error updating template file:', error)
      message.error('Cập nhật file JSON template thất bại!')
    }
  }

  // Danh sách template names từ PageEditor
  const TEMPLATE_NAMES = [
    'home',
    'enterprise',
    'house-hold',
    'personal',
    'home-affiliate-iot',
    'home-affiliate-onesme',
    'home-affiliate-household',
    'home-affiliate-genz',
    'home-affiliate-teen',
    'create'
  ]

  // Phân biệt 2 trường hợp dựa trên pageId
  const isTemplateEditor = () => {
    const currentPageId = typeof pageId === 'string' ? pageId : pageId?.[0]

    return TEMPLATE_NAMES.includes(currentPageId || '')
  }

  const isPageBuilderContent = () => {
    const currentPageId = typeof pageId === 'string' ? pageId : pageId?.[0]
    // Nếu pageId là số hoặc không nằm trong template names

    return !isNaN(Number(currentPageId)) || !TEMPLATE_NAMES.includes(currentPageId || '')
  }

  const getButtonText = () => {
    if (isTemplateEditor()) {
      return 'Cập nhật content template hiện tại'
    } else if (isPageBuilderContent()) {
      return 'Cập nhật content page builder hiện tại'
    }

    return 'Cập nhật'
  }

  const getConfirmMessage = () => {
    if (isTemplateEditor()) {
      return {
        first:
          '⚠️ CẢNH BÁO CỰC KỲ NGUY HIỂM\n\nBạn hãy chắc chắn về điều bạn sắp làm!\nHành động này sẽ cập nhật template hiện tại trên server.\n\nNhấn OK để tiếp tục, Cancel để hủy bỏ.',
        second:
          '🚨 CẢNH BÁO CUỐI CÙNG\n\nBạn có thực sự muốn cập nhật template?\n\n⚠️ Hành động này không thể hoàn tác!\nTemplate hiện tại sẽ được thay thế bằng content từ editor.\n\nNhấn OK để xác nhận cập nhật.'
      }
    }

    return {
      first:
        '⚠️ CẢNH BÁO CỰC KỲ NGUY HIỂM\n\nBạn hãy chắc chắn về điều bạn sắp làm!\nHành động này sẽ cập nhật content page builder hiện tại trên server.\n\nNhấn OK để tiếp tục, Cancel để hủy bỏ.',
      second:
        '🚨 CẢNH BÁO CUỐI CÙNG\n\nBạn có thực sự muốn cập nhật content page builder?\n\n⚠️ Hành động này không thể hoàn tác!\nContent page builder sẽ được thay thế bằng content từ editor.\n\nNhấn OK để xác nhận cập nhật.'
    }
  }

  const handleLogin = async (type: 'staging' | 'internal_staging') => {
    const ROOT =
      type === 'internal_staging' ? 'https://stagingonedx.vnpt-technology.vn:6443' : 'https://staging.onesme.vn'

    setLoading(true)
    form.validateFields().then(async values => {
      try {
        const bodyFormData = new FormData()

        bodyFormData.append('username', values.username)
        bodyFormData.append('password', values.password)
        bodyFormData.append('grant_type', 'password')
        bodyFormData.append('client_id', 'vnpt_clientid')
        bodyFormData.append('scope', 'scope')

        const response = await axios.post(`${ROOT}/auth-server/oauth/login`, bodyFormData, {
          headers: { 'Content-Type': 'multipart/form-data', Authorization: 'Basic dm5wdF9jbGllbnRpZDpzZWNyZXQ=' }
        })

        type === 'internal_staging'
          ? setInternalStagingToken(response.data.access_token)
          : setStagingToken(response.data.access_token)
        message.success('Đăng nhập thành công!')
      } catch (error) {
        message.error('Đăng nhập thất bại!')
      } finally {
        setLoading(false)
      }
    })
  }

  const handleUpdateTemplates = async (type: 'staging' | 'internal_staging') => {
    // Sử dụng window.confirm vì Modal.confirm không hiển thị
    const messages = getConfirmMessage()

    const firstConfirm = window.confirm(messages.first)

    if (!firstConfirm) return

    const secondConfirm = window.confirm(messages.second)

    if (secondConfirm) {
      await executeUpdate(type)
    }
  }

  const executeUpdate = async (type: 'staging' | 'internal_staging') => {
    // Thực hiện cập nhật template
    const ROOT =
      type === 'internal_staging' ? 'https://stagingonedx.vnpt-technology.vn:6443' : 'https://staging.onesme.vn'

    const API_URL = `${ROOT}/api/admin-portal/page-builder/internal/update-page-builder`

    const token = type === 'internal_staging' ? internalStagingToken : stagingToken

    setLoading(true)

    const IT_STAGING_IDS: Record<
      | 'HOME'
      | 'ENTERPRISE'
      | 'HOUSE_HOLD'
      | 'PERSONAL'
      | 'HOME_AFFILIATE_IOT'
      | 'HOME_AFFILIATE_ONESME'
      | 'HOME_AFFILIATE_GENZ'
      | 'HOME_AFFILIATE_HOUSEHOLD'
      | 'HOME_AFFILIATE_TEEN',
      number
    > = {
      HOME: 5,
      ENTERPRISE: 6,
      HOUSE_HOLD: 7,
      PERSONAL: 8,
      HOME_AFFILIATE_IOT: 9,
      HOME_AFFILIATE_ONESME: 10,
      HOME_AFFILIATE_GENZ: 11,
      HOME_AFFILIATE_HOUSEHOLD: 12,
      HOME_AFFILIATE_TEEN: 13
    }

    const STAGING_IDS: Record<
      | 'HOME'
      | 'ENTERPRISE'
      | 'HOUSE_HOLD'
      | 'PERSONAL'
      | 'HOME_AFFILIATE_IOT'
      | 'HOME_AFFILIATE_ONESME'
      | 'HOME_AFFILIATE_GENZ'
      | 'HOME_AFFILIATE_HOUSEHOLD'
      | 'HOME_AFFILIATE_TEEN',
      number
    > = {
      HOME: 5,
      ENTERPRISE: 6,
      HOUSE_HOLD: 7,
      PERSONAL: 8,
      HOME_AFFILIATE_IOT: 9,
      HOME_AFFILIATE_ONESME: 10,
      HOME_AFFILIATE_GENZ: 11,
      HOME_AFFILIATE_HOUSEHOLD: 12,
      HOME_AFFILIATE_TEEN: 13
    }

    const IDS = type === 'internal_staging' ? IT_STAGING_IDS : STAGING_IDS

    // Lấy content hiện tại từ editor
    const currentContent = query.serialize()

    // Xác định template cần cập nhật dựa trên pageId hiện tại
    const getTemplateByPageId = () => {
      const currentPageId = typeof pageId === 'string' ? pageId : pageId?.[0]

      // Nếu pageId là số, trả về trực tiếp với id đó
      if (!isNaN(Number(currentPageId))) {
        return [{ id: Number(currentPageId), template: currentContent, name: `PAGE_${currentPageId}` }]
      }

      // Nếu pageId là template name, dùng mapping từ IDS
      switch (currentPageId) {
        case 'home':
          return [{ id: IDS.HOME, template: currentContent, name: 'HOME' }]
        case 'enterprise':
          return [{ id: IDS.ENTERPRISE, template: currentContent, name: 'ENTERPRISE' }]
        case 'house-hold':
          return [{ id: IDS.HOUSE_HOLD, template: currentContent, name: 'HOUSE_HOLD' }]
        case 'personal':
          return [{ id: IDS.PERSONAL, template: currentContent, name: 'PERSONAL' }]
        case 'home-affiliate-iot':
          return [{ id: IDS.HOME_AFFILIATE_IOT, template: currentContent, name: 'HOME_AFFILIATE_IOT' }]
        case 'home-affiliate-onesme':
          return [{ id: IDS.HOME_AFFILIATE_ONESME, template: currentContent, name: 'HOME_AFFILIATE_ONESME' }]
        case 'home-affiliate-genz':
          return [{ id: IDS.HOME_AFFILIATE_GENZ, template: currentContent, name: 'HOME_AFFILIATE_GENZ' }]
        case 'home-affiliate-household':
          return [{ id: IDS.HOME_AFFILIATE_HOUSEHOLD, template: currentContent, name: 'HOME_AFFILIATE_HOUSEHOLD' }]
        case 'home-affiliate-teen':
          return [{ id: IDS.HOME_AFFILIATE_TEEN, template: currentContent, name: 'HOME_AFFILIATE_TEEN' }]
        default:
          message.error('Không xác định được template hiện tại!')

          return []
      }
    }

    const templates = getTemplateByPageId()

    if (templates.length === 0) {
      setLoading(false)

      return
    }

    try {
      // Phân biệt logic cập nhật dựa trên loại pageId
      const updateRequests = templates.map(({ id, template }) => {
        const updateType = isTemplateEditor() ? 'TEMPLATE_CONTENT' : 'PAGE_CONTENT'

        return axios.put(
          API_URL,
          {
            id: id,
            type: updateType,
            content: template
          },
          {
            headers: {
              Authorization: `Bearer ${token}`
            }
          }
        )
      })

      await Promise.all(updateRequests)

      // Cập nhật file JSON template nếu là template editor
      if (isTemplateEditor()) {
        await updateTemplateFileOnly()
      }

      message.success(
        `Cập nhật ${isTemplateEditor() ? 'template' : 'page builder'} ${templates[0].name} thành công với content hiện tại!${isTemplateEditor() ? ' File JSON template cũng đã được cập nhật.' : ''}`
      )
    } catch (error) {
      message.error('Cập nhật template thất bại!')
      console.error('Update template error:', error)
    } finally {
      setLoading(false)
    }
  }

  // region return
  return (
    <div className='flex gap-2'>
      <Button onClick={() => setOpenModalLogin(true)}>{getButtonText()}</Button>
      {isTemplateEditor() && (
        <Button type='primary' onClick={updateTemplateFileOnly} loading={loading}>
          Chỉ cập nhật file JSON
        </Button>
      )}

      <Modal
        zIndex={1000000}
        title='Đăng nhập'
        open={openModalLogin}
        onCancel={() => setOpenModalLogin(false)}
        footer={
          <div>
            <div className='flex justify-end gap-4'>
              <Button onClick={() => handleLogin('internal_staging')} loading={loading}>
                Đăng nhập internal staging
              </Button>
              <Button onClick={() => handleLogin('staging')} loading={loading}>
                Đăng nhập staging
              </Button>
            </div>
            <div className='mt-4 flex justify-end gap-4'>
              <Button
                type='primary'
                color='danger'
                disabled={!internalStagingToken}
                onClick={() => {
                  handleUpdateTemplates('internal_staging')
                }}
                loading={loading}
              >
                Cập nhật template internal staging
              </Button>
              <Button
                type='primary'
                color='danger'
                disabled={!stagingToken}
                onClick={() => {
                  handleUpdateTemplates('staging')
                }}
                loading={loading}
              >
                Cập nhật template staging
              </Button>
            </div>
          </div>
        }
      >
        <Form layout='vertical' form={form}>
          <i className='mb-4 text-red-500'>Vui lòng đăng nhập để thực hiện cập nhật template</i>
          <Form.Item label='Username' name='username' rules={[{ required: true, message: 'Vui lòng nhập username!' }]}>
            <Input placeholder='Nhập username' />
          </Form.Item>
          <Form.Item label='Password' name='password' rules={[{ required: true, message: 'Vui lòng nhập password!' }]}>
            <Input.Password placeholder='Nhập password' />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
  // endregion
}
