import React, { forwardRef, useState } from 'react'

import { useParams } from 'next/navigation'

import {
  Arrow<PERSON>eftOutlined,
  ArrowRightOutlined,
  DesktopOutlined,
  EyeOutlined,
  MobileOutlined,
  ReloadOutlined,
  SaveOutlined
} from '@ant-design/icons'

import { useEditor } from '@craftjs/core'

import { useMutation } from '@tanstack/react-query'

import type { FormInstance } from 'antd'
import { Spin, Breadcrumb, Button, message } from 'antd'

import classNames from 'classnames'
import html2canvas from 'html2canvas-pro'

import { useDispatch, useSelector } from 'react-redux'

import { DataURIToBlob } from '@/utils/image'
import { updateScreenType } from '@/redux-store/slices/builderSlice'
import type { AppDispatch, RootState } from '@/redux-store'
import Builder from '@/models/Builder'
import { SCREEN_TYPE } from '@/constants/page-builder'
import { navigateToDisplayList } from '@/utils/page-builder/navigation'

import { ModalPreview } from '../Viewport/ModalPreview'
import UpdateTemplateButtons from './UpdateTemplateButtons'

interface Props {
  form: FormInstance<any>
  pageName: string
  handleReloadPageContent: () => void
  isLoadingPageContent: boolean
}

export const ButtonScreenType = ({ icon, type }: { icon: React.ReactNode; type?: any }) => {
  const screenType = useSelector((state: RootState) => state.builderReducer.screenType)
  const dispatch: AppDispatch = useDispatch()

  const {
    actions: { selectNode }
  } = useEditor()

  return (
    <Button
      type='text'
      className={classNames(screenType === type && 'bg-primary font-bold text-white')}
      onClick={() => {
        selectNode(undefined)

        if (screenType !== type) {
          dispatch(updateScreenType(type))
        }
      }}
    >
      {icon}
    </Button>
  )
}

export const Topbar: React.FC<Props> = forwardRef(
  ({ form, pageName, handleReloadPageContent, isLoadingPageContent }, pageContainerRef) => {
    const { id: pageId } = useParams()

    const [openPreviewModal, setOpenPreviewModal] = useState(false)
    const [dataPreview, setDataPreview] = useState({})

    const {
      actions: { selectNode, history },
      query,
      canUndo,
      canRedo
    } = useEditor((state, query) => ({
      enabled: state.options.enabled,
      canUndo: state.options.enabled && query.history.canUndo(),
      canRedo: state.options.enabled && query.history.canRedo()
    }))

    const takeScreenshot = async () => {
      // const element = pageContainerRef && pageContainerRef.current
      const element: any = pageContainerRef && 'current' in pageContainerRef && pageContainerRef.current

      if (element) {
        const canvas = await html2canvas(element)
        const imgData = canvas.toDataURL('image/png')

        // Chuyển đổi imgData thành Blob và thêm vào FormData
        const formData = new FormData()

        formData.append('files', DataURIToBlob(imgData), `avatar.png`)

        uploadFileMutation.mutate(formData)
      }
    }

    const updatePageBuilder = useMutation({
      mutationKey: ['updatePageBuilder'],
      mutationFn: Builder.updatePageBuilder,
      onSuccess: async () => {
        await message.success('Cập nhật giao diện thành công')
        navigateToDisplayList('/admin-portal/display/list')
      },
      onError: (error: any) => {
        console.log('err', error)
        if (error?.errorCode === 'error.data.exists' && error?.field === 'name') {
          message.error('Tên giao diện đã tồn tại trong hệ thống')
        } else if (error?.errorCode === 'error.pagebuilder.active' && error?.field === 'slugName') {
          message.error('Đã có giao điện được bật cho slugname này. Vui lòng chọn giao diện khác')
        } else message.error('Cập nhật giao diện thất bại')
      }
    })

    const uploadFileMutation = useMutation({
      mutationKey: ['uploadFile'],
      mutationFn: Builder.uploadFile,
      onSuccess: (response: any) => {
        const { id } = response?.[0]

        updatePageBuilder.mutate({
          status: [true, 1].includes(form.getFieldValue('status')) ? 1 : 0,
          pageName: form.getFieldValue('pageName'),
          slugName: form.getFieldValue('slugName'),
          id: Number(pageId),
          imgId: id,
          content: query.serialize()
        })
      },
      onError: () => {
        message.error('Tải ảnh giao diện thất bại')
      }
    })

    // region return
    return (
      <Spin spinning={updatePageBuilder.isPending}>
        <Breadcrumb
          items={[
            { title: 'Giao diện' },
            {
              title: <a onClick={() => navigateToDisplayList('/admin-portal/display/list')}>Danh sách giao diện</a>
            },
            { title: pageName }
          ]}
          className='mb-6'
        />
        <div className='flex items-center justify-between'>
          <Button
            loading={isLoadingPageContent}
            type='default'
            icon={<ReloadOutlined />}
            iconPosition='end'
            onClick={handleReloadPageContent}
          >
            Trở về mặc định
          </Button>
          <div className='flex items-center gap-4'>
            {process.env.NODE_ENV === 'development' && <UpdateTemplateButtons query={query} pageId={pageId} />}
            <Button
              type='default'
              icon={<EyeOutlined />}
              iconPosition='end'
              onClick={() => {
                setOpenPreviewModal(true)
                setDataPreview(query.serialize())
              }}
            >
              Xem trước
            </Button>
            <Button
              type='primary'
              icon={<SaveOutlined />}
              iconPosition='end'
              onClick={() => {
                // 0: Bỏ chọn node hiện tại
                selectNode(undefined)
                // 1. validate form (status, pageName, slugName)
                form
                  .validateFields()
                  .then(() => {
                    // 2. Chụp ảnh màn hình từ thư viện => Lưu vào server
                    takeScreenshot()
                    // 3. Ảnh chụp màn hình lưu thành công => Gửi dữ liệu lên server: form, id, imgId, content
                  })
                  .catch(err => {
                    console.log(err)
                  })
              }}
            >
              Lưu lại
            </Button>
          </div>
        </div>
        <div
          className='mx-auto mt-6 flex items-center justify-between rounded-t-2xl bg-bg-primary-light'
          style={{ width: 'min(1440px , 100%)' }}
        >
          <div className='ml-4 flex items-center gap-4'>
            <Button disabled={!canUndo} type='text' icon={<ArrowLeftOutlined />} onClick={() => history.undo()} />
            <Button disabled={!canRedo} type='text' icon={<ArrowRightOutlined />} onClick={() => history.redo()} />
          </div>
          <div></div>
          <div className='flex gap-4'>
            <ButtonScreenType icon={<DesktopOutlined />} type={SCREEN_TYPE.DESKTOP} />
            {/* <ButtonScreenType icon={<TabletOutlined />} type={SCREEN_TYPE.TABLET} /> */}
            <ButtonScreenType icon={<MobileOutlined />} type={SCREEN_TYPE.MOBILE} />
          </div>
        </div>
        {/* Modal Xem trước */}
        {openPreviewModal && <ModalPreview open={openPreviewModal} setOpen={setOpenPreviewModal} data={dataPreview} />}
      </Spin>
    )
    // endregion
  }
)
