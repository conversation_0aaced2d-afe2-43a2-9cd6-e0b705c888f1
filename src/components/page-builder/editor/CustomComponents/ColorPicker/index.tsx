import React, { useEffect, useRef, useState } from 'react'

import { Button, Input, Popover, theme } from 'antd'
import { RgbaColorPicker } from 'react-colorful'

import { COLOR_SYSTEM } from '@/constants/page-builder'
import type { ColorPickerProps, RgbaColor } from '@/types/page-builder/colorPickerTypes'
import { hexToRgba, rgbaToHex } from '@/utils/page-builder/colorPicker'
import { SyncPropertyButton } from '../../ElementCustomize'
import { ColorGrid } from './ColorGrid'

export const ColorPicker: React.FC<ColorPickerProps> = ({
  value,
  onChange,
  label = 'Color',
  defaultColor = '#000000',
  propertyName
}) => {
  const { token } = theme.useToken()
  const [color, setColor] = useState<RgbaColor>({ r: 0, g: 0, b: 0, a: 1 })
  const [hexColor, setHexColor] = useState<string>(value || defaultColor)
  const [inputValue, setInputValue] = useState<string>(value || defaultColor)
  const isDraggingRef = useRef(false)

  const [savedColors, setSavedColors] = useState<string[]>(() => {
    const saved = localStorage.getItem('savedColors')

    return saved ? JSON.parse(saved) : []
  })

  // set màu cho color picker - chỉ sync khi không đang drag
  useEffect(() => {
    if (value && !isDraggingRef.current) {
      setHexColor(value)
      setInputValue(value)
      const rgbaColor = hexToRgba(value)

      if (rgbaColor) setColor(rgbaColor)
    }
  }, [value])

  useEffect(() => {
    localStorage.setItem('savedColors', JSON.stringify(savedColors))
  }, [savedColors])

  const handleRgbaChange = (newColor: RgbaColor) => {
    isDraggingRef.current = true
    setColor(newColor)
    const hex = rgbaToHex(newColor)

    setHexColor(hex)
    setInputValue(hex)
    onChange?.(hex)
    
    // Reset dragging flag sau một khoảng thời gian ngắn
    setTimeout(() => {
      isDraggingRef.current = false
    }, 100)
  }

  const handleHexChange = (value: string) => {
    setInputValue(value)

    const isValidHex = /^#?([0-9A-F]{0,6})$/i.test(value)

    if (!isValidHex) return

    const formattedValue = value.startsWith('#') ? value : `#${value}`

    if (formattedValue.length === 7) {
      setHexColor(formattedValue)
      const rgbaColor = hexToRgba(formattedValue)

      if (rgbaColor) {
        setColor(rgbaColor)
        onChange?.(formattedValue)
      }
    }
  }

  const handleColorSelect = (hex: string) => {
    setHexColor(hex)
    setInputValue(hex)
    const rgbaColor = hexToRgba(hex)

    if (rgbaColor) {
      setColor(rgbaColor)
      onChange?.(hex)
    }
  }

  const saveColor = () => {
    if (!savedColors.includes(hexColor)) {
      setSavedColors(prev => [...prev, hexColor])
    }
  }

  const content = (
    <div
      className='w-full'
      style={{ display: 'flex', flexDirection: 'column', gap: '16px', maxHeight: '60vh', overflowY: 'auto' }}
    >
      <Popover
        trigger='click'
        placement='leftBottom'
        overlayStyle={{ zIndex: 9999 }}
        content={
          <>
            <RgbaColorPicker color={color} onChange={handleRgbaChange} />
            <div className='mt-2 flex flex-col gap-2'>
              <Input
                value={inputValue}
                onChange={e => handleHexChange(e.target.value)}
                style={{ textTransform: 'uppercase' }}
                maxLength={7}
                placeholder='#000000'
              />
              <div className='flex justify-end'>
                <Button
                  type='primary'
                  className='w-[fit] rounded-md'
                  iconPosition='end'
                  icon={<i className='onedx-add' />}
                  onClick={saveColor}
                >
                  Lưu màu
                </Button>
              </div>
            </div>
          </>
        }
      >
        <div className='flex w-1/4 items-center'>
          <div
            style={{ backgroundImage: 'url("/assets/images/pages/AddColor.png")', width: '24px', height: '24px' }}
            className='cursor-pointer object-fill'
          />
          <span className='ml-3 cursor-pointer text-sm'>Thêm màu</span>
        </div>
      </Popover>

      <div>
        <h4 className='mb-2'>Màu của tôi</h4>
        <ColorGrid colors={savedColors} selectedColor={hexColor} onSelect={handleColorSelect} />
      </div>

      <div>
        <h4 className='mb-2'>Màu hệ thống</h4>
        {Object.keys(COLOR_SYSTEM).map(lineKey => (
          <div key={lineKey} style={{ marginBottom: '16px' }}>
            <ColorGrid
              colors={COLOR_SYSTEM[lineKey as keyof typeof COLOR_SYSTEM]}
              selectedColor={hexColor}
              onSelect={handleColorSelect}
            />
          </div>
        ))}
      </div>
    </div>
  )

  return (
    <div className='flex items-center justify-between'>
      <div className='flex items-center gap-1'>
        <label className='block'>{label}</label>
        <SyncPropertyButton propertyName={propertyName || 'color'} defaultValue='#000000' newValue={value} />
      </div>
      <Popover
        content={content}
        trigger='click'
        placement='bottomLeft'
        overlayStyle={{ width: '500px', zIndex: 9999, maxHeight: '70vh' }}
      >
        <div style={{ width: 100, display: 'flex', alignItems: 'center', gap: 8, cursor: 'pointer' }}>
          <div
            style={{
              width: 24,
              height: 24,
              backgroundColor: hexColor,
              border: `1px solid ${token.colorBorder}`,
              borderRadius: token.borderRadius,
              justifyContent: 'flex-end',
              marginLeft: 'auto'
            }}
          />
        </div>
      </Popover>
    </div>
  )
}
