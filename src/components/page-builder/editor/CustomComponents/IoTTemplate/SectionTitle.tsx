'use client'
import React from 'react'

import { useNode, type UserComponent } from '@craftjs/core'

import type { ResponsiveBaseCustomizeProps } from '@/types/page-builder'

interface SectionTitleCustomizeProps {
  text?: string
  fontSize?: number
  fontWeight?: number
  color?: string
  textAlign?: 'left' | 'center' | 'right'
  marginBottom?: number
}

type SectionTitleProps = ResponsiveBaseCustomizeProps<SectionTitleCustomizeProps>

export const SectionTitle: UserComponent<SectionTitleProps> = props => {
  const {
    connectors: { connect, drag }
  } = useNode()

  // Get current screen props (simplified - in real app this would be responsive)
  const currentProps = props.desktop || {
    text: 'Section Title',
    fontSize: 32,
    fontWeight: 700,
    color: '#00529c',
    textAlign: 'center' as const,
    marginBottom: 40
  }

  const {
    text = 'Section Title',
    fontSize = 32,
    fontWeight = 700,
    color = '#00529c',
    textAlign = 'center',
    marginBottom = 40
  } = currentProps

  const style: React.CSSProperties = {
    fontSize: `${fontSize}px`,
    fontWeight,
    color,
    textAlign,
    marginBottom: `${marginBottom}px`,
    margin: '0 auto'
  }

  return (
    <h2
      ref={(ref: HTMLHeadingElement | null) => {
        if (ref) {
          connect(drag(ref))
        }
      }}
      style={style}
      className='section-title'
    >
      {text}
    </h2>
  )
}

SectionTitle.craft = {
  displayName: 'Văn bản',
  props: {
    desktop: {
      text: 'Section Title',
      fontSize: 32,
      fontWeight: 700,
      color: '#00529c',
      textAlign: 'center',
      marginBottom: 40
    },
    tablet: {
      text: 'Section Title',
      fontSize: 28,
      fontWeight: 700,
      color: '#00529c',
      textAlign: 'center',
      marginBottom: 32
    },
    mobile: {
      text: 'Section Title',
      fontSize: 24,
      fontWeight: 700,
      color: '#00529c',
      textAlign: 'center',
      marginBottom: 24
    }
  },
  rules: {
    canDrag: () => true,
    canMoveIn: () => false
  }
}
