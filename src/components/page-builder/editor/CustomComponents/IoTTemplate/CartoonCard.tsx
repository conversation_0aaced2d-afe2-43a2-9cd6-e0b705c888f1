'use client'
import React from 'react'

import { useNode, type UserComponent } from '@craftjs/core'

type CartoonCardProps = React.HTMLAttributes<HTMLDivElement> & {
  children?: React.ReactNode
}

export const CartoonCard: UserComponent<CartoonCardProps> = ({ children, className = '', ...props }) => {
  const {
    connectors: { connect, drag }
  } = useNode()

  return (
    <div
      className='w-full'
      ref={(ref: HTMLDivElement | null) => {
        if (ref) {
          connect(drag(ref))
        }
      }}
    >
      <div className={`product-card-cartoon overflow-hidden ${className}`} {...props}>
        {children}
        {/* Scoped styles for the card container */}
        <style jsx>{`
          .product-card-cartoon {
            background-color: white;
            border-radius: 1.5rem;
            border: 3px solid #2a2a2a;
            box-shadow: 8px 8px 0 #2a2a2a;
            transition: all 0.2s ease-in-out;
            display: flex;
            flex-direction: column;
            height: 100%;
          }
          .product-card-cartoon:hover {
            transform: translate(4px, 4px);
            box-shadow: 4px 4px 0 #2a2a2a;
          }
        `}</style>
        {/* Global styles for cartoon buttons so they apply to children */}
        <style jsx global>{`
          .btn-cartoon-primary {
            font-family: 'Baloo 2', cursive;
            background-color: #ffec8a;
            color: #2a2a2a;
            border: 2px solid #2a2a2a;
            border-radius: 0.75rem;
            box-shadow: 4px 4px 0 #2a2a2a;
            transition: all 0.2s ease-in-out;
            text-align: center;
          }
          .btn-cartoon-primary:hover {
            background-color: #ffec8a;
            transform: translate(2px, 2px);
            box-shadow: 2px 2px 0 #2a2a2a;
          }
          .btn-cartoon-secondary {
            font-family: 'Baloo 2', cursive;
            background-color: #e0e0e0;
            color: #2a2a2a;
            border: 2px solid #2a2a2a;
            border-radius: 0.75rem;
            transition: all 0.2s ease-in-out;
            text-align: center;
          }
          .btn-cartoon-secondary:hover {
            background-color: #f0f0f0;
          }
          .font-cartoon {
            font-family: 'Baloo 2', cursive;
          }
        `}</style>
      </div>
    </div>
  )
}

CartoonCard.craft = {
  displayName: 'Offer Card'
}
