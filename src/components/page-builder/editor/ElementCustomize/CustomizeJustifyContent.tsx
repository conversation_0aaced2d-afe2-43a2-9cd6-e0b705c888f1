import { Pic<PERSON>enterOutlined, VerticalAlignBottomOutlined, VerticalAlignTopOutlined } from '@ant-design/icons'
import { useNode } from '@craftjs/core'
import { Button, Space } from 'antd'
import { useSelector } from 'react-redux'

import { selectScreenType } from '@/redux-store/slices/builderSlice'

const alignOptions = [
  { value: 'start', icon: <VerticalAlignTopOutlined /> },
  { value: 'center', icon: <PicCenterOutlined /> },
  { value: 'end', icon: <VerticalAlignBottomOutlined /> }
]

export const CustomizeJustifyContent = () => {
  const screenType = useSelector(selectScreenType)

  const {
    actions: { setProp },
    values
  } = useNode(node => ({
    values: node.data.props
  }))

  return (
    <div className='flex w-full flex-col gap-2'>
      <div className='flex items-center gap-1'>
        <label className='block'>Justify Content</label>
      </div>
      <Space.Compact className='w-full'>
        {alignOptions.map(({ value, icon }) => (
          <Button
            className='w-1/3'
            size='large'
            key={value}
            type={(values[screenType].justifyContent || 'center') === value ? 'primary' : 'default'}
            icon={icon}
            onClick={() => {
              setProp((props: any) => {
                props[screenType].justifyContent = value
              })
            }}
          />
        ))}
      </Space.Compact>
    </div>
  )
}
