import { Align<PERSON>enterOutlined, Align<PERSON><PERSON>tOutlined, Align<PERSON>ightOutlined } from '@ant-design/icons'
import { Button, Space } from 'antd'
import { useNode } from '@craftjs/core'
import { useSelector } from 'react-redux'

import { selectScreenType } from '@/redux-store/slices/builderSlice'

const alignOptions = [
  { value: 'start', icon: <AlignLeftOutlined /> },
  { value: 'center', icon: <AlignCenterOutlined /> },
  { value: 'end', icon: <AlignRightOutlined /> }
]

export const CustomizeContentAlign = () => {
  const screenType = useSelector(selectScreenType)

  const {
    actions: { setProp },
    values
  } = useNode(node => ({
    values: node.data.props
  }))

  return (
    <div className='flex w-full flex-col gap-2'>
      <div className='flex items-center gap-1'>
        <label className='block'>Content align</label>
      </div>
      <Space.Compact className='w-full'>
        {alignOptions.map(({ value: alignValue, icon }) => (
          <Button
            className='w-1/3'
            size='large'
            key={alignValue}
            type={(values[screenType].contentAlign || 'start') === alignValue ? 'primary' : 'default'}
            icon={icon}
            onClick={() => {
              // Update column contentAlign
              // Text children sẽ tự động inherit từ ColumnContext
              setProp((props: any) => {
                props[screenType].contentAlign = alignValue
              })
            }}
          />
        ))}
      </Space.Compact>
    </div>
  )
}
