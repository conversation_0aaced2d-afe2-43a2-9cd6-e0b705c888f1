import React from 'react'

import { useNode } from '@craftjs/core'

import { InputNumber, Select, Slider } from 'antd'

import { useSelector } from 'react-redux'

import { ColorPicker } from '@/components/page-builder/editor/CustomComponents/ColorPicker'

import { BORDER_STYLE } from '@/constants/page-builder'
import { selectScreenType } from '@/redux-store/slices/builderSlice'
import { handleKeyPress, handlePaste } from '@/validator'

interface Props {
  label?: string
  keyProp?: string
  maxBorderThickness?: number
}

export const CustomizeBorder: React.FC<Props> = ({ label, keyProp, maxBorderThickness = 9 }) => {
  const screenType = useSelector(selectScreenType)

  const {
    actions: { setProp },
    values
  } = useNode(node => ({
    values: node.data.props
  }))

  const updateBorder = (property: string, value: any) => {
    setProp((props: any) => {
      if (!props[screenType][keyProp || 'border']) {
        props[screenType][keyProp || 'border'] = {}
      }

      props[screenType][keyProp || 'border'][property] = value
    }, 300)
  }

  const handleChangeBorder = (value: string) => updateBorder('style', value)
  const handleThicknessChange = (value: number) => updateBorder('thickness', value)

  // Safe fallbacks to avoid uncontrolled UI defaults (e.g., Antd Select showing first option "Solid")
  const borderKey = keyProp || 'border'
  const currentBorder = (values as any)[screenType]?.[borderKey] || {}
  const currentStyle: string = currentBorder.style ?? 'none'
  const currentRadius: number = currentBorder.radius ?? 0
  const currentThickness: number = currentBorder.thickness ?? 0
  const currentColor: string = currentBorder.color ?? ''

  return (
    <div className='space-y-4'>
      {label && <div className='text-base'> {label} </div>}
      {/* Border Style */}
      <div className='flex items-center justify-between'>
        <div>Border style</div>
        <div>
          <Select
            className='w-30'
            value={currentStyle}
            onChange={handleChangeBorder}
            options={BORDER_STYLE}
          />
        </div>
      </div>
      {currentStyle !== 'none' && (
        <>
          {/* Border Color */}
          <div className='flex items-center justify-between space-y-2'>
            <div>Border Color</div>
            <div>
              <ColorPicker
                label=''
                value={currentColor}
                onChange={color => updateBorder('color', color)}
              />
            </div>
          </div>

          {/* Border Thickness */}
          <div className='flex flex-col'>
            <div className='w-full'>Border thickness</div>
            <div className='flex items-center justify-between gap-4'>
              <Slider
                className='w-4/5'
                min={0}
                max={maxBorderThickness}
                value={currentThickness}
                onChange={value => handleThicknessChange(value as number)}
              />
              <InputNumber
                min={0}
                max={maxBorderThickness}
                maxLength={1}
                className='w-44'
                value={currentThickness}
                onChange={value => handleThicknessChange(value as number)}
                addonAfter='px'
                onKeyPress={handleKeyPress}
                onPaste={handlePaste}
                controls={false}
              />
            </div>
          </div>

          {/* Border Radius */}
          <div className='flex items-center justify-between gap-4'>
            <div>Radius</div>
            <Select
              className='w-30'
              value={currentRadius}
              onChange={value => updateBorder('radius', value)}
              options={[
                { label: '0', value: 0 },
                { label: '4', value: 4 },
                { label: '6', value: 6 },
                { label: '8', value: 8 },
                { label: '12', value: 12 },
                { label: '16', value: 16 },
                { label: '24', value: 24 },
                { label: 'full', value: 999 }
              ]}
            />
          </div>
        </>
      )}
    </div>
  )
}
