import React, { useEffect, useState } from 'react'

import { useNode } from '@craftjs/core'
import { useMutation } from '@tanstack/react-query'
import { Button, Radio, Space, Tabs, Upload, message } from 'antd'
import type { TabsProps } from 'antd/lib'

import { useSelector } from 'react-redux'

import { UPLOAD_TYPE } from '@/constants/custom-field/modalCreateVariant'
import { type UseCraftJsPropOptions } from '@/hooks'
import { API_ROOT } from '@/models/Base'
import UploadImage from '@/models/UploadImage'
import { selectScreenType } from '@/redux-store/slices/builderSlice'
import { URLUploadModal } from '../CustomComponents'
import { ImageLibraryModal } from '../CustomComponents/Modal'

const { Dragger } = Upload

interface CustomizeImageUploadProps extends UseCraftJsPropOptions {
  label?: string
}

export const CustomizeImageUpload: React.FC<CustomizeImageUploadProps> = () => {
  const {
    actions: { setProp },
    values
  } = useNode(node => ({
    values: node.data.props
  }))

  const screenType = useSelector(selectScreenType)

  // region state
  const [typeUpload, setTypeUpload] = useState(values[screenType].typeUpload || 'EXIST')
  const [openLibraryModal, setOpenLibraryModal] = useState(false)
  const [openInputUrlModal, setOpenInputUrlModal] = useState(false)
  const [activeTab, setActiveTab] = useState<'desktop' | 'mobile'>(screenType === 'mobile' ? 'mobile' : 'desktop')

  const [imgName, setImgName] = useState('')

  // Đồng bộ activeTab với screenType
  useEffect(() => {
    setActiveTab(screenType === 'mobile' ? 'mobile' : 'desktop')
  }, [screenType])

  // Đồng bộ typeUpload với props khi thay đổi screenType/values
  useEffect(() => {
    const current = values[screenType]?.typeUpload
    const next = current || 'EXIST'

    setTypeUpload(next)

    if (!current) {
      setProp((props: any) => {
        props[screenType].typeUpload = 'EXIST'
      }, 300)
    }
  }, [screenType, values, setProp])

  // region upload Api
  const insertImageMutation = useMutation({
    mutationFn: UploadImage.insert
  })

  // region handler
  const handleDelete = () => {
    if (activeTab === 'desktop') {
      setProp((props: any) => {
        props['desktop'].imgSrc = ''
        props['desktop'].externalLink = ''
      }, 300)
    } else {
      setProp((props: any) => {
        props['mobile'].imgSrcMobile = ''
        props['mobile'].externalLinkMobile = ''
      }, 300)
    }
  }

  const validateImg = (file: { type: string; size: number; name: string }) => {
    const acceptedTypes = ['image/jpeg', 'image/webp']

    if (file.name.toLowerCase().endsWith('.jfif') || file.name.toLowerCase().endsWith('.jpg')) {
      message.error('Vui lòng tải lên file ảnh định dạng JPEG, WEBP')

      return false
    }

    if (!acceptedTypes.includes(file.type)) {
      message.error('Vui lòng tải lên file ảnh định dạng JPEG, WEBP')

      return false
    }

    const maxSize = 1 * 1024 * 1024

    if (file.size > maxSize) {
      message.error('Vui lòng tải lên file ảnh dung lượng không vượt quá 1MB')

      return false
    }

    return true
  }

  const handleUpload = async ({ file, onSuccess, onError }: any) => {
    try {
      if (validateImg(file)) {
        const formData = new FormData()

        formData.append('files', file)
        formData.append('fileSize', file.size)

        const res = await insertImageMutation.mutateAsync(formData)
        const imageUrl = res.filePath

        setImgName(res.fileName)

        Promise.all([
          new Promise(resolve => {
            setProp((props: any) => {
              if (activeTab === 'desktop') {
                props['desktop'].imgSrc = `${API_ROOT}${imageUrl}`
                props['desktop'].externalLink = ''
                props['desktop'].typeUpload = UPLOAD_TYPE.UPLOAD
              } else {
                props['mobile'].imgSrcMobile = `${API_ROOT}${imageUrl}`
                props['mobile'].externalLinkMobile = ''
                props['mobile'].typeUpload = UPLOAD_TYPE.UPLOAD
              }
            }, 300)
            resolve(true)
          })
        ]).then(() => {
          onSuccess()
        })
      }
    } catch (error) {
      message.error('Tải ảnh lỗi')
      onError()
    }
  }

  const handleUrlUpload = (uploadedImageUrl: string) => {
    setProp((props: any) => {
      if (activeTab === 'desktop') {
        props['desktop'].externalLink = uploadedImageUrl
        props['desktop'].imgSrc = ''
        props['desktop'].typeUpload = UPLOAD_TYPE.UPLOAD
      } else {
        props['mobile'].externalLinkMobile = uploadedImageUrl
        props['mobile'].imgSrcMobile = ''
        props['mobile'].typeUpload = UPLOAD_TYPE.UPLOAD
      }
    }, 300)
  }

  const handleSelectImage = (imageUrl: string) => {
    setProp((props: any) => {
      if (activeTab === 'desktop') {
        props['desktop'].imgSrc = imageUrl
        props['desktop'].externalLink = ''
        props['desktop'].typeUpload = 'EXIST'
      } else {
        props['mobile'].imgSrcMobile = imageUrl
        props['mobile'].externalLinkMobile = ''
        props['mobile'].typeUpload = 'EXIST'
      }
    }, 300)
    setOpenLibraryModal(false)
  }

  const getCurrentImageSource = () => {
    if (activeTab === 'desktop') {
      if (values['desktop'].typeUpload === typeUpload) {
        return values['desktop'].externalLink || values['desktop'].imgSrc || ''
      }

      return ''
    }

    // Mobile: kiểm tra mobile values trước, sau đó fallback về desktop values
    const mobileTypeUpload = values['mobile']?.typeUpload || values['desktop']?.typeUpload || typeUpload
    
    if (mobileTypeUpload === typeUpload) {
      return (
        values['mobile']?.externalLinkMobile || 
        values['mobile']?.imgSrcMobile || 
        values['desktop']?.externalLink || 
        values['desktop']?.imgSrc || 
        ''
      )
    }

    return ''
  }

  const renderUploadType = (typeUpload: string) => {
    const currentImageSource = getCurrentImageSource()

    const ImagePreview = () => (
      <div className='grid h-[158px] place-content-center rounded-xl bg-[#f3f2f5]'>
        <div className='flex flex-col items-center justify-center gap-2'>
          <div
            style={{
              width: '115px',
              height: '70px',
              backgroundImage: `url(${currentImageSource})`,
              borderRadius: '8px',
              backgroundSize: 'contain',
              backgroundRepeat: 'no-repeat',
              backgroundPosition: 'center'
            }}
          >
            <div
              className='flex w-5 items-center justify-center rounded-full bg-bg-primary-default text-white'
              style={{ marginTop: '-10px', marginLeft: '-7px' }}
            >
              {currentImageSource && <i onClick={handleDelete} className='onedx-close-icon size-5 cursor-pointer' />}
            </div>
          </div>
          <div className='line-clamp-1 max-w-30 overflow-hidden text-ellipsis'>{imgName}</div>
        </div>
      </div>
    )

    if (typeUpload === 'EXIST') {
      return (
        <div>
          {!currentImageSource ? (
            <div className='flex h-[158px] flex-col items-center justify-center gap-2 rounded-lg bg-[#f3f2f5]'>
              <div
                className='flex w-30 flex-col items-center justify-center rounded-lg border border-dashed border-gray-300 bg-white p-4 hover:cursor-pointer hover:border-border-primary-default'
                onClick={() => setOpenLibraryModal(true)}
              >
                <i className='onedx-upload-traffic size-8 text-text-primary-default' />
                <div className='text-headline-16'>Chọn ảnh</div>
              </div>
            </div>
          ) : (
            <ImagePreview />
          )}
        </div>
      )
    }

    if (typeUpload === UPLOAD_TYPE.UPLOAD) {
      return (
        <div>
          {currentImageSource ? (
            <ImagePreview />
          ) : (
            <div className='flex h-[158px] flex-col items-center justify-center gap-2 rounded-lg bg-[#f3f2f5]'>
              <Dragger
                name='file'
                multiple={false}
                // accept='.jpeg,.webp'
                customRequest={handleUpload}
                className='w-30 bg-white'
                showUploadList={false}
              >
                <div>
                  <i className='onedx-upload-traffic size-8 text-text-primary-default' />
                  <div className='text-headline-16'>Tải file lên</div>
                </div>
              </Dragger>
              <Button type='default' onClick={() => setOpenInputUrlModal(true)} className='w-30 bg-white'>
                Nhập URL
              </Button>
            </div>
          )}
        </div>
      )
    }
  }

  const items: TabsProps['items'] = [
    {
      key: 'desktop',
      label: 'Desktop',
      children: renderUploadType(typeUpload)
    },
    {
      key: 'mobile',
      label: 'Mobile',
      children: renderUploadType(typeUpload)
    }
  ]

  // region return
  return (
    <div className='w-full'>
      <Radio.Group
        onChange={e => {
          setTypeUpload(e.target.value)
        }}
        value={typeUpload}
        size='large'
        className='w-full'
      >
        <Space direction='vertical' size='middle' className='w-full'>
          <Radio value='EXIST'>Chọn ảnh từ thư viện</Radio>
          {typeUpload === 'EXIST' && (
            <Tabs
              defaultActiveKey='desktop'
              activeKey={activeTab}
              items={items}
              onChange={key => setActiveTab(key as 'desktop' | 'mobile')}
            />
          )}

          <Radio value={UPLOAD_TYPE.UPLOAD}>Upload ảnh</Radio>
          {typeUpload === UPLOAD_TYPE.UPLOAD && (
            <Tabs
              defaultActiveKey='desktop'
              activeKey={activeTab}
              items={items}
              onChange={key => setActiveTab(key as 'desktop' | 'mobile')}
            />
          )}
        </Space>
      </Radio.Group>

      {openInputUrlModal && (
        <URLUploadModal
          isOpen={openInputUrlModal}
          onClose={() => setOpenInputUrlModal(false)}
          onSuccess={handleUrlUpload}
          type='image'
        />
      )}

      {openLibraryModal && (
        <ImageLibraryModal
          visible={openLibraryModal}
          onClose={() => setOpenLibraryModal(false)}
          onSelectImage={handleSelectImage}
          type='image'
        />
      )}
    </div>
  )
}

export default CustomizeImageUpload
