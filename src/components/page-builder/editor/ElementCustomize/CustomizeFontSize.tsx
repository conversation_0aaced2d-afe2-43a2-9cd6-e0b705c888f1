import React from 'react'

import { Select } from 'antd'

import { FONT_OPTIONS } from '@/constants/page-builder'
import { SyncPropertyButton } from './SyncPropertyButton'
import type { UseCraftJsPropOptions } from '@/hooks'
import { useCraftJsProp } from '@/hooks'

export const CustomizeFontSize: React.FC<UseCraftJsPropOptions> = ({
  arrayName = '',
  propertyName = 'fontSize',
  index,
  label = 'Font size',
  defaultValue = 16
}) => {
  const { value, updateValue } = useCraftJsProp({
    arrayName,
    propertyName,
    index,
    defaultValue
  })

  return (
    <div className='flex items-center justify-between'>
      <div className='flex items-center gap-1'>
        <label className='block'>{label}</label>
        <SyncPropertyButton propertyName={propertyName} defaultValue={defaultValue} newValue={value} />
      </div>
      <Select className='w-2/5' value={value} onChange={updateValue}>
        {FONT_OPTIONS.map(option => (
          <Select.Option key={option.value} value={option.value}>
            {option.label}
          </Select.Option>
        ))}
      </Select>
    </div>
  )
}
