import React, { useEffect, useState } from 'react'

import { Button, Form, message, Radio, Space, Tabs } from 'antd'
import { useSelector } from 'react-redux'
import { useNode } from '@craftjs/core'
import { DeleteOutlined, UploadOutlined } from '@ant-design/icons'

import { selectScreenType } from '@/redux-store/slices/builderSlice'
import { CustomizeContentBanner } from './CustomizeContentBanner'
import { CustomizeButtonLink } from './CustomizeButtonLink'
import { SCREEN_TYPE } from '@/constants/page-builder'
import { ImageLibraryModal } from '../CustomComponents/Modal'
import { CustomizeTextAlign } from './CustomizeTextAlign'
import { CustomizeBackgroundColor } from './CustomizeBackgroundColor'
import { CustomizeImageFit } from './CustomizeImageFit'
import { CustomizeSEO } from './CustomizeSEO'
import { CustomizeImageUploadBanner } from './CustomizeImageUploadBanner'
import { CustomizeImageSize } from './CustomizeImageSize'

interface CustomizeImageSlideProps {
  initialValues?: any
  onFinish?: (values: any) => void
  onCancel?: () => void
  onDelete?: () => void
  submitText?: string
  currentImgSrc?: string
  totalImages?: number
  hasButton?: boolean
  onValuesChange?: (values: any) => void
  onImageUpload?: (imageUrl: string) => void
  index?: number
  type?: 'edit' | 'add'
  showOfType: 'Banner' | 'Ảnh'
}

export const CustomizeImageSlide: React.FC<CustomizeImageSlideProps> = ({
  initialValues,
  onFinish,
  onCancel,
  onDelete,
  submitText = 'Thêm',
  currentImgSrc,
  totalImages = 0,
  index = 0,
  type,
  showOfType
}) => {
  const screenType = useSelector(selectScreenType)
  const [form] = Form.useForm()
  const isMobile = screenType === SCREEN_TYPE.MOBILE
  const [isLibraryModalVisible, setIsLibraryModalVisible] = useState(false)
  const [activeTab, setActiveTab] = useState('desktop')

  const {
    actions: { setProp },
    values
  } = useNode(node => ({
    values: node.data.props
  }))

  useEffect(() => {
    if (currentImgSrc) {
      form.setFieldValue('url', currentImgSrc.replace('https://', ''))
    }
  }, [currentImgSrc, form])

  useEffect(() => {
    if (initialValues?.url) {
      setProp((props: any) => (props[screenType].imgSrc = initialValues?.url), 300)
    }
  }, [initialValues?.url, screenType, setProp])

  // Thiết lập giá trị mặc định cho imageSelectType
  useEffect(() => {
    setProp((props: any) => {
      if (!props[screenType]) props[screenType] = {}
      if (!props[screenType].images) props[screenType].images = []
      if (!props[screenType].images[index]) props[screenType].images[index] = {}

      // Chỉ thiết lập mặc định nếu chưa có giá trị hoặc giá trị không hợp lệ
      const currentType = props[screenType].images[index].imageSelectType
      
      if (!currentType || (currentType !== 'library' && currentType !== 'upload')) {
        props[screenType].images[index].imageSelectType = 'library'
      }
    })
  }, [screenType, index, setProp])

  const handleSubmit = async () => {
    try {
      const backgroundImage = isMobile
        ? values[screenType]?.images[index]?.urlMobile || values[screenType]?.images[index]?.urlLibraryMobile
        : values[screenType]?.images[index]?.url || values[screenType]?.images[index]?.urlLibrary

      const hasImage = backgroundImage || currentImgSrc

      if (!hasImage) {
        message.error('Vui lòng tải ảnh lên trước khi tiếp tục')

        return
      }

      const formValues = await form.validateFields()

      // set lại isAdded nếu item này được bấm vào nút thêm
      setProp((props: any) => {
        if (!props[screenType]) props[screenType] = {}
        if (!props[screenType].images) props[screenType].images = []
        if (!props[screenType].images[index]) props[screenType].images[index] = {}

        props[screenType].images[index].isAdded = true
      })

      if (onFinish) onFinish(formValues)
    } catch (error) {
      console.error('Validation failed:', error)
    }
  }

  const handleLibraryImageSelect = (imageUrl: string) => {
    setProp((props: any) => {
      // Ensure the structure exists
      if (!props.DESKTOP) props.DESKTOP = {}
      if (!props.MOBILE) props.MOBILE = {}
      if (!props.DESKTOP.images) props.DESKTOP.images = []
      if (!props.MOBILE.images) props.MOBILE.images = []
      if (!props.DESKTOP.images[index]) props.DESKTOP.images[index] = {}
      if (!props.MOBILE.images[index]) props.MOBILE.images[index] = {}

      if (activeTab === 'desktop') {
        props[screenType].images[index].urlLibrary = imageUrl
      } else {
        props[screenType].images[index].urlLibraryMobile = imageUrl
      }
    })
    setIsLibraryModalVisible(false)
  }

  const handleDeleteImage = (type: 'desktop' | 'mobile') => {
    setProp((props: any) => {
      if (type === 'desktop') {
        props[screenType].images[index].urlLibrary = ''
      } else {
        props[screenType].images[index].urlLibraryMobile = ''
      }
    })
  }

  const renderImageUploadContent = () => (
    <div className='space-y-4'>
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: 'desktop',
            label: 'Desktop',
            children: (
              <Form.Item name='backgroundImage'>
                <CustomizeImageUploadBanner
                  label=''
                  isUpdateDesktop
                  arrayName='images'
                  propertyName='url'
                  index={index}
                  maxFileSize={1}
                />
              </Form.Item>
            )
          },
          {
            key: 'mobile',
            label: 'Mobile',
            children: (
              <Form.Item name='backgroundImageMobile'>
                <CustomizeImageUploadBanner
                  label=''
                  isUpdateMobile
                  arrayName='images'
                  propertyName='urlMobile'
                  index={index}
                  maxFileSize={1}
                />
              </Form.Item>
            )
          }
        ]}
      />
    </div>
  )

  const renderLibraryContent = () => {
    const desktopImage = values[screenType]?.images[index]?.urlLibrary || ''
    const mobileImage = values[screenType]?.images[index]?.urlLibraryMobile || ''

    return (
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: 'desktop',
            label: 'Desktop',
            children: (
              <div className='mt-2 flex h-40 items-center justify-center overflow-hidden rounded-lg bg-gray-100'>
                {!desktopImage ? (
                  <Button
                    type='dashed'
                    onClick={() => setIsLibraryModalVisible(true)}
                    className='flex h-20 w-1/3 items-center justify-center'
                  >
                    <div className='flex h-full flex-col items-center justify-center'>
                      <UploadOutlined className='mb-2 text-9xl text-blue-500' />
                      <p className='m-0 text-base'>Chọn ảnh</p>
                    </div>
                  </Button>
                ) : (
                  <div className='group relative max-h-40'>
                    <img src={desktopImage} alt='Preview' className='h-40 w-full object-contain' />
                    <div className='absolute inset-0 flex items-center justify-center bg-black/10 opacity-0 transition-opacity group-hover:opacity-100'>
                      <Button
                        icon={<DeleteOutlined className='text-white' />}
                        onClick={() => handleDeleteImage('desktop')}
                        className='border-0 bg-black/60 hover:bg-black/40'
                      />
                    </div>
                  </div>
                )}
              </div>
            )
          },
          {
            key: 'mobile',
            label: 'Mobile',
            children: (
              <div className='mt-2 flex h-40 items-center justify-center overflow-hidden rounded-lg bg-gray-100'>
                {!mobileImage ? (
                  <Button
                    type='dashed'
                    onClick={() => setIsLibraryModalVisible(true)}
                    className='flex h-32 w-full items-center justify-center'
                  >
                    Chọn ảnh từ thư viện cho Mobile
                  </Button>
                ) : (
                  <div className='group relative max-h-40'>
                    <img src={mobileImage} alt='Preview' className='h-40 w-full object-contain' />
                    <div className='absolute inset-0 flex items-center justify-center bg-black/10 opacity-0 transition-opacity group-hover:opacity-100'>
                      <Button
                        icon={<DeleteOutlined className='text-white' />}
                        onClick={() => handleDeleteImage('mobile')}
                        className='border-0 bg-black/60 hover:bg-black/40'
                      />
                    </div>
                  </div>
                )}
              </div>
            )
          }
        ]}
      />
    )
  }

  return (
    <Form form={form} layout='vertical' initialValues={initialValues} onFinish={handleSubmit} style={{ rowGap: 10 }}>
      <CustomizeButtonLink
        label={showOfType === 'Banner' ? 'Banner link' : 'Link ảnh'}
        arrayName='images'
        propertyName='href'
        index={index}
        maxLength={500}
      />

      <div className='my-2'>
        <Radio.Group
          value={values[screenType]?.images[index]?.imageSelectType || 'library'}
          onChange={e =>
            setProp((props: any) => {
              props[screenType].images[index].imageSelectType = e.target.value
            })
          }
        >
          <Space direction='vertical'>
            <Radio value='upload'>Upload ảnh</Radio>
            <Radio value='library'>Chọn ảnh từ thư viện</Radio>
          </Space>
        </Radio.Group>
      </div>

      <div className='mb-4'>
        {values[screenType]?.images[index]?.imageSelectType === 'upload'
          ? renderImageUploadContent()
          : renderLibraryContent()}
      </div>

      <div className='mb-2'>
        <CustomizeImageFit label='Image fit' arrayName='images' propertyName='imageFit' index={index} />
      </div>

      {showOfType !== 'Banner' && (
        <div className='mb-2'>
          <CustomizeImageSize label='Image size' arrayName='images' propertyName='imageSize' index={index} />
        </div>
      )}

      {initialValues?.imageFit === 'fit' && (
        <div className='mb-2'>
          <CustomizeTextAlign label='Image align' arrayName='images' propertyName='imageAlign' index={index} />
        </div>
      )}

      {showOfType === 'Banner' && (
        <div className='mb-4'>
          <CustomizeTextAlign label='Content align' arrayName='images' propertyName='contentAlign' index={index} />
        </div>
      )}

      <CustomizeBackgroundColor
        label='Background color'
        arrayName='images'
        propertyName='backgroundColor'
        index={index}
      />

      <CustomizeSEO label='SEO' arrayName='images' propertyName='seo' index={index} />

      {showOfType === 'Banner' && (
        <div className='mt-4'>
          <CustomizeContentBanner indexEdit={index} />
        </div>
      )}

      <div className='flex w-full justify-between'>
        {totalImages >= 2 && onDelete && (
          <Button className='w-full' danger icon={<DeleteOutlined />} onClick={onDelete}>
            Xóa banner
          </Button>
        )}
        {type === 'add' && (
          <div className='mt-6 flex w-full gap-2'>
            <Button className='w-1/2' type='primary' htmlType='submit'>
              {submitText}
            </Button>
            <Button className='w-1/2' onClick={onCancel}>
              Hủy
            </Button>
          </div>
        )}
      </div>

      <ImageLibraryModal
        visible={isLibraryModalVisible}
        onClose={() => setIsLibraryModalVisible(false)}
        onSelectImage={handleLibraryImageSelect}
        type='image'
      />
    </Form>
  )
}

export default CustomizeImageSlide
