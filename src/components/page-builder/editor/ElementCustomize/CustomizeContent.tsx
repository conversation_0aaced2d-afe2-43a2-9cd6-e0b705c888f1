import React from 'react'

import { Input } from 'antd'
import type { TextAreaProps } from 'antd/es/input'

import { SyncPropertyButton } from './SyncPropertyButton'
import { useCraftJsProp, type UseCraftJsPropOptions } from '@/hooks'

export const CustomizeContent: React.FC<UseCraftJsPropOptions> = ({
  arrayName = '',
  propertyName = 'text',
  index,
  label = 'Content',
  defaultValue = 'text'
}) => {
  const { value, updateValue } = useCraftJsProp({
    arrayName,
    propertyName,
    index,
    defaultValue
  })

  const displayValue =
    value === null || value === undefined ? '' : typeof value === 'object' ? JSON.stringify(value) : String(value)

  const handleChange: TextAreaProps['onChange'] = e => {
    const formattedValue = e.target.value
      .replace(/ {2,}/g, match => '&nbsp;'.repeat(match.length))
      .replace(/\n/g, '<br>')

    updateValue(formattedValue)
  }

  const textareaValue = displayValue
    .replace(/&nbsp;/g, ' ')
    .replace(/<br>/g, '\n')
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'")

  return (
    <div className='flex flex-col gap-2'>
      <div className='flex items-center gap-2'>
        <label className='block'>{label}</label>
        <SyncPropertyButton propertyName={propertyName} defaultValue={defaultValue} newValue={value} />
      </div>
      <Input.TextArea
        value={textareaValue}
        onChange={handleChange}
        autoSize={{ minRows: 1, maxRows: 20 }}
        placeholder='Nhập nội dung'
      />
    </div>
  )
}
