import React from 'react'

interface TableEmptyProps {
  icon?: React.ReactNode
  title?: string
  description?: string
}

export const TableEmpty: React.FC<TableEmptyProps> = ({
  icon,
  title = 'Danh sách trống',
  description = 'Không có dữ liệu mã loại trạng thái'
}) => {
  return (
    <div className='flex flex-col items-center justify-center py-12'>
      {icon || <i className='onedx-empty-table size-40' />}
      <div className='headline-16-semibold text-gray-11'>{title}</div>
      <div className='body-14-regular mt-1 text-gray-8'>{description}</div>
    </div>
  )
}

export default TableEmpty
