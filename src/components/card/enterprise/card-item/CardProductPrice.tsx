import React from 'react'

import classNames from 'classnames'

import { formatCurrency } from '@/constants/commonFunction'

import { If } from '@/components/common'

export const CardProductPrice: React.FC<{
  className?: string
  name: string
  price?: string | number
  priceFrom?: string | number
  priceAfterCoupon?: string | number
  discount?: any
  isActive?: boolean
  includedVAT?: boolean
  discountCombo?: any
  typeCard?: string
  displayPromotion?: boolean
  isDetailPricing?: boolean
}> = ({
  price,
  priceFrom,
  priceAfterCoupon,
  discount,
  className,
  name,
  isActive,
  includedVAT,
  discountCombo,
  typeCard,
  displayPromotion = true,
  isDetailPricing
}) => {
  const displayPrice =
    typeCard === 'solution'
      ? formatCurrency(priceAfterCoupon)
      : typeCard === 'pricing-solution'
        ? formatCurrency(priceFrom)
        : price || 0

  return (
    <div>
      <div className={classNames(`flex items-baseline gap-2 ${typeCard === 'solution' && 'h-[48px]'}`, className)}>
        {typeCard !== 'solution' && <div className='whitespace-nowrap'>{name}</div>}

        <div
          className={classNames(
            'relative truncate flex items-baseline text-headline-20 font-semibold text-text-success-strong ',
            isActive ? 'text-white' : 'text-black ',
            isDetailPricing && 'group-hover:text-white'
          )}
        >
          <span className='text-caption-12 font-normal' style={{ lineHeight: 1, transform: 'translateY(-12px)' }}>
            ₫
          </span>
          <>{displayPrice}</>
        </div>

        {typeCard === 'solution' && (
          <div className='font-inter text-xs font-normal leading-4 tracking-[0.75%] text-[#02173C40] line-through group-hover:text-white'>
            ₫{formatCurrency(priceFrom)}
          </div>
        )}

        <If condition={displayPromotion}>
          <>
            {typeCard === 'combo' && discountCombo && (
              <div className='flex -translate-y-1 items-start rounded-lg bg-bg-error-light px-2 py-1 text-caption-12 font-medium text-text-error-strong'>
                {discountCombo}
              </div>
            )}

            {typeCard !== 'combo' && discount && (
              <div className='flex -translate-y-1 items-start rounded-lg bg-bg-error-light px-2 py-1 text-caption-12 font-medium text-text-error-strong'>
                {discount}
              </div>
            )}
          </>
        </If>
      </div>
      {!includedVAT && (
        <div className={`text-xs font-normal ${isDetailPricing && 'group-hover:text-white'}`}>
          {typeCard !== 'solution' ? (
            <div className='whitespace-nowrap'>(Giá trên chưa bao gồm VAT)</div>
          ) : (
            <div className='font-inter text-xs font-normal leading-4 tracking-[0.75%] text-gray-6 group-hover:text-white'>
              (Giá trên đã bao gồm tất cả sản phẩm, chưa bao gồm thuế VAT)
            </div>
          )}
        </div>
      )}
    </div>
  )
}
