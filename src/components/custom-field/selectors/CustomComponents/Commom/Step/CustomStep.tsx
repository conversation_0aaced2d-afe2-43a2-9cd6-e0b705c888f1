'use client'

import type { ReactNode } from 'react'

import { useRouter } from 'next/navigation'

import clsx from 'clsx'

import { ArrowLeftOutlined } from '@ant-design/icons'
import { useNode } from '@craftjs/core'
import { Button, Form, Typography } from 'antd'

import { useDispatch } from 'react-redux'

import { CF_DATA_TYPES, commonProps } from '@/constants/custom-field'
import { updateInfoCustomField } from '@/redux-store/slices/customFieldSlice'

interface StepItem {
  key: string
  title: string
}

interface StepProps {
  canAction: boolean
  showLabel?: boolean
  label?: string
  formControlName?: string
  valueSelectView?: string
  children?: ReactNode
  items?: StepItem[]
  title?: string
  backUrl?: string
  current?: number
  onChange?: (step: number) => void
}

const defaultProps = {
  ...commonProps,
  canAction: false,
  showLabel: true,
  label: 'Label',
  formControlName: '',
  valueSelectView: 'bold',
  items: [
    { key: '1', title: 'Thông tin chung' },
    { key: '2', title: '<PERSON><PERSON>ế<PERSON> thể' },
    { key: '3', title: 'Bảng giá' },
    { key: '4', title: 'Thông tin bổ sung' },
    { key: '5', title: 'Cấu hình dịch vụ' }
  ],
  title: 'Tạo sản phẩm',
  backUrl: '/product-catalog/list',
  current: 0
}

const { Title } = Typography

export const CustomStep = (props: Partial<StepProps>) => {
  const {
    children,
    items = defaultProps.items,
    title = defaultProps.title,
    current = defaultProps.current,
    backUrl = defaultProps.backUrl,
    onChange
  } = props

  const {
    connectors: { connect, drag }
  } = useNode()

  const router = useRouter()

  const form = Form.useFormInstance()
  const dispatch = useDispatch()

  // Hàm xử lý điều hướng khi nhấn nút quay lại
  const handleNavigation = () => {
    form.resetFields()
    dispatch(
      updateInfoCustomField({
        currentStepCreateService: 0,
        isServiceCreatePricing: false
      })
    )
    router.push(backUrl)
  }

  return (
    <div
      ref={(ref: HTMLDivElement | null) => {
        if (ref) {
          connect(drag(ref))
        }
      }}
      className={clsx('size-full bg-gray-100 p-6')}
    >
      <div className='flex items-center justify-between rounded-lg bg-bg-surface p-5'>
        <div className='flex flex-1 gap-x-3'>
          <Button type='text' icon={<ArrowLeftOutlined />} onClick={handleNavigation} />
          <Title level={4} style={{ margin: 0 }}>
            {title}
          </Title>
        </div>
        <div className='flex justify-end'>
          <div className='flex items-center'>
            {items.map((step, index) => {
              const isActive = index === current
              const isCompleted = index < current
              const isClickable = typeof onChange === 'function'

              return (
                <>
                  <div
                    key={step.key}
                    className={`flex min-w-0 items-center justify-center ${isClickable ? 'cursor-pointer hover:opacity-80' : ''}`}
                    onClick={() => {
                      if (isClickable) onChange?.(index)
                    }}
                  >
                    <div className='flex items-center'>
                      <div
                        className={`caption-12-medium mr-2 flex size-7 shrink-0 items-center justify-center rounded-full ${
                          isActive || isCompleted ? 'bg-bg-primary-default text-white' : 'bg-gray-1 text-black'
                        }`}
                      >
                        {index + 1}
                      </div>
                      <span className='caption-12-medium truncate'>{step.title}</span>
                    </div>
                  </div>
                  {index < items.length - 1 && <i className='onedx-chevron-right mx-3 size-4 text-main' />}
                </>
              )
            })}
          </div>
        </div>
      </div>

      {/* Hiển thị nội dung của step hiện tại */}
      <div className='h-6 bg-gray-100' />
      {children}
    </div>
  )
}

CustomStep.craft = {
  displayName: CF_DATA_TYPES.CONTAINER,
  props: defaultProps,
  rules: {
    canDrag: () => true,
    canMoveIn: () => true
  }
}
