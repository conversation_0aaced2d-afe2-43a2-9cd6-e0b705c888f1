import { PreventNavigation } from '@/utils/page-builder/PreventNavigation'
import Schemas from '@/utils/SEO/Schemas'
import { CreateService } from '@/views/custom-field/editor/pages/CreateService'
import { ServiceDetail } from '@/views/custom-field/editor/pages/ServiceDetail'
import { AdditionalInformation } from '@/views/custom-field/editor/sections/create-service/additional-information'
import {
  ServiceClassify,
  ServiceGeneralInformation,
  ServiceSnapshots,
  ServiceSpecifications,
  ServiceTechnologies
} from '@/views/custom-field/editor/sections/create-service/general-information'
import { ServiceFeatures } from '@/views/custom-field/editor/sections/create-service/general-information/ServiceFeatures'
import { AddPriceList } from '@/views/custom-field/editor/sections/create-service/price-list'
import { PricingTable } from '@/views/custom-field/editor/sections/create-service/price-list/PricingTable'
import { ConfigMultiSubscription } from '@/views/custom-field/editor/sections/create-service/service-config/Config'
import { PaymentMethod } from '@/views/custom-field/editor/sections/create-service/service-config/PaymentMethod'
import { RelatedProductsSection } from '@/views/custom-field/editor/sections/create-service/service-config/related-product'
import { SEOSection } from '@/views/custom-field/editor/sections/create-service/service-config/seo'
import { TopicList } from '@/views/custom-field/editor/sections/create-service/service-config/Topic'
import { ActivityHistoryService } from '@/views/custom-field/editor/sections/service-detail/activity-history'
import { PriceListHistory } from '@/views/custom-field/editor/sections/service-detail/price-list/history'
import {
  Configuration,
  GeneralInformationDetail,
  Installation,
  InventoryDetails,
  PaymentMethodDetail,
  PriceList as PriceListDetail,
  SetupCost,
  ShippingAndDelivery,
  TechnicalSpecification,
  VariantAttribute,
  WarrantyReturnPolicy
} from '@/views/custom-field/editor/sections/service-detail/processing-information'
import { ServiceSupport } from '@/views/custom-field/editor/sections/service-detail/support/ServiceSupport'
import {
  GeneralInformation,
  PricingConfig,
  ServiceConfiguration,
  Variant
} from '@/views/custom-field/editor/steps/create-service'

import {
  ActivityHistory,
  AttributeDetail,
  Configuration as ConfigurationTab,
  ServiceInfo,
  Support,
  VariantDetail
} from '@/views/custom-field/editor/tabs/service-detail'

import { FooterBuilder } from '@components/custom-field/editor/CustomComponents/FooterBuilder'
import { Header } from '@components/custom-field/editor/CustomComponents/HeaderBuilder'
import { PricingDetail } from '@views/custom-field/editor/pages/PricingDetail'

import { CreatePricing } from '@views/custom-field/editor/pages/CreatePricing'
import {
  ClassifyInfo,
  ConfigInfo,
  FeaturesInfo,
  GeneralInfo,
  InstallationConfigInfo,
  LayoutsInfo,
  OutstandingTechnologyInfo,
  PaymentMethodInfo,
  PriceInfo,
  ProductSuggestionsInfo,
  SEOConfigInfo,
  SetupFeeInfo,
  SpecificationInfo,
  TaxInfo,
  TopicInfo,
  VariantAttributeInfo,
  UpdateReason,
  Inventory
} from '@views/custom-field/editor/sections/service-detail'
import { ServiceConfig } from '@views/custom-field/editor/sections/service-detail/config'
import {
  PricingInfoAddon,
  PricingInfoCondition,
  PricingInfoConfig,
  PricingInfoFrame,
  PricingInfoGeneral,
  PricingInfoPlan,
  PricingInfoReason,
  PricingInfoScreen,
  PricingInfoSEO,
  PricingInfoTab,
  PricingInfoFee,
  PricingInfoFeature
} from '@views/custom-field/editor/sections/service-detail/pricing-detail'
import { Avatar } from './Avatar'
import { Checkbox } from './Checkbox'
import { Column } from './Column'
import { Container } from './Container'
import { CustomStep, CustomTab, SubscriptionTabs } from './CustomComponents'
import { DatePicker } from './DatePicker'
import { DateTimePicker } from './DateTimePicker'
import { DropdownList } from './DropdownList'
import { Email } from './Email'
import { FileUpload } from './File'
import { ImageUpload } from './Image'
import { InputTag } from './InputTag'
import { Label } from './Label'
import { MultiLineText } from './MultiLineText'
import { MultiSelect } from './MultiSelect'
import { Radiobox } from './Radiobox'
import { Section } from './Section'
import { SingleLineText } from './SingleLineText'
import { Switch } from './Switch'
import { Text } from './Text'
import { Timestamp } from './Timestamp'
import { Unit } from './Unit'
import { Url } from './Url'
import { User } from './User'
import { Video } from './Video'
import { PricingConfiguration, CreatePricingElement } from '@/views/custom-field/editor/steps/create-pricing'
import {
  FeatureList,
  SetupInformation
} from '@/views/custom-field/editor/sections/service-detail/price-list/create-service-package'

export const resolver = {
  // region Selector
  Container,
  Text,
  DatePicker,
  DateTimePicker,
  Checkbox,
  Email,
  Header,
  FooterBuilder,
  Section,
  Column,
  Switch,
  User,
  Video,
  InputTag,
  SingleLineText,
  MultiLineText,
  Avatar,
  Label,
  Unit,
  FileUpload,
  ImageUpload,
  Number,
  Radiobox,
  Url,
  Timestamp,
  DropdownList,
  MultiSelect,

  // start custom components
  CustomStep,
  CustomTab,
  SubscriptionTabs,
  // end custom component
  // endregion

  // region pages
  CreateService,
  CreatePricing,
  ServiceDetail,
  PricingDetail,
  AdditionalInformation,
  PaymentMethod,
  ConfigMultiSubscription,
  SEOSection,
  TopicList,
  RelatedProductsSection,
  // processingInfor
  ShippingAndDelivery,
  TechnicalSpecification,
  GeneralInformationDetail,
  InventoryDetails,
  Installation,
  Configuration,
  PaymentMethodDetail,
  WarrantyReturnPolicy,
  VariantAttribute,
  SetupCost,
  PriceListDetail,

  PricingTable,

  // endregion

  // region sections
  // Add your sections here
  ServiceClassify,
  ServiceGeneralInformation,
  ServiceSpecifications,
  ServiceFeatures,
  ServiceSnapshots,
  ServiceTechnologies,
  ClassifyInfo,
  ConfigInfo,
  GeneralInfo,
  PaymentMethodInfo,
  OutstandingTechnologyInfo,
  LayoutsInfo,
  FeaturesInfo,
  ProductSuggestionsInfo,
  UpdateReason,
  TopicInfo,
  SEOConfigInfo,
  VariantAttributeInfo,
  PriceInfo,
  SetupFeeInfo,
  SpecificationInfo,
  TaxInfo,
  InstallationConfigInfo,
  ServiceSupport,
  ServiceConfiguration,
  AddPriceList,
  ActivityHistoryService,
  PricingInfoScreen,
  PricingInfoTab,
  PricingInfoAddon,
  PricingInfoCondition,
  PricingInfoFrame,
  PricingInfoGeneral,
  PricingInfoPlan,
  PricingInfoFee,
  PricingInfoFeature,
  PricingInfoConfig,
  PricingInfoSEO,
  PricingInfoReason,
  ServiceConfig,
  PriceListHistory,
  SetupInformation,
  FeatureList,
  Inventory,
  // endregion

  // region steps
  GeneralInformation,
  Variant,
  PricingConfig,
  CreatePricingElement,
  PricingConfiguration,
  // endregion

  // region tabs
  ServiceInfo,
  Support,
  ConfigurationTab,
  ActivityHistory,
  AttributeDetail,
  VariantDetail,
  // endregion

  //utils
  PreventNavigation,
  Schemas
}
