import type { FC } from 'react'

import React from 'react'

import { usePara<PERSON>, useRouter } from 'next/navigation'

import { Button, Tabs, Tag } from 'antd'

import { ArrowLeftOutlined } from '@ant-design/icons'

import classNames from 'classnames'

import type { LogisticCarrier } from '@/types/logistic/carrier'
import GeneralInfor from './detail/GeneralInfo'
import EndpointConfigList from './detail/EndpointConfigList'
import History from './detail/History'

interface CarrierDetailsProps {
  carrier: LogisticCarrier
}

const STATUS_STYLES: Record<'ACTIVE' | 'INACTIVE' | 'DRAFT', { label: string; bg: string; text: string }> = {
  ACTIVE: {
    label: 'Hoạt động',
    bg: 'bg-bg-success-light',
    text: 'text-bg-success-default'
  },
  INACTIVE: {
    label: 'Không hoạt động',
    bg: 'bg-gray-200',
    text: 'text-bg-gray-500'
  },
  DRAFT: {
    label: '<PERSON><PERSON><PERSON> nháp',
    bg: 'bg-bg-warning-light',
    text: 'text-bg-warning-default'
  }
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const CarrierDetails: FC<CarrierDetailsProps> = ({ carrier }) => {
  const { id } = useParams<{ id: string }>()

  const { label, bg, text } = STATUS_STYLES[carrier.status]

  const router = useRouter()

  const handleNavigation = () => {
    router.push('/carrier/list')
  }

  const stats = [
    { label: 'Tổng số kết nối', value: carrier.requestTotal },
    { label: 'Tổng số lần gọi API', value: carrier.totalApiCalls },
    { label: 'Tỷ lệ thành công', value: carrier.successRate },
    { label: 'Tỷ lệ lỗi', value: carrier.failRate },
    { label: 'Thời gian phản hồi trung bình', value: carrier.timeResponseAvg }
  ]

  const detailTabs = [
    {
      key: 'GENERALINFO',
      label: 'Thông tin chung',
      children: <GeneralInfor id={id} />
    },
    {
      key: 'CONFIG',
      label: 'Cấu hình kết nối API',
      children: <EndpointConfigList />
    },
    {
      key: 'HISTORY',
      label: 'Lịch sử thay đổi',
      children: <History id={id} />
    }
  ]

  return (
    <div className='flex flex-col gap-y-3 bg-gray-100'>
      <div className='flex w-full items-center gap-3 bg-white p-4'>
        <Button type='text' icon={<ArrowLeftOutlined />} className='mr-2' onClick={handleNavigation} />
        <div className='flex truncate text-xl font-semibold text-black'>{carrier.name}</div>
        <Tag
          className={classNames(
            'rounded-md border-none items-center w-36 h-5 flex justify-center text-xs font-medium',
            bg,
            text
          )}
        >
          {label}
        </Tag>
      </div>

      <div className='flex w-full items-center gap-3 self-stretch bg-bg-surface p-4'>
        {stats.map((stat, index) => (
          <React.Fragment key={index}>
            <div className='flex flex-1 flex-col gap-1 rounded-xl px-3 pt-3'>
              <div className='text-sm font-normal leading-tight tracking-tight text-text-neutral-strong'>
                {stat.label}
              </div>
              <div className='flex items-center gap-2'>
                <div className='flex items-start gap-1'>
                  <div className='text-title-36 font-semibold leading-[56px] text-text-neutral-medium'>
                    {stat.value}
                  </div>
                </div>
              </div>
            </div>

            {index < stats.length - 1 && <div className='h-16 w-px bg-border-neutral-light/10' />}
          </React.Fragment>
        ))}
      </div>

      <div className='w-full bg-white'>
        <Tabs
          renderTabBar={
            ((props: any, TabBar: React.ComponentClass) => (
              <div className='rounded-b-2xl bg-white px-8 pb-1 pt-3'>
                <TabBar {...props} />
              </div>
            )) as any
          }
          items={detailTabs.map((item: any) => ({
            forceRender: true,
            style: {
              borderRadius: 12,
              background: '#FFFFFF',
              padding: 20,
              overflow: 'hidden',
              maxWidth: '100%'
            },
            ...item
          }))}
        />
      </div>
    </div>
  )
}

export default CarrierDetails
