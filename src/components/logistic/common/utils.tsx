import { isEqual } from 'lodash'

import dayjs from 'dayjs'
import isBetween from 'dayjs/plugin/isBetween'

dayjs.extend(isBetween)

import type { ColumnsType } from 'antd/es/table'

import { Form, Select } from 'antd'

import type {
  ApiConfig,
  CarrierActionList,
  CarrierApiConfig,
  CreateCarrierApiConfigDto,
  CreateLogisticCarrierDto,
  ErrorMapping,
  UpdateCarrierApiConfigDto,
  UpdateLogisticCarrierDto
} from '@/types/logistic/carrier'
import { convertConfigApi } from '@/views/inventory/common/hooks/useConvertAndValidation'
import { message } from '@/components/notification'
import { compareStringWithVietnamese, filterOptionTrim } from '@/utils/string'
import { API_STATUS_ENUM, AUTH_TYPE, errorMappingOptions, HTTP_METHOD } from './constant'
import type { CheckedState } from '@/components/filter'
import { DATE_FORMATS } from '@/utils/date'

// #region Functions
/**
 * @description: Hàm phân tích URL để tách các tham số và biến đường dẫn
 * @example: parseUrl('https://api.example.com/users/:userId/posts/:postId?page=1&limit=10')
 * @returns: { params: Array<{ key: string, value: string }>, pathVariables: Array<{ key: string, value: string }> }
 * @param {string} url
 * @typedef {Object} KeyValue
 * @description: Hàm này sẽ phân tích URL và trả về danh sách các tham số và biến đường dẫn.
 * Các biến đường dẫn thường được đánh dấu bằng dấu hai chấm (:) hoặc dấu ngoặc nhọn ({}).
 * Các tham số truy vấn sẽ được tách ra từ phần query string của URL.
 * Ví dụ: với URL 'https://api.example.com/users/:userId/posts/:postId?page=1&limit=10',
 * hàm sẽ trả về:
 * {
 *   params: [
 *     { key: 'page', value: '1' },
 *     { key: 'limit', value: '10' }
 *   ],
 *   pathVariables: [
 *     { key: 'userId', value: ':userId' },
 *     { key: 'postId', value: ':postId' }
 *   ]
 * }
 */
export const parseUrl = (
  url: string,
  options?: {
    hiddenValuesParams?: boolean
  }
) => {
  try {
    const urlObj = new URL(url)

    const params: { key: string; value?: string }[] = []

    urlObj.searchParams.forEach((value, key) => {
      const paramsValues = options?.hiddenValuesParams ? undefined : value // Ẩn giá trị nếu được cấu hình

      params.push({ key, value: paramsValues })
    })

    const pathVariables: { key: string; value?: string }[] = []
    const pathParts = urlObj.pathname.split('/').filter(Boolean)

    pathParts.forEach(part => {
      if (part.startsWith(':') || (part.startsWith('{') && part.endsWith('}'))) {
        const key = part.replace(/^[:{}]+|[{}]+$/g, '')

        pathVariables.push({
          key,
          value: undefined
        })
      }
    })

    return {
      params,
      pathVariables
    }
  } catch (error) {
    return {
      params: [],
      pathVariables: []
    }
  }
}

export const validateDuplicateConnectItem = ({
  data,
  extra,
  enabled = true
}: {
  data: any
  extra?: any
  enabled?: boolean
}): boolean => {
  if (!enabled) {
    return true
  }

  const { connectList } = extra

  if (!connectList || connectList.length === 0) {
    return true
  }

  // Validate nếu có action trùng lặp
  const duplicateAction = connectList.some((item: any) => item.action === data.action)

  if (duplicateAction) {
    message.error('Kết nối API đã tồn tại với action này. Vui lòng chọn action khác.')

    return false
  }

  return true
}

export const handleConvertConnectItem = (data: any): CreateCarrierApiConfigDto | UpdateCarrierApiConfigDto => {
  const apiConfig: ApiConfig = {
    method: data.apiConfig?.method || HTTP_METHOD.GET,
    url: {
      raw: data.apiConfig?.url.raw,
      variables: data.pathVariables || []
      // parameters: data.params || []
    },
    headers: data.headers || [],
    auth: {
      type: data.apiConfig.auth?.type || AUTH_TYPE.NONE,
      apiKey: data.apiConfig.auth?.apiKey,
      basic: data.apiConfig.auth?.basic,
      bearer: data.apiConfig.auth?.bearer
    },
    securities: data.apiConfig.securities || {}
    // additionalParams: data.apiConfig.additionalParams || {}
  }

  const detailConfigApi = convertConfigApi(data)

  // Thêm thời gian cập nhập
  const modifiedAt = dayjs().format(DATE_FORMATS.DD_MM_YY_HH_MM_SS)

  return {
    id: data.id,
    state: data.state ?? API_STATUS_ENUM.READY_FOR_TEST, // Mặc định là READY_FOR_TEST nếu ở trường hợp tạo mới
    action: data.action,
    actionName: data.actionName,
    modifiedAt,
    apiConfig,
    inputMapping: detailConfigApi.inputMapping,
    outputMapping: detailConfigApi.outputMapping,
    dxReqFields: detailConfigApi.dxReqFields,
    dxRespFields: detailConfigApi.dxRespFields,
    partnerReqFields: detailConfigApi.partnerReqFields,
    partnerRespFields: detailConfigApi.partnerRespFields,
    errorMapping: data.errorMapping || [],
    timeout: data.timeout,
    retryNumber: data.retryNumber,
    retryInterval: data.retryInterval
  }
}

export const validateCarrier = (
  data: CreateLogisticCarrierDto | UpdateLogisticCarrierDto,
  options?: {
    checkActiveStatus?: boolean
    requiredActions?: CarrierActionList[]
    hiddenErrorMessages?: boolean
  }
): {
  isValid: boolean
  type: 'empty' | 'invalid' | 'valid'
  errorMessages: string
  extra: any
} => {
  const displayMessage = (content: string) => {
    if (options?.hiddenErrorMessages) {
      return
    }

    message.error(content)
  }

  // Kiểm tra cơ bản - phải có ít nhất một kết nối API
  if (!data.connectList || data.connectList?.length === 0) {
    displayMessage('Vui lòng thêm ít nhất một kết nối API')

    return {
      isValid: false,
      type: 'empty',
      errorMessages: 'Vui lòng thêm ít nhất một kết nối API',
      extra: {}
    }
  }

  // Kiểm tra các action bắt buộc (nếu có)
  if (options?.checkActiveStatus && options?.requiredActions && options.requiredActions.length > 0) {
    // Trường hợp chưa cấu hình action bắt buộc
    const missingRequiredActions: CarrierActionList[] =
      options.requiredActions.filter(action => !data.connectList?.some(item => item.action === action.code)) || []

    // Trường hợp đã cấu hình nhưng không hoạt động
    const listRequiredCode = options.requiredActions.map(action => action.code)
    const requiredEndpoints = data.connectList.filter(item => listRequiredCode.includes(item.action))

    const inactiveRequiredEndpoints: CarrierActionList[] = []

    options.requiredActions.forEach(action => {
      const endpoint = requiredEndpoints.find(item => item.action === action.code)

      if (endpoint && endpoint.state !== API_STATUS_ENUM.ACTIVE) {
        inactiveRequiredEndpoints.push(action)
      }
    })

    const errorEndpoints = [...missingRequiredActions, ...inactiveRequiredEndpoints]

    if (errorEndpoints.length > 0) {
      return {
        isValid: false,
        type: 'invalid',
        errorMessages: `Không thể bật hoạt động đơn vị vận chuyển`,
        extra: {
          listError: errorEndpoints // Reference Object
        }
      }
    }
  }

  return {
    isValid: true,
    type: 'valid',
    errorMessages: '',
    extra: {}
  }
}

export const convertSubmitData = (data: any): CreateLogisticCarrierDto | UpdateLogisticCarrierDto => {
  return {
    ...data,
    logoUrl: data.logoUrl?.[0]?.url || undefined
  }
}

export const copyObjectToClipboard = (obj: any) => {
  const text = JSON.stringify(obj, null, 2) // Chuyển đổi đối tượng thành chuỗi JSON với định dạng đẹp

  if (navigator && navigator.clipboard && typeof navigator.clipboard.writeText === 'function') {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        console.log('Đã sao chép đối tượng vào clipboard:', text)
      })
      .catch(err => {
        console.error('Lỗi khi sao chép đối tượng vào clipboard:', err)
      })
  } else {
    console.error('Clipboard API không khả dụng trên trình duyệt này.')
  }
}

const sortConnectList = (a: any, b: any) => {
  // ModifiedAt is newest
  const timeA = a.modifiedAt ? dayjs(a.modifiedAt, DATE_FORMATS.DD_MM_YY_HH_MM_SS).valueOf() : 0
  const timeB = b.modifiedAt ? dayjs(b.modifiedAt, DATE_FORMATS.DD_MM_YY_HH_MM_SS).valueOf() : 0

  return timeB - timeA
}

// Filter ApiConfigTable DataSource
export const handleFilterDataSource = ({
  carrierActionList,
  data,
  advancedSearch,
  filter
}: {
  carrierActionList?: CarrierActionList[]
  data: CarrierApiConfig[]
  advancedSearch: {
    search: string
    selector: CheckedState
  }
  filter: Record<string, any>
}) => {
  // Preprocess data
  const newData = data.map(item => {
    const carrierAction = carrierActionList?.find(action => action.code === item.action)

    return {
      ...item,
      actionName: carrierAction?.name || item.action
    }
  })

  // Processing
  return newData
    .filter(item => {
      const { search, selector } = advancedSearch

      // Handle advanced search based on selected fields
      let matchesSearch = true

      if (search.trim()) {
        const searchConditions: boolean[] = []

        // Nếu thêm advancedSearch.mapper vào đây dưới dạng array thì có thể tạo vòng lặp for each
        // Search in action field if isAction is checked
        if (selector.isAction) {
          searchConditions.push(compareStringWithVietnamese(search, item.actionName))
        }

        // Search in endpoint field if isEndpoint is checked
        if (selector.isEndpoint) {
          searchConditions.push(compareStringWithVietnamese(search, item.apiConfig.url.raw))
        }

        // If no selector is checked, dont do anything
        matchesSearch = searchConditions.some(condition => condition)
      }

      // Handle filter conditions
      const matchesFilters = Object.entries(filter).every(([key, value]) => {
        if (!Array.isArray(value) || !value.length) return true

        if (key === 'dateRange') {
          const [startDate, endDate] = value
          const itemDate = dayjs(item.modifiedAt, DATE_FORMATS.DD_MM_YY_HH_MM_SS)

          const start = dayjs(startDate).startOf('day')
          const end = dayjs(endDate).endOf('day')

          return itemDate.isBetween(start, end, null, '[]')
        }

        // Handle nested object properties like 'apiConfig.method'
        if (key.includes('.')) {
          const keys = key.split('.')
          let itemValue = item as any

          for (const k of keys) {
            itemValue = itemValue?.[k]
          }

          return value.includes(itemValue)
        }

        return value.includes((item as any)[key])
      })

      return matchesSearch && matchesFilters
    })
    .sort(sortConnectList)
}

export const validateDataMapping = (data: any) => {
  const requiredRequestItems = data.requestObject.filter((item: any) => item.mandatory === true)
  const requiredResponseItems = data.responseObject.filter((item: any) => item.mandatory === true)

  if (data.isVisibleRequestObject && data.isRequiredRequestObject) {
    if (data.dropdownRequestObject.length === 0) {
      message.error('Dữ liệu không được bỏ trống')

      return false
    }

    if (
      requiredRequestItems?.length &&
      requiredRequestItems.some((item: any) => item.sourceField == null || item.sourceField == undefined)
    ) {
      message.error('Vui lòng chuyển đổi các trường dữ liệu')

      return false
    }
  }

  if (data.isVisibleResponseObject && data.isRequiredResponseObject) {
    if (data.dropdownResponseObject.length === 0) {
      message.error('Dữ liệu không được bỏ trống')

      return false
    }

    if (
      requiredResponseItems?.length &&
      requiredResponseItems.some((item: any) => item.sourceField == null || item.sourceField == undefined)
    ) {
      message.error('Vui lòng chuyển đổi các trường dữ liệu')

      return false
    }
  }

  return true
}
// #endregion

// #region ShouldUpdate
/**
 * Tạo shouldUpdate function để kiểm tra thay đổi của các fields cụ thể
 * @param fields - Mảng các field paths cần kiểm tra
 * @returns Function để sử dụng trong shouldUpdate
 */
export const createShouldUpdate = (fields: string[]) => {
  return (prevValues: any, currentValues: any) => {
    return fields.some(field => {
      const prevVal = field.split('.').reduce((obj, key) => obj?.[key], prevValues)
      const currentVal = field.split('.').reduce((obj, key) => obj?.[key], currentValues)

      return !isEqual(prevVal, currentVal)
    })
  }
}

// Các shouldUpdate functions cụ thể cho từng use case
export const shouldUpdateApiConfig = createShouldUpdate(['apiConfig.url.raw', 'params', 'pathVariables'])

export const shouldUpdateAuthType = createShouldUpdate(['apiConfig.auth.type'])

export const shouldUpdateDataMapping = createShouldUpdate([
  'action',
  'key',
  'apiConfig.method',
  'inputMapping',
  'outputMapping'
  // 'errorMapping'
])

export const shouldUpdateErrorMapping = createShouldUpdate(['errorMapping', 'dropdownResponseObject'])

export const shouldUpdateConnectList = createShouldUpdate(['connectList'])

// Hoặc tạo một object chứa tất cả các shouldUpdate functions
export const shouldUpdateFields = {
  apiConfig: shouldUpdateApiConfig,
  authType: shouldUpdateAuthType,
  dataMapping: shouldUpdateDataMapping,
  errorMapping: shouldUpdateErrorMapping,
  connectList: shouldUpdateConnectList,

  // Có thể thêm custom fields
  custom: (fields: string[]) => createShouldUpdate(fields)
} as const
// #endregion

// #region Table Columns
export const errorMappingColumns: ColumnsType<ErrorMapping> = [
  {
    title: 'HTTP',
    dataIndex: 'httpStatusCode',
    key: 'httpStatusCode'
  },
  {
    title: 'Mã lỗi (error_code)',
    dataIndex: 'partnerCode',
    key: 'partnerCode'
  },
  {
    title: 'Thông điệp (error_message)',
    dataIndex: 'note',
    key: 'note'
  },
  {
    title: 'Trạng thái (Loại lỗi Mapping Marketplace)',
    dataIndex: 'bosCode',
    key: 'bosCode',
    // Dropdown Form
    render: (_, record, index) => (
      <Form.Item name={['errorMapping', index, 'bosCode']} noStyle>
        <Select
          options={errorMappingOptions}
          placeholder='Chọn trạng thái'
          style={{ width: '100%' }}
          filterOption={filterOptionTrim}
        />
      </Form.Item>
    )
  }
]
// #endregion
