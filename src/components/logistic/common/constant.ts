import type { MenuProps } from 'antd'

import type { ApiStatus, CarrierAction, CarrierStatus, ErrorMapping } from '@/types/logistic/carrier'
import type { LogisticTagColor } from './LogisticComponents'
import { baseColorLight } from '@/utils/colors'

export const STATUS_OPTIONS = [
  {
    label: 'Bản nháp',
    value: 'UNSET'
  },
  {
    label: 'Hoạt động',
    value: 'ACTIVE'
  },
  {
    label: 'Không hoạt động',
    value: 'INACTIVE'
  }
]

export const tagStatus = {
  ACTIVE: {
    color: '#D2F9E2',
    textColor: '#07945F',
    text: 'Đang hoạt động',
    value: 'ACTIVE'
  },
  INACTIVE: {
    color: '#F2F4F9',
    textColor: '#394867',
    text: 'Vô hiệu hóa',
    value: 'INACTIVE'
  }
}

export const checkboxOptions = [
  {
    label: 'Mã định danh',
    value: 'code',
    status: 'disabled'
  },
  {
    label: 'Tên đơn vị vận chuyển',
    value: 'name',
    status: 'disabled'
  },
  {
    label: 'Mô tả',
    value: 'description',
    status: 'disabled'
  },
  {
    label: 'Trạng thái',
    value: 'status',
    status: 'disabled'
  },
  {
    label: 'Thông tin liên hệ kỹ thuật',
    value: 'supportEmail',
    status: 'disabled'
  },
  {
    label: 'Thời gian cập nhật',
    value: 'modifiedAt'
  },
  {
    label: 'Người tạo',
    value: 'createdBy'
  }
]

export const statusOptions: {
  label: string
  value: CarrierStatus
}[] = [
  { label: 'Hoạt động', value: 'ACTIVE' },
  { label: 'Không hoạt động', value: 'INACTIVE' }
]

export const stylesCard = {
  header: {
    backgroundColor: baseColorLight['gray-alpha-12'],
    borderTopLeftRadius: '8px',
    borderTopRightRadius: '8px'
  },
  body: {
    backgroundColor: baseColorLight['gray-alpha-12'],
    borderRadius: '0px 0px 8px 8px'
  }
}

export const HTTP_METHOD = {
  GET: 'GET',
  POST: 'POST',
  PUT: 'PUT',
  PATCH: 'PATCH',
  DELETE: 'DELETE'
} as const
// Export Type ngay đây để thực hiện chức năng giống enum
export type HTTP_METHOD = (typeof HTTP_METHOD)[keyof typeof HTTP_METHOD]

export const AUTH_TYPE = {
  NONE: 'NONE',
  BASIC: 'BASIC',
  BEARER: 'BEARER',
  API_KEY: 'API_KEY'
} as const

export type AUTH_TYPE = (typeof AUTH_TYPE)[keyof typeof AUTH_TYPE]

export const TIME_UNIT = {
  SECOND: 's',
  MINUTE: 'm',
  HOUR: 'h'
} as const

export type TIME_UNIT = (typeof TIME_UNIT)[keyof typeof TIME_UNIT]

// Cập nhật lại các constant sử dụng enum
export const methodOptions = [
  { label: 'GET', value: HTTP_METHOD.GET },
  { label: 'POST', value: HTTP_METHOD.POST },
  { label: 'PUT', value: HTTP_METHOD.PUT },
  { label: 'PATCH', value: HTTP_METHOD.PATCH },
  { label: 'DELETE', value: HTTP_METHOD.DELETE }
]

export const authTypeOptions = [
  { label: 'No Auth', value: AUTH_TYPE.NONE },
  // { label: 'Xác thực cơ bản', value: AUTH_TYPE.BASIC },
  { label: 'Bearer Token', value: AUTH_TYPE.BEARER },
  { label: 'API Key', value: AUTH_TYPE.API_KEY }
]

export const timeOptions = [
  { label: 'Giây', value: TIME_UNIT.SECOND },
  { label: 'Phút', value: TIME_UNIT.MINUTE }
  // { label: 'Giờ', value: TimeUnit.HOUR }
]

export const apiConfigListFilter = [
  {
    name: 'apiConfig.method',
    label: 'Phương thức kết nối',
    placeHolder: true,
    placeHolderText: 'Chọn phương thức kết nối',
    multiple: true,
    notSelectFirstValue: true,
    options: methodOptions
  },
  {
    name: 'apiConfig.auth.type',
    label: 'Phương thức xác thực API',
    placeHolder: true,
    placeHolderText: 'Chọn phương thức xác thực',
    multiple: true,
    notSelectFirstValue: true,
    options: authTypeOptions
  }
]

export const ACTION_OPTIONS: {
  label: string
  value: CarrierAction
}[] = [
  { label: 'Tính phí vận chuyển', value: 'CALCULATE_FEE' },
  { label: 'Tạo đơn vận chuyển', value: 'CREATE_ORDER' },
  { label: 'Hủy đơn vận chuyển', value: 'CANCEL_ORDER' },
  { label: 'Lấy trạng thái đơn hàng theo mã vận đơn', value: 'GET_STATUS' },
  { label: 'Định vị kiện hàng theo thời gian thực', value: 'LIVE_TRACKING' },
  { label: 'Xác nhận kiện hàng đã được giao thành công', value: 'CONFIRM_DELIVERY' },
  { label: 'Kiểm tra trạng thái đổi/trả hàng', value: 'RETURN_STATUS' },
  { label: 'Đăng ký dịch vụ thu hộ COD', value: 'REGISTER_COD' },
  { label: 'Kiểm tra trạng thái thu hộ COD', value: 'GET_COD_STATUS' },
  { label: 'Đối soát và chuyển tiền COD', value: 'SETTLE_COD' },
  { label: 'Kiểm tra tồn kho tại kho hàng nhà vận chuyển', value: 'GET_INVENTORY' },
  { label: 'Gửi hàng vào kho trung gian của đơn vị vận chuyển', value: 'STORE_PANEL' },
  { label: 'Đơn vị vận chuyển thực hiện đóng gói & giao hàng thay Marketplace', value: 'FULFILL_ORDER' },
  { label: 'Đăng ký webhook trạng thái vận chuyển', value: 'REGISTER_WEBHOOK' },
  { label: 'Đối soát chi phí vận chuyển với đối tác vận chuyển', value: 'RECONCILE_SHIPPING_FEE' }
]

export const API_STATUS_ENUM = {
  READY_FOR_TEST: 'READY_FOR_TEST', // Đã ánh xạ dữ liệu (mapping request và response) xong và chưa kiểm thử kết nối.
  ACTIVE: 'ACTIVE', // Kết nối đã kiểm thử thành công, hệ thống đang hoạt động.
  INACTIVE: 'INACTIVE', // Kiểm thử thất bại, trả ra kết quả lỗi
  FAILURE: 'FAILURE' // 	Người dùng vô hiệu hóa hoạt động cấu hình. Phải kích hoạt lại thì cấu hình mới hoạt động
  /** @deprecated bắt buộc ánh xạ khi tạo */
  // WAIT_MAPPING = 'WAIT_MAPPING', // Đã bắt đầu quá trình cấu hình, người dùng chỉ cấu hình endpoint và chưa mapping dữ liệu
} as const

// Cập nhật STATE_OPTIONS sử dụng const
export const STATE_OPTIONS: {
  label: string
  value: ApiStatus
  type: LogisticTagColor
}[] = [
  // { label: 'Chờ ánh xạ', value: API_STATUS_ENUM.WAIT_MAPPING, type: 'warning' },
  { label: 'Sẵn sàng kiểm thử', value: API_STATUS_ENUM.READY_FOR_TEST, type: 'primary' },
  { label: 'Hoạt động', value: API_STATUS_ENUM.ACTIVE, type: 'success' },
  { label: 'Không hoạt động', value: API_STATUS_ENUM.INACTIVE, type: 'default' },
  { label: 'Lỗi', value: API_STATUS_ENUM.FAILURE, type: 'error' }
]

// SUCCESS(200), FAILURE(502, 504), INVALID_PARAM(400, 404), FAIL_AUTHENTICATION(401, 403), ERROR_PARTNER(500, 502, 503)
export const errorMappingErrors: {
  [key: string]: {
    label: string
    httpStatusCode: number[]
  }
} = {
  INVALID_PARAM: {
    label: 'Tham số không hợp lệ',
    httpStatusCode: [400, 404]
  },
  FAIL_AUTHENTICATION: {
    label: 'Xác thực thất bại',
    httpStatusCode: [401, 403]
  },
  ERROR_PARTNER: {
    label: 'Lỗi từ đối tác',
    httpStatusCode: [500, 502, 503]
  },
  SUCCESS: {
    label: 'Thành công',
    httpStatusCode: [200]
  },
  FAILURE: {
    label: 'Lỗi hệ thống',
    httpStatusCode: [502, 504]
  }
}

export const errorMappingOptions = [
  { label: 'Tham số không hợp lệ', value: 'INVALID_PARAM' },
  { label: 'Xác thực thất bại', value: 'FAIL_AUTHENTICATION' },
  { label: 'Lỗi từ đối tác', value: 'ERROR_PARTNER' },
  { label: 'Thành công', value: 'SUCCESS' },
  { label: 'Lỗi hệ thống', value: 'FAILURE' }
]

export const errorMappingDataSource: ErrorMapping[] = [
  {
    httpStatusCode: 400,
    partnerCode: 'INVALID_PARAM',
    note: 'Tham số không hợp lệ'
  },
  {
    httpStatusCode: 404,
    partnerCode: 'NOT_FOUND',
    note: 'Không tìm thấy tài nguyên'
  }
]

export const ACTION_BTN_KEY = {
  CHECK_CONNECTION: 'CHECK_CONNECTION',
  DISABLE: 'DISABLE',
  ENABLE: 'ENABLE',
  DELETE: 'DELETE'
} as const

export const actionBtns: MenuProps['items'] = [
  {
    label: 'Kiểm thử kết nối',
    key: ACTION_BTN_KEY.CHECK_CONNECTION
  },
  {
    label: 'Vô hiệu hóa',
    key: ACTION_BTN_KEY.DISABLE
  },
  {
    label: 'Kích hoạt lại',
    key: ACTION_BTN_KEY.ENABLE
  },
  {
    label: 'Xóa cấu hình',
    key: ACTION_BTN_KEY.DELETE
  }
]
export const ALGORITHM_LIST = [
  { label: 'SHA-256', value: 'SHA-256' },
  { label: 'SHA-512', value: 'SHA-512' },
  { label: 'SHA-MD5', value: 'SHA-MD5' }
]

export const systemParamsOptions = [
  {
    label: '$SUBSCRIPTION_CODE',
    value: 'SUBSCRIPTION_CODE'
  },
  {
    label: '$PARTNER_CODE',
    value: 'PARTNER_CODE'
  }
]
