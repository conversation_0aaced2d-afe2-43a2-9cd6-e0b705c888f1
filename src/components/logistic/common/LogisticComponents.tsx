import React from 'react'

import type { FC, PropsWithChildren } from 'react'

import { Button, Empty, Tag } from 'antd'
import classNames from 'classnames'

import type { ButtonType, ButtonVariantType } from 'antd/es/button'

import type { TableLocale } from 'antd/es/table/interface'

import { CustomCollapse } from '@/components/custom-field/common/CustomCollapse'
import { baseColorLight, colors } from '@/utils/colors'
import type { ModalConfirmProps } from '@/components/subscription/modal/ModalConfirm'
import ModalConfirm from '@/components/subscription/modal/ModalConfirm'

// #region Constants
export const localeTable: TableLocale = {
  emptyText: <Empty description={<span>Không có dữ liệu</span>} />
}

// #region LogisticTitle
interface TitleProps {
  children?: React.ReactNode
  className?: string
  extraButtons?: React.ReactNode
  navigation?: {
    goBack?: () => void
    icon?: React.ReactNode
  }
}

export const LogisticTitle: FC<PropsWithChildren<TitleProps>> = ({
  children,
  className = '',
  extraButtons,
  navigation
}) => {
  return (
    <div
      className={`headline-20-semibold flex w-full items-center justify-between bg-bg-surface p-4 text-text-neutral-strong ${className}`}
      style={{
        borderBottom: '1px solid #02173C09'
      }}
    >
      <div className='flex items-center gap-3'>
        <Button
          type='text'
          icon={navigation?.icon || <i className='onedx-chevron-left size-5' />}
          onClick={() => navigation?.goBack && navigation.goBack()}
        />
        {children}
      </div>
      {extraButtons && <div className='ml-2 inline-block'>{extraButtons}</div>}
    </div>
  )
}
// #endregion

// #region LogisticHeader
export const LogisticHeader = ({
  title,
  rootClassname,
  iconClassname,
  textClassname,
  color = 'default',
  type = 'default'
}: {
  title: string | React.ReactNode
  rootClassname?: HTMLDivElement['className']
  iconClassname?: HTMLDivElement['className']
  textClassname?: HTMLDivElement['className']
  color?: 'default' | 'red' | 'green' | 'blue'
  type?: 'default' | 'medium' | 'small'
}) => {
  const colorClass = classNames({
    'bg-icon-warning-default': color === 'default',
    'bg-icon-error-default': color === 'red',
    'bg-icon-success-default': color === 'green',
    'bg-icon-info-default': color === 'blue'
  })

  const typeClass = classNames({
    'h-1 w-1 rounded-full': type === 'small',
    'h-2 w-2 rounded-full': type === 'medium',
    'h-4 w-1 rounded-xs': type === 'default'
  })

  const rootIconClassname = classNames(colorClass, typeClass, iconClassname)

  return (
    <div className={classNames('inline-flex items-center justify-start gap-2', rootClassname)}>
      <div className={rootIconClassname} />
      <div className={classNames('headline-16-medium text-text-neutral-strong', textClassname)}>{title}</div>
    </div>
  )
}
// #endregion

// #region LogisticCard
type LogisticCardProps = Omit<React.ComponentProps<typeof CustomCollapse>, 'label' | 'visibleToolTip'> & {
  title: string
}

export const LogisticCard: FC<PropsWithChildren<LogisticCardProps>> = ({ children, title, styles, ...rest }) => {
  return (
    <CustomCollapse
      label={<LogisticHeader title={title} />}
      visibleToolTip={false}
      className='text-icon-neutral-strong'
      styles={{
        header: {
          backgroundColor: '#fff',
          borderBottom: '1px solid',
          borderColor: '#02173C09',
          borderRadius: 0,
          ...styles?.header
        },
        body: {
          backgroundColor: '#fff',
          borderRadius: 0,
          ...styles?.body
        }
      }}
      {...rest}
    >
      {children}
    </CustomCollapse>
  )
}
// #endregion

// #region LogisticTag
export type LogisticTagColor = 'success' | 'error' | 'warning' | 'primary' | 'default'

export const LogisticTag: FC<{
  children: React.ReactNode
  className?: string
  color?: LogisticTagColor
}> = ({ children, className, color = 'default' }) => {
  const colorMap: Record<
    string,
    {
      background: string
      color: string
    }
  > = {
    success: {
      background: colors.green200,
      color: colors.green700
    },
    error: {
      background: colors.red200,
      color: colors.red700
    },
    warning: {
      background: colors.yellow200,
      color: colors.yellow700
    },
    default: {
      background: baseColorLight['gray-1'],
      color: baseColorLight['gray-8']
    },
    primary: {
      background: colors.primary200,
      color: colors.primary700
    }
  }

  return (
    <Tag
      className={classNames('border-none rounded-md text-center w-full', className)}
      style={{
        background: colorMap[color].background,
        color: colorMap[color].color
      }}
    >
      {children}
    </Tag>
  )
}
// #endregion

// #region LogisticButton
interface LogisticButtonProps extends Omit<React.ComponentProps<typeof Button>, 'type' | 'color' | 'variant'> {
  type?: 'primary' | 'error' | ButtonType
  variant?: 'outlined' | 'solid' | 'text' | 'link'
}

export const LogisticButton: FC<LogisticButtonProps> = ({
  children,
  type = 'primary',
  variant = 'solid',
  className,
  disabled,
  ...rest
}) => {
  switch (variant) {
    case 'outlined':
      const outlinedClass = {
        primary: 'border-primary-blue text-text-primary-blue',
        error: 'border-icon-error-default text-text-error-default',
        disabled: 'border-gray-300 text-gray-300 cursor-not-allowed shadow-none'
      }

      const extraOutlinedType = disabled ? 'disabled' : type

      return (
        <Button
          variant='outlined'
          color='primary'
          className={classNames(outlinedClass[extraOutlinedType as keyof typeof outlinedClass] || '', className)}
          type={type as ButtonType}
          {...rest}
        >
          {children}
        </Button>
      )

    case 'solid':
      const solidClass = {
        primary: 'bg-primary-blue',
        error: 'bg-bg-error-lighter text-text-error-default',
        disabled: 'bg-gray-300 text-white cursor-not-allowed shadow-none'
      }

      const extraSolidType = disabled ? 'disabled' : type

      return (
        <Button
          className={classNames(solidClass[extraSolidType as keyof typeof solidClass] || '', className)}
          type={type as ButtonType}
          {...rest}
        >
          {children}
        </Button>
      )
    default:
      return (
        <Button
          type={type as ButtonType}
          variant={variant as ButtonVariantType}
          className={classNames('text-text-neutral-strong', className)}
          {...rest}
        >
          {children}
        </Button>
      )
  }
}
// #endregion

// #region LogisticModal
export const LogisticModal = ({
  children,
  open,
  setOpen,
  type = 'primary',
  variant,
  icon,
  controlTitle,
  footer
}: {
  children: React.ReactNode
  open: boolean
  setOpen: (open: boolean) => void
  type?: 'primary' | 'error'
  variant?: LogisticButtonProps['variant']
  icon?: React.ReactNode
  controlTitle?: ModalConfirmProps['controlTitle']
  footer: {
    render?: React.ReactNode
    wrapperClassName?: string
    okText?: string
    okFunction?: () => void
    cancelText?: string
    cancelFunction?: () => void
  }
}) => {
  // Add modal related components or functions here if needed
  return (
    <ModalConfirm
      open={open}
      setOpen={setOpen}
      icon={
        icon ||
        (type === 'error' ? <i className='onedx-check-circle size-6' /> : <i className='onedx-refresh size-6' />)
      }
      controlTitle={{
        titleText: type === 'error' ? 'Lỗi' : 'Thông báo',
        iconClassName: classNames({
          'bg-bg-error-lighter text-text-error-default': type === 'error',
          'bg-[#CDE4FE] text-icon-info-default': type === 'primary'
        }),
        ...controlTitle
      }}
      controlFooter={{
        render: (
          <div className={footer.wrapperClassName || 'grid grid-cols-2 items-center justify-center gap-2'}>
            {footer.render || (
              <>
                <LogisticButton
                  variant='outlined'
                  type='primary'
                  onClick={() => {
                    footer.cancelFunction && footer.cancelFunction()
                    setOpen(false)
                  }}
                >
                  {footer.cancelText || 'Hủy'}
                </LogisticButton>
                <LogisticButton
                  type={type}
                  variant={variant}
                  onClick={() => {
                    footer.okFunction && footer.okFunction()
                  }}
                >
                  {footer.okText || 'Xác nhận'}
                </LogisticButton>
              </>
            )}
          </div>
        )
      }}
    >
      {children}
    </ModalConfirm>
  )
}
