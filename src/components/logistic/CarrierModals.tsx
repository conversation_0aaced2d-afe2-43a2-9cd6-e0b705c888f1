import React, { type FC } from 'react'

import { useLocalState, useWatchLocal } from '@/hooks'

import { LogisticButton, LogisticModal } from './common/LogisticComponents'
import type { CarrierAction, CarrierActionList, CarrierApiConfig } from '@/types/logistic/carrier'

type CarrierModals = 'deprecate' | 'reactivate' | 'delete' | 'resend' | 'inactiveStatus'

interface CarrierModalsProps {
  activeModals: CarrierModals[]
  onDeprecate?: (extra: CarrierApiConfig) => void
  onReactivate?: (extra: CarrierApiConfig) => void
  onDelete?: (ids: string[] | string) => void
  onResend?: (extra: CarrierApiConfig) => void
  onInactiveStatus?: (extra: CarrierAction[]) => void
}

// Hook chuyên biệt cho từng use case - tối ưu hiệu suất cao hơn
// export const useDeprecateModal = () => {
//   const { setFieldValue } = useLocalState()
//   const isOpen = useWatchLocal((state: any) => state.modals?.deprecate ?? false)

//   return {
//     isOpen,
//     open: () => setFieldValue(['modals', 'deprecate'], true),
//     close: () => setFieldValue(['modals', 'deprecate'], false)
//   }
// }

// Hook tiện ích để quản lý modal states - tối ưu hiệu suất
export const useCarrierModals = (activeModals: CarrierModals[]) => {
  const { setFieldValue } = useLocalState()

  // Subscribe tất cả states nhưng chỉ sử dụng những cái cần thiết
  const isOpenModalDeprecate = useWatchLocal((state: any) => state.modals?.deprecate ?? false)
  const isOpenModalReactivate = useWatchLocal((state: any) => state.modals?.reactivate ?? false)
  const isOpenModalDelete = useWatchLocal((state: any) => state.modals?.delete ?? false)
  const isOpenModalResend = useWatchLocal((state: any) => state.modals?.resend ?? false)
  const isOpenModalInactiveStatus = useWatchLocal((state: any) => state.modals?.inactiveStatus ?? false)

  const openModal = React.useCallback(
    (modalType: CarrierModals) => {
      // Chỉ cho phép mở modal nếu nó có trong activeModals
      if (activeModals.includes(modalType)) {
        setFieldValue(['modals', modalType], true)
      }
    },
    [activeModals, setFieldValue]
  )

  const closeModal = React.useCallback(
    (modalType: CarrierModals) => {
      setFieldValue(['modals', modalType], false)
    },
    [setFieldValue]
  )

  // Sử dụng useMemo để tối ưu re-render
  return React.useMemo(
    () => ({
      // States - chỉ trả về true nếu modal được kích hoạt và đang mở
      isOpenModalDeprecate: activeModals.includes('deprecate') && isOpenModalDeprecate,
      isOpenModalReactivate: activeModals.includes('reactivate') && isOpenModalReactivate,
      isOpenModalDelete: activeModals.includes('delete') && isOpenModalDelete,
      isOpenModalResend: activeModals.includes('resend') && isOpenModalResend,
      isOpenModalInactiveStatus: activeModals.includes('inactiveStatus') && isOpenModalInactiveStatus,
      // Actions
      openModal,
      closeModal,
      // Convenience methods
      openDeprecateModal: () => openModal('deprecate'),
      openReactivateModal: () => openModal('reactivate'),
      openDeleteModal: () => openModal('delete'),
      openResendModal: () => openModal('resend'),
      openInactiveStatusModal: () => openModal('inactiveStatus')
    }),
    [
      activeModals,
      isOpenModalDeprecate,
      isOpenModalReactivate,
      isOpenModalDelete,
      isOpenModalResend,
      isOpenModalInactiveStatus,
      openModal,
      closeModal
    ]
  )
}

export const CarrierModals: FC<CarrierModalsProps> = ({
  activeModals = ['deprecate', 'reactivate', 'delete', 'resend', 'inactiveStatus'],
  onDeprecate,
  onReactivate,
  onDelete,
  onResend
}) => {
  const {
    isOpenModalDeprecate,
    isOpenModalReactivate,
    isOpenModalDelete,
    isOpenModalResend,
    isOpenModalInactiveStatus,
    closeModal
  } = useCarrierModals(activeModals)

  const { setFieldValue } = useLocalState()
  const extra = useWatchLocal((state: any) => state.modals?.extra ?? false)
  const carrierActionList = useWatchLocal<CarrierActionList[]>((state: any) => state.carrierActionList ?? [])

  const getActionName = (code: string) => {
    if (!carrierActionList || !code) return ''

    return carrierActionList.find(item => item.code === code)?.name || code
  }

  const handleDeprecate = () => {
    onDeprecate?.(extra)
    closeModal('deprecate')
  }

  const handleReactivate = () => {
    onReactivate?.(extra)
    closeModal('reactivate')
  }

  const handleDelete = () => {
    onDelete?.(extra)
    closeModal('delete')
  }

  const handleResend = () => {
    onResend?.(extra)
    closeModal('resend')
  }

  return (
    <>
      {/* Modal Vô hiệu hóa */}
      {activeModals.includes('deprecate') && (
        <LogisticModal
          open={isOpenModalDeprecate}
          setOpen={open => setFieldValue(['modals', 'deprecate'], open)}
          type='error'
          icon={<i className='onedx-circle-warning size-6' />}
          controlTitle={{
            titleText: 'Vô hiệu hóa',
            iconClassName: 'bg-bg-error-lighter text-text-error-default'
          }}
          footer={{
            cancelText: 'Hủy',
            cancelFunction: () => closeModal('deprecate'),
            okText: 'Vô hiệu hóa',
            okFunction: handleDeprecate
          }}
        >
          <div className='text-text-neutral text-sm'>
            Bạn có chắc chắn muốn vô hiệu hóa hành động &quot;
            <strong>{extra?.actionName || getActionName(extra?.action)}</strong>
            &quot; này?
          </div>
          <div className='text-text-neutral text-sm'>
            Sau khi vô hiệu hóa, thông tin sẽ chuyển sang trạng thái &quot;<strong>Không hoạt động</strong>&quot;.
          </div>
        </LogisticModal>
      )}

      {/* Modal Kích hoạt lại */}
      {activeModals.includes('reactivate') && (
        <LogisticModal
          open={isOpenModalReactivate}
          setOpen={open => setFieldValue(['modals', 'reactivate'], open)}
          type='primary'
          icon={<i className='onedx-refresh size-6' />}
          controlTitle={{
            titleText: 'Kích hoạt lại',
            iconClassName: 'bg-[#CDE4FE] text-icon-info-default'
          }}
          footer={{
            cancelFunction: () => closeModal('reactivate'),
            okText: 'Kích hoạt lại',
            okFunction: handleReactivate
          }}
        >
          <div className='mt-1 flex flex-col py-4'>
            <div className='text-text-neutral text-sm'>
              Bạn có chắc chắn muốn kích hoạt lại hành động &quot;
              <strong>{extra?.actionName || getActionName(extra?.action)}</strong>
              &quot; này?
            </div>
            <div className='text-text-neutral text-sm'>
              Sau khi kích hoạt lại, thông tin sẽ chuyển sang trạng thái &quot;<strong>Hoạt động</strong>&quot;.
            </div>
          </div>
        </LogisticModal>
      )}

      {/* Modal Xóa */}
      {activeModals.includes('delete') && (
        <LogisticModal
          open={isOpenModalDelete}
          setOpen={open => setFieldValue(['modals', 'delete'], open)}
          type='error'
          controlTitle={{
            titleText: 'Bạn có chắc chắn muốn xóa hoạt động?'
          }}
          footer={{
            okFunction: handleDelete,
            okText: 'Xóa'
          }}
        >
          <div className='h-4'></div>
        </LogisticModal>
      )}

      {/* Modal Gửi lại bản ghi */}
      {activeModals.includes('resend') && (
        <LogisticModal
          open={isOpenModalResend}
          setOpen={open => setFieldValue(['modals', 'resend'], open)}
          type='primary'
          controlTitle={{
            titleText: 'Gửi lại bản ghi'
          }}
          footer={{
            okFunction: handleResend,
            okText: 'Gửi lại'
          }}
        >
          <div className='body-14-regular py-4'>Bạn có muốn gửi lại bản ghi không?</div>
        </LogisticModal>
      )}

      {/* Modal Không thể bật hoạt động */}
      {activeModals.includes('inactiveStatus') && (
        <LogisticModal
          open={isOpenModalInactiveStatus}
          // open
          setOpen={open => setFieldValue(['modals', 'inactiveStatus'], open)}
          icon={<i className='onedx-circle-warning size-6' />}
          controlTitle={{
            titleText: 'Không thể bật hoạt động đơn vị vận chuyển',
            iconClassName: 'bg-bg-warning-lighter text-text-warning-strong'
          }}
          footer={{
            wrapperClassName: 'flex justify-end',
            render: (
              <LogisticButton
                variant='outlined'
                type='primary'
                onClick={() => {
                  closeModal('inactiveStatus')
                }}
              >
                Đóng
              </LogisticButton>
            )
          }}
        >
          <div className='body-14-regular py-4'>
            <p>
              Để bật hoạt động đơn vị vận chuyển, tất cả các cấu hình endpoint bắt buộc phải ở trạng thái hoạt động. Vui
              lòng kiểm tra và kích hoạt các endpoint sau:
            </p>
            {extra?.listError && (
              <ul className='list-disc space-y-1 pl-6'>
                {extra.listError.map((error: CarrierActionList, index: number) => (
                  <li key={index} className='body-14-medium'>
                    {error.name}
                  </li>
                ))}
              </ul>
            )}
          </div>
        </LogisticModal>
      )}
    </>
  )
}

export default CarrierModals
