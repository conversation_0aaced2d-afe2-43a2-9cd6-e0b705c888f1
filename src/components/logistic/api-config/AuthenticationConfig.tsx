import React from 'react'

import { Form, Input, Select } from 'antd'

import { useBlockVietnameseInput } from '@/hooks/useBlockVietnameseInput'

import { shouldUpdateFields } from '../common/utils'
import { validateRequire, validateRequireInput, validateSpecialCharacters, validateVietnamese } from '@/validator'
import { filterOption } from '@/utils/string'
import { AUTH_TYPE, authTypeOptions } from '../common/constant'

export const AuthenticationConfig = () => {
  const noVietnameseHandlers = useBlockVietnameseInput()

  return (
    <div className='grid grid-cols-[repeat(auto-fit,minmax(50px,1fr))] gap-4'>
      <Form.Item
        label='Phương thức xác thực API'
        name={['apiConfig', 'auth', 'type']}
        initialValue={AUTH_TYPE.BEARER}
        rules={[validateRequire('Phương thức xác thực không được bỏ trống')]}
      >
        <Select
          placeholder='Chọn phương thức xác thực API'
          options={authTypeOptions}
          showSearch
          filterOption={filterOption}
        />
      </Form.Item>

      {/* <Form.Item label='Header' name={['apiConfig', 'header']}>
        <Input placeholder='Nhập header' maxLength={250} />
      </Form.Item> */}

      {/* <div className='col-span-2 grid grid-cols-[repeat(auto-fit,minmax(50px,1fr))] gap-4'> */}
      <Form.Item noStyle shouldUpdate={shouldUpdateFields.authType}>
        {({ getFieldValue }) => {
          const authType = getFieldValue(['apiConfig', 'auth', 'type'])

          switch (authType) {
            case AUTH_TYPE.API_KEY:
              // Chuyển thành Form.List sau
              return (
                <>
                  <Form.Item
                    label='Key Placement'
                    name={['apiConfig', 'auth', 'apikey', 0, 'type']}
                    rules={[validateRequire('Key Placement không được bỏ trống')]}
                  >
                    <Select
                      placeholder='Chọn loại API key'
                      options={[
                        { label: 'Query', value: 'query' },
                        { label: 'Header', value: 'header' }
                      ]}
                    />
                  </Form.Item>
                  <Form.Item
                    label='API Key'
                    name={['apiConfig', 'auth', 'apikey', 0, 'key']}
                    rules={[
                      validateRequireInput('API Key không được bỏ trống'),
                      validateSpecialCharacters('Không cho phép nhập kí tự đặc biệt')
                    ]}
                  >
                    <Input placeholder='Nhập API Key' maxLength={250} />
                  </Form.Item>
                  <Form.Item
                    label='Key Value'
                    name={['apiConfig', 'auth', 'apikey', 0, 'value']}
                    rules={[
                      validateRequireInput('Key Value không được bỏ trống'),
                      validateSpecialCharacters('Không cho phép nhập kí tự đặc biệt')
                    ]}
                  >
                    <Input placeholder='Nhập Key Value' maxLength={250} />
                  </Form.Item>
                </>
              )

            case AUTH_TYPE.BEARER:
              return (
                <>
                  {/* <Form.Item label='Header' name={['apiConfig', 'auth', 'bearer', 'key']}>
                    <Input placeholder='Nhập Header' maxLength={250} />
                  </Form.Item> */}
                  <Form.Item
                    label='Token Value'
                    name={['apiConfig', 'auth', 'bearer', 'value']}
                    rules={[
                      validateRequireInput('Token value không được bỏ trống'),
                      validateVietnamese() // validate chặn chữ tượng hình, icon, ký tự đặc biệt
                    ]}
                  >
                    <Input placeholder='Nhập Token value' maxLength={250} {...noVietnameseHandlers} />
                  </Form.Item>
                </>
              )

            case AUTH_TYPE.BASIC:
              return (
                <>
                  <Form.Item
                    label='Username'
                    name={['apiConfig', 'auth', 'basic', 'userName']}
                    rules={[validateRequireInput('Tên đăng nhập không được bỏ trống')]}
                  >
                    <Input placeholder='Nhập tên đăng nhập' />
                  </Form.Item>
                  <Form.Item
                    label='Password'
                    name={['apiConfig', 'auth', 'basic', 'password']}
                    rules={[validateRequireInput('Mật khẩu không được bỏ trống')]}
                  >
                    <Input.Password placeholder='Nhập mật khẩu' />
                  </Form.Item>
                </>
              )
            default:
              return null
          }
        }}
      </Form.Item>
      {/* </div> */}
    </div>
  )
}
