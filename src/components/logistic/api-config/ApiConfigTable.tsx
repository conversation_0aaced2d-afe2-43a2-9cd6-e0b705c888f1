import React, { useMemo, useState } from 'react'

import { <PERSON><PERSON>, DatePicker, Dropdown, Pagination, Select, Table, Tooltip } from 'antd'

import type { ColumnsType } from 'antd/es/table'

import { styled } from 'styled-components'

import { debounce } from 'lodash'

import SelectFilter from '@views/product-catalog/common/SelectFilter'
import {
  apiConfigListFilter,
  API_STATUS_ENUM,
  STATE_OPTIONS,
  actionBtns,
  ACTION_BTN_KEY,
  authTypeOptions
} from '../common/constant'
import { localeTable, LogisticButton, LogisticTag } from '../common/LogisticComponents'
import type {
  CarrierApiConfig,
  ApiStatus,
  CarrierActionList,
  CreateCarrierApiConfigDto,
  UpdateCarrierApiConfigDto
} from '@/types/logistic/carrier'
import type { CheckboxOption, CheckedState } from '@/components/filter'
import SettingInput from '@/components/filter/SettingInput'
import { useLocalState, useW<PERSON><PERSON>ield } from '@/hooks'
import { handleFilterDataSource } from '../common/utils'
import { baseColorLight } from '@/utils/colors'

export const CustomTable = styled(Table)<any>`
  &.custom-bg-hover {
    .ant-table-tbody > tr:hover > td {
      background: #f2f4f9 !important;
    }
  }

  .ant-table-tbody > tr:hover .text-color-blue {
    color: #2a6aeb !important;
  }

  .ant-table-tbody > tr:hover .text-underline {
    text-decoration: underline;
  }
` as unknown as typeof Table

export const ApiConfigTable = ({
  connectList,
  onUpdate,
  onCheckConnectionTableRows
}: {
  connectList: CarrierApiConfig[]
  onUpdate: (key: string, initData: Partial<CreateCarrierApiConfigDto | UpdateCarrierApiConfigDto>) => void
  onCheckConnectionTableRows: (
    id: string,
    initData: Partial<CreateCarrierApiConfigDto | UpdateCarrierApiConfigDto>
  ) => void
}) => {
  const defaultSearchParams: Record<string, any> = {
    method: undefined,
    authType: undefined,
    dateRange: undefined,
    state: undefined
  }

  const [filterParams, setFilterParams] = useState(defaultSearchParams)
  const [page, setPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([])
  const [searchValue, setSearchValue] = useState<string>('')
  const [datePickerOpen, setDatePickerOpen] = useState(false)

  const { setFieldValue: setLocalState } = useLocalState()

  const carrierActionList = useWatchField<CarrierActionList[]>('carrierActionList')

  const [checked, setChecked] = useState<CheckedState>({
    isAction: true,
    isEndpoint: true
  })

  const checkBoxOptions: CheckboxOption[] = [
    { label: 'Hành động', key: 'isAction' },
    { label: 'Endpont URL', key: 'isEndpoint' }
  ]

  const filteredDataSource = useMemo(() => {
    return handleFilterDataSource({
      carrierActionList,
      data: connectList,
      advancedSearch: {
        search: searchValue,
        selector: checked
      },
      filter: filterParams
    })
  }, [connectList, carrierActionList, searchValue, checked, filterParams])

  const debouncedSetFilterParams = debounce(params => {
    setFilterParams(params)
  }, 200)

  const debouncedSetSearchValue = debounce(value => {
    setSearchValue(value)
  }, 400)

  const handleMenuClick = (record: any, key: string) => {
    switch (key) {
      case ACTION_BTN_KEY.CHECK_CONNECTION:
        onCheckConnectionTableRows(record.action, record)
        break
      case ACTION_BTN_KEY.DISABLE:
        setLocalState('modals', {
          deprecate: true,
          extra: record
        })
        break
      case ACTION_BTN_KEY.ENABLE:
        setLocalState('modals', {
          reactivate: true,
          extra: record
        })
        break
      case ACTION_BTN_KEY.DELETE:
        setLocalState('modals', {
          delete: true,
          extra: record.action
        })
        break
    }
  }

  const viewsItemButton = (state: ApiStatus) => {
    if (!actionBtns) return []

    switch (state) {
      case API_STATUS_ENUM.READY_FOR_TEST:
        return actionBtns.filter(
          item => item?.key === ACTION_BTN_KEY.DELETE || item?.key === ACTION_BTN_KEY.CHECK_CONNECTION
        )
      case API_STATUS_ENUM.ACTIVE:
        return actionBtns.filter(item => item?.key === ACTION_BTN_KEY.DELETE || item?.key === ACTION_BTN_KEY.DISABLE)
      case API_STATUS_ENUM.INACTIVE:
        return actionBtns.filter(item => item?.key === ACTION_BTN_KEY.DELETE || item?.key === ACTION_BTN_KEY.ENABLE)
      case API_STATUS_ENUM.FAILURE:
        return actionBtns.filter(
          item => item?.key === ACTION_BTN_KEY.DELETE || item?.key === ACTION_BTN_KEY.CHECK_CONNECTION
        )
      default:
        return actionBtns
    }
  }

  const apiConfigColumns: ColumnsType = [
    {
      title: 'STT',
      dataIndex: 'index',
      render: (_, __, index) => index + 1
    },
    {
      title: 'Hành động',
      dataIndex: 'action',
      render: (text, record) => (
        <div onClick={() => onUpdate(record.action, record)} className='text-color-blue text-underline cursor-pointer'>
          {record.actionName || carrierActionList?.find(option => option.code === text)?.name || text}
        </div>
      )
    },
    // Endpoint URL
    {
      title: 'Endpoint URL',
      dataIndex: ['apiConfig', 'url', 'raw'],
      render: text => {
        return (
          <Tooltip title={text}>
            <div className='text-color-blue'>{text}</div>
          </Tooltip>
        )
      }
    },
    {
      title: 'Phương thức kết nối',
      dataIndex: ['apiConfig', 'method'],
      render: text => {
        return <div className='text-color-blue'>{text}</div>
      }
    },
    {
      title: 'Phương thức xác thực API',
      dataIndex: ['apiConfig', 'auth', 'type'],
      render: text => {
        return <div className='text-color-blue'>{authTypeOptions.find(item => item.value === text)?.label || text}</div>
      }
    },
    {
      // Thời gian cập nhập
      title: 'Thời gian cập nhập',
      dataIndex: 'modifiedAt',
      render: text => {
        return <div className='text-color-blue'>{text}</div>
      }
    },
    {
      title: 'Trạng thái',
      dataIndex: 'state',
      width: 120,
      render: text => {
        const option = STATE_OPTIONS.find(item => item.value === text)

        return option ? <LogisticTag color={option.type}>{option.label}</LogisticTag> : text
      }
    },
    {
      key: 'actions',
      width: 40,
      align: 'center',
      render: (_, record) => (
        <div className='opacity-0 transition-opacity duration-200 group-hover:opacity-100'>
          <Dropdown
            menu={{
              items: viewsItemButton(record.state),
              onClick: ({ key }) => handleMenuClick(record, key)
            }}
            trigger={['click']}
            placement='bottomRight'
          >
            <Tooltip title='Thao tác'>
              <i className='onedx-menu-dot-vertical size-5 cursor-pointer rounded p-1' />
            </Tooltip>
          </Dropdown>
        </div>
      )
    }
  ]

  const handleFilterChange = (key: string, value: any) => {
    debouncedSetFilterParams((prev: any) => {
      const updatedParams = {
        ...prev,
        [key]: value
      }

      return updatedParams
    })
  }

  return (
    <div className='flex flex-col gap-4'>
      <div
        aria-hidden={!connectList || connectList?.length === 0}
        className='flex items-center gap-2 aria-hidden:hidden'
      >
        <SettingInput
          placeholder='Tìm kiếm'
          styles={{ flexGrow: 1 }}
          size='small'
          placeholderColor={baseColorLight['gray-6']}
          checked={checked}
          setChecked={setChecked}
          onKeyDown={(event: React.KeyboardEvent<HTMLInputElement>) => {
            if (event.key === 'Enter') {
              event.preventDefault()
              debouncedSetSearchValue(event.currentTarget.value)
            }
          }}
          onChange={value => {
            debouncedSetSearchValue(value)
          }}
          checkBoxOptions={checkBoxOptions}
        />
        <div className='flex gap-2'>
          <Select
            style={{ minWidth: '124px', width: '200px' }}
            placeholder='Trạng thái'
            mode='multiple'
            options={STATE_OPTIONS}
            onChange={value => handleFilterChange('state', value)}
          />
          <div className='relative'>
            <DatePicker.RangePicker
              className='absolute'
              placeholder={['Từ ngày', 'Đến ngày']}
              open={datePickerOpen}
              onOpenChange={open => {
                setDatePickerOpen(open)
              }}
              onChange={(_, dateStrings) => handleFilterChange('dateRange', dateStrings)}
            />
            <Button
              id='date-picker-button'
              onClick={() => {
                setDatePickerOpen(prev => !prev)
              }}
              className='body-14-regular flex size-full items-center gap-2 text-text-neutral-light'
            >
              <i className='onedx-calendar size-5' />
              Khoảng thời gian
            </Button>
          </div>
          <SelectFilter
            size='large'
            setFilterParams={debouncedSetFilterParams}
            filterOptions={apiConfigListFilter}
            filterParams={filterParams}
            defaultFilter={defaultSearchParams}
          />
        </div>
      </div>
      {/* Table */}
      <CustomTable<any>
        columns={apiConfigColumns}
        className='custom-bg-hover'
        rowClassName='group'
        rowSelection={{
          type: 'checkbox',
          selectedRowKeys,
          onChange: selectedKeys => {
            setSelectedRowKeys(selectedKeys)
          }
        }}
        dataSource={filteredDataSource}
        pagination={{
          pageSize,
          current: page,
          onChange: page => setPage(page),
          className: 'hidden'
        }}
        locale={localeTable}
        summary={() => {
          return (
            <Table.Summary fixed='bottom'>
              <Table.Summary.Row>
                <Table.Summary.Cell index={0} colSpan={apiConfigColumns?.length || 1}>
                  <div className='flex w-full items-center justify-between'>
                    <div className='flex items-center gap-4'>
                      <div className='text-sm text-text-neutral-strong'>Đã chọn: {selectedRowKeys.length}</div>
                      <LogisticButton
                        variant='outlined'
                        type={selectedRowKeys.length === 0 ? 'default' : 'primary'}
                        disabled={selectedRowKeys.length === 0}
                        className={selectedRowKeys.length === 0 ? 'border-0 bg-gray-alpha-13 text-gray-alpha-5' : ''}
                        onClick={() => {
                          setLocalState(['modals'], {
                            delete: true,
                            idsDelete: selectedRowKeys
                          })
                        }}
                      >
                        Xóa
                      </LogisticButton>
                    </div>
                    <Pagination
                      total={filteredDataSource?.length ?? 0}
                      pageSize={pageSize}
                      current={page}
                      onChange={page => setPage(page)}
                      showSizeChanger={false}
                    />
                    <Pagination
                      total={filteredDataSource?.length ?? 0}
                      pageSize={pageSize}
                      current={page}
                      showSizeChanger={true}
                      showQuickJumper={false}
                      onChange={() => {}} // Loại bỏ thông báo lỗi
                      onShowSizeChange={(_, size) => {
                        setPageSize(size)
                        setPage(1) // Reset to first page on size change
                      }}
                      // Hide navigation buttons via style
                      style={{ display: 'inline-block', minWidth: 120 }}
                      itemRender={() => null}
                      locale={{
                        items_per_page: '/ Trang'
                      }}
                    />
                  </div>
                </Table.Summary.Cell>
              </Table.Summary.Row>
            </Table.Summary>
          )
        }}
      />
    </div>
  )
}
