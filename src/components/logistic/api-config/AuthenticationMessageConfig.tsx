import { useState } from 'react'

import { Checkbox, Form, Input, Select } from 'antd'

import { isEmpty } from 'lodash'

import { filterOption, filterOptionTrim } from '@/utils/string'

import { validateRequireInput, validateSeparator, validateTechniqueName } from '@/validator'

import { ALGORITHM_LIST } from '../common/constant'
import { useBlockVietnameseInput } from '@/hooks/useBlockVietnameseInput'

export const AuthenticationMessageConfig = () => {
  const [useAuthenticationMessage, setUseAuthenticationMessage] = useState(false)
  const noVietnameseHandlers = useBlockVietnameseInput()
  const form = Form.useFormInstance()
  const requestObject = Form.useWatch('requestObject', form)

  const dropdownRequestObject = Form.useWatch('dropdownRequestObject', form)

  return (
    <div>
      <Checkbox onChange={e => setUseAuthenticationMessage(e.target.checked)}>S<PERSON> dụng xác thực tin nhắn HMAC</Checkbox>

      {useAuthenticationMessage && (
        <div>
          <div className='mt-4 grid grid-cols-2 gap-4'>
            <Form.Item
              label='Secret Key'
              name={['apiConfig', 'securities', 'hmac', 'secretKey']}
              rules={[validateRequireInput('Secret Key không được bỏ trống')]}
            >
              <Input maxLength={250} placeholder='Nhập Secret key' {...noVietnameseHandlers} onPaste={() => false} />
            </Form.Item>

            <Form.Item
              label='Thuật toán'
              name={['apiConfig', 'securities', 'hmac', 'encode']}
              rules={[validateRequireInput('Thuật toán không được bỏ trống')]}
              initialValue={'SHA-256'}
            >
              <Select
                options={ALGORITHM_LIST}
                showSearch
                filterOption={(input, option: any) => filterOptionTrim(input, option)}
                notFoundContent='Không có dữ liệu'
              />
            </Form.Item>
          </div>

          <div className='mt-4 grid grid-cols-3 gap-4'>
            <Form.Item
              label='Tên kỹ thuật'
              name={['apiConfig', 'securities', 'hmac', 'field']}
              rules={[validateRequireInput('Tên kỹ thuật không được bỏ trống'), validateTechniqueName()]}
            >
              <Input
                maxLength={50}
                placeholder='Nhập tên kỹ thuật'
                {...noVietnameseHandlers}
                onKeyDown={e => {
                  if (e.key === ' ') {
                    e.preventDefault()
                  }
                }}
              />
            </Form.Item>
            <Form.Item
              label='Tham số'
              rules={[validateRequireInput('Tham số không được bỏ trống')]}
              name={['apiConfig', 'securities', 'hmac', 'participants']}
            >
              <Select
                placeholder='Chọn tham số'
                showSearch
                filterOption={filterOption}
                notFoundContent={
                  !dropdownRequestObject || dropdownRequestObject.length === 0
                    ? 'Vui lòng thiết lập request để hiển thị tham số'
                    : 'Không có dữ liệu'
                }
                options={
                  !isEmpty(dropdownRequestObject) &&
                  requestObject?.map((param: any) => ({
                    label: param.name,
                    value: param.name
                  }))
                }
                mode='multiple'
              />
            </Form.Item>
            <Form.Item
              label='Ký tự ngăn cách'
              name={['apiConfig', 'securities', 'hmac', 'separator']}
              rules={[validateSeparator()]}
            >
              <Input placeholder='Nhập ký tự ngăn cách' />
            </Form.Item>
          </div>
        </div>
      )}
    </div>
  )
}
