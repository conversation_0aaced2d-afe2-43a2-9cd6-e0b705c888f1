'use client'

import React, { useEffect, useState } from 'react'

import { Drawer, Form, Input, InputNumber, Select, Spin } from 'antd'

import { isEmpty, isNil } from 'lodash'

import type { FormInstance } from 'antd/lib'

import { LogisticButton, LogisticCard } from '../common/LogisticComponents'
import { copyObjectToClipboard, parseUrl, shouldUpdateFields } from '../common/utils'
import {
  normalizeInputNumber,
  validateArrayRequired,
  validateLink,
  validateRequire,
  validateRequireInput
} from '@/validator'
import DataMapping from '@/views/logistic/common/DataMapping'
import { If } from '@/components/common'
import {
  buildDropdownMappingObject,
  buildRequestObject,
  buildResponseObject
} from '@/views/inventory/common/hooks/useConvertAndValidation'
import JsonMappingPreview from '@/views/logistic/JsonMappingPreview'
import type { ErrorMapping } from '@/types/logistic/carrier'
import { AuthenticationConfig } from './AuthenticationConfig'
import { HTTP_METHOD, methodOptions, stylesCard, timeOptions, TIME_UNIT, systemParamsOptions } from '../common/constant'
import { filterOptionTrim } from '@/utils/string'
import { AuthenticationMessageConfig } from './AuthenticationMessageConfig'

interface Props {
  open: boolean
  setOpen: (open: boolean) => void
  initialData?: any
  [key: string]: any
}

export const CreateApiConfigForm = ({ form, carrierAction }: { form: FormInstance; carrierAction: any }) => {
  const action = Form.useWatch('action', form)

  const handleUrlChange = (url: string) => {
    const { params, pathVariables } = parseUrl(url, {
      hiddenValuesParams: true
    })

    form.setFieldsValue({
      params,
      pathVariables
    })
  }

  return (
    <Spin spinning={carrierAction.isLoading}>
      <Form
        form={form}
        name='api-config-form'
        layout='vertical'
        onValuesChange={changedValues => {
          if (changedValues.apiConfig?.url?.raw) {
            handleUrlChange(changedValues.apiConfig.url.raw)
          }

          if (changedValues.inputMapping || changedValues.outputMapping) {
            form.setFieldValue('isDisabledPreview', false)
          }
        }}
        className='flex flex-col gap-6'
        colon={false}
      >
        <div className='flex flex-col gap-5'>
          <LogisticCard title='Cấu hình endpoint' styles={stylesCard}>
            <div className='flex flex-col gap-5'>
              <div className='grid grid-cols-[repeat(auto-fit,minmax(100px,1fr))] gap-4'>
                <Form.Item
                  label='Chọn hành động'
                  name='action'
                  rules={[{ required: true, message: 'Hành động không được bỏ trống' }]}
                >
                  <Select
                    placeholder='Chọn hành động'
                    options={carrierAction.options}
                    showSearch
                    filterOption={filterOptionTrim}
                    notFoundContent='Không có dữ liệu'
                    onChange={(_, option) => {
                      if (option?.label) {
                        form.setFieldValue('actionName', option?.label)
                      }
                    }}
                  />
                </Form.Item>

                <Form.Item
                  label='Phương thức kết nối'
                  initialValue={HTTP_METHOD.GET}
                  name={['apiConfig', 'method']}
                  rules={[{ required: true, message: 'Phương thức HTTP không được bỏ trống' }]}
                >
                  <Select
                    placeholder='Chọn phương thức kết nối'
                    options={methodOptions}
                    showSearch
                    filterOption={(input, option: any) => filterOptionTrim(input, option)}
                    notFoundContent='Không có dữ liệu'
                  />
                </Form.Item>

                <Form.Item
                  label='URL Endpoint API'
                  name={['apiConfig', 'url', 'raw']}
                  rules={[
                    validateRequireInput('Endpoint URL không được bỏ trống'),
                    validateLink('Endpoint URL không đúng định dạng')
                  ]}
                >
                  <Input placeholder='Nhập URL' />
                </Form.Item>

                <Form.Item label='Timeout' name='timeout' initialValue={1} normalize={normalizeInputNumber}>
                  <Input
                    type='number'
                    placeholder='Nhập timeout'
                    min={0}
                    max={99999}
                    onKeyPress={e => {
                      // Chặn dấu "-", Chặn nhập quá 5 ký tự
                      if (e.key === '-' || e.key === ',' || e.currentTarget.value.length >= 5) {
                        e.preventDefault()
                      }
                    }}
                    maxLength={5}
                    addonAfter={<Select defaultValue={TIME_UNIT.MINUTE} options={timeOptions} />}
                  />
                </Form.Item>
              </div>
              <Form.Item noStyle shouldUpdate={shouldUpdateFields.apiConfig}>
                {({ getFieldValue }) => {
                  const url = getFieldValue(['apiConfig', 'url', 'raw'])
                  const params = getFieldValue('params')
                  const pathVariables = getFieldValue('pathVariables')

                  const shouldShow = !(isNil(url) || isEmpty(url)) && (params?.length > 0 || pathVariables?.length > 0)

                  const shouldShowParams = shouldShow && params?.length > 0
                  const shouldShowPathVariables = shouldShow && pathVariables?.length > 0

                  return shouldShow ? (
                    <div className='flex flex-col gap-5 rounded-lg bg-bg-neutral-lightest p-5'>
                      {/* Params Form List */}
                      <If condition={shouldShowParams}>
                        <div className='flex flex-col gap-2.5'>
                          <div className='body-14-medium text-text-neutral-strong'>Params</div>
                          <Form.List name='params' rules={[validateArrayRequired('Params không được bỏ trống')]}>
                            {fields => (
                              <>
                                {fields.map(({ key, name }) => (
                                  <div key={key} className='grid grid-cols-2 gap-2'>
                                    <Form.Item name={[name, 'key']} noStyle>
                                      <Input placeholder='Nhập key' prefix='Key:' maxLength={50} />
                                    </Form.Item>
                                    <Form.Item name={[name, 'value']} noStyle>
                                      <Select
                                        placeholder='Value không được bỏ trống'
                                        showSearch
                                        filterOption={filterOptionTrim}
                                        notFoundContent='Không có dữ liệu'
                                        allowClear
                                        options={systemParamsOptions}
                                      />
                                    </Form.Item>
                                  </div>
                                ))}
                              </>
                            )}
                          </Form.List>
                        </div>
                      </If>

                      {/* Path Variables Form List */}
                      <If condition={shouldShowPathVariables}>
                        <div className='flex flex-col gap-2.5'>
                          <div className='body-14-medium text-text-neutral-strong'>Path Variables</div>
                          <Form.List
                            name='pathVariables'
                            rules={[
                              {
                                validator: async (_, value) =>
                                  value && value.length > 0
                                    ? Promise.resolve()
                                    : Promise.reject('Path Variables không được bỏ trống')
                              }
                            ]}
                          >
                            {fields => (
                              <>
                                {fields.map(({ key, name }) => (
                                  <div key={key} className='grid grid-cols-2 items-center gap-2'>
                                    <Form.Item name={[name, 'key']} noStyle>
                                      <Input placeholder='Nhập key' prefix='Key:' maxLength={50} />
                                    </Form.Item>
                                    <Form.Item name={[name, 'value']} noStyle>
                                      <Select
                                        placeholder='Value không được bỏ trống'
                                        showSearch
                                        filterOption={filterOptionTrim}
                                        notFoundContent='Không có dữ liệu'
                                        allowClear
                                        options={systemParamsOptions}
                                      />
                                    </Form.Item>
                                  </div>
                                ))}
                              </>
                            )}
                          </Form.List>
                        </div>
                      </If>
                    </div>
                  ) : null
                }}
              </Form.Item>
              <AuthenticationConfig />
              <AuthenticationMessageConfig />
            </div>
          </LogisticCard>
          <LogisticCard title='Cấu hình thử lại' styles={stylesCard}>
            <div className='grid grid-cols-2 gap-4'>
              <Form.Item
                layout='vertical'
                label='Số lần thử lại (retry)'
                name='retryNumber'
                normalize={normalizeInputNumber}
                rules={[validateRequire('Số lần thử lại không được bỏ trống')]}
              >
                <InputNumber
                  min={0}
                  max={5}
                  step={1}
                  maxLength={1}
                  className='w-full'
                  onKeyPress={e => {
                    if (!/[0-5]/.test(e.key)) {
                      e.preventDefault()
                    }
                  }}
                  placeholder='Nhập số lần thử lại'
                />
              </Form.Item>
              <Form.Item noStyle shouldUpdate={shouldUpdateFields.custom(['retryNumber'])}>
                {({ getFieldValue }) => {
                  const retryNumber = getFieldValue('retryNumber')

                  return (
                    <Form.Item
                      layout='vertical'
                      label='Thời gian giữa các số lần thử lại'
                      normalize={normalizeInputNumber}
                      name='retryInterval'
                    >
                      <InputNumber
                        placeholder='Nhập khoảng thời gian'
                        type='number'
                        min={0}
                        step={1}
                        maxLength={250}
                        disabled={!retryNumber}
                        className='w-full'
                        addonAfter={<Select defaultValue='s' options={timeOptions} />}
                      />
                    </Form.Item>
                  )
                }}
              </Form.Item>
            </div>
          </LogisticCard>
          <If condition={!isNil(action)}>
            <Form.Item noStyle shouldUpdate={shouldUpdateFields.dataMapping}>
              {({ getFieldsValue }) => {
                const values = getFieldsValue(true)
                let initData

                if (!isNil(values.action) && !values.requestObject && !values.responseObject) {
                  initData = {
                    requestObject: buildRequestObject(values.dxReqFields, values.inputMapping),
                    dropdownRequestObject: buildDropdownMappingObject(values.partnerReqFields),
                    responseObject: buildResponseObject(values.dxRespFields, values.outputMapping),
                    dropdownResponseObject: buildDropdownMappingObject(values.partnerRespFields),
                    errorMapping: values.errorMapping as ErrorMapping[]
                  }
                }

                return (
                  <DataMapping
                    reqCode={values.action}
                    resCode={values.action}
                    form={form}
                    initData={initData}
                    isVisibleRequestObject={values?.apiConfig?.method !== HTTP_METHOD.GET}
                  />
                )
              }}
            </Form.Item>
          </If>
        </div>
      </Form>
    </Spin>
  )
}

const CreateApiConfigDrawer = ({
  open,
  setOpen,
  title = 'Tạo cấu hình kết nối API',
  initialData,
  carrierAction,
  ...rest
}: Props) => {
  const [form] = Form.useForm()
  const isDisabledPreview = Form.useWatch('isDisabledPreview', form) ?? true
  const [openPreview, setOpenPreview] = useState(false)

  useEffect(() => {
    if (initialData) {
      form.setFieldsValue(initialData)
    } else {
      form.resetFields()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [initialData, open])

  return (
    <Drawer
      open={open}
      onClose={() => {
        form.resetFields()
        setOpen(false)
      }}
      title={title}
      destroyOnClose
      width={1040}
      classNames={{
        body: 'p-6',
        footer: 'p-6'
      }}
      footer={
        <div className='flex justify-between gap-3'>
          <LogisticButton
            type='primary'
            variant='outlined'
            htmlType='button'
            onClick={() => {
              setOpenPreview(true)
            }}
            disabled={isDisabledPreview}
          >
            Xem trước Mapping Json
          </LogisticButton>
          <div className='flex gap-3'>
            <LogisticButton
              type='primary'
              variant='outlined'
              htmlType='reset'
              form='api-config-form'
              onClick={() => setOpen(false)}
            >
              Đóng
            </LogisticButton>
            <LogisticButton type='primary' htmlType='submit' form='api-config-form'>
              Xác nhận
            </LogisticButton>
          </div>
        </div>
      }
    >
      <>
        <CreateApiConfigForm form={form} carrierAction={carrierAction} {...rest} />
        <JsonMappingPreview
          visible={openPreview}
          onClose={() => setOpenPreview(false)}
          dataObject={{
            example: true
          }}
          handleCopyJsonMapping={() => {
            const formValues = form.getFieldsValue(true)

            copyObjectToClipboard(formValues)
            setOpenPreview(false)
          }}
        />
      </>
    </Drawer>
  )
}

export default CreateApiConfigDrawer
