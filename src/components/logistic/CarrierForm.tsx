import React, { useEffect, useState, type FC } from 'react'

import { useRouter } from 'next/navigation'

import { Button, Form, Input, Spin, Switch, Tooltip } from 'antd'

import { useMutation } from '@tanstack/react-query'

import { API_STATUS_ENUM, statusOptions } from '@/components/logistic/common/constant'

import ModalCheckConnection from '@views/inventory/common/modal/ModalCheckConnection'

import type {
  CreateLogisticCarrierDto,
  UpdateLogisticCarrierDto,
  ApiStatus,
  CarrierStatus,
  CarrierActionList,
  CarrierApiConfig,
  CreateCarrierApiConfigDto,
  UpdateCarrierApiConfigDto,
  CarrierAction
} from '@/types/logistic/carrier'
import { LogisticButton, LogisticCard, LogisticHeader, LogisticModal } from './common/LogisticComponents'
import { UploadFile } from '../custom-field/editor/CustomComponents/ModalCreateVariant/components'
import {
  validateEmail,
  validateKeyboardCharacters,
  validateNonVietnamese,
  validatePhoneNumber,
  validateRequireInput,
  validateSpecialCharacters
} from '@/validator'
import { ApiConfigTable } from './api-config/ApiConfigTable'
import LogisticInstance from '@/models/Logistic'
import { useAsyncValidator, useLocalState } from '@/hooks'
import { validateCarrier } from './common/utils'
import { CarrierModals } from './CarrierModals'
// import { generateCarrierCode } from './common/constant'

interface CarrierFormProps {
  initialData?: UpdateLogisticCarrierDto
  // onSubmit: (data: CreateLogisticCarrierDto | UpdateLogisticCarrierDto) => void
  onOpenApiConfig?: ({
    type,
    key,
    initData
  }: {
    type?: 'create' | 'update'
    key?: string
    initData?: Partial<CreateCarrierApiConfigDto | UpdateCarrierApiConfigDto>
  }) => void
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const CarrierForm: FC<CarrierFormProps> = ({ initialData, onOpenApiConfig }) => {
  const [form] = Form.useForm<CreateLogisticCarrierDto | UpdateLogisticCarrierDto>()
  const status = Form.useWatch('status', form)
  const [openDraftModal, setOpenDraftModal] = useState(false)
  const { getFieldValue: getLocalState, setFieldValue: setLocalState } = useLocalState()
  const router = useRouter()

  const [openCheckConnection, setOpenCheckConnection] = useState(false)
  const [connectionInitData, setConnectionInitData] = useState<any>({})

  const handleCheckConnectionTableRows = (id: string, initData: any) => {
    setConnectionInitData({
      key: id,
      urlApi: initData.apiConfig.url.raw,
      httpType: initData.apiConfig.method,
      authentication: initData.apiConfig.auth?.type.toLowerCase(),
      headerName: 'token',
      tokenValue: initData.apiConfig.auth?.bearer?.value || '',
      username: initData.apiConfig.auth?.basic?.userName || '',
      password: initData.apiConfig.auth?.basic?.password || '',
      apiKey: initData.apiConfig.auth?.apikey != null ? initData.auth?.apikey[0]?.key : ''
    })
    setOpenCheckConnection(true)
  }

  const handleUpdateTableRowStatus = (action: CarrierAction, status: ApiStatus) => {
    if (!action) return

    const filteredConnectList = form.getFieldValue('connectList').map((item: any) => {
      if (item.action === action) {
        item.state = status
      }

      return item
    })

    form.setFieldValue('connectList', filteredConnectList)
  }

  const handleDeleteTableRows = (id: string | string[]) => {
    if (Array.isArray(id) && id.length === 0) return

    const connectList = form.getFieldValue('connectList')
    const idsToDelete = Array.isArray(id) ? id : [id]

    const filteredConnectList = connectList.filter((item: any) => !idsToDelete.includes(item.key))

    form.setFieldValue('connectList', filteredConnectList)
  }

  const onChangeStatus = (status: CarrierStatus) => {
    if (status === 'ACTIVE') {
      const fieldsValue = form.getFieldsValue(true)
      const action = getLocalState('carrierActionList') as CarrierActionList[]

      const { isValid, type, extra } = validateCarrier(fieldsValue, {
        checkActiveStatus: true,
        requiredActions: action?.filter((item: any) => item.required)
      })

      if (type === 'invalid') {
        setLocalState('modals', {
          inactiveStatus: true,
          extra
        })
      }

      if (!isValid) {
        return
      }
    }

    form.setFieldValue('status', status)
  }

  const generateCarrierCode = useMutation({
    mutationFn: () => LogisticInstance.generateCarrierCode()
  })

  // Validator cho tên đơn vị vận chuyển
  const validateCarrierNameAsync = useAsyncValidator({
    validatorFn: (name: string) => LogisticInstance.validateCarrierName({ name, id: initialData?.id }),
    debounceMs: 1000,
    resultPassed: false,
    errorMessage: 'Tên đơn vị vận chuyển đã tồn tại'
  })

  // Validator cho mã đối tác (ví dụ)
  const validateCarrierCodeAsync = useAsyncValidator({
    validatorFn: (code: string) => LogisticInstance.validateCarrierCode({ name: code, id: initialData?.id }),
    debounceMs: 1000,
    resultPassed: false,
    errorMessage: 'Mã đối tác đã tồn tại'
  })

  // Set initial values if provided
  useEffect(() => {
    async function setInitialValues() {
      if (initialData) {
        form.setFieldsValue(initialData)
      }

      if (!initialData?.code) {
        const generatedCode = await generateCarrierCode.mutateAsync()

        form.setFieldValue('code', generatedCode)
      }
    }

    setInitialValues()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [initialData])

  return (
    <Spin spinning={generateCarrierCode.isPending}>
      <Form form={form} name='carrier-form' className='flex flex-col gap-2' layout='vertical'>
        <LogisticCard title='Thông tin chung'>
          <div className='grid grid-cols-4 gap-4'>
            <Form.Item
              name='name'
              label='Tên đơn vị vận chuyển'
              validateFirst
              rules={[
                validateRequireInput('Tên đơn vị vận chuyển không được bỏ trống'),
                { max: 250, message: 'Tên đơn vị vận chuyển tối đa 250 ký tự' },
                validateKeyboardCharacters(),
                validateSpecialCharacters(),
                validateCarrierNameAsync
              ]}
            >
              <Input placeholder='Nhập tên đơn vị vận chuyển' maxLength={250} />
            </Form.Item>
            <Form.Item name='code' label='Mã định danh' rules={[validateRequireInput('Vui lòng nhập mã định danh')]}>
              <Input placeholder='Nhập mã định danh' disabled />
            </Form.Item>
            <Form.Item
              name='partnerCode'
              label={
                <div className='flex items-center gap-1'>
                  Mã đối tác tích hợp
                  <Tooltip title='Mã tích hợp đối tác'>
                    <i className='onedx-system-warning size-5' />
                  </Tooltip>
                </div>
              }
              validateFirst
              rules={[
                { max: 50, message: 'Mã đối tác tích hợp tối đa 50 ký tự' },
                validateKeyboardCharacters(),
                validateSpecialCharacters(),
                validateNonVietnamese(),
                validateCarrierCodeAsync
              ]}
            >
              <Input placeholder='Nhập mã đối tác tích hợp' maxLength={50} />
            </Form.Item>
            <Form.Item
              name='status'
              label='Trạng thái hoạt động'
              initialValue={'INACTIVE'}
              rules={[{ required: true, message: 'Vui lòng chọn trạng thái hoạt động' }]}
            >
              <div className='flex items-center gap-3 rounded-lg border border-solid border-border-neutral-light px-3 py-2'>
                <Switch
                  checked={status === 'ACTIVE'}
                  onChange={checked => onChangeStatus(checked ? 'ACTIVE' : 'INACTIVE')}
                />
                <span className='caption-12-regular text-text-neutral'>
                  {statusOptions.find(option => option.value === status)?.label || 'Chưa xác định'}
                </span>
              </div>
            </Form.Item>
            <div className='col-span-2'>
              <Form.Item name='logoUrl'>
                <UploadFile
                  type='image'
                  label='Logo đơn vị vận chuyển'
                  allowImgType={['image/jpeg', 'image/webp']}
                  errorMessage='File được upload phải ở định dạng JPEG, WEBP'
                  maxFileSize={1}
                  acceptAllUrl={true}
                />
              </Form.Item>
            </div>
            <div className='col-span-2 flex flex-col rounded-lg bg-bg-neutral-lightest p-3'>
              <Form.Item
                name='description'
                label='Mô tả'
                rules={[validateKeyboardCharacters(), validateSpecialCharacters()]}
              >
                <Input.TextArea
                  placeholder='Nhập mô tả'
                  maxLength={500}
                  style={{
                    minHeight: '138px'
                  }}
                />
              </Form.Item>
            </div>
          </div>
        </LogisticCard>
        <LogisticCard title='Thông tin liên hệ kỹ thuật'>
          <div className='grid grid-cols-2 gap-4'>
            <Form.Item
              label='Email'
              name='supportEmail'
              validateFirst
              rules={[validateRequireInput('Email không được bỏ trống'), validateEmail('Sai định dạng email')]}
              className='mb-4'
            >
              <Input maxLength={100} placeholder='Nhập địa chỉ email' />
            </Form.Item>
            <Form.Item
              label='Số điện thoại'
              name='supportPhone'
              validateFirst
              rules={[
                validateRequireInput('Số điện thoại không được bỏ trống'),
                validatePhoneNumber('Sai định dạng số điện thoại')
              ]}
            >
              <Input placeholder='Nhập số điện thoại' />
            </Form.Item>
          </div>
        </LogisticCard>

        <Form.Item
          noStyle
          shouldUpdate={(prevValues, currentValues) => prevValues.connectList !== currentValues.connectList}
        >
          {({ getFieldValue }) => {
            const connectList = getFieldValue('connectList')
            const isEmpty = !connectList || connectList.length === 0

            if (isEmpty) {
              return (
                <div className='flex justify-between gap-5 bg-white p-4'>
                  <LogisticHeader title='Cấu hình kết nối API' />
                  <LogisticButton
                    type='primary'
                    onClick={e => {
                      e.stopPropagation()
                      onOpenApiConfig &&
                        onOpenApiConfig({
                          type: 'create'
                        })
                    }}
                  >
                    <i className='onedx-progress size-5' />
                    Thêm cấu hình
                  </LogisticButton>
                </div>
              )
            }

            return (
              <LogisticCard
                title='Cấu hình kết nối API'
                rightContent={
                  <Button
                    type='text'
                    className='text-primary-blue'
                    onClick={e => {
                      e.stopPropagation()
                      onOpenApiConfig &&
                        onOpenApiConfig({
                          type: 'create'
                        })
                    }}
                  >
                    <i className='onedx-plus size-5' />
                    Thêm cấu hình
                  </Button>
                }
              >
                <ApiConfigTable
                  connectList={connectList || []}
                  onUpdate={(key, data) => {
                    if (onOpenApiConfig) {
                      onOpenApiConfig({
                        type: 'update',
                        // key,
                        initData: data
                      })
                    } else {
                      console.error('onOpenApiConfig function is not provided')

                      return
                    }
                  }}
                  onCheckConnectionTableRows={handleCheckConnectionTableRows}
                />
              </LogisticCard>
            )
          }}
        </Form.Item>

        <div className='mb-4 flex items-center justify-end gap-4 bg-white p-4'>
          <LogisticButton variant='outlined' type='primary' onClick={() => setOpenDraftModal(true)}>
            Hủy
          </LogisticButton>
          <LogisticButton type='primary' variant='solid' htmlType='submit'>
            {initialData ? 'Cập nhật' : 'Tạo'}
          </LogisticButton>
        </div>
      </Form>

      <CarrierModals
        activeModals={['deprecate', 'reactivate', 'delete', 'inactiveStatus']}
        onDeprecate={(record: CarrierApiConfig) => {
          handleUpdateTableRowStatus(record.action, API_STATUS_ENUM.INACTIVE)
        }}
        onReactivate={(record: CarrierApiConfig) => {
          handleUpdateTableRowStatus(record.action, API_STATUS_ENUM.ACTIVE)
        }}
        onDelete={idsDelete => {
          handleDeleteTableRows(idsDelete)
        }}
      />

      {/* Form này chỉ để lưu nháp, không liên quan đến việc tạo mới hay cập nhật */}
      <LogisticModal
        open={openDraftModal}
        setOpen={setOpenDraftModal} // Khi ấn nút "Thoát": Hủy bỏ việc lưu nháp, giữ nguyên form
        type='primary'
        icon={<i className='onedx-circle-warning size-6' />}
        controlTitle={{
          titleText: 'Tạo “Đơn vị vận chuyển” chưa hoàn tất',
          iconClassName: 'bg-bg-warning-lighter text-text-warning-strong'
        }}
        footer={{
          render: (
            <Form
              name='draft-form'
              onFinish={() => {
                setOpenDraftModal(false)
              }}
              className='col-span-2 grid grid-cols-2 items-center justify-center gap-2'
            >
              <LogisticButton
                type='text'
                className='text-text-error-default'
                onClick={() => {
                  setOpenDraftModal(false)
                  // Hủy bỏ việc lưu nháp, nhưng cũng hủy bỏ các thay đổi trong form
                  form.resetFields()
                  router.push('/carrier/list')
                }}
              >
                Thoát
              </LogisticButton>
              <LogisticButton type='primary' htmlType='submit'>
                Lưu bản nháp
              </LogisticButton>
            </Form>
          )
        }}
      >
        <div className='body-14-regular py-4'>Bạn có thể lưu nháp và mở lại sau để hoàn tất tạo đơn vị vận chuyển</div>
      </LogisticModal>

      <ModalCheckConnection
        open={openCheckConnection}
        setOpen={setOpenCheckConnection}
        initValue={connectionInitData}
        layout='vertical'
        getDataAfterTest={status => {
          if (status === 'success') {
            handleUpdateTableRowStatus(connectionInitData.action, API_STATUS_ENUM.ACTIVE)
          }

          if (status === 'error') {
            handleUpdateTableRowStatus(connectionInitData.action, API_STATUS_ENUM.FAILURE)
          }
        }}
      ></ModalCheckConnection>
    </Spin>
  )
}
