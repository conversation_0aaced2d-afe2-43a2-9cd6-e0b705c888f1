import React, { useCallback, useState } from 'react'

import { <PERSON><PERSON>, DatePicker, Pagination, Popover, Select, Table } from 'antd'

import type { ColumnsType } from 'antd/es/table'

import { debounce } from 'lodash'

import dayjs from 'dayjs'

import customParseFormat from 'dayjs/plugin/customParseFormat'

import { EmptySearch } from '@components/inventory/common/EmptySearch'

dayjs.extend(customParseFormat)

import { usePage } from '@/hooks'
import Logistic from '@/models/Logistic'
import SettingInput, { type CheckboxOption, type CheckedState } from '@components/filter/SettingInput'
import { pageSizeOptions } from '@views/product-catalog/constants/constants'

type LogStatus = 'SUCCESS' | 'ERROR'

interface MappingLog {
  key: string
  createdAt: string
  actionType: string
  isLink?: boolean
  userName: string
  status: LogStatus
  actionDescription: string
  isNoteLink?: boolean
}

interface Props {
  id: string
}

interface IParamsMappingLog {
  value?: string
  isActionType?: number
  isName?: number
  isNote?: number
  status?: LogStatus
  startDate?: number | ''
  endDate?: number | ''
  page?: number
  size?: number
  sort?: string
}

const { RangePicker } = DatePicker

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const STATUS_STYLE: Record<LogStatus, { label: string; bg: string; text: string }> = {
  SUCCESS: {
    label: 'Thành công',
    bg: 'bg-bg-success-light',
    text: 'text-bg-success-default'
  },
  ERROR: {
    label: 'Thất bại',
    bg: 'bg-bg-error-light',
    text: 'text-bg-error-default'
  }
}

// const ACTIONS_OPTIONS = [
//   { label: 'Tạo mới cấu hình kết nối', value: 'CREATE_ORDER' },
//   { label: 'Cập nhật cấu hình kết nối', value: 'UPDATE_ORDER' },
//   { label: 'Kiểm thử cấu hình kết nối', value: 'TEST_ORDER' },
//   { label: 'Xóa cấu hình kết nối', value: 'DELETE_ORDER' }
// ]

// const data: MappingLog[] = [
//   {
//     key: '1',
//     timestamp: '02/02/2025 - 10:00',
//     action: 'Kiểm thử mapping',
//     actor: 'Admin',
//     status: 'success',
//     note: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit'
//   },
//   {
//     key: '2',
//     timestamp: '03/02/2025 - 11:30',
//     action: 'Cập nhật mapping request',
//     actor: 'Admin',
//     status: 'error',
//     note: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit'
//   },
//   {
//     key: '3',
//     timestamp: '02/02/2025 - 12:00',
//     action: 'Đồng bộ API',
//     actor: 'Admin',
//     status: 'success',
//     note: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit',
//     isLink: true,
//     isNoteLink: true
//   }
// ]

const MappingLogTable = ({ id }: Props) => {
  const [datePickerOpen, setDatePickerOpen] = useState(false)

  const [checked, setChecked] = useState<CheckedState>({
    isActionType: true,
    isActionDescription: true,
    isName: true
  })

  const checkBoxOptions: CheckboxOption[] = [
    { label: 'Hành động', key: 'isActionType' },
    { label: 'Mô tả hành động', key: 'isActionDescription' },
    { label: 'Người thực hiện', key: 'isName' }
  ]

  const defaultSearchParams: IParamsMappingLog = {
    page: 0,
    size: 10,
    startDate: '',
    endDate: ''
  }

  const [filterParams, setFilterParams] = useState(defaultSearchParams)

  const debouncedSetFilterParams = debounce(params => {
    setFilterParams(params)
  }, 200)

  const handleSearch = useCallback(
    (value: string) => {
      debouncedSetFilterParams({
        ...filterParams,
        value: value || '',
        isActionType: Number(Boolean(checked.isActionType)),
        isActionDescription: Number(Boolean(checked.isActionDescription)),
        isName: Number(Boolean(checked.isName))
      })
    },
    [filterParams, checked, debouncedSetFilterParams]
  )

  const {
    page,
    pageSize,
    content,
    pagination,
    setPageSize
    // refetch,
    // isFetching: isLoadingMappingLog
  } = usePage(['mapping-log-list', 'table', id, filterParams], async () => {
    const params = {
      ...filterParams,
      page: page - 1,
      pageSize,
      size: pageSize
    }

    const res = await Logistic.getMappingLog(id, { ...params })

    return res
  })

  const handleFilterChange = (key: string, value: any) => {
    const formattedValue = Array.isArray(value) ? value.join(',') : value

    setFilterParams(prev => {
      const updatedParams = {
        ...prev,
        [key]: formattedValue
      }

      return updatedParams
    })
  }

  const columns: ColumnsType<MappingLog> = [
    {
      title: 'STT',
      key: 'index',
      render: (_, __, index) => index + 1,
      width: 50,
      align: 'center'
    },
    {
      title: 'Thời gian',
      dataIndex: 'createdAt',
      key: 'createdAt',
      align: 'center',
      render: (value: string) => {
        const formatted = dayjs(value, 'DD/MM/YYYY HH:mm').format('DD/MM/YYYY - HH:mm')

        return <>{formatted}</>
      }
    },
    {
      title: 'Hành động',
      dataIndex: 'actionType',
      key: 'actionType',
      align: 'center'
      // render: (value: string) => {
      //   const at = ACTIONS_OPTIONS.find(a => a.value === value)
      //
      //   return at?.label ?? value
      // }
    },
    {
      title: 'Mô tả hành động',
      dataIndex: 'actionDescription',
      key: 'actionDescription',
      align: 'center'
    },
    {
      title: 'Người thực hiện',
      dataIndex: 'userName',
      key: 'userName',
      align: 'center'
    }
    // {
    //   title: 'Trạng thái',
    //   dataIndex: 'status',
    //   key: 'status',
    //   align: 'center',
    //   render: (status: LogStatus) => {
    //     const { label, bg, text } = STATUS_STYLE[status]
    //
    //     return (
    //       <div className='flex justify-center'>
    //         <Tag
    //           className={clsx(
    //             'flex h-5 w-36  items-center justify-center rounded-md border-none text-xs font-medium',
    //             bg,
    //             text
    //           )}
    //         >
    //           {label}
    //         </Tag>
    //       </div>
    //     )
    //   }
    // },
    // {
    //   title: 'Ghi chú / Lỗi nếu có',
    //   dataIndex: 'errorDescription',
    //   key: 'errorDescription',
    //   align: 'center'
    // }
  ]

  const renderPagination = () => (
    <div className='mt-[10px] flex w-full justify-between'>
      <div className='flex gap-4'></div>
      <Pagination {...pagination} showSizeChanger={false} />
      <Select defaultValue={10} options={pageSizeOptions} onChange={value => setPageSize(value)} />
    </div>
  )

  return (
    <div className='flex flex-col gap-y-4'>
      <div className='mb-4 flex items-center gap-x-2'>
        {/* Tìm kiếm */}
        <SettingInput
          placeholder='Tìm kiếm theo hành động/ người dùng hoặc chú thích'
          styles={{ width: '100%' }}
          checked={checked}
          setChecked={setChecked}
          onChange={e => handleSearch(e)}
          checkBoxOptions={checkBoxOptions}
        />

        {/* Filter: Thời gian*/}
        <div className='flex h-6 min-w-[160px] cursor-pointer items-center rounded-lg border border-solid border-[#D9D9D9] py-[19px] text-gray-6'>
          <Popover
            placement='bottom'
            open={datePickerOpen}
            onOpenChange={open => {
              setDatePickerOpen(open)
            }}
            content={
              <RangePicker
                format='DD/MM/YYYY'
                value={[dayjs().startOf('month'), dayjs().endOf('month')]}
                renderExtraFooter={() => (
                  <div className='m-2 flex justify-end'>
                    <Button
                      type='primary'
                      onClick={() => {
                        setDatePickerOpen(false)
                      }}
                    >
                      Xác nhận
                    </Button>
                  </div>
                )}
                autoFocus
                onChange={dates => {
                  if (dates && dates.length === 2) {
                    const [start, end] = dates
                    const startTimestamp = start?.format('DD/MM/YYYY') || ''
                    const endTimestamp = end?.format('DD/MM/YYYY') || ''

                    handleFilterChange('startDate', startTimestamp as any)
                    handleFilterChange('endDate', endTimestamp as any)

                    setDatePickerOpen(false)
                  } else {
                    handleFilterChange('startDate', '' as any)
                    handleFilterChange('endDate', '' as any)
                  }
                }}
              />
            }
            trigger='click'
          >
            <div className='flex'>
              <div className='mx-1 text-sm font-normal'>Khoảng thời gian</div>
              <i className='onedx-calendar size-5' />
            </div>
          </Popover>
        </div>
      </div>

      <Table
        columns={columns}
        dataSource={content || []}
        pagination={false}
        rowClassName='hover:text-blue-600'
        locale={{
          emptyText: (
            <div className='flex items-center justify-center py-20'>
              <EmptySearch title='Danh sách trống' description='Không có dữ liệu' />
            </div>
          )
        }}
      />

      {/* Pagination */}
      {renderPagination()}
    </div>
  )
}

export default MappingLogTable
