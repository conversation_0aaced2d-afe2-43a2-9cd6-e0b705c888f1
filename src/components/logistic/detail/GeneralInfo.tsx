import { useMemo } from 'react'

import { useRouter } from 'next/navigation'

import { useQuery } from '@tanstack/react-query'

import { DetailInfoCustomField } from '@/components/custom-field/selectors'
import Logistic from '@/models/Logistic'
import type { LogisticCarrier } from '@/types/logistic/carrier'

import OrderList from './OrderList'
import { CustomCollapse } from '../common/CustomCollapse'

interface Props {
  id: string
}

const GeneralInfor = ({ id }: Props) => {
  const router = useRouter()

  const { data: generalInfo } = useQuery<LogisticCarrier>({
    queryKey: ['carrierDetail', id],
    queryFn: () => Logistic.getCarrierDetail(id),
    enabled: !!id
  })

  const general = useMemo(() => {
    if (!generalInfo) return []

    return [
      { label: 'Tên đơn vị vận chuyển', value: generalInfo.name },
      { label: 'Mã định danh', value: generalInfo.code },
      {
        label: 'Logo đơn vị vận chuyển',
        value: generalInfo.logoUrl ? 'Xem file' : 'Chưa có thông tin',
        linkUri: generalInfo.logoUrl
      },
      { label: 'Mô tả', value: generalInfo.description }
    ]
  }, [generalInfo])

  const support = useMemo(() => {
    if (!generalInfo) return []

    return [
      { label: 'Email', value: generalInfo.supportEmail },
      { label: 'Số điện thoại', value: generalInfo.supportPhone }
    ]
  }, [generalInfo])

  return (
    <div className='flex flex-col gap-y-4'>
      <CustomCollapse title='Thông tin chung' onEdit={() => router.push(`/carrier/update/${id}`)}>
        <div className='grid grid-cols-4 gap-4'>
          {general?.map((item, index) => <DetailInfoCustomField {...item} key={index} />)}
        </div>
      </CustomCollapse>

      <CustomCollapse title='Thông số liên hệ kỹ thuật' onEdit={() => router.push(`/carrier/update/${id}`)}>
        <div className='grid grid-cols-4 gap-4'>
          {support?.map((item, index) => <DetailInfoCustomField {...item} key={index} />)}
        </div>
      </CustomCollapse>

      <CustomCollapse title='Danh sách đơn hàng áp dụng đơn vị vận chuyển'>
        <OrderList id={id} />
      </CustomCollapse>
    </div>
  )
}

export default GeneralInfor
