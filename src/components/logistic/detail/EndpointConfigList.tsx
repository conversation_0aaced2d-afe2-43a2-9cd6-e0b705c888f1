'use client'

import React, { useMemo, useState } from 'react'

import { useParams } from 'next/navigation'

import { Table, Select, Pagination, DatePicker, Popover, Button } from 'antd'
import type { ColumnsType } from 'antd/es/table'

import dayjs from 'dayjs'

import EndpointConfigDetail from './EndpointConfigDetail'
import { pageSizeOptions } from '@/views/product-catalog/constants/constants'
import type { CheckboxOption, CheckedState } from '@/components/filter'
import { useDebounceValue } from '@/hooks/useDebounceValue'
import useGetEndpontList from '@/hooks/useGetEndpointList'
import SettingInput from '@/components/filter/SettingInput'
import SelectFilter from '@/views/product-catalog/common/SelectFilter'
import { LogisticCard, LogisticTag } from '../common/LogisticComponents'
import { authTypeOptions, methodOptions, STATE_OPTIONS } from '../common/constant'
import type { ConfigItem, FilterParam } from '@/types/logistic/carrier'
import { EmptySearch } from '@/components/inventory/common/EmptySearch'

const { RangePicker } = DatePicker

const EndpointConfigList = () => {
  const { id } = useParams() as { id: string }

  // Quick search
  const [searchValue, setSearchValue] = useState('')

  const quickSearchValue = useDebounceValue(searchValue, 300)

  const [checked, setChecked] = useState<CheckedState>({
    isAction: true,
    isEndpoint: true
  })

  const checkBoxOptions: CheckboxOption[] = [
    { label: 'Hành động', key: 'isAction' },
    { label: 'Endpont URL', key: 'isEndpoint' }
  ]

  // Advanced search
  const [filterParam, setFilterParam] = useState<FilterParam>({
    states: [],
    connectMethods: null,
    authType: null,
    startDate: null,
    endDate: null
  })

  const defaultFilter = useMemo(() => {
    return {
      states: [],
      connectMethods: null,
      authType: null,
      startDate: null,
      endDate: null
    }
  }, [])

  const [datePickerOpen, setDatePickerOpen] = useState(false)

  const { endpointsData, pagination, setPageSize } = useGetEndpontList({
    id,
    quickSearchValue,
    checked,
    filterParam
  })

  const [viewDetail, setViewDetail] = useState(false)
  const [actionCode, setActionCode] = useState('')

  const columns: ColumnsType<ConfigItem> = [
    {
      title: 'STT',
      key: 'index',
      render: (_, __, index) => index + 1,
      width: 50
    },
    {
      title: 'Hành động',
      dataIndex: 'action',
      key: 'action',
      render: (text: string, record) => (
        <div
          className='cursor-pointer'
          onClick={() => {
            setViewDetail(true)
            setActionCode(record.actionType)
          }}
        >
          {text}
        </div>
      )
    },
    {
      title: 'Endpoint URL',
      dataIndex: 'endpointUrl',
      key: 'endpointUrl'
    },
    {
      title: 'Phương thức kết nối',
      dataIndex: 'connectionMethod',
      key: 'connectionMethod'
    },
    {
      title: 'Phương thức xác thực API',
      dataIndex: 'authenticationMethod',
      key: 'authenticationMethod'
    },
    {
      title: 'Thời gian cập nhật',
      dataIndex: 'modifiedAt',
      key: 'modifiedAt',
      render: (value: string | null) => value ?? '--'
    },
    {
      title: 'Trạng thái',
      dataIndex: 'state',
      key: 'state',
      render: text => {
        const option = STATE_OPTIONS.find(item => item.value === text)

        return option ? <LogisticTag color={option.type}>{option.label}</LogisticTag> : text
      }
    }
  ]

  const handleFilterChange = (key: keyof FilterParam, value: any) => {
    setFilterParam(prev => ({ ...prev, [key]: value }))
  }

  const listFilter: any = [
    {
      name: 'connectMethods',
      label: 'Phương thức kết nối',
      options: methodOptions,
      multiple: true,
      canSearch: true,
      placeHolder: 'Tất cả'
    },
    {
      name: 'authType',
      label: 'Phương thức xác thực API',
      options: authTypeOptions
    }
  ]

  return (
    <LogisticCard title='Cấu hình kết nối API'>
      <div className='flex gap-4'>
        <SettingInput
          placeholder='Tìm kiếm theo hành động hoặc endpoint URL'
          styles={{ width: '100%' }}
          checked={checked}
          setChecked={setChecked}
          onKeyDown={(event: React.KeyboardEvent<HTMLInputElement>) => {
            if (event.key === 'Enter') {
              event.preventDefault()
              setSearchValue(event.currentTarget.value)
            }
          }}
          checkBoxOptions={checkBoxOptions}
        />

        <Select
          className='w-60'
          mode='multiple'
          allowClear
          placeholder='Trạng thái'
          options={STATE_OPTIONS}
          value={filterParam?.states}
          onChange={value => {
            setFilterParam(prev => ({ ...prev, states: value }))
          }}
        />

        {/* Khoảng thời gian */}
        <Popover
          open={datePickerOpen}
          onOpenChange={setDatePickerOpen}
          placement='bottom'
          trigger='click'
          content={
            <RangePicker
              format='DD/MM/YYYY'
              defaultValue={
                filterParam.startDate && filterParam.endDate
                  ? [dayjs(filterParam.startDate, 'DD/MM/YYYY'), dayjs(filterParam.endDate, 'DD/MM/YYYY')]
                  : undefined
              }
              renderExtraFooter={() => (
                <div className='m-2 flex justify-end'>
                  <Button type='primary' onClick={() => setDatePickerOpen(false)}>
                    Xác nhận
                  </Button>
                </div>
              )}
              onChange={dates => {
                if (dates && dates.length === 2) {
                  const [start, end] = dates

                  handleFilterChange('startDate', start?.format('DD/MM/YYYY'))
                  handleFilterChange('endDate', end?.format('DD/MM/YYYY'))
                } else {
                  handleFilterChange('startDate', null)
                  handleFilterChange('endDate', null)
                }
              }}
            />
          }
        >
          <div className='flex h-6 w-60 cursor-pointer items-center rounded-lg border border-solid border-[#D9D9D9] px-3 py-[19px] text-gray-6'>
            <div className='mx-1 text-sm font-normal'>Khoảng thời gian</div>
            <i className='onedx-calendar size-5' />
          </div>
        </Popover>

        <SelectFilter
          setFilterParams={setFilterParam}
          filterOptions={listFilter}
          filterParams={filterParam}
          defaultFilter={defaultFilter}
          customStyle={'h-[40px]'}
        />
      </div>

      <Table
        className='my-4'
        columns={columns}
        dataSource={endpointsData}
        pagination={false}
        rowClassName='hover:text-blue-600'
        locale={{
          emptyText: (
            <div className='flex items-center justify-center py-20'>
              <EmptySearch title='Danh sách trống' description='Không có dữ liệu' />
            </div>
          )
        }}
      />

      <div className='flex items-center'>
        <div className='flex flex-1 justify-center'>
          <Pagination {...pagination} showSizeChanger={false} />
        </div>
        <Select defaultValue={10} options={pageSizeOptions} onChange={value => setPageSize(value)} />
      </div>

      <EndpointConfigDetail action={actionCode} open={viewDetail} setOpen={setViewDetail} />
    </LogisticCard>
  )
}

export default EndpointConfigList
