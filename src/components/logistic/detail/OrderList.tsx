import React, { useCallback, useState } from 'react'

import { But<PERSON>, DatePicker, Pagination, Popover, Select, Table, Tag, Tooltip } from 'antd'
import { styled } from 'styled-components'
import type { ColumnsType } from 'antd/es/table'

import classNames from 'classnames'

import { debounce } from 'lodash'
import dayjs from 'dayjs'
import customParseFormat from 'dayjs/plugin/customParseFormat'

import { EmptyData } from '@views/management/current/EmptyData'

import { pageSizeOptions } from '@/views/product-catalog/constants/constants'
import SettingInput, { type CheckboxOption, type CheckedState } from '@components/filter/SettingInput'
import Logistic from '@/models/Logistic'
import { usePage } from '@/hooks'
import DXPortal from '@/models/DXPortal'

dayjs.extend(customParseFormat)

type OrderStatus = 0 | 1 | 2 | 3

interface Props {
  id: string
}

interface Order {
  key: string
  shippingCode: string
  productName: string[]
  amount: string
  orderDate: string
  progressOrder: OrderStatus
}

interface IParamsApplyOrder {
  value?: string
  isCode?: number
  isService?: number
  status?: string
  startDate?: number | ''
  endDate?: number | ''
  classification?: string
  page?: number
  size?: number
  sort?: string
}

const { RangePicker } = DatePicker

const CustomMultiSelect = styled(Select)`
  .ant-select-selection-wrap {
    block-size: 100% !important;
  }
`

const PROGRESS_ORDER = [
  { label: 'Tiếp nhận đơn hàng', value: 0 },
  { label: 'Đang xử lý', value: 1 },
  { label: 'Đã hoàn tất', value: 2 },
  { label: 'Đã hủy', value: 3 }
]

const SERVICE_TYPE = [
  { label: 'Tất cả', value: 'UNSET' },
  { label: 'Hàng hóa vật lý', value: 'PHYSICAL' },
  { label: 'Giải pháp - Gói dịch vụ', value: 'SERVICE' }
]

const STATUS_STYLES: Record<OrderStatus, { label: string; bgColor: string; textColor: string }> = {
  0: {
    label: 'Tiếp nhận đơn hàng',
    bgColor: 'bg-bg-info-light',
    textColor: 'text-bg-info-default'
  },
  1: {
    label: 'Đang xử lý',
    bgColor: 'bg-bg-info-light',
    textColor: 'text-bg-info-default'
  },
  2: {
    label: 'Đã hoàn tất',
    bgColor: 'bg-bg-success-light',
    textColor: 'text-bg-success-default'
  },
  3: {
    label: 'Đã huỷ',
    bgColor: 'bg-bg-error-light',
    textColor: 'text-bg-error-default'
  }
}

const OrderList = ({ id }: Props) => {
  const [datePickerOpen, setDatePickerOpen] = useState(false)

  const [checked, setChecked] = useState<CheckedState>({
    isCode: true,
    isService: true
  })

  const checkBoxOptions: CheckboxOption[] = [
    { label: 'Mã đơn hàng', key: 'isCode' },
    { label: 'Te SPDV', key: 'isService' }
  ]

  const defaultSearchParams: IParamsApplyOrder = {
    page: 0,
    size: 10,
    startDate: '',
    endDate: '',
    classification: 'UNSET'
  }

  const [filterParams, setFilterParams] = useState(defaultSearchParams)

  const debouncedSetFilterParams = debounce(params => {
    setFilterParams(params)
  }, 200)

  const handleSearch = useCallback(
    (value: string) => {
      debouncedSetFilterParams({
        ...filterParams,
        value: value || '',
        isCode: Number(Boolean(checked.isCode)),
        isService: Number(Boolean(checked.isService))
      })
    },
    [filterParams, checked, debouncedSetFilterParams]
  )

  const { page, pageSize, content } = usePage(['apply-order-list', 'table', id, filterParams], async () => {
    const params = {
      ...filterParams,
      page: page - 1,
      pageSize,
      size: pageSize
    }

    const res = await Logistic.getApplyOrder(id, { ...params })

    return res
  })

  const handleFilterChange = (key: string, value: any) => {
    const formattedValue = Array.isArray(value) ? value.join(',') : value

    setFilterParams(prev => {
      const updatedParams = {
        ...prev,
        [key]: formattedValue
      }

      return updatedParams
    })
  }

  const renderProducts = (productName: string | string[]) => {
    const productList: string[] = Array.isArray(productName)
      ? productName
      : productName.split(',').map((p: string) => p.trim())

    const [first, ...rest] = productList

    return (
      <div className='flex justify-center'>
        <Tag
          color='default'
          className={classNames(
            'bg-[#f5f5f5] text-[#333] border-none rounded transition-all duration-200',
            'group-hover:bg-[#e6f4ff] group-hover:text-[#1677ff]'
          )}
        >
          {first}
        </Tag>
        {rest.length > 0 && (
          <Tooltip title={rest.join(', ')}>
            <Tag className='cursor-pointer rounded '>+{rest.length}</Tag>
          </Tooltip>
        )}
      </div>
    )
  }

  const renderStatusTag = (progressOrder: OrderStatus) => {
    const { label, bgColor, textColor } = STATUS_STYLES[progressOrder]

    return (
      <div className='flex justify-center'>
        <Tag
          className={classNames(
            'rounded-md items-center flex justify-center w-36 h-5 border-none text-xs font-medium',
            bgColor,
            textColor
          )}
        >
          {label}
        </Tag>
      </div>
    )
  }

  const columns: ColumnsType<Order> = [
    {
      title: 'STT',
      dataIndex: 'key',
      key: 'key',
      align: 'center',
      render: (_, __, index) => index + 1
    },
    {
      title: 'Mã đơn hàng',
      dataIndex: 'shippingCode',
      key: 'shippingCode',
      align: 'center'
    },
    {
      title: 'Sản phẩm',
      dataIndex: 'productName',
      key: 'productName',
      align: 'center',
      render: renderProducts
    },
    {
      title: 'Số tiền',
      dataIndex: 'amount',
      key: 'amount',
      align: 'center',
      render: (amount: number) => <div>đ{DXPortal.formatStringCurrency(amount)}</div>
    },
    {
      title: 'Ngày đặt hàng',
      dataIndex: 'orderDate',
      key: 'orderDate',
      align: 'center',
      render: (value: string) => (value ? dayjs(value, 'DD/MM/YYYY HH:mm').format('DD/MM/YYYY - HH:mm') : '--')
    },
    {
      title: 'Tiến trình đơn hàng',
      dataIndex: 'progressOrder',
      key: 'progressOrder',
      align: 'center',
      render: renderStatusTag
    }
  ]

  return (
    <div className='flex flex-col gap-y-4'>
      <div className='mb-4 flex items-center gap-x-2'>
        {/* Tìm kiếm */}
        <SettingInput
          placeholder='Tìm kiếm theo mã đơn hàng'
          styles={{ width: '100%' }}
          checked={checked}
          setChecked={setChecked}
          onChange={e => handleSearch(e)}
          checkBoxOptions={checkBoxOptions}
        />

        {/* Filter: Tiến trình đơn hàng */}
        <CustomMultiSelect
          showSearch
          optionFilterProp='label'
          filterOption={(input, option) => (option?.label ?? '').toLowerCase().includes(input.toLowerCase())}
          allowClear
          className='h-[40px] min-w-[240px]'
          placeholder={<div className='text-gray-6'>Tiến trình đơn hàng</div>}
          options={PROGRESS_ORDER}
          maxTagCount={1}
          mode='multiple'
          onChange={value => handleFilterChange('status', value)}
        />

        {/* Filter: Loại dịch vụ đã đặt */}
        <CustomMultiSelect
          showSearch
          optionFilterProp='label'
          filterOption={(input, option) => (option?.label ?? '').toLowerCase().includes(input.toLowerCase())}
          allowClear
          className='h-[40px] min-w-[240px]'
          placeholder={<div className='text-gray-6'>Loại dịch vụ đã đặt</div>}
          options={SERVICE_TYPE}
          maxTagCount={1}
          mode='multiple'
          onChange={value => handleFilterChange('classification', value)}
        />

        {/* Filter: Thời gian*/}
        <div className='flex h-6 min-w-[135px] cursor-pointer items-center rounded-lg border border-solid border-[#D9D9D9] bg-white py-[19px] text-gray-6'>
          <Popover
            placement='bottom'
            open={datePickerOpen}
            onOpenChange={open => {
              setDatePickerOpen(open)
            }}
            content={
              <RangePicker
                format='DD/MM/YYYY'
                renderExtraFooter={() => (
                  <div className='m-2 flex justify-end'>
                    <Button
                      type='primary'
                      onClick={() => {
                        setDatePickerOpen(false)
                      }}
                    >
                      Xác nhận
                    </Button>
                  </div>
                )}
                autoFocus
                onChange={dates => {
                  if (dates && dates.length === 2) {
                    const [start, end] = dates
                    const startTimestamp = start?.format('DD/MM/YYYY') || ''
                    const endTimestamp = end?.format('DD/MM/YYYY') || ''

                    handleFilterChange('startDate', startTimestamp as any)
                    handleFilterChange('endDate', endTimestamp as any)

                    setDatePickerOpen(false)
                  } else {
                    handleFilterChange('startDate', '' as any)
                    handleFilterChange('endDate', '' as any)
                  }
                }}
              />
            }
            trigger='click'
          >
            <div className='flex'>
              <div className='mx-1 text-sm font-normal'>Ngày đặt hàng</div>
              <i className='onedx-calendar size-5' />
            </div>
          </Popover>
        </div>
      </div>

      <Table
        columns={columns}
        dataSource={content || []}
        pagination={false}
        rowClassName={() => 'group hover:bg-gray-50 hover:text-blue-600'}
        locale={{
          emptyText: <EmptyData label='Không có dữ liệu theo thông tin tìm kiếm' />
        }}
      />

      <div className='flex items-center'>
        <div className='flex flex-1 justify-center'>
          <Pagination showSizeChanger={false} />
        </div>
        <Select defaultValue={10} options={pageSizeOptions} />
      </div>
    </div>
  )
}

export default OrderList
