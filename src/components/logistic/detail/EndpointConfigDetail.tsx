import React, { useState } from 'react'

import { useParams, useRouter } from 'next/navigation'

import { But<PERSON>, Drawer, Table, Tabs } from 'antd'
import type { ColumnsType } from 'antd/es/table'

import { useQuery } from '@tanstack/react-query'

import { DetailInfoCustomField } from '@/components/custom-field/selectors'
import { LogisticCard } from '../common/LogisticComponents'
import { stylesCard } from '../common/constant'
import Logistic from '@/models/Logistic'
import { buildRequestTable, buildResponseTable } from '@/views/inventory/common/hooks/useConvertAndValidation'
import { DataMappingRequestTab } from '@/views/inventory/connection-config/detail/DataMappingRequestTab'
import { DataMappingResponseTab } from '@/views/inventory/connection-config/detail/DataMappingResponseTab'
import type { EndpointConfigDetailProps, ErrorMapping } from '@/types/logistic/carrier'
import { TabKey } from '@/types/logistic/carrier'

const EndpointConfigDetail: React.FC<EndpointConfigDetailProps> = ({ open, setOpen, action }) => {
  const { id } = useParams() as { id: string }

  const router = useRouter()

  const [activeTab, setActiveTab] = useState<TabKey>(TabKey.Request)

  const [initRequestTable, setInitRequestTable] = useState<any>([])

  const [initResponseTable, setInitResponseTable] = useState<any>([])

  const { data } = useQuery({
    queryKey: ['getEndpointConfigDetail'],
    queryFn: async () => {
      const res = await Logistic.getEndpointConfigDetail(id, action)

      setInitRequestTable(buildRequestTable(res?.dxReqFields, res?.inputMapping, res?.partnerReqFields))

      setInitResponseTable(buildResponseTable(res?.dxRespFields, res?.outputMapping, res?.partnerRespFields))

      return res
    },
    enabled: !!action
  })

  const handleNavigateUpdate = () => router.push(`/carrier/update/${id}?openDrawer=${data?.action}`)

  const formatJsonForPopup = (jsonData: any) => {
    try {
      const formattedJson = JSON.stringify(JSON.parse(JSON.stringify(jsonData)), null, 2).trim()

      return `<pre>${formattedJson}</pre>`
    } catch (error) {
      return '<div>Không thể hiển thị dữ liệu</div>'
    }
  }

  const columns: ColumnsType<ErrorMapping> = [
    {
      title: 'HTTP',
      dataIndex: 'httpStatusCode',
      key: 'httpStatusCode',
      width: 80,
      align: 'center'
    },
    {
      title: 'Mã lỗi (error_code)',
      dataIndex: 'partnerCode',
      key: 'partnerCode',
      width: 200
    },
    {
      title: 'Thông điệp (error_message)',
      dataIndex: 'note',
      key: 'note',
      width: 250
    },
    {
      title: 'Logi UI (Mapping Marketplace)',
      dataIndex: 'bosCode',
      key: 'bosCode',
      width: 250
    }
  ]

  return (
    <Drawer
      width='60%'
      onClose={() => setOpen(false)}
      open={open}
      closable={false}
      title={
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-x-4 text-headline-16 font-semibold text-text-neutral-strong'>
            <div className='flex size-10 items-center justify-center rounded-full bg-bg-primary-light'>
              <i className='onedx-open-new-tab size-6 text-text-primary-default' />
            </div>
            {`Chi tiết cấu hình "${data?.actionName}"`}
          </div>
          <i className='onedx-close-icon size-5 text-icon-neutral-medium' onClick={() => setOpen(false)} />
        </div>
      }
    >
      <div className='flex flex-col gap-y-4'>
        <LogisticCard
          title='Thông tin chung'
          styles={stylesCard}
          rightContent={
            <Button color='primary' variant='text' onClick={handleNavigateUpdate}>
              <i className='onedx-edit size-4' /> Chỉnh sửa
            </Button>
          }
        >
          <div className='grid grid-cols-4 gap-4'>
            {[
              { label: 'Chọn hành động', value: data?.actionName },
              { label: 'Phương thức kết nối', value: data?.apiConfig?.method },
              { label: 'URL Endpoint API', value: data?.apiConfig?.url?.raw },
              { label: 'Timeout (ms)', value: data?.timeout },
              {
                label: 'Params/ Parth variable',
                value: 'Xem chi tiết',
                descriptionPopup: formatJsonForPopup(data?.apiConfig?.url || {})
              },
              { label: 'Phương thức xác thực API', value: data?.apiConfig?.auth?.type },
              { label: 'Header', value: data?.apiConfig?.headers?.[0]?.value },
              { label: 'Token Value', value: data?.apiConfig?.auth?.bearer?.value }
            ].map((item, index) => (
              <DetailInfoCustomField {...item} key={index} />
            ))}
          </div>
        </LogisticCard>

        <LogisticCard
          title='Cấu hình thử lại'
          styles={stylesCard}
          rightContent={
            <Button color='primary' variant='text' onClick={handleNavigateUpdate}>
              <i className='onedx-edit size-4' /> Chỉnh sửa
            </Button>
          }
        >
          <div className='grid grid-cols-4 gap-4'>
            {[
              { label: 'Số lần thử lại (retry)', value: data?.retryNumber },
              { label: 'Thời gian giữa các lần thử (ms)', value: data?.retryInterval }
            ].map((item, index) => (
              <DetailInfoCustomField {...item} key={index} />
            ))}
          </div>
        </LogisticCard>

        <LogisticCard
          title='Ánh xạ dữ liệu'
          styles={stylesCard}
          rightContent={
            <div className='flex  gap-3'>
              <Tabs
                activeKey={activeTab}
                onChange={key => setActiveTab(key as TabKey)}
                items={[
                  {
                    label: 'Request',
                    key: TabKey.Request
                  },
                  {
                    label: 'Response',
                    key: TabKey.Response
                  }
                ]}
                className='!h-6 !items-center !justify-center'
              />
              <div className='h-3 w-0 outline outline-1 outline-offset-[-0.50px] outline-border-neutral-medium/20' />

              <Button color='primary' variant='text' onClick={handleNavigateUpdate}>
                <i className='onedx-edit size-4' /> Chỉnh sửa
              </Button>
            </div>
          }
        >
          <div className='flex flex-col gap-y-4'>
            {activeTab === TabKey.Request && (
              <div className=' overflow-x-auto'>
                <DataMappingRequestTab data={initRequestTable} />
              </div>
            )}
            {activeTab === TabKey.Response && (
              <div className=' overflow-x-auto'>
                <DataMappingResponseTab data={initResponseTable} />
              </div>
            )}

            <div className='flex items-center gap-2'>
              <i className='onedx-dot size-3 bg-bg-success-default' />
              <span className='text-text-neutral-light'>Xử lý lỗi và mapping response</span>
            </div>

            <Table
              columns={columns}
              dataSource={data?.errorMapping?.map((item: any, idx: any) => ({ ...item, key: idx.toString() }))}
              pagination={false}
            />
          </div>
        </LogisticCard>
      </div>
    </Drawer>
  )
}

export default EndpointConfigDetail
