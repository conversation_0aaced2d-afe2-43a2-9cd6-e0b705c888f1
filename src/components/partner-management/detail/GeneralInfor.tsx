import React, { useState } from 'react'

import { CaretDownOutlined, CaretUpOutlined } from '@ant-design/icons'
import { useQuery } from '@tanstack/react-query'

import moment from 'moment'

import DashboardManagement from '@/models/DashboardManagement'
import { RevenueInforModal } from './modal/RevenueInforModal'

import type { CustomerInfo, RevenueInfo, SalesInfo } from '@/types/partner-portal/partner'
import { DATE_FORMAT, DEFAULT_END_DATE, DEFAULT_START_DATE, MONTH_TYPE } from '../common/constants'
import { calculateGrowthRate, formatCurrency, getCurrentOfMonth, getStartOfMonth } from '@/utils/partnerUtils'
import { SectionHeader } from '../common/SectionHeader'
import { OrderRevenueDashboard } from './OrderRevenueDashboard'
import { TopSubServiceChart } from './TopSubServiceChart'

export const GeneralInfor = ({ id }: { id: number | string }) => {
  const DEFAULT_DATE = '01/01/1970'

  const { data: customerInfor }: { data: CustomerInfo | undefined } = useQuery({
    queryKey: ['overviewCustomerByMonth'],
    queryFn: async () => {
      const res = await DashboardManagement.getOverviewCustomer({ id })

      return res
    },
    enabled: !!id
  })

  const { data: revenueInfor }: { data: RevenueInfo | undefined } = useQuery({
    queryKey: ['overviewRevenueByMonth'],
    queryFn: async () => {
      const res = await DashboardManagement.overviewRevenueByMonth({ id })

      return res
    },
    enabled: !!id
  })

  const [timeParams, setTimeParams] = useState({
    startTime: DEFAULT_DATE,
    endTime: moment().format(DATE_FORMAT)
  })

  const [isModalVisible, setIsModalVisible] = useState(false)

  const onSettingParamDate = (startTime: string, endTime: string) => {
    setTimeParams({ startTime, endTime })
  }

  const previewRevenue = (monthType: number) => {
    switch (monthType) {
      // case thời gian tháng này
      case MONTH_TYPE.THIS_MONTH:
        onSettingParamDate(moment().startOf('month').format(DATE_FORMAT), DEFAULT_END_DATE)
        break
      // case thời gian tháng trước
      case MONTH_TYPE.LAST_MONTH:
        onSettingParamDate(getStartOfMonth(1), getCurrentOfMonth(1))
        break
      // case thời gian 2 tháng trước
      case MONTH_TYPE.TWO_MONTH:
        onSettingParamDate(getStartOfMonth(2), getCurrentOfMonth(2))
        break
      // case thời gian 3 tháng trước
      case MONTH_TYPE.THREE_MONTH:
        onSettingParamDate(getStartOfMonth(3), getCurrentOfMonth(3))
        break
      default:
        onSettingParamDate(DEFAULT_START_DATE, DEFAULT_END_DATE)
    }

    setIsModalVisible(true)
  }

  const generalInfo = [
    { label: 'Tổng khách hàng (Hiện tại)', value: customerInfor?.totalUser },
    { label: 'Tổng đơn hàng', value: customerInfor?.totalSub },
    { label: 'Sản phẩm cung cấp', value: customerInfor?.totalService },
    { label: 'Giải pháp cung cấp', value: customerInfor?.totalSolution }
  ]

  // Tạo salesInfor từ revenueInfor
  const salesInfor: SalesInfo | null = React.useMemo(() => {
    if (!revenueInfor) return null

    const { total, currentMonth, oneMonthAgo, twoMonthAgo, threeMonthAgo } = revenueInfor

    return {
      total: formatCurrency(total),
      detail: [
        {
          label: 'Tháng này',
          value: formatCurrency(currentMonth)
        },
        {
          label: 'Tháng trước',
          value: formatCurrency(oneMonthAgo),
          rate: calculateGrowthRate(currentMonth, oneMonthAgo)
        },
        {
          label: '2 tháng trước',
          value: formatCurrency(twoMonthAgo),
          rate: calculateGrowthRate(currentMonth, twoMonthAgo)
        },
        {
          label: '3 tháng trước',
          value: formatCurrency(threeMonthAgo),
          rate: calculateGrowthRate(currentMonth, threeMonthAgo)
        }
      ]
    }
  }, [revenueInfor])

  // Loading state
  if (!salesInfor) {
    return <div>Loading...</div>
  }

  return (
    <div className='flex flex-col gap-2 bg-bg-neutral-lighter'>
      {/* General Info */}
      <div className='flex flex-col gap-5 rounded-xl bg-bg-surface p-4'>
        <SectionHeader icon='onedx-user' title='Tổng quan' />

        {/* Stats Grid */}
        <div className='flex w-full items-center gap-7'>
          {generalInfo.map((stat, index) => (
            <React.Fragment key={stat.label}>
              <div className='group flex flex-1 flex-col gap-1 rounded-xl px-3 pt-3 transition-colors duration-200 ease-in-out hover:bg-bg-primary-default'>
                <div className='text-sm font-normal leading-tight tracking-tight text-text-neutral-strong group-hover:text-white'>
                  {stat.label}
                </div>
                <div className='text-title-36 font-semibold leading-[56px] text-text-neutral-medium group-hover:text-white'>
                  {stat?.value?.toLocaleString()}
                </div>
              </div>
              {index < generalInfo.length - 1 && <div className='h-16 w-px bg-border-neutral-light/10' />}
            </React.Fragment>
          ))}
        </div>
      </div>

      {/* Sales Info */}
      <div className='grid grid-cols-2 gap-2'>
        {/* Left block */}
        <div className='flex flex-col gap-5 bg-bg-surface p-4'>
          <SectionHeader icon='onedx-money-invoice' title='Doanh thu' />

          {/* Tổng doanh thu */}
          <div>
            <div className='text-sm font-normal text-text-neutral-strong'>Tổng (Hiện tại)</div>
            <div className='flex items-start font-semibold text-text-primary-default-hover'>
              <span className='text-title-32 font-normal'>₫</span>
              <span className='text-title-42 font-semibold'>{salesInfor.total}</span>
            </div>
          </div>

          <div className='h-px w-full bg-border-neutral-light/10' />

          <div className='grid grid-cols-2 gap-px rounded-xl border border-border-neutral-light/10 bg-border-neutral-light/10'>
            {salesInfor.detail.map((item, index) => (
              <div
                key={index}
                className={`group bg-bg-surface px-3 py-2 hover:bg-bg-primary-default ${
                  index === 0
                    ? 'rounded-tl-xl'
                    : index === 1
                      ? 'rounded-tr-xl'
                      : index === 2
                        ? 'rounded-bl-xl'
                        : 'rounded-br-xl'
                }`}
                onClick={() => previewRevenue(index)}
              >
                <div className='text-sm font-normal text-text-neutral-strong group-hover:text-white'>{item.label}</div>
                <div className='flex items-center gap-2 group-hover:text-white'>
                  <span className='text-title-36 font-semibold'>{item.value}</span>
                  {!!item.rate && (
                    <span
                      className={`text-sm group-hover:text-white ${
                        parseFloat(item.rate) < 0 ? 'text-red-500' : 'text-green-500'
                      }`}
                    >
                      {parseFloat(item.rate) < 0 ? <CaretDownOutlined /> : <CaretUpOutlined />}{' '}
                      {Math.abs(parseFloat(item.rate))}%
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        <OrderRevenueDashboard chartName='orderRevenueChart' />
      </div>

      <div className='flex flex-col gap-5 rounded-xl bg-bg-surface p-4'>
        <TopSubServiceChart chartName='topsubServiceChart' />
      </div>

      <RevenueInforModal id={id} open={isModalVisible} setOpen={setIsModalVisible} timeParams={timeParams} />
    </div>
  )
}
