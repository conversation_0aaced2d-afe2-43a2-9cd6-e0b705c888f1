import React from 'react'

import { Tabs } from 'antd'

import { useChartData } from '@/hooks/useChartData'
import { processRevenueData } from '@/utils/partnerUtils'
import { RevenueColumnChart } from './component/RevenueColumnChart'
import { FilterButtons } from './component/FilterButton'
import { CHART_TYPE } from '../common/constants'

export const OrderRevenueDashboard = (props: any) => {
  const { chartName } = props

  const {
    activeTab,
    reportCycle,
    handleTabChange,
    setReportCycle,
    growthData,
    cumulativeData,
    isLoadingGrowth,
    isLoadingCumulative
  } = useChartData(chartName, 'overviewServiceRevenue')

  const processedGrowthData = processRevenueData(growthData)
  const processedCumulativeData = processRevenueData(cumulativeData)

  const handleChartSelect = (selectedData: any, chartType: string) => {
    console.log(`Selected ${chartType} data:`, selectedData)
  }

  const revenueDashboardTabs = [
    {
      key: 'GROWTH',
      label: 'Tăng trưởng',
      children: (
        <RevenueColumnChart
          chartName={`${CHART_TYPE.GROWTH}-${chartName}`}
          topAreaData={processedGrowthData}
          chartType={CHART_TYPE.GROWTH}
          isLoadingButton={isLoadingGrowth}
          getSelectArea={(data: any) => handleChartSelect(data, CHART_TYPE.GROWTH)}
          onFilterChart={(filters: any) => {
            console.log('Growth chart filters:', filters)
          }}
        />
      )
    },
    {
      key: 'CUMULATIVE',
      label: 'Lũy kế',
      children: (
        <RevenueColumnChart
          chartName={`${CHART_TYPE.CUMULATIVE}-${chartName}`}
          topAreaData={processedCumulativeData}
          chartType={CHART_TYPE.CUMULATIVE}
          isLoadingButton={isLoadingCumulative}
          getSelectArea={(data: any) => handleChartSelect(data, CHART_TYPE.CUMULATIVE)}
          onFilterChart={(filters: any) => {
            console.log('Cumulative chart filters:', filters)
          }}
        />
      )
    }
  ]

  return (
    <div className='bg-bg-surface'>
      <Tabs
        activeKey={activeTab}
        onChange={handleTabChange}
        tabBarExtraContent={<FilterButtons reportCycle={reportCycle} onTimeFilter={setReportCycle} />}
        renderTabBar={
          ((props: any, TabBar: React.ComponentClass) => (
            <div className='rounded-b-2xl px-4 pb-1 pt-3'>
              <TabBar {...props} />
            </div>
          )) as any
        }
        items={revenueDashboardTabs.map((item: any) => ({
          forceRender: true,
          style: {
            borderRadius: 12,
            background: '#FFFFFF',
            padding: 16,
            overflow: 'hidden',
            maxWidth: '100%'
          },
          ...item
        }))}
      />
    </div>
  )
}
