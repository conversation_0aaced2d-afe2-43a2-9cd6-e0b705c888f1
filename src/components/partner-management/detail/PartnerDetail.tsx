import { useState } from 'react'

import { useRouter } from 'next/navigation'

import { Avatar, Button, Tabs } from 'antd'

import { PartnerTag, PartnerTitle } from '../common'
import { GeneralInfor } from './GeneralInfor'
import { DetailInfor } from './DetailInfor'
import { Document } from './Document'
import { useUser } from '@/hooks'
import { usePartnerDetailInfo } from '@/hooks/usePartnerDetailInfor'
import { handleSrcImg } from '@/utils/string'
import { PARTNER_STATUS, statusColorMap, statusTextMap } from '../common/constants'
import { usePartnerActions } from '@/hooks/usePartnerAction'
import { ApprovalPopover } from './component/ApprovalPopover'
import { ApprovalModal } from './modal/ApprovalModal'
import { PasswordModal } from './modal/UpdatePasswordModal'
import { ActivityLog } from './ActivityLog'

export const PartnerDetail = ({ id, isDashboard }: { id: number | string; isDashboard?: boolean }) => {
  const router = useRouter()
  const { user } = useUser()

  const { data } = usePartnerDetailInfo(id, isDashboard)

  // State cho modal Phê duyệt
  const [modalOpen, setModalOpen] = useState(false)
  const [option, setOption] = useState<(typeof PARTNER_STATUS)[keyof typeof PARTNER_STATUS] | null>(null)
  const [comment, setComment] = useState('')
  const [popoverOpen, setPopoverOpen] = useState(false)

  // State cho modal đổi mật khẩu
  const [passwordModalOpen, setPasswordModalOpen] = useState(false)
  const isAdmin = user?.portalType === 'ADMIN'

  const detailTabs = [
    {
      key: 'GENERAL_INFOR',
      label: 'Tổng quan',
      children: <GeneralInfor id={id} />
    },
    {
      key: 'DETAIL_INFOR',
      label: 'Thông tin chi tiết',
      children: <DetailInfor id={id} />
    },
    {
      key: 'DOCUMENT',
      label: 'Hợp đồng & Tài liệu',
      children: <Document id={id} />
    },
    {
      key: 'ACTIVITY_LOG',
      label: 'Lịch sử hoạt động',
      children: <ActivityLog id={id} />
    }
  ]

  const filteredTabs = isDashboard
    ? detailTabs.filter(tab => tab.key === 'GENERAL_INFOR')
    : isAdmin
      ? detailTabs
      : detailTabs.filter(tab => tab.key !== 'GENERAL_INFOR')

  const { handleApprove, handleChangePassword } = usePartnerActions(id)

  const handleModalClose = () => {
    setModalOpen(false)
    setComment('')
    setOption(null)
  }

  const handleApprovalSubmit = () => {
    handleApprove({
      option,
      comment,
      onSuccess: handleModalClose
    })
  }

  const handlePasswordSubmit = (passwords: { newPassword: string; confirmPassword: string }) => {
    handleChangePassword({
      ...passwords,
      onSuccess: () => setPasswordModalOpen(false)
    })
  }

  return (
    <div className='partner-detail'>
      {isAdmin && (
        <PartnerTitle
          navigation={{ goBack: () => router.push('/partner-management/list') }}
          extraButtons={
            <div className='flex gap-4'>
              {isAdmin && (
                <Button color='primary' variant='outlined' onClick={() => setPasswordModalOpen(true)}>
                  Thay đổi mật khẩu
                </Button>
              )}

              {isAdmin && data?.state === PARTNER_STATUS.PROFILE_COMPLETED && (
                <ApprovalPopover
                  open={popoverOpen}
                  onOpenChange={setPopoverOpen}
                  onOptionSelect={selectedOption => {
                    setOption(selectedOption)
                    setModalOpen(true)
                    setPopoverOpen(false)
                  }}
                />
              )}
            </div>
          }
        >
          <div className='flex gap-4'>
            <Avatar size={56} src={data?.icon ? handleSrcImg(data.icon) : undefined}>
              {!data?.icon && data?.name ? data.name.charAt(0).toUpperCase() : ''}
            </Avatar>
            <div className='flex flex-col'>
              <div className='flex items-center gap-1'>
                <div className='w-96 truncate text-sm font-semibold'>{data?.name}</div>
                <PartnerTag
                  className='flex h-8 w-40 items-center justify-center text-xs'
                  color={statusColorMap[data?.state || 'NEW']}
                >
                  {statusTextMap[data?.state || 'NEW']}
                </PartnerTag>
              </div>
              <span className='text-sm font-normal'>{data?.email}</span>
            </div>
          </div>
        </PartnerTitle>
      )}

      <div className='w-full bg-white'>
        <Tabs
          renderTabBar={
            ((props: any, TabBar: React.ComponentClass) => (
              <div className='rounded-b-2xl bg-white px-8 pb-1 pt-3'>
                <TabBar {...props} />
              </div>
            )) as any
          }
          items={filteredTabs.map((item: any) => ({
            forceRender: true,
            style: {
              borderRadius: 12,
              background: '#FFFFFF',
              padding: 20,
              overflow: 'hidden',
              maxWidth: '100%'
            },
            ...item
          }))}
        />
      </div>

      <ApprovalModal
        open={modalOpen}
        option={option}
        comment={comment}
        onCommentChange={setComment}
        onClose={handleModalClose}
        onSubmit={handleApprovalSubmit}
      />

      <PasswordModal
        open={passwordModalOpen}
        onClose={() => setPasswordModalOpen(false)}
        onSubmit={handlePasswordSubmit}
      />
    </div>
  )
}

export default PartnerDetail
