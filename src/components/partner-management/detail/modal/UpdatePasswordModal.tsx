import { useState } from 'react'

import { Input } from 'antd'

import { PartnerModal } from '../../common'

interface PasswordModalProps {
  open: boolean
  onClose: () => void
  onSubmit: (passwords: { newPassword: string; confirmPassword: string }) => void
  loading?: boolean
}

export const PasswordModal: React.FC<PasswordModalProps> = ({ open, onClose, onSubmit }) => {
  const [newPassword, setNewPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')

  const handleSubmit = () => {
    onSubmit({ newPassword, confirmPassword })
  }

  const handleClose = () => {
    setNewPassword('')
    setConfirmPassword('')
    onClose()
  }

  return (
    <PartnerModal
      open={open}
      setOpen={handleClose}
      type='primary'
      icon={<i className='onedx-lock size-6 text-primary' />}
      controlTitle={{ titleText: 'Thiết lập mật khẩu' }}
      footer={{
        okText: 'Xác nhận',
        cancelText: 'Hủy',
        okFunction: handleSubmit
      }}
    >
      <div className='flex flex-col gap-4 py-4'>
        <div className='flex flex-col gap-2'>
          <div>
            <span className='text-error'>*</span>
            <span className='text-xs'>Mật khẩu mới</span>
          </div>
          <Input.Password
            value={newPassword}
            onChange={e => setNewPassword(e.target.value)}
            placeholder='Nhập mật khẩu mới'
          />
        </div>
        <div className='flex flex-col gap-2'>
          <div>
            <span className='text-error'>*</span> <span className='text-xs'>Xác nhận mật khẩu mới</span>
          </div>
          <Input.Password
            value={confirmPassword}
            onChange={e => setConfirmPassword(e.target.value)}
            placeholder='Nhập lại mật khẩu mới'
          />
        </div>
      </div>
    </PartnerModal>
  )
}
