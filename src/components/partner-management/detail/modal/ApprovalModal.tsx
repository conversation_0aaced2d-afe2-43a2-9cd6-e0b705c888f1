// components/ApprovalModal.tsx
import { Input } from 'antd'

import { PartnerModal } from '../../common'
import { PARTNER_STATUS } from '../../common/constants'

interface ApprovalModalProps {
  open: boolean
  option: (typeof PARTNER_STATUS)[keyof typeof PARTNER_STATUS] | null
  comment: string
  onCommentChange: (comment: string) => void
  onClose: () => void
  onSubmit: () => void
  loading?: boolean
}

export const ApprovalModal: React.FC<ApprovalModalProps> = ({
  open,
  option,
  comment,
  onCommentChange,
  onClose,
  onSubmit
}) => {
  const renderModalContent = () => {
    if (option === PARTNER_STATUS.APPROVED) {
      return (
        <div className='py-6'>
          Bạn có chắc chắn muốn <b>Chấp thuận</b> yêu cầu phê duyệt không?
        </div>
      )
    }

    if (option === PARTNER_STATUS.REJECTED || option === PARTNER_STATUS.PROFILE_COMPLETED) {
      return (
        <div className='py-6'>
          <div className='flex gap-1 pb-2'>
            <span className='text-error'>*</span>
            <span>Nội dung comment</span>
          </div>
          <Input.TextArea
            value={comment}
            onChange={e => onCommentChange(e.target.value)}
            placeholder='Nhập nội dung'
            rows={4}
          />
        </div>
      )
    }

    return null
  }

  const getTitle = () => {
    switch (option) {
      case PARTNER_STATUS.APPROVED:
        return 'Chấp thuận phê duyệt'
      case PARTNER_STATUS.REJECTED:
        return 'Từ chối phê duyệt tài khoản'
      case PARTNER_STATUS.PROFILE_COMPLETED:
        return 'Yêu cầu phê duyệt lại'
      default:
        return ''
    }
  }

  return (
    <PartnerModal
      open={open}
      setOpen={onClose}
      type={option === PARTNER_STATUS.REJECTED ? 'error' : 'primary'}
      icon={option === PARTNER_STATUS.APPROVED ? <i className='onedx-check-circle size-6' /> : undefined}
      controlTitle={{ titleText: getTitle() }}
      footer={{
        okText: 'Xác nhận',
        cancelText: 'Hủy',
        okFunction: onSubmit
      }}
    >
      {renderModalContent()}
    </PartnerModal>
  )
}
