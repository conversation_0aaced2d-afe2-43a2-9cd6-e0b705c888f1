import { useState } from 'react'

import { But<PERSON>, Modal, Pagination, Select, Table } from 'antd'

import { usePage } from '@/hooks'
import DashboardManagement from '@/models/DashboardManagement'
import { pageSizeOptions } from '@/views/product-catalog/constants/constants'
import { customerTypeOptions, defaultParamPreview, serviceTypeOptions } from '../../common/constants'
import { useRevenueFiltersOptions } from '@/hooks/useRevenueFilterOptions'

export const RevenueInforModal = ({ open, setOpen, timeParams, id }: any) => {
  const DEFAULT_VALUE = -1
  const CUSTOMER_DEFAULT_VALUE = 'ALL'

  const [dataFromFilterTable, setDataFromFilterTable] = useState(defaultParamPreview)

  const { provinceList, serviceList } = useRevenueFiltersOptions()

  const { page, pageSize, content } = usePage(
    [`previewRevenueByMonth`, timeParams, dataFromFilterTable, id],
    async () => {
      const res = await DashboardManagement.previewRevenueByMonth({
        ...dataFromFilterTable,
        ...timeParams,
        id
      })

      return res
    }
  )

  const handleChange = (key: string, val: any, fallback: any) => {
    setDataFromFilterTable(prev => ({
      ...prev,
      [key]: val.length ? val : fallback
    }))
  }

  const columns = [
    {
      title: 'STT',
      dataIndex: 'id',
      key: 'id',
      render: (_: any, record: any, index: number) => (page - 1) * pageSize + index + 1
    },
    {
      title: 'Tỉnh thành',
      dataIndex: 'provinceName',
      key: 'provinceName'
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email'
    },
    {
      title: ' Tên khách hàng',
      dataIndex: 'smeName',
      key: 'smeName'
    },
    {
      title: 'Số giấy tờ',
      dataIndex: 'identityNo',
      key: 'identityNo'
    },
    {
      title: 'Tên dịch vụ',
      dataIndex: 'serviceName',
      key: 'serviceName'
    },
    {
      title: 'Tên gói dịch vụ',
      dataIndex: 'pricingName',
      key: 'pricingName'
    },
    {
      title: 'Loại thuê bao',
      dataIndex: 'subType',
      key: 'subType'
    },
    {
      title: 'Số tiền thanh toán trước thuế',
      dataIndex: 'amountPreTax',
      key: 'amountPreTax',
      render: (value: number) => value.toLocaleString()
    },
    {
      title: 'Số tiền thanh toán sau thuế',
      dataIndex: 'amountAfterTax',
      key: 'amountAfterTax',
      render: (value: number) => value.toLocaleString()
    },
    {
      title: 'Tổng khuyến mại đi kèm',
      dataIndex: 'amountDiscount',
      key: 'amountDiscount',
      render: (value: number) => value.toLocaleString()
    },
    {
      title: 'Ngày tạo thuê bao',
      dataIndex: 'createdAt',
      key: 'createdAt'
    },
    {
      title: 'Nguồn tạo',
      dataIndex: 'createdSource',
      key: 'createdSource'
    }
  ]

  return (
    <Modal
      open={open}
      closable={false}
      onCancel={() => setOpen(false)}
      width='90%'
      title='Chi tiết thông tin doanh thu'
      footer={[
        <Button key='back' onClick={() => setOpen(false)}>
          Đóng
        </Button>
      ]}
    >
      <div className=' flex flex-col gap-6'>
        <div className='mb-4 grid grid-cols-4 gap-4'>
          <Select
            mode='multiple'
            allowClear
            placeholder='Tỉnh thành: Tất cả'
            options={provinceList}
            value={
              dataFromFilterTable.lstProvinceId.length === 1 && dataFromFilterTable.lstProvinceId[0] === DEFAULT_VALUE
                ? []
                : dataFromFilterTable.lstProvinceId
            }
            onChange={val => handleChange('lstProvinceId', val, [DEFAULT_VALUE])}
          />
          <Select
            placeholder='Loại dịch vụ'
            options={serviceTypeOptions}
            value={dataFromFilterTable.serviceType}
            onChange={val => setDataFromFilterTable(prev => ({ ...prev, serviceType: val }))}
          />
          <Select
            mode='multiple'
            allowClear
            placeholder='Sản phẩm dịch vụ: Tất cả'
            options={serviceList}
            value={
              dataFromFilterTable.lstServiceProductUniqueId.length === 1 &&
              dataFromFilterTable.lstServiceProductUniqueId[0] === DEFAULT_VALUE
                ? []
                : dataFromFilterTable.lstServiceProductUniqueId
            }
            onChange={val => handleChange('lstServiceProductUniqueId', val, [DEFAULT_VALUE])}
          />
          <Select
            mode='multiple'
            allowClear
            placeholder='Đối tượng khách hàng: Tất cả'
            options={customerTypeOptions}
            value={
              dataFromFilterTable.lstCustomerType.length === 1 &&
              dataFromFilterTable.lstCustomerType[0] === CUSTOMER_DEFAULT_VALUE
                ? []
                : dataFromFilterTable.lstCustomerType
            }
            onChange={val => handleChange('lstCustomerType', val, [CUSTOMER_DEFAULT_VALUE])}
          />
        </div>

        <Table columns={columns} dataSource={content} scroll={{ x: 2800 }} pagination={false} />
        <div className='flex items-center'>
          <div className='flex flex-1 justify-center'>
            <Pagination showSizeChanger={false} />
          </div>
          <Select defaultValue={10} options={pageSizeOptions} />
        </div>
      </div>
    </Modal>
  )
}
