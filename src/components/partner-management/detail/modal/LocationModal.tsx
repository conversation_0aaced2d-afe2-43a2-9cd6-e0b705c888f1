import { Button, Modal, Table } from 'antd'

type LocationModalProps = {
  open: boolean
  onClose: () => void
  columns: any[]
  dataSource: any[]
}

export const LocationModal: React.FC<LocationModalProps> = ({ open, onClose, columns, dataSource }) => (
  <Modal
    title='Đ<PERSON>a bàn ho<PERSON>t động'
    open={open}
    onCancel={onClose}
    width='50%'
    footer={[
      <Button key='close' color='primary' variant='outlined' onClick={onClose}>
        Đóng
      </Button>
    ]}
  >
    <Table
      columns={columns}
      dataSource={dataSource}
      pagination={false}
      rowClassName={() => 'group hover:bg-gray-50 hover:text-blue-600'}
      className='border-y-2 border-solid border-neutral-200 py-6'
    />
  </Modal>
)
