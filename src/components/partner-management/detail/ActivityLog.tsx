import React, { useCallback, useState } from 'react'

import { Table, Button, Dropdown, <PERSON>u, Pagination, Select } from 'antd'
import { DownOutlined } from '@ant-design/icons'

import { debounce } from 'lodash'

import { useQuery } from '@tanstack/react-query'

import type { CheckboxOption, CheckedState } from '@/components/filter/SettingInput'
import SettingInput from '@/components/filter/SettingInput'
import { pageSizeOptions } from '@/views/product-catalog/constants/constants'
import DashboardManagement from '@/models/DashboardManagement'
import type { Log } from '@/types/partner-portal/partner'

export const ActivityLog = ({ id }: { id: number | string }) => {
  const { data: activeLog } = useQuery<Log[]>({
    queryKey: ['getAccountHistory', id],
    queryFn: async () => {
      const res = await DashboardManagement.getAccountHistory(id)

      return res.content
    },
    enabled: !!id
  })

  const [checked, setChecked] = useState<CheckedState>({
    isCode: true,
    isService: true
  })

  const checkBoxOptions: CheckboxOption[] = [
    { label: 'Hành động', key: 'isAction' },
    { label: 'Mô tả', key: 'isDescription' }
  ]

  const defaultSearchParams = {
    page: 0,
    size: 10,
    startDate: '',
    endDate: '',
    classification: 'UNSET'
  }

  const [filterParams, setFilterParams] = useState(defaultSearchParams)

  const debouncedSetFilterParams = debounce(params => {
    setFilterParams(params)
  }, 200)

  const handleSearch = useCallback(
    (value: string) => {
      debouncedSetFilterParams({
        ...filterParams,
        value: value || '',
        isAction: Number(Boolean(checked.isAction)),
        isDescription: Number(Boolean(checked.isDescription))
      })
    },
    [filterParams, checked, debouncedSetFilterParams]
  )

  const columns = [
    {
      title: 'Hành động',
      dataIndex: 'actionName'
    },
    {
      title: 'Nội dung',
      dataIndex: 'content'
    },
    {
      title: 'Người thực hiện',
      dataIndex: 'actorRole'
    },

    {
      title: 'Thời gian thực hiện',
      dataIndex: 'createdAt'
    }
  ]

  const activityMenu = (
    <Menu
      items={[
        { key: '1', label: 'Tất cả' },
        { key: '2', label: 'Vấn đề/ Lỗi' },
        { key: '3', label: 'Cập nhật' }
      ]}
    />
  )

  return (
    <div className='flex flex-col gap-6'>
      <div className='mb-3 flex items-center justify-between'>
        <div className='flex w-full gap-4'>
          {/* Tìm kiếm */}
          <SettingInput
            placeholder='Tìm kiếm theo tên nhật ký'
            styles={{ width: '100%' }}
            checked={checked}
            setChecked={setChecked}
            onChange={e => handleSearch(e)}
            checkBoxOptions={checkBoxOptions}
          />
          <Dropdown overlay={activityMenu} trigger={['hover']}>
            <Button className='h-10'>
              Loại hoạt động (4) <DownOutlined />
            </Button>
          </Dropdown>
        </div>
      </div>

      <Table
        columns={columns}
        dataSource={activeLog}
        pagination={false}
        rowClassName={() => 'group hover:bg-gray-50 hover:text-blue-600'}
      />

      <div className='flex items-center'>
        <div className='flex flex-1 justify-center'>
          <Pagination showSizeChanger={false} />
        </div>

        <Select defaultValue={10} options={pageSizeOptions} />
      </div>
    </div>
  )
}
