'use client'

import { useCallback, useState } from 'react'

import { Table, Select, Button, Pagination } from 'antd'
import type { ColumnsType } from 'antd/es/table'

import { debounce } from 'lodash'

import { useMutation } from '@tanstack/react-query'

import { PartnerTag } from '../common'
import { pageSizeOptions } from '@/views/product-catalog/constants/constants'
import type { CheckboxOption, CheckedState } from '@/components/filter/SettingInput'
import SettingInput from '@/components/filter/SettingInput'
import { usePage } from '@/hooks'
import PartnerManagement from '@/models/PartnerManagement'
import type { Contract } from '@/types/partner-portal/partner'
import { getDocumentStatus, getFileIcon, getTypeDisplay } from '@/utils/partnerUtils'
import { STATUS_COLORS, STATUS_LABELS } from '../common/constants'
import { DateFilterPopover } from './component/DateFilterPopover'
import { showMessage } from '@/components/notification/NotificationProvider'

export const Document = ({ id }: { id: number | string }) => {
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([])

  const [openUploadDateFilter, setOpenUploadDateFilter] = useState(false)
  const [openExpirationDateFilter, setOpenExpirationDateFilter] = useState(false)

  const [checked, setChecked] = useState<CheckedState>({
    isName: true
  })

  const checkBoxOptions: CheckboxOption[] = [{ label: 'Tên tài liệu', key: 'isName' }]

  const defaultSearchParams = {
    page: 0,
    size: 10,
    startDate: '',
    endDate: '',
    status: -1,
    type: null
  }

  const handleFilterChange = useCallback((key: string, value: any) => {
    const formattedValue = Array.isArray(value) ? value.join(',') : value

    setFilterParams(prev => {
      const updatedParams = {
        ...prev,
        [key]: formattedValue
      }

      return updatedParams
    })
  }, [])

  const [filterParams, setFilterParams] = useState(defaultSearchParams)

  const debouncedSetFilterParams = debounce(params => {
    setFilterParams(params)
  }, 200)

  const handleSearch = useCallback(
    (value: string) => {
      debouncedSetFilterParams({
        ...filterParams,
        name: value || ''
      })
    },
    [filterParams, debouncedSetFilterParams]
  )

  const { page, pageSize, content } = usePage(['documnet-list', id, filterParams], async () => {
    const params = {
      ...filterParams,
      page: page - 1,
      pageSize,
      size: pageSize
    }

    const res = await PartnerManagement.getDocumentsList(id, { ...params })

    return res
  })

  const mutation = useMutation({
    mutationFn: PartnerManagement.deteleContract,
    onSuccess: () => {
      setSelectedRowKeys([])
      showMessage.success('Xóa hợp đồng thành công')
    },
    onError: () => {
      showMessage.error('Xóa hợp đồng thất bại')
    }
  })

  const handleDeleteContract = () => {
    mutation.mutate(selectedRowKeys)
  }

  const rowSelection = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(newSelectedRowKeys)
    }
  }

  const columns: ColumnsType<Contract> = [
    {
      title: 'Tên tài liệu',
      dataIndex: 'name',
      render: text => (
        <div className='flex items-center gap-2'>
          {getFileIcon(text)}
          <span>{text}</span>
        </div>
      )
    },
    {
      title: 'Loại tài liệu',
      dataIndex: 'type',
      render: text => <>{getTypeDisplay(text)}</>
    },
    {
      title: 'Người tải lên',
      dataIndex: 'createdBy'
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      width: 100,
      render: (_, record: Contract) => {
        const status = getDocumentStatus(record.expireAt)

        return <PartnerTag color={STATUS_COLORS[status]}>{STATUS_LABELS[status]}</PartnerTag>
      }
    },
    {
      title: 'Ngày hết hạn',
      dataIndex: 'expireAt'
    },
    {
      title: 'Ngày tải lên',
      dataIndex: 'createdAt'
    }
  ]

  const handleUploadDateChange = useCallback(
    (dates: any) => {
      if (dates && dates.length === 2) {
        const [start, end] = dates
        const startTimestamp = start?.format('DD/MM/YYYY') || ''
        const endTimestamp = end?.format('DD/MM/YYYY') || ''

        handleFilterChange('startDate', startTimestamp)
        handleFilterChange('endDate', endTimestamp)
        setOpenUploadDateFilter(false)
      } else {
        handleFilterChange('startDate', '')
        handleFilterChange('endDate', '')
      }
    },
    [handleFilterChange]
  )

  const handleExpirationDateChange = useCallback(
    (dates: any) => {
      if (dates && dates.length === 2) {
        const [start, end] = dates
        const startTimestamp = start?.format('DD/MM/YYYY') || ''
        const endTimestamp = end?.format('DD/MM/YYYY') || ''

        handleFilterChange('startExpireDate', startTimestamp)
        handleFilterChange('endExpireDate', endTimestamp)
        setOpenExpirationDateFilter(false)
      } else {
        handleFilterChange('startExpireDate', '')
        handleFilterChange('endExpireDate', '')
      }
    },
    [handleFilterChange]
  )

  return (
    <div className='rounded-xl bg-white p-6 shadow-sm'>
      {/* Thanh filter */}
      <div className='mb-4 flex gap-6'>
        <SettingInput
          placeholder='Tìm kiếm theo tên hợp đồng và tài liệu'
          styles={{ width: '100%' }}
          checked={checked}
          setChecked={setChecked}
          onChange={e => handleSearch(e)}
          checkBoxOptions={checkBoxOptions}
        />
        <Select
          allowClear
          placeholder='Loại tài liệu'
          className='w-40'
          options={[
            { label: 'Hợp đồng', value: 'CONTRACT' },
            { label: 'Pháp lý', value: 'LEGAL' }
          ]}
          value={!!filterParams.type ? filterParams?.type : undefined}
          onChange={value => handleFilterChange('type', value)}
        />
        <Select
          allowClear
          placeholder='Trạng thái'
          className='w-40'
          options={[
            { label: 'Hiệu lực', value: 1 },
            { label: 'Hết hiệu lực', value: 0 }
          ]}
          value={filterParams.status !== -1 ? filterParams.status : undefined}
          onChange={value => handleFilterChange('status', value)}
        />
        {/* Filter: Thời gian*/}
        <DateFilterPopover
          open={openUploadDateFilter}
          onOpenChange={setOpenUploadDateFilter}
          label='Ngày tải lên'
          onDateChange={handleUploadDateChange}
        />

        <DateFilterPopover
          open={openExpirationDateFilter}
          onOpenChange={setOpenExpirationDateFilter}
          label='Thời gian hết hạn'
          onDateChange={handleExpirationDateChange}
        />
      </div>

      {/* Bảng */}
      <Table
        rowSelection={rowSelection}
        columns={columns}
        dataSource={content}
        pagination={false}
        rowClassName={() => 'group hover:bg-gray-50 hover:text-blue-600'}
      />

      <div className='mt-2 flex items-center justify-between gap-2'>
        <div className='flex items-center gap-4'>
          <span>Đã chọn: {selectedRowKeys.length}</span>
          <Button color='primary' variant='outlined' onClick={handleDeleteContract}>
            Xóa <i className='onedx-delete' />
          </Button>
        </div>

        <Pagination showSizeChanger={false} />

        <Select defaultValue={10} options={pageSizeOptions} />
      </div>
    </div>
  )
}
