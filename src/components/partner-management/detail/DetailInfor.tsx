import { useMemo, useState } from 'react'

import { useRouter } from 'next/navigation'

import { Button, Collapse, Modal, Table, Tooltip } from 'antd'

import { PartnerCard, PartnerTag } from '../common'
import { DetailInfoCustomField } from '@/components/custom-field/selectors'
import { CUSTOMER_TYPE } from '@/views/product-catalog/constants/constants'
import { convertCapabilities } from '@/utils/partnerUtils'
import { usePartnerDetailInfo } from '@/hooks/usePartnerDetailInfor'

type InfoItem = {
  label: string
  value: string | number
}

export const DetailInfor = ({ id }: { id: number | string }) => {
  const router = useRouter()

  const [isLocationModalOpen, setIsLocationModalOpen] = useState(false)

  const { data } = usePartnerDetailInfo(id)

  const locationColumns = [
    {
      title: 'Địa bàn',
      render: (_: any, record: any, index: number) => <>{index + 1}</>
    },
    {
      title: 'Tỉnh/thành',
      dataIndex: 'provinceName',
      key: 'provinceName'
    },
    {
      title: 'Phường/xã',
      dataIndex: 'wardName',
      key: 'wardName'
    },
    {
      title: 'Phố/đường',
      dataIndex: 'streetName',
      key: 'streetName'
    }
  ]

  const renderInfor = (item: InfoItem) => (
    <div className='flex justify-between'>
      <span className='font-normal text-text-neutral-light'>{item.label}</span>
      <Tooltip title={item.value}>
        <span className='line-clamp-2 max-w-64 break-words text-right font-medium text-gray-800'>{item.value}</span>
      </Tooltip>
    </div>
  )

  const createFieldHtml = (label: string, value: string) => `
    <div>
      <div style="color: #6b7280; font-size: 14px; margin-bottom: 4px;">${label}</div>
      <div style="color: #374151; font-weight: 500;">${value}</div>
    </div>
  `

  const renderContact = () => {
    return data?.secondaryContacts
      ?.map((contact: any, index: number) => {
        const fields = [
          { label: 'Họ và tên', value: contact.name },
          { label: 'Chức vụ', value: contact.position },
          { label: 'Số điện thoại', value: contact.phone },
          { label: 'Email', value: contact.email }
        ]

        const fieldsHtml = fields.map(field => createFieldHtml(field.label, field.value)).join('')

        return `
        <div className="w-[1000px]" style="margin-bottom: 16px; background: #f8fafc; border-radius: 8px">
          <h4 style="margin-bottom: 12px; font-weight: 600; color: #374151;">Liên hệ phụ ${index + 1}</h4>
          <div style="padding: 12px 16px; border: 1px solid #e5e7eb;">
            <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 12px;">
              ${fieldsHtml}
            </div>
          </div>
        </div>
      `
      })
      .join('')
  }

  const enterpriseGeneralInfor = useMemo(() => {
    if (!data) return []

    return [
      { label: 'Loại hình đối tác', value: 'Doanh nghiệp' },
      { label: 'Tên doanh nghiệp', value: data?.name },
      {
        label: 'Mã số thuế',
        value: data?.taxCode
      },
      { label: 'Mã BHXH', value: data?.socialInsuranceNumber },
      {
        label: 'Website',
        value: data?.website
      }
    ]
  }, [data])

  const personalGeneralInfor = useMemo(() => {
    if (!data) return []

    return [
      { label: 'Loại hình đối tác', value: 'Cá nhân' },
      {
        label: 'Họ và tên',
        value: data?.name
      },
      { label: 'Loại giấy chứng thực', value: data?.repPersonalCertType },
      {
        label: 'Số giấy chứng thực',
        value: data?.repPersonalCertNumber
      }
    ]
  }, [data])

  const generalInfor = data?.customerType === CUSTOMER_TYPE.PERSONAL ? personalGeneralInfor : enterpriseGeneralInfor

  const address = useMemo(() => {
    if (!data) return []

    return [
      { label: 'Quốc gia', value: 'Việt Nam' },

      {
        label: 'Tỉnh/Thành',
        value: data?.provinceName
      },
      { label: 'Phường/Xã', value: data?.wardName },
      {
        label: 'Phố/Đường',
        value: data?.streetName
      },
      {
        label: 'Địa chỉ đăng ký kinh doanh',
        value: data?.businessRegistrationAddress
      }
    ]
  }, [data])

  const contact = useMemo(() => {
    if (!data) return []

    return [
      { label: 'Số điện thoại chính', value: data?.phoneNumber },

      {
        label: 'Email chính',
        value: data?.primaryEmail
      },
      {
        label: 'Liên hệ phụ',
        value: `${data?.secondaryContacts?.length || 0} liên hệ`,
        descriptionPopup: renderContact()
      },
      {
        label: 'Địa chỉ văn phòng làm việc',
        value: data?.officeAddress
      }
    ]
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data])

  const representative = useMemo(() => {
    if (!data) return []

    return [
      {
        label: 'Người đại diện pháp luật',
        value: data?.representative?.name
      },
      {
        label: 'Chức danh',
        value: data?.representative?.position
      },
      {
        label: 'Giới tính',
        value: data?.representative?.gender === 'MALE' ? 'Nam' : 'Nữ'
      },
      {
        label: 'Ngày sinh',
        value: data?.representative?.dateOfBirth
      },

      // Thông tin giấy tờ
      {
        label: 'Loại giấy chứng thực',
        value: data?.representative?.certType
      },
      {
        label: 'Số giấy chứng thực',
        value: data?.representative?.certNo
      },
      {
        label: 'Ngày cấp',
        value: data?.representative?.certIssueDate
      },
      {
        label: 'Nơi cấp',
        value: data?.representative?.certIssuePlace
      },

      {
        label: 'Nơi đăng ký hộ khẩu',
        value: data?.representative?.registeredResidence
      },
      {
        label: 'Chỗ ở hiện tại',
        value: data?.representative?.currentAddress
      }
    ]
  }, [data])

  const activityInfo = useMemo(() => {
    if (!data) return []

    return [
      { label: 'Email đăng nhập', value: data?.email },

      {
        label: 'Mã đối tác',
        value: data?.code
      },
      {
        label: 'Trạng thái hoạt động',
        value:
          data?.status === 'ACTIVE' ? (
            <PartnerTag color='success'>Đang bật</PartnerTag>
          ) : (
            <PartnerTag color='error'>Đang tắt</PartnerTag>
          )
      }
    ]
  }, [data])

  const capacity = useMemo(() => {
    if (!data) return []

    return [
      { label: 'Năng lực cung cấp', value: convertCapabilities(data.capabilities).join(', ') },
      {
        label: 'Địa bàn cung cấp',
        value: `${data?.operatingAreas?.length} địa bàn`,
        onClick: () => setIsLocationModalOpen(true)
      }
    ]
  }, [data])

  const finance = useMemo(() => {
    if (!data) return []

    return [
      { label: 'Tên chủ tài khoản', value: data?.bankAccount?.name },

      {
        label: 'Tên ngân hàng',
        value: data?.bankAccount?.bank
      },
      {
        label: 'Số tài khoản',
        value: data?.bankAccount?.number
      }
    ]
  }, [data])

  const handleNavigateUpdate = () => router.push(`/partner-management/update/${id}`)

  const rightContent = (
    <Button color='primary' variant='text' onClick={handleNavigateUpdate}>
      <i className='onedx-edit size-4' /> Chỉnh sửa
    </Button>
  )

  return (
    <div className='grid h-full grid-cols-3 items-stretch gap-2 bg-bg-neutral-lighter'>
      <div className='col-span-2 grid h-full gap-2'>
        <PartnerCard title='Thông tin chung & pháp lý' rightContent={rightContent}>
          <div className='grid grid-cols-4 gap-4'>
            {generalInfor?.map((item, index) => <DetailInfoCustomField {...item} key={index} />)}
          </div>

          <Collapse
            ghost
            className='py-4'
            expandIconPosition='end'
            defaultActiveKey={['condition']}
            items={[
              {
                key: 'condition',
                className: 'rounded-lg bg-[#02173C06]',
                classNames: { body: 'pt-0' },
                label: (
                  <div className='flex items-center gap-1'>
                    <i className='onedx-dot size-2 text-bg-success-default' />
                    <span className='text-sm font-medium text-text-neutral-light'>Thông tin địa chỉ kinh doanh</span>
                  </div>
                ),
                children: (
                  <div className='grid w-full grid-cols-4 gap-4 border-t border-solid border-neutral-200 pt-4'>
                    {address?.map((item, index) => <DetailInfoCustomField {...item} key={index} />)}
                  </div>
                )
              }
            ]}
          />
        </PartnerCard>

        <PartnerCard title='Thông tin liên hệ' rightContent={rightContent}>
          <div className='grid w-full grid-cols-4 gap-4 '>
            {contact?.map((item, index) => <DetailInfoCustomField {...item} key={index} />)}
          </div>
        </PartnerCard>

        <PartnerCard title='Thông tin liên hệ' rightContent={rightContent}>
          <div className='grid w-full grid-cols-4 gap-4 '>
            {representative?.map((item, index) => <DetailInfoCustomField {...item} key={index} />)}
          </div>
        </PartnerCard>
      </div>

      <div className='flex h-full flex-col gap-2'>
        <PartnerCard title='Thông tin hoạt động' rightContent={rightContent}>
          <div className='flex flex-col gap-4 '>{activityInfo?.map(item => renderInfor(item))}</div>
        </PartnerCard>

        <PartnerCard title='Năng lực & địa bàn' rightContent={rightContent}>
          <div className='flex flex-col gap-4 '>
            {capacity?.map((item, index) =>
              item.onClick ? (
                <div key={index} className='flex justify-between'>
                  <span className='font-normal text-text-neutral-medium'>{item.label}</span>
                  <span
                    className='cursor-pointer font-medium text-blue-600 underline hover:text-blue-800'
                    onClick={item.onClick}
                  >
                    {item.value}
                  </span>
                </div>
              ) : (
                renderInfor(item)
              )
            )}
          </div>
        </PartnerCard>

        <PartnerCard
          styles={{
            body: {
              display: 'flex',
              flexDirection: 'column',
              flex: 1,
              height: '100%'
            }
          }}
          title='Thông tin tài chính'
          rightContent={rightContent}
        >
          <div className='flex flex-col gap-4 '>{finance?.map(item => renderInfor(item))}</div>
        </PartnerCard>
      </div>

      <Modal
        title='Địa bàn hoạt động'
        open={isLocationModalOpen}
        onCancel={() => setIsLocationModalOpen(false)}
        width='50%'
        footer={[
          <Button key='close' color='primary' variant='outlined' onClick={() => setIsLocationModalOpen(false)}>
            Đóng
          </Button>
        ]}
      >
        <Table
          columns={locationColumns}
          dataSource={data?.operatingAreas}
          pagination={false}
          rowClassName={() => 'group hover:bg-gray-50 hover:text-blue-600'}
          className='border-y-2 border-solid border-neutral-200 py-6'
        />
      </Modal>
    </div>
  )
}
