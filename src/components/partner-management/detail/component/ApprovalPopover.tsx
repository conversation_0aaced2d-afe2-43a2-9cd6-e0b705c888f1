import { Button, Popover } from 'antd'

interface ApprovalPopoverProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onOptionSelect: (option: 'APPROVED' | 'REJECTED' | 'PROFILE_COMPLETED') => void
}

export const ApprovalPopover: React.FC<ApprovalPopoverProps> = ({ open, onOpenChange, onOptionSelect }) => {
  const popoverContent = (
    <div className='flex flex-col justify-start gap-2'>
      <Button
        color='primary'
        variant='text'
        onClick={() => onOptionSelect('APPROVED')}
        className='flex items-center justify-start'
      >
        Phê duyệt
      </Button>
      <Button
        color='primary'
        variant='text'
        onClick={() => onOptionSelect('REJECTED')}
        className='flex items-center justify-start'
      >
        Từ chối
      </Button>
      <Button
        color='primary'
        variant='text'
        onClick={() => onOptionSelect('PROFILE_COMPLETED')}
        className='flex items-center justify-start'
      >
        <PERSON><PERSON><PERSON> cầu cập nhật lại
      </Button>
    </div>
  )

  return (
    <Popover content={popoverContent} trigger='click' open={open} onOpenChange={onOpenChange} placement='bottom'>
      <Button type='primary'>Phê duyệt</Button>
    </Popover>
  )
}
