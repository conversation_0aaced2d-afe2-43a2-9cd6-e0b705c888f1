import type { ReactNode } from 'react'

import { Collapse, Tooltip } from 'antd'

import { PartnerCard } from '../../common'
import { DetailInfoCustomField } from '@/components/custom-field/selectors'

type InfoItem = {
  label: string
  value: string | number | ReactNode
  onClick?: () => void
  clickable?: boolean
  type?: 'contact-list' | 'default'
  data?: any
}

type InfoSectionProps = {
  title: string
  rightContent?: ReactNode
  items: InfoItem[]
  addressItems?: InfoItem[]
  hasCollapse?: boolean
  layout?: 'grid' | 'vertical'
  hasClickableItems?: boolean
  styles?: any
  customRenderer?: React.ComponentType<{ items: InfoItem[] }>
}

export const InfoSection: React.FC<InfoSectionProps> = ({
  title,
  rightContent,
  items,
  addressItems,
  hasCollapse,
  layout = 'grid',
  styles,
  customRenderer: CustomRenderer
}) => {
  const renderItem = (item: InfoItem, index: number) => {
    if (layout === 'vertical') {
      if (item.clickable && item.onClick) {
        return (
          <div key={index} className='flex justify-between'>
            <span className='font-normal text-text-neutral-medium'>{item.label}</span>
            <span
              className='cursor-pointer font-medium text-blue-600 underline hover:text-blue-800'
              onClick={item.onClick}
            >
              {item.value}
            </span>
          </div>
        )
      }

      return (
        <div key={index} className='flex justify-between'>
          <span className='font-normal text-text-neutral-light'>{item.label}</span>
          <Tooltip title={item.value}>
            <span className='line-clamp-2 max-w-64 break-words text-right font-medium text-gray-800'>{item.value}</span>
          </Tooltip>
        </div>
      )
    }

    return <DetailInfoCustomField {...item} key={index} />
  }

  const content = CustomRenderer ? (
    <CustomRenderer items={items} />
  ) : (
    <div className={layout === 'grid' ? 'grid grid-cols-4 gap-4' : 'flex flex-col gap-4'}>{items.map(renderItem)}</div>
  )

  return (
    <PartnerCard title={title} rightContent={rightContent} styles={styles}>
      {content}

      {hasCollapse && addressItems && (
        <Collapse
          ghost
          className='py-4'
          expandIconPosition='end'
          defaultActiveKey={['condition']}
          items={[
            {
              key: 'condition',
              className: 'rounded-lg bg-[#02173C06]',
              classNames: { body: 'pt-0' },
              label: (
                <div className='flex items-center gap-1'>
                  <i className='onedx-dot size-2 text-bg-success-default' />
                  <span className='text-sm font-medium text-text-neutral-light'>Thông tin địa chỉ kinh doanh</span>
                </div>
              ),
              children: (
                <div className='grid w-full grid-cols-4 gap-4 border-t border-solid border-neutral-200 pt-4'>
                  {addressItems.map((item, index) => (
                    <DetailInfoCustomField {...item} key={index} />
                  ))}
                </div>
              )
            }
          ]}
        />
      )}
    </PartnerCard>
  )
}
