import { Button, DatePicker, Popover } from 'antd'

export const DateFilterPopover = ({
  open,
  onOpenChange,
  label,
  onDateChange
}: {
  open: boolean
  onOpenChange: (open: boolean) => void
  label: string
  onDateChange: (dates: any) => void
}) => (
  <div className='flex h-6 min-w-[135px] cursor-pointer items-center rounded-lg border border-solid border-[#D9D9D9] bg-white py-[19px] text-gray-6'>
    <Popover
      placement='bottom'
      open={open}
      onOpenChange={onOpenChange}
      content={
        <DatePicker.RangePicker
          format='DD/MM/YYYY'
          renderExtraFooter={() => (
            <div className='m-2 flex justify-end'>
              <Button type='primary' onClick={() => onOpenChange(false)}>
                Xác nhận
              </Button>
            </div>
          )}
          autoFocus
          onChange={onDateChange}
        />
      }
      trigger='click'
    >
      <div className='flex w-48'>
        <div className='mx-1 text-sm font-normal'>{label}</div>
        <i className='onedx-calendar size-5' />
      </div>
    </Popover>
  </div>
)
