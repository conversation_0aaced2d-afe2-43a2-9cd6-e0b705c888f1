// components/FilterButtons.tsx
import React from 'react'

import { Button } from 'antd'

import { timeFilters, topFilters } from '../../common/constants'

interface FilterButtonsProps {
  reportCycle: number
  topFilter?: number
  onTimeFilter: (cycle: number) => void
  onTopFilter?: (top: number) => void
  showTopFilter?: boolean
}

export const FilterButtons: React.FC<FilterButtonsProps> = ({
  reportCycle,
  topFilter,
  onTimeFilter,
  onTopFilter,
  showTopFilter = false
}) => {
  const buttonStyle = (isActive: boolean) => `
    rounded-md px-3 py-1 text-sm font-medium transition-all duration-200
    ${
      isActive
        ? 'border-none bg-blue-500 text-white shadow-sm'
        : 'border-none bg-bg-primary-lighter text-text-primary-blue'
    }
  `

  return (
    <div className='flex flex-wrap gap-4'>
      {showTopFilter && topFilter !== undefined && onTopFilter && (
        <div className='flex gap-2'>
          {topFilters.map(filter => (
            <Button
              key={filter.key}
              size='small'
              onClick={() => onTopFilter(filter.key)}
              className={buttonStyle(topFilter === filter.key)}
            >
              {filter.label}
            </Button>
          ))}
        </div>
      )}

      <div className='flex gap-2'>
        {timeFilters.map(filter => (
          <Button
            key={filter.key}
            size='small'
            onClick={() => onTimeFilter(filter.key)}
            className={buttonStyle(reportCycle === filter.key)}
          >
            {filter.label}
          </Button>
        ))}
      </div>
    </div>
  )
}
