import React, { useMemo } from 'react'

import type { ApexOptions } from 'apexcharts'

import { BaseChart } from './BaseChart'
import { formatCurrencyMoney } from '@/utils/partnerUtils'
import { CHART_TYPE } from '../../common/constants'

export const RevenueColumnChart = (props: any) => {
  const { chartName, topAreaData, getSelectArea, isLoadingButton, chartType = CHART_TYPE.GROWTH } = props

  const { series, options } = useMemo(() => {
    if (!topAreaData || topAreaData.length === 0) {
      return { series: [], options: {} }
    }

    const seriesData = [
      {
        name: chartType === CHART_TYPE.GROWTH ? 'Doanh thu tăng trưởng' : 'Doanh thu lũy kế',
        data: topAreaData.map((item: any) => ({
          x: item.name,
          y: item.clickRate || 0,
          fillColor: '#2A6AEB'
        }))
      }
    ]

    const chartOptions: ApexOptions = {
      chart: {
        id: `revenue-chart-${chartName}`,
        type: 'bar',
        toolbar: { show: false },
        zoom: { enabled: true, autoScaleYaxis: true },
        animations: {
          enabled: true,
          easing: 'easeinout',
          speed: 1000,
          animateGradually: { enabled: true, delay: 150 }
        },
        events: {
          dataPointSelection: function (event, chartContext, config) {
            const dataPoint = topAreaData[config.dataPointIndex]

            if (dataPoint && getSelectArea) {
              getSelectArea(dataPoint)
            }
          }
        }
      },
      plotOptions: {
        bar: {
          columnWidth: '50%',
          distributed: true,
          dataLabels: { position: 'top' }
        }
      },
      dataLabels: {
        enabled: true,
        formatter: (val: number) => formatCurrencyMoney(val),
        style: {
          fontSize: '12px',
          fontWeight: 'bold',
          colors: ['#333']
        },
        offsetY: -20
      },
      xaxis: {
        type: 'category',
        categories: topAreaData?.map((item: any) => item.name),
        labels: {
          rotate: 0,
          rotateAlways: false,
          style: {
            fontFamily: 'Montserrat',
            fontSize: '12px',
            colors: '#666'
          }
        }
      },
      yaxis: {
        labels: {
          style: {
            fontFamily: 'Montserrat',
            fontSize: '12px',
            colors: '#666'
          },
          formatter: (val: number) => formatCurrencyMoney(val)
        }
      },
      grid: {
        show: true,
        strokeDashArray: 3,
        borderColor: '#f0f0f0',
        xaxis: { lines: { show: false } },
        yaxis: { lines: { show: true } }
      },
      colors: topAreaData?.map(() => '#2A6AEB'),
      tooltip: {
        enabled: true,
        shared: false,
        followCursor: true,
        custom: function ({ dataPointIndex }) {
          const data = topAreaData[dataPointIndex]

          if (!data) return ''

          return `
            <div style="
              background-color: rgba(26, 26, 26, 0.8);
              color: white;
              padding: 14px;
              border-radius: 16px;
              box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.1);
              font-family: Montserrat;
              min-width: 200px;
            ">
              <div style="font-weight: 700; font-size: 16px; margin-bottom: 8px;">
                ${data.name}
              </div>
              <div style="display: flex; align-items: center; gap: 8px;">
                <div style="
                  width: 12px;
                  height: 12px;
                  background-color: ${data.isCurrentMonth ? '#1890ff' : '#91caff'};
                  border-radius: 50%;
                "></div>
                <span style="font-size: 14px;">
                  ${chartType === CHART_TYPE.GROWTH ? 'Doanh thu tăng trưởng' : 'Doanh thu lũy kế'}:
                </span>
                <span style="font-weight: 700; font-size: 14px;">
                  ${formatCurrencyMoney(data.clickRate)} VNĐ
                </span>
              </div>
            </div>
          `
        }
      },
      legend: { show: false },
      states: {
        active: {
          allowMultipleDataPointsSelection: false,
          filter: { type: 'none' }
        },
        hover: {
          filter: { type: 'lighten', value: 0.1 }
        }
      }
    }

    return { series: seriesData, options: chartOptions }
  }, [topAreaData, chartType, chartName, getSelectArea])

  return (
    <div id={chartName} className='mt-10 w-full'>
      <BaseChart options={options} series={series} type='bar' height={400} isLoading={isLoadingButton} />
    </div>
  )
}
