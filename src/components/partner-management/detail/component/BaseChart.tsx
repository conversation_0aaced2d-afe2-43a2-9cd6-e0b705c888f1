import React from 'react'

import React<PERSON>pex<PERSON>hart from 'react-apexcharts'
import type { ApexOptions } from 'apexcharts'

interface BaseChartProps {
  options: ApexOptions
  series: any[]
  type: 'bar' | 'line' | 'area'
  height?: number | string
  width?: number | string
  isLoading?: boolean
  emptyMessage?: string
}

export const BaseChart: React.FC<BaseChartProps> = ({
  options,
  series,
  type,
  height = 400,
  width = '100%',
  isLoading = false,
  emptyMessage = 'Không có dữ liệu để hiển thị'
}) => {
  if (isLoading) {
    return (
      <div className='flex items-center justify-center' style={{ height }}>
        <div className='text-gray-500'>Đang tải dữ liệu...</div>
      </div>
    )
  }

  if (!series || series.length === 0) {
    return (
      <div className='flex items-center justify-center' style={{ height }}>
        <div className='text-gray-500'>{emptyMessage}</div>
      </div>
    )
  }

  return <ReactApexChart options={options} series={series} type={type} height={height} width={width} />
}
