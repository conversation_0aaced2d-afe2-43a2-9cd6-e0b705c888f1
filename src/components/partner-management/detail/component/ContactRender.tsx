type ContactRendererProps = {
  items: Array<{
    label: string
    value: any
    type?: string
    data?: any
  }>
}

export const ContactRenderer: React.FC<ContactRendererProps> = ({ items }) => {
  const createFieldHtml = (label: string, value: string) => `
    <div>
      <div style="color: #6b7280; font-size: 14px; margin-bottom: 4px;">${label}</div>
      <div style="color: #374151; font-weight: 500;">${value}</div>
    </div>
  `

  const renderContact = (contacts: any[]) => {
    return contacts
      ?.map((contact: any, index: number) => {
        const fields = [
          { label: 'Họ và tên', value: contact.name },
          { label: 'Chức vụ', value: contact.position },
          { label: 'Số điện thoại', value: contact.phone },
          { label: 'Email', value: contact.email }
        ]

        const fieldsHtml = fields.map(field => createFieldHtml(field.label, field.value)).join('')

        return `
        <div className="w-[1000px]" style="margin-bottom: 16px; background: #f8fafc; border-radius: 8px">
          <h4 style="margin-bottom: 12px; font-weight: 600; color: #374151;">Liên hệ phụ ${index + 1}</h4>
          <div style="padding: 12px 16px; border: 1px solid #e5e7eb;">
            <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 12px;">
              ${fieldsHtml}
            </div>
          </div>
        </div>
      `
      })
      .join('')
  }

  return (
    <div className='grid w-full grid-cols-4 gap-4'>
      {items.map((item, index) => {
        if (item.type === 'contact-list') {
          return (
            <div key={index}>
              <span className='font-normal text-text-neutral-light'>{item.label}</span>
              <div
                className='font-medium text-gray-800'
                dangerouslySetInnerHTML={{ __html: renderContact(item.data) }}
              />
            </div>
          )
        }

        return (
          <div key={index}>
            <span className='font-normal text-text-neutral-light'>{item.label}</span>
            <div className='font-medium text-gray-800'>{item.value}</div>
          </div>
        )
      })}
    </div>
  )
}
