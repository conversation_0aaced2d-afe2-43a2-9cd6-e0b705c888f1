import React, { useState, useCallback } from 'react'

import { <PERSON><PERSON>, <PERSON><PERSON>, Spin } from 'antd'
import { ArrowDownOutlined } from '@ant-design/icons'
import type { ApexOptions } from 'apexcharts'
import moment from 'moment'

import { SectionHeader } from '../common/SectionHeader'
import { baseColors, DATE_TIME } from '../common/constants'
import DashboardManagement from '@/models/DashboardManagement'
import DXPortal from '@/models/DXPortal'
import { useChartData } from '@/hooks/useChartData'
import { formatCurrencyMoney } from '@/utils/partnerUtils'
import { FilterButtons } from './component/FilterButton'
import { BaseChart } from './component/BaseChart'

interface TopSubServiceChartProps {
  chartName: string
}

const { TabPane } = Tabs

const CHART_TYPE = {
  GROWTH: 'GROWTH',
  CUMULATIVE: 'CUMULATIVE'
}

export const TopSubServiceChart: React.FC<TopSubServiceChartProps> = ({ chartName }) => {
  // const [isModalVisible, setIsModalVisible] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const {
    activeTab,
    reportCycle,
    topFilter,
    handleTabChange,
    setReportCycle,
    setTopFilter,
    growthData,
    cumulativeData,
    isLoadingGrowth,
    isLoadingCumulative,
    growthParams,
    cumulativeParams
  } = useChartData(chartName, 'overviewTopPricing')

  // Process chart data for stacked bar chart
  const processStackedData = useCallback(
    (rawData: any[]) => {
      if (!rawData || !Array.isArray(rawData)) return []

      const groupedByTime = rawData.reduce(
        (acc, item) => {
          const timeKey = item.labelTime

          if (!acc[timeKey]) acc[timeKey] = []
          acc[timeKey].push(item)

          return acc
        },
        {} as Record<string, any[]>
      )

      return Object.keys(groupedByTime)
        .sort()
        .map(timeKey => {
          const items = groupedByTime[timeKey]
          const parts = timeKey.split('/')
          const displayTime = parts.length > 1 ? parts.reverse().join('/') : timeKey

          const limit = topFilter === -1 ? items.length : Math.min(topFilter, items.length)

          const services = items
            .sort((a: any, b: any) => b.amountPreTax - a.amountPreTax)
            .slice(0, limit)
            .map((item: any) => ({
              name: `${item.serviceName} - ${item.pricingName}`,
              value: item.amountPreTax,
              pricingLatestUniqueId: item.pricingLatestUniqueId,
              fullName: `${item.serviceName} - ${item.pricingName}`
            }))

          return {
            labelTime: timeKey,
            displayTime,
            services
          }
        })
    },
    [topFilter]
  )

  const generateColors = (count: number): string[] => {
    const colors: string[] = []

    for (let i = 0; i < count; i++) {
      colors.push(baseColors[i % baseColors.length])
    }

    return colors
  }

  const createStackedChartConfig = (data: any[]) => {
    if (!data || data.length === 0) {
      return { series: [], options: {} }
    }

    const allServiceNames = new Set<string>()

    data.forEach(timeData => {
      timeData.services.forEach((service: any) => {
        allServiceNames.add(service.name)
      })
    })

    const serviceNamesArray = Array.from(allServiceNames)
    const colors = generateColors(serviceNamesArray.length)

    const series = serviceNamesArray.map((serviceName, index) => ({
      name: serviceName,
      data: data.map(timeData => {
        const service = timeData.services.find((s: any) => s.name === serviceName)

        return service?.value || 0
      }),
      color: colors[index]
    }))

    const options: ApexOptions = {
      chart: {
        type: 'bar',
        stacked: true,
        toolbar: { show: false },
        zoom: { enabled: false },
        animations: {
          enabled: true,
          easing: 'easeinout',
          speed: 800
        }
      },
      plotOptions: {
        bar: {
          horizontal: false,
          columnWidth: '60%',
          dataLabels: {
            total: {
              enabled: true,
              style: {
                fontSize: '12px',
                fontWeight: 'bold'
              }
            }
          }
        }
      },
      dataLabels: { enabled: false },
      xaxis: {
        categories: data.map(item => item.displayTime),
        labels: {
          style: { fontSize: '12px', colors: '#666' }
        }
      },
      yaxis: {
        labels: {
          formatter: (val: number) => formatCurrencyMoney(val),
          style: { fontSize: '12px', colors: '#666' }
        }
      },
      tooltip: {
        shared: true,
        intersect: false,
        followCursor: true,
        custom: function ({ dataPointIndex }) {
          const timeData = data[dataPointIndex]

          if (!timeData) return ''

          // Calculate total value for this time period
          const totalValue = timeData.services.reduce((sum: number, service: any) => sum + service.value, 0)

          return `
            <div style="
              background-color: rgba(26, 26, 26, 0.8);
              color: white;
              padding: 14px;
              border-radius: 16px;
              box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.1);
              font-family: Montserrat;
              min-width: 250px;
            ">
              <div style="font-weight: 700; font-size: 16px; margin-bottom: 12px;">
                ${timeData.displayTime}
              </div>
              <div style="margin-bottom: 8px;">
                <span style="font-size: 14px; font-weight: 600;">
                  Tổng doanh thu: ${formatCurrencyMoney(totalValue)} VNĐ
                </span>
              </div>
              <div style="border-top: 1px solid rgba(255,255,255,0.2); padding-top: 8px;">
                ${timeData.services
                  .map(
                    (service: any) => `
                  <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 6px;">
                    <div style="
                      width: 12px;
                      height: 12px;
                      background-color: ${colors[serviceNamesArray.findIndex(name => name === service.name)]};
                      border-radius: 50%;
                    "></div>
                    <span style="font-size: 12px; flex: 1;">
                      ${service.name}:
                    </span>
                    <span style="font-weight: 600; font-size: 12px;">
                      ${formatCurrencyMoney(service.value)} VNĐ
                    </span>
                  </div>
                `
                  )
                  .join('')}
              </div>
            </div>
          `
        }
      },
      legend: {
        position: 'bottom',
        fontSize: '12px',
        labels: { useSeriesColors: true }
      },
      colors: colors,
      grid: {
        strokeDashArray: 3,
        borderColor: '#f0f0f0'
      }
    }

    return { series, options }
  }

  const handleReport = async () => {
    try {
      setIsLoading(true)
      const paramsSource = activeTab === CHART_TYPE.GROWTH ? growthParams : cumulativeParams

      const res = await DashboardManagement.reportTopPricingData({
        top: paramsSource.top || 5,
        reportCycle: paramsSource.reportCycle,
        chartType: paramsSource.chartType,
        startTime: paramsSource.startTime,
        endTime: paramsSource.endTime,
        serviceType: paramsSource.serviceType
      })

      const fileName = `Chi tiết top gói cước có doanh thu cao nhất_${moment().format(DATE_TIME)}`

      if (res.type === 'text/csv') {
        DXPortal.exportFile(res?.body, fileName, 'text/csv;charset=utf-8')
      } else {
        DXPortal.exportFile(res?.body, fileName)
      }
    } catch (error) {
      console.error('Export error:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const currentData = activeTab === CHART_TYPE.GROWTH ? growthData : cumulativeData
  const processedData = processStackedData(currentData || [])
  const { series, options } = createStackedChartConfig(processedData)

  return (
    <div className='flex flex-col gap-5 rounded-xl'>
      <Tabs
        activeKey={activeTab}
        onChange={handleTabChange}
        renderTabBar={(tabBarProps, DefaultTabBar) => (
          <div className='flex items-center justify-between'>
            <SectionHeader icon='onedx-money-invoice' title='Top gói cước có doanh thu cao nhất' />
            <DefaultTabBar {...tabBarProps} />
          </div>
        )}
        className='custom-tabs'
      >
        <TabPane tab='Tăng trưởng' key={CHART_TYPE.GROWTH}>
          <div className='flex justify-between pt-6'>
            <FilterButtons
              reportCycle={reportCycle}
              topFilter={topFilter}
              onTimeFilter={setReportCycle}
              onTopFilter={setTopFilter}
              showTopFilter={true}
            />
            <Button
              color='primary'
              variant='outlined'
              onClick={handleReport}
              loading={isLoading}
              icon={<ArrowDownOutlined />}
            >
              Tải báo cáo
            </Button>
          </div>

          <Spin spinning={isLoadingGrowth}>
            <div className='h-96'>
              <BaseChart options={options} series={series} type='bar' height='100%' isLoading={isLoadingGrowth} />
            </div>
          </Spin>
        </TabPane>

        <TabPane tab='Lũy kế' key={CHART_TYPE.CUMULATIVE}>
          <div className='flex justify-between pt-6'>
            <FilterButtons
              reportCycle={reportCycle}
              topFilter={topFilter}
              onTimeFilter={setReportCycle}
              onTopFilter={setTopFilter}
              showTopFilter={true}
            />
            <Button
              color='primary'
              variant='outlined'
              onClick={handleReport}
              loading={isLoading}
              icon={<ArrowDownOutlined />}
            >
              Tải báo cáo
            </Button>
          </div>

          <Spin spinning={isLoadingCumulative}>
            <div className='h-96'>
              <BaseChart options={options} series={series} type='bar' height='100%' isLoading={isLoadingCumulative} />
            </div>
          </Spin>
        </TabPane>
      </Tabs>
    </div>
  )
}
