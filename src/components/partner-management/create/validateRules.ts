import type { Rule } from 'antd/es/form'

import type {
  CertificateType,
  ContactFields,
  CustomerType,
  FinancialFields,
  GeneralInfoFields,
  RepresentativeFields
} from '@/types/partner-portal/partner'
import {
  validateCode,
  validateEmail,
  validateNumberInput,
  validatePassword,
  validatePhoneNumber1,
  validateRequire,
  validateStrLength,
  validateUrl
} from '@/validator'
import partnerManagementInstance from '@/models/PartnerManagement'
import { getEndOfDay } from '@/utils/date'
import { createAsyncValidatorWithParams } from '@/validator/asyncValidator'
import { USER_EXCLUDE_NONE } from '@/views/dev-auth/utils/constants'

// TODO: File này có thể tạo thành một utility function dùng chung để tái sử dụng

type Result = { rules?: Rule[]; [key: string]: any }
type ExtraProps<T extends string | number | symbol> = Partial<Record<T, Result>>

type GeneralInfoProps = (params: {
  id?: number
  customerType?: CustomerType
  repPersonalCertType?: CertificateType
}) => ExtraProps<GeneralInfoFields>

type ContactInfoProps = (params: { id?: number }) => ExtraProps<ContactFields>

const taxCodeRegex = /^[a-zA-Z0-9]+$/

const formProperty = ['rules', 'validateFirst', 'normalize'] // Add more properties as needed

const antdProperty = ['maxLength', 'maxDate', 'minDate'] // Add more properties as needed

// #region API Validator
const validateTaxCodeDuplicate = createAsyncValidatorWithParams({
  validatorFn: partnerManagementInstance.checkTaxCode,
  errorMessage: 'Mã số thuế đã tồn tại trong hệ thống',
  resultPassed: false, // API trả về true = tồn tại = invalid
  debounceMs: 500
})

export const validateEmailDuplicate = createAsyncValidatorWithParams({
  validatorFn: partnerManagementInstance.checkPrimaryEmail,
  errorMessage: 'Email đã tồn tại trong hệ thống',
  resultPassed: false,
  debounceMs: 500
})

export const validateUserEmailDuplicate = createAsyncValidatorWithParams({
  validatorFn: value => partnerManagementInstance.checkEmail(value, USER_EXCLUDE_NONE),
  errorMessage: 'Email đã tồn tại trong hệ thống',
  resultPassed: false,
  debounceMs: 500
})

export const validatePhoneDuplicate = createAsyncValidatorWithParams({
  validatorFn: partnerManagementInstance.checkPhoneNumber,
  errorMessage: 'Số điện thoại đã tồn tại trong hệ thống',
  resultPassed: false,
  debounceMs: 500
})

const validateRepPersonalCertNumber = createAsyncValidatorWithParams({
  validatorFn: partnerManagementInstance.checkRepPersonalCertNumber,
  errorMessage: 'Số giấy chứng nhận đã tồn tại trong hệ thống',
  resultPassed: false,
  debounceMs: 500
})
// #endregion API Validator

// #region Thông tin chung
const getNameExtra = (customerType?: CustomerType) => {
  if (customerType === 'PERSONAL') {
    return {
      rules: [validateRequire('Họ và tên không được bỏ trống')],
      maxLength: 100
    }
  } else if (customerType === 'ENTERPRISE') {
    return {
      rules: [validateRequire('Tên doanh nghiệp không được bỏ trống')],
      maxLength: 300
    }
  } else {
    return {}
  }
}

export const generalInfoExtraProps: GeneralInfoProps = ({ customerType, id, repPersonalCertType }) => ({
  // Loại hình đối tác
  customerType: {
    rules: [validateRequire('Loại hình đối tác không được bỏ trống')]
  },
  name: getNameExtra(customerType), // Tên Doanh nghiệp
  // Mã số thuế
  taxCode: {
    rules: [
      validateRequire('Mã số thuế không được bỏ trống'),
      validateStrLength(10, 13, 'Sai định dạng mã số thuế'),
      validateCode('Sai định dạng mã số thuế', taxCodeRegex),
      validateTaxCodeDuplicate(id)
    ],
    validateFirst: true,
    maxLength: 13
  },
  // Số BHXH
  socialInsuranceNumber: {
    rules: [validateCode('Sai định dạng Số BHXH')],
    maxLength: 10
  },
  website: {
    rules: [validateUrl()],
    maxLength: 100
  },
  // Giấy chứng thực
  repPersonalCertType: {
    rules: [validateRequire('Giấy chứng thực không được bỏ trống')]
  },
  // Số giấy chứng thực
  repPersonalCertNumber: {
    rules: [
      validateRequire('Số giấy chứng thực không được bỏ trống'),
      validateRepPersonalCertNumber(id, repPersonalCertType)
    ]
  },
  // Địa chỉ đăng ký kinh doanh
  businessRegistrationAddress: {
    rules: [validateRequire('Địa chỉ đăng ký kinh doanh không được bỏ trống')],
    maxLength: 500
  },
  residentAddress: {
    rules: [validateRequire('Địa chỉ thường trú không được bỏ trống')],
    maxLength: 500
  }
})

// #endregion Thông tin chung

// #region Năng lực và Địa bàn

// export const capabilityLocationExtraProps: ExtraProps<CapacityLocationFields> = {
//   // Loại hình năng lực
//   capabilities: {
//     rules: [validateRequire('Năng lực cung cấp không được bỏ trống')]
//   },
//   operatingAreas: {
//     rules: [validateArrayRequired('Địa bàn hoạt động không được bỏ trống')]
//   }
// }
// #endregion Năng lực và Địa bàn

//#region Người đại diện

export const representativeExtraProps: ExtraProps<RepresentativeFields> = {
  // Tên người đại diện
  name: {
    maxLength: 50
  },
  // Chức danh
  position: {
    maxLength: 50
  },
  // // Giới tính
  // gender: {
  // },
  // // Ngày sinh
  // dateOfBirth: {
  // },
  // //Loại giấy chứng thực
  // certType: {
  // },
  // // Số giấy chứng thực
  // certNo: {
  // },
  // Ngày cấp
  certIssueDate: {
    // rules: [validateRequire('Ngày cấp không được bỏ trống')]
    maxDate: getEndOfDay()
  },
  // Nơi cấp
  certIssuePlace: {
    maxLength: 50
  },
  // Nơi đăng ký hộ khẩu
  registeredResidence: {
    maxLength: 500
  },
  // Chỗ ở hiện tại
  currentAddress: {
    maxLength: 500
  }
}
// #endregion Người đại diện

// #region Liên hệ
export const contactInfoExtraProps: ContactInfoProps = ({ id }) => ({
  phoneNumber: {
    rules: [
      validateRequire('Số điện thoại không được bỏ trống'),
      validatePhoneNumber1('Sai định dạng số điện thoại', 10),
      validatePhoneDuplicate(id)
    ],
    validateFirst: true
  },
  primaryEmail: {
    rules: [
      validateRequire('Email không được bỏ trống'),
      validateEmail('Sai định dạng email'),
      validateEmailDuplicate(id)
    ],
    validateFirst: true,
    maxLength: 100
  },
  officeAddress: {
    maxLength: 500
  },
  // Liên hệ phụ
  name: {
    maxLength: 100
  },
  email: {
    rules: [validateEmail('Sai định dạng email')],
    maxLength: 100
  },
  phone: {
    rules: [validatePhoneNumber1('Sai định dạng số điện thoại', 10)]
  }
})
// #endregion Liên hệ

// #region Tài chính, Tài khoản
export const financialExtraProps: ExtraProps<FinancialFields> = {
  email: {
    rules: [validateRequire('Email đăng nhập không được bỏ trống'), validateEmail('Sai định dạng email')],
    maxLength: 100
  },
  password: {
    rules: [validatePassword(), validateRequire('Mật khẩu không được bỏ trống')]
  },
  name: {
    rules: [validateRequire('Tên chủ tài khoản không được bỏ trống')],
    maxLength: 100
  },
  number: {
    rules: [validateRequire('Số tài khoản không được bỏ trống'), validateNumberInput('Sai định dạng số tài khoản')],
    maxLength: 100
  },
  bank: {
    rules: [validateRequire('Tên ngân hàng không được bỏ trống')],
    maxLength: 100
  },
  branch: {
    rules: [validateRequire('Chi nhánh không được bỏ trống')],
    maxLength: 100
  }
}
// #endregion Tài chính, Tài khoản

// #region Function

const resolveFieldKey = (field: any): string => {
  if (Array.isArray(field) && field.length > 0) {
    return String(field[field.length - 1])
  }

  return String(field)
}

/**
 * Helper chung để trích các thuộc tính từ extraProps cho 1 field
 */
const extractFieldProps = <T extends string | number | symbol>(
  field: T | Array<string | number | symbol>,
  extraProps: ExtraProps<T>,
  propertiesToExtract: string[]
): Record<string, any> => {
  if (!field || !extraProps) return {}

  const key = resolveFieldKey(field)
  const fieldProps = (extraProps as any)[key]

  if (!fieldProps) return {}

  const result: Record<string, any> = {}

  propertiesToExtract.forEach(prop => {
    if (fieldProps[prop] !== undefined) {
      result[prop] = fieldProps[prop]
    }
  })

  return result
}

// Lấy {...props} trong <Form.Item>
export const getFormItemProps = <T extends string | number | symbol>(
  field: T | Array<string | number | symbol>,
  extraProps: ExtraProps<T>,
  customProperties?: string[]
) => {
  const propertiesToExtract = customProperties || formProperty

  return extractFieldProps(field, extraProps, propertiesToExtract)
}

// Lấy {...props} trong antd Component khác
export const getAntdComponentProps = <T extends string | number | symbol>(
  field: T | Array<string | number | symbol>,
  extraProps: ExtraProps<T>,
  customProperties?: string[]
) => {
  const propertiesToExtract = customProperties || antdProperty

  return extractFieldProps(field, extraProps, propertiesToExtract)
}
