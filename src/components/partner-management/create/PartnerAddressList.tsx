import { Button, Form, Select } from 'antd'

import { PartnerCard } from '../common'
import { stylesSubCard } from '../common/constants'
import type { OperatingArea } from '@/types/partner-portal/partner'
import { validateArrayRequired, validateRequire } from '@/validator'
import { useNewLocation } from '@/hooks/useNewLocation'

interface Props {
  name: string
  title?: string
}

interface AddressItemProps {
  field: any
  index: number
  onRemove: () => void
  name: string
  title: string
}

const initialValues: Partial<OperatingArea>[] = [
  {
    provinceId: undefined,
    wardId: undefined,
    streetId: undefined,
    provinceName: '',
    wardName: '',
    streetName: ''
  }
]

// Component con cho mỗi địa bàn với instance useNewLocation riêng
const AddressItem: React.FC<AddressItemProps> = ({ field, index, onRemove, name, title }) => {
  const form = Form.useFormInstance()

  // Mỗi item có instance useNewLocation riêng
  const { address, updateAddress, provinceList, wardList, streetList, loadingProvince, loadingWard, loadingStreet } =
    useNewLocation(`${name}-useNewLocation`)

  const getOptionLabel = (option: any, key?: string): string => {
    key = key || 'label'
    const selectedOption = Array.isArray(option) ? option[0] : option

    return selectedOption && typeof selectedOption === 'object' && key in selectedOption ? selectedOption[key] : ''
  }

  const handleProvinceChange = (value: any, option: any) => {
    updateAddress('provinceId', value)
    form.setFieldsValue({
      [name]: {
        ...form.getFieldValue(name),
        [field.name]: {
          ...form.getFieldValue([name, field.name]),
          wardId: null,
          streetId: null,
          provinceName: getOptionLabel(option)
          // provinceCode: getOptionLabel(option, 'code')
        }
      }
    })
  }

  const handleWardChange = (value: any, option: any) => {
    updateAddress('wardId', value)
    form.setFieldsValue({
      [name]: {
        ...form.getFieldValue(name),
        [field.name]: {
          ...form.getFieldValue([name, field.name]),
          streetId: null,
          wardName: getOptionLabel(option)
          // wardCode: getOptionLabel(option, 'code')
        }
      }
    })
  }

  const handleStreetChange = (value: any, option: any) => {
    updateAddress('streetId', value)
    form.setFieldsValue({
      [name]: {
        ...form.getFieldValue(name),
        [field.name]: {
          ...form.getFieldValue([name, field.name]),
          streetName: getOptionLabel(option)
        }
      }
    })
  }

  return (
    <PartnerCard
      key={field.key}
      isUseRawTitle
      title={
        <div className='body-14-medium inline-flex items-center justify-start text-text-neutral-light'>
          {title} {index + 1}
        </div>
      }
      styles={stylesSubCard}
      rightContent={
        <Button type='link' onClick={onRemove}>
          <i className='onedx-delete size-4 text-icon-neutral-light' />
        </Button>
      }
    >
      <div className='grid grid-cols-3 gap-4'>
        <Form.Item
          {...field}
          layout='vertical'
          key={`province-${field.key}`}
          name={[field.name, 'provinceId']}
          label='Tỉnh thành'
          rules={[validateRequire('Tỉnh thành không được bỏ trống')]}
        >
          <Select
            placeholder='Chọn tỉnh thành'
            loading={loadingProvince}
            options={provinceList}
            onChange={handleProvinceChange}
            showSearch
            filterOption={(input, option) => (option?.label ?? '').toLowerCase().includes(input.toLowerCase())}
          />
        </Form.Item>

        <Form.Item
          {...field}
          layout='vertical'
          key={`ward-${field.key}`}
          name={[field.name, 'wardId']}
          label='Phường/xã'
          rules={[validateRequire('Phường/xã không được bỏ trống')]}
        >
          <Select
            placeholder='Chọn phường/xã'
            loading={loadingWard}
            options={wardList}
            onChange={handleWardChange}
            disabled={!address.provinceId}
            filterOption={(input, option) => (option?.label ?? '').toLowerCase().includes(input.toLowerCase())}
          />
        </Form.Item>

        <Form.Item
          {...field}
          layout='vertical'
          key={`street-${field.key}`}
          name={[field.name, 'streetId']}
          label='Phố/đường'
          rules={[validateRequire('Phố/đường không được bỏ trống')]}
        >
          <Select
            placeholder='Chọn phố/đường'
            loading={loadingStreet}
            options={streetList}
            onChange={handleStreetChange}
            disabled={!address.wardId}
            showSearch
            filterOption={(input, option) => (option?.label ?? '').toLowerCase().includes(input.toLowerCase())}
          />
        </Form.Item>
      </div>
    </PartnerCard>
  )
}

const PartnerAddressList: React.FC<Props> = ({ name, title = 'Địa bàn' }) => {
  return (
    <Form.List
      name={name}
      initialValue={initialValues}
      rules={[validateArrayRequired('Địa bàn hoạt động không được bỏ trống')]}
    >
      {(fields, { remove }, { errors }) => (
        <div className='flex flex-col gap-4'>
          {fields.map((field, index) => (
            <AddressItem
              key={field.key}
              field={field}
              index={index}
              onRemove={() => remove(field.name)}
              name={name}
              title={title}
            />
          ))}
          <Form.ErrorList className='text-[#ff4d4f]' errors={errors} />
        </div>
      )}
    </Form.List>
  )
}

export { PartnerAddressList }
