'use client'

import React, { useEffect } from 'react'

import { Checkbox, DatePicker, Form, Input, Select } from 'antd'

import { PartnerButton, PartnerCard, PartnerHeader } from '../common'
import { stylesCard } from '../common/constants'
import {
  addFormListItem,
  convertSubmitData,
  representativeTypeOptions,
  serviceCapabilityOptions
} from '@/views/partner-management/common'
import { generatePassword } from '@/utils/string'
import { PartnerAddressList } from './PartnerAddressList'
import { GeneralInfoSection, ContactInfoSection, ContractSection } from './section'
import type { CreatePartnerRequest, UpdatePartnerRequest } from '@/types/partner-portal/partner'
import Field from '../common/Field'
import { financialExtraProps, representativeExtraProps } from './validateRules'
import { validateRequire } from '@/validator'
import { DATE_FORMATS, formatDate, getDate } from '@/utils/date'

interface PartnerFormProps {
  id?: number
  initialValues?: Partial<CreatePartnerRequest | UpdatePartnerRequest>
  onSubmitAction: (values: CreatePartnerRequest | UpdatePartnerRequest) => void
  onCancel?: () => void
  loading?: boolean
  mode?: 'create' | 'edit'
  portalType?: 'DEV' | 'SME'
  className?: string
}

export const PartnerForm: React.FC<PartnerFormProps> = ({
  id,
  initialValues,
  onSubmitAction,
  onCancel,
  loading = false,
  mode = 'create',
  portalType = 'DEV',
  className = ''
}) => {
  const [form] = Form.useForm()

  const handleSubmit = () => {
    const values = form.getFieldsValue(true) as CreatePartnerRequest | UpdatePartnerRequest

    // Lọc bỏ các object rỗng (chỉ chứa key-value rỗng hoặc null/undefined)
    const secondaryContacts = (values.secondaryContacts || []).filter(
      (contact: any) =>
        contact &&
        Object.keys(contact).some(key => contact[key] !== null && contact[key] !== undefined && contact[key] !== '')
    )

    const submitData = convertSubmitData({
      ...values,
      portalType,
      secondaryContacts
    })

    onSubmitAction(submitData)
  }

  useEffect(() => {
    if (initialValues) {
      form.setFieldsValue(initialValues)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [initialValues])

  return (
    <div className={`partner-form ${className}`}>
      <Form
        form={form}
        className='flex flex-col gap-2'
        layout='vertical'
        initialValues={initialValues}
        onFinish={handleSubmit}
        onFinishFailed={info => {
          console.log('Form submission failed:', info, form.getFieldsValue(true))
        }}
        disabled={loading}
      >
        <div className='grid grid-cols-10 gap-2'>
          <div className='col-span-3 flex flex-col gap-2'>
            {/* Thông tin chung & pháp lý */}
            <div className='bg-white'>
              <GeneralInfoSection id={id} />
            </div>

            {/* Thông tin tài chính & tài khoản */}
            <div className='flex-1 bg-white'>
              <PartnerCard title='Thông tin tài chính & tài khoản'>
                <div className='flex flex-col gap-4'>
                  <PartnerCard title='Tài khoản ngân hàng' dotColor='green' dotType='medium' styles={stylesCard}>
                    <div className='grid grid-cols-2 gap-4'>
                      <Field
                        name={['bankAccount', 'name']}
                        label='Tên chủ tài khoản'
                        validateRules={financialExtraProps}
                      >
                        <Input placeholder='Nhập tên chủ tài khoản' />
                      </Field>

                      <Field name={['bankAccount', 'number']} label='Số tài khoản' validateRules={financialExtraProps}>
                        <Input placeholder='Nhập số tài khoản' />
                      </Field>

                      <Field name={['bankAccount', 'bank']} label='Tên ngân hàng' validateRules={financialExtraProps}>
                        <Input placeholder='Nhập tên ngân hàng' />
                      </Field>

                      <Field name={['bankAccount', 'branch']} label='Chi nhánh' validateRules={financialExtraProps}>
                        <Input placeholder='Nhập chi nhánh' />
                      </Field>
                    </div>
                  </PartnerCard>

                  <PartnerCard title='Tài khoản đối tác' styles={stylesCard} dotColor='green' dotType='medium'>
                    <div className='flex flex-col gap-4'>
                      <Form.Item name='welcomeEmail'>
                        <Checkbox>Gửi email chào mừng</Checkbox>
                      </Form.Item>

                      <Field name='email' label='Email đăng nhập' validateRules={financialExtraProps}>
                        <Input placeholder='Nhập email đăng nhập' autoComplete='username' />
                      </Field>

                      <div className='flex gap-2'>
                        <Field name='password' label='Mật khẩu' className='flex-1' validateRules={financialExtraProps}>
                          <Input.Password placeholder='Nhập mật khẩu' autoComplete='new-password' />
                        </Field>
                        <Form.Item label=' '>
                          <PartnerButton
                            type='primary'
                            onClick={() => {
                              form.setFieldsValue({ password: generatePassword() })
                            }}
                            className='w-full'
                          >
                            Mật khẩu ngẫu nhiên
                          </PartnerButton>
                        </Form.Item>
                      </div>
                    </div>
                  </PartnerCard>
                </div>
              </PartnerCard>
            </div>
          </div>

          <div className='col-span-7 flex flex-col gap-2'>
            {/* Năng lực và Địa bàn */}
            <PartnerCard title='Năng lực và Địa bàn'>
              <div className='grid grid-cols-1 gap-4'>
                <div className='flex flex-col gap-2.5'>
                  <PartnerHeader title='Năng lực cung cấp' color='red' type='small' textClassname='caption-12-medium' />
                  <Form.Item name='capabilities' rules={[validateRequire('Năng lực cung cấp không được bỏ trống')]}>
                    <Select
                      mode='multiple'
                      placeholder='Chọn một hoặc nhiều năng lực cung cấp'
                      options={serviceCapabilityOptions}
                      allowClear
                      showSearch={false}
                    />
                  </Form.Item>
                </div>
                <PartnerCard
                  title='Địa bàn hoạt động'
                  dotColor='green'
                  dotType='medium'
                  styles={stylesCard}
                  rightContent={
                    <PartnerButton
                      type='link'
                      className='items-justify-center flex'
                      onClick={e => {
                        e.stopPropagation()
                        addFormListItem(form, 'operatingAreas')
                      }}
                    >
                      <i className='onedx-plus size-5' />
                      <span>Thêm địa bàn</span>
                    </PartnerButton>
                  }
                >
                  <PartnerAddressList name='operatingAreas' />
                </PartnerCard>
              </div>
            </PartnerCard>

            {/* Thông tin người đại diện */}
            <PartnerCard title='Thông tin người đại diện'>
              <div className='flex flex-col gap-4'>
                <div className='grid grid-cols-5 gap-4'>
                  <Field
                    name={['representative', 'name']}
                    label='Người đại diện pháp luật'
                    validateRules={representativeExtraProps}
                  >
                    <Input placeholder='Nhập tên người đại diện pháp luật' />
                  </Field>

                  <Field
                    name={['representative', 'position']}
                    label='Chức danh'
                    validateRules={representativeExtraProps}
                  >
                    <Input placeholder='Nhập chức danh' />
                  </Field>

                  <Field name={['representative', 'gender']} label='Giới tính' validateRules={representativeExtraProps}>
                    <Select placeholder='Chọn giới tính'>
                      <Select.Option value='MALE'>Nam</Select.Option>
                      <Select.Option value='FEMALE'>Nữ</Select.Option>
                      <Select.Option value='OTHER'>Khác</Select.Option>
                    </Select>
                  </Field>

                  <Field
                    name={['representative', 'dateOfBirth']}
                    label='Ngày sinh'
                    validateRules={representativeExtraProps}
                    getValueProps={value =>
                      value ? { value: getDate(value, DATE_FORMATS.DD_MM_YYYY_SLASH) } : { value: undefined }
                    }
                    normalize={value => (value ? formatDate(value, DATE_FORMATS.DD_MM_YYYY_SLASH) : '')}
                  >
                    <DatePicker
                      className='w-full'
                      format={DATE_FORMATS.DD_MM_YYYY_SLASH}
                      placeholder='Chọn ngày sinh'
                    />
                  </Field>

                  <Field
                    name={['representative', 'certType']}
                    label='Loại giấy chứng thực'
                    validateRules={representativeExtraProps}
                  >
                    <Select placeholder='Chọn loại giấy chứng thực' options={representativeTypeOptions} />
                  </Field>
                </div>

                <div className='grid grid-cols-5 gap-4'>
                  <Field
                    name={['representative', 'certNo']}
                    label='Số giấy chứng thực'
                    validateRules={representativeExtraProps}
                  >
                    <Input placeholder='Nhập số giấy chứng thực' />
                  </Field>

                  <Field
                    name={['representative', 'certIssueDate']}
                    label='Ngày cấp'
                    getValueProps={value =>
                      value ? { value: getDate(value, DATE_FORMATS.DD_MM_YYYY_SLASH) } : { value: undefined }
                    }
                    normalize={value => (value ? formatDate(value, DATE_FORMATS.DD_MM_YYYY_SLASH) : '')}
                    validateRules={representativeExtraProps}
                  >
                    <DatePicker
                      style={{ width: '100%' }}
                      format={DATE_FORMATS.DD_MM_YYYY_SLASH}
                      placeholder='Chọn ngày cấp'
                    />
                  </Field>

                  <Field
                    name={['representative', 'certIssuePlace']}
                    label='Nơi cấp'
                    validateRules={representativeExtraProps}
                  >
                    <Input placeholder='Nhập nơi cấp' />
                  </Field>

                  <Field
                    name={['representative', 'registeredResidence']}
                    label='Nơi đăng ký hộ khẩu'
                    validateRules={representativeExtraProps}
                  >
                    <Input placeholder='Nhập nơi đăng ký hộ khẩu' />
                  </Field>

                  <Field
                    name={['representative', 'currentAddress']}
                    label='Chỗ ở hiện tại'
                    validateRules={representativeExtraProps}
                  >
                    <Input placeholder='Nhập chỗ ở hiện tại' />
                  </Field>
                </div>
              </div>
            </PartnerCard>

            {/* Thông tin liên hệ */}
            <ContactInfoSection id={id} />

            {/* Hợp đồng & tài liệu */}
            <div className='flex-1 bg-white'>
              <ContractSection />
            </div>
          </div>
        </div>

        <div className='flex items-center justify-end gap-4 bg-white p-4'>
          <PartnerButton variant='outlined' type='primary' onClick={() => onCancel && onCancel()}>
            Hủy
          </PartnerButton>
          <PartnerButton type='primary' variant='solid' htmlType='submit'>
            {mode === 'edit' ? 'Cập nhật' : 'Tạo'}
          </PartnerButton>
        </div>
      </Form>
    </div>
  )
}

export default PartnerForm
