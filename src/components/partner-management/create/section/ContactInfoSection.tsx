'use client'

import React, { useMemo } from 'react'

import { Form, Input } from 'antd'

import { PartnerButton, PartnerCard } from '../../common'
import { stylesCard, stylesSubCard } from '../../common/constants'
import { addFormListItem } from '@/views/partner-management/common'
import { contactInfoExtraProps } from '../validateRules'
import Field from '../../common/Field'

interface ContactInfoSectionProps {
  id?: number
}

export const ContactInfoSection: React.FC<ContactInfoSectionProps> = ({ id }) => {
  const form = Form.useFormInstance()

  const validateRules = useMemo(() => {
    const rules = contactInfoExtraProps({ id })

    return rules
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id])

  return (
    <PartnerCard title='Thông tin liên hệ'>
      <div className='flex flex-col gap-4'>
        <div className='grid grid-cols-3 gap-4'>
          <Field
            name='phoneNumber'
            label='<PERSON><PERSON> điện thoại chính'
            validateRules={validateRules}
            // rules={[
            //   { required: true, message: '<PERSON>ui lòng nhập số điện thoại chính' },
            //   { pattern: /^[0-9]{9,12}$/, message: 'Số điện thoại không hợp lệ' }
            // ]}
          >
            <Input placeholder='Nhập số điện thoại chính' />
          </Field>
          <Field name='primaryEmail' label='Email chính' validateRules={validateRules}>
            <Input placeholder='Nhập email chính' />
          </Field>
          <Field name='officeAddress' label='Địa chỉ văn phòng làm việc' validateRules={validateRules}>
            <Input placeholder='Nhập địa chỉ văn phòng làm việc' />
          </Field>
        </div>

        <PartnerCard
          title='Liên hệ phụ'
          dotColor='green'
          dotType='medium'
          styles={stylesCard}
          rightContent={
            <PartnerButton
              type='link'
              className='items-justify-center flex'
              onClick={e => {
                e.stopPropagation()
                addFormListItem(form, 'secondaryContacts')
              }}
            >
              <i className='onedx-plus size-5' />
              <span>Thêm liên hệ</span>
            </PartnerButton>
          }
        >
          <Form.List name='secondaryContacts' initialValue={[{}]}>
            {(fields, { remove }) => (
              <div className='flex flex-col gap-4'>
                {fields.map(({ key, name, ...restField }, index) => (
                  <PartnerCard
                    key={key}
                    title={<div className='body-14-medium text-text-neutral-light'>Liên hệ {index + 1}</div>}
                    styles={stylesSubCard}
                    isUseRawTitle
                    rightContent={
                      <PartnerButton size='small' type='link' onClick={() => remove(name)}>
                        <i className='onedx-delete size-4 text-icon-neutral-light' />
                      </PartnerButton>
                    }
                  >
                    <div className='grid grid-cols-4 gap-4'>
                      <Field {...restField} name={[name, 'name']} label='Họ và tên' validateRules={validateRules}>
                        <Input placeholder='Nhập họ và tên' />
                      </Field>

                      <Field {...restField} name={[name, 'position']} label='Chức vụ' validateRules={validateRules}>
                        <Input placeholder='Nhập chức vụ' />
                      </Field>

                      <Field
                        {...restField}
                        name={[name, 'email']}
                        label='Email'
                        rules={[{ type: 'email', message: 'Email không hợp lệ' }]}
                        validateRules={validateRules}
                      >
                        <Input placeholder='Nhập email' />
                      </Field>

                      <Field {...restField} name={[name, 'phone']} label='Số điện thoại' validateRules={validateRules}>
                        <Input placeholder='Nhập số điện thoại' />
                      </Field>
                    </div>
                  </PartnerCard>
                ))}
              </div>
            )}
          </Form.List>
        </PartnerCard>
      </div>
    </PartnerCard>
  )
}
