'use client'

import React, { useMemo } from 'react'

import { Form, Input, Select, Switch } from 'antd'

import { PartnerCard } from '../../common'
import { stylesCard } from '../../common/constants'
import {
  customerTypeOptions,
  representativeTypeOptions,
  STATUS_ENUM,
  statusOptions
} from '@/views/partner-management/common'
import { generalInfoExtraProps } from '../validateRules'
import Field from '../../common/Field'
import { useNewLocation } from '@/hooks/useNewLocation'
import { validateRequire } from '@/validator'
import { filterOptionTrim } from '@/utils/string'

export const GeneralInfoSection: React.FC<{ id?: number }> = ({ id }) => {
  const form = Form.useFormInstance()
  const status = Form.useWatch('status', form)
  const customerType = Form.useWatch('customerType', form)
  const repPersonalCertType = Form.useWatch('repPersonalCertType', form)
  const isEnterprise = useMemo(() => customerType === 'ENTERPRISE', [customerType])

  const getOptionLabel = (option: any, key?: string): string => {
    key = key || 'label'
    const selectedOption = Array.isArray(option) ? option[0] : option

    return selectedOption && typeof selectedOption === 'object' && key in selectedOption ? selectedOption[key] : ''
  }

  const validateRules = useMemo(() => {
    // const id = form.getFieldValue('id')
    // Nếu phải truyền props quá nhiều, hãy truyền thẳng form
    const rules = generalInfoExtraProps({ customerType, id, repPersonalCertType })

    return rules
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [customerType, repPersonalCertType, id])

  const {
    address,
    updateAddress,
    // regionList,
    provinceList,
    wardList,
    streetList,
    loadingProvince,
    loadingWard,
    loadingStreet
  } = useNewLocation('general-info')

  const handleProvinceChange = (value: any, option: any) => {
    updateAddress('provinceId', value)
    form.setFieldsValue({
      wardId: null,
      streetId: null,
      provinceCode: getOptionLabel(option, 'code'),
      province: option
    })
  }

  // Xử lý khi chọn phường/xã
  const handleWardChange = (value: any, option: any) => {
    updateAddress('wardId', value)
    form.setFieldsValue({
      streetId: null,
      wardCode: getOptionLabel(option, 'code'),
      ward: option
    })
  }

  // Xử lý khi chọn phố/đường
  const handleStreetChange = (value: any, option: any) => {
    updateAddress('streetId', value)
    form.setFieldsValue({
      streetCode: getOptionLabel(option, 'code'),
      street: option
    })
  }

  return (
    <PartnerCard title='Thông tin chung & pháp lý'>
      <div className='flex flex-col gap-4'>
        <div className={`grid grid-cols-2 gap-4`}>
          <Field name='customerType' label='Loại hình đối tác' validateRules={validateRules}>
            <Select placeholder='Chọn loại hình đối tác' options={customerTypeOptions} />
          </Field>

          {/* Trạng thái hoạt động */}
          <Form.Item name='status' label='Trạng thái hoạt động' initialValue={STATUS_ENUM.INACTIVE}>
            <div className='flex items-center gap-3 rounded-lg border border-solid border-border-neutral-light px-3 py-2'>
              <Switch
                checked={status === STATUS_ENUM.ACTIVE}
                onChange={checked => {
                  form.setFieldsValue({ status: checked ? STATUS_ENUM.ACTIVE : STATUS_ENUM.INACTIVE })
                }}
              />
              <span className='caption-12-regular text-text-neutral'>
                {statusOptions.find(option => option.value === status)?.label || 'Chưa xác định'}
              </span>
            </div>
          </Form.Item>

          <Field
            name='name'
            className='col-span-2'
            label={isEnterprise ? 'Tên doanh nghiệp' : 'Họ & tên'}
            validateRules={validateRules}
          >
            <Input placeholder={isEnterprise ? 'Nhập tên doanh nghiệp' : 'Nhập họ và tên'} />
          </Field>

          {isEnterprise ? (
            <>
              <Field name='taxCode' label='Mã số thuế' validateRules={validateRules}>
                <Input placeholder='Nhập mã số thuế' />
              </Field>

              <Field name='socialInsuranceNumber' label='Mã BHXH' validateRules={validateRules}>
                <Input placeholder='Nhập mã BHXH' />
              </Field>

              <Field name='website' className='col-span-2' label='Website' validateRules={validateRules}>
                <Input placeholder='Nhập website' />
              </Field>
            </>
          ) : (
            <>
              <Field
                name='repPersonalCertType'
                className='col-span-2'
                label='Loại giấy chứng thực'
                validateRules={validateRules}
              >
                <Select placeholder='Chọn loại giấy chứng thực' options={representativeTypeOptions} />
              </Field>

              <Field
                name='repPersonalCertNumber'
                className='col-span-2'
                label='Số giấy chứng thực'
                validateRules={validateRules}
              >
                <Input placeholder='Nhập số giấy chứng thực' />
              </Field>
            </>
          )}
        </div>

        <PartnerCard title='thông tin địa chỉ kinh doanh' styles={stylesCard}>
          <div className={`grid grid-cols-2 gap-4`}>
            {/* <Form.Item name='nationId' label='Quốc gia' rules={[{ required: true, message: 'Vui lòng chọn quốc gia' }]}>
              <Select placeholder='Chọn quốc gia'>
                <Select.Option value='vietnam'>Việt Nam</Select.Option>
                <Select.Option value='usa'>Hoa Kỳ</Select.Option>
                <Select.Option value='japan'>Nhật Bản</Select.Option>
                <Select.Option value='korea'>Hàn Quốc</Select.Option>
                <Select.Option value='singapore'>Singapore</Select.Option>
              </Select>
            </Form.Item> */}

            <Form.Item name='provinceId' label='Tỉnh/thành' rules={[validateRequire('Tỉnh/thành không được bỏ trống')]}>
              <Select
                placeholder='Chọn tỉnh/thành'
                loading={loadingProvince}
                options={provinceList}
                onChange={handleProvinceChange}
                showSearch
                filterOption={filterOptionTrim}
              />
            </Form.Item>

            <Form.Item name='wardId' label='Phường/xã' rules={[validateRequire('Phường/xã không được bỏ trống')]}>
              <Select
                placeholder='Chọn phường/xã'
                loading={loadingWard}
                options={wardList}
                onChange={handleWardChange}
                disabled={!address.provinceId}
                showSearch
                filterOption={filterOptionTrim}
              />
            </Form.Item>

            <Form.Item
              name='streetId'
              label='Phố/đường'
              className='col-span-2'
              rules={[validateRequire('Phố/đường không được bỏ trống')]}
            >
              <Select
                placeholder='Chọn phố/đường'
                loading={loadingStreet}
                options={streetList}
                onChange={handleStreetChange}
                disabled={!address.wardId}
                showSearch
                filterOption={filterOptionTrim}
              />
            </Form.Item>

            {isEnterprise ? (
              <Field
                name='businessRegistrationAddress'
                label='Địa chỉ đăng ký kinh doanh'
                className='col-span-2'
                validateRules={validateRules}
              >
                <Input placeholder='Nhập địa chỉ đăng ký kinh doanh' />
              </Field>
            ) : (
              <Field
                name='residentAddress'
                label='Địa chỉ thường trú'
                className='col-span-2'
                validateRules={validateRules}
              >
                <Input placeholder='Nhập địa chỉ thường trú' />
              </Field>
            )}
          </div>
        </PartnerCard>
      </div>
    </PartnerCard>
  )
}
