import { useState } from 'react'

import { DatePicker, Form, Input, Select } from 'antd'

import { useMutation } from '@tanstack/react-query'

import { PartnerCard, FileUpload, PartnerModal } from '../../common'
import UploadImage from '@/models/UploadImage'
import { message } from '@/components/notification'
import { DOCUMENT_TYPES } from '../../common/constants'

interface UploadFile extends File {
  fileUuid: string
}

const ContractSection = () => {
  const [open, setOpen] = useState(false)

  const insertMutation = useMutation({ mutationFn: UploadImage.insert })
  const [loading, setLoading] = useState(false)
  const form = Form.useFormInstance()

  const onUploadFile = async (
    file: UploadFile,
    {
      onSuccess,
      onError
    }: {
      onSuccess?: (result: any) => void
      onError?: (error: any) => void
    }
  ) => {
    try {
      setLoading(true)
      const formData = new FormData()

      formData.append('files', file)
      formData.append('fileSize', String(file.size))

      const res = await insertMutation.mutateAsync(formData)

      onSuccess?.(res)

      return {
        ...res,
        fileUuid: file.fileUuid
      }
    } catch (e) {
      onError?.(e)
      throw e
    } finally {
      setLoading(false)
    }
  }

  const onSuccess = (res: any) => {
    const file = res.body[0]
    const contracts = form.getFieldValue('contracts') || []

    form.setFieldsValue({
      contracts: [...contracts, { ...file, fileUuid: file.uuid }]
    })
  }

  const onError = () => {
    message.error('Tải file thất bại')
  }

  const description = (
    <>
      Định dạng ảnh <span className='font-semibold'>(PDF</span>,{' '}
      <span className='font-semibold'>DOCX, XLS, XLSX, CGV)</span> - Dung lượng 1 file{' '}
      <span className='font-semibold'>(&lt;25Mb)</span> - Dung lượng tổng{' '}
      <span className='font-semibold'>(&lt;100Mb)</span>
    </>
  )

  return (
    <>
      <PartnerCard title='Hợp đồng & tài liệu'>
        <FileUpload
          label='Tải file'
          asButton
          onClick={() => {
            setOpen(true)
          }}
          title='Tải file Hợp đồng & Tài liệu lên hoặc kéo thả'
          description={description}
        />
      </PartnerCard>
      <PartnerModal
        open={open}
        setOpen={setOpen}
        controlTitle={{
          titleText: 'Thông tin hợp đồng & tài liệu',
          iconClassName: 'hidden'
        }}
        footer={{
          wrapperClassName: 'flex items-center justify-end gap-2'
        }}
        width={680}
      >
        <div className='flex flex-col gap-3'>
          <div className='grid grid-cols-2 gap-x-4 gap-y-6'>
            <Form.Item label='Tên tài liệu' name='documentName' layout='vertical'>
              <Input placeholder='Nhập tên tài liệu' />
            </Form.Item>
            <Form.Item label='Loại tài liệu' name='documentType' layout='vertical'>
              <Select placeholder='Chọn loại tài liệu'>
                <Select.Option value={DOCUMENT_TYPES.CONTRACT}>Hợp đồng</Select.Option>
                <Select.Option value={DOCUMENT_TYPES.LEGAL}>Pháp lý</Select.Option>
              </Select>
            </Form.Item>
            <Form.Item label='Ngày hết hạn' name='expireAt' layout='vertical' className='col-span-2'>
              <DatePicker placeholder='Chọn ngày hết hạn' format='DD/MM/YYYY' className='w-full' size='middle' />
            </Form.Item>
          </div>
          <FileUpload
            label='Tải file'
            title='Tải file Hợp đồng & Tài liệu lên hoặc kéo thả'
            fileList={[]}
            isPending={loading}
            customRequest={async ({ file }) => {
              await onUploadFile(file as unknown as UploadFile, {
                onSuccess,
                onError
              })
            }}
            description={description}
          />
        </div>
      </PartnerModal>
    </>
  )
}

export { ContractSection }
