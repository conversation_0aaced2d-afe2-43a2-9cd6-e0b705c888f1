import React from 'react'

import type { FC } from 'react'

import { Button } from 'antd'
import classNames from 'classnames'

import type { ButtonType, ButtonVariantType } from 'antd/es/button'

interface PartnerButtonProps extends Omit<React.ComponentProps<typeof Button>, 'type' | 'color' | 'variant'> {
  type?: 'primary' | 'error' | ButtonType
  variant?: 'outlined' | 'solid' | 'text' | 'link'
}

export const PartnerButton: FC<PartnerButtonProps> = ({
  children,
  type = 'primary',
  variant = 'solid',
  className,
  ...rest
}) => {
  switch (variant) {
    case 'outlined':
      const outlinedClass = {
        primary: 'border-primary-blue text-text-primary-blue',
        error: 'border-icon-error-default text-text-error-default'
      }

      return (
        <Button
          variant='outlined'
          color='primary'
          className={classNames(outlinedClass[type as keyof typeof outlinedClass] || '', className)}
          type={type as ButtonType}
          {...rest}
        >
          {children}
        </Button>
      )

    case 'solid':
      const solidClass = {
        primary: 'bg-primary-blue',
        error: 'bg-bg-error-lighter text-text-error-default'
      }

      return (
        <Button
          className={classNames(solidClass[type as keyof typeof solidClass] || '', className)}
          type={type as ButtonType}
          {...rest}
        >
          {children}
        </Button>
      )
    default:
      return (
        <Button
          type={type as ButtonType}
          variant={variant as ButtonVariantType}
          className={classNames('text-text-neutral-strong', className)}
          {...rest}
        >
          {children}
        </Button>
      )
  }
}
