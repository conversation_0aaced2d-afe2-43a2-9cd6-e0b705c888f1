import React from 'react'

import type { FC, PropsWithChildren } from 'react'

import { Button } from 'antd'

interface TitleProps {
  children?: React.ReactNode
  className?: string
  extraButtons?: React.ReactNode
  navigation?: {
    goBack?: () => void
    icon?: React.ReactNode
  }
}

export const PartnerTitle: FC<PropsWithChildren<TitleProps>> = ({
  children,
  className = '',
  extraButtons,
  navigation
}) => {
  return (
    <div
      className={`headline-20-semibold flex w-full items-center justify-between bg-bg-surface p-4 text-text-neutral-strong ${className}`}
      style={{
        borderBottom: '1px solid #02173C09'
      }}
    >
      <div className='flex items-center gap-3'>
        <Button
          type='text'
          icon={navigation?.icon || <i className='onedx-chevron-left size-5' />}
          onClick={() => navigation?.goBack && navigation.goBack()}
        />
        {children}
      </div>
      {extraButtons && <div className='ml-2 inline-block'>{extraButtons}</div>}
    </div>
  )
}
