import React from 'react'

import type { FC } from 'react'

import { Tag } from 'antd'
import classNames from 'classnames'

import { baseColorLight, colors } from '@/utils/colors'

export type PartnerTagColor = 'success' | 'error' | 'warning' | 'primary' | 'default'

export const PartnerTag: FC<{
  children: React.ReactNode
  className?: string
  color?: PartnerTagColor
}> = ({ children, className, color = 'default' }) => {
  const colorMap: Record<
    PartnerTagColor,
    {
      background: string
      color: string
    }
  > = {
    success: {
      background: colors.green200,
      color: colors.green700
    },
    error: {
      background: colors.red200,
      color: colors.red700
    },
    warning: {
      background: colors.yellow200,
      color: colors.yellow700
    },
    default: {
      background: baseColorLight['gray-1'],
      color: baseColorLight['gray-8']
    },
    primary: {
      background: colors.primary200,
      color: colors.primary700
    }
  }

  return (
    <Tag
      className={classNames('border-none rounded-md text-center w-full', className)}
      style={{
        background: colorMap[color].background,
        color: colorMap[color].color
      }}
    >
      {children}
    </Tag>
  )
}
