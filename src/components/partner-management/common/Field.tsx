/**
 * Component sẽ tự động truyền các props cần thiết cho component con (ví dụ: Input) dựa trên validateRules.
 *
 * ## Hướng dẫn sử dụng
 * ```tsx
 * <Field
 *   name="username"
 *   label="Tên đăng nhập"
 *   validateRules={{ required: true, message: '<PERSON>ui lòng nhập tên đăng nhập' }}
 * >
 *   <Input placeholder="Nhập tên đăng nhập" />
 * </Field>
 * ```
 */
import React from 'react'

import { Form } from 'antd'
import type { FormItemProps } from 'antd/es/form'

import { getFormItemProps, getAntdComponentProps } from '../create/validateRules'

type FieldProps = {
  name: any
  label?: React.ReactNode
  validateRules?: any
  children: React.ReactElement
} & Partial<FormItemProps>

/**
 * Components sử dụng validateRules tự khai báo các props cần thiết cho Form.Item và component con.
 */
const Field: React.FC<FieldProps> = ({ name, label, validateRules, children, ...rest }) => {
  const formItemProps = getFormItemProps(name, validateRules)
  const componentProps = getAntdComponentProps(name, validateRules)

  const child = React.isValidElement(children)
    ? React.cloneElement(children, Object.assign({}, componentProps, children.props))
    : children

  return (
    <Form.Item name={name} label={label} {...formItemProps} {...rest}>
      {child}
    </Form.Item>
  )
}

export default Field
