import React from 'react'

import { Empty } from 'antd'

import type { TableLocale } from 'antd/es/table/interface'

import moment from 'moment'

import { baseColorLight } from '@/utils/colors'

/**
 * UI Constants Only
 */

export const localeTable: TableLocale = {
  emptyText: <Empty description={<span>Không có dữ liệu</span>} />
}

export const DEFAULT_FILTER = {
  capabilities: null,
  provinceIds: null,
  status: -1,
  taxCodes: null,
  repPersonalCertNumbers: null
}

export const STATUS = [
  { label: 'Tất cả', value: -1 },
  { label: 'Hoạt động', value: 1 },
  { label: 'Không hoạt động', value: 0 }
]

export const CAPABILITIES = [
  { label: 'Cung cấp thiết bị', value: 'EQUIPMENT' },
  { label: 'Dịch vụ lắp đặt', value: 'INSTALLATION_SERVICE' },
  { label: 'Dịch vụ bảo trì, sửa chữa', value: 'MAINTENANCE_SERVICE' },
  { label: 'Dịch vụ vận chuyển', value: 'SHIPPING_SERVICE' },
  { label: 'Phát triển phần mềm, ứng dụng', value: 'SOFTWARE' },
  { label: 'Tư vấn giải pháp IoT', value: 'IOT_CONSULTING' }
]

export const stylesCard = {
  header: {
    backgroundColor: baseColorLight['gray-alpha-12'],
    borderTopLeftRadius: '8px',
    borderTopRightRadius: '8px'
  },
  body: {
    backgroundColor: baseColorLight['gray-alpha-12'],
    borderRadius: '0px 0px 8px 8px'
  }
}

export const STATUS_OPTIONS = [
  { label: 'Đăng ký mới', value: 'NEW' },
  { label: 'Hoàn thiện hồ sơ', value: 'PROFILE_COMPLETED' },
  { label: 'Đã duyệt', value: 'APPROVED' },
  { label: 'Từ chối', value: 'REJECTED' }
]

export const PARTNER_TYPE_OPTIONS = [
  { label: 'Doanh nghiệp', value: 'ENTERPRISE' },
  { label: 'Cá nhân', value: 'PERSONAL' }
]

export const statusToUI: Record<string, { text: string; color: string; textColor: string }> = {
  NEW: {
    text: 'Đăng ký mới',
    color: '#CDE4FE',
    textColor: '#2A6AEB'
  },
  PROFILE_COMPLETED: {
    text: 'Hoàn thiện hồ sơ',
    color: '#FFF6B3',
    textColor: '#E28800'
  },
  APPROVED: {
    text: 'Đã duyệt',
    color: '#D2F9E2',
    textColor: '#07945F'
  },
  REJECTED: {
    text: 'Từ chối',
    color: '#FFE1E0',
    textColor: '#D82D2A'
  }
}

export const activeStatusToUI: Record<string, { text: string; color: string; textColor: string }> = {
  true: {
    text: 'Hoạt động',
    color: '#D2F9E2',
    textColor: '#07945F'
  },
  false: {
    text: 'Không hoạt động',
    color: '#FFE1E0',
    textColor: '#D82D2A'
  }
}

export const partnerCheckboxOptions = [
  { label: 'Mã đối tác', value: 'code', status: 'disabled' },
  { label: 'Tên đối tác', value: 'name', status: 'disabled' },
  { label: 'Loại hình đối tác', value: 'customerType', status: 'disabled' },
  { label: 'Năng lực cung cấp', value: 'capabilityLst', status: 'disabled' },
  { label: 'Địa bàn hoạt động', value: 'operatingAreaLst', status: 'disabled' },
  { label: 'Trạng thái phê duyệt', value: 'state', status: 'disabled' },
  { label: 'Trạng thái hoạt động', value: 'status', status: 'disabled' },
  { label: 'Tỉnh thành', value: 'provinceName', status: 'disabled' },
  { label: 'Số chứng thực', value: 'insuranceNumber' },
  { label: 'Email', value: 'email' },
  { label: 'Số điện thoại', value: 'phoneNumber' },
  { label: 'Địa chỉ', value: 'address' },
  { label: 'Người tạo', value: 'createdBy' },
  { label: 'Thời gian cập nhật', value: 'modifiedAt' }
]

export const stylesSubCard = {
  header: {
    background: 'linear-gradient(0deg, rgba(2, 23, 60, 0.02) 0%, rgba(2, 23, 60, 0.02) 100%), #F2F4F9',
    borderTopLeftRadius: '8px',
    borderTopRightRadius: '8px'
  },
  body: {
    background: 'linear-gradient(0deg, rgba(2, 23, 60, 0.02) 0%, rgba(2, 23, 60, 0.02) 100%), #F2F4F9',
    borderRadius: '0px 0px 8px 8px'
  }
}

export const capabilityMapping: Record<string, string> = {
  EQUIPMENT: 'Cung cấp thiết bị',
  INSTALLATION_SERVICE: 'Dịch vụ lắp đặt',
  MAINTENANCE_SERVICE: 'Dịch vụ bảo trì, sửa chữa',
  SHIPPING_SERVICE: 'Dịch vụ vận chuyển',
  SOFTWARE: 'Phát triển phần mềm, ứng dụng',
  IOT_CONSULTING: 'Tư vấn giải pháp IoT'
}

// Constants
export const FILE_ICONS = {
  pdf: <i className='onedx-pdf size-6' />,
  doc: <i className='onedx-word size-6' />,
  docx: <i className='onedx-word size-6' />,
  default: <i className='onedx-excel size-6' />
} as const

export const DOCUMENT_TYPES = {
  CONTRACT: 'Hợp đồng',
  LEGAL: 'Pháp lý'
} as const

export const STATUS_COLORS = {
  active: 'success',
  expired: 'error'
} as const

export const STATUS_LABELS = {
  active: 'Hiệu lực',
  expired: 'Hết hiệu lực'
} as const

export const SELECT_OPTIONS = {
  documentType: [
    { label: 'Hợp đồng', value: 'CONTRACT' },
    { label: 'Pháp lý', value: 'LEGAL' }
  ],
  status: [
    { label: 'Hiệu lực', value: 1 },
    { label: 'Hết hiệu lực', value: 0 }
  ]
} as const

export const DEFAULT_SEARCH_PARAMS = {
  page: 0,
  size: 10,
  startDate: '',
  endDate: '',
  status: -1,
  type: -1
} as const

export const DATE_FORMAT = 'DD/MM/YYYY'
export const DEFAULT_START_TIME = moment().subtract(6, 'months')
export const DEFAULT_END_TIME = moment()

export const DEFAULT_START_DATE = '01/01/1970'
export const DEFAULT_END_DATE = moment().format(DATE_FORMAT)
export const DATE_TIME = 'DDMMYYYY HHSSMM'

export const MONTH_TYPE = {
  CURRENT_TIME: -1,
  THIS_MONTH: 0,
  LAST_MONTH: 1,
  TWO_MONTH: 2,
  THREE_MONTH: 3,
  TWELVE_MONTH: 12
}

export const defaultParamFilterTable = {
  top: 10,
  startTime: moment().subtract(90, 'days').format('DD/MM/YYYY'),
  endTime: moment().format('DD/MM/YYYY'),
  lstCustomerType: ['ALL'],
  serviceType: -1,
  lstServiceId: [-1],
  lstProvinceId: [-1],
  reportCycle: 1
}

export const topOptionFilterTable = [
  {
    label: 'Top 5',
    value: 5
  },
  {
    label: 'Top 10',
    value: 10
  },
  {
    label: 'Top 20',
    value: 20
  },
  {
    label: 'Top 50',
    value: 50
  },
  {
    label: 'Tất cả',
    value: -1
  }
]

export const CURRENCY_THRESHOLDS = {
  BILLION: 1000000000,
  MILLION: 1000000,
  THOUSAND: 1000
}

export const baseColors = [
  // Blue
  '#2A6AEB',
  '#4A80F0',
  '#6A96F5',
  '#1F55C8',
  '#143C91',
  '#5EC2F5',
  '#8AD8F7',
  '#B5E7FB',

  // Aqua / Cyan
  '#1CA4D9',
  '#127FAF',
  '#2AEBD6',
  '#5FF0DF',
  '#8FF5E7',
  '#19C2B4',
  '#0F8A81',
  '#4AEBA6',
  '#78F0BD',
  '#A4F5D5',

  // Green
  '#2FC272',
  '#1D8A50',
  '#76EB5A',
  '#A0F07E',
  '#C6F5A8',
  '#D4EB5C',
  '#B2C947',

  // Yellow
  '#F2E45C',
  '#F7EE8A',
  '#FBF5B8',
  '#E0C22A',
  '#A58A1D',

  // Orange
  '#F2B84A',
  '#F7CE78',
  '#FADFA8',
  '#EB8C2A',
  '#B25F1D',

  // Red
  '#EB5A2A',
  '#F28066',
  '#F5A896',
  '#D9411F',
  '#A32E14',

  // Pink / Magenta
  '#EB2A5C',
  '#F2668F',
  '#F5A1BF',
  '#D91C85',
  '#A11463',

  // Purple / Violet
  '#B92AEB',
  '#CF66F2',
  '#E5A8F7',
  '#7A1F9C',
  '#4D1366'
]

export const statusTextMap: Record<string, string> = {
  NEW: 'Đăng ký mới',
  PROFILE_COMPLETED: 'Hoàn thiện hồ sơ',
  APPROVED: 'Đã duyệt',
  REJECTED: 'Từ chối'
}

export const statusColorMap: Record<string, 'warning' | 'primary' | 'success' | 'error'> = {
  NEW: 'warning',
  PROFILE_COMPLETED: 'primary',
  APPROVED: 'success',
  REJECTED: 'error'
}

export const timeFilters = [
  { key: 0, label: 'Ngày' },
  { key: 1, label: 'Tháng' },
  { key: 2, label: 'Năm' }
]

export const topFilters = [
  { key: -1, label: 'Tất cả' },
  { key: 50, label: 'Top 50' },
  { key: 20, label: 'Top 20' },
  { key: 10, label: 'Top 10' },
  { key: 5, label: 'Top 5' }
]

export const SECTION_TITLES = {
  GENERAL_LEGAL: 'Thông tin chung & pháp lý',
  CONTACT: 'Thông tin liên hệ',
  REPRESENTATIVE: 'Thông tin liên hệ', // Seems like duplicate, might need clarification
  ACTIVITY: 'Thông tin hoạt động',
  CAPACITY: 'Năng lực & địa bàn',
  FINANCE: 'Thông tin tài chính'
} as const

export const CUSTOMER_TYPES = {
  PERSONAL: 'PERSONAL',
  ENTERPRISE: 'ENTERPRISE'
} as const

export const GENDER_LABELS = {
  MALE: 'Nam',
  FEMALE: 'Nữ'
} as const

export const STATUS_LABEL = {
  ACTIVE: 'Đang bật',
  INACTIVE: 'Đang tắt'
} as const

export const SPECIAL_FORMATTED_TIME = '02/2025'
export const SPECIAL_LABEL_TIME = '2025/02'

export const REVENUE_DATE_FORMAT = 'MM/YYYY'

export const PARTNER_STATUS = {
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED',
  PROFILE_COMPLETED: 'PROFILE_COMPLETED'
} as const

export const FORGOT_PASSWORD_PATH: Record<'IOT' | 'DEV' | 'SME', string> = {
  IOT: '/iot-portal/forgot-password',
  DEV: '/partner-portal/forgot-password',
  SME: '/sme-portal/forgot-password'
}

export const CHART_TYPE = {
  GROWTH: 'growth',
  CUMULATIVE: 'cumulative'
}

export const defaultParamPreview = {
  lstProvinceId: [-1],
  serviceType: -1,
  lstServiceProductUniqueId: [-1],
  lstCustomerType: ['ALL'],
  startTime: moment().subtract(90, 'days').format(DATE_FORMAT),
  endTime: moment().format(DATE_FORMAT)
}

// Thêm các option cho select
export const serviceTypeOptions = [
  { label: 'Tất cả', value: -1 },
  { label: 'ON', value: 0 },
  { label: 'OS', value: 1 },
  { label: 'COMBO', value: 2 }
]

export const customerTypeOptions = [
  { label: 'Cá nhân', value: 'CN' },
  { label: 'Hộ kinh doanh', value: 'HKD' },
  { label: 'Doanh nghiệp', value: 'KHDN' }
]
