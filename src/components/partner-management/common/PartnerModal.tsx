import React from 'react'

import classNames from 'classnames'

import ModalConfirm from '@/components/subscription/modal/ModalConfirm'

import type { ModalConfirmProps } from '@/components/subscription/modal/ModalConfirm'

import { PartnerButton } from './PartnerButton'

interface PartnerButtonProps {
  type?: 'primary' | 'error'
  variant?: 'outlined' | 'solid' | 'text' | 'link'
}

export const PartnerModal = ({
  children,
  open,
  setOpen,
  type = 'primary',
  variant,
  width,
  icon,
  controlTitle,
  footer
}: {
  children: React.ReactNode
  open: boolean
  setOpen: (open: boolean) => void
  type?: 'primary' | 'error'
  variant?: PartnerButtonProps['variant']
  width?: number
  icon?: React.ReactNode
  controlTitle?: ModalConfirmProps['controlTitle']
  footer?: {
    render?: React.ReactNode
    wrapperClassName?: string
    okText?: string
    okFunction?: () => void
    cancelText?: string
    cancelFunction?: () => void
  }
}) => {
  return (
    <ModalConfirm
      open={open}
      setOpen={setOpen}
      width={width}
      icon={
        icon ||
        (type === 'error' ? <i className='onedx-check-circle size-6' /> : <i className='onedx-refresh size-6' />)
      }
      controlTitle={{
        titleText: type === 'error' ? 'Lỗi' : 'Thông báo',
        iconClassName: classNames({
          'bg-bg-error-lighter text-text-error-default': type === 'error',
          'bg-[#CDE4FE] text-icon-info-default': type === 'primary'
        }),
        ...controlTitle
      }}
      controlFooter={{
        render: (
          <div className={footer?.wrapperClassName || 'grid grid-cols-2 items-center justify-center gap-2'}>
            {footer?.render || (
              <>
                <PartnerButton
                  variant='outlined'
                  type='primary'
                  onClick={() => {
                    footer?.cancelFunction && footer.cancelFunction()
                    setOpen(false)
                  }}
                >
                  {footer?.cancelText || 'Hủy'}
                </PartnerButton>
                <PartnerButton
                  type={type}
                  variant={variant}
                  onClick={() => {
                    footer?.okFunction && footer.okFunction()
                  }}
                >
                  {footer?.okText || 'Xác nhận'}
                </PartnerButton>
              </>
            )}
          </div>
        )
      }}
    >
      {children}
    </ModalConfirm>
  )
}
