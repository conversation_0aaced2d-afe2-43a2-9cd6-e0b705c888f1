import React from 'react'

import type { FC, PropsWithChildren } from 'react'

import { CustomCollapse } from '@/components/custom-field/common/CustomCollapse'

import { PartnerHeader } from './PartnerHeader'

type PartnerCardProps = Omit<React.ComponentProps<typeof CustomCollapse>, 'label' | 'visibleToolTip'> & {
  title: string | React.ReactNode
  dotColor?: 'default' | 'red' | 'green' | 'blue'
  dotType?: 'default' | 'medium' | 'small'
  isUseRawTitle?: boolean
}

export const PartnerCard: FC<PropsWithChildren<PartnerCardProps>> = ({
  children,
  title,
  styles,
  dotColor,
  dotType,
  isUseRawTitle,
  ...rest
}) => {
  return (
    <CustomCollapse
      label={isUseRawTitle ? title : <PartnerHeader title={title} color={dotColor} type={dotType} />}
      visibleToolTip={false}
      className='text-icon-neutral-strong'
      styles={{
        header: {
          backgroundColor: '#fff',
          borderBottom: '1px solid',
          borderColor: '#02173C09',
          borderRadius: 0,
          ...styles?.header
        },
        body: {
          backgroundColor: '#fff',
          borderRadius: 0,
          ...styles?.body
        }
      }}
      {...rest}
    >
      {children}
    </CustomCollapse>
  )
}
