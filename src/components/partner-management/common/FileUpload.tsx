import type { <PERSON>actN<PERSON>, MouseEvent } from 'react'

import Dragger from 'antd/es/upload/Dragger'
import type { DraggerProps } from 'antd/es/upload'
import { Spin } from 'antd'

interface FileUploadProps extends Omit<DraggerProps, 'onClick'> {
  label?: string
  showLabel?: boolean
  showRedDot?: boolean
  title?: string
  description?: ReactNode
  iconName?: string
  iconBgColor?: string
  isPending?: boolean
  // Button mode props
  asButton?: boolean
  onClick?: (e: MouseEvent<HTMLDivElement>) => void
}

/**
 * Giao diện tải file
 * @note Đường dẫn giao diện
 * @link http://wiki.vnpt-technology.vn/pages/viewpage.action?pageId=183763797 -> Quản lý Hợp đồng & Tài liệu
 */
const FileUpload = ({
  label = 'Tải file',
  showLabel = true,
  showRedDot = true,
  title = 'Tải file Hợp đồng & <PERSON>ài liệu lên hoặc kéo thả',
  description = 'Định dạng ảnh (PDF, DOCX, XLS, XLSX, CGV) - Dung lượng 1 file (<25Mb) - Dung lượng tổng (<100Mb)',
  iconName = 'onedx-pdf',
  iconBgColor = 'bg-[#e6f1fe]',
  className,
  isPending = false,
  asButton = false,
  onClick,
  ...props
}: FileUploadProps) => {
  const handleClick = (e: MouseEvent<HTMLDivElement>) => {
    if (asButton && onClick) {
      e.preventDefault()
      e.stopPropagation()
      onClick(e)
    }
  }

  const content = (
    <div className='flex items-center gap-4'>
      {/* Icon */}
      <div className={`shrink-0 rounded-xl p-2.5 ${iconBgColor}`}>
        <i className={`${iconName} size-8`} />
      </div>

      {/* Text content */}
      <div className='flex flex-1 flex-col gap-1 text-left'>
        <p className='body-14-medium m-0 text-text-neutral-strong'>{title}</p>
        <p className='caption-12-regular m-0 text-text-neutral-medium'>{description}</p>
      </div>
    </div>
  )

  return (
    <Spin spinning={isPending}>
      <div className='flex flex-col gap-2.5 rounded-lg bg-bg-canvas p-3'>
        {/* Label with red dot indicator */}
        {showLabel && (
          <div className='flex items-center gap-1'>
            {showRedDot && <div className='size-1 rounded-full bg-icon-error-default' />}
            <span className='body-14-medium text-text-neutral-strong'>{label}</span>
          </div>
        )}

        {/* Upload area */}
        <div className='relative'>
          {asButton ? (
            <div
              className={`cursor-pointer rounded-2xl bg-white p-4 transition-all duration-200 hover:shadow-sm ${className || ''}`}
              style={{
                border: 'none',
                background: '#ffffff'
              }}
              onClick={handleClick}
            >
              {content}
            </div>
          ) : (
            <Dragger
              className={className}
              style={{
                border: 'none',
                background: '#ffffff',
                borderRadius: '12px'
              }}
              {...props}
            >
              {content}
            </Dragger>
          )}

          {/* Blue dashed border overlay */}
          <div className='pointer-events-none absolute inset-0 rounded-2xl border border-dashed border-primary-blue' />
        </div>
      </div>
    </Spin>
  )
}

export { FileUpload }
export type { FileUploadProps }
