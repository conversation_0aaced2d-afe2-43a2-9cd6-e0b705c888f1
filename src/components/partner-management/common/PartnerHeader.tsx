import React from 'react'

import classNames from 'classnames'

export const PartnerHeader = ({
  title,
  rootClassname,
  iconClassname,
  textClassname,
  color = 'default',
  type = 'default'
}: {
  title: string | React.ReactNode
  rootClassname?: HTMLDivElement['className']
  iconClassname?: HTMLDivElement['className']
  textClassname?: HTMLDivElement['className']
  color?: 'default' | 'red' | 'green' | 'blue'
  type?: 'default' | 'medium' | 'small'
}) => {
  const colorClass = classNames({
    'bg-icon-warning-default': color === 'default',
    'bg-icon-error-default': color === 'red',
    'bg-icon-success-default': color === 'green',
    'bg-icon-info-default': color === 'blue'
  })

  const typeClass = classNames({
    'h-1 w-1 rounded-full': type === 'small',
    'h-2 w-2 rounded-full': type === 'medium',
    'h-4 w-1 rounded-xs': type === 'default'
  })

  const rootIconClassname = classNames(colorClass, typeClass, iconClassname)

  return (
    <div className={classNames('inline-flex items-center justify-start gap-2', rootClassname)}>
      <div className={rootIconClassname} />
      <div className={classNames('headline-16-medium text-text-neutral-strong', textClassname)}>{title}</div>
    </div>
  )
}
