'use client'

import React, { useEffect, useState } from 'react'

import { Button, Empty, Pagination, Select } from 'antd'
import { styled } from 'styled-components'

import { useNewLocation } from '@/hooks/useNewLocation'

import { usePartnersList } from '@/hooks/custom-field/usePartnersList'
import {
  STATUS_OPTIONS,
  PARTNER_TYPE_OPTIONS,
  DEFAULT_FILTER,
  STATUS,
  CAPABILITIES
} from '@components/partner-management/common/constants'
import { filterOption } from '@/utils/string'

import SettingInput from '@components/filter/SettingInput'
import TableCheckboxEdit from '@views/product-catalog/common/TableCheckboxEdit'
import { StyledTable } from '@views/custom-field/editor/sections/create-service/price-list'
import { EmptySearch } from '@components/inventory/common/EmptySearch'
import { ModalNotification } from '@components/modal'

import { ModalOperatingAreaLst } from './ModalOperatingAreaLst'
import SelectFilter from '@views/product-catalog/common/SelectFilter'

const CustomMultiSelect = styled(Select)`
  .ant-select-selection-wrap {
    block-size: 100%;
  }
`

export const PartnersList: React.FC = () => {
  const {
    checked,
    setChecked,
    checkBoxOptions,
    filteredColumns,
    handleSearch,
    handleFilterChange,
    handleUpdateFilterChange,
    rowSelection,
    content,
    pagination,
    pageSizeOptions,
    setPageSize,
    selectedRowKeys,
    handleRemoveService,
    visibleModal,
    setVisibleModal,
    infoModal,
    selectedColumns,
    formatCount,
    setSelectedColumns,
    partnerCheckboxOptions,
    openOperatingAreaLst,
    setOpenOperatingAreaLst,
    taxCodeList,
    repPersonalCertNumberList
  } = usePartnersList()

  const { provinceList } = useNewLocation('register-dev')

  const [filterParam, setFilterParam] = useState({})

  useEffect(() => {
    handleUpdateFilterChange(filterParam)

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filterParam])

  const listFilter: any = [
    {
      name: 'capabilities',
      label: 'Năng lực cung cấp',
      options: CAPABILITIES,
      multiple: true,
      canSearch: true,
      placeHolder: 'Tất cả'
    },
    {
      name: 'provinceIds',
      label: 'Tỉnh thành',
      options: provinceList || [],
      multiple: true,
      canSearch: true,
      placeHolder: 'Tất cả'
    },
    {
      name: 'status',
      label: 'Trạng thái hoạt động',
      options: STATUS
    },
    {
      name: 'taxCodes',
      label: 'Mã số thuế',
      options: taxCodeList || [],
      multiple: true,
      canSearch: true,
      placeHolder: 'Tất cả'
    },
    {
      name: 'repPersonalCertNumbers',
      label: 'Số chứng thực',
      options: repPersonalCertNumberList || [],
      multiple: true,
      canSearch: true,
      placeHolder: 'Tất cả'
    }
  ]

  return (
    <div className='w-full gap-3 bg-white p-4'>
      <div className='mb-4 flex items-center gap-x-2'>
        <SettingInput
          placeholder='Tìm kiếm theo mã đối tác hoặc tên đối tác'
          styles={{ width: '100%' }}
          placeholderColor='#5E7699'
          checked={checked}
          setChecked={setChecked}
          onChange={e => handleSearch(e)}
          checkBoxOptions={checkBoxOptions}
        />

        <CustomMultiSelect
          className='h-[40px] min-w-[230px]'
          placeholder={<span className='items-center justify-center text-gray-6'>Loại hình đối tác</span>}
          options={PARTNER_TYPE_OPTIONS}
          maxTagCount={1}
          onChange={value => handleFilterChange('customerType', value)}
          allowClear={true}
        />

        <CustomMultiSelect
          className='h-[40px] min-w-[230px]'
          placeholder={<span className='items-center justify-center text-gray-6'>Trạng thái phê duyệt</span>}
          options={STATUS_OPTIONS}
          maxTagCount={1}
          mode='multiple'
          onChange={value => handleFilterChange('state', value)}
          allowClear={true}
          filterOption={filterOption}
          notFoundContent={<Empty description='Không có dữ liệu' image={Empty.PRESENTED_IMAGE_SIMPLE} />}
        />

        <SelectFilter
          customStyle={'h-[40px]'}
          setFilterParams={setFilterParam}
          filterOptions={listFilter}
          filterParams={filterParam}
          defaultFilter={DEFAULT_FILTER}
        />

        <TableCheckboxEdit
          selectedColumns={selectedColumns}
          setSelectedColumns={setSelectedColumns}
          checkboxOptions={partnerCheckboxOptions}
          height={'40px'}
        />
      </div>

      <div className='w-full'>
        <StyledTable<any>
          rowSelection={{ type: 'checkbox', ...rowSelection }}
          className='custom-pricing-table custom-bg-hover'
          columns={filteredColumns}
          dataSource={content || []}
          rowKey='id'
          pagination={false}
          scroll={{ x: '1000px' }}
          locale={{
            emptyText: (
              <div className='flex items-center justify-center py-20'>
                <EmptySearch title='Danh sách trống' description='Không có dữ liệu đối tác' />
              </div>
            )
          }}
        />

        <div className='mt-[10px] flex w-full justify-between'>
          <div className='flex gap-4'>
            <div className='pt-2'>Đã chọn: {formatCount(selectedRowKeys.length ?? 0)}</div>
            <Button
              onClick={() => handleRemoveService(selectedRowKeys.map((item: any) => item.id))}
              className='border-primary bg-white text-primary'
              disabled={selectedRowKeys.length === 0}
            >
              Xóa <i className='onedx-delete-traffic size-4' />
            </Button>
          </div>
          <Pagination {...pagination} showSizeChanger={false} />
          <Select defaultValue={10} options={pageSizeOptions} onChange={value => setPageSize(value)} />
        </div>
      </div>

      <ModalOperatingAreaLst
        visibleModal={openOperatingAreaLst?.open}
        setVisibleModal={setOpenOperatingAreaLst}
        dataOperatingAreaLst={openOperatingAreaLst?.data}
      />
      <ModalNotification visibleModal={visibleModal} setVisibleModal={setVisibleModal} infoModal={infoModal} />
    </div>
  )
}

export default PartnersList
