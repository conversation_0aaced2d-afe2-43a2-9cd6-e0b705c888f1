'use client'

import React from 'react'

import { But<PERSON>, Modal } from 'antd'

import { StyledTable } from '@views/custom-field/editor/sections/create-service/price-list'

import { EmptySearch } from '@/components/inventory/common/EmptySearch'

export const ModalOperatingAreaLst = ({ visibleModal, setVisibleModal, dataOperatingAreaLst }: any) => {
  const columns = [
    {
      title: 'Địa bàn',
      dataIndex: 'key',
      key: 'index',
      render: (_: any, __: any, index: number) => index + 1
    },
    {
      title: 'Tỉnh/thành',
      dataIndex: 'provinceName',
      key: 'provinceName'
    },
    {
      title: 'Phường/xã',
      dataIndex: 'wardName',
      key: 'wardName'
    },
    {
      title: 'Phố/đường',
      dataIndex: 'streetName',
      key: 'streetName'
    }
  ]

  return (
    <Modal
      centered
      open={visibleModal}
      onCancel={() => {
        setVisibleModal(false)
      }}
      footer={null}
      closable={false}
      maskClosable={false}
      width={520}
      title={
        <div className='flex items-center justify-between pb-3'>
          <div className='flex items-center justify-center gap-4'>
            <div className='headline-16-semibold pb-2 sm:text-sm'>Địa bàn hoạt động</div>
          </div>
          {/*icon close*/}
          <i
            className='onedx-close-icon size-6 cursor-pointer text-text-neutral-medium'
            onClick={() => setVisibleModal({ open: false, data: [] })}
          />
        </div>
      }
    >
      <div>
        <StyledTable<any>
          className='custom-pricing-table custom-bg-hover'
          columns={columns}
          dataSource={dataOperatingAreaLst}
          pagination={false}
          locale={{
            emptyText: (
              <div className='flex items-center justify-center py-20'>
                <EmptySearch title='Danh sách trống' description='Không có dữ liệu đối tác' />
              </div>
            )
          }}
        />
      </div>
      <div style={{ textAlign: 'right', marginTop: '24px' }}>
        <Button
          type='default'
          onClick={() => setVisibleModal({ open: false, data: [] })}
          style={{
            marginTop: 16,
            borderRadius: 6,
            borderColor: '#2A6AEB',
            color: '#2A6AEB'
          }}
        >
          Đóng
        </Button>
      </div>
    </Modal>
  )
}
