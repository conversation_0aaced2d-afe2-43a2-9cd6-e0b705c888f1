import React, { useEffect, useState } from 'react'

import Image from 'next/image'

import { usePathname } from 'next/navigation'

import type { MenuProps } from 'antd'

import { Avatar, Divider, Dropdown } from 'antd'

import classNames from 'classnames'

import { SearchOutlined } from '@ant-design/icons'

import { useScrollDirection, useUpdateCart, useUser } from '@/hooks'
import { useAffiliateLog } from '@/hooks/useAffiliateLog'

// region import switch acc
import type { NavbarStatusDesktopProps } from '@/types/page-builder'
import { handleSrcImg } from '@/utils/string'

import { verticalLayoutClasses } from '@layouts/utils/layoutClasses'

// Import the correct VNPT logo
import logo from '@public/assets/images/logo.svg'

// eslint-disable-next-line import/order

import NavbarLogoutModal from './nav-bar-items/NavbarLogoutModal'

// endregion

// region Navbar Status
export const NavbarStatusDesktopDevPortal: React.FC<NavbarStatusDesktopProps> = ({ bgColorHeader }) => {
  const { user, isLoggedIn } = useUser()
  const { affiliateCookieExpireTime, isInactiveLink } = useAffiliateLog()
  const pathname = usePathname()
  const isEditorScreen = pathname.includes('/page-builder/editor')

  // state logout confirm
  const [logoutConfirmOpen, setLogoutConfirmOpen] = useState(false)

  const scrollToGuideSection = (element: string) => {
    const guideSection = document.getElementById(element)

    if (guideSection) {
      guideSection.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      })
    }
  }

  // Khai báo đối tượng khách hàng - hàm lấy id giỏ hàng - merge giỏ hàng
  const { MERGE_CART } = useUpdateCart()

  useEffect(() => {
    if (isLoggedIn) {
      MERGE_CART()
    }
  }, [MERGE_CART, isLoggedIn])

  useEffect(() => {
    if (!isInactiveLink) {
      const tokenExpireTime = (1 / 24) * 10

      const cookieExpireTime =
        isLoggedIn && affiliateCookieExpireTime === 0 ? tokenExpireTime : affiliateCookieExpireTime

      const existingScript = document.getElementById('affiliate-script')

      if (existingScript) {
        existingScript.remove()
      }

      const script = document.createElement('script')

      script.id = 'affiliate-script'
      script.src = '/assets/affiliate/affiliate-tracking.js'
      script.async = true
      script.setAttribute('data-cookie-expire-time', cookieExpireTime.toString())
      document.body.appendChild(script)

      return () => {
        document.getElementById('affiliate-script')?.remove()
      }
    }
  }, [isLoggedIn, affiliateCookieExpireTime, isInactiveLink])

  // region Menu
  const items: MenuProps['items'] = [
    // region Đăng xuất
    {
      key: '4',
      label: (
        <div
          className='flex items-center gap-2 p-1 text-body-14 font-medium'
          onClick={() => {
            setLogoutConfirmOpen(true)
          }}
        >
          <i className='onedx-sign-out size-5' />
          <div className='w-36 text-body-14 font-medium'>Đăng xuất</div>
        </div>
      )
    }
    // endregion
  ]
  // endregion

  // region menu header
  const menuItems = [
    { element: 'gioi-thieu-section', label: 'Giới Thiệu' },
    { element: 'huong-dan-section', label: 'Hướng Dẫn' },
    { element: 'loi-ich-section', label: 'Lợi Ích' },
    { element: 'hoa-hong-section', label: 'Hoa Hồng' },
    { element: 'faq-section', label: 'FAQ' },
    { element: 'lien-he-section', label: 'Liên hệ' }
  ]
  // endregion

  // region return
  return (
    <div
      className='flex h-16 w-full items-center'
      style={{ backgroundColor: bgColorHeader ? bgColorHeader : '#FFFFFF' }}
    >
      {/* Logo section - positioned at left */}
      <div className='ml-[75px] shrink-0'>
        <div className='flex items-center'>
          <Image
            src={logo}
            alt='VNPT'
            height={33}
            className='mr-1 cursor-pointer'
            onClick={() => (window.location.href = '/partner-portal/home')}
          />
          <Divider type='vertical' className='h-[14px] bg-[#03307D]' />
          <div className='ml-1 mt-1.5 text-xs font-medium text-[#0961B6]'>
            <div className='h-[13px]'>Become a</div>
            <div>Partner</div>
          </div>
        </div>
      </div>

      {/* Navigation Links - centered in the page */}
      <div
        className={classNames(isEditorScreen && 'pointer-events-none', 'flex flex-grow justify-center items-center')}
        onClick={e => {
          if (isEditorScreen) {
            e.preventDefault()
            e.stopPropagation()
          }
        }}
      >
        <div className='flex gap-6 px-5 py-2'>
          {menuItems.map((item, index) => (
            <div key={index}>
              <button
                onClick={() => scrollToGuideSection(item?.element)}
                className='cursor-pointer bg-white text-sm font-medium text-gray-700'
              >
                {item.label}
              </button>
            </div>
          ))}
        </div>
      </div>

      {/* Right Side Menu */}
      <div className='mr-[75px] flex shrink-0 items-center'>
        <SearchOutlined className='mr-[20px] hidden cursor-pointer' />
        <Divider type='vertical' className='mx-0 hidden h-[22px] bg-[#6B7585]' />
        {/* Hiển thị avtar/button auth khi login/logout */}
        {user?.id ? (
          <div className='relative ml-[20px] flex items-center gap-3'>
            <span className='text-sm font-medium text-gray-700'>{user.name}</span>
            <Dropdown trigger={['click']} menu={{ items }} placement='bottom' className='cursor-pointer'>
              <div className='text-body-14 font-medium'>
                {user?.avatar ? (
                  <Avatar src={handleSrcImg(user?.avatar)} className='bg-primary object-cover' />
                ) : (
                  <Avatar className='bg-primary object-cover'>{user?.name?.length ? user?.name[0] : ''}</Avatar>
                )}
              </div>
            </Dropdown>
          </div>
        ) : (
          <>
            {/* Đăng nhập button */}
            <div
              onClick={() => (window.location.href = '/partner-portal/login')}
              className='cursor-pointer px-[20px] py-[10px] text-sm font-medium text-gray-700'
            >
              Đăng nhập
            </div>
            {/* Đăng ký button */}
            <div
              onClick={() => (window.location.href = '/partner-portal/register')}
              className='cursor-pointer bg-[#0961B6] px-[20px] py-[10px] text-sm font-medium text-white'
            >
              Đăng ký
            </div>
          </>
        )}
      </div>

      {/* Modal Đăng xuất */}
      <NavbarLogoutModal openModal={logoutConfirmOpen} setOpenModal={setLogoutConfirmOpen} isIotPortal />
    </div>
  )
}
// endregion

// region Navbar Desktop
export const NavbarDesktopDevPortal = () => {
  const headerHeight = 64 // Fixed height to match design
  const { down } = useScrollDirection(headerHeight)

  return (
    <>
      <div style={{ height: headerHeight }} />
      <div
        className={`${classNames(verticalLayoutClasses.navbarContent)} fixed top-0 z-[999] h-16 w-screen bg-white`}
        style={{ transform: down ? 'translateY(-100%)' : 'translateY(0%)', transition: 'transform 0.25s' }}
      >
        <NavbarStatusDesktopDevPortal />
      </div>
    </>
  )
}
// endregion
