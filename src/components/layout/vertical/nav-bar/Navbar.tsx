'use client'

// Component Imports
import { usePathname } from 'next/navigation'

import { useResponsive } from '@/hooks'
import { NavbarMobile } from '@components/layout/vertical/nav-bar/NavbarMobile'
import { NavbarDesktop } from './NavbarDesktop'
import { NavbarDesktopIotPortal } from './NavbarDesktopIotPortal'
import { NavbarDesktopDevPortal } from './NavbarDesktopDevPortal'

// Thanh header tổng
export const Navbar = () => {
  // Trạng thái đang ở nền tảng mobile
  const { isMobile } = useResponsive()

  const pathname = usePathname()
  const isIotPortal = pathname.includes('/iot-portal')
  const isDevPortal = pathname.includes('/partner-portal') || pathname.includes('/dev-portal')

  const navbar = () => {
    if (isMobile) return <NavbarMobile />
    if (isIotPortal) return <NavbarDesktopIotPortal />
    if (isDevPortal) return <NavbarDesktopDevPortal />

    return <NavbarDesktop />
  }

  return <header>{navbar()}</header>
}
