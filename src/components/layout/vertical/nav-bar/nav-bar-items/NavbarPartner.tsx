import React, { useState } from 'react'

import { Collapse } from 'antd'

import { useResponsive } from '@/hooks'

import Link from '@components/Link'

/** <PERSON><PERSON><PERSON> tác ở header */
export const NavbarPartner = () => {
  // Kiểm tra responsive
  const { isMobile } = useResponsive()

  const [visible, setVisible] = useState(false)

  return isMobile ? (
    <Collapse
      ghost
      expandIconPosition='end'
      items={[
        {
          key: 'mobile_navbar_product',
          label: <div className='-ml-4 text-base font-medium'>Đ<PERSON><PERSON> tác</div>,
          children: (
            <div className='-ml-2 space-y-6'>
              <div>
                <Link
                  href='/partner-portal/login'
                  target='_blank'
                  className='cursor-pointer text-base font-medium text-neutral-500 transition hover:text-primary'
                >
                  Nhà cung cấp
                </Link>
              </div>
              <div>
                <Link
                  href='https://onesme.vn/home-page/affiliate'
                  target='_blank'
                  className='cursor-pointer text-base font-medium text-neutral-500 transition hover:text-primary'
                >
                  <PERSON>h<PERSON>ch hàng
                </Link>
              </div>
            </div>
          )
        }
      ]}
    />
  ) : (
    <div className='relative' onMouseOver={() => setVisible(true)} onMouseOut={() => setVisible(false)}>
      <div className='my-2 flex w-[95px] cursor-pointer items-center justify-center gap-2 py-2'>
        <div>Đối tác</div>
        <i className='onedx-chevron-down size-4' />
      </div>
      <div
        id='NavbarPartnerDropdown'
        className='absolute left-0 top-0 z-10 space-y-2 whitespace-nowrap rounded-b-lg bg-white p-4 transition-all'
        style={{
          boxShadow: '0px 4px 18px 0px #4B465C1A',
          transform: visible ? 'translateY(60%)' : 'translateY(0)',
          transition: 'all 0.25s',
          visibility: visible ? 'visible' : 'hidden',
          opacity: visible ? 1 : 0
        }}
      >
        <div>
          <Link href='/partner-portal/login' target='_blank' className='cursor-pointer transition hover:text-primary'>
            Nhà cung cấp
          </Link>
        </div>
        <div>
          <Link
            href='https://onesme.vn/home-page/affiliate'
            target='_blank'
            className='cursor-pointer transition hover:text-primary'
          >
            Khách hàng
          </Link>
        </div>
      </div>
    </div>
  )
}
