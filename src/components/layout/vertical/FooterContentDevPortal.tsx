'use client'

// Next Imports
// import Link from 'next/link'

import React, { useState } from 'react'

import Image from 'next/image'

import classnames from 'classnames'

// Hook Imports
// import useVerticalNav from '@menu/hooks/useVerticalNav'

// Util Imports
import logo from '@public/assets/images/logo.svg'

import Link from '@/components/Link'
import { verticalLayoutClasses } from '@layouts/utils/layoutClasses'

interface IDivProps extends React.HTMLAttributes<HTMLDivElement> {
  customProp?: string
}

/** Menu */
export const MenuLinks = ({ ...rest }: IDivProps) => {
  const scrollToGuideSection = (element: string) => {
    const guideSection = document.getElementById(element)

    if (guideSection) {
      guideSection.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      })
    }
  }

  return (
    <div {...rest}>
      <div
        onClick={() => scrollToGuideSection('gioi-thieu-section')}
        className='block cursor-pointer bg-transparent text-white'
      >
        Giớ<PERSON> thiệu
      </div>
      <div
        onClick={() => scrollToGuideSection('huong-dan-section')}
        className='block cursor-pointer bg-transparent text-white'
      >
        Đường dẫn
      </div>
      <div
        onClick={() => scrollToGuideSection('loi-ich-section')}
        className='block cursor-pointer bg-transparent text-white'
      >
        Lợi ích
      </div>
      <div
        onClick={() => scrollToGuideSection('faq-section')}
        className='block cursor-pointer bg-transparent text-white'
      >
        FAQ
      </div>
      <div
        onClick={() => scrollToGuideSection('lien-he-section')}
        className='block cursor-pointer bg-transparent text-white'
      >
        Liên hệ
      </div>
    </div>
  )
}

/** Điều khoản */
export const RuleLinks = ({ ...rest }: IDivProps) => (
  <div {...rest}>
    <Link className='block text-white' href='https://onesme.vn/blog/dieu-khoan-dang-ky-dich-vu' target='_blank'>
      Điều khoản chung
    </Link>
    <Link className='block text-white' href='https://onesme.vn/blog/chinh-sach-van-chuyen-va-giao-nhan' target='_blank'>
      Chính sách vận chuyển và giao nhận
    </Link>
    <Link className='block text-white' href='https://onesme.vn/blog/chinh-sach-bao-mat' target='_blank'>
      Chính sách bảo mật
    </Link>
    <Link className='block text-white' href='https://onesme.vn/blog/quy-dinh-thanh-toan' target='_blank'>
      Quy định thanh toán
    </Link>
    <Link className='block text-white' href='https://onesme.vn/blog/chinh-sach-bao-ve-du-lieu-ca-nhan' target='_blank'>
      Chính sách bảo vệ dữ liệu cá nhân - NĐ13
    </Link>
  </div>
)

/** Thông tin công ty */
const CompanyInformation = ({ ...rest }: IDivProps) => (
  <div {...rest}>
    <div className='font-semibold uppercase'>TỔNG CÔNG TY DỊCH VỤ VIỄN THÔNG (VNPT-VINAPHONE)</div>
    <div className='mt-6 font-normal'>
      Chịu trách nhiệm nội dung: Tổng công ty Dịch vụ Viễn thông - Tập đoàn Bưu chính Viễn thông Việt Nam.
      <br />
      Giấy chứng nhận kinh doanh số: 0100684378 do Sở Kế hoạch và Đầu tư TP.Hà Nội cấp lần đầu ngày 17/8/2010. Đăng ký
      thay đổi lần 4 ngày 20/03/2017, giấy ủy quyền cung cấp SPDV viễn thông số 2106A/GUQ-VNPT VNP-PCTT, cấp ngày
      17/05/2021
    </div>
    <div className='mt-6 flex gap-2 font-normal'>
      <i className='onedx-location-maker size-5 shrink-0' />
      <div>Tòa nhà VNPT, số 57 Huỳnh Thúc Kháng, Phường Láng Hạ, Quận Đống Đa, Thành phố Hà Nội, Việt Nam</div>
    </div>
    <div className='mt-4 flex gap-2'>
      <i className='onedx-phone size-5' />
      <div>Hotline: 1800 1260</div>
    </div>
  </div>
)

export const FooterIconsLink = ({ ...rest }: IDivProps) => (
  <div {...rest}>
    <Link href='https://www.facebook.com/OneSMEShop/' target='_blank' className='h-6' aria-label='Facebook'>
      <i className='onedx-facebook-icon size-6' />
    </Link>
    <Link
      href='https://www.youtube.com/channel/UCGu5Wgx1uVg5xmmTVBEFA4g'
      target='_blank'
      className='h-6'
      aria-label='Youtube'
    >
      <i className='onedx-youtube size-6' />
    </Link>
    <Link href='https://www.linkedin.com/company/vnptonesme/' target='_blank' className='h-6' aria-label='Linkedin'>
      <i className='onedx-linkedin size-6' />
    </Link>
  </div>
)

/** Đã đăng ký Bộ Công Thương */
const RegisterMOIT = () => (
  <Link href='http://online.gov.vn/Home/WebDetails/97804' target='_blank' className='mt-4'>
    <img
      src='/assets/images/pages/home/<USER>'
      alt='Đã đăng ký Bộ Công Thương'
      width={140}
      height={43}
      className='mt-4'
    />
  </Link>
)

const Copyright = () => (
  <div className='bg-[#021B48]'>
    <div className='container mx-auto p-3 text-center text-body-14 text-white'>© Copyright by VNPT Technology</div>
  </div>
)

const FooterContentDevPortal = () => {
  // Hooks
  // const { isBreakpointReached } = useVerticalNav()
  const [openMenuCollapse, setOpenMenuCollapse] = useState(false)
  const [openRulesCollapse, setOpenRulesCollapse] = useState(false)

  // region Mobile
  const FooterContentMobile = () => (
    <div className='container mx-auto hidden bg-[#03296B] px-4 py-8 text-body-14 text-white sm:block'>
      <Image src={logo} alt='VNPT' height={33} />

      {/* MENU */}
      <div className='mt-8 flex items-center justify-between' onClick={() => setOpenMenuCollapse(!openMenuCollapse)}>
        <div className='font-semibold uppercase'>MENU</div>
        <i className={classnames('onedx-chevron-down size-5 transition-transform', openMenuCollapse && 'rotate-180')} />
      </div>
      <MenuLinks className='space-y-4 pl-2 pt-2 font-normal transition-all' hidden={!openMenuCollapse} />

      {/* ĐIỀU KHOẢN */}
      <div className='mt-8 flex items-center justify-between' onClick={() => setOpenRulesCollapse(!openRulesCollapse)}>
        <div className='font-semibold uppercase'>MENU HỖ TRỢ</div>
        <i
          className={classnames('onedx-chevron-down size-5 transition-transform', openRulesCollapse && 'rotate-180')}
        />
      </div>
      <RuleLinks className='space-y-4 pl-2 pt-2 font-normal transition-all' hidden={!openRulesCollapse} />

      <RegisterMOIT />

      <CompanyInformation className='mt-8 text-body-14' />

      <FooterIconsLink className='mt-8 flex justify-center gap-6 py-3' />
    </div>
  )

  // endregion

  // region Desktop
  const FooterContentDesktop = () => (
    <div className='bg-[#03296B] py-10 text-white sm:hidden'>
      <div className='container mx-auto flex justify-between gap-4 sm:flex-col'>
        {/* Cột 1 */}
        <div className='max-w-[554px]'>
          <Image src={logo} alt='VNPT' height={32} />
          <CompanyInformation className='mt-10 text-body-14' />
        </div>
        {/* Cột 2 */}
        <div className='flex gap-14'>
          <div>
            <div className='flex gap-2'>
              <Image
                width={36}
                height={36}
                alt='qr-app-store'
                src='/assets/images/footer/qr-app-store.svg'
                loading='lazy'
              />
              <Link>
                <img
                  src='/assets/images/pages/home/<USER>'
                  alt='Download on the App Store'
                  width={130}
                  height={38}
                />
              </Link>
            </div>
            {/* Menu */}
            <div className='mt-10 space-y-4 text-body-14'>
              <div className='font-semibold uppercase'>MENU</div>
              <MenuLinks className='space-y-4 font-normal' />
            </div>
            <FooterIconsLink className='mt-4 flex gap-6 pt-3' />
          </div>

          <div>
            {/* Google Play */}
            <div className='flex gap-2'>
              <Image
                width={36}
                height={36}
                alt='qr-google-play'
                src='/assets/images/footer/qr-google-play.svg'
                loading='lazy'
              />
              <Link>
                <img
                  src='/assets/images/pages/home/<USER>'
                  alt='Download on the Google Play'
                  width={130}
                  height={38}
                />
              </Link>
            </div>
            {/* ĐIỀU KHOẢN */}
            <div className='mt-10 space-y-4 text-body-14'>
              <div className='font-semibold uppercase'>MENU HỖ TRỢ</div>
              <RuleLinks className='space-y-4 font-normal' />
            </div>
            <RegisterMOIT />
          </div>
        </div>
      </div>
    </div>
  )

  // endregion Desktop

  return (
    <div className={classnames(verticalLayoutClasses.footerContent)}>
      {/* Mobile */}
      <FooterContentMobile />
      {/* Desktop */}
      <FooterContentDesktop />
      <Copyright />
    </div>
  )
}

export default FooterContentDevPortal
