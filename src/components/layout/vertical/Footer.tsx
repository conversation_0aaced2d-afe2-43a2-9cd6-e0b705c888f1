// Component Imports
import { usePathname } from 'next/navigation'

import FooterContent from './FooterContent'
import FooterContentIotPortal from './FooterContentIotPortal'
import FooterContentDevPortal from './FooterContentDevPortal'

const Footer = () => {
  const pathname = usePathname()
  const isIotPortal = pathname.includes('/iot-portal')
  const isDevPortal = pathname.includes('/partner-portal') || pathname.includes('/dev-portal')

  const footer = () => {
    if (isIotPortal) return <FooterContentIotPortal />
    if (isDevPortal) return <FooterContentDevPortal />

    return <FooterContent />
  }

  return <footer>{footer()}</footer>
}

export default Footer
