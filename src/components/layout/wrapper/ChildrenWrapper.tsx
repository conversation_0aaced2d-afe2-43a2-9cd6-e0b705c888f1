'use client'

import { useEffect, useState, useMemo } from 'react'

import { usePathname } from 'next/navigation'

import { Editor, Frame } from '@craftjs/core'

import { useQuery } from '@tanstack/react-query'

import BackTop from 'antd/es/float-button/BackTop'

import { useDispatch } from 'react-redux'

import Builder from '@/models/Builder'
import { resolver } from '@/components/page-builder/selectors'

import VerticalFooter from '@components/layout/vertical/Footer'
import { Navbar } from '@components/layout/vertical/nav-bar'
import type { ChildrenType } from '@core/types'

import VerticalLayout from '@layouts/VerticalLayout'
import { useCheckUnauthorizedPortal, useResponsive, useUser } from '@/hooks'

import { setInfoPageBuilder, updateScreenType } from '@/redux-store/slices/builderSlice'
import { DataPrivacyModal } from '@/components/modal/DataPrivacyModal'
import SmeAcceptTerms from '@/models/SmeAcceptTerms'
import ContactSidebar from '@/components/page-builder/editor/CustomComponents/ContactSidebar'

type Props = ChildrenType

type AllSlugNames = {
  [key in 'all' | 'enterprise' | 'houseHold' | 'personal' | 'landingPage']: string[]
}

const removeFirstChar = (text: string): string => {
  return text.length > 1 ? text.slice(1) : text
}

const EditorFrame = ({ data, key }: { data: any; key?: string }) => (
  <Editor key={key} enabled={false} resolver={resolver}>
    <Frame data={data as any} />
  </Editor>
)

export const ChildrenWrapper = ({ children }: Props) => {
  const pathname = usePathname()
  const { isMobile } = useResponsive()
  const dispatch = useDispatch()

  const [openModal, setOpenModal] = useState(false)

  const { user, isLoggedIn } = useUser()

  // region privacy
  const isdDataPrivacyPath = pathname.includes('/van-ban-chap-thuan-xu-ly-bao-ve-du-lieu-ca-nhan')

  // Chính sách bảo vệ dữ liệu cá nhân
  const { data: dataPrivacy } = useQuery({
    queryKey: ['dataPrivacy'],
    queryFn: async () => await SmeAcceptTerms.getDataPolicy(),
    enabled: !!user?.permissions && !isdDataPrivacyPath
  })

  useEffect(() => {
    if (!!dataPrivacy && isLoggedIn && !dataPrivacy?.confirmStatus && !isdDataPrivacyPath) {
      setOpenModal(true)
    } else {
      setOpenModal(false)
    }
  }, [dataPrivacy?.confirmStatus, isLoggedIn, dataPrivacy, isdDataPrivacyPath])
  // endregion

  // region page builder
  const {
    data: allSlugNames,
    isLoading: isLoadingAllSlugs,
    error: slugsError
  } = useQuery({
    queryKey: ['getAllSlugNames'],
    queryFn: async () => {
      // Thêm timeout riêng cho query này
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('getAllSlugs timeout')), 3000) // 3 giây timeout
      })

      const apiPromise = Builder.getAllSlugs()

      const res: AllSlugNames = (await Promise.race([apiPromise, timeoutPromise])) as AllSlugNames

      return res
    },
    retry: 0, // Không retry để tránh chờ lâu
    staleTime: Infinity, // Giữ dữ liệu luôn "fresh", không refetch
    gcTime: Infinity, // Không xóa cache, giữ dữ liệu mãi mãi
    // Không throw error để tránh crash app
    throwOnError: false
  })

  // Kiểm tra pathname có tồn tại trong danh sách không
  const shouldFetchDetailPage = useMemo(() => {
    // Nếu getAllSlugs chưa load xong hoặc lỗi, không fetch page builder
    // Để tránh trường hợp render page builder mà không có dữ liệu
    if (!allSlugNames) {
      return false
    }

    return Object.values(allSlugNames).some(slugs => slugs.includes(pathname))
  }, [allSlugNames, pathname])

  const firstSegment = useMemo(() => {
    const res = '/' + removeFirstChar(pathname).split('/').filter(Boolean)[0] || '/'

    // Nếu là trang /home thì load page builder của /
    return res === '/home' ? '/' : res
  }, [pathname])

  // Kiểm tra pathname[0] có tồn tại trong danh sách không
  const shouldFetchHeaderFooter = useMemo(() => {
    // Kiểm tra xem nếu không phải pageBuilder
    // Nếu phần tử thứ 0 của pathname, check xem nó là loại KH gì
    // Có thì hiển thị header, footer tương ứng
    const customerType =
      firstSegment === '/enterprise'
        ? 'enterprise'
        : firstSegment === '/house-hold'
          ? 'houseHold'
          : firstSegment === '/personal'
            ? 'personal'
            : firstSegment === '/'
              ? 'all'
              : ''

    // Nếu không có loại KH thì dùng code thuần
    if (!customerType) return false

    // Nếu getAllSlugs chưa load xong hoặc lỗi, fallback = false để dùng header/footer mặc định
    if (!allSlugNames) return false

    return allSlugNames[customerType]?.includes(firstSegment)
  }, [allSlugNames, firstSegment])

  /** API Chi tiết trang */
  const { data: pageBuilderDetail, isLoading: isLoadingDetailPage } = useQuery({
    queryKey: ['getDetailBySlugName', shouldFetchDetailPage, shouldFetchHeaderFooter, pathname],
    queryFn: async () => {
      // Thêm timeout cho API này để tránh loading vô hạn
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('getDetailBySlugName timeout')), 5000) // 5 giây timeout
      })

      const slugName = shouldFetchDetailPage ? pathname : firstSegment
      const apiPromise = Builder.getDetailBySlugName(slugName)

      const res: any = await Promise.race([apiPromise, timeoutPromise])

      dispatch(
        setInfoPageBuilder({
          customerType: res.customerTypeEnum,
          pageName: res.pageName,
          slugName: res.slugName,
          status: res.status
        })
      )

      const pageContent: Record<string, any> = JSON.parse(res.pageContent)

      // Hàm chung để thu thập tất cả các nodes liên quan đến một loại section (Header / Footer)
      const collectSectionNodes = (sectionName: string): Record<string, any> => {
        const sectionNodes: Record<string, any> = {}
        const collectedKeys = new Set<string>()

        const collectRelatedNodes = (key: string) => {
          if (!pageContent[key] || collectedKeys.has(key)) return

          collectedKeys.add(key)
          sectionNodes[key] = pageContent[key]

          // Duyệt tiếp nodes và linkedNodes
          pageContent[key].nodes.forEach(collectRelatedNodes)
          Object.values(pageContent[key].linkedNodes || {}).forEach(linkedKey =>
            collectRelatedNodes(linkedKey as string)
          )
        }

        // Lọc object theo `displayName`
        Object.keys(pageContent).forEach(key => {
          if (pageContent[key].displayName === sectionName) {
            collectedKeys.add(key)
            sectionNodes[key] = pageContent[key]
            pageContent[key].nodes.forEach(collectRelatedNodes)
            Object.values(pageContent[key].linkedNodes || {}).forEach(linkedKey =>
              collectRelatedNodes(linkedKey as string)
            )
          }
        })

        return sectionNodes
      }

      // Lấy dữ liệu riêng biệt cho Header và Footer
      const headerNodes = collectSectionNodes('Header')
      const footerNodes = collectSectionNodes('FooterBuilder')

      const headerKey = Object.keys(headerNodes).find(key => headerNodes[key].displayName === 'Header')
      const footerKey = Object.keys(footerNodes).find(key => footerNodes[key].displayName === 'FooterBuilder')

      res.header = {
        ROOT: { ...pageContent.ROOT, nodes: [headerKey] },
        ...headerNodes
      }
      res.footer = {
        ROOT: { ...pageContent.ROOT, nodes: [footerKey] },
        ...footerNodes
      }

      return res
    },
    retry: 0,
    enabled: shouldFetchDetailPage || shouldFetchHeaderFooter, // Chỉ fetch nếu API allSlugNames đã xong
    staleTime: 1000 * 60 * 5, // Cache dữ liệu trong 5 phút
    gcTime: 1000 * 60 * 10, // Dữ liệu vẫn được lưu trong cache 10 phút ngay cả khi không dùng
    throwOnError: false // Không throw error để tránh crash app
  })
  // endregion

  // Cập nhật screenType theo kích thước màn hình hiện tại khi đang áp dụng
  useEffect(() => {
    if (isMobile) {
      dispatch(updateScreenType('mobile'))
    } else {
      dispatch(updateScreenType('desktop'))
    }
  }, [dispatch, isMobile])

  // Log error khi getAllSlugs thất bại (chỉ trong development)
  useEffect(() => {
    if (slugsError && process.env.NODE_ENV === 'development') {
      console.warn('getAllSlugs API failed:', slugsError)
    }
  }, [slugsError])

  // Fallback state để tránh loading quá lâu
  const [shouldUseFallback, setShouldUseFallback] = useState(false)

  useEffect(() => {
    // Sau 2 giây nếu vẫn đang loading getAllSlugs, chuyển sang fallback
    const timer = setTimeout(() => {
      if (isLoadingAllSlugs && !allSlugNames) {
        setShouldUseFallback(true)
      }
    }, 2000)

    // Clear timer nếu API đã xong
    if (!isLoadingAllSlugs || allSlugNames) {
      setShouldUseFallback(false)
      clearTimeout(timer)
    }

    return () => clearTimeout(timer)
  }, [isLoadingAllSlugs, allSlugNames])

  // Kiểm tra có đang đăng nhập admin/dev không
  const unauthorizedComponent = useCheckUnauthorizedPortal()

  if (unauthorizedComponent) {
    return unauthorizedComponent
  }

  // Chỉ block render khi đang load detail page
  if (isLoadingDetailPage) return <></>

  // Nếu getAllSlugs đang loading lần đầu hoặc đã timeout, hiển thị layout mặc định
  if ((isLoadingAllSlugs && !allSlugNames) || shouldUseFallback) {
    return (
      <VerticalLayout navigation={<></>} navbar={<Navbar />} footer={<VerticalFooter />}>
        {children}
        <BackTop icon={<i className='onedx-chevron-up text-sme-blue-7' />} />
      </VerticalLayout>
    )
  }

  // region return
  return shouldFetchDetailPage ? (
    <>
      <EditorFrame data={pageBuilderDetail?.pageContent} key={pathname} />
      {openModal && <DataPrivacyModal openModal={openModal} setOpenModal={setOpenModal} />}
      <ContactSidebar />
      <BackTop icon={<i className='onedx-chevron-up text-sme-blue-7' />} />
    </>
  ) : (
    <VerticalLayout
      navigation={<></>}
      navbar={shouldFetchHeaderFooter ? <EditorFrame data={pageBuilderDetail?.header} /> : <Navbar />}
      footer={shouldFetchHeaderFooter ? <EditorFrame data={pageBuilderDetail?.footer} /> : <VerticalFooter />}
    >
      {children}
      <BackTop icon={<i className='onedx-chevron-up text-sme-blue-7' />} />
    </VerticalLayout>
  )
  // endregion
}
