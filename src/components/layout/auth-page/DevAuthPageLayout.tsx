import type { ReactNode } from 'react'
import React, { useState } from 'react'

import { But<PERSON> } from 'antd'

import ContactFormModal from '@views/enterprise/contact-customer/ContactFormModal'

interface AuthPageLayoutProps {
  children: ReactNode
  title?: string
  description?: string
  footer?: ReactNode
  goBackText?: string
  onGoBack?: any
}

const DevAuthPageLayout: React.FC<AuthPageLayoutProps> = ({
  children,
  title,
  footer,
  onGoBack,
  goBackText,
  description
}) => {
  const [isModalVisible, setIsModalVisible] = useState(false)

  return (
    <div className='relative flex h-screen justify-end'>
      {/* Form bên trái */}
      <div className='h-full w-[45%] flex-1 bg-white sm:w-full sm:px-4'>
        <div className='flex h-screen flex-col items-center overflow-y-auto'>
          <div className='flex h-full min-h-full w-[498px] flex-col pb-[40px] pt-[127px] sm:pt-[18px]'>
            {onGoBack && (
              <div>
                <Button
                  onClick={onGoBack}
                  icon={<i className='onedx-back-icon mt-px size-4' />}
                  className='border-none p-0 text-text-neutral-medium shadow-none'
                >
                  {goBackText}
                </Button>
              </div>
            )}
            <div className='content mx-auto mt-[20px] w-full flex-1'>
              {/* Title */}
              {title && (
                <div className={`${description ? 'mb-[20px]' : 'mb-[40px]'} text-9xl font-semibold leading-[40px]`}>
                  {title}
                </div>
              )}
              {/* Description */}
              {description && (
                <div className='font-inter mb-[40px] text-sm font-normal leading-5 tracking-wide text-gray-8'>
                  {description}
                </div>
              )}
              {/* Form */}
              <div>{children}</div>
            </div>

            {/* Footer */}
            <div className='content mx-auto mt-[40px] w-full sm:mt-[18px]'>{footer}</div>
          </div>

          {/* Button liên hệ */}
          {/*<div className='fixed right-0 top-1/2 z-[1001] flex flex-col items-end'>*/}
          <div className='fixed right-0 top-1/2 z-[1001] hidden flex-col items-end'>
            {/* Button liên hệ */}
            <div
              aria-hidden='true'
              role='button'
              onClick={() => setIsModalVisible(true)}
              className='flex h-[117px] w-9 cursor-pointer'
              style={{
                backgroundImage: 'url(/assets/images/contactButton.svg)',
                backgroundSize: 'cover',
                backgroundPosition: 'center',
                backgroundRepeat: 'no-repeat',
                backgroundColor: '#f0f0f0',
                borderRadius: '4px 0 0 4px'
              }}
            />

            {/* Spacer */}
            <div className='h-1'></div>

            {/* Phone number */}
            <div
              className='group flex w-9 cursor-pointer items-center gap-2 bg-primary p-2 hover:w-full'
              onClick={() => (window.location.href = 'tel:18001260')}
            >
              <i className='onedx-banner-phone size-5 cursor-pointer text-white' />
              <span className='hidden text-sm text-white group-hover:block'>1800 1260</span>
            </div>
          </div>

          {/* Contact modal */}
          {isModalVisible && <ContactFormModal isModalVisible={isModalVisible} setIsModalVisible={setIsModalVisible} />}
        </div>
      </div>

      {/* Ảnh nền bên phải */}
      <div className='h-full w-[55%] sm:hidden'>
        <img
          src='/assets/images/pages/dev-portal/auth/authentication-bg.png'
          alt='Authentication Background'
          className='size-full'
          draggable={false}
        />
      </div>
    </div>
  )
}

export default DevAuthPageLayout
