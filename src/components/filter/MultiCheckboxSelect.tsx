import { useState } from 'react'

import { Checkbox, Popover } from 'antd'

interface MultiCheckboxSelectProps {
  optionFilter: any[]
  placeholder: string
  onChange?: (values: any[]) => void
  value?: any[]
  height?: number | string
  width?: number | string
}

export const MultiCheckboxSelect = ({
  optionFilter,
  placeholder,
  onChange,
  value = [],
  height = 36,
  width = 190
}: MultiCheckboxSelectProps) => {
  const [openPopup, setOpenPopup] = useState(false)
  const [selectedValues, setSelectedValues] = useState<any[]>(value)

  const handleSelectItem = (item: any, checked: boolean) => {
    let newValues: any[]

    if (checked) {
      newValues = [...selectedValues, item.value]
    } else {
      newValues = selectedValues.filter(val => val !== item.value)
    }

    setSelectedValues(newValues)
    onChange?.(newValues)
  }

  return (
    <Popover
      placement='bottom'
      overlayStyle={{ width: typeof width === 'number' ? width : 'auto', minWidth: '190px' }}
      arrow={false}
      content={
        <div className='w-full items-start justify-start gap-2 bg-white'>
          {/* Options */}
          <div className='flex max-h-64 w-full justify-start overflow-y-auto'>
            <div className='flex w-full flex-col'>
              {optionFilter?.length > 0 ? (
                optionFilter.map((item: any, index: number) => (
                  <div
                    key={index}
                    className='hover:bg-primary200 flex items-start justify-start gap-3 self-stretch rounded-xl py-2 transition duration-300 hover:cursor-pointer'
                  >
                    <Checkbox
                      value={item.value}
                      checked={selectedValues.includes(item.value)}
                      onChange={e => handleSelectItem(item, e.target.checked)}
                    >
                      {item.label}
                    </Checkbox>
                  </div>
                ))
              ) : (
                <div className='text-center text-sm font-normal leading-tight tracking-tight text-gray-800'>
                  Không có giá trị
                </div>
              )}
            </div>
          </div>
        </div>
      }
      trigger='click'
      open={openPopup}
      onOpenChange={visible => setOpenPopup(visible)}
    >
      <div
        style={{ borderColor: '#d9d9d9', height, width }}
        className='relative inline-flex shrink-0 cursor-pointer items-center justify-start rounded-lg border border-solid bg-white px-3 py-1'
      >
        <div className='flex w-full items-center justify-between text-text-neutral-light'>
          <div className='truncate'>{placeholder}</div>
          {openPopup ? (
            <i className='onedx-chevron-down size-5 rotate-180' />
          ) : (
            <i className='onedx-chevron-down size-5 ' />
          )}
        </div>
      </div>
    </Popover>
  )
}
