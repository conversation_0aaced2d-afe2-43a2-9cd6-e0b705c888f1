import React from 'react'

import type { TableColumnsType } from 'antd'
import { Spin, Tooltip } from 'antd'

import { formatCurrency } from '@/constants/commonFunction'
import { BlueHeaderTable } from '@/constants/subscriptions'

interface Props {
  isLoading: boolean
  dataSource: any
  CAN_VIEW_EINVOICE: boolean
  CAN_DOWNLOAD_EINVOICE: boolean
  viewFkey: (record: any) => void
  downloadPdfFkey: (record: any) => void
  isBillingCode: boolean
}

const INVOICE_NUMBER_LENGTH = 8

const EinvoiceInfo = ({
  isLoading,
  dataSource,
  CAN_VIEW_EINVOICE,
  CAN_DOWNLOAD_EINVOICE,
  viewFkey,
  downloadPdfFkey,
  isBillingCode
}: Props) => {
  const padLeadingZeros = (num: number, size: number) => {
    // <PERSON><PERSON>ể<PERSON> sang chuỗi để áp dụng cho hàm padStart
    const numStr = num?.toString()

    // Dùng padStart để thêm số 0 vào đầu cho đến khi đủ độ dài mong muốn
    return numStr?.padStart(size, '0')
  }

  const eInvoiceColumns: TableColumnsType<any> = [
    {
      title: 'Số hóa đơn',
      dataIndex: isBillingCode ? 'invoiceNo' : 'code',
      key: isBillingCode ? 'invoiceNo' : 'code',
      width: '20%',
      render: (value: number) => padLeadingZeros(value, INVOICE_NUMBER_LENGTH),
      ellipsis: true
    },
    {
      title: 'Ngày hóa đơn',
      dataIndex: isBillingCode ? 'invoiceDate' : 'einvoiceDate',
      key: isBillingCode ? 'invoiceDate' : 'einvoiceDate',
      width: '20%',
      ellipsis: true
    },
    {
      title: 'Mã tra cứu',
      dataIndex: 'fkey',
      key: 'fkey',
      width: '40%',
      ellipsis: true
    },
    {
      title: 'Số tiền',
      dataIndex: isBillingCode ? 'price' : 'totalAmount',
      key: isBillingCode ? 'price' : 'totalAmount',
      render: (value: number) => (
        <div className='flex'>
          <div className='text-caption-12 leading-4'>₫</div>
          <div className='leading-5'>{formatCurrency(value)}</div>
        </div>
      ),
      width: '20%',
      ellipsis: true
    },
    {
      dataIndex: 'action',
      key: 'action',
      render: (_: any, record: any) => (
        <div className='flex'>
          {CAN_VIEW_EINVOICE && (
            <button type='button' className='cursor-pointer bg-inherit text-gray-8' onClick={() => viewFkey(record)}>
              <Tooltip title='Xem chi tiết'>
                <i className='onedx-list2 size-6' />
              </Tooltip>
            </button>
          )}
          {CAN_DOWNLOAD_EINVOICE && (
            <button
              type='button'
              className='cursor-pointer bg-inherit text-gray-8'
              onClick={() => downloadPdfFkey(record)}
            >
              <Tooltip title='Tải về'>
                <i className='onedx-download size-6' />
              </Tooltip>
            </button>
          )}
        </div>
      ),
      fixed: 'right',
      width: 100
    }
  ]

  return (
    <Spin spinning={isLoading}>
      <BlueHeaderTable
        columns={eInvoiceColumns}
        dataSource={dataSource}
        rowKey={(record: any) => (isBillingCode ? record.invoiceNo : record.code)}
        pagination={false}
        rowClassName='group'
        scroll={{ x: 750 }}
      />
    </Spin>
  )
}

export default EinvoiceInfo
