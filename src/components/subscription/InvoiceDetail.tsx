import React, { useState } from 'react'

import { useRouter } from 'next/navigation'

import { useMutation, useQuery } from '@tanstack/react-query'

import { message, Popover, But<PERSON>, Spin } from 'antd'

import { useDispatch } from 'react-redux'

import moment from 'moment'

import { IDENTITY_TYPE } from '@/constants/constantEnum'

import { paymentMethodEnum } from '@views/payment/utils/paymentUtils'

import { useResponsive, useUser } from '@/hooks'
import {
  handleSetActiveTab,
  handleSetCustomerInfo,
  handleSetSelectedInvoice
} from '@/redux-store/slices/SubscriptionInvoiceSlice'
import { paymentActions } from '@/redux-store/slices/PaymentSlice'

import SubscriptionManagement from '@/models/SubscriptionManagement'
import SmeSubscription from '@/models/SmeSubscription'
import BillingSme from '@/models/BillingSme'
import DXPortal from '@/models/DXPortal'

import {
  ADDRESS_TYPE,
  PAYMENT_METHOD,
  Status,
  PaymentMethod,
  INVOICE_TYPE,
  PAYMENT_METHOD_STR,
  INVOICE_STATUS
} from '@/constants/subscriptions'
import {
  addDays,
  convertCustomerInfo,
  formatNormalizeTaxCodev2,
  renderPaymentMethod,
  renderPaymentMethodByString
} from '@/utils/subscription'

import ModalConfirm from './modal/ModalConfirm'
import InvoiceStatusTag from './InvoiceStatusTag'
import ModalEditInvoice from './modal/ModalEditInvoice'
import InvoiceServiceInfo from './InvoiceServiceInfo'
import MobileInvoiceServiceInfo from './mobile/MobileInvoiceServiceInfo'
import EinvoiceInfo from './EinvoiceInfo'
import MobileEInvoiceInfo from './mobile/MobileEinvoiceInfo'
import ImpQRCodeModal from '@/views/payment/components/order-summary/ImpQRCodeModal'

interface Props {
  selectedInvoice: any
  setSelectedCreditNote: (value: any) => void
}

const InvoiceDetail = ({ selectedInvoice, setSelectedCreditNote }: Props) => {
  const { user } = useUser()
  const dispatch = useDispatch()
  const router = useRouter()
  const { isDesktop } = useResponsive()

  const [eInvoiceName, setEInvoiceName] = useState<string>('')
  const [openEditModal, setOpenEditModal] = useState<boolean>(false)
  const [openPublishModal, setOpenPublishModal] = useState<boolean>(false)
  const [paymentApply, setPaymentApply] = useState<string>('')

  const {
    data: invoiceDetail,
    refetch: refetchInvoiceDetail,
    isFetching: invoiceDetailLoading
  } = useQuery({
    queryKey: selectedInvoice?.isBillingCode
      ? ['getInvoiceDetail', selectedInvoice?.billingCode]
      : ['getInvoiceDetail', selectedInvoice?.billingId],
    queryFn: async () => {
      const res = selectedInvoice?.isBillingCode
        ? await SubscriptionManagement.getBillDetail({ billingCode: selectedInvoice.billingCode })
        : await BillingSme.getOneById(selectedInvoice?.billingId)

      if (res?.customer) {
        dispatch(handleSetCustomerInfo(convertCustomerInfo(res.customer)))
      }

      return res
    },
    enabled: !!selectedInvoice
  })

  const canExportInvoice = (billItems: any) => billItems?.some((item: any) => item?.isOn === true)

  const canExportInvoiceThird = (data: { billItem: any[]; billing: { serviceOwner: string } }) =>
    data?.billItem?.some((item: any) => item?.isBuyService === true && item?.isOn === true) &&
    data?.billing.serviceOwner === 'VNPT'

  const isThirdParty = (billItem: any) => billItem?.some((item: any) => item?.isBuyService === true)
  const CAN_VIEW_EINVOICE = DXPortal.canAccessFeature('sme/view-einvoice', user.permissions)
  const CAN_DOWNLOAD_EINVOICE = DXPortal.canAccessFeature('sme/download-einvoice', user.permissions)
  const CAN_EXPORT_EINVOICE = DXPortal.canAccessFeature('sme/export-einvoice', user.permissions)

  // check điều kiện hiển thị Xuất hóa đơn + Sửa hóa đơn
  const checkConditionsActionInvoice = (
    status: string | number,
    canPublish: boolean,
    serviceOwner: string,
    createdSourceMigration: string,
    isCart: boolean,
    isThirdParty: boolean,
    canExportInvoiceThird: boolean,
    isExportInvoice: boolean,
    isCartHaveItemOn: boolean
  ) => {
    if (!isCart && (serviceOwner === 'OTHER' || serviceOwner === 'NONE')) return false
    if (
      (status === 'PAID' || status === INVOICE_STATUS.PAID) &&
      CAN_EXPORT_EINVOICE &&
      createdSourceMigration !== 'DHSXKD' &&
      canPublish &&
      !isCart &&
      !isThirdParty &&
      isExportInvoice
    )
      return true

    if (
      status === INVOICE_STATUS.PAID &&
      CAN_EXPORT_EINVOICE &&
      createdSourceMigration !== 'DHSXKD' &&
      canPublish &&
      (isCart || (isThirdParty && canExportInvoiceThird)) &&
      isCartHaveItemOn
    )
      return true

    return false
  }

  const CAN_ACTION_INVOICE = selectedInvoice?.isBillingCode
    ? checkConditionsActionInvoice(
        invoiceDetail?.payment?.billingStatus,
        !(
          (invoiceDetail?.einvoice?.length > 0 || invoiceDetail?.totalAfterTaxFinalAmount === 0) // tổng tiền = 0 ko thể xuất hoá đơn điện tử
        ),
        invoiceDetail?.payment?.serviceOwner,
        invoiceDetail?.createdSourceMigration,
        invoiceDetail?.payment?.isCart,
        isThirdParty(invoiceDetail?.billItem),
        canExportInvoiceThird(invoiceDetail),
        invoiceDetail?.billing?.exportInvoice,
        canExportInvoice(invoiceDetail?.billItem)
      ) &&
      [PAYMENT_METHOD.VNPTPAY, PAYMENT_METHOD.VNPTPAY_QR].includes(
        invoiceDetail?.billing?.isOneTime === 0 || !invoiceDetail?.billing?.isCreate
          ? PAYMENT_METHOD.VNPTPAY
          : invoiceDetail?.payment?.paymentMethod
      )
    : checkConditionsActionInvoice(
        invoiceDetail?.billing?.status,
        !(
          (invoiceDetail?.einvoices?.length > 0 || invoiceDetail?.totalAmountAfterRefund === 0) // tổng tiền = 0 ko thể xuất hoá đơn điện tử
        ),
        invoiceDetail?.billing?.serviceOwner,
        invoiceDetail?.createdSourceMigration,
        false,
        false,
        false,
        invoiceDetail?.billing?.exportInvoice,
        canExportInvoice(invoiceDetail?.billItem)
      ) &&
      [PAYMENT_METHOD_STR.VNPTPAY, PAYMENT_METHOD_STR.VNPTPAY_QR].includes(
        invoiceDetail?.billing?.isCreate ? invoiceDetail?.billing?.paymentMethod : PAYMENT_METHOD_STR.VNPTPAY
      )

  const publishBill = useMutation({
    mutationKey: ['publishBill'],
    mutationFn: BillingSme.publishBill,
    onSuccess: () => {
      message.success('Xuất hóa đơn PDF thành công')
      refetchInvoiceDetail()
      setOpenPublishModal(false)
    },
    onError: (e: any) => {
      if (e?.errorCode === 'error.invoice.authen.invalid') {
        message.error('Sai tài khoản xuất hoá đơn')
      } else if (e?.errorCode === 'error.invoice.xml.invalid') {
        message.error('Nội dung xml xuất không đúng')
      } else if (e?.errorCode === 'error.invoice.not.found.company') {
        message.error('Không tìm thấy công ty')
      } else if (e?.errorCode === 'error.invoice.pattern.serial.invalid') {
        message.error('Sai thông tin pattern hoặc serial')
      } else if (e?.errorCode === 'error.invoice.not.publish') {
        message.error('Cannot publish invoice')
      } else if (e?.errorCode === 'error.einvoice.amount.zero') {
        message.error('Không xuất được hoá đơn có tổng thanh toán bằng 0')
      } else {
        message.error('Xuất hóa đơn không thành công!')
      }
    }
  })

  const publishBillIsCart = useMutation({
    mutationKey: ['publishBillIsCart'],
    mutationFn: BillingSme.publishBillIsCart,
    onSuccess: () => {
      message.success('Xuất hóa đơn PDF thành công')
      refetchInvoiceDetail()
      setOpenPublishModal(false)
    },
    onError: (e: any) => {
      if (e?.errorCode === 'error.invoice.authen.invalid') {
        message.error('Sai tài khoản xuất hoá đơn')
      } else if (e?.errorCode === 'error.invoice.xml.invalid') {
        message.error('Nội dung xml xuất không đúng')
      } else if (e?.errorCode === 'error.invoice.not.found.company') {
        message.error('Không tìm thấy công ty')
      } else if (e?.errorCode === 'error.invoice.pattern.serial.invalid') {
        message.error('Sai thông tin pattern hoặc serial')
      } else if (e?.errorCode === 'error.invoice.not.publish') {
        message.error('Cannot publish invoice')
      } else if (e?.errorCode === 'error.einvoice.amount.zero') {
        message.error('Không xuất được hoá đơn có tổng thanh toán bằng 0')
      } else {
        message.error('Xuất hóa đơn không thành công!')
      }
    }
  })

  // thanh toán
  const payMutate = useMutation({
    mutationKey: ['payBill'],
    mutationFn: SmeSubscription.payment,
    onSuccess: async ({ redirectURL, qrcodeImage, expirationTime, billId }) => {
      if (qrcodeImage) {
        dispatch(
          paymentActions.handleSetQRCodeInfo({
            name: 'InvoiceDetail-payMutate',
            image: qrcodeImage,
            timeLeft: expirationTime,
            billId,
            state: {
              totalFinalPrice: invoiceDetail?.totalAfterTaxFinalAmount,
              totalQuantity: invoiceDetail?.billItem?.length,
              totalPrice: invoiceDetail?.totalAfterTaxAmount,
              shippingFee: 0
            },
            functions: {
              refetchQRCode
            }
          })
        )
        dispatch(paymentActions.handleToggleQRCodeModal(true))
      } else {
        if (redirectURL) {
          window.location.href = redirectURL
        } else {
          router.push(
            DXPortal.getPortalByCustomerType(user?.customerType).createPath(`/payment-error?paymentFrom=BILLING`)
          )
        }

        return null
      }
    },
    onError: () => {
      router.push(DXPortal.getPortalByCustomerType(user?.customerType).createPath(`/payment-error?paymentFrom=BILLING`))
    }
  })

  const downloadPDFFkeyMutate = useMutation({
    mutationKey: ['downloadEInvoicePdfSme'],
    mutationFn: BillingSme.downloadEInvoicePdf,
    onSuccess: res => {
      const filename = `${eInvoiceName || 'eInvoice'}.pdf`

      DXPortal.exportFile(res, filename, 'application/pdf')
    },
    onError: () => {
      message.error('Xác nhận không thành công')
    }
  })

  const viewFkeyMutate = useMutation({
    mutationKey: ['viewEInvoiceSme'],
    mutationFn: BillingSme.viewEInvoice,
    onSuccess: ({ content }: any) => {
      const newWindow = window.open('', 'PRINT', 'height=900,width=950')

      // @ts-ignore
      newWindow.document.documentElement.innerHTML = content
      // @ts-ignore

      newWindow.document.documentElement.style.overflow = 'auto'
      // @ts-ignore

      newWindow.document.body.style.overflow = 'auto'
      // @ts-ignore

      newWindow.focus()

      const check = () => {
        // @ts-ignore

        if (newWindow.document) {
          // if loaded
          // @ts-ignore

          newWindow.document.title = eInvoiceName // set title
        } else {
          // if not loaded yet
          setTimeout(check, 10) // check in another 10ms
        }
      }

      check()

      return true
    },
    onError: (e: any) => {
      if (e) message.error(`Không thành công`)
    }
  })

  const viewFkey = (data: any) => {
    const code = selectedInvoice?.isBillingCode ? data?.invoiceNo : data?.code
    const filename = `e-invoice${code}_${moment().format('DD/MM/YYYY')}`

    setEInvoiceName(filename)
    viewFkeyMutate.mutate(data?.fKey)
  }

  const downloadPdfFkey = ({ fKey, invoiceNo }: any) => {
    const filename = `e-invoice${invoiceNo}_${moment().format('DD/MM/YYYY')}`

    setEInvoiceName(filename)
    downloadPDFFkeyMutate.mutate(fKey)
  }

  const refetchQRCode = () => {
    dispatch(paymentActions.handleToggleQRCodeModal(false))
    payMutate.mutate({
      id: invoiceDetail?.billItem[0]?.billingId,
      paymentMethod: 'VNPTPAY_QR'
    })
  }

  const publishInvoiceDate = (data: any) => {
    if (selectedInvoice?.isBillingCode) {
      const paymentDate = data?.payment?.paymentDate ? data?.payment?.paymentDate : null

      if (data !== null && data?.einvoice?.length === 0) {
        const splitDate = paymentDate.split('/')
        const date = new Date(`${splitDate[1]}/${splitDate[0]}/${splitDate[2]}`)
        const addDate = addDays(date, data?.payment?.numberOfDayExportBill)
        const monthNumber = addDate.getMonth() + 1 < 10 ? `0${addDate.getMonth() + 1}` : addDate.getMonth() + 1
        const dateNumber = addDate.getDate() < 10 ? `0${addDate.getDate()}` : addDate.getDate()

        return `${dateNumber}/${monthNumber}/${addDate.getFullYear()}`
      }

      return null
    }

    const paymentDate = data?.billing?.paymentDate ? data?.billing?.paymentDate : null

    if (paymentDate !== null && data?.einvoices?.length === 0) {
      const splitDate = paymentDate.split('/')
      const date = new Date(`${splitDate[1]}/${splitDate[0]}/${splitDate[2]}`)
      const addDate = addDays(date, data?.numberOfDayExportBill)
      const monthNumber = addDate.getMonth() + 1 < 10 ? `0${addDate.getMonth() + 1}` : addDate.getMonth() + 1
      const dateNumber = addDate.getDate() < 10 ? `0${addDate.getDate()}` : addDate.getDate()

      return `${dateNumber}/${monthNumber}/${addDate.getFullYear()}`
    }

    return null
  }

  const onClickPaymentMethod = (method: string) => {
    setPaymentApply(method)
    payMutate.mutate({
      id: invoiceDetail?.billItem[0]?.billingId,
      paymentMethod: method
    })
  }

  const renderNote = (data: any) => {
    if (data?.type === INVOICE_TYPE.OTHER || data?.type === INVOICE_TYPE.NONE) {
      const currentCycleFrom = data?.billing?.currentCycleFrom
      const currentCycleTo = data?.billing?.currentCycleTo
      const startNewCycleAfterRenew = data?.billing?.startNewCycleAfterRenew
      const endNewCycleAfterRenew = data?.billing?.endNewCycleAfterRenew
      const label = 'Thanh toán cho chu kỳ'

      if (currentCycleFrom && currentCycleTo && startNewCycleAfterRenew && endNewCycleAfterRenew) {
        if (currentCycleFrom === currentCycleTo) {
          return (
            <div className='font-medium'>
              {label} {currentCycleFrom} (từ {startNewCycleAfterRenew} đến {endNewCycleAfterRenew})
            </div>
          )
        }

        if (currentCycleFrom > currentCycleTo) {
          return (
            <div className='font-medium'>
              {label} {currentCycleTo} đến {currentCycleFrom} <br /> (từ {startNewCycleAfterRenew} đến{' '}
              {endNewCycleAfterRenew})
            </div>
          )
        }

        return (
          <div className='font-medium'>
            {label} {currentCycleFrom} đến {currentCycleTo} <br /> (từ {startNewCycleAfterRenew} đến{' '}
            {endNewCycleAfterRenew})
          </div>
        )
      }

      return (
        data?.billing?.currentCycle &&
        data?.billing?.billingDate &&
        data?.billing?.endDate && (
          <div className='text-end font-medium'>
            {label} {data?.billing?.currentCycle} <br />
            (từ {data?.billing?.billingDate} đến {data?.billing?.endDate})
          </div>
        )
      )
    }

    return (
      <div className='text-end font-medium'>
        Thanh toán phát sinh thay đổi ngày <br />
        {data?.billing?.billingDate}
      </div>
    )
  }

  const publishHandle = () => {
    selectedInvoice?.isBillingCode
      ? isThirdParty(invoiceDetail?.billItem)
        ? publishBillIsCart.mutate(selectedInvoice?.billingCode)
        : publishBill.mutate(selectedInvoice?.billingId)
      : publishBill.mutate(selectedInvoice?.billingId)
  }

  return (
    <>
      <div
        className={`bg-white py-0 transition-all ${selectedInvoice ? 'w-[520px] translate-x-0 pl-4 pr-5 opacity-100 sm:w-screen sm:translate-x-[-100vw]' : 'size-0 translate-x-full opacity-0 sm:translate-x-0'}`}
      >
        <Spin spinning={invoiceDetailLoading}>
          <div style={{ width: isDesktop ? 'auto' : 'calc(100vw - 32px)' }}>
            {/* Header */}
            <div className='flex p-2 sm:px-0'>
              <button
                type='button'
                className='inline-flex h-fit cursor-pointer items-center bg-inherit p-[10px] sm:py-2 sm:pl-0'
                onClick={() => dispatch(handleSetSelectedInvoice(undefined))}
              >
                <i className='onedx-arrow-left size-5' />
              </button>
              <div className='flex flex-1 flex-wrap justify-between sm:flex-nowrap'>
                <div className='flex w-full flex-col sm:w-4/5'>
                  <div className='flex flex-1 items-center text-base font-semibold'>
                    Chi tiết hóa đơn &lt;
                    {selectedInvoice?.isBillingCode
                      ? invoiceDetail?.payment?.billingCode
                      : invoiceDetail?.billing?.code}
                    &gt;
                  </div>
                  {CAN_ACTION_INVOICE && (
                    <div className='line-clamp-2 text-xs text-gray-8'>
                      Hóa đơn sẽ được tự động xuất trước ngày {publishInvoiceDate(invoiceDetail)}. Quý khách vui lòng
                      kiểm tra, chỉnh sửa nếu cần
                    </div>
                  )}
                </div>

                <div className='mt-4 flex sm:mt-0'>
                  {CAN_ACTION_INVOICE && (
                    <>
                      {isDesktop ? (
                        <Button
                          type='primary'
                          icon={<i className='onedx-download size-4' />}
                          iconPosition='start'
                          className='mr-4 gap-2 rounded-md px-3 py-1 font-medium'
                          onClick={() => setOpenPublishModal(true)}
                        >
                          Xuất hóa đơn
                        </Button>
                      ) : (
                        <Popover content='Xuất hóa đơn' placement='bottomRight'>
                          <button
                            type='button'
                            className='cursor-pointer bg-inherit px-2 py-[10px]'
                            onClick={() => setOpenPublishModal(true)}
                          >
                            <i className='onedx-download size-5' />
                          </button>
                        </Popover>
                      )}
                    </>
                  )}

                  {CAN_ACTION_INVOICE && (
                    <>
                      {isDesktop ? (
                        <Button
                          icon={<i className='onedx-edit size-4' />}
                          iconPosition='start'
                          className='gap-2 rounded-md border-none bg-sme-blue-1 px-3 py-1 text-sm font-medium text-sme-blue-7 hover:bg-blue-100'
                          onClick={() => setOpenEditModal(true)}
                        >
                          Chỉnh sửa hóa đơn
                        </Button>
                      ) : (
                        <Popover content='Chỉnh sửa' placement='bottomRight'>
                          <button
                            type='button'
                            className='cursor-pointer bg-inherit px-2 py-[10px]'
                            onClick={() => setOpenEditModal(true)}
                          >
                            <i className='onedx-edit size-5' />
                          </button>
                        </Popover>
                      )}
                    </>
                  )}
                </div>
              </div>
            </div>
            {/* Detail */}
            <div className='flex flex-col gap-3 sm:gap-2'>
              {/* Provider-info */}
              <div className='flex flex-col gap-3 p-4 sm:gap-5 sm:px-0 sm:pb-3'>
                <div className='text-sm font-semibold sm:border-l-2 sm:border-solid sm:border-l-sme-orange-7 sm:pl-2'>
                  Thông tin nhà cung cấp
                </div>
                <div className='flex flex-col gap-2'>
                  {selectedInvoice?.isBillingCode ? (
                    <>
                      <div className='flex justify-between text-sm'>
                        <div className='w-1/5 shrink-0 font-normal'>Tên công ty</div>
                        <div className='font-medium'>{invoiceDetail?.provider?.providerName}</div>
                      </div>
                      <div className='flex justify-between text-sm'>
                        <div className='font-normal'>Mã số thuế</div>
                        <div className='font-medium'>{invoiceDetail?.provider?.providerTaxCode}</div>
                      </div>
                    </>
                  ) : (
                    <>
                      <div className='flex justify-between text-sm'>
                        <div className='w-1/5 shrink-0 font-normal'>Họ tên</div>
                        <div className='font-medium'>{invoiceDetail?.developer?.developerName}</div>
                      </div>
                      <div className='flex justify-between text-sm'>
                        <div className='font-normal'>Số chứng thực</div>
                        <div className='font-medium'>{invoiceDetail?.developer?.repPersonalCertNumber}</div>
                      </div>
                    </>
                  )}
                  <div className='flex justify-between text-sm'>
                    <div className='w-1/5 shrink-0 font-normal'>Địa chỉ</div>
                    <div className='text-wrap font-medium' title={invoiceDetail?.provider?.providerAddress}>
                      {selectedInvoice?.isBillingCode
                        ? invoiceDetail?.provider?.providerAddress
                        : invoiceDetail?.developer?.address}
                    </div>
                  </div>
                </div>
              </div>
              {/* Customer-info */}
              <div className='flex flex-col gap-3 p-4 sm:gap-5 sm:px-0 sm:pb-3 sm:pt-2'>
                <div className='text-sm font-semibold sm:border-l-2 sm:border-solid sm:border-l-sme-orange-7 sm:pl-2'>
                  Thông tin khách hàng
                </div>

                {user.customerType === 'PERSONAL' ? (
                  <div className='flex flex-col gap-2'>
                    <div className='flex justify-between text-sm'>
                      <div className='w-1/5 shrink-0 font-normal'>Tên khách hàng</div>
                      <div className='font-medium'>
                        {selectedInvoice?.isBillingCode
                          ? invoiceDetail?.customer?.customerName ||
                            `${invoiceDetail?.customer?.customerFirstName} ${invoiceDetail?.customer?.customerLastName}`
                          : `${invoiceDetail?.customer?.lastName} ${invoiceDetail?.customer?.firstName}`}
                      </div>
                    </div>
                    <div className='flex justify-between text-sm'>
                      <div className='font-normal'>Số chứng thực</div>
                      <div className='font-medium'>
                        {selectedInvoice?.isBillingCode
                          ? invoiceDetail?.customer?.identityNo
                          : invoiceDetail?.customer?.repPersonalCertNumber}
                      </div>
                    </div>
                    <div className='flex justify-between text-sm'>
                      <div className='w-1/5 shrink-0 font-normal'>Địa chỉ</div>
                      <div
                        className='text-wrap font-medium'
                        title={
                          selectedInvoice?.isBillingCode
                            ? invoiceDetail?.customer?.customerAddress
                            : invoiceDetail?.customer?.address
                        }
                      >
                        {selectedInvoice?.isBillingCode
                          ? invoiceDetail?.customer?.customerAddress
                          : invoiceDetail?.customer?.address}
                      </div>
                    </div>
                    <div className='flex justify-between text-sm'>
                      <div className='font-normal'>Loại địa chỉ</div>
                      <div className='font-medium'>
                        {(selectedInvoice?.isBillingCode
                          ? invoiceDetail?.customer?.addressType
                          : invoiceDetail?.customer?.typeAddress) === ADDRESS_TYPE.HOME
                          ? 'Nhà riêng/ Chung cư'
                          : 'Cơ quan/ Công ty'}
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className='flex flex-col gap-2'>
                    <div className='flex justify-between text-sm'>
                      <div className='w-1/5 shrink-0 font-normal'>Tên công ty</div>
                      <div className='font-medium'>
                        {selectedInvoice?.isBillingCode
                          ? invoiceDetail?.customer?.companyName
                          : invoiceDetail?.customer?.smeName}
                      </div>
                    </div>
                    {selectedInvoice?.customer?.identityTypeHKD === IDENTITY_TYPE.TAX_CODE ? (
                      <div className='flex justify-between text-sm'>
                        <div className='font-normal'>Mã số thuế</div>
                        <div className='font-medium'>
                          {formatNormalizeTaxCodev2(
                            selectedInvoice?.isBillingCode
                              ? invoiceDetail?.customer?.customerTaxCode
                              : invoiceDetail?.customer?.taxNo
                          )}
                        </div>
                      </div>
                    ) : (
                      <div className='flex justify-between text-sm'>
                        <div className='font-normal'>Số chứng thực</div>
                        <div className='font-medium'>
                          {formatNormalizeTaxCodev2(
                            selectedInvoice?.isBillingCode
                              ? invoiceDetail?.customer?.identityNo
                              : invoiceDetail?.customer?.repPersonalCertNumber
                          )}
                        </div>
                      </div>
                    )}

                    <div className='flex justify-between text-sm'>
                      <div className='w-1/5 shrink-0 font-normal'>Địa chỉ</div>
                      <div
                        className='text-wrap font-medium'
                        title={
                          selectedInvoice?.isBillingCode
                            ? invoiceDetail?.customer?.customerAddress
                            : invoiceDetail?.customer?.address
                        }
                      >
                        {selectedInvoice?.isBillingCode
                          ? invoiceDetail?.customer?.customerAddress
                          : invoiceDetail?.customer?.address}
                      </div>
                    </div>
                  </div>
                )}
              </div>
              {/* Invoice-info */}
              <div className='flex flex-col gap-3 p-4 sm:gap-5 sm:px-0 sm:pb-3 sm:pt-2'>
                <div className='text-sm font-semibold sm:border-l-2 sm:border-solid sm:border-l-sme-orange-7 sm:pl-2'>
                  Thông tin thanh toán
                </div>
                <div className='flex flex-col gap-2'>
                  <div className='flex justify-between text-sm'>
                    <div className='font-normal'>Mã hoá đơn</div>
                    <div className='font-medium'>
                      {selectedInvoice?.isBillingCode
                        ? invoiceDetail?.payment?.billingCode
                        : invoiceDetail?.billing?.code}
                    </div>
                  </div>
                  <div className='flex justify-between text-sm'>
                    <div className='font-normal'>Mã đăng ký</div>
                    <div className='font-medium'>
                      {selectedInvoice?.isBillingCode
                        ? invoiceDetail?.payment?.registerCode
                        : invoiceDetail?.billing?.subscriptionCode}
                    </div>
                  </div>
                  <div className='flex justify-between text-sm'>
                    <div className='font-normal'>Trạng thái</div>
                    <div>
                      <InvoiceStatusTag
                        status={
                          selectedInvoice?.isBillingCode
                            ? invoiceDetail?.payment?.billingStatus
                            : invoiceDetail?.billing?.status
                        }
                      />
                    </div>
                  </div>
                  <div className='flex justify-between text-sm'>
                    <div className='font-normal'>Phương thức thanh toán</div>
                    <div className='font-medium'>
                      {selectedInvoice?.isBillingCode
                        ? renderPaymentMethod(
                            !invoiceDetail?.billing?.isCreate
                              ? PAYMENT_METHOD.VNPTPAY
                              : invoiceDetail?.payment?.paymentMethod
                          )
                        : renderPaymentMethodByString(
                            invoiceDetail?.billing?.isCreate ? invoiceDetail?.billing?.paymentMethod : 'VNPTPAY'
                          )}
                    </div>
                  </div>
                  {!!invoiceDetail?.billing?.isOneTime && (
                    <div className='flex justify-between text-sm'>
                      <div className='font-normal'>Kỳ thanh toán tiếp theo</div>
                      <div className='font-medium'>{invoiceDetail?.billing?.nextPaymentDate}</div>
                    </div>
                  )}
                  <div className='flex justify-between text-sm'>
                    <div className='font-normal'>Ngày yêu cầu thanh toán</div>
                    <div className='font-medium'>
                      {selectedInvoice?.isBillingCode
                        ? invoiceDetail?.payment?.currentPaymentDate
                        : invoiceDetail?.billing?.requirePaymentDate}
                    </div>
                  </div>
                  <div className='flex justify-between text-sm'>
                    <div className='font-normal'>Hạn thanh toán cuối cùng</div>
                    <div className='font-medium'>
                      {selectedInvoice?.isBillingCode
                        ? invoiceDetail?.payment?.finalPaymentDate
                        : invoiceDetail?.billing?.deadlinePayment}
                    </div>
                  </div>
                  <div className='flex justify-between text-sm'>
                    <div className='font-normal'>Ngày thanh toán</div>
                    <div className='font-medium'>
                      {selectedInvoice?.isBillingCode
                        ? invoiceDetail?.payment?.paymentDate
                        : invoiceDetail?.billing?.paymentDate}
                    </div>
                  </div>
                  {selectedInvoice?.isBillingCode &&
                    invoiceDetail?.billing?.variantDraftId &&
                    invoiceDetail?.billing?.isOneTime === 1 && (
                      <div className='flex justify-between text-nowrap text-sm'>
                        <div className='w-1/5 shrink-0 font-normal'>Ghi chú</div>
                        {invoiceDetail?.billing?.currentCycle &&
                          invoiceDetail?.billing?.billingDate &&
                          invoiceDetail?.billing?.endDate && (
                            <div className='font-medium'>
                              Thanh toán cho chu kỳ ${invoiceDetail?.billing?.currentCycle} <br />
                              (từ {invoiceDetail?.billing?.billingDate} đến {invoiceDetail?.billing?.endDate})
                            </div>
                          )}
                      </div>
                    )}

                  {!selectedInvoice?.isBillingCode && invoiceDetail?.billing?.billNoteMigration !== 'HIDE' && (
                    <div className='flex justify-between text-nowrap text-sm'>
                      <div className='w-1/5 shrink-0 font-normal'>Ghi chú</div>
                      <div className='font-medium'>{renderNote(invoiceDetail)}</div>
                    </div>
                  )}

                  {/* THANH TOÁN VỚI VNPTPAY */}
                  {[Status.PENDING_PAYMENT, Status.PAYMENT_EXPIRED].includes(invoiceDetail?.payment?.billingStatus) &&
                    [PaymentMethod.VNPTPAY, PaymentMethod.VNPTPAY_QR].includes(
                      invoiceDetail?.payment?.paymentMethod
                    ) && (
                      <div className='mt-2 flex w-full flex-col gap-3'>
                        <Button
                          type='primary'
                          className='w-full px-4 py-2'
                          loading={payMutate.isPending && paymentApply === paymentMethodEnum.VNPTPAY}
                          onClick={() => onClickPaymentMethod(paymentMethodEnum.VNPTPAY)}
                        >
                          <img src='/assets/images/pages/payment/vnpt-money.webp' className='size-4 object-cover' />
                          <div>Thanh toán với VNPT Pay</div>
                        </Button>
                        <Button
                          className='w-full border-none bg-[#E28800] px-4 py-2 text-white hover:opacity-85'
                          loading={payMutate.isPending && paymentApply === paymentMethodEnum.QR_CODE}
                          onClick={() => onClickPaymentMethod(paymentMethodEnum.QR_CODE)}
                        >
                          <i className='onedx-qr-code size-4' />
                          <div>Thanh toán bằng QR Code</div>
                        </Button>
                      </div>
                    )}
                </div>
              </div>
              {/* Service-info */}
              {((selectedInvoice?.isBillingCode && invoiceDetail?.billItem?.length > 0) ||
                (!selectedInvoice?.isBillingCode &&
                  invoiceDetail?.calculationDetail?.currentBill?.costIncurred?.length > 0)) && (
                <div className='flex flex-col gap-3 p-4 sm:gap-5 sm:px-0 sm:pb-3 sm:pt-2'>
                  <div className='text-sm font-semibold sm:border-l-2 sm:border-solid sm:border-l-sme-orange-7 sm:pl-2'>
                    Thông tin dịch vụ
                  </div>
                  {isDesktop ? (
                    <InvoiceServiceInfo
                      invoiceDetail={invoiceDetail}
                      refundOnClick={() => {
                        dispatch(handleSetSelectedInvoice(undefined))
                        dispatch(handleSetActiveTab('CREDIT_NOTES'))
                        setSelectedCreditNote(invoiceDetail?.creditNoteApplies[0])
                      }}
                      isBillingCode={selectedInvoice?.isBillingCode}
                    />
                  ) : (
                    <MobileInvoiceServiceInfo
                      list={
                        selectedInvoice?.isBillingCode
                          ? invoiceDetail?.billItem
                          : invoiceDetail?.calculationDetail?.currentBill?.costIncurred.filter(
                              (x: any) => x.finalAmountAfterTax >= 0
                            )
                      }
                      isBillingCode={selectedInvoice?.isBillingCode}
                      totalAfterTaxAmount={
                        selectedInvoice?.isBillingCode
                          ? invoiceDetail?.totalAfterTaxAmount
                          : invoiceDetail?.calculationDetail?.currentBill?.costIncurred?.reduce(
                              (total: number, e: any) => total + e.finalAmountAfterTax,
                              0
                            )
                      }
                      creditNoteApplies={invoiceDetail?.creditNoteApplies}
                      totalAfterTaxFinalAmount={
                        selectedInvoice?.isBillingCode
                          ? invoiceDetail?.totalAfterTaxFinalAmount
                          : invoiceDetail?.totalAmountAfterRefund
                      }
                      refundOnClick={() => {
                        dispatch(handleSetSelectedInvoice(undefined))
                        dispatch(handleSetActiveTab('CREDIT_NOTES'))
                        setSelectedCreditNote(invoiceDetail?.creditNoteApplies[0])
                      }}
                    />
                  )}
                </div>
              )}

              {/* eInvoice-info */}
              {((selectedInvoice?.isBillingCode && invoiceDetail?.einvoice?.length > 0) ||
                (!selectedInvoice?.isBillingCode && invoiceDetail?.einvoices?.length > 0)) && (
                <div className='flex flex-col gap-3 p-4 sm:gap-5 sm:px-0 sm:pb-0 sm:pt-2'>
                  <div className='text-sm font-semibold sm:border-l-2 sm:border-solid sm:border-l-sme-orange-7 sm:pl-2'>
                    Thông tin hóa đơn điện tử
                  </div>
                  {isDesktop ? (
                    <EinvoiceInfo
                      isLoading={viewFkeyMutate.isPending || downloadPDFFkeyMutate.isPending}
                      dataSource={selectedInvoice?.isBillingCode ? invoiceDetail?.einvoice : invoiceDetail?.einvoices}
                      CAN_VIEW_EINVOICE={CAN_VIEW_EINVOICE}
                      CAN_DOWNLOAD_EINVOICE={CAN_DOWNLOAD_EINVOICE}
                      downloadPdfFkey={downloadPdfFkey}
                      viewFkey={viewFkey}
                      isBillingCode={selectedInvoice?.isBillingCode}
                    />
                  ) : (
                    <MobileEInvoiceInfo
                      isLoading={viewFkeyMutate.isPending || downloadPDFFkeyMutate.isPending}
                      CAN_DOWNLOAD_EINVOICE={CAN_DOWNLOAD_EINVOICE}
                      CAN_VIEW_EINVOICE={CAN_VIEW_EINVOICE}
                      eInvoiceList={selectedInvoice?.isBillingCode ? invoiceDetail?.einvoice : invoiceDetail?.einvoices}
                      downloadPdfFkey={downloadPdfFkey}
                      viewFkey={viewFkey}
                    />
                  )}
                </div>
              )}
            </div>
          </div>
        </Spin>
      </div>
      <ModalConfirm
        open={openPublishModal}
        setOpen={setOpenPublishModal}
        cancelText='Hủy'
        okText='Xác nhận'
        icon={<i className='onedx-download size-6' />}
        onOk={publishHandle}
        isLoading={publishBill.isPending}
      >
        <div className='pb-6 pt-4 text-base font-semibold'>Bạn có chắc muốn xuất hoá đơn điện tử</div>
      </ModalConfirm>

      {/* Modal chỉnh sửa hóa đơn */}
      <ModalEditInvoice
        invoiceId={selectedInvoice?.billingId}
        open={openEditModal}
        setOpen={setOpenEditModal}
        invoiceDetail={invoiceDetail}
        refetchInvoiceDetail={refetchInvoiceDetail}
        isBillingCode={selectedInvoice?.isBillingCode}
      />

      {/* <QRCodeModal
        isOpenModal={openQRCodeModal}
        handleCloseModal={handleCloseModal}
        name='InvoiceDetail-Root'
        state={{
          totalQuantity: invoiceDetail?.billItem?.length,
          totalPrice: invoiceDetail?.totalAfterTaxAmount,
          shippingFee: 0,
          totalFinalPrice: invoiceDetail?.totalAfterTaxFinalAmount
        }}
        expiredTime={timeLeft}
        qrCodeImage={image}
        functions={{
          refetchQRCode
        }}
      /> */}

      <ImpQRCodeModal />
    </>
  )
}

export default InvoiceDetail
