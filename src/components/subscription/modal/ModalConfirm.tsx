import type { HTMLAttributes, ReactNode } from 'react'
import React from 'react'

import { <PERSON><PERSON>, <PERSON>dal, Spin } from 'antd'
import classNames from 'classnames'
import { defaultsDeep } from 'lodash'

interface ControlTitle {
  wrapperClassName?: HTMLAttributes<HTMLDivElement>['className']
  titleClassName?: HTMLAttributes<HTMLDivElement>['className']
  iconClassName?: HTMLAttributes<HTMLDivElement>['className']
  titleText?: string
}

export interface ModalConfirmProps {
  open: boolean
  // setOpen: React.Dispatch<React.SetStateAction<boolean>> // useState setter only
  setOpen: (open: boolean) => void
  cancelText?: string
  children: ReactNode
  okText?: string
  onOk?: () => void
  icon?: ReactNode
  width?: number
  isLoading?: boolean
  controlTitle?: ControlTitle
  controlFooter?: {
    wrapperClassName?: HTMLAttributes<HTMLDivElement>['className']
    okButtonClassName?: HTMLAttributes<HTMLButtonElement>['className']
    cancelButtonClassName?: HTMLAttributes<HTMLButtonElement>['className']
    render?: ReactNode
  }
}

export default function ModalConfirm({
  open,
  setOpen,
  cancelText = 'Hủy',
  children,
  okText = 'Xác nhận',
  onOk,
  icon,
  width = 384,
  isLoading = false,
  controlTitle,
  controlFooter
}: ModalConfirmProps) {
  const defaultControlTitle = {
    wrapperClassName: 'justify-between',
    titleClassName: 'text-base font-semibold text-text-neutral-strong',
    iconClassName: ' bg-sme-blue-2 text-sme-blue-8',
    titleText: ''
  }

  const defaultControlFooter = {
    wrapperClassName: 'gap-4 justify-end',
    okButtonClassName: 'w-30',
    cancelButtonClassName: 'w-30 bg-sme-blue-1 text-sme-blue-7'
  }

  const { wrapperClassName, titleClassName, iconClassName, titleText } = defaultsDeep(
    controlTitle || {},
    defaultControlTitle
  )

  const {
    wrapperClassName: footerWrapperClassName,
    okButtonClassName,
    cancelButtonClassName,
    render: footerRender
  } = defaultsDeep(controlFooter || {}, defaultControlFooter)

  return (
    <Modal
      width={width}
      closeIcon={false}
      open={open}
      onCancel={() => setOpen(false)}
      onOk={onOk}
      centered
      title={
        <div className={classNames('flex', wrapperClassName)}>
          <div className='flex items-center gap-2'>
            <div className={classNames('size-10 rounded-full p-2', iconClassName)}>{icon}</div>
            <div className={classNames(titleClassName)}>{titleText}</div>
          </div>
          <button
            type='button'
            className='flex w-fit cursor-pointer items-start bg-inherit'
            onClick={() => setOpen(false)}
          >
            <i className='onedx-close-icon size-6 text-icon-neutral-medium' />
          </button>
        </div>
      }
      footer={
        footerRender || (
          <div className={classNames('flex', footerWrapperClassName)}>
            <Button type='primary' loading={isLoading} onClick={onOk} className={okButtonClassName}>
              {okText}
            </Button>
            <Button
              type='text'
              loading={isLoading}
              className={classNames(cancelButtonClassName)}
              onClick={() => setOpen(false)}
            >
              {cancelText}
            </Button>
          </div>
        )
      }
    >
      <Spin spinning={isLoading}>{children}</Spin>
    </Modal>
  )
}
