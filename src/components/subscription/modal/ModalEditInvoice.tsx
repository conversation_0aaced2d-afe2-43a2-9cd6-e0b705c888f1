import React, { useEffect } from 'react'

import { useWatch } from 'antd/es/form/Form'

import { useDispatch, useSelector } from 'react-redux'

import { useMutation, useQuery } from '@tanstack/react-query'

import { Button, Checkbox, Form, Input, message, Modal, Radio } from 'antd'

import { useNewLocation } from '@/hooks/useNewLocation'

import smeSubscriptionInstance from '@/models/SmeSubscription'
import smeAddressInstance from '@/models/SmeAddress'
import SubscriptionManagement from '@/models/SubscriptionManagement'

import { useResponsive, useUser } from '@/hooks'

import {
  handleSetCustomerInfo,
  handleSetInvoiceAddressList,
  handleToggleInvoiceAddressList,
  selectCustomerInfo
} from '@/redux-store/slices/SubscriptionInvoiceSlice'

import { ADDRESS_TYPE, PAYMENT_METHOD } from '@/constants/subscriptions'
import { handleAddress } from '@/views/payment/utils/serviceUtils'
import { TYPE_EXPORT_INVOICE } from '@views/payment/constant/PaymentConstant'
import { convertCustomerInfo, renderPaymentMethod } from '@/utils/subscription'
import { validateCode, validateLengthTinNoRequire, validateRequireInput } from '@/validator'

import SmallTitle from '@/views/subscription/SmallTitle'
import { EmptyFormItem } from '@views/auth/register/components/RegisterPersonalForm'
import InvoiceStatusTag from '../InvoiceStatusTag'
import FormChooseAddress from '../form/FormChooseAddress'
import ModalInvoiceAddressList from './ModalInvoiceAddressList'
import ModalCreateInvoiceAddress from './ModalCreateInvoiceAddress'
import InvoiceServiceInfo from '../InvoiceServiceInfo'
import MobileInvoiceServiceInfo from '../mobile/MobileInvoiceServiceInfo'

interface Props {
  open: boolean
  setOpen: (value: boolean) => void
  invoiceDetail: any
  invoiceId: number
  refetchInvoiceDetail: () => Promise<any>
  isBillingCode: boolean
}

const ModalEditInvoice = ({ open, setOpen, invoiceDetail, invoiceId, refetchInvoiceDetail, isBillingCode }: Props) => {
  const { user } = useUser()
  const [form] = Form.useForm()
  const dispatch = useDispatch()
  const { isDesktop, isMobile } = useResponsive()
  const { provinceList, wardList, streetList, updateAddress } = useNewLocation()
  const customerInfo = useSelector(selectCustomerInfo)
  const IS_PERSONAL = user.customerType === 'PERSONAL'

  const typeAddress = useWatch('typeAddress', form)

  const handleCreateAddress = (field: any, option: any) => {
    handleAddress(field, option, form, 'address')
  }

  /* Set thông tin khách hàng + địa chỉ xuất hóa đơn */
  useEffect(() => {
    if (customerInfo) {
      const { provinceId, districtId, wardId } = customerInfo

      if (provinceId) updateAddress('provinceId', provinceId)
      if (districtId) updateAddress('districtId', districtId)
      if (wardId) updateAddress('wardId', wardId)

      form.setFieldsValue(customerInfo)
    }
  }, [customerInfo, form, updateAddress])

  /* Set thông tin nhà cung cấp */
  useEffect(() => {
    if (invoiceDetail) {
      const provider = {
        providerName: invoiceDetail?.provider?.providerName || invoiceDetail?.developer?.developerName,
        providerTaxCode: invoiceDetail?.provider?.providerTaxCode || invoiceDetail?.developer?.taxNo,
        providerAddress: invoiceDetail?.provider?.providerAddress || invoiceDetail?.developer?.address
      }

      form.setFieldsValue(provider)
    }
  }, [invoiceDetail, form])

  const insertMutation = useMutation({
    mutationKey: ['createAddress'],
    mutationFn: smeAddressInstance.createAddress,
    onSuccess: () => {
      message.success('Thêm mới địa chỉ thành công')
      refetch()
    },
    onError: () => message.error('Thêm mới địa chỉ thất bại')
  })

  const editInvoiceMutation = useMutation({
    mutationKey: ['editInvoice'],
    mutationFn: SubscriptionManagement.editBill,
    onSuccess: () => {
      message.success('Cập nhật hóa đơn thành công')
      refetchInvoiceDetail()
      setOpen(false)
    },
    onError: (e: any) => {
      if (e?.errorCode === 'error.invoice.authen.invalid') {
        message.error('Sai tài khoản xuất hoá đơn')
      } else if (e?.errorCode === 'error.invoice.xml.invalid') {
        message.error('Nội dung xml xuất không đúng')
      } else if (e?.errorCode === 'error.invoice.not.found.company') {
        message.error('Không tìm thấy công ty')
      } else if (e?.errorCode === 'error.invoice.pattern.serial.invalid') {
        message.error('Sai thông tin pattern hoặc serial')
      } else {
        message.error('Cập nhật hóa đơn không thành công!')
      }
    }
  })

  const handleClose = () => {
    dispatch(handleSetCustomerInfo(convertCustomerInfo(invoiceDetail?.customer)))
    setOpen(false)
  }

  const handleFinish = (data: any) => {
    if (data.saveAddress) {
      const createObj = {
        userId: user.id,
        smeName: data.smeName,
        firstName: data.firstName,
        lastName: data.lastName,
        tin: data.tin,
        repPersonalCertNumber: data.repPersonalCertNumber,
        provinceId: data.provinceId,
        districtId: data.districtId,
        wardId: data.wardId,
        streetId: data.streetId,
        typeAddress: data.typeAddress,
        address: data.address,
        type: TYPE_EXPORT_INVOICE,
        defaultLocation: 0
      }

      insertMutation.mutate(createObj)
    }

    const submitObj = {
      addressId: customerInfo?.id,
      smeName: data?.smeName,
      taxNo: data?.tin,
      personalCertNumber: data?.repPersonalCertNumber,
      address: data?.address,
      firstName: data?.firstName,
      lastName: data?.lastName,
      typeAddress: data?.typeAddress || ADDRESS_TYPE.HOME,
      customerDistrictId: data?.districtId,
      customerProvinceId: data?.provinceId,
      customerWardId: data?.wardId,
      customerStreetId: data?.streetId
    }

    editInvoiceMutation.mutate({ id: invoiceId, body: submitObj })
  }

  const { refetch } = useQuery({
    queryKey: ['getInvoiceAddressList'],
    queryFn: async () => {
      let res = await smeSubscriptionInstance.getListAddressByTypeAndUserId(TYPE_EXPORT_INVOICE, user.id)

      res = res.sort((a: any, b: any) => b.defaultLocation - a.defaultLocation)

      dispatch(handleSetInvoiceAddressList(res))

      return res
    },
    initialData: [],
    enabled: !!user.id
  })

  return (
    <>
      <Modal
        style={{ maxHeight: '100vh' }}
        width={isDesktop ? 632 : '100vw'}
        closeIcon={false}
        centered
        open={open}
        onOk={form.submit}
        title={
          <div className='flex justify-between'>
            <div className='text-base font-semibold'>Chỉnh sửa chi tiết hóa đơn</div>
            <button type='button' className='flex w-fit cursor-pointer items-start bg-inherit' onClick={handleClose}>
              <i className='onedx-close-icon size-6' />
            </button>
          </div>
        }
        footer={
          <div className='flex justify-end gap-4'>
            <Button type='primary' style={{ width: '120px' }} onClick={form.submit}>
              Lưu
            </Button>
            <Button
              className='border-none bg-gray-1 text-gray-8'
              type='text'
              style={{ width: '120px' }}
              onClick={handleClose}
            >
              Đóng
            </Button>
          </div>
        }
      >
        <Form form={form} layout='vertical' onFinish={handleFinish}>
          <div className='flex max-h-screen flex-col gap-3 overflow-scroll sm:gap-4'>
            {/* Thông tin nhà cung cấp */}
            <div className='flex flex-col pt-2'>
              <SmallTitle title='Thông tin nhà cung cấp' />
              <div className='mt-3 flex gap-6 sm:flex-col sm:gap-4'>
                <Form.Item name='providerName' label='Tên công ty' className='mb-3 w-1/2 sm:mb-0 sm:w-full'>
                  <Input disabled />
                </Form.Item>
                <Form.Item name='providerTaxCode' label='Mã số thuế' className='mb-3 w-1/2 sm:mb-4 sm:w-full'>
                  <Input disabled />
                </Form.Item>
              </div>
              <Form.Item name='providerAddress' label='Địa chỉ' className='mb-3 sm:mb-0'>
                <Input disabled />
              </Form.Item>
            </div>
            {/* Thông tin khách hàng  */}
            {IS_PERSONAL ? (
              <div className='flex flex-col pt-2'>
                <SmallTitle title='Thông tin khách hàng' className='mb-3' />
                {typeAddress === 1 && (
                  <Form.Item
                    name='smeName'
                    label='Tên doanh nghiệp'
                    className='my-3 sm:mb-4'
                    required
                    rules={[validateRequireInput('Tên doanh nghiệp không được bỏ trống')]}
                  >
                    <Input placeholder='Nhập tên doanh nghiệp' maxLength={200} />
                  </Form.Item>
                )}

                <EmptyFormItem label='Họ và tên' style={{ marginBottom: 0 }} required />
                <div className='flex items-center justify-between gap-6 sm:gap-4'>
                  <Form.Item
                    className='mb-3 w-1/2 sm:mb-4'
                    name='lastName'
                    rules={[validateRequireInput('Họ không được bỏ trống')]}
                    required
                  >
                    <Input maxLength={20} placeholder='Nhập họ' />
                  </Form.Item>
                  <Form.Item
                    className='mb-3 w-1/2 sm:mb-4'
                    name='firstName'
                    rules={[validateRequireInput('Tên không được bỏ trống')]}
                    required
                  >
                    <Input maxLength={20} placeholder='Nhập tên' />
                  </Form.Item>
                </div>
                <Form.Item
                  name='repPersonalCertNumber'
                  label='Nhập số chứng thực cá nhân'
                  className='mb-3'
                  required
                  rules={[validateRequireInput('Số chứng thực cá nhân không được bỏ trống')]}
                >
                  <Input placeholder='Nhập số chứng thực cá nhân' maxLength={30} />
                </Form.Item>
                <Form.Item
                  initialValue={0}
                  name='typeAddress'
                  label={<div className='text-sm font-semibold'>Loại địa chỉ</div>}
                  className='mb-3'
                >
                  <Radio.Group className='sm:flex sm:flex-col sm:gap-2'>
                    <Radio value={ADDRESS_TYPE.HOME}>Nhà riêng/ Chung cư</Radio>
                    <Radio value={ADDRESS_TYPE.COMPANY}>Cơ quan/ Công ty</Radio>
                  </Radio.Group>
                </Form.Item>
              </div>
            ) : (
              <div className='flex flex-col py-2'>
                <SmallTitle title='Thông tin khách hàng' />
                <Form.Item
                  name='smeName'
                  label='Tên doanh nghiệp'
                  className='my-3 sm:mb-4'
                  required
                  rules={[validateRequireInput('Tên doanh nghiệp không được bỏ trống')]}
                >
                  <Input placeholder='Nhập tên doanh nghiệp' maxLength={200} />
                </Form.Item>
                <Form.Item
                  name='tin'
                  label='Mã số thuế'
                  className='mb-2'
                  required
                  rules={[
                    validateRequireInput('Mã số thuế không được bỏ trống'),
                    validateLengthTinNoRequire(10, 13, 'Sai định dạng mã số thuế'),
                    validateCode('Không được chứa chữ cái tiếng Việt hoặc ký tự đặc biệt', /^[0-9]*$/)
                  ]}
                >
                  <Input placeholder='Nhập mã số thuế' maxLength={13} />
                </Form.Item>
              </div>
            )}
            {/* Địa chỉ xuất hóa đơn */}
            <div className='flex flex-col py-2'>
              <SmallTitle
                title='Thông tin địa chỉ xuất hoá đơn'
                rightPart={
                  isMobile && (
                    <Button
                      type='text'
                      className='border border-solid border-sme-blue-7 text-sme-blue-7'
                      onClick={() => dispatch(handleToggleInvoiceAddressList(true))}
                    >
                      Chọn địa chỉ
                    </Button>
                  )
                }
              />
              <FormChooseAddress
                provinceList={provinceList}
                wardList={wardList}
                streetList={streetList}
                updateAddress={updateAddress}
                handleCreateAddress={handleCreateAddress}
                addressTitle='Địa chỉ xuất hóa đơn'
              />
              <div className='flex justify-between'>
                <Form.Item name='saveAddress' className='mb-3 sm:mb-0' valuePropName='checked'>
                  <Checkbox>
                    <span className='ml-1.5'>Lưu vào sổ địa chỉ của doanh nghiệp</span>
                  </Checkbox>
                </Form.Item>
                {isDesktop && (
                  <Button
                    type='text'
                    className='border border-solid border-sme-blue-7 text-sme-blue-7'
                    onClick={() => dispatch(handleToggleInvoiceAddressList(true))}
                  >
                    Chọn địa chỉ
                  </Button>
                )}
              </div>
            </div>
            {/* Thông tin thanh toán */}
            <div className='flex flex-col gap-3 pt-2 sm:gap-4'>
              <SmallTitle title='Thông tin thanh toán' />
              <div className='flex flex-col gap-2 p-4 sm:gap-4'>
                <div className='flex justify-between text-sm'>
                  <div className='font-normal'>Mã hóa đơn</div>
                  <div className='font-medium'>{invoiceDetail?.payment?.billingCode}</div>
                </div>
                <div className='flex justify-between text-sm'>
                  <div className='font-normal'>Mã đăng ký</div>
                  <div className='font-medium'>{invoiceDetail?.billing?.subscriptionCode}</div>
                </div>
                <div className='flex justify-between text-sm'>
                  <div className='font-normal'>Trạng thái</div>
                  <div>
                    <InvoiceStatusTag status={invoiceDetail?.payment?.billingStatus} />
                  </div>
                </div>
                <div className='flex justify-between text-sm'>
                  <div className='font-normal'>Phương thức thanh toán</div>
                  <div className='font-medium'>
                    {renderPaymentMethod(
                      invoiceDetail?.billing?.isCreate
                        ? invoiceDetail?.payment?.paymentMethod || PAYMENT_METHOD[invoiceDetail?.billing?.paymentMethod]
                        : PAYMENT_METHOD.VNPTPAY
                    )}
                  </div>
                </div>
                <div className='flex justify-between text-sm'>
                  <div className='font-normal'>Kỳ thanh toán tiếp theo</div>
                  <div className='font-medium'>{invoiceDetail?.billing?.nextPaymentDate}</div>
                </div>
                <div className='flex justify-between text-sm'>
                  <div className='font-normal'>Ngày yêu cầu thanh toán</div>
                  <div className='font-medium'>{invoiceDetail?.billing?.requirePaymentDate}</div>
                </div>
                <div className='flex justify-between text-sm'>
                  <div className='font-normal'>Hạn thanh toán cuối cùng</div>
                  <div className='font-medium'>{invoiceDetail?.billing?.deadlinePayment}</div>
                </div>
                <div className='flex justify-between text-sm'>
                  <div className='font-normal'>Ngày thanh toán</div>
                  <div className='font-medium'>{invoiceDetail?.billing?.paymentDate}</div>
                </div>
                <div className='flex justify-between text-sm'>
                  <div className='w-1/5 shrink-0 font-normal'>Ghi chú</div>
                  <div className='font-medium'>
                    {invoiceDetail?.billing?.currentCycle &&
                      invoiceDetail?.billing?.billingDate &&
                      invoiceDetail?.billing?.endDate && (
                        <div className='text-end font-medium'>
                          Thanh toán cho chu kỳ {invoiceDetail?.billing?.currentCycle} <br />
                          (từ {invoiceDetail?.billing?.billingDate} đến {invoiceDetail?.billing?.endDate})
                        </div>
                      )}
                  </div>
                </div>
              </div>
            </div>
            {/* Thông tin dịch vụ */}
            <div className='flex flex-col gap-3 pt-2 sm:gap-4'>
              <SmallTitle title='Thông tin dịch vụ' />
              {isDesktop ? (
                <InvoiceServiceInfo isBillingCode={isBillingCode} invoiceDetail={invoiceDetail} />
              ) : (
                <div className='px-4 py-3'>
                  <MobileInvoiceServiceInfo
                    list={invoiceDetail?.billItem}
                    totalAfterTaxAmount={invoiceDetail?.totalAfterTaxAmount}
                    creditNoteApplies={invoiceDetail?.creditNoteApplies}
                    totalAfterTaxFinalAmount={invoiceDetail?.totalAfterTaxFinalAmount}
                    isBillingCode={isBillingCode}
                  />
                </div>
              )}
            </div>
          </div>
        </Form>
      </Modal>
      <ModalInvoiceAddressList />
      <ModalCreateInvoiceAddress />
    </>
  )
}

export default ModalEditInvoice
