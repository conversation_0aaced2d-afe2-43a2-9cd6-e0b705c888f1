import React, { memo } from 'react'

interface StepsSectionProps {
  current: number
  onChange: (step: number, isClickedNext: boolean) => void
  items: { key: string; title: string }[]
}

const StepsSection = memo(({ current, onChange, items }: StepsSectionProps) => (
  <div className='flex justify-end'>
    <div className='flex items-center'>
      {items.map((step, index) => {
        const isActive = index === current
        const isCompleted = index < current
        const isClickable = typeof onChange === 'function'

        return (
          <>
            <div
              key={step.key}
              className={`flex min-w-0 items-center justify-center ${isClickable ? 'cursor-pointer hover:opacity-80' : ''}`}
              onClick={() => {
                if (isClickable) onChange?.(index, false)
              }}
            >
              <div className='flex items-center'>
                <div
                  className={`caption-12-medium mr-2 flex size-7 shrink-0 items-center justify-center rounded-full ${
                    isActive || isCompleted ? 'bg-bg-primary-default text-white' : 'bg-gray-1 text-black'
                  }`}
                >
                  {index + 1}
                </div>
                <span className='caption-12-medium truncate'>{step.title}</span>
              </div>
            </div>
            {index < items.length - 1 && <i className='onedx-chevron-right mx-3 size-4 text-main' />}
          </>
        )
      })}
    </div>
  </div>
))

StepsSection.displayName = 'StepsSection'

export default StepsSection
