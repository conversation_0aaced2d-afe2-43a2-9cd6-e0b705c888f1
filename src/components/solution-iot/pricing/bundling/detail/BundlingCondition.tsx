import React from 'react'

import { Collapse } from 'antd'

import { CUSTOMER_TYPE_NAME } from '@/constants/convert'

import { WrapSection } from '@components/custom-field/common'
import { DetailInfoCustomField } from '@components/custom-field/selectors'
import { BUNDLING_STEP, BUNDLING_TAB } from '@views/solution-iot/pricing/detail/bundling-info/BundlingInfo'

// Điều kiện áp dụng - Chi tiết gói dịch vụ
export const BundlingCondition = (props: any) => {
  const { data, editButton } = props

  const fullCustomerType = data?.applyCondition?.customerTypes?.map((item: any) => CUSTOMER_TYPE_NAME[item]).join(', ')

  const customerType =
    data?.applyCondition?.customerTypes?.length > 2
      ? `${data.applyCondition?.customerTypes
          .slice(0, 2)
          .map((item: any) => CUSTOMER_TYPE_NAME[item])
          .join(', ')} +...`
      : data?.applyCondition?.customerTypes?.map((item: any) => CUSTOMER_TYPE_NAME[item]).join(', ')

  const fullListDomain = data?.lstDomain?.map((item: any) => item.name).join(', ')

  const listDomain =
    data?.lstDomain?.length > 2
      ? `${data.lstDomain
          .slice(0, 2)
          .map((item: any) => item.name)
          .join(', ')} +...`
      : data?.lstDomain?.map((item: any) => item.name).join(', ')

  const fullListCategory = data?.lstCategory?.map((item: any) => item.name).join(', ')

  const listCategory =
    data?.lstCategory?.length > 2
      ? `${data.lstCategory
          .slice(0, 2)
          .map((item: any) => item.name)
          .join(', ')} +...`
      : data?.lstCategory?.map((item: any) => item.name).join(', ')

  return (
    <WrapSection
      title='Đối tượng áp dụng'
      rightContent={editButton?.(BUNDLING_STEP.GENERAL_INFORMATION, BUNDLING_TAB.BUNDLING_CONDITION)}
    >
      <div className='grid grid-cols-4 gap-4'>
        <DetailInfoCustomField label='Đối tượng khách hàng' value={customerType} valueTooltip={fullCustomerType} />
        <DetailInfoCustomField label='Lĩnh vực áp dụng' value={listDomain} valueTooltip={fullListDomain} />
        <DetailInfoCustomField label='Danh mục sản phẩm' value={listCategory} valueTooltip={fullListCategory} />
      </div>
      {/*<FormItemText*/}
      {/*  label='Đối tượng khách hàng'*/}
      {/*  name='customerTypeCode'*/}
      {/*  value={(value: string[]) => value?.map(item => CUSTOMER_TYPE_NAME[item]).join(', ')}*/}
      {/*/>*/}
      <Collapse
        ghost
        className='space-y-4'
        expandIconPosition='end'
        defaultActiveKey={['condition']}
        items={[
          {
            key: 'condition',
            className: 'rounded-lg bg-[#02173C06]',
            classNames: { header: 'text-sm font-semibold text-gray-8', body: 'pt-0' },
            label: 'Áp dụng điều kiện bổ sung',
            children: (
              <div className='grid w-full grid-cols-3 gap-4 border-t border-solid border-neutral-200 pt-4'>
                <DetailInfoCustomField
                  label='Tỉnh thành'
                  value={data?.applyCondition?.province?.map((item: any) => item?.name).join('/ ')}
                />
                <DetailInfoCustomField
                  label='Phường/Xã'
                  value={data?.applyCondition?.ward?.map((item: any) => item?.name).join('/ ')}
                />

                <DetailInfoCustomField
                  label='Phố/đường'
                  value={data?.applyCondition?.street?.map((item: any) => item?.name).join('/ ')}
                />
              </div>
            )
          }
        ]}
      />
    </WrapSection>
  )
}
