/* eslint-disable no-nested-ternary */
import React, { useState } from 'react'

import { useSearchParams } from 'next/navigation'

import classnames from 'classnames'

import { Button, Modal, Radio, Space } from 'antd'
import { useQuery } from '@tanstack/react-query'

import ContactFormModal from '@views/enterprise/contact-customer/ContactFormModal'

import SystemParam from '@/models/SystemParam'
import type { NotificationInfoType } from '@/types/notificationModalTypes'
import IconWrapper from '@components/icon-wrapper/IconWrapper'

interface Props {
  visibleModal: boolean
  setVisibleModal: any
  infoModal: NotificationInfoType
  layoutLogin?: any
  isBack?: boolean
  onFinish?: any
}

type SystemConfig = {
  paramHotlineValue: string
}

export const METHOD_SEND_OTP = {
  EMAIL: 'EMAIL',
  SMS: 'SMS'
}

const PARTNER = 'PARTNER'

export default function DevModalNotification({
  visibleModal,
  setVisibleModal,
  infoModal,
  layoutLogin,
  isBack = false,
  onFinish
}: Props) {
  const [verificationMethod, setVerificationMethod] = useState(METHOD_SEND_OTP.EMAIL)
  const [showContact, setShowContact] = useState(false)

  const searchParams = useSearchParams()

  const {
    iconType = 'SUCCESS',
    title,
    subTitle,
    redirectPage,
    textButton,
    typeButton = 'primary',
    isContact,
    isCloseModal,
    closeModalPosition = 'second',
    closeModalTextButton,
    closeModalTypeButton = 'primary',
    haveHotline,
    handleConfirm,
    handleConfirmUpdate,
    handleConfirmSendOTP,
    methodOTP,
    handleCancel,
    customerType,
    width = 450
  } = infoModal

  // API Cấu hình hệ thống
  const { data: dataSystemHotline } = useQuery({
    queryKey: ['system-config-hotline'],
    queryFn: async () => {
      return await SystemParam.getSystemHotline()
    }
  })

  const closeModal = () => {
    setVisibleModal(false)

    if (!isBack && layoutLogin) {
      if (showContact) setShowContact(false)
    } else if (!layoutLogin && searchParams.get('mst') === null && redirectPage) {
      window.location.href = redirectPage
    }

    handleConfirm && handleConfirm({ isConfirmRegister: true, methodSendOTP: verificationMethod })

    handleConfirmUpdate && handleConfirmUpdate()

    handleConfirmSendOTP && handleConfirmSendOTP(verificationMethod)

    onFinish && onFinish()
  }

  const systemConfig = dataSystemHotline?.paramHotlineValue as SystemConfig

  // region Return
  return (
    <Modal
      centered
      open={visibleModal}
      onCancel={() => {
        setVisibleModal(false)
      }}
      footer={null}
      closable={false}
      maskClosable={false}
      width={width}
      className='rounded-none'
      title={
        <div className='mt-[4px] flex items-center justify-between pb-3'>
          <div className='flex items-center justify-center gap-4'>
            <IconWrapper iconType={iconType} size={40} />
            <div className='text-base font-semibold leading-6 tracking-wider'>{title}</div>
          </div>
          {/*icon close*/}
          <i
            className='onedx-close-icon size-6 cursor-pointer text-text-neutral-medium'
            onClick={() => {
              setVisibleModal(false)
              handleCancel && handleCancel()
            }}
          />
        </div>
      }
    >
      {showContact ? (
        <ContactFormModal
          isModalVisible={showContact}
          setIsModalVisible={setShowContact}
          customerType={customerType}
          createdSource={PARTNER}
        />
      ) : (
        <div>
          <div className='body-14-regular py-[24px]'>
            <div className='text-base font-medium leading-6 tracking-wider'>{subTitle}</div>
            {haveHotline && <>{systemConfig}</>}
            <div>
              {methodOTP && (
                <div>
                  <Radio.Group
                    className='float-left mb-4'
                    onChange={e => setVerificationMethod(e.target.value)}
                    value={verificationMethod}
                  >
                    <Space direction='vertical' className='mt-4'>
                      <Radio value={METHOD_SEND_OTP.EMAIL}>
                        <div className='flex justify-between gap-4'>
                          <div>Email</div>
                          {verificationMethod === METHOD_SEND_OTP.EMAIL ? <div>{methodOTP.emailMask}</div> : null}
                        </div>
                      </Radio>
                      <Radio value={METHOD_SEND_OTP.SMS}>
                        <div className='flex justify-between gap-4'>
                          <div>Số điện thoại</div>
                          {verificationMethod === METHOD_SEND_OTP.SMS ? (
                            <div className={methodOTP.phoneMask === '' ? 'text-red-600' : ''}>
                              {methodOTP.phoneMask !== '' ? methodOTP.phoneMask : 'Số điện thoại không hợp lệ'}
                            </div>
                          ) : null}
                        </div>
                      </Radio>
                    </Space>
                  </Radio.Group>
                </div>
              )}
            </div>
          </div>
          <div className='flex w-full justify-center justify-items-end space-x-6 py-[24px]'>
            {/* Nút đóng Modal */}
            {isCloseModal && closeModalPosition === 'first' && (
              <Button
                className={classnames(
                  'body-14-regular w-[195px] cursor-pointer rounded-none py-2',
                  closeModalTypeButton === 'secondary'
                    ? 'border-primary bg-white text-primary'
                    : 'bg-primary text-white'
                )}
                onClick={() => {
                  setVisibleModal(false)
                  handleCancel && handleCancel()
                }}
              >
                {closeModalTextButton}
              </Button>
            )}
            <Button
              className={classnames(
                `body-14-regular cursor-pointer rounded-none py-2 ${isContact || isCloseModal ? 'w-[195px]' : 'w-full'}`,
                typeButton === 'secondary' ? 'border-primary bg-white text-primary' : 'bg-primary text-white'
              )}
              onClick={closeModal}
              disabled={methodOTP?.phoneMask === '' && verificationMethod === METHOD_SEND_OTP.SMS}
            >
              {textButton}
            </Button>
            {/* Nút Liên hệ */}
            {isContact && (
              <Button
                onClick={() => setShowContact(true)}
                className='body-14-regular w-30 cursor-pointer rounded-lg border-none bg-bg-primary-default py-2 text-white'
              >
                Liên hệ
              </Button>
            )}
            {/* Nút đóng Modal */}
            {isCloseModal && closeModalPosition === 'second' && (
              <Button
                className={classnames(
                  'body-14-regular w-[195px] cursor-pointer rounded-none py-2',
                  closeModalTypeButton === 'secondary'
                    ? 'border-primary bg-white text-primary'
                    : 'bg-primary text-white'
                )}
                onClick={() => {
                  setVisibleModal(false)
                  handleCancel && handleCancel()
                }}
              >
                {closeModalTextButton}
              </Button>
            )}
          </div>
        </div>
      )}
    </Modal>
  )
  // endregion
}
