// Types representing the API response structure
export interface IParamsWarehouseType {
  value?: string
  isName?: number
  isCode?: number
  description?: string
  warehouseIds?: number[]
  status?: string
  startTime?: number | ''
  endTime?: number | ''
  page?: number
  size?: number
  sort?: string
}
export interface IPartnerParams {
  page: number
  size: number
  isEmail: number
  isName?: number
  isPhoneNumber: number
  isCode?: number
  customerType?: any
  capabilities?: any
  provinceIds?: any
  taxCodes?: any
  status?: any
  state?: any
  repPersonalCertNumbers?: any
}

export interface IParamsWarehouseHistory {
  value?: string
  isCode?: number
  startTime?: number | ''
  endTime?: number | ''
  syncStatus?: string
  apiTypes?: string
  warehouseTypeIdList?: number[]
  partnerIdList?: number[]
  warehouseIdList?: number[]
  page?: number
  size?: number
  sort?: string
}

export interface IParamsHistoryDetail {
  value?: string
  isSku?: number
  isProductName?: number
  startTime?: number | ''
  endTime?: number | ''
  statusLst?: string
  actionTypeLst?: string
  warehouseIdStr?: string
  warehouseTypeIdList?: number[]
  page?: number
  size?: number
  sort?: string
}

export interface IParamsVirtualWarehouse {
  value?: string
  isName?: number
  isCode?: number
  status?: string
  partnerIds?: number[]
  typeIds?: number[]
  provinceId?: number[]
  districtId?: number[]
  wardId?: number[]
  streetId?: number[]
  provinceIds?: number[]
  districtIds?: number[]
  wardIds?: number[]
  streetIds?: number[]
  fullAddress?: string
  zipCode?: string
  contactName?: string
  contactPhone?: string
  contactEmail?: string
  page?: number
  size?: number
  sort?: string
}

export interface IParamsConnectionConfig {
  value?: string
  isName?: number
  isCode?: number
  warehouseIdList?: string
  warehouseTypeIdList?: string[]
  typeList?: string[]
  partnerIdList?: string[]
  connectionMethodList?: string[]
  page?: number
  size?: number
  sort?: string
}

export interface IParamsCurrentInventory {
  value?: string
  isName?: number
  isCode?: number
  startTime?: number | ''
  endTime?: number | ''
  stockStatus?: string
  page?: number
  size?: number
  sort?: string
}
