// Type Imports
import { ConfigProvider } from 'antd'

import type { ThemeConfig } from 'antd/lib'

import type { ChildrenType } from '@core/types'

// Component Imports
import BlankLayoutWrapper from '@/@layouts/BlankLayoutWrapper'
import Providers from '@components/Providers'

// Util Imports
import { getSystemMode } from '@core/utils/serverHelpers'
import { baseColorLight, colors } from '@/utils/colors'
import LocalStateProvider from '@/context/LocalStateContext'

type Props = ChildrenType

/**
 * @see themeAntd
 */
const Layout = async ({ children }: Props) => {
  // Vars
  const direction = 'ltr'
  const systemMode = await getSystemMode()

  const partnerManagementTheme: ThemeConfig = {
    components: {
      Form: {
        verticalLabelPadding: '0 0 8px',
        itemMarginBottom: 0,
        inlineItemMarginBottom: 20,
        fontSize: 14,
        labelFontSize: 12
      },
      Input: {
        paddingBlock: 8,
        paddingInline: 12
      },
      Select: {
        optionHeight: 36,
        controlHeight: 36,
        optionLineHeight: 1.25,
        optionPadding: '8px 12px'
      },
      Tabs: {
        verticalItemMargin: '0',
        // Color settings
        inkBarColor: baseColorLight['gray-6'],
        itemSelectedColor: baseColorLight['gray-6'],
        itemHoverColor: baseColorLight['gray-5']
        // itemActiveColor: colors.primary600
      },
      Typography: {
        colorTextDisabled: 'rgb(57,72,103)'
      },
      Checkbox: {
        borderRadiusSM: 4,
        lineWidth: 1.5,
        colorBorder: baseColorLight['gray-alpha-3']
      },
      Radio: {
        wrapperMarginInlineEnd: 40
      },
      Card: {
        bodyPadding: 16,
        padding: 0,
        borderRadiusLG: 12
      },
      Table: {
        headerColor: baseColorLight['gray-6'],
        headerBg: baseColorLight['gray-1'],
        rowSelectedBg: baseColorLight['gray-alpha-1'],
        rowSelectedHoverBg: baseColorLight['gray-alpha-2'],
        rowHoverBg: baseColorLight['gray-1']
      },
      DatePicker: {
        controlHeight: 36
      }
    },
    token: {
      margin: 0,
      borderRadius: 8,
      lineHeight: 1.25,
      fontFamily: 'Inter, sans-serif',
      colorPrimary: colors.primary600,
      colorPrimaryHover: colors.primary500,
      colorPrimaryActive: colors.primary700,
      colorPrimaryBorder: colors.primary600,
      colorText: baseColorLight['gray-11'],
      colorTextDescription: baseColorLight['gray-9'],
      colorTextPlaceholder: baseColorLight['gray-6'],
      colorTextDisabled: baseColorLight['gray-6'],
      colorBorder: baseColorLight['gray-alpha-3'],
      boxShadow: '0 0 0 0 rgba(165, 163, 174, 0.00), 0 4px 16px 0 rgba(165, 163, 174, 0.30)'
    }
  }

  return (
    <ConfigProvider theme={partnerManagementTheme}>
      <Providers direction={direction}>
        <LocalStateProvider>
          <BlankLayoutWrapper systemMode={systemMode} postHrefMessage>
            {children}
          </BlankLayoutWrapper>
        </LocalStateProvider>
      </Providers>
    </ConfigProvider>
  )
}

export default Layout
