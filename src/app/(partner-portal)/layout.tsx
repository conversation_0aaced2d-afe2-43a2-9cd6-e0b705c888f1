// Type Imports
import { cache } from 'react'

import { headers } from 'next/headers'

import { ConfigProvider } from 'antd'

import type { Metadata } from 'next'

import type { ChildrenType } from '@core/types'

// Component Imports
import BlankLayoutWrapper from '@/@layouts/BlankLayoutWrapper'
import Providers from '@components/Providers'

// Util Imports
import seoAdminInstance from '@/models/SEOAdmin'
import { defaultSeoConfig, homepageSeoConfig, hostName } from '@/utils/SEO/seoConfigs'
import { getSystemMode } from '@core/utils/serverHelpers'

import { ChildrenWrapper } from '@components/layout/wrapper/ChildrenWrapper'
import BlankLayout from '@layouts/BlankLayout'
import LayoutWrapper from '@layouts/LayoutWrapper'

type Props = ChildrenType

const getSEOData = cache(async (typeCode: string) => {
  try {
    const data = await seoAdminInstance.getSEOSMEDetail({ typeCode })

    return data || null
  } catch (error) {
    return null
  }
})

export async function generateMetadata(): Promise<Metadata> {
  if (!hostName.includes('onesme.vn')) return defaultSeoConfig()

  const headersList = await headers()
  const pathName = headersList.get('x-pathname') ?? '/home'
  const seoDTO = await getSEOData('CAU_HINH_TRANG_CHU')

  return seoDTO ? homepageSeoConfig(seoDTO, pathName) : defaultSeoConfig()
}

const Layout = async ({ children }: Props) => {
  // Vars
  const direction = 'ltr'
  const systemMode = await getSystemMode()

  const headersList = await headers()
  const pathName = headersList.get('x-pathname')
  const isHomePage = !!pathName && pathName.includes('/partner-portal/home')

  return (
    <>
      {isHomePage ? (
        <Providers direction={direction}>
          <LayoutWrapper
            systemMode={systemMode}
            verticalLayout={<ChildrenWrapper>{children}</ChildrenWrapper>}
            horizontalLayout={<BlankLayout systemMode={systemMode}>{children}</BlankLayout>}
          />
        </Providers>
      ) : (
        <ConfigProvider
          theme={{
            token: {
              fontFamily: 'Inter, sans-serif'
            }
          }}
        >
          <Providers direction={direction}>
            <BlankLayoutWrapper systemMode={systemMode}>{children}</BlankLayoutWrapper>
          </Providers>
        </ConfigProvider>
      )}
    </>
  )
}

export default Layout
