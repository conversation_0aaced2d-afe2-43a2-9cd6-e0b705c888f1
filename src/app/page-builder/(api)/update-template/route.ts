import fs from 'fs'
import path from 'path'

import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { templateName, content } = await request.json()

    if (!templateName || !content) {
      return NextResponse.json({ message: 'Template name and content are required' }, { status: 400 })
    }

    // Mapping template names to file paths
    const templateFileMap: Record<string, string> = {
      home: 'homePageBuilderTemplate.json',
      enterprise: 'enterprisePageBuilderTemplate.json',
      'house-hold': 'householdPageBuilderTemplate.json',
      personal: 'personalPageBuilderTemplate.json',
      'home-affiliate-iot': 'templates/homeAffiliateIoTPageBuilderTemplate.json',
      'home-affiliate-onesme': 'templates/homeAffiliateSmePageBuilderTemplate.json',
      'home-affiliate-genz': 'templates/homeAffiliateGenzPageBuilderTemplate.json',
      'home-affiliate-household': 'templates/homeAffiliateHouseholdPageBuilderTemplate.json',
      'home-affiliate-teen': 'templates/homeAffiliateTeenPageBuilderTemplate.json'
    }

    const fileName = templateFileMap[templateName]

    if (!fileName) {
      return NextResponse.json({ message: 'Invalid template name' }, { status: 400 })
    }

    // Construct file path
    const filePath = path.join(process.cwd(), 'src', 'views', 'page-builder', 'assets', fileName)

    // Write content to file
    // Kiểm tra nếu content đã là string thì không stringify lại
    const contentToWrite = typeof content === 'string' ? content : JSON.stringify(content, null, 2)

    fs.writeFileSync(filePath, contentToWrite, 'utf8')

    return NextResponse.json({
      message: `Template file ${fileName} updated successfully`,
      filePath: fileName
    })
  } catch (error) {
    console.error('Error updating template file:', error)

    return NextResponse.json({ message: 'Internal server error' }, { status: 500 })
  }
}
