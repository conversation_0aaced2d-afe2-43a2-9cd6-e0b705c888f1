export const tagStatus = {
  UNAPPROVED: {
    color: '#f3bf1b',
    text: 'Chưa duyệt',
    value: 'UNAPPROVED'
  },
  APPROVED: {
    color: '#5ab98d',
    text: 'Đã duyệt',
    value: 'APPROVED'
  },
  AWAITING_APPROVAL: {
    color: '#808bc0',
    text: 'Chờ duyệt',
    value: 'AWAITING_APPROVAL'
  },
  REJECTED: {
    color: 'error',
    text: 'Từ chối',
    value: 'REJECTED'
  }
}

export const customerTypeSelect = [
  { label: 'Tất cả', value: 'UNSET' },
  { label: 'Doanh nghiệp', value: 'ENTERPRISE' },
  { label: 'Hộ kinh doanh', value: 'HOUSE_HOLD' },
  { label: 'Cá nhân', value: 'PERSONAL' }
]

export const displayedSelect = [
  {
    value: 'UNSET',
    label: 'Tất cả'
  },
  {
    value: 'VISIBLE',
    label: '<PERSON><PERSON><PERSON> thị'
  },
  {
    value: 'INVISIBLE',
    label: 'Ẩn'
  }
]

export const approvalStatusOptions = [
  {
    value: 'UNSET',
    label: 'Tất cả'
  },
  {
    value: 'APPROVED',
    label: 'Đã duyệt'
  },
  {
    value: 'UNAPPROVED',
    label: 'Chưa duyệt'
  },
  {
    value: 'AWAITING_APPROVAL',
    label: 'Chờ duyệt'
  },
  {
    value: 'REJECTED',
    label: 'Từ chối'
  }
]

export const multiSubSelect = [
  { label: 'Tất cả', value: -1, labelAll: 'Multisub: Tất cả' },
  { label: 'Bật', value: 1, labelAll: 'Multisub: Bật' },
  { label: 'Tắt', value: 0, labelAll: 'Multisub: Tắt' }
]

const defaultService = {
  customerCode: 'UNSET',
  allowMultiSub: -1
}

const defaultServiceList = {
  status: 'UNSET',
  displayed: 'UNSET'
}

export const devDefaultFilter = {
  ...defaultService,
  ...defaultServiceList
}

export const adminDefaultFilter = {
  ...defaultService,
  ...defaultServiceList,
  serviceOwner: 'UNSET',
  categoriesId: -1
}

export const adminDraftDefaultFilter = {
  customerType: 'UNSET',
  allowMultiSub: -1,
  serviceOwner: 'UNSET',
  categoriesId: -1
}

export const pageSizeOptions = [
  { label: '10 / trang', value: 10 },
  { label: '20 / trang', value: 20 },
  { label: '50 / trang', value: 50 }
]

export const serviceCommonOption = [
  {
    name: 'customerCode',
    label: 'Đối tượng khách hàng',
    options: customerTypeSelect
  },
  {
    name: 'allowMultiSub',
    label: 'Multisub',
    options: multiSubSelect
  }
]

export const serviceListCommonOption = [
  {
    name: 'displayed',
    label: 'Trạng thái hiển thị',
    options: displayedSelect
  },
  {
    name: 'status',
    label: 'Trạng thái duyệt',
    options: approvalStatusOptions
  }
]

export const serviceOwnerOptions = [
  {
    value: -1,
    label: 'Tất cả'
  },
  {
    value: 0,
    label: 'VNPT'
  },
  {
    value: 1,
    label: 'VNPT Partner'
  },
  {
    value: 2,
    label: 'Third party'
  }
]

export const serviceOwnerStringOptions = [
  {
    value: 'UNSET',
    label: 'Tất cả'
  },
  {
    value: 'VNPT',
    label: 'VNPT'
  },
  {
    value: 'PARTNER',
    label: 'VNPT Partner'
  },
  {
    value: 'THIRD_PARTY',
    label: 'Third party'
  }
]

export const adminFilterOptions = [
  ...serviceCommonOption,
  ...serviceListCommonOption,
  {
    name: 'providerType',
    label: 'Đơn vị phát triển',
    options: serviceOwnerOptions
  }
]

export const adminDraftFilterOptions = [
  {
    name: 'customerType',
    label: 'Đối tượng khách hàng',
    options: customerTypeSelect
  },
  {
    name: 'allowMultiSub',
    label: 'Multisub',
    options: multiSubSelect
  },
  {
    name: 'serviceOwner',
    label: 'Đơn vị phát triển',
    options: serviceOwnerStringOptions
  }
]

export const checkboxOptions = [
  { label: 'Tên dịch vụ', value: 'name', status: 'disabled' },
  { label: 'Developer', value: 'developer', status: 'disabled' },
  { label: 'Trạng thái duyệt', value: 'status' },
  { label: 'Thời gian cập nhật', value: 'updatedTime' },
  { label: 'Multisub', value: 'allowMultiSub' },
  { label: '', value: 'subscriptionNumber' }
]

export enum CUSTOMER_TYPE {
  ALL = 'ALL',
  ENTERPRISE = 'ENTERPRISE',
  HOUSE_HOLD = 'HOUSE_HOLD',
  PERSONAL = 'PERSONAL'
}

export enum DISPLAYED {
  UNSET = -1,
  VISIBLE = 1,
  INVISIBLE = 0
}

export enum APPROVAL_STATUS {
  UNSET = 'UNSET',
  APPROVED = 'APPROVED',
  UNAPPROVED = 'UNAPPROVED',
  AWAITING_APPROVAL = 'AWAITING_APPROVAL',
  REJECTED = 'REJECTED'
}

export const CYCLE_TYPE_MAPPING = {
  DAY: 1,
  MONTH: 3,
  YEAR: 4
}

export const CYCLE_FREQUENCY_MAPPING = {
  DAILY: 0,
  WEEKLY: 1,
  MONTHLY: 2,
  YEARLY: 3
}

export const CYCLE_TYPE_NUMERIC_MAPPING: Record<number | string, 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'YEARLY'> = {
  0: 'DAILY',
  1: 'WEEKLY',
  2: 'MONTHLY',
  3: 'YEARLY'
}
