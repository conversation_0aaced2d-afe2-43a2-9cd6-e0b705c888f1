'use client'

import React, { useCallback, useEffect, useState } from 'react'

import { useRouter, usePathname } from 'next/navigation'

import { Editor, Frame } from '@craftjs/core'
import { Button, Form, Input, message, Modal, Spin } from 'antd'
import { useDispatch, useSelector } from 'react-redux'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import isEqual from 'lodash/isEqual'

import { resolver } from '@components/custom-field/selectors'
import createPricing from '@assets/custom-field/pricing/createPricing.json'
import type { RootState } from '@/redux-store'
import { updateInfoCustomField } from '@/redux-store/slices/customFieldSlice'
import { useUser } from '@/hooks'
import DevPricing from '@/models/DevPricing'
import {
  convertToFormInputFromApi,
  convertToPricingPlan,
  handlePricingError,
  toNumber
} from '@/views/custom-field/editor/sections/create-service/price-list/utils'
import { convertConfiguration } from '@/views/custom-field/editor/sections/service-detail/price-list/create-service-package'
import attributeInstance from '@/models/Attribute'
import VariantInstance from '@/models/Variant'
import { convertAttributeApiList } from '@/constants/custom-field/custom-conponents/variant'
import type { Attribute } from '@/types/custom-field/custom-conponents/attribute'
import type { Variant } from '@/types/custom-field/custom-conponents/variant'
import type { IServiceBasicInfo } from '@/types/custom-field'
import CommonService from '@/models/CommonService'
import ModalConfirmExitService from '@/views/custom-field/service/ModalConfirmExitService'

interface PricingFormProps {
  serviceId: string
  pricingId?: string
  mode: 'create' | 'update'
}

export default function PricingForm({ serviceId, pricingId, mode }: PricingFormProps) {
  const dispatch = useDispatch()
  const [form] = Form.useForm()
  const router = useRouter()
  const pathname = usePathname()
  const { user } = useUser()
  const queryClient = useQueryClient()

  const objectCheck = {
    currencyDTO: useSelector((state: RootState) => state.customFieldReducer.currencyDTO),
    taxDTO: useSelector((state: RootState) => state.customFieldReducer.taxDTO)
  }

  const [openUpdateModal, setOpenUpdateModal] = useState(false)
  const [updateReason, setReason] = useState('')

  const [openConfirmExitModal, setOpenConfirmExitModal] = useState(false)

  const [initialFormValues, setInitialFormValues] = useState<any>({})

  const currentStepCreatePricing =
    useSelector((state: RootState) => state.customFieldReducer.currentStepCreatePricing) || 0

  const portal = ((pathname || '').split('/').find(item => item === 'admin-portal') ||
    `${user?.portalType?.toLowerCase() || 'dev'}-portal`) as 'admin-portal' | 'partner-portal'

  const queryKeys = React.useMemo(
    () => ({
      pricing: ['pricing', pricingId] as const,
      serviceInfo: ['getServiceInfo', serviceId, 'PROCESSING'] as const,
      variants: ['getVariant', serviceId] as const,
      attributes: ['attributesData', serviceId] as const
    }),
    [pricingId, serviceId]
  )

  const isUpdateMode = mode === 'update' && !!pricingId
  const hasServiceId = !!serviceId
  const hasUserPortalType = !!user?.portalType

  // Query pricing data - chỉ chạy khi update mode
  const { isLoading: isLoadingPricing } = useQuery({
    queryKey: queryKeys.pricing,
    queryFn: async () => {
      const response = await DevPricing.getPricing(portal, pricingId!, 'PROCESSING')
      const formInput = convertToFormInputFromApi(response)

      // lưu thông tin Áp dụng điều kiện
      dispatch(
        updateInfoCustomField({
          conditionsApply: formInput.pricingConfigDTO
        })
      )

      const initialValues = {
        ...response,
        ...formInput,
        hasChangeQuantity: response.hasChangeQuantity === 'ALL' ? ['INCREASE', 'DECREASE'] : response.hasChangeQuantity,
        pageType: 'edit-pricing'
      }

      const data = {
        ...initialValues,

        temporaryVariable: {
          // initialValues: initialValues,
          initialPricingStrategies: response.pricingStrategies
        }
      }

      form.setFieldsValue(data)

      setInitialFormValues(data)

      return response
    },
    enabled: isUpdateMode
  })

  // API chi tiết dịch vụ
  useQuery<IServiceBasicInfo>({
    queryKey: queryKeys.serviceInfo,
    queryFn: async () => {
      // const cachedData = queryClient.getQueryData<IServiceBasicInfo>(['getServiceInfo', serviceId, 'PROCESSING'])

      // if (cachedData) return cachedData // nếu đã có thì dùng cache

      const res = await CommonService.getServiceBasicDetail(serviceId, 'PROCESSING', user?.portalType?.toLowerCase())

      return res
    },
    enabled: hasServiceId && hasUserPortalType,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    select: data => {
      form.setFieldsValue({
        serviceId: data.id,
        serviceName: data.name,
        providerName: data?.providerDTO?.name,
        icon: data?.icon || data?.iconService?.[0],
        customerTypeService: data.customerTypeCode,
        customerTypeCode: data.customerTypeCode,
        seoList: data?.seoList
      })

      // set giá trị cho custom field
      dispatch(
        updateInfoCustomField({
          classification: data.classification
        })
      )

      return data
    }
  })

  useQuery<Variant[]>({
    queryKey: queryKeys.variants,
    queryFn: async () => {
      const res = await VariantInstance.getAllVariants(serviceId)

      const lstVariant = res?.map((item: any) => ({
        ...item,
        name: item.fullName
      }))

      dispatch(
        updateInfoCustomField({
          variants: lstVariant
        })
      )

      return lstVariant
    },
    enabled: hasServiceId,
    initialData: [],
    refetchOnWindowFocus: false
  })

  useQuery<Attribute[]>({
    queryKey: queryKeys.attributes,
    queryFn: async () => {
      const res = await attributeInstance.getAttributesInService(serviceId)
      const lstAttribute = convertAttributeApiList(res)

      dispatch(
        updateInfoCustomField({
          attributes: lstAttribute
        })
      )

      return lstAttribute
    },
    initialData: [],
    enabled: hasServiceId,
    refetchOnWindowFocus: false
  })

  const isFormChanged = useCallback(() => {
    try {
      // Lấy giá trị hiện tại của form
      const currentValues = form.getFieldsValue(true)

      // Chuẩn hóa dữ liệu để so sánh
      const normalizeValue = (value: any) => {
        // Coi các giá trị rỗng là tương đương nhau
        if (value === undefined || value === null || value === '') {
          return null
        }

        // Xử lý mảng rỗng
        if (Array.isArray(value) && value.length === 0) {
          return null
        }

        // Xử lý object rỗng
        if (typeof value === 'object' && value !== null && Object.keys(value).length === 0) {
          return null
        }

        return value
      }

      // Chuẩn hóa toàn bộ đối tượng
      const normalizeObject = (obj: any) => {
        if (!obj) return {}

        const result: any = {}

        Object.entries(obj).forEach(([key, value]) => {
          const normalizedValue = normalizeValue(value)

          // Chỉ giữ lại các giá trị không rỗng
          if (normalizedValue !== null) {
            result[key] = normalizedValue
          }
        })

        return result
      }

      // Chuẩn hóa cả giá trị hiện tại và giá trị ban đầu
      const normalizedCurrentValues = normalizeObject(currentValues)
      const normalizedInitialValues = normalizeObject(initialFormValues)

      // Kiểm tra xem sau khi chuẩn hóa, form có thực sự thay đổi không
      const currentKeys = Object.keys(normalizedCurrentValues)
      const initialKeys = Object.keys(normalizedInitialValues)

      // Nếu không có trường nào có giá trị sau khi chuẩn hóa, coi như form chưa thay đổi
      if (currentKeys.length === 0 && initialKeys.length === 0) {
        return false
      }

      // So sánh và trả về kết quả
      const hasChanged = !isEqual(normalizedCurrentValues, normalizedInitialValues)

      return hasChanged
    } catch (error) {
      console.error('Lỗi khi kiểm tra thay đổi form:', error)

      // Nếu có lỗi, giả định form đã thay đổi để an toàn
      return true
    }
  }, [form, initialFormValues])

  const updateStep = (newStep: number) => {
    dispatch(
      updateInfoCustomField({
        currentStepCreatePricing: newStep
      })
    )
  }

  const onNextStep = () => {
    form
      .validateFields()
      .then(() => {
        updateStep(currentStepCreatePricing + 1)
      })
      .catch(errorInfo => console.log(errorInfo))
  }

  const onPreStep = () => {
    form
      .validateFields()
      .then(() => {
        updateStep(currentStepCreatePricing - 1)
      })
      .catch(errorInfo => console.log(errorInfo))
  }

  const mutation = useMutation({
    mutationFn: DevPricing.insertPricingByServiceId,
    onSuccess: () => {
      message.success(mode === 'create' ? 'Tạo gói dịch vụ thành công' : 'Cập nhật gói dịch vụ thành công')

      updateStep(0)

      queryClient.invalidateQueries({ queryKey: queryKeys.pricing })
      queryClient.invalidateQueries({ queryKey: queryKeys.serviceInfo })

      router.push(`/product-catalog/detail/${serviceId}`)
    },
    onError: (e: any) => handlePricingError(e, form, mode, objectCheck)
  })

  const updateMutation = useMutation({
    mutationFn: DevPricing.updatePricing,
    onSuccess: () => {
      message.success('Cập nhật gói dịch vụ thành công')

      updateStep(0)

      queryClient.invalidateQueries({ queryKey: queryKeys.pricing })
      queryClient.invalidateQueries({ queryKey: queryKeys.serviceInfo })

      router.push(`/product-catalog/detail/${serviceId}?pricingId=${pricingId}`)

      setOpenUpdateModal(false)

      setReason('')
    },
    onError: (e: any) => {
      handlePricingError(e, form, mode, objectCheck)
      setOpenUpdateModal(false)
    }
  })

  const convertSubmitData = (data: any) => {
    return {
      serviceId: toNumber(serviceId),
      ...data,
      ...convertConfiguration(data),
      ...convertToPricingPlan(data),

      pageType: undefined,
      variantCodeApply: undefined,
      // updateReason: "lý do chỉnh sửa gói",
      seoId: data?.seoReqDTO?.id || undefined,
      seoReqDTO: data?.seoReqDTO?.planUrl
        ? {
            ...data.seoReqDTO,
            keywords: [],
            attachs: data?.seoReqDTO?.attachs || [],
            isImage: data?.seoReqDTO?.isImage === 'banner' ? true : false,
            productUrl: data?.seoReqDTO?.planUrl
          }
        : null
    }
  }

  const onSubmit = () => {
    if (mode === 'create') {
      const allData = form.getFieldsValue(true)
      const data = convertSubmitData(allData)

      mutation.mutate({ portal, serviceId, data })
    } else {
      setOpenUpdateModal(true)
    }
  }

  const handleConfirmUpdate = () => {
    const allData = form.getFieldsValue(true)
    const data = convertSubmitData({ ...allData, updateReason })

    updateMutation.mutate({ portal, serviceId, pricingId, data })
  }

  // Handle postMessage for exit confirmation
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (event.data?.type === 'REQUEST_EXIT_CONFIRM') {
        setOpenConfirmExitModal(true)
      }
    }

    window.addEventListener('message', handleMessage)

    return () => {
      window.removeEventListener('message', handleMessage)
    }
  }, [])

  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (event.data?.type === 'REQUEST_EXIT_CONFIRM') {
        // Chỉ mở modal nếu form đã thực sự thay đổi
        if (isFormChanged()) {
          setOpenConfirmExitModal(true)
        } else {
          // Nếu chưa thay đổi, chuyển menu luôn
          window.parent.postMessage(
            {
              type: 'CONFIRM_EXIT'
            },
            '*'
          )

          router.push(
            mode === 'create'
              ? `/product-catalog/detail/${serviceId}`
              : `/product-catalog/detail/${serviceId}?pricingId=${pricingId}`
          )
        }
      }
    }

    window.addEventListener('message', handleMessage)

    return () => window.removeEventListener('message', handleMessage)
  }, [form, serviceId, pricingId, router, isFormChanged, mode])

  const onConfirmExit = () => {
    setOpenConfirmExitModal(false)

    !!pricingId && handleConfirmUpdate()
  }

  const handleCancelOnClick = () => {
    // Kiểm tra trực tiếp sự thay đổi của form thay vì dùng state
    const formChanged = isFormChanged()

    if (formChanged) {
      setOpenConfirmExitModal(true)
    } else {
      router.push(
        mode === 'create'
          ? `/product-catalog/detail/${serviceId}`
          : `/product-catalog/detail/${serviceId}?pricingId=${pricingId}`
      )
      dispatch(
        updateInfoCustomField({
          currentStepCreatePricing: 0
        })
      )
    }
  }

  return (
    <div className=''>
      <Spin spinning={isLoadingPricing} className='w-full'>
        <Editor enabled={false} resolver={resolver}>
          <Form
            form={form}
            initialValues={{
              pageType: pricingId ? 'edit-pricing' : 'create-pricing'
            }}
            onFinish={onSubmit}
            onFinishFailed={errorInfo => {
              console.log('🚀 ~ onFinishFailed ~ errorInfo:', errorInfo)
            }}
            layout='vertical'
            className='w-full'
          >
            <Frame data={createPricing as any} />
          </Form>
        </Editor>
        <div
          className={`mt-4 flex items-center gap-4 rounded-2xl bg-white p-4 ${
            isUpdateMode ? 'justify-between' : 'justify-end'
          }`}
        >
          {isUpdateMode && (
            <div className='flex items-center gap-4'>
              {currentStepCreatePricing > 0 && (
                <Button color='primary' variant='outlined' onClick={onPreStep}>
                  {`Quay lại bước ${currentStepCreatePricing}`}
                </Button>
              )}
              {currentStepCreatePricing < 1 && (
                <Button color='primary' variant='outlined' onClick={onNextStep}>
                  Bước tiếp theo
                </Button>
              )}
            </div>
          )}

          <div className='flex items-center gap-4'>
            <Button color='primary' variant='outlined' onClick={handleCancelOnClick}>
              Hủy
            </Button>

            {!isUpdateMode ? (
              <>
                {currentStepCreatePricing !== 1 ? (
                  <Button type='primary' onClick={onNextStep}>
                    Tiếp theo
                  </Button>
                ) : (
                  <Button
                    type='primary'
                    onClick={() => {
                      form.submit()
                    }}
                    htmlType='submit'
                  >
                    Tạo
                  </Button>
                )}
              </>
            ) : (
              <Button
                type='primary'
                onClick={() => {
                  form.submit()
                }}
                htmlType='submit'
              >
                Lưu
              </Button>
            )}
          </div>
        </div>
      </Spin>

      <Modal
        width='40%'
        title={
          <div className='flex items-center gap-4'>
            <div className='size-10 rounded-full bg-blue-100 p-2'>
              <i className='onedx-message size-6 text-bg-info-default' />
            </div>
            <span className='text-base font-bold'>Lý do cập nhật</span>
          </div>
        }
        open={openUpdateModal}
        onCancel={() => {
          setOpenUpdateModal(false)
        }}
        footer={
          <div className='flex justify-end gap-4'>
            <Button color='primary' variant='outlined' onClick={() => setOpenUpdateModal(false)}>
              Huỷ
            </Button>
            <Button disabled={updateReason.trim().length === 0} type='primary' onClick={handleConfirmUpdate}>
              Lưu
            </Button>
          </div>
        }
      >
        <Form layout='vertical' className='my-6'>
          <Form.Item required label='Mô tả lý do'>
            <Input.TextArea rows={4} placeholder='Nhập lý do' onChange={e => setReason(e.target.value)} />
          </Form.Item>
        </Form>
      </Modal>

      {/* Modal Confirm Exit */}
      {openConfirmExitModal && (
        <ModalConfirmExitService
          open={openConfirmExitModal}
          setOpen={setOpenConfirmExitModal}
          isLoading={updateMutation.isPending}
          onConfirmExit={onConfirmExit}
        />
      )}
    </div>
  )
}
