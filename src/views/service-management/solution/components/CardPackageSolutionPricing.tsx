import React, { useMemo, useState } from 'react'

import { useRouter } from 'next/navigation'

import { useDispatch } from 'react-redux'
import { Button } from '@mui/material'
import { Form, Image, Popover, Tag, Tooltip } from 'antd'
import classNames from 'classnames'

import DOMPurify from 'dompurify'

import { getCouponValue } from '@/constants/commonFunction'

import { processDetailService } from '@views/enterprise/convert'

import PricingComboMobile from '@views/service-management/combo-detail/components/PricingComboMobile'

import { useResponsive } from '@/hooks'

import { CardProductDetail } from '@components/card/enterprise/CardProductDetail'
import { CardProductPrice } from '@/components/card/enterprise/card-item'
import { CardProductFeature } from '@/components/card/enterprise/card-item/CardProductFeature'
import { paymentActions } from '@/redux-store/slices/PaymentSlice'

import type { MainCardProps, ServiceCardProps } from '@/types/Enterprise'

const CardPackageSolutionPricing: React.FC<ServiceCardProps> = props => {
  return (
    <BaseCard {...props}>
      <ContentCard {...props} />
    </BaseCard>
  )
}

// render thông tin gói cước (tên và  mô tả)
const PricingInfo = ({
  name,
  desc,
  isActive = false,
  solutionId,
  dataPricingSolution
}: {
  name: string
  desc: string
  isActive: boolean
  solutionId: number
  dataPricingSolution: any
}) => {
  const router = useRouter()

  function mapPackageToCardProps(input: any) {
    const lstAddon = input?.lstProduct?.flatMap((p: any) => p.addons)

    return {
      provider: input?.providerName,
      comboVariants: input?.lstProduct?.filter((p: any) => p.variantName != null),
      comboPricing: input?.lstProduct?.filter((p: any) => p.variantName == null),
      features: input?.lstFeature,
      addonsList: lstAddon,
      ...input
    }
  }

  return (
    <div
      className='flex flex-col items-start gap-y-1'
      onClick={() => router.push(`/enterprise/solution/${solutionId}/pricing/${dataPricingSolution?.packageId}`)}
    >
      <div
        className={classNames(
          'text-headline-20 font-bold flex justify-between items-center w-full',
          isActive ? 'text-white' : 'text-deep-blue-11'
        )}
      >
        <Tooltip title={name} placement='topLeft'>
          <div className='max-w-[230px] truncate text-headline-18 font-semibold'>{name}</div>
        </Tooltip>

        <div
          onClick={e => e.stopPropagation()}
          className='flex size-9 cursor-pointer items-center justify-between rounded-xl bg-white opacity-0 transition-opacity duration-200 group-hover:opacity-100'
        >
          <Popover
            content={
              <div className='px-4 py-1'>
                <CardProductDetail
                  typeCard='pricing-solution'
                  dataPricingSolution={mapPackageToCardProps(dataPricingSolution)}
                  {...(mapPackageToCardProps(dataPricingSolution) as any)}
                />
              </div>
            }
            trigger='click'
            placement='rightTop'
            className='detailCard'
            overlayInnerStyle={{ borderRadius: '12px' }}
          >
            <div
              className={`flex items-center justify-center gap-2 rounded-2.5xl border bg-blue-50 p-2 text-sme-blue-7`}
            >
              <i className='onedx-eye size-5' />
            </div>
          </Popover>
        </div>
      </div>

      <Tooltip title={<div dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(desc) }} />} placement='topLeft'>
        <div
          className={classNames(
            'text-caption-12 three-lines break-words max-w-[90%] ',
            isActive ? 'text-white' : 'text-text-neutral-strong'
          )}
          dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(desc) }}
        />
      </Tooltip>
    </div>
  )
}

const ContentCard = ({ ...props }: any) => {
  const { name, descriptions, packageId, form, priceFrom, lstFeature, solutionId, bestDeal } = props

  // Biến lưu thông tin id gói được chọn
  const activeId = Form.useWatch('activeId', form)

  // Biến lưu thông tin chu kì gói được chọn theo ID
  // const activePlanId = Form.useWatch([id, 'pricingMultiPlan'], form)

  // Biến kiểm tra gói có đang được chọn hay không
  const isActive = useMemo(() => {
    return activeId === packageId
  }, [activeId, packageId])

  const discount = getCouponValue(bestDeal)

  return (
    <>
      <PricingInfo
        name={name}
        desc={descriptions}
        isActive={isActive}
        dataPricingSolution={props}
        solutionId={solutionId}
      />

      <div>
        <span className='text-base font-semibold'>Bao gồm</span>
        <div className='mt-2 flex justify-between rounded-xl bg-blue-50 px-4 py-2 text-xs font-medium text-primary'>
          {!!props.numOfDevice && props.numOfDevice > 0 && (
            <div className='flex gap-2'>
              <i className='onedx-gift-box size-4' />
              {props.numOfDevice} thiết bị
            </div>
          )}
          {!!props.numOfService && props.numOfService > 0 && (
            <div className='flex gap-2'>
              <i className='onedx-message size-4' />
              {props.numOfService} dịch vụ
            </div>
          )}
        </div>
      </div>

      {/* Danh sách tính năng */}
      <CardProductFeature name='Tính năng nổi bật' features={lstFeature} isActive={isActive} />
      {/* Thông tin giá*/}
      <CardProductPrice
        isActive={isActive}
        name=''
        price={Math.round(priceFrom)?.toLocaleString()}
        discount={discount}
      />
    </>
  )
}

const BaseCard: React.FC<MainCardProps> = ({ children, ...props }) => {
  const { hasProductSource = false, fromProduct, form, packageId, name, recommended } = props

  const dispatch = useDispatch()
  const { isMobile } = useResponsive()
  const [openMobilePricingCombo, setOpenMobilePricingCombo] = useState<boolean>(false)

  const activeId = Form.useWatch('activeId', form)

  const onSelectPricing = () => {
    if (activeId === packageId) {
      form.setFieldsValue({
        activeId: null
      })
    } else
      form.setFieldsValue({
        activeId: packageId
      })

    // const convertPricing = convertToCalculateService({ ...selectedPricingPlan, name, pricingId: id, id })

    dispatch(
      paymentActions.handleUpdateComboPlan({
        comboPlan: {
          packageId,
          name
        }
      })
    )
  }

  return (
    <div className='group'>
      <div className='group relative h-[530px] transition-all duration-300'>
        {recommended && (
          <div
            className='absolute flex h-20 w-full max-w-[300px] items-center justify-center rounded-t-[20px]'
            style={{ zIndex: '-1', background: 'linear-gradient(135deg, rgba(0, 179, 167, 1), rgba(92, 232, 197, 1))' }}
          >
            <div className='flex translate-y-[-16px] flex-row items-center gap-2 text-base font-semibold text-white'>
              <i className='onedx-message-check size-5'></i>
              <div>Khuyên dùng</div>
            </div>
          </div>
        )}
        <div
          className={classNames(
            'sm:[#F9FAFA] relative flex w-full max-w-[300px] flex-col rounded-3xl group-hover:shadow-3 h-full',
            activeId === packageId ? 'bg-sme-blue-8 text-white' : 'bg-gray-13 group-hover:bg-bg-surface',
            recommended ? 'top-12' : ''
          )}
        >
          {/* Content */}
          <div className={`'min-h-[250px] flex flex-1 flex-col justify-between gap-3 px-4 pt-5`}>{children}</div>

          {/* Footer */}
          <div className='mt-5 flex items-center justify-between gap-6 px-4 pb-5'>
            <Button
              className='w-full rounded-2xl text-body-14 font-medium'
              color={activeId === packageId ? 'secondary' : 'primary'}
              variant='contained'
              onClick={onSelectPricing}
            >
              {activeId === packageId ? 'Bỏ chọn' : 'Chọn gói '}
            </Button>
          </div>
          {isMobile && (
            <div className='flex items-center justify-between gap-6 px-4 pb-5'>
              <Button
                className='w-full rounded-2xl bg-blue-50 text-body-14 font-medium text-primary'
                onClick={() => setOpenMobilePricingCombo(true)}
              >
                Xem chi tiết
              </Button>
            </div>
          )}

          {/* Nguồn gói cước */}
          {hasProductSource && (
            <div className='px-4 pb-5'>
              <div className='border-t border-solid border-border-neutral-light pt-5 group-hover:border-sme-blue-7'>
                <div className='mb-3 text-caption-12 font-medium text-text-neutral-medium group-hover:text-white'>
                  Từ sản phẩm
                </div>
                <div className='flex items-start gap-x-3'>
                  <div className='rounded-2xl p-2 group-hover:bg-white'>
                    <Image
                      src={fromProduct?.avatarUrl}
                      className='size-12 rounded-2xl object-contain'
                      preview={false}
                    />
                  </div>
                  <div className='flex flex-col gap-y-1'>
                    <div className='max-w-[180px] break-words text-headline-16 font-semibold text-sme-blue-11 group-hover:text-white'>
                      {fromProduct?.serviceName}
                    </div>
                    <div className='flex items-center gap-x-1'>
                      {fromProduct?.tags?.map((el: string) => (
                        <Tag key={el} color='blue' className='w-[fit] rounded-md px-2 py-[2px]'>
                          {el}
                        </Tag>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              <div className='flex items-center justify-center'>
                <Button
                  endIcon={<i className='onedx-chevron-right size-4' />}
                  variant='text'
                  className='mt-3 text-body-14 font-medium text-sme-blue-7 group-hover:text-white'
                >
                  Xem thêm
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
      {isMobile && (
        <PricingComboMobile
          {...processDetailService(props)}
          openMobilePricingCombo={openMobilePricingCombo}
          setOpenMobilePricingCombo={setOpenMobilePricingCombo}
        />
      )}
    </div>
  )
}

export default CardPackageSolutionPricing
