'use client'

import { useEffect, useState } from 'react'

import { But<PERSON>, DatePicker, Drawer, Form, Input, message, Select } from 'antd'
import { styled } from 'styled-components'

import { useMutation } from '@tanstack/react-query'

import dayjs from 'dayjs'

import { IDENTITY_TYPE } from '@/constants/constantEnum'

import User from '@/models/User'
import { handleSrcImg } from '@/utils/string'
import { validateEmail, validateRequireInput } from '@/validator'
import type UploadAvatarProps from './UploadAvatar'
import { UploadAvatar } from './UploadAvatar'
import { disabledAfterCurrent } from '../constants'
import ModalNotification from '@/components/modal/ModalNotification'
import type { NotificationInfoType } from '@/types/notificationModalTypes'
import { RenderHeaderMobile } from './RenderHeaderMobile'

interface EditCommonInfoProps {
  open: boolean
  setOpen: any
  user: any
  updateUser: any
  refetchPersonal: any
  userData: any
  isMobile?: boolean
}

interface SubmitValue {
  firstname: string
  lastname: string
  birthdate: any
  gender: string
  email: string
  tin: string
}

const genderOptions = [
  { value: 'MALE', label: 'Nam' },
  { value: 'FEMALE', label: 'Nữ' },
  { value: 'OTHER', label: 'Khác' }
]

export const EditUserInfo = ({
  open,
  setOpen,
  user,
  updateUser,
  userData,
  refetchPersonal,
  isMobile
}: EditCommonInfoProps) => {
  const [form] = Form.useForm()
  const [isFormChanged, setIsFormChanged] = useState(false)

  const [openNotificationModal, setOpenNotificationModal] = useState(false)
  const [infoNotificationModal, setInfoNotificationModal] = useState<NotificationInfoType>({})

  const [valueUploadAvatar, setValueUploadAvatar] = useState<UploadAvatarProps>()

  // region Mutation
  const updateAccountMutation = useMutation({
    mutationFn: User.updateProfileUser,
    onSuccess: res => {
      const newUser = {
        ...user,
        ...res
      }

      if (res.birthdate !== null) {
        newUser.birthdate = dayjs(res.birthdate, 'DD/MM/YYYY')
      } else {
        newUser.birthdate = undefined
      }

      updateUser(newUser)
      message.success('Cập nhật thông tin thành công!')
      const userInfo = JSON.parse(localStorage.getItem('userInfo') as any)

      refetchPersonal()

      if (userInfo) {
        updateUser({ ...user, ...userInfo })
      }

      localStorage.removeItem('userInfo')

      setOpen(false)
    },
    onError: (err: any) => {
      if (err.errorCode === 'exists' && err.field === 'phoneNumber') {
        form.setFields([{ name: 'personal.contactPhoneNumber.3', errors: ['Số điện thoại đã tồn tại'] }])
        form.scrollToField(err.field, { behavior: 'smooth', block: 'center' })
        message.error('Số điện thoại đã tồn tại.')
      } else if (err.errorCode === 'exists' && err.field === 'email') {
        form.setFields([{ name: 'personal.contactEmail.3', errors: [`Email đã tồn tại`] }])
        form.scrollToField(err.field, { behavior: 'smooth', block: 'center' })
      } else if (err.errorCode === 'error.duplicate.value') {
        const fieldLabel = err.message.replace('Value of ', '').replace(' is duplicated', '')

        form.setFields([{ name: err.field, errors: [`${fieldLabel} đã tồn tại trong hệ thống`] }])
        form.scrollToField(err.field, { behavior: 'smooth', block: 'center' })
      } else if (err?.errorCode === 'error.exists.email.enterprise.house_hold') {
        setOpenNotificationModal(true)
        setInfoNotificationModal({
          iconType: 'ERROR',
          title: 'Email đã được đăng ký tài khoản',
          subTitle: 'Bạn có chắc muốn sử dụng email đó?',
          textButton: 'Xác nhận',
          isCloseModal: true
        })
      } else if (err?.errorCode === 'error.exists.email') {
        message.error('Email đã tồn tại trong hệ thống')
      } else if (err?.errorCode === 'error.duplicate.identityNo') {
        message.error('Số chứng thức cá nhân đã tồn tại trong hệ thống')
      } else {
        message.error('Đã có lỗi xảy ra!')
      }
    }
  })
  // endregion

  // region Submit
  const getDefaultFormSubmit = () => {
    const formData: SubmitValue = form.getFieldsValue(true)

    return {
      avatarUuid: valueUploadAvatar?.uuid,
      avatar: valueUploadAvatar?.url,
      firstname: formData.firstname,
      lastname: formData.lastname,
      birthdate: formData.birthdate ? formData.birthdate.format('DD/MM/YYYY') : null,
      gender: formData.gender,
      email: formData.email,
      tin: formData.tin
    }
  }

  const handleSubmit = () => {
    const formData = getDefaultFormSubmit()

    updateAccountMutation.mutate(formData)
  }

  const handleSubmitNotificationModal = () => {
    const formData = getDefaultFormSubmit()

    updateAccountMutation.mutate({
      ...formData,
      isConfirmUpdate: true
    })
  }

  // endregion

  const handleClose = () => {
    setOpen(false)
  }

  // Set form data khi data từ API thay đổi
  useEffect(() => {
    form.setFieldsValue({
      firstname: userData?.firstname,
      lastname: userData?.lastname,
      birthdate: userData?.birthdate ? dayjs(userData?.birthdate, 'DD/MM/YYYY') : null,
      gender: userData?.gender,
      tin: userData?.tin,
      repPersonalCertNumber: userData?.repPersonalCertNumber,
      email: userData?.email
    })
  }, [form, userData])

  // region Return
  return (
    <CustomDrawerEdit
      width={640}
      onClose={handleClose}
      open={open}
      closable={false}
      title={
        !isMobile ? (
          <div className='flex items-center justify-between px-4 pb-3 pt-4'>
            <div className='flex items-center gap-x-4 text-headline-16 font-semibold text-text-neutral-strong'>
              <div className='flex size-10 items-center justify-center rounded-full bg-bg-primary-light'>
                <i className='onedx-edit size-6 text-text-primary-default' />
              </div>
              Chỉnh sửa thông tin cá nhân
            </div>
            <i className='onedx-close-icon size-5 text-icon-neutral-medium' onClick={handleClose} />
          </div>
        ) : (
          <RenderHeaderMobile
            title='Chỉnh sửa thông tin cá nhân'
            rightBtn={<div className='text-body-14 font-medium text-text-primary-default'>Lưu</div>}
            onclickLeftBtn={() => setOpen && setOpen(false)}
            onClickRightBtn={() => form.submit()}
          />
        )
      }
      footer={
        <div className='float-right flex items-center gap-x-4 bg-white p-4 pt-3 sm:hidden'>
          <Button type='primary' htmlType='submit' onClick={() => form.submit()} disabled={!isFormChanged}>
            Lưu lại
          </Button>
          <Button type='primary' className='bg-blueLight text-sme-blue-7' onClick={handleClose}>
            Hủy
          </Button>
        </div>
      }
    >
      <div className='bg-bg-surface px-4 pb-4 pt-6 sm:mt-2'>
        <div className='text-caption-12 font-medium text-text-neutral-strong'>Ảnh đại diện</div>
        <div className='flex justify-center'>
          <UploadAvatar
            setValueUpload={setValueUploadAvatar}
            defaultImage={userData?.avatar ? handleSrcImg(userData?.avatar) : ''}
            onChange={() => setIsFormChanged(true)}
          />
        </div>
      </div>

      <Form
        onFinish={handleSubmit}
        onValuesChange={() => setIsFormChanged(true)}
        layout='vertical'
        form={form}
        className='h-full bg-bg-surface px-4'
      >
        <EmptyFormItem label='Họ và tên' style={{ marginBottom: 0 }} required />
        <div className='flex items-center justify-between gap-2'>
          {/* Họ */}
          <Form.Item className='w-1/2' name='lastname' rules={[validateRequireInput('Họ không được bỏ trống')]}>
            <Input placeholder='Nhập họ' maxLength={20} />
          </Form.Item>
          {/* Tên */}
          <Form.Item className='w-1/2' name='firstname' rules={[validateRequireInput('Tên không được bỏ trống')]}>
            <Input placeholder='Nhập tên' maxLength={20} />
          </Form.Item>
        </div>
        {userData?.identityTypeHKD !== IDENTITY_TYPE.CERT_NUMBER ? (
          <>
            {/* Mã số thuế */}
            <Form.Item className='w-full' label='Tên đăng nhập (Mã số thuế)' name='tin'>
              <Input placeholder='Nhập chức danh' maxLength={50} disabled />
            </Form.Item>
          </>
        ) : (
          <>
            {/* Số chứng thực */}
            <Form.Item className='w-full' label='Tên đăng nhập (Số chứng thực)' name='repPersonalCertNumber'>
              <Input placeholder='Nhập chức danh' maxLength={50} disabled />
            </Form.Item>
          </>
        )}

        {/* Email */}
        <Form.Item
          className='w-full'
          label='Email'
          name='email'
          required
          rules={[validateEmail('Sai định dạng email'), validateRequireInput('Email không được bỏ trống')]}
        >
          <Input placeholder='Nhập email' maxLength={100} />
        </Form.Item>
        <div className='flex items-center justify-between gap-2'>
          {/* Ngày sinh */}
          <Form.Item className='w-1/2' label='Ngày sinh' name='birthdate'>
            <DatePicker
              placeholder='dd/mm/yyyy'
              className='w-full'
              format='DD/MM/YYYY'
              disabledDate={disabledAfterCurrent}
            />
          </Form.Item>
          {/* Giới tính */}
          <Form.Item className='w-1/2' label='Giới tính' name='gender'>
            <Select className='w-full' options={genderOptions} placeholder='Chọn giới tính' />
          </Form.Item>
        </div>
      </Form>
      {/* Modal Thông báo */}
      {openNotificationModal && (
        <ModalNotification
          visibleModal={openNotificationModal}
          setVisibleModal={setOpenNotificationModal}
          infoModal={infoNotificationModal}
          onFinish={handleSubmitNotificationModal}
        />
      )}
    </CustomDrawerEdit>
  )
  // endregion
}

const EmptyFormItem = styled(Form.Item)`
  .ant-form-item-control-input {
    min-block-size: 0 !important;
  }
`

export const CustomDrawerEdit = styled(Drawer)`
  & .ant-drawer-header {
    padding: 0 !important;
  }

  & .ant-drawer-body {
    padding: 0 !important;
    background-color: #fff !important;
  }
`
