import { useEffect, useState } from 'react'

import { Button, Empty, Form, Input, message, Select } from 'antd'

import { useMutation, useQuery } from '@tanstack/react-query'

import TextArea from 'antd/es/input/TextArea'

import { useNewLocation } from '@/hooks/useNewLocation'

import { handleSrcImg } from '@/utils/string'
import User from '@/models/User'
import { UploadAvatar } from './UploadAvatar'
import type UploadAvatarProps from './UploadAvatar'
import SmeProfile from '@/models/SmeProfile'
import { validateCode, validatePhoneNumber2, validateRequireInput, validateUrl } from '@/validator'
import type { NotificationInfoType } from '@/types/notificationModalTypes'
import ModalNotification from '@/components/modal/ModalNotification'
import { RenderHeaderMobile } from './RenderHeaderMobile'
import { CustomDrawerEdit } from './EditUserInfo'

interface EditContactProps {
  open: boolean
  setOpen: any
  user: any
  updateUser: any
  refetchEnterprise: any
  enterpriseData: any
  isMobile?: boolean
}

interface SubmitValue {
  taxCode: string
  name: string
  phoneNumber: string
  website: string
  countryId: number
  provinceId: number
  provinceCode: string
  wardId: number
  streetId: number
  homeNumber: string
  address: string
  businessAreasId: number
  businessScaleId: number
  socialInsuranceNumber: string
  description: string
}

interface IRenderAddress {
  province: string
  ward: string
  street: string
  homeNumber: string
}

const nationSelect = [{ label: 'Việt Nam', value: 1 }]

const filterOption = (input: any, option?: any) => option?.label.toLowerCase().includes(input.toLowerCase())

export const EditEnterpriseInfo = ({
  open,
  setOpen,
  user,
  updateUser,
  enterpriseData,
  refetchEnterprise,
  isMobile
}: EditContactProps) => {
  // region state
  const [form] = Form.useForm()
  const [isFormChanged, setIsFormChanged] = useState(false)
  const [valueUploadAvatar, setValueUploadAvatar] = useState<UploadAvatarProps>()

  // region Location
  const [selectedProvince, setSelectedProvince] = useState<number | null>(
    enterpriseData?.provinceId ? enterpriseData.provinceId : null
  )

  const [selectedWard, setSelectedWard] = useState<number | null>(enterpriseData?.wardId ? enterpriseData.wardId : null)

  // end region

  // region api business
  const { data: businessArea } = useQuery({
    queryKey: ['getBussinessArea'],
    queryFn: async () => {
      const res = await SmeProfile.getBussinessArea()
      const areaTemp: any = []

      res.map((item: { id: any; name: any }) =>
        areaTemp.push({
          value: item.id,
          label: item.name
        })
      )

      return areaTemp
    }
  })

  const { data: businessScale } = useQuery({
    queryKey: ['getBussinessScale'],
    queryFn: async () => {
      const res = await SmeProfile.getBussinessScale()
      const areaTemp: any = []

      res.map((item: { id: any; name: any }) =>
        areaTemp.push({
          value: item.id,
          label: item.name
        })
      )

      return areaTemp
    }
  })
  // end region

  // region address
  const { updateAddress, provinceList, wardList, streetList, loadingProvince, loadingStreet, loadingWard } =
    useNewLocation()

  const [renderAddress, setRenderAddress] = useState<IRenderAddress>({
    province: '',
    ward: '',
    street: '',
    homeNumber: ''
  })

  const [activeRenderAddress, setActiveRenderAddress] = useState<number>(0)

  const setNewProvinceValue = (value: number, option: any) => {
    setSelectedProvince(value)
    setSelectedWard(null)

    setRenderAddress({ ...renderAddress, province: option.label, ward: '', street: '' })
    setActiveRenderAddress(activeRenderAddress + 1)

    updateAddress('provinceId', value)
    form.setFieldsValue({
      wardId: null,
      streetId: null
    })
  }

  const setNewWardValue = (value: number) => {
    setSelectedWard(value)

    const wardChange = wardList.find((item: { value: number }) => item.value === value)

    if (wardChange) {
      setRenderAddress({ ...renderAddress, ward: wardChange.label, street: '' })
    }

    setActiveRenderAddress(activeRenderAddress + 1)
    updateAddress('wardId', value)
    form.setFieldsValue({
      streetId: null
    })
  }

  const setNewStreetValue = (value: number) => {
    const streetChange = streetList.find((item: { value: number }) => item.value === value)

    if (streetChange) {
      setRenderAddress({ ...renderAddress, street: streetChange.label })
    }

    setActiveRenderAddress(activeRenderAddress + 1)
    updateAddress('streetId', value)
  }

  const setNewHomeNumber = (value: string) => {
    setRenderAddress(prev => ({ ...prev, homeNumber: value }))
    setActiveRenderAddress(prev => prev + 1)
    updateAddress('homeNumber', value)

    const currentAddress = form.getFieldValue('address')
    const addressParts = currentAddress.split(',')

    addressParts[0] = value.trim()
    const newAddress = addressParts.join(',').replace(/^,\s*/, '')

    form.setFieldsValue({ address: newAddress })
  }

  useEffect(() => {
    if (activeRenderAddress > 0) {
      const arr: string[] = []

      Object.keys(renderAddress).forEach(key => arr.push(renderAddress[key as keyof IRenderAddress]))
      form.setFieldsValue({
        address: arr
          .reverse()
          .filter(item => item)
          .join(', ')
      })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [renderAddress])

  // endregion

  // region Submit
  const [openNotificationModal, setOpenNotificationModal] = useState(false)
  const [infoNotificationModal, setInfoNotificationModal] = useState<NotificationInfoType>({})

  const updateSmeMutation = useMutation({
    mutationFn: User.updateSmeProfile,
    onSuccess: res => {
      const newUser = {
        ...user,
        ...res
      }

      updateUser(newUser)
      message.success('Cập nhật thông tin thành công!')
      const userInfo = JSON.parse(localStorage.getItem('userInfo') as any)

      refetchEnterprise()

      if (userInfo) {
        updateUser({ ...user, ...userInfo })
      }

      localStorage.removeItem('userInfo')

      setOpen(false)
    },
    onError: (err: any) => {
      const { errorCode } = err || {}

      if (errorCode === 'error.exists.phone' || errorCode === 'error.exists.phone.dev.admin') {
        message.error('Số điện thoại đã tồn tại trên hệ thống')
      } else if (errorCode === 'error.exists.phone.enterprise.house_hold') {
        setOpenNotificationModal(true)
        setInfoNotificationModal({
          iconType: 'ERROR',
          title: 'Số điện thoại đã được đăng ký tài khoản',
          subTitle: 'Bạn có chắc chắn muốn sử dụng số điện thoại đó?',
          textButton: 'Xác nhận',
          isCloseModal: true
        })
      } else {
        message.error('Đã có lỗi xảy ra!')
      }
    }
  })
  // end region

  const getDefaultFormSubmit = () => {
    const formData: SubmitValue = form.getFieldsValue(true)

    // Tìm province dựa trên provinceId
    const selectedProvince = provinceList.find((province: { value: number }) => province.value === formData.provinceId)

    // Lấy provinceCode nếu province tồn tại
    const provinceCode = selectedProvince ? selectedProvince.code : ''

    return {
      iconUuid: valueUploadAvatar?.uuid,
      taxCode: formData.taxCode,
      name: formData.name,
      phoneNumber: formData.phoneNumber,
      website: formData.website,
      countryId: formData.countryId,
      provinceId: formData.provinceId,
      provinceCode: provinceCode,
      wardId: formData.wardId,
      streetId: formData.streetId,
      homeNumber: formData.homeNumber,
      address: formData.address,
      businessAreasId: formData.businessAreasId,
      businessScaleId: formData.businessScaleId,
      socialInsuranceNumber: formData.socialInsuranceNumber,
      description: formData.description,
      isConfirmUpdate: false,
      isUpdateEnterpriseSme: true,
      updateType: user?.customerType
    }
  }

  function handleSubmit() {
    const formData = getDefaultFormSubmit()

    updateSmeMutation.mutate(formData)
  }

  // Trùng email trong doanh nghiệp
  const handleSubmitNotificationModal = () => {
    const formData = getDefaultFormSubmit()

    updateSmeMutation.mutate({
      ...formData,
      isConfirmUpdate: true
    })
  }

  const handleClose = () => {
    setOpen(false)
  }
  // endregion

  // region Init value
  // Khởi tạo giá trị
  useEffect(() => {
    if (enterpriseData) {
      updateAddress('provinceId', enterpriseData.provinceId)
      updateAddress('wardId', enterpriseData.wardId)
      updateAddress('streetId', enterpriseData.streetId)
    }

    setRenderAddress({
      province: enterpriseData?.provinceName || '',
      ward: enterpriseData?.wardName || '',
      street: enterpriseData?.streetName || '',
      homeNumber: enterpriseData?.homeNumber || ''
    })

    form.setFieldsValue({
      icon: valueUploadAvatar?.id,
      taxCode: enterpriseData?.taxCode,
      name: enterpriseData?.name,
      phoneNumber: enterpriseData?.phoneNumber,
      website: enterpriseData?.website,
      countryId: enterpriseData?.countryId,
      provinceId: enterpriseData?.provinceId,
      wardId: enterpriseData?.wardId,
      streetId: enterpriseData?.streetId,
      homeNumber: enterpriseData?.homeNumber,
      address: enterpriseData?.address,
      businessAreasId: enterpriseData?.businessAreasId,
      businessScaleId: enterpriseData?.businessScaleId,
      socialInsuranceNumber: enterpriseData?.socialInsuranceNumber,
      description: enterpriseData?.description
    })
  }, [form, enterpriseData, updateAddress, valueUploadAvatar?.id])
  // endregion

  // region Return
  return (
    <CustomDrawerEdit
      width={640}
      onClose={handleClose}
      open={open}
      closable={false}
      title={
        !isMobile ? (
          <div className='flex items-center justify-between px-4 pb-3 pt-4'>
            <div className='flex items-center gap-x-4 text-headline-16 font-semibold text-text-neutral-strong'>
              <div className='flex size-10 items-center justify-center rounded-full bg-bg-primary-light'>
                <i className='onedx-edit size-6 text-text-primary-default' />
              </div>
              Chỉnh sửa thông tin doanh nghiệp
            </div>
            <i className='onedx-close-icon size-5 text-icon-neutral-medium' onClick={handleClose} />
          </div>
        ) : (
          <RenderHeaderMobile
            title='Chỉnh sửa thông tin người đại diện'
            rightBtn={<div className='text-body-14 font-medium text-text-primary-default'>Lưu</div>}
            onclickLeftBtn={() => setOpen && setOpen(false)}
            onClickRightBtn={() => form.submit()}
          />
        )
      }
      footer={
        <div className='float-right flex items-center gap-x-4 bg-white p-4 pt-3 sm:hidden'>
          <Button type='primary' htmlType='submit' onClick={() => form.submit()} disabled={!isFormChanged}>
            Lưu lại
          </Button>
          <Button type='primary' className='bg-blueLight text-sme-blue-7' onClick={handleClose}>
            Hủy
          </Button>
        </div>
      }
    >
      <div className='bg-bg-surface px-4 pb-4 pt-6 sm:mt-2'>
        <div className='text-caption-12 font-medium text-text-neutral-strong'>Ảnh đại diện</div>
        <div className='flex justify-center'>
          <UploadAvatar
            setValueUpload={setValueUploadAvatar}
            defaultImage={enterpriseData?.icon ? handleSrcImg(enterpriseData?.icon) : ''}
            onChange={() => setIsFormChanged(true)}
          />
        </div>
      </div>
      <Form
        onFinish={handleSubmit}
        onValuesChange={() => setIsFormChanged(true)}
        layout='vertical'
        form={form}
        className='h-full bg-bg-surface px-4'
      >
        <Form.Item className='w-full' name='taxCode' label='Mã số thuế' required>
          <Input placeholder='Mã số thuế' disabled />
        </Form.Item>

        <Form.Item
          className='w-full'
          name='name'
          label='Tên doanh nghiệp'
          required
          rules={[validateRequireInput('Tên doanh nghiệp không được bỏ trống')]}
        >
          <Input placeholder='Tên doanh nghiệp' maxLength={300} />
        </Form.Item>

        <div className='flex items-center justify-between gap-2'>
          <Form.Item
            className='w-1/2'
            name='phoneNumber'
            label='Số điện thoại'
            required
            rules={[
              validatePhoneNumber2('Số điện thoại không đúng định dạng'),
              validateRequireInput('Số điện thoại không được bỏ trống')
            ]}
            normalize={value => {
              return value.replace(/\D/g, '')
            }}
          >
            <Input.Password placeholder='Số điện thoại' maxLength={12} />
          </Form.Item>
          <Form.Item
            className='w-1/2'
            name='website'
            label='Website'
            rules={[validateUrl('Website không đúng định dạng')]}
          >
            <Input placeholder='Website' maxLength={100} />
          </Form.Item>
        </div>

        <Form.Item
          className='w-full'
          label='Quốc gia'
          name='countryId'
          required
          rules={[validateRequireInput('Quốc gia không được bỏ trống')]}
        >
          <Select allowClear placeholder='Quốc gia' options={nationSelect} />
        </Form.Item>

        <div className='flex items-center justify-between gap-2'>
          <Form.Item
            name='provinceId'
            label='Tỉnh/Thành phố'
            className='w-1/2'
            required
            rules={[validateRequireInput('Tỉnh/Thành phố không được bỏ trống')]}
          >
            <Select
              showSearch
              options={provinceList}
              filterOption={filterOption}
              onChange={setNewProvinceValue}
              loading={loadingProvince}
              placeholder='Chọn thành phố/tỉnh'
              notFoundContent={<Empty description='Không có dữ liệu' />}
            />
          </Form.Item>
          <Form.Item
            className='w-1/2'
            label='Phường/Xã'
            name='wardId'
            required
            rules={[validateRequireInput('Phường/Xã không được bỏ trống')]}
          >
            <Select
              showSearch
              filterOption={filterOption}
              options={wardList}
              onChange={setNewWardValue}
              placeholder='Chọn phường/xã'
              loading={loadingWard}
              disabled={!selectedProvince}
              notFoundContent={<Empty description='Không có dữ liệu' />}
            />
          </Form.Item>
        </div>

        <Form.Item
          className='w-full sm:mb-4'
          label='Phố/Đường'
          name='streetId'
          required
          rules={[validateRequireInput('Phố/Đường phố không được bỏ trống')]}
        >
          <Select
            showSearch
            filterOption={filterOption}
            options={streetList}
            onChange={setNewStreetValue}
            placeholder='Chọn phố/đường'
            loading={loadingStreet}
            disabled={!selectedWard}
            notFoundContent={<Empty description='Không có dữ liệu' />}
          />
        </Form.Item>

        <Form.Item className='w-full sm:mb-4' label='Số nhà' name='homeNumber'>
          <Input
            className='w-full'
            placeholder='Số nhà'
            maxLength={200}
            onChange={e => setNewHomeNumber(e.target.value)}
          />
        </Form.Item>

        <Form.Item className='w-full sm:mb-4' label='Địa chỉ' name='address'>
          <Input className='w-full' placeholder='Địa chỉ' maxLength={200} />
        </Form.Item>

        <Form.Item
          className='w-full sm:mb-4'
          label='Mã số BHXH'
          name='socialInsuranceNumber'
          rules={[validateCode('Mã số BHXH sai định dạng')]}
        >
          <Input className='w-full' placeholder='Mã số BHXH' maxLength={10} />
        </Form.Item>

        <Form.Item className='w-full sm:mb-4' label='Ngành nghề kinh doanh' name='businessAreasId'>
          <Select className='w-full' allowClear placeholder='Ngành nghề kinh doanh' options={businessArea} />
        </Form.Item>

        <Form.Item className='w-full sm:mb-4' label='Quy mô doanh nghiệp' name='businessScaleId'>
          <Select className='w-full' allowClear placeholder='Quy mô doanh nghiệp' options={businessScale} />
        </Form.Item>

        <Form.Item className='w-full sm:mb-4' label='Giới thiệu chung' name='description'>
          <TextArea className='w-full' placeholder='Giới thiệu chung' maxLength={300} />
        </Form.Item>
      </Form>

      {/* Modal Thông báo */}
      {openNotificationModal && (
        <ModalNotification
          visibleModal={openNotificationModal}
          setVisibleModal={setOpenNotificationModal}
          infoModal={infoNotificationModal}
          onFinish={handleSubmitNotificationModal}
        />
      )}
    </CustomDrawerEdit>
  )
  // endregion
}
