'use client'

import { useEffect, useState } from 'react'

import { isEmpty } from 'lodash'

import { Button, DatePicker, Form, Input, message, Select } from 'antd'

import { useMutation } from '@tanstack/react-query'

import dayjs from 'dayjs'

import { UploadAvatar } from './UploadAvatar'
import User from '@/models/User'
import { handleSrcImg } from '@/utils/string'
import type UploadAvatarProps from './UploadAvatar'
import { disabledFromCurrent } from '../constants'
import { RenderHeaderMobile } from './RenderHeaderMobile'
import { CustomDrawerEdit } from './EditUserInfo'

// region interface
interface EditCommonInfoProps {
  open: boolean
  setOpen: any
  user: any
  updateUser: any
  refetchRepresent: any
  representData: any
  isMobile?: boolean
}

interface SubmitValue {
  repFullName: string
  repGender: string
  repTitle: string
  repNationId: number
  repFolkId: number
  repPersonalCertTypeId: number
  repPersonalCertNumber: string
  repPersonalCertDate: string
  repPersonalCertPlace: string
  repRegisteredPlace: string
  repAddress: string
  repBirthday: string
}
// end region

// region const
const genderOptions = [
  { value: 1, label: 'Nam' },
  { value: 0, label: 'Nữ' },
  { value: 2, label: 'Khác' }
]

const dataPersonalCertType = [
  { value: 1, label: 'Chứng minh nhân dân' },
  { value: 2, label: 'Hộ chiếu' },
  { value: 3, label: 'Thẻ Căn cước Công dân' },
  { value: 4, label: 'Khác' }
]
// end region

const folkSelect = [{ value: 1, label: 'Kinh' }]

const nationSelect = [{ label: 'Việt Nam', value: 1 }]

export const EditRepresentInfo = ({
  open,
  setOpen,
  user,
  updateUser,
  representData,
  refetchRepresent,
  isMobile
}: EditCommonInfoProps) => {
  const [form] = Form.useForm()

  // region state
  const [isFormChanged, setIsFormChanged] = useState(false)
  const [valueUploadAvatar, setValueUploadAvatar] = useState<UploadAvatarProps>()

  // end region

  // region api
  const updateRepresentMutation = useMutation({
    mutationFn: User.updateRepresentProfile,
    onSuccess: res => {
      const newUser = {
        ...user,
        ...res
      }

      if (res.repBirthday !== null) {
        newUser.repBirthday = dayjs(res.repBirthday, 'DD/MM/YYYY')
      } else {
        newUser.repBirthday = undefined
      }

      updateUser(newUser)
      message.success('Cập nhật thông tin thành công!')
      const userInfo = JSON.parse(localStorage.getItem('userInfo') as any)

      refetchRepresent()

      if (userInfo) {
        updateUser({ ...user, ...userInfo })
      }

      localStorage.removeItem('userInfo')
      setOpen(false)
    },
    onError: () => {
      message.error('Đã có lỗi xảy ra!')
    }
  })

  // region handler
  const handleSubmit = (value: SubmitValue) => {
    const {
      repFullName,
      repGender,
      repBirthday,
      repTitle,
      repNationId,
      repFolkId,
      repPersonalCertTypeId,
      repPersonalCertNumber,
      repPersonalCertDate,
      repPersonalCertPlace,
      repRegisteredPlace,
      repAddress
    } = value

    const formattedRepBirthday = repBirthday ? dayjs(repBirthday).format('DD/MM/YYYY') : null
    const formattedRepPersonalCertDate = repPersonalCertDate ? dayjs(repPersonalCertDate).format('DD/MM/YYYY') : null

    const dataUpdate = {
      iconUuid: valueUploadAvatar?.uuid,
      repFullName,
      repBirthday: formattedRepBirthday,
      repGender,
      repTitle,
      repNationId,
      repFolkId,
      repPersonalCertTypeId,
      repPersonalCertNumber,
      repPersonalCertDate: formattedRepPersonalCertDate,
      repPersonalCertPlace,
      repRegisteredPlace,
      repAddress
    }

    updateRepresentMutation.mutate(dataUpdate)
  }

  const handleClose = () => {
    setOpen(false)
  }
  // end region

  // region initialState
  useEffect(() => {
    form.setFieldsValue({
      repFullName: representData?.repFullName,
      repGender: representData?.repGender,
      repBirthday: representData.repBirthday ? dayjs(representData.repBirthday, 'DD/MM/YYYY') : null,
      repTitle: representData?.repTitle,
      repNationId: representData?.repNationId,
      repFolkId: representData?.repFolkId,
      repPersonalCertTypeId: representData?.repPersonalCertTypeId,
      repPersonalCertNumber: representData?.repPersonalCertNumber,
      repPersonalCertDate: representData?.repPersonalCertDate
        ? dayjs(representData?.repPersonalCertDate, 'DD/MM/YYYY')
        : null,
      repPersonalCertPlace: representData?.repPersonalCertPlace,
      repRegisteredPlace: representData?.repRegisteredPlace,
      repAddress: representData?.repAddress
    })
  }, [form, representData])
  // end region

  // region return
  return (
    <CustomDrawerEdit
      width={640}
      onClose={handleClose}
      open={open}
      closable={false}
      title={
        !isMobile ? (
          <div className='flex items-center justify-between px-4 pb-3 pt-4'>
            <div className='flex items-center gap-x-4 text-headline-16 font-semibold text-text-neutral-strong'>
              <div className='flex size-10 items-center justify-center rounded-full bg-bg-primary-light'>
                <i className='onedx-edit size-6 text-text-primary-default' />
              </div>
              Chỉnh sửa thông tin người đại diện
            </div>
            <i className='onedx-close-icon size-5 text-icon-neutral-medium' onClick={handleClose} />
          </div>
        ) : (
          <RenderHeaderMobile
            title='Chỉnh sửa thông tin người đại diện'
            rightBtn={<div className='text-body-14 font-medium text-text-primary-default'>Lưu</div>}
            onclickLeftBtn={() => setOpen && setOpen(false)}
            onClickRightBtn={() => form.submit()}
          />
        )
      }
      footer={
        <div className='float-right flex items-center gap-x-4 bg-white p-4 pt-3 sm:hidden'>
          <Button type='primary' htmlType='submit' onClick={() => form.submit()} disabled={!isFormChanged}>
            Lưu lại
          </Button>
          <Button type='primary' className='bg-blueLight text-sme-blue-7' onClick={handleClose}>
            Hủy
          </Button>
        </div>
      }
    >
      <div className='bg-bg-surface px-4 pb-4 pt-6 sm:mt-2'>
        <div className='text-caption-12 font-medium text-text-neutral-strong'>Ảnh đại diện</div>
        <div className='flex justify-center'>
          <UploadAvatar
            setValueUpload={setValueUploadAvatar}
            defaultImage={representData?.icon ? handleSrcImg(representData?.icon) : ''}
            onChange={() => setIsFormChanged(true)}
          />
        </div>
      </div>

      <Form
        onFinish={value => {
          handleSubmit(value)
        }}
        onValuesChange={() => setIsFormChanged(true)}
        layout='vertical'
        form={form}
        className='h-screen bg-bg-surface px-4'
      >
        <Form.Item className='w-full' name='repFullName' label='Họ và tên'>
          <Input placeholder='Nhập họ và tên' maxLength={99} />
        </Form.Item>

        <div className='flex items-center justify-between gap-2'>
          <Form.Item className='w-1/2' label='Ngày sinh' name='repBirthday'>
            <DatePicker
              placeholder='dd/mm/yyyy'
              className='w-full'
              format='DD/MM/YYYY'
              disabledDate={disabledFromCurrent}
            />
          </Form.Item>
          <Form.Item className='w-1/2 ' label='Giới tính' name='repGender'>
            <Select className='w-full' options={genderOptions} placeholder='Chọn giới tính' />
          </Form.Item>
        </div>
        <Form.Item className='w-full' label='Chức danh' name='repTitle'>
          <Input placeholder='Nhập chức danh' maxLength={50} />
        </Form.Item>
        <div className='flex items-center justify-between gap-2'>
          <Form.Item className='w-1/2' label='Quốc tịch' name='repNationId'>
            <Select className='w-full' options={nationSelect} placeholder='Chọn quốc tịch' />
          </Form.Item>
          <Form.Item className='w-1/2 sm:mb-4' label='Dân tộc' name='repFolkId'>
            <Select className='w-full' options={folkSelect} placeholder='Chọn dân tộc' />
          </Form.Item>
        </div>

        <Form.Item className='w-full sm:mb-4' label='Giấy chứng thực' name='repPersonalCertTypeId'>
          <Select className='w-full' options={dataPersonalCertType} placeholder='Chọn giấy chứng thực' />
        </Form.Item>

        <Form.Item className='w-full' label='Số chứng thực' name='repPersonalCertNumber'>
          <Input placeholder='Số chứng thực' maxLength={30} disabled={!isEmpty(representData?.repPersonalCertNumber)} />
        </Form.Item>

        <div className='flex items-center justify-between gap-2'>
          <Form.Item className='w-1/2' label='Ngày cấp' name='repPersonalCertDate'>
            <DatePicker
              placeholder='dd/mm/yyyy'
              className='w-full'
              format='DD/MM/YYYY'
              disabledDate={disabledFromCurrent}
            />
          </Form.Item>
          <Form.Item className='w-1/2 sm:mb-4' label='Nơi cấp' name='repPersonalCertPlace'>
            <Input placeholder='Nơi cấp' maxLength={150} />
          </Form.Item>
        </div>

        <Form.Item className='w-full sm:mb-4' label='Nơi đăng kí hộ khẩu' name='repRegisteredPlace'>
          <Input className='w-full' placeholder='Nơi đăng kí hộ khẩu' maxLength={150} />
        </Form.Item>

        <Form.Item className='w-full' label='Chỗ ở hiện tại' name='repAddress'>
          <Input placeholder='Chỗ ở hiện tại' maxLength={150} />
        </Form.Item>
      </Form>
    </CustomDrawerEdit>
  )
}
