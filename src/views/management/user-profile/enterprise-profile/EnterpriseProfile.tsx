'use client'

import React, { useState } from 'react'

import { Button, Checkbox, Form, Input, message, Modal } from 'antd'

import { useMutation, useQuery } from '@tanstack/react-query'

import dayjs from 'dayjs'

import { IDENTITY_TYPE } from '@/constants/constantEnum'

import { formatNormalizeNumber, validateCode, validateRequireInput, validateStrLength } from '@/validator'

import { useResponsive, useUser } from '@/hooks'
import { CERTIFICATE_TYPE_ID, convertGenderFromNumber, convertGenderFromString } from '../constants'
import User from '@/models/User'
import { EditRepresentInfo } from '../components/EditRepresentInfo'
import { EditEnterpriseInfo } from '../components/EditEnterpriseInfo'
import { EditUserInfo, InfoSecurity, RenderHeaderMobile } from '../components'
import { RenderProfileInfo } from '../components/RenderProfileInfo'

export const EnterpriseProfile = () => {
  const { isMobile } = useResponsive()
  const [formTransferInfo] = Form.useForm()

  const [openRepresentInfo, setOpenRepresentInfo] = useState(false)
  const [openEnterpriseInfo, setOpenEnterpriseInfo] = useState(false)
  const [openUserInfo, setOpenUserInfo] = useState(false)
  const [openEditRepresentInfo, setOpenEditRepresentInfo] = useState(false)
  const [openEditUserInfo, setOpenEditUserInfo] = useState(false)
  const [openEditEnterpriseInfo, setOpenEditEnterpriseInfo] = useState(false)
  const [openTransferInfo, setOpenTransferInfo] = useState(false)

  const isAgree = Form.useWatch('isAgree', formTransferInfo)

  const { user, updateUser, isLoggedIn } = useUser()
  const customerType = user?.customerType

  const { data: userInfoPersonal, refetch: refetchPersonal } = useQuery({
    queryKey: ['getDataInfoUserDetail'],
    queryFn: async () => {
      const res = await User.getMyProfile()

      const data = {
        ...res,
        repPersonalCertDate: res.repPersonalCertDate ? dayjs(res.repPersonalCertDate, 'DD/MM/YYYY') : '',
        representative: res.representative || []
      }

      return data
    },
    enabled: isLoggedIn
  })

  // gọi api lấy thông tin đại diện
  const { data: representData, refetch: refetchRepresent } = useQuery({
    queryKey: ['representData'],
    queryFn: () => User.getRepresent(),
    enabled: isLoggedIn
  })

  // gọi api lấy thông tin doanh nghiệp
  const { data: enterpriseData, refetch: refetchEnterprise } = useQuery({
    queryKey: ['enterpriseData'],
    queryFn: () => User.getSmeInfor({ customerTypeCode: customerType }),
    enabled: customerType !== 'PERSONAL' && isLoggedIn
  })

  const userRepresentData = [
    {
      label: 'Chức danh',
      value: representData?.repTitle
    },
    {
      label: 'Ngày sinh',
      value: representData?.repBirthday
    },
    {
      label: 'Giới tính',
      value: convertGenderFromNumber(representData?.repGender)
    },
    {
      label: 'Dân tộc',
      value: representData?.repNameFolk
    },
    {
      label: 'Quốc gia',
      value: representData?.repNation
    },
    {
      label: 'Nơi đăng ký hộ khẩu',
      value: representData?.repRegisteredPlace
    },
    {
      label: 'Chỗ ở hiện tại',
      value: representData?.repAddress
    },
    {
      label: 'Giấy chứng thực',
      value: CERTIFICATE_TYPE_ID[representData?.repPersonalCertTypeId]
    },
    {
      label: 'Số chứng thực',
      value: representData?.repPersonalCertNumber
    },
    {
      label: 'Ngày cấp',
      value: representData?.repPersonalCertDate
    },
    {
      label: 'Nơi cấp',
      value: representData?.repPersonalCertPlace
    }
  ]

  const userData = [
    {
      label: `Tên đăng nhập ${userInfoPersonal?.identityTypeHKD === IDENTITY_TYPE.CERT_NUMBER ? '(Số chứng thực)' : '(Mã số thuế)'}`,
      value:
        userInfoPersonal?.identityTypeHKD === IDENTITY_TYPE.CERT_NUMBER
          ? userInfoPersonal?.repPersonalCertNumber
          : userInfoPersonal?.tin
    },
    {
      label: 'Email',
      value: userInfoPersonal?.email
    },
    {
      label: 'Ngày sinh',
      value: userInfoPersonal?.birthdate
    },
    {
      label: 'Giới tính',
      value: convertGenderFromString(userInfoPersonal?.gender)
    }
  ]

  const userEnterpriseData = [
    {
      label: 'Mã số thuế',
      value: enterpriseData?.taxCode
    },
    {
      label: 'Số điện thoại',
      value: enterpriseData?.phoneNumber
    },
    {
      label: 'Website',
      value: enterpriseData?.website
    },
    {
      label: 'Quốc gia',
      value: enterpriseData?.countryName
    },
    {
      label: 'Tỉnh/ Thành phố',
      value: enterpriseData?.provinceName
    },
    {
      label: 'Phường/ Xã',
      value: enterpriseData?.wardName
    },
    {
      label: 'Phố/ Đường',
      value: enterpriseData?.streetName
    },
    {
      label: 'Số nhà',
      value: enterpriseData?.homeNumber
    },
    {
      label: 'Địa chỉ cụ thể',
      value: enterpriseData?.address
    },
    {
      label: 'Mã số BHXH',
      value: enterpriseData?.socialInsuranceNumber
    },
    {
      label: 'Ngành nghề kinh doanh',
      value: enterpriseData?.businessAreasName
    },
    {
      label: 'Quy mô doanh nghiệp',
      value: enterpriseData?.businessScaleName
    },
    {
      label: 'Giới thiệu chung',
      value: enterpriseData?.description
    }
  ]

  const updateTransferInfo = useMutation({
    mutationFn: User.updateSmeTransferInfo,
    onSuccess: res => {
      const newUser = {
        ...user,
        ...res
      }

      updateUser(newUser)
      message.success('Cập nhật thông tin thành công!')
      const userInfo = JSON.parse(localStorage.getItem('userInfo') as any)

      refetchEnterprise().then(() => {})
      refetchPersonal().then(() => {})
      refetchRepresent().then(() => {})

      if (userInfo) {
        updateUser({ ...user, ...userInfo })
      }

      localStorage.removeItem('userInfo')

      setOpenTransferInfo(false)
    },
    onError: () => {
      message.error('Đã có lỗi xảy ra!')
    }
  })

  const handleOk = async () => {
    try {
      const values = await formTransferInfo.validateFields()

      const req = { id: user?.id, repPersonalCertNumber: values?.repPersonalCertNumber }

      updateTransferInfo.mutate(req)
    } catch (errorInfo) {
      console.error('Validation failed:', errorInfo)
    }
  }

  return (
    <>
      {/* Thông tin */}
      {!isMobile ? (
        <div className='rounded-lg bg-white p-6'>
          <div className='flex items-center justify-between'>
            <div className='mb-4 text-headline-18 font-medium text-text-neutral-strong'>Tài khoản</div>
            {userInfoPersonal?.identityTypeHKD === IDENTITY_TYPE.TAX_CODE && (
              <Button
                color='primary'
                variant='outlined'
                className='h-[24px] self-center font-medium'
                onClick={() => setOpenTransferInfo(true)}
              >
                Chuyển đổi thông tin định danh
              </Button>
            )}
          </div>
          {/* Thông tin người đại diện */}
          <RenderProfileInfo
            title='Thông tin người đại diện'
            onClick={() => setOpenEditRepresentInfo(true)}
            avatar={representData?.icon}
            nameAvatar={representData?.repFullName}
            fullName={representData?.repFullName}
            profileData={userRepresentData}
            isShowAvatar
          />

          {/* Thông tin cá nhân */}
          <RenderProfileInfo
            title='Thông tin cá nhân'
            onClick={() => setOpenEditUserInfo(true)}
            avatar={userInfoPersonal?.avatar}
            nameAvatar={userInfoPersonal?.firstname}
            fullName={`${userInfoPersonal?.firstname} ${userInfoPersonal?.lastname}`}
            profileData={userData}
            isShowAvatar
          />

          {/* Thông tin doanh nghiệp */}
          <RenderProfileInfo
            title='Thông tin doanh nghiệp'
            onClick={() => setOpenEditEnterpriseInfo(true)}
            avatar={enterpriseData?.icon}
            nameAvatar={enterpriseData?.name}
            fullName={enterpriseData?.name}
            profileData={userEnterpriseData}
            isEnterprise
            isShowAvatar
          />
        </div>
      ) : (
        <>
          <RenderHeaderMobile title='Thiết lập' rightBtn={<i className='onedx-search size-5' />} />
          <div className='mt-2 pt-3'>
            <div className='mb-2 flex items-center gap-x-2 px-4'>
              <div className='h-4 w-[2px] bg-bg-primary-sme-orange-default' />
              <div className='text-body-14 font-semibold text-text-neutral-medium'>Tài khoản</div>
            </div>
            <div
              className='flex h-10 items-center justify-between border-b border-solid border-b-border-neutral-light bg-bg-surface px-4 py-[2px]'
              onClick={() => setOpenRepresentInfo(true)}
            >
              <div className='text-body-14 font-normal text-text-neutral-strong'>Thông tin người đại diện</div>
              <i className='onedx-chevron-right size-5' />
            </div>
            <div
              className='flex h-10 items-center justify-between border-b border-solid border-b-border-neutral-light bg-bg-surface px-4 py-[2px]'
              onClick={() => setOpenUserInfo(true)}
            >
              <div className='text-body-14 font-normal text-text-neutral-strong'>Thông tin cá nhân</div>
              <i className='onedx-chevron-right size-5' />
            </div>
            <div
              className='flex h-10 items-center justify-between border-b border-solid border-b-border-neutral-light bg-bg-surface px-4 py-[2px]'
              onClick={() => setOpenEnterpriseInfo(true)}
            >
              <div className='text-body-14 font-normal text-text-neutral-strong'>Thông tin doanh nghiệp</div>
              <i className='onedx-chevron-right size-5' />
            </div>
            <RenderProfileInfo
              title='Thông tin người đại diện'
              onClick={() => setOpenRepresentInfo(true)}
              avatar={representData?.icon}
              nameAvatar={representData?.repFullName}
              fullName={representData?.repFullName}
              profileData={userRepresentData}
              isShowAvatar
              isMobile={isMobile}
              open={openRepresentInfo}
              setOpen={setOpenRepresentInfo}
              onOpenEdit={() => setOpenEditRepresentInfo(true)}
            />
            <RenderProfileInfo
              title='Thông tin cá nhân'
              onClick={() => setOpenUserInfo(true)}
              avatar={userInfoPersonal?.avatar}
              nameAvatar={userInfoPersonal?.firstname}
              fullName={`${userInfoPersonal?.firstname} ${userInfoPersonal?.lastname}`}
              profileData={userData}
              isShowAvatar
              open={openUserInfo}
              setOpen={setOpenUserInfo}
              onOpenEdit={() => setOpenEditUserInfo(true)}
              isMobile={isMobile}
            />
            <RenderProfileInfo
              title='Thông tin doanh nghiệp'
              onClick={() => setOpenEnterpriseInfo(true)}
              avatar={enterpriseData?.icon}
              nameAvatar={enterpriseData?.name}
              fullName={enterpriseData?.name}
              profileData={userEnterpriseData}
              isEnterprise
              isShowAvatar
              open={openEnterpriseInfo}
              setOpen={setOpenEnterpriseInfo}
              onOpenEdit={() => setOpenEditEnterpriseInfo(true)}
              isMobile={isMobile}
            />
          </div>
        </>
      )}

      {/* Bảo mật  */}
      <InfoSecurity isMobile={isMobile} />
      {/* Chỉnh sửa thông tin người đại diện */}
      {openEditRepresentInfo && (
        <EditRepresentInfo
          open={openEditRepresentInfo}
          setOpen={setOpenEditRepresentInfo}
          user={user}
          updateUser={updateUser}
          representData={representData}
          refetchRepresent={refetchRepresent}
          isMobile={isMobile}
        />
      )}
      {/* Chỉnh sửa thông tin cá nhân */}
      {openEditUserInfo && (
        <EditUserInfo
          open={openEditUserInfo}
          setOpen={setOpenEditUserInfo}
          user={user}
          updateUser={updateUser}
          userData={userInfoPersonal}
          refetchPersonal={refetchPersonal}
          isMobile={isMobile}
        />
      )}
      {/* Chỉnh sửa thông tin doanh nghiệp */}
      {openEditEnterpriseInfo && (
        <EditEnterpriseInfo
          open={openEditEnterpriseInfo}
          setOpen={setOpenEditEnterpriseInfo}
          user={user}
          updateUser={updateUser}
          enterpriseData={enterpriseData}
          refetchEnterprise={refetchEnterprise}
          isMobile={isMobile}
        />
      )}
      {/* Chỉnh sửa thông tin doanh nghiệp */}
      {openTransferInfo && (
        <Modal
          width={700}
          title={
            <div className='text-base font-semibold leading-normal tracking-tight'>Cập nhật thông tin định danh</div>
          }
          open={openTransferInfo}
          onCancel={() => setOpenTransferInfo(false)}
          className='custom-mobile sm:absolute sm:bottom-0 sm:top-auto sm:my-0 sm:w-full sm:rounded-none sm:pb-0'
          footer={[
            <>
              <Button
                className='ml-4'
                type='primary'
                disabled={!isAgree} // disable khi chưa tick
                onClick={() =>
                  // xử lý submit form ở đây
                  handleOk()
                }
              >
                Xác nhận
              </Button>
              <Button className={'border-solid border-gray-alpha-3'} onClick={() => setOpenTransferInfo(false)}>
                Thoát
              </Button>
            </>
          ]}
        >
          <Form form={formTransferInfo} autoComplete='off' layout='vertical'>
            <div className='flex flex-col pt-6'>
              <div className=' mb-6 rounded border-l-4 border-solid border-gray-500 bg-gray-12 p-3'>
                <div className='font-inter align-middle text-sm font-normal not-italic leading-[20px] tracking-[0.005em]'>
                  Theo quy định mới, các Hộ kinh doanh cần sử dụng Số chứng thực làm thông tin định danh chính thức. Để
                  đảm bảo tuân thủ, quý khách vui lòng cập nhật Số chứng thực!
                </div>
              </div>
              <Form.Item
                label='Tên đăng nhập'
                name='repPersonalCertNumber'
                rules={[
                  validateRequireInput('Số giấy chứng thực không được bỏ trống'),
                  validateStrLength(6, 12, 'Sai định dạng số chứng thực'),
                  validateCode('Sai định dạng số chứng thực', /^[a-zA-Z0-9]+$/)
                ]}
                normalize={value => formatNormalizeNumber(value)}
                required
              >
                <Input allowClear maxLength={12} placeholder={'Nhập số chứng thực'} />
              </Form.Item>
              <Form.Item name='isAgree' valuePropName='checked' className='-mb-2'>
                <Checkbox>
                  <span className='font-inter  text-sm font-normal not-italic leading-5 tracking-[0.005em]'>
                    Đồng ý thay đổi định danh tài khoản thành
                    <span className=' font-semibold'> Số chứng thực</span>
                  </span>
                </Checkbox>
              </Form.Item>
            </div>
          </Form>
        </Modal>
      )}
    </>
  )
}
