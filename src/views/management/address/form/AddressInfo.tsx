import React, { useEffect, useMemo, useRef } from 'react'

import { Form, Input } from 'antd'

import { useNewLocation } from '@/hooks/useNewLocation'

import { validateMaxLengthStr, validateRequireInput } from '@/validator'
import { useAddressBuilder } from '../hooks/useAddressBuilder'
import LocationGroup from './address/LocationGroup'

const AddressInfo = React.memo(() => {
  const form = Form.useFormInstance()

  const { updateAddress, provinceList, wardList, streetList, loadingProvince, loadingStreet, loadingWard } =
    useNewLocation()

  const provinceId = Form.useWatch('provinceId', form)
  const wardId = Form.useWatch('wardId', form)
  const streetId = Form.useWatch('streetId', form)

  // Sử dụng useRef để theo dõi giá trị trước đó
  const prevValues = useRef({
    provinceId: undefined,
    wardId: undefined,
    streetId: undefined
  })

  const memoizedLists = useMemo(
    () => ({
      provinceList,
      wardList,
      streetList
    }),
    [provinceList, wardList, streetList]
  )

  const loadingStates = useMemo(
    () => ({
      province: loadingProvince,
      ward: loadingWard,
      street: loadingStreet
    }),
    [loadingProvince, loadingWard, loadingStreet]
  )

  // Chỉ sử dụng hook để tự động tạo địa chỉ
  useAddressBuilder({
    provinceId,
    wardId,
    streetId,
    lists: memoizedLists
  })

  // Chỉ gọi updateAddress khi giá trị thực sự thay đổi
  useEffect(() => {
    const { provinceId: prevProvinceId, wardId: prevWardId, streetId: prevStreetId } = prevValues.current

    if (provinceId !== prevProvinceId) {
      updateAddress('provinceId', provinceId)
      prevValues.current.provinceId = provinceId
      form.setFieldsValue({
        wardId: null,
        streetId: null
      })
    }

    if (wardId !== prevWardId) {
      updateAddress('wardId', wardId)
      prevValues.current.wardId = wardId
      form.setFieldsValue({
        streetId: null
      })
    }

    if (streetId !== prevStreetId) {
      updateAddress('streetId', streetId)
      prevValues.current.streetId = streetId
    }
  }, [provinceId, wardId, streetId, updateAddress, form])

  return (
    <div className='mt-8'>
      <div className='inline-flex items-center justify-start gap-2'>
        <div className='h-4 w-0.5 bg-sme-orange-7' />
        <div className='text-sm font-semibold leading-tight tracking-tight text-gray-8'>Thông tin phân loại</div>
      </div>

      <LocationGroup
        provinceList={provinceList}
        wardList={wardList}
        streetList={streetList}
        loading={loadingStates}
        onProvinceChange={value => updateAddress('provinceId', value)}
        onWardChange={value => updateAddress('wardId', value)}
        onStreetChange={value => updateAddress('streetId', value)}
      />

      <Form.Item
        label={<div className='text-xs font-medium text-black'>Địa chỉ</div>}
        className='col-span-2'
        name='address'
        rules={[
          validateRequireInput('Địa chỉ không được bỏ trống'),
          validateMaxLengthStr(200, 'Địa chỉ vượt quá độ dài cho phép')
        ]}
      >
        <Input placeholder='Nhập địa chỉ' />
      </Form.Item>
    </div>
  )
})

AddressInfo.displayName = 'AddressInfo'

export default AddressInfo
