'use client'

import { useEffect, useState } from 'react'

import { usePara<PERSON>, useRouter, useSearchParams } from 'next/navigation'

import { styled } from '@mui/material'
import { useMutation } from '@tanstack/react-query'
import { But<PERSON>, Spin } from 'antd'
import Modal from 'antd/lib/modal/Modal'

import { useUser } from '@/hooks'
import { API_ROOT } from '@/models/Base'
import ThirdPartySource from '@/models/ThirdPartySource'
import User from '@/models/User'

const CustomButton = styled(Button)`
  &:hover,
  &:focus {
    background-color: #394867 !important;
  }
`

export default function VerifyPage() {
  const router = useRouter()
  const { isLoggedIn } = useUser()
  const searchParams = useSearchParams()
  const { id, activeKey }: { id: string; activeKey: string } = useParams()

  const callBack = searchParams.get('callback')

  const [checkError, setCheckError] = useState<boolean | null>(null)
  const [messageError, setMessageError] = useState('')
  const [visibleModal, setVisibleModal] = useState(false)

  const activeMutation = useMutation({
    mutationFn: User.activeAccount,
    onSuccess: () => setCheckError(true),
    onError: (res: { errorCode: any }) => {
      setCheckError(false)

      switch (res.errorCode) {
        case 'error.user.activated':
          setMessageError('Tài khoản đã được kích hoạt trước đó. Vui lòng kiểm tra lại!')
          break
        case 'error.user.activation.key.not.matched':
          setMessageError('Link kích hoạt không còn hiệu lực.  Vui lòng kiểm tra lại!')
          break
        case 'error.user.activation.key.expired':
          setMessageError('Link kích hoạt không còn hiệu lực. Vui lòng kiểm tra lại!')
          break
        default:
          setMessageError('Đã xảy ra lỗi khi kích hoạt tài khoản của bạn! Vui lòng thử lại sau.')
      }
    }
  })

  useEffect(() => {
    const checkUrl = async () => {
      // Đã đăng nhập sẽ yêu cầu đăng xuất trước khi kích hoạt
      if (isLoggedIn) {
        setVisibleModal(true)

        return
      }

      const res = await ThirdPartySource.postUrlSourceLogin()

      if (
        callBack?.includes(`/sme-portal/login`) ||
        callBack === `${API_ROOT}/partner-portal/login` ||
        callBack?.includes(`/sme-portal/forgot-password`) ||
        callBack?.includes(`/bos-portal/login`) ||
        callBack?.includes(`/bos-portal/forgot-password`) ||
        callBack?.includes(`/iot-portal/login`) ||
        callBack?.includes(`/iot-portal/forgot-password`) ||
        callBack?.includes(`/partner-portal/login`) ||
        callBack?.includes(`/partner-portal/forgot-password`) ||
        (res && res.includes(callBack))
      ) {
        activeMutation.mutate({ id, activeKey })
      }
    }

    checkUrl()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  // region Return
  if (visibleModal) {
    return (
      <Modal
        open
        footer={
          <CustomButton
            type='primary'
            className='body-14-medium bg-neutral900 text-white'
            onClick={() => {
              router.push('/bos-portal')
              setVisibleModal(false)
            }}
          >
            Xác nhận
          </CustomButton>
        }
        closable={false}
        width={384}
      >
        <div className='mb-2 text-base font-semibold'>Kích hoạt thất bại</div>
        <div className='text-neutral800 text-sm'>Vui lòng đăng xuất và thực hiện kích hoạt</div>
      </Modal>
    )
  }

  return checkError === null ? (
    <div className='py-16 text-center'>
      <Spin />
    </div>
  ) : (
    <Modal open footer={false} closable={false}>
      <div className='text-center'>
        <div className='mb-4 flex items-center justify-center'>
          {checkError ? (
            <div className='flex items-center justify-center bg-bg-success-lighter p-2' style={{ borderRadius: '50%' }}>
              <i className='onedx-success-icon size-10 text-text-success-default' />
            </div>
          ) : (
            <div className='flex items-center justify-center bg-bg-error-lighter p-2' style={{ borderRadius: '50%' }}>
              <i className='onedx-warning-icon size-10 text-text-error-default' />
            </div>
          )}
        </div>
        <div className='mb-2 text-lg font-bold'>
          {checkError ? 'Tài khoản của bạn đã được kích hoạt!' : 'Tài khoản của bạn kích hoạt không thành công!'}
        </div>
        {/* eslint-disable-next-line no-nested-ternary */}
        {checkError ? (
          callBack?.includes(`/sme-portal/forgot-password`) || callBack?.includes(`/bos-portal/forgot-password`) ? (
            <>
              <div className='mb-4'>Địa chỉ email của bạn đã được xác nhận</div>
              <Button type='primary'>
                <a href={callBack} rel='noreferrer' className='block size-full'>
                  Đến trang quên mật khẩu
                </a>
              </Button>
            </>
          ) : (
            <>
              <div className='mb-4'>
                Địa chỉ email của bạn đã được xác nhận. Bạn có thể đăng nhập trực tiếp vào tài khoản của mình.
              </div>
              <Button type='primary'>
                <a href={callBack as string} rel='noreferrer' className='block size-full'>
                  Đăng nhập
                </a>
              </Button>
            </>
          )
        ) : (
          <div className=''>{messageError}</div>
        )}
      </div>
    </Modal>
  )
  // endregion
}
