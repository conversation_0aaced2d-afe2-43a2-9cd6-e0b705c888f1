'use client'

import React, { useMemo, useRef, useState } from 'react'

import { useRouter } from 'next/navigation'

import type { TabsProps } from 'antd'
import { Button, Checkbox, Form } from 'antd'

// import { isEmpty } from 'lodash'
import { useMutation, useQuery } from '@tanstack/react-query'

// eslint-disable-next-line import/no-named-as-default
import ReCAPTCHA from 'react-google-recaptcha'

import RegisterHouseHoldForm from '@views/auth/register/components/RegisterHouseHoldForm'

import NewAddress from '@/models/NewAddress'

import { checkAll, checkSome } from '@views/auth/utils/constants'

import { weakPasswordContent } from '@views/auth/utils/auth'

import { TAB_CONVERT } from '@views/auth/register/RegisterPage'

import ConfirmModal from '@/components/modal/ConfirmModal'
import { CustomTab } from '../../login/LoginForm'
import RegisterEnterpriseForm from './RegisterEnterpriseForm'
import RegisterPersonalForm from './RegisterPersonalForm'

// import useQueryUrl from '@/hooks/useQueryUrl'
import { TermsModal } from '@/components/modal'
import MasterData from '@/models/MasterData'
import type { NotificationInfoType } from '@/types/notificationModalTypes'
import type { CheckboxesState } from '@/types/registerTypes'
import ModalNotification from '@components/modal/ModalNotification'
import User from '@/models/User'

interface RenderCustomerTypeProps {
  type: any
  enterpriseFunc: any
  householdFunc: any
  personalFunc: any
}

interface RegisterFormProps {
  goRegister: () => void
  goRegisterBusinessHouseHolds: () => void
  goRegisterPersonal: () => void
  formRegister: any
  loading: boolean
  isLoginVnpt: boolean

  // data: any
  forwardRef: any
  active: keyof typeof TAB_CONVERT
  setActive: any
  disableTaxCode: boolean
  step: 1 | 2
  setStep: React.Dispatch<React.SetStateAction<1 | 2>>
  setTaxState: React.Dispatch<React.SetStateAction<{ state: string; lastTaxStr: null }>>
  checkboxes: CheckboxesState
  setCheckboxes: React.Dispatch<React.SetStateAction<CheckboxesState>>
  portalType: 'SME' | 'IOT'
}

/** Các tab loại khách hàng */
const items: TabsProps['items'] = [
  {
    key: '1',
    label: 'Doanh nghiệp'
  },
  {
    key: '2',
    label: 'Hộ kinh doanh'
  },
  {
    key: '3',
    label: 'Cá nhân'
  }
]

const RegisterForm = ({
  // isLoginVnpt,
  goRegister,
  goRegisterBusinessHouseHolds,
  goRegisterPersonal,
  formRegister,
  loading,

  // data,
  forwardRef,
  active,
  setActive,
  disableTaxCode,
  step,
  setStep,
  setTaxState,
  checkboxes,
  setCheckboxes,
  portalType
}: RegisterFormProps) => {
  const router = useRouter()

  const [open, setOpen] = useState(false)

  /** state modal điều khoản sử dụng */
  const [showPolicy, setShowPolicy] = useState(false)

  // const pathname = usePathname()
  // const searchParams = useSearchParams()

  // Captcha
  const captchaRef = useRef(null)

  /** state lưu thông tin hiển thị popup */
  const [infoModal, setInfoModal] = useState<NotificationInfoType>({})
  const [visibleModal, setVisibleModal] = useState(false)

  // State lưu trạng thái đồng ý điều khoản sử dụng tổng
  const [agreedPersonal, setAgreedPersonal] = useState(false)

  const regex = /^[a-zA-Z0-9]+$/

  const isPersonal = TAB_CONVERT[active] === 'CN'

  const isCheckAll = useMemo(() => checkAll(isPersonal, checkboxes), [checkboxes, isPersonal])

  const isCheckSome = useMemo(() => checkSome(isPersonal, checkboxes), [checkboxes, isPersonal])

  // Hàm xử lý gán lại biến checkboxes
  const onSettingCheckbox = (isCheck: boolean) => {
    Object.keys(checkboxes).forEach(key => {
      checkboxes[key as keyof CheckboxesState] = isCheck
    })

    setCheckboxes({ ...checkboxes })
  }

  const handleTabChange = (key: string) => {
    // setTabs(key)

    formRegister.resetFields()

    setActive(`${key}`)
    setStep(1)

    // @ts-ignore
    captchaRef.current.reset()

    // Reset term checkbox trong popup về chưa chọn
    onSettingCheckbox(false)

    setTaxState({ state: 'NONE', lastTaxStr: null })

    setAgreedPersonal(false)
  }

  const { data: provinceList, isFetching: loadingProvince } = useQuery({
    queryKey: ['getProvince'],
    queryFn: async () => {
      const res = await NewAddress.getProvinces()

      if (Array.isArray(res?.content)) {
        return res?.content?.map((item: any) => ({
          value: `${item.id}/${item.code ? item.code : ''}`,
          label: item.name
        }))
      } else {
        return []
      }
    }
  })

  const RenderCustomerType: React.FC<RenderCustomerTypeProps> = ({
    type,
    enterpriseFunc,
    householdFunc,
    personalFunc
  }) => {
    switch (active) {
      case '1':
        return type === 'func' ? enterpriseFunc() : enterpriseFunc
      case '2':
        return type === 'func' ? householdFunc() : householdFunc
      case '3':
        return type === 'func' ? personalFunc() : personalFunc
      default:
        return type === 'func' ? enterpriseFunc() : enterpriseFunc
    }
  }

  const renderNameItemStep = (stepBlock: any) => {
    switch (active) {
      // DN
      case '1':
        return stepBlock === 1
          ? ['password', 'confirmPassword', 'taxCode']
          : ['lastname', 'firstname', 'phoneNumber', 'email', 'smeName', 'provinceId', 'captchaToken']

      // HKD
      case '2':
        return stepBlock === 1
          ? ['password', 'confirmPassword', 'taxCode']
          : ['lastname', 'firstname', 'phoneNumber', 'email', 'smeName', 'provinceId', 'captchaToken']

      // CN
      case '3':
        return stepBlock === 1
          ? ['email', 'password', 'confirmPassword']
          : ['lastname', 'firstname', 'provinceId', 'captchaToken']
      default:
        return []
    }
  }

  // TH enterprise + household => check tin
  const mutationCheckTax = useMutation({
    mutationFn: MasterData.checkTin,
    onSuccess: (response: any) => {
      if (response.existSme === 1) {
        formRegister.setFields([{ name: 'taxCode', errors: ['Mã số thuế đã được đăng ký tài khoản'] }])
        formRegister.resetFields(['taxCode'])

        setVisibleModal(true)
        setInfoModal({
          iconType: 'ERROR',
          title: 'Mã số thuế đã sử dụng',
          textButton: 'Đóng',
          isContact: true,
          customerType: TAB_CONVERT[active],
          subTitle: `Vui lòng kiểm tra lại mã số thuế hoặc liên hệ với quản trị viên để được hỗ trợ`
        })
        formRegister.setFieldsValue({ smeName: undefined, hasTax: 'NO' })
        setTaxState({ state: 'NONE', lastTaxStr: formRegister.getFieldValue('taxCode') })

        // document.getElementById('inputTaxCode').classList.add('show-clear')
      } else {
        formRegister.setFieldsValue({
          smeName: response.company,
          email: response.email,
          lastname: response.lastName,
          firstname: response.firstName,
          phoneNumber: response.phone,
          provinceId:
            response.provinceId && response.provinceCode ? `${response.provinceId}/${response.provinceCode}` : '',
          hasTax: 'YES'
        })

        setTaxState({
          state: active === '1' || undefined ? 'BUSINESS_YES' : 'BUSINESS_HOUSEHOLDS_YES',
          lastTaxStr: formRegister.getFieldValue('taxCode')
        })

        if (document.getElementById('inputTaxCode')) {
          document.getElementById('inputTaxCode')?.classList.remove('show-clear')
        }
      }
    },

    // && queryUrl?.get('mst') === null
    onError: (res: any) => {
      if (res?.errorCode === 'error.data.exists' && res?.field === 'taxCode') {
        formRegister.setFields([{ name: 'taxCode', errors: ['Mã số thuế đã được đăng ký tài khoản'] }])
        formRegister.resetFields(['taxCode'])

        setVisibleModal(true)
        setInfoModal({
          iconType: 'WARNING',
          title: 'Mã số thuế đã được đăng ký tài khoản',
          textButton: 'Đóng',
          typeButton: 'secondary',
          isContact: true,
          customerType: TAB_CONVERT[active],
          subTitle: `Vui lòng kiểm tra lại mã số thuế hoặc liên hệ với quản trị viên để được hỗ trợ`
        })
        formRegister.setFieldsValue({ smeName: undefined, hasTax: 'NO' })
        setTaxState({ state: 'NONE', lastTaxStr: formRegister.getFieldValue('taxCode') })
        document.getElementById('inputTaxCode')?.classList?.add('show-clear')
      } else {
        formRegister.setFieldsValue({ smeName: undefined, hasTax: 'NO' })
        setTaxState({ state: 'NO', lastTaxStr: formRegister.getFieldValue('taxCode') })

        if (document.getElementById('inputTaxCode')) {
          document.getElementById('inputTaxCode')?.classList.remove('show-clear')
        }
      }
    }
  })

  const checkTax = async (e: { key: string }, type: string) => {
    if (type === 'keyup' && e.key !== 'Enter') {
      return
    }

    const taxError = formRegister.getFieldError('taxCode')
    const taxStr = formRegister.getFieldValue('taxCode')

    if (taxError.length > 0 || !taxStr) {
      setTaxState({ state: 'NONE', lastTaxStr: taxStr })

      return
    }

    mutationCheckTax.mutate(taxStr)
  }

  // Hàm xử lý sau khi click vào nút đồng ý điều khoản popup chính sách
  const handleAgreeTerms = (checkboxes: React.SetStateAction<CheckboxesState>) => {
    setCheckboxes(checkboxes)
    setShowPolicy(false)
    setAgreedPersonal(true)
  }

  const handleNextStep = async () => {
    try {
      await Promise.all([
        User.validatePasswordQuality({
          password: formRegister.getFieldValue('password')
        }),
        formRegister.validateFields(renderNameItemStep(1))
      ])

      setStep(2)
    } catch (error: any) {
      if (error?.errorCode === 'error.weak.password') {
        setVisibleModal(true)
        setInfoModal({
          iconType: 'WARNING',
          title: 'Mật khẩu chưa đủ bảo mật',
          textButton: 'Xác nhận',
          subTitle: weakPasswordContent,
          isCloseModal: false
        })
      }
    }
  }

  return (
    <>
      <Form form={formRegister} autoComplete='off' layout='vertical'>
        {/* <CustomTab onChange={handleTabChange} activeKey={active} className='mt-2 w-full text-base'>
          <TabPane tab='Doanh nghiệp' key='1' />
          <TabPane tab='Hộ kinh doanh' key='2' />
          <TabPane tab='Cá nhân' key='3' />
        </CustomTab> */}
        <CustomTab defaultActiveKey='1' items={items} onChange={handleTabChange} />
        <RenderCustomerType
          type='params'
          enterpriseFunc={
            <RegisterEnterpriseForm
              step={step}
              provinceList={provinceList}
              loadingProvince={loadingProvince}
              checkTax={checkTax}
              regex={regex}
              forwardRef={forwardRef}
              disableTaxCode={disableTaxCode}
              portalType={portalType}
            />
          }
          householdFunc={
            <RegisterHouseHoldForm
              step={step}
              provinceList={provinceList}
              loadingProvince={loadingProvince}
              checkTax={checkTax}
              regex={regex}
              forwardRef={forwardRef}
              disableTaxCode={disableTaxCode}
              portalType={portalType}
            />
          }
          personalFunc={
            <RegisterPersonalForm
              step={step}
              provinceList={provinceList}
              loadingProvince={loadingProvince}
              portalType={portalType}
            />
          }
        />

        {/* Step 1 */}
        <div hidden={step !== 1}>
          <div className='mb-10 mt-6 flex items-center gap-2 sm:my-4'>
            <Checkbox
              checked={isCheckAll}
              indeterminate={isCheckAll ? false : isCheckSome || (isPersonal && agreedPersonal)} // Nếu user không đồng ý chính sách trong modal nhưng đã đồng ý chính sách bên ngoài => tick một phần
              onChange={e => {
                const isCheck = e.target.checked

                isPersonal && setAgreedPersonal(isCheck)
                onSettingCheckbox(isCheck)
              }}
            />
            {portalType === 'SME' ? (
              <div>
                Đồng ý với&nbsp;
                <span
                  className='cursor-pointer text-primary hover:underline'
                  onClickCapture={e => {
                    e.preventDefault()
                    setShowPolicy(true)
                  }}
                >
                  {isPersonal ? 'Điều khoản sử dụng và chính sách bảo vệ thông tin khách hàng' : 'Điều khoản sử dụng'}
                </span>
              </div>
            ) : (
              <div className='body-14-regular'>Đồng ý với Chính sách và Điều khoản</div>
            )}
          </div>
          <Button
            className='w-full bg-bg-primary-default text-sm font-medium text-white'
            type='primary'
            onClick={handleNextStep}
            disabled={!isCheckAll && !isCheckSome && (!isPersonal || !agreedPersonal)}
          >
            Tiếp tục
          </Button>
        </div>

        {/* Step 2 */}
        {/* Hiển thị capcha */}

        <div hidden={step !== 2} className='mt-10'>
          <div className='mb-4'>
            <Form.Item name='captchaToken' noStyle hidden />
            <ReCAPTCHA
              ref={captchaRef}
              sitekey={process.env.NEXT_PUBLIC_RECAPCHA_KEY || '6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI'}
              hl='vi'
              onChange={val => {
                formRegister.setFieldsValue({ captchaToken: val })
              }}
            />
          </div>
          <Button
            className='mb-4 w-full bg-bg-primary-default text-sm font-medium'
            onClick={() => {
              formRegister.validateFields(renderNameItemStep(2)).then(() => {
                RenderCustomerType({
                  type: 'func',
                  enterpriseFunc: goRegister,
                  householdFunc: goRegisterBusinessHouseHolds,
                  personalFunc: goRegisterPersonal
                })

                // @ts-ignore
                captchaRef.current.reset()
              })
            }}
            type='primary'
            loading={loading}
          >
            Đăng ký
          </Button>
        </div>
      </Form>

      <div className='mt-4 flex flex-wrap items-center justify-center gap-2 sm:mt-3'>
        <div className='text-body-14 font-normal' style={{ color: portalType === 'SME' ? '' : '#7F8898' }}>
          Bạn đã có tài khoản?
        </div>
        <Button
          type='link'
          className='cursor-pointer text-body-14 font-medium text-text-primary-default no-underline'
          onClick={() => router.push('/sme-portal/login')}
        >
          {portalType === 'SME' ? 'Đăng nhập' : 'Đăng nhập ngay'}
        </Button>
      </div>

      <ConfirmModal
        open={open}
        setOpen={setOpen}
        btnText='Đăng nhập'
        modalTitle='Đăng ký thành công'
        description='Bạn có thể đăng nhập vào tài khoản của mình với tên đăng nhập 00004679.'
        onClick={() => router.push('/sme-portal/login')}
      />

      {/* modal Điều khoản sử dụng */}
      {showPolicy && (
        <TermsModal
          openModal={showPolicy}
          setOpenModal={setShowPolicy}
          handleAgreeTerms={value => handleAgreeTerms(value as CheckboxesState)}
          initCheckbox={checkboxes}
          customerType={TAB_CONVERT[active]}
        />
      )}

      {/* modal thông báo các message trong quá trình đăng ký */}
      <ModalNotification
        visibleModal={visibleModal}
        setVisibleModal={setVisibleModal}
        infoModal={infoModal}
        layoutLogin={infoModal.typeEmail}
      />
    </>
  )
}

export default RegisterForm
