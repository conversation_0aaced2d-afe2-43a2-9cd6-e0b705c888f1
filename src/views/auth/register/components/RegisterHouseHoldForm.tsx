'use client'

import React from 'react'

import { Form, Input, Radio, Select } from 'antd'

import { trim } from 'lodash'

import { REGEX_MAIL_OR_TAX_CODE_OR_CERT } from '@views/auth/utils/constants'

import { IDENTITY_TYPE, PORTAL_TYPE } from '@/constants/constantEnum'

import {
  formatNormalizeNumber,
  validateCode,
  validateCustomPattern,
  validateEmail,
  validatePassword,
  validatePhoneNumber,
  validateRequireInput,
  validateStrLength
} from '@/validator'
import { EmptyFormItem } from '@views/auth/register/components/RegisterPersonalForm'

interface HouseHoldRegisterFormProps {
  step: number
  loadingProvince: any
  provinceList: any
  regex: any
  checkTax: any
  disableTaxCode: boolean
  forwardRef: any
  portalType: 'SME' | 'IOT'
}

const RegisterHouseHoldForm = ({
  step,
  loadingProvince,
  provinceList,
  regex,
  checkTax,
  disableTaxCode,
  forwardRef,
  portalType
}: HouseHoldRegisterFormProps) => {
  return (
    <>
      {/* Đăng ký: Bước 1 */}
      <div hidden={step !== 1}>
        <Form.Item
          label='Thông tin định danh'
          name='identityTypeHKD'
          rules={[{ required: true, message: 'Vui lòng chọn trạng thái' }]}
          initialValue={IDENTITY_TYPE.CERT_NUMBER}
        >
          <Radio.Group className='flex gap-24'>
            <Radio value={IDENTITY_TYPE.TAX_CODE}>Mã số thuế</Radio>
            <Radio value={IDENTITY_TYPE.CERT_NUMBER}>Số chứng thực</Radio>
          </Radio.Group>
        </Form.Item>

        <div>
          <Form.Item noStyle dependencies={['identityTypeHKD']}>
            {({ getFieldValue }) => {
              const identityTypeHKD = getFieldValue('identityTypeHKD')

              return identityTypeHKD === IDENTITY_TYPE.TAX_CODE ? (
                <Form.Item
                  label='Tên đăng nhập'
                  name='taxCode'
                  rules={[
                    validateRequireInput('Mã số thuế không được bỏ trống'),
                    validateStrLength(10, 13, 'Sai định dạng mã số thuế'),
                    validateCode('Sai định dạng mã số thuế', regex)
                  ]}
                  normalize={value => formatNormalizeNumber(value)}
                  className={portalType === PORTAL_TYPE.SME ? 'sm:mb-4' : 'mb-2'}
                  required
                >
                  <Input
                    allowClear
                    maxLength={13}
                    placeholder={'Nhập mã số thuế'}
                    onBlur={e => checkTax(e, 'blur')}
                    onKeyUp={e => checkTax(e, 'keyup')}
                    disabled={disableTaxCode}
                    ref={forwardRef}
                  />
                </Form.Item>
              ) : (
                <Form.Item
                  label='Tên đăng nhập'
                  name='repPersonalCertNumber'
                  rules={[
                    validateRequireInput('Số giấy chứng thực không được bỏ trống'),
                    validateCustomPattern(REGEX_MAIL_OR_TAX_CODE_OR_CERT, 'Sai định dạng số chứng thực')
                  ]}
                  normalize={value => formatNormalizeNumber(value)}
                  className={portalType === PORTAL_TYPE.SME ? 'sm:mb-4' : 'mb-2'}
                  required
                >
                  <Input
                    allowClear
                    maxLength={13}
                    placeholder={'Nhập số chứng thực'}
                    onBlur={e => checkTax(e, 'blur')}
                    onKeyUp={e => checkTax(e, 'keyup')}
                    disabled={disableTaxCode}
                    ref={forwardRef}
                  />
                </Form.Item>
              )
            }}
          </Form.Item>
        </div>

        <Form.Item
          label='Nhập mật khẩu'
          name='password'
          normalize={trim}
          rules={[
            validateRequireInput('Mật khẩu không được bỏ trống'),
            validatePassword(
              'Mật khẩu phải có từ 8-16 ký tự, bao gồm ít nhất 1 chữ viết hoa, 1 chữ viết thường, 1 chữ số và 1 ký tự đặc biệt trong !@#$%^&+= (không bao gồm ký tự "space")'
            )
          ]}
          className={portalType === PORTAL_TYPE.SME ? 'sm:mb-4' : 'mb-2'}
        >
          <Input.Password type='password' placeholder='Nhập mật khẩu' maxLength={16} autoComplete='new-password' />
        </Form.Item>

        <Form.Item
          label={portalType === PORTAL_TYPE.SME ? 'Xác nhận mật khẩu' : 'Nhập lại mật khẩu'}
          name='confirmPassword'
          normalize={trim}
          dependencies={['password']}
          rules={[
            validateRequireInput('Xác nhận mật khẩu không được bỏ trống'),
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue('password') === value) {
                  return Promise.resolve()
                }

                return Promise.reject('Xác nhận mật khẩu không khớp với mật khẩu')
              }
            })
          ]}
          className='sm:mb-4'
        >
          <Input.Password type='password' maxLength={16} placeholder='Nhập lại mật khẩu' />
        </Form.Item>
      </div>

      {/* Đăng ký: Bước 2*/}
      <div hidden={step !== 2}>
        <EmptyFormItem label='Họ và tên' required />
        <div className='flex items-center justify-between gap-6'>
          <Form.Item
            className='w-1/2 sm:mb-4'
            name='lastname'
            rules={[validateRequireInput('Họ không được bỏ trống')]}
            required
          >
            <Input maxLength={20} placeholder='Nhập họ' />
          </Form.Item>
          <Form.Item
            className='w-1/2 sm:mb-4'
            name='firstname'
            rules={[validateRequireInput('Tên không được bỏ trống')]}
            required
          >
            <Input maxLength={20} placeholder='Nhập tên' />
          </Form.Item>
        </div>

        <div className='flex items-start justify-between gap-6 sm:block'>
          <Form.Item
            name='phoneNumber'
            label='Số điện thoại'
            className='w-1/2 sm:mb-4 sm:w-full'
            rules={[
              validateRequireInput('Số điện thoại không được bỏ trống'),
              validatePhoneNumber('Sai định dạng số điện thoại')
            ]}
            required
          >
            <Input maxLength={12} placeholder='Nhập số điện thoại' />
          </Form.Item>
          <Form.Item
            name='email'
            label='Email'
            className='w-1/2 sm:mb-4 sm:w-full'
            rules={[validateRequireInput('Email không được bỏ trống'), validateEmail('Sai định dạng email')]}
            required
          >
            <Input maxLength={100} placeholder='Nhập email' />
          </Form.Item>
        </div>
        <Form.Item
          name='provinceId'
          label='Tỉnh/Thành phố'
          required
          rules={[validateRequireInput('Tỉnh/Thành phố không được bỏ trống')]}
          className='w-full sm:mb-4'
        >
          <Select placeholder='Chọn thành phố/tỉnh' options={provinceList} loading={loadingProvince} />
        </Form.Item>
      </div>
    </>
  )
}

export default RegisterHouseHoldForm
