'use client'

import { useState } from 'react'

import { useRouter } from 'next/navigation'

import { Button, Form, Input, Tabs } from 'antd'

import { useForm } from 'antd/es/form/Form'

import { useMutation } from '@tanstack/react-query'

import { setCookie, getCookie } from 'cookies-next'

import { PORTAL_TYPE } from '@/constants/constantEnum'

import { CUSTOMER_TYPE } from '@views/product-catalog/constants/constants'

import AuthPageLayout from '@components/layout/auth-page/AuthPageLayout'
import IotAuthPageLayout from '@components/layout/auth-page/IotAuthPageLayout'
import { API_ROOT } from '@/models/Base'
import Users from '@/models/User'
import { CustomTab } from '../login/LoginForm'
import { validateCustomPattern, validateRequireInput } from '@/validator'
import type { InfoModal } from './ResetPasswordForm'
import ConfirmModal from '@/components/modal/ConfirmModal'
import {
  ACTION_TYPE,
  CUSTOMER_TYPE_CONVERT,
  PARAM_SEND_OTP_COOKIE_NAME,
  REGEX_MAIL_OR_TAX_CODE,
  REGEX_MAIL_OR_TAX_CODE_OR_CERT
} from '../utils/constants'
import ModalNotification from '@/components/modal/ModalNotification'
import type { NotificationInfoType } from '@/types/notificationModalTypes'
import ContactFormModal from '@/views/enterprise/contact-customer/ContactFormModal'

export type InfoNotification = {
  iconType?: string
  title?: string
  textButton?: string
  subTitle?: string
  methodOTP?: any
  isCloseModal?: boolean
  handleConfirm: {}
}

interface ForgotPasswordFormProps {
  portalType: 'SME' | 'IOT'
}

const ForgotPasswordForm = ({ portalType }: ForgotPasswordFormProps) => {
  const router = useRouter()
  const [tabs, setTabs] = useState<keyof typeof CUSTOMER_TYPE_CONVERT>('ENTERPRISE')
  const [form] = useForm()
  const [visibleModal, setVisibleModal] = useState(false)
  const [visibleModalNotification, setVisibleModalNotification] = useState(false)
  const [visibleModalContact, setVisibleModalContact] = useState(false)

  const [infoModal, setInfoModal] = useState<InfoModal>({
    title: '',
    btnText: '',
    description: '',
    redirectPage: () => {}
  })

  const [infoNotification, setInfoNotification] = useState<NotificationInfoType>({
    iconType: 'WARNING',
    title: '',
    textButton: '',
    subTitle: '',
    handleConfirm: {}
  })

  const handleTabChange = (key: any) => {
    setTabs(key)
    form.resetFields()
  }

  const successModal: InfoModal = {
    title: (
      <div className='flex items-center justify-start gap-4'>
        <div className='flex size-10 items-center justify-center rounded-full bg-sme-blue-2'>
          <i className='onedx-mail-icons size-6'></i>
        </div>
        <div className='headline-16-semibold'>Hệ thống gửi email hướng dẫn thành công</div>
      </div>
    ),
    btnText: 'Đóng',
    description: (
      <>
        <div className='body-14-regular mt-2'>Vui lòng kiểm tra email đăng ký của bạn và làm theo hướng dẫn</div>
      </>
    ),
    redirectPage: () => setVisibleModal(false)
  }

  const errorModal: InfoModal = {
    title: 'Gửi token tạm thời tới email lỗi',
    btnText: 'Đóng',
    description: 'Đã có lỗi xảy ra. Vui lòng thử lại sau một vài phút.',
    redirectPage: () => router.push(`/sme-portal/login`)
  }

  const errorModalDisable: InfoModal = {
    title: (
      <div className='flex items-center justify-start gap-4'>
        <div className='flex size-10 items-center justify-center rounded-full bg-red-1'>
          <i className='onedx-warning-less-icon size-6'></i>
        </div>
        <div className='headline-16-semibold'>Tài khoản đã bị vô hiệu hóa</div>
      </div>
    ),
    btnText: 'Liên hệ',
    btnCancel: 'Đóng',
    description: (
      <>
        <div className='body-14-regular mt-2'>Liên hệ với quản trị viên để được hỗ trợ</div>
      </>
    ),
    redirectPage: () => {
      setVisibleModal(false), setVisibleModalContact(true)
    }
  }

  const mutationSendOTP = useMutation({
    mutationFn: Users.sendOTP,
    onSuccess: () => {
      if (portalType === 'IOT') {
        router.push('/iot-portal/verification-code?actionType=CHANGE_PASSWORD')
      } else {
        router.push('/sme-portal/verification-code?actionType=CHANGE_PASSWORD')
      }
    },
    onError: () => {
      setVisibleModal(true)
      setInfoModal(errorModal)
    }
  })

  const mutationSendLinkActivate = useMutation({
    mutationFn: Users.resendActivationMail,
    onSuccess: () => {
      router.push(`/sme-portal/forgot-password`)
    },
    onError: () => {
      setVisibleModal(true)
      setInfoModal(errorModal)
    }
  })

  // Xử lý xác nhận để chuyển sang màn nhập OTP
  const handleConfirm = (value: { methodSendOTP: any }) => {
    const paramSendOTP = getCookie(PARAM_SEND_OTP_COOKIE_NAME)

    if (paramSendOTP && JSON.parse(paramSendOTP).hash) {
      const bodySendOTP = {
        ...JSON.parse(paramSendOTP),
        sendType: value.methodSendOTP
      }

      setCookie(PARAM_SEND_OTP_COOKIE_NAME, JSON.stringify(bodySendOTP))
      mutationSendOTP.mutate(bodySendOTP)
    } else {
      const bodySendLinkActivate = {
        username: form.getFieldValue('username'),
        redirectUrl: `${API_ROOT}/sme-portal/forgot-password?customerType=${tabs}`
      }

      mutationSendLinkActivate.mutate(bodySendLinkActivate)
    }
  }

  const mutation = useMutation({
    mutationFn: Users.forgotPassword,
    onSuccess: (res: any) => {
      if (res.needActivate) {
        const paramSendOTP = {
          username: form.getFieldValue('username'),
          hash: res.hashOTP,
          actionType: ACTION_TYPE.ACTIVATE
        }

        setCookie(PARAM_SEND_OTP_COOKIE_NAME, paramSendOTP)

        // check cấu hình Đặt lại mật khẩu
        if (res.hashOTP) {
          setVisibleModalNotification(true)
          setInfoNotification({
            iconType: 'WARNING',
            title: 'Tài khoản của quý khách chưa được kích hoạt',
            textButton: 'Xác nhận',
            subTitle: 'Chúng tôi sẽ gửi mã kích hoạt đến email hoặc số điện thoại của quý khách đã đăng ký',
            methodOTP: { emailMask: res.emailMask, phoneMask: res.phoneMask },
            isCloseModal: true,
            handleConfirm
          })
        } else {
          setVisibleModalNotification(true)
          setInfoNotification({
            iconType: 'WARNING',
            title: 'Tài khoản của quý khách chưa được kích hoạt',
            subTitle: `Chúng tôi sẽ gửi mã kích hoạt đến email ${res.emailMask} của quý khách`,
            textButton: 'Xác nhận',
            isCloseModal: true,
            handleConfirm
          })
        }
      } else if (res.hashOTP) {
        const paramSendOTP = {
          username: form.getFieldValue('username'),
          hash: res.hashOTP,
          actionType: ACTION_TYPE.CHANGE_PASSWORD
        }

        setCookie(PARAM_SEND_OTP_COOKIE_NAME, JSON.stringify(paramSendOTP))

        // handleConfirm({ methodSendOTP: 'EMAIL' })
        setVisibleModalNotification(true)
        setInfoNotification({
          iconType: 'WARNING',
          title: 'Xác thực mã OTP',
          textButton: 'Xác nhận',
          subTitle: 'Vui lòng chọn hình thức gửi mã OTP xác thực',
          methodOTP: { emailMask: res.emailMask, phoneMask: res.phoneMask },
          isCloseModal: true,
          handleConfirm
        })
      } else {
        setVisibleModal(true)
        setInfoModal(successModal)
      }
    },
    onError: (error: any) => {
      if (error?.field === 'users' && ['error.object.not.found', 'error.object.not.exist'].includes(error?.errorCode)) {
        form.setFields([
          {
            name: 'username',
            errors: ['Tên đăng nhập không tồn tại']
          }
        ])
      } else if (error.field === 'status' && error.errorCode === 'error.user.disable') {
        setVisibleModal(true)
        setInfoModal(errorModalDisable)
      }
    }
  })

  const handleSubmit = (event: any) => {
    const value = { ...event, portal: portalType === 'IOT' ? 'IOT_PORTAL' : 'SME', customerType: tabs }

    mutation.mutate(value)
  }

  const { TabPane } = Tabs

  // region Return
  const Layout = portalType === 'IOT' ? IotAuthPageLayout : AuthPageLayout

  return (
    <Layout
      title='Quên mật khẩu'
      description={`Đặt lại mật khẩu để tiếp tục sử dụng ${portalType === 'IOT' ? 'VNPT IoT Marketplace' : 'oneSME'}`}
      goBackText='Quay lại'
      onGoBack={() => router.back()}
      customerType={CUSTOMER_TYPE_CONVERT[tabs]}
    >
      <div className='flex items-center justify-center'>
        <div className='flex w-full flex-col'>
          <CustomTab onChange={handleTabChange} activeKey={tabs} className='mt-2 w-full text-base'>
            <TabPane tab='Doanh nghiệp' key='ENTERPRISE' />
            <TabPane tab='Hộ kinh doanh' key='HOUSE_HOLD' />
            <TabPane tab='Cá nhân' key='PERSONAL' />
          </CustomTab>
          <Form layout='vertical' form={form} onFinish={handleSubmit}>
            {tabs === CUSTOMER_TYPE.ENTERPRISE && (
              <Form.Item
                label='Tên đăng nhập'
                name='username'
                className='w-full'
                rules={[
                  validateRequireInput('Tên đăng nhập không được bỏ trống'),
                  validateCustomPattern(REGEX_MAIL_OR_TAX_CODE, 'Sai định dạng mã số thuế')
                ]}
              >
                <Input
                  maxLength={100}
                  autoFocus
                  placeholder={`${portalType === 'IOT' ? 'Nhập mã số thuế/email' : 'Tên đăng nhập'}`}
                />
              </Form.Item>
            )}
            {tabs === CUSTOMER_TYPE.HOUSE_HOLD && (
              <Form.Item
                label='Tên đăng nhập'
                name='username'
                className='w-full'
                rules={[
                  validateRequireInput('Tên đăng nhập không được bỏ trống'),
                  validateCustomPattern(
                    REGEX_MAIL_OR_TAX_CODE_OR_CERT,
                    'Tên đăng nhập không chính xác. Vui lòng kiểm tra lại!'
                  )
                ]}
              >
                <Input
                  maxLength={13}
                  allowClear
                  placeholder={`${portalType === PORTAL_TYPE.IOT ? 'Nhập mã số thuế/email/số chứng thực' : 'Tên đăng nhập'}`}
                />
              </Form.Item>
            )}
            {tabs === CUSTOMER_TYPE.PERSONAL && (
              <Form.Item
                label={portalType === 'IOT' ? 'Tên đăng nhập ' : 'Email'}
                name='username'
                rules={[
                  validateRequireInput('Email không được bỏ trống'),
                  validateCustomPattern(REGEX_MAIL_OR_TAX_CODE, 'Email sai định dạng')
                ]}
              >
                <Input maxLength={100} autoFocus placeholder='Nhập email' />
              </Form.Item>
            )}

            <Button type='primary' htmlType='submit' className='w-full bg-bg-primary-default text-white'>
              Tiếp tục
            </Button>
          </Form>
        </div>
      </div>
      {visibleModal && (
        <ConfirmModal
          open={visibleModal}
          setOpen={setVisibleModal}
          modalTitle={infoModal.title}
          description={infoModal.description}
          btnText={infoModal.btnText}
          btnCancel={infoModal.btnCancel}
          onClick={() => infoModal.redirectPage()}
          onClickCancel={() => setVisibleModal(false)}
        />
      )}
      {visibleModalNotification && (
        <ModalNotification
          visibleModal={visibleModalNotification}
          setVisibleModal={setVisibleModalNotification}
          infoModal={infoNotification}
        />
      )}
      {visibleModalContact && (
        <ContactFormModal
          isModalVisible={visibleModalContact}
          setIsModalVisible={setVisibleModalContact}
          customerType='KHCN'
        />
      )}
    </Layout>
  )
  // endregion
}

export default ForgotPasswordForm
