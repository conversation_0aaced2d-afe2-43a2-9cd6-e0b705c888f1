'use client'

// React Imports
import React, { useEffect, useRef, useState } from 'react'

import { Button, Divider, Form, Input, message } from 'antd'

import { useMutation, useQuery } from '@tanstack/react-query'

import Cookies from 'js-cookie'

import { trim } from 'lodash'

// eslint-disable-next-line import/no-named-as-default
import ReCAPTCHA from 'react-google-recaptcha'

// import superagent from 'superagent'

import { API_ROOT, clearToken, getTokenByUsernamePassword, setToken } from '@/models/Base'

import { useUser } from '@/hooks/useUser'

import DevModalNotification from '@/components/modal/DevModalNotification'
import { trackLogin } from '@/components/third-parties/matomo/utils/tracking'
import DXPortal from '@/models/DXPortal'
import SystemParam from '@/models/SystemParam'
import Users from '@/models/User'
import type { NotificationInfoType } from '@/types/notificationModalTypes'
import { EXPIRED_PASSWORD_CONTENT, PASSWORD_ISSUE, SENDMAIL_SUCCESS_CONTENT } from '@/constants/auth'
import { isEmail } from '@/utils/string'
import { validateEmail, validateRequireInput } from '@/validator'
import { createStoreSessionIfNotExist } from '../utils/createStoreSessionIfNotExist'
import DevLoginCaptchaWarningModal from './DevLoginCaptchaWarningModal'
import WarningModal from '@/components/modal/WarningModal'
import SuccessModal from '@/components/modal/SuccessModal'

// region Interface
interface ILoginForm {
  username: string
  password: string
  ipAddress?: string
  parent_id?: number
  captchaToken?: any
}

interface LoginFormProps {
  AuthLayout: React.ComponentType<any>
  footer?: React.ReactNode
}

const defaultFooter = (
  <div className='flex justify-between'>
    <div className='flex gap-2 text-body-14'>
      <div>Bạn chưa có tài khoản?</div>
      <div
        onClick={() => (window.location.href = '/partner-portal/register')}
        className='cursor-pointer font-medium text-primary'
      >
        Đăng ký
      </div>
    </div>
    <div className='text-center'>
      <div
        className='cursor-pointer text-sm font-medium text-primary'
        onClick={() => {
          Cookies.remove('infoSwitchAccount')
          window.location.href = '/partner-portal/forgot-password'
        }}
      >
        Quên mật khẩu
      </div>
    </div>
  </div>
)

const DEFAULT_KEY = '6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI'

const EMAIL_REGEX =
  /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9]{2,}(?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+$/

const DevLoginForm: React.FC<LoginFormProps> = ({ AuthLayout, footer = defaultFooter }) => {
  const [form] = Form.useForm<ILoginForm>()

  const { updateUser, changeStatus } = useUser()

  // region Captcha
  // Capcha cho màn login
  const captchaRef = useRef(null)

  // Hiển thị modal cảnh báo captcha
  const [warningModal, setShowWarningModal] = useState(false)

  const [warningType, setWarningType] = useState('warning')

  const [captchaToken, setCaptchaToken] = useState(null)

  // Lấy thông tin cấu hình captcha
  const { data: dataConfig } = useQuery({
    queryKey: ['getCaptchaConfigurationConfig'],
    queryFn: async () => {
      const res = await SystemParam.getSecurityConfig()

      return JSON.parse(res?.paramTextValue)
    },
    retry: 3
  })

  const [visibleModalNotification, setVisibleModalNotification] = useState(false)
  const [valueReactive, setValueReactive] = useState(false)
  const [infoModal, setInfoModal] = useState<NotificationInfoType>({})
  const [openExpiredPasswordModal, setOpenExpiredPasswordModal] = useState(false)
  const [openSendEmailSuccess, setOpenSendEmailSuccess] = useState(false)

  // Biến kiểm tra hiển thị pop up login với nhiều doanh nghiệp
  // const [open, setOpen] = useState(false)

  // const [openPolicyApprovalModal, setOpenPolicyApprovalModal] = useState(false)
  const [isConfirmPolicy, setIsConfirmPolicy] = useState(false) // biến kiểm tra đã xác nhận điều khoản hay chưa

  const captchaConfig = dataConfig?.captchaConfig?.isOldSme

  // endregion

  // region Login Success
  // Xử lý sau khi login thành công
  const handleLoginSuccess = async () => {
    try {
      const { getSessionInfoDefault } = Users

      const newUser = await Users.getMyProfile()

      if (DXPortal.dev.canAccessPortal(newUser)) {
        updateUser(newUser)

        trackLogin(newUser)

        Cookies.set('userId', JSON.stringify(newUser.id), { expires: 1 })
        Cookies.set('confirmedDataPolicies', JSON.stringify(false))
        createStoreSessionIfNotExist()
        const sessionInfo = Cookies.get('sessionInfo')
        const mapData = sessionInfo ? JSON.parse(sessionInfo) : {}

        if (!mapData[newUser.id]) {
          mapData[newUser.id] = getSessionInfoDefault()
          Cookies.set('sessionInfo', JSON.stringify(mapData), { expires: 1 / 48 })
        }
      } else {
        changeStatus(Users.ACC_STATUS.DENIED_FROM_LOGIN)
        setVisibleModalNotification(true)
        setInfoModal({
          iconType: 'WARNING',
          title: 'Cảnh báo đăng nhập',
          textButton: 'Quên mật khẩu',
          typeButton: 'secondary',
          redirectPage: `${DXPortal.dev.createPath('/forgot-password', null)}`,
          isCloseModal: true,
          closeModalTextButton: 'Xác nhận',
          subTitle: `Tên đăng nhập hoặc mật khẩu không chính xác! Quý khách lưu ý tài khoản sẽ bị khóa nếu nhập sai mật khẩu từ 5 lần trở lên`,
          width: 450
        })
        clearToken()
      }

      // TODO: refetch()
      localStorage.setItem('Home.getListContact', '')
    } catch (e) {
      setVisibleModalNotification(true)
      setInfoModal({
        iconType: 'WARNING',
        title: 'Cảnh báo đăng nhập',
        textButton: 'Quên mật khẩu',
        typeButton: 'secondary',
        redirectPage: `${DXPortal.dev.createPath('/forgot-password', null)}`,
        isCloseModal: true,
        closeModalTextButton: 'Xác nhận',
        subTitle: `Tên đăng nhập hoặc mật khẩu không chính xác! Quý khách lưu ý tài khoản sẽ bị khóa nếu nhập sai mật khẩu từ 5 lần trở lên`,
        width: 450
      })
      console.log(e)
    }
  }

  // API gửi link tái kích hoạt Tài khoản
  const mutationSendLinkReActivate = useMutation({
    mutationFn: Users.resendActivationMail,
    onSuccess: () => {
      setVisibleModalNotification(true)
      const username = form.getFieldValue('username')
      const value = isEmail(username) ? username : valueReactive

      setInfoModal({
        iconType: 'ACTIVE',
        title: 'Gửi lại email kích hoạt thành công',
        textButton: 'Đóng',
        redirectPage: `${DXPortal.dev.createPath('/login', null)}`,
        subTitle: `Một email đã được gửi đến địa chỉ ${value} của bạn. Vui lòng kiểm tra trong hộp thư để hoàn tất quá trình.`,
        width: 450
      })
    },
    onError: () => {
      setVisibleModalNotification(true)
      setInfoModal({
        iconType: 'ERROR',
        title: 'Đăng ký thất bại',
        subTitle: 'retryError',
        textButton: 'Đóng',
        redirectPage: `${DXPortal.dev.createPath('/login', null)}`,
        width: 450
      })
    }
  })

  // Kích hoạt tài khoản
  const handleReactive = () => {
    const bodySendLinkActivate = {
      username: form.getFieldValue('username'),
      redirectUrl: `${API_ROOT}/partner-portal/login`
    }

    mutationSendLinkReActivate.mutate(bodySendLinkActivate)
  }
  // endregion

  const recoveryAccountMutation = useMutation({
    mutationFn: Users.recoveryAccount,
    onSuccess: () => {
      setShowWarningModal(false)
      setOpenSendEmailSuccess(true)
      localStorage.removeItem('uuid')
    },
    onError: () => {
      message.error('Đã có lỗi xảy ra vui lòng thử lại')
    }
  })

  const handleRecoveryAccount = () => {
    const userUuid = localStorage.getItem('uuid')

    recoveryAccountMutation.mutate({
      userUuid,
      portal: 'DEV'
    })
  }

  // @ts-ignore
  const mutation = useMutation({
    mutationFn: getTokenByUsernamePassword,
    onSuccess: (res: any) => {
      if (res?.password_issue === PASSWORD_ISSUE.PASSWORD_EXPIRED) {
        setOpenExpiredPasswordModal(true)
        localStorage.setItem('uuid', res?.user_uuid)

        return
      }

      if (res?.password_change_reminder) {
        localStorage.setItem('uuid', res?.user_uuid)
        localStorage.setItem('old_password', form.getFieldValue('password'))
        localStorage.setItem('password_issue', res?.password_issue)

        return
      }

      setToken(res)
      handleLoginSuccess()

      window.parent.postMessage(
        {
          type: 'LOGIN_SUCCESS',
          token: res
        },
        '*'
      )
    },
    onError: (e: any) => {
      // Reset mã captcha
      form.setFieldsValue({ captchaToken: null })
      setCaptchaToken(null)

      // Kiểm tra lỗi nhập captcha, mật khẩu không chính xác
      if (e.errorCode === 'error.invalid.captcha.token') {
        message.error('Lỗi nhập Captcha')
      } else if (
        e.errorCode === 'error.user.disable' ||
        e.error_code === 'error.user.inactive' ||
        e.error_description === 'User is disabled'
      ) {
        setVisibleModalNotification(true)
        setInfoModal({
          iconType: 'ERROR',
          title: 'Tài khoản đã bị vô hiệu hoá',
          subTitle: 'Liên hệ với quản trị viên để được hỗ trợ',
          textButton: 'Thoát',
          typeButton: 'secondary',
          // thông tin isContact luôn đi kèm cùng thông tin customerType để khi gửi liên hệ thì biết của loại KH nào
          isContact: true,
          width: 450
          // customerType: TAB_CONVERT[currentTab]
        })
      } else if (e.errorCode === 'error.data.exists.not.active') {
        const email = e.field.split('-')[1]

        setValueReactive(email)
        setVisibleModalNotification(true)
        setInfoModal({
          iconType: 'WARNING',
          title: 'Tài khoản chưa được kích hoạt',
          textButton: 'Kích hoạt',
          subTitle: 'Vui lòng kích hoạt tài khoản hoặc liên hê quản trị viên để được hỗ trợ!',
          haveHotline: false,
          isCloseModal: true,
          closeModalPosition: 'first',
          closeModalTextButton: 'Hủy',
          closeModalTypeButton: 'secondary',
          handleConfirm: handleReactive,
          width: 450
        })
      } else if (!e.dontCatchError) {
        if (e.error_description === 'Bad credentials') {
          // Lỗi nhập mật khẩu sai dưới 5 lần
          setShowWarningModal(true)
          setWarningType('warning')
        } else if (e.error_description.includes('WRONG PASSWORD LIMITED')) {
          const splitDes = e?.error_description?.split('-')

          // Lỗi nhập mật khẩu sai quá 5 lần
          setShowWarningModal(true)
          // @ts-ignore
          setWarningType(parseInt(splitDes[1], 10))
          localStorage.setItem('uuid', e?.error_description.split(':')[1])
        } else {
          // Tài khoản không chính xác
          setVisibleModalNotification(true)
          setInfoModal({
            iconType: 'WARNING',
            title: 'Cảnh báo đăng nhập',
            textButton: 'Quên mật khẩu',
            typeButton: 'secondary',
            redirectPage: `${DXPortal.dev.createPath('/forgot-password', null)}`,
            isCloseModal: true,
            closeModalTextButton: 'Xác nhận',
            subTitle: `Tên đăng nhập hoặc mật khẩu không chính xác! Quý khách lưu ý tài khoản sẽ bị khóa nếu nhập sai mật khẩu từ 5 lần trở lên`,
            width: 450
          })
        }
      }
    }
  })

  // Xử lý submit đăng nhập
  const handleSubmitLogin = async (data: any) => {
    // Reset lại capcha
    // @ts-ignore
    captchaRef?.current?.reset()
    mutation.mutate(data)
  }

  // Sau khi xác nhận đồng ý với điều khoản sẽ gửi thêm địa chỉ IP và đăng nhập lại
  useEffect(() => {
    if (isConfirmPolicy) {
      form.submit()
      setIsConfirmPolicy(false)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isConfirmPolicy])

  // endregion

  // region VNPT ID
  const handleReturnHomePage = () => {
    window.location.href = '/partner-portal/home'
  }

  // region Return
  return (
    <AuthLayout title='Đăng nhập' footer={footer}>
      <>
        <div>
          <div className='mb-[40px] flex items-center justify-center'>
            <div className='flex w-full flex-col'>
              {/* Form */}
              <Form form={form} onFinish={handleSubmitLogin} layout='vertical'>
                {/* Tài khoản */}
                <Form.Item
                  label={<div className='text-xs font-medium leading-[16px]'>Email</div>}
                  name='username'
                  rules={[
                    validateRequireInput('Email không được bỏ trống'),
                    validateEmail('Sai định dạng email', EMAIL_REGEX)
                  ]}
                  className='mb-[40px]'
                  normalize={trim}
                >
                  <Input className='h-[40px] rounded-none' maxLength={100} autoFocus placeholder='Nhập email' />
                </Form.Item>
                {/* Mật khẩu */}
                <Form.Item
                  label={<div className='text-xs font-medium leading-[16px]'>Mật khẩu</div>}
                  name='password'
                  rules={[validateRequireInput('Mật khẩu không được bỏ trống')]}
                  className='mb-[20px]'
                >
                  <Input.Password className='h-[40px] rounded-none' type='password' placeholder='Nhập mật khẩu' />
                </Form.Item>
                {/* Hiển thị captcha */}
                {captchaConfig && (
                  <div className='my-[20px]'>
                    <Form.Item name='captchaToken' noStyle hidden />
                    <ReCAPTCHA
                      ref={captchaRef}
                      sitekey={process.env.NEXT_PUBLIC_RECAPCHA_KEY || DEFAULT_KEY}
                      hl='vi'
                      onChange={val => {
                        form.setFieldsValue({ captchaToken: val })
                        setCaptchaToken(val as any)
                      }}
                    />
                  </div>
                )}
                {/* Đăng nhập */}
                <Button
                  type='primary'
                  htmlType='submit'
                  className='mb-[10px] mt-[20px] h-[40px] w-full rounded-none'
                  loading={mutation?.isPending}
                  disabled={!captchaToken && captchaConfig}
                >
                  Đăng nhập
                </Button>
                {/* Đăng nhập bằng VNPT ID */}
                <Button
                  color='primary'
                  variant='outlined'
                  className='h-[40px] w-full rounded-none'
                  onClick={handleReturnHomePage}
                >
                  Quay lại trang chủ
                </Button>
              </Form>
            </div>
          </div>

          <div className='hidden'>
            <Divider>Hoặc đăng nhập bằng</Divider>

            <div className='mt-[40px] flex w-full flex-col'>
              {/* Đăng nhập */}
              <Button
                type='default'
                className='mb-[10px] h-[40px] w-full rounded-none'
                icon={<i className='onedx-google-icon mt-[10px] size-4 rounded-full' />}
              >
                Đăng nhập bằng Google
              </Button>
              {/* Đăng nhập bằng VNPT ID */}
              <Button
                type='default'
                className='h-[40px] w-full rounded-none'
                icon={<i className='onedx-facebook-blue mt-[10px] size-4 rounded-full' />}
              >
                Đăng nhập bằng Facebook
              </Button>
            </div>
          </div>
        </div>
        {/* Thông báo khi có lỗi hoặc thành công */}
        {visibleModalNotification && infoModal && (
          <DevModalNotification
            visibleModal={visibleModalNotification}
            setVisibleModal={setVisibleModalNotification}
            infoModal={infoModal}
            // @ts-ignore
            layoutLogin={infoModal?.typeEmail}
          />
        )}
        {/* Modal cảnh báo captcha */}
        {warningModal && (
          <DevLoginCaptchaWarningModal
            contentType={warningType}
            visibleModal={warningModal}
            setVisibleModal={setShowWarningModal}
            failLoginLimit={dataConfig?.failedLogin?.limit}
            onOk={handleRecoveryAccount}
          />
        )}
        <WarningModal
          openModal={openExpiredPasswordModal}
          setOpenModal={setOpenExpiredPasswordModal}
          onOk={handleRecoveryAccount}
          content={EXPIRED_PASSWORD_CONTENT}
          okText='Khôi phục tài khoản'
          title='Tài khoản đã bị khóa'
          loading={recoveryAccountMutation.isPending}
        />
        <SuccessModal
          openModal={openSendEmailSuccess}
          setOpenModal={setOpenSendEmailSuccess}
          content={SENDMAIL_SUCCESS_CONTENT}
          title='Gửi email khôi phục tài khoản'
        />
      </>
    </AuthLayout>
  )

  // endregion
}

export default DevLoginForm
