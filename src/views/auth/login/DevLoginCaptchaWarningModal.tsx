import React from 'react'

import { useRouter } from 'next/navigation'

import { Button, Modal } from 'antd'

import { isNaN } from 'lodash'
import moment from 'moment'

interface ILoginCaptchaWarningModal {
  visibleModal: boolean
  setVisibleModal: React.Dispatch<React.SetStateAction<boolean>>
  contentType: string
  failLoginLimit: string
  onOk?: () => void
}

/**
 * Modal cảnh báo captcha
 */
export default function LoginCaptchaWarningModal({
  visibleModal,
  setVisibleModal,
  contentType,
  failLoginLimit,
  onOk
}: ILoginCaptchaWarningModal) {
  const router = useRouter()
  const isWarning = contentType === 'warning'

  const title = isWarning ? 'Tên đăng nhập hoặc mật khẩu không chính xác' : 'Cảnh báo đăng nhập'

  // Nội dung modal
  const content = isWarning
    ? `Quý khách lưu ý tài khoản sẽ bị khóa nếu nhập sai mật khẩu từ ${failLoginLimit} lần trở lên.`
    : `Tài khoản của quý khách tạm thời bị vô hiệu hóa do quý khách đã nhập sai thông tin đăng nhập ${failLoginLimit} lần liên tiếp. Vui lòng thử lại vào ${moment()
        .add(!isNaN(Number(contentType)) ? Math.round(Number(contentType) / 60) : 30, 'minutes')
        .format('HH:mm')} hoặc liên hệ quản trị viên để được hỗ trợ!`

  // Icon và tiêu đề của modal
  const renderTitle = () => (
    <div className='flex items-center gap-4'>
      {isWarning ? (
        <div className='size-10 rounded-full bg-yellow-50 p-2'>
          <i className='onedx-system-warning size-6 text-yellow-400' />
        </div>
      ) : (
        <div className='size-10 rounded-full bg-bg-error-lighter p-2'>
          <i className='onedx-warning size-6 text-text-error-default' />
        </div>
      )}
      <div className='headline-16-semibold py-4 text-center text-text-neutral-strong'>{title}</div>
    </div>
  )

  // Footer: Các button
  const renderFooter = () => (
    <div className='mobile:px-0 flex justify-items-center space-x-6 px-16'>
      <Button className='w-[195px] rounded-none' onClick={() => setVisibleModal(false)}>
        {isWarning ? 'Xác nhận' : 'Đóng'}
      </Button>
      {isWarning ? (
        <Button
          className='w-[195px] rounded-none'
          type='primary'
          onClick={() => router.push('dev-portal/forgot-password')}
        >
          Quên mật khẩu
        </Button>
      ) : (
        <Button className='w-[195px] rounded-none' type='primary' onClick={onOk}>
          Khôi phục tài khoản
        </Button>
      )}
    </div>
  )

  return (
    <Modal
      centered
      open={visibleModal}
      onCancel={() => setVisibleModal(false)}
      footer={null}
      closable={true}
      maskClosable={false}
      width={450}
      title={renderTitle()}
    >
      <div className='flex flex-col place-items-center gap-4 py-[24px]'>
        <div className='mb-[24px] text-base'>{content}</div>
        {renderFooter()}
      </div>
    </Modal>
  )
}
