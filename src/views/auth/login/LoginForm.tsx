'use client'

// React Imports
import { useEffect, useRef, useState } from 'react'

import { usePathname, useRouter, useSearchParams } from 'next/navigation'

import type { TabsProps } from 'antd'
import { Alert, Button, Form, Input, message, Tabs, Tooltip } from 'antd'

import { styled } from '@mui/material'

import { useMutation, useQuery } from '@tanstack/react-query'

import Cookies from 'js-cookie'

import { trim } from 'lodash'

// eslint-disable-next-line import/no-named-as-default
import ReCAPTCHA from 'react-google-recaptcha'

import superagent from 'superagent'

import { CUSTOMER_TYPE } from '@views/product-catalog/constants/constants'

import Link from '@components/Link'

import { API_ROOT, clearToken, getTokenByUsernamePasswordForSme, OAUTH_ROOT, setToken } from '@/models/Base'

import { useUser } from '@/hooks/useUser'

import ModalNotification from '@/components/modal/ModalNotification'
import { trackLogin } from '@/components/third-parties/matomo/utils/tracking'
import { useSendOTP } from '@/hooks'
import useSendLinkActivate from '@/hooks/useSendLinkActivate'
import ClientIp from '@/models/ClientIp'
import DXPortal from '@/models/DXPortal'
import SystemParam from '@/models/SystemParam'
import Users from '@/models/User'
import type { NotificationInfoType } from '@/types/notificationModalTypes'
import type { IEnterpriseAccount } from '@/types/userType'
import { EXPIRED_PASSWORD_CONTENT, PASSWORD_ISSUE, SENDMAIL_SUCCESS_CONTENT } from '@/constants/auth'
import { isEmail } from '@/utils/string'
import { validateCustomPattern, validateEmail, validateRequireInput } from '@/validator'
import {
  CUSTOMER_TYPE_CONVERT as TAB_CONVERT,
  REGEX_MAIL_OR_TAX_CODE,
  REGEX_MAIL_OR_TAX_CODE_OR_CERT
} from '../utils/constants'
import { createStoreSessionIfNotExist } from '../utils/createStoreSessionIfNotExist'
import LoginCaptchaWarningModal from './LoginCaptchaWarningModal'
import LoginChooseEnterpriseModal from './LoginChooseEnterpriseModal'
import WarningModal from '@/components/modal/WarningModal'
import SuccessModal from '@/components/modal/SuccessModal'

// region Constants
const PARAM_SEND_OTP_COOKIE_NAME = 'paramSendOTP'

const CLIENT_ID = process.env.NEXT_PUBLIC_CLIENT_ID_VNPT
const SANDBOX = process.env.NEXT_PUBLIC_SANDBOX_VNPT
const NEXT_PUBLIC_AUTH_SERVER = process.env.NEXT_PUBLIC_AUTH_SERVER_DEV

export const pathLoginSME = {
  NONE: '/sme-portal',
  ENTERPRISE: '/enterprise',
  PERSONAL: '/personal',
  HOUSE_HOLD: '/house-hold',
  LOGIN: '/sme-portal/login',
  REGISTER: '/sme-portal/register'
}

const ACTION_TYPE = {
  LOGIN: 'LOGIN',
  ACTIVATE: 'ACTIVATE',
  CHANGE_PASSWORD: 'CHANGE_PASSWORD',
  SWITCH_ACCOUNT: 'SWITCH_ACCOUNT'
}

const customerTypeConvert: Record<string, string> = {
  CN: 'PERSONAL',
  HKD: 'HOUSE_HOLD',
  KHDN: 'ENTERPRISE',
  PERSONAL: 'CN',
  HOUSE_HOLD: 'HKD',
  ENTERPRISE: 'KHDN'
}

// endregion

// region Interface
interface ILoginForm {
  username: string
  password: string
  ipAddress?: string
  parent_id?: number
  captchaToken?: any
}

interface LoginFormProps {
  AuthLayout: React.ComponentType<any>
  footer?: React.ReactNode
}
// endregion

const defaultFooter = (
  <>
    <div className='text-center'>
      <Link
        href='/sme-portal/forgot-password'
        className='text-sm font-medium text-primary'
        onClick={() => {
          Cookies.remove('infoSwitchAccount')
        }}
      >
        Quên mật khẩu
      </Link>
    </div>
    <div className='flex gap-2 text-body-14'>
      <div>Bạn chưa có tài khoản?</div>
      <Link href='/sme-portal/register' className='font-medium text-primary'>
        Đăng ký
      </Link>
    </div>
  </>
)

const LoginForm: React.FC<LoginFormProps> = ({ AuthLayout, footer = defaultFooter }) => {
  const { user } = useUser()

  const [form] = Form.useForm<ILoginForm>()
  const router = useRouter()
  const searchParams = useSearchParams()
  const pathname = usePathname()
  const isIotPortal = pathname.includes('/iot-portal')
  const redirect = searchParams.get('redirect')

  const { updateUser, changeStatus } = useUser()

  // region Captcha
  // Capcha cho màn login
  const captchaRef = useRef(null)

  // Hiển thị modal cảnh báo captcha
  const [warningModal, setShowWarningModal] = useState(false)

  const [warningType, setWarningType] = useState('warning')

  const [captchaToken, setCaptchaToken] = useState(null)

  // Lấy thông tin cấu hình captcha
  const { data: dataConfig } = useQuery({
    queryKey: ['getCaptchaConfigurationConfig'],
    queryFn: async () => {
      const res = await SystemParam.getSecurityConfig()

      return JSON.parse(res?.paramTextValue)
    },
    retry: 3
  })

  // endregion

  // region Alert Error
  const [error, setError] = useState('')

  // endregion

  // const { popupInfo, isLoading } = usePopup()
  const [visibleModalNotification, setVisibleModalNotification] = useState(false)
  const [valueReactive, setValueReactive] = useState(false)
  const [infoModal, setInfoModal] = useState<NotificationInfoType>({})
  const [openExpiredPasswordModal, setOpenExpiredPasswordModal] = useState(false)
  const [openSendEmailSuccess, setOpenSendEmailSuccess] = useState(false)

  // Biến kiểm tra hiển thị pop up login với nhiều doanh nghiệp
  // const [open, setOpen] = useState(false)

  // const [openPolicyApprovalModal, setOpenPolicyApprovalModal] = useState(false)
  const [isConfirmPolicy, setIsConfirmPolicy] = useState(false) // biến kiểm tra đã xác nhận điều khoản hay chưa

  // biến chứa thông tin trả về khi đăng  nhập (trường hợp chưa đồng ý với chính sách)
  const [inforConfirmPolicy, setInforConfirmPolicy] = useState({
    uuid: '',
    username: '',
    password: ''
  })

  // region Tabs
  const [currentTab, setCurrentTab] = useState<keyof typeof TAB_CONVERT>('ENTERPRISE')

  const tabs: TabsProps['items'] = [
    {
      key: 'ENTERPRISE',
      label: 'Doanh nghiệp'
    },
    {
      key: 'HOUSE_HOLD',
      label: 'Hộ kinh doanh'
    },
    {
      key: 'PERSONAL',
      label: 'Cá nhân'
    }
  ]

  const onChangeTab = (key: string) => {
    form.resetFields()
    setCurrentTab(key as keyof typeof TAB_CONVERT)
    setError('')
  }

  useEffect(() => {
    sessionStorage.setItem('tabs', currentTab)
  }, [currentTab])

  // endregion

  // region Login Success
  // Xử lý sau khi login thành công
  const handleLoginSuccess = async () => {
    try {
      const { getSessionInfoDefault } = Users

      const newUser = await Users.getMyProfile()

      if (newUser.portals?.toString()?.includes('DEV') && currentTab !== 'ENTERPRISE') {
        changeStatus(Users.ACC_STATUS.DENIED_FROM_LOGIN)
        clearToken()
      } else if (DXPortal.sme.canAccessPortal(newUser)) {
        updateUser(newUser)

        trackLogin(newUser)

        Cookies.set('userId', JSON.stringify(newUser.id), { expires: 1 })
        Cookies.set('confirmedDataPolicies', JSON.stringify(false))
        createStoreSessionIfNotExist()
        const sessionInfo = Cookies.get('sessionInfo')
        const mapData = sessionInfo ? JSON.parse(sessionInfo) : {}

        if (!mapData[newUser.id]) {
          mapData[newUser.id] = getSessionInfoDefault()
          Cookies.set('sessionInfo', JSON.stringify(mapData), { expires: 1 / 48 })
        }

        const homePath = pathLoginSME[currentTab as keyof typeof pathLoginSME]

        // router.push(`/sme-portal/login?redirect=${encodeURIComponent(currentPath)}`) => to get redirect
        const redirectPath = redirect ? decodeURIComponent(redirect) : homePath

        // Điều hướng sau đăng nhập thành công
        const targetPath = isIotPortal ? (redirect ? decodeURIComponent(redirect) : '/iot-portal/home') : redirectPath

        router.replace(targetPath)
      } else {
        changeStatus(Users.ACC_STATUS.DENIED_FROM_LOGIN)
        clearToken()
      }

      // TODO: refetch()
      localStorage.setItem('Home.getListContact', '')
    } catch (e) {
      setError('Tên đăng nhập hoặc mật khẩu không chính xác')
      console.log(e)
    }
  }

  // endregion

  // region OTP, Active
  // Gửi OTP nếu bật cấu hình OTP
  const { mutationSendOTP } = useSendOTP({
    router,
    setVisibleModal: setVisibleModalNotification,
    setInfoModal,
    errorModal: {},
    user,
    url: `/verification-code?actionType=${ACTION_TYPE.LOGIN}`,
    isIotPortal
  })

  // Kích hoạt tài khoản nếu tài khoản vừa được đăng ký
  // Liên quan đến cấu hình bật/ tắt OTP hay không
  const { mutationSendLinkActivate } = useSendLinkActivate({
    router: router,
    setVisibleModal: setVisibleModalNotification,
    setInfoModal,
    errorModal: {},
    user,
    url: '/login',
    isIotPortal
  })

  // API gửi link tái kích hoạt Tài khoản
  const mutationSendLinkReActivate = useMutation({
    mutationFn: Users.resendActivationMail,
    onSuccess: () => {
      setVisibleModalNotification(true)
      const username = form.getFieldValue('username')
      const value = isEmail(username) ? username : valueReactive

      setInfoModal({
        iconType: 'ACTIVE',
        title: 'Gửi lại email kích hoạt thành công',
        // subTitle: 'Vui lòng kiểm tra email của bạn để kích hoạt tài khoản vừa đăng ký',
        // subTitle: 'Gửi lại email kích hoạt thành công',
        textButton: 'Đóng',
        redirectPage: isIotPortal ? `${API_ROOT}/iot-portal/login` : `${DXPortal.sme.createPath('/login', null)}`,
        subTitle: `Một email đã được gửi đến địa chỉ ${value} của bạn. Vui lòng kiểm tra trong hộp thư để hoàn tất quá trình.`,
        width: 648
      })
    },
    onError: () => {
      setVisibleModalNotification(true)
      setInfoModal({
        iconType: 'ERROR',
        title: 'Đăng ký thất bại',
        subTitle: 'retryError',
        textButton: 'Đóng',
        redirectPage: isIotPortal ? `${API_ROOT}/iot-portal/login` : `${DXPortal.sme.createPath('/login', null)}`
      })
    }
  })

  // endregion

  // region Enterprise
  // Biến kiểm tra hiển thị pop up login với nhiều doanh nghiệp
  const [openChooseEnterpriseModal, setOpenChooseEnterpriseModal] = useState(false)
  // Danh sách doanh nghiệp có thể chọn
  const [enterpriseAccLst, setEnterpriseAccLst] = useState<IEnterpriseAccount[]>([])

  // Hàm search thông tin tài khoản trong doanh nghiệp
  const handleValidateEnterprise = async () => {
    let response = ''
    const username = form.getFieldValue('username')
    const password = form.getFieldValue('password')

    await superagent
      .get(`${OAUTH_ROOT}/api/users-sme/employee/smes?email=${username}&password=${password}`, {
        // @ts-ignore
        headers: {}
      })
      .then(async res => {
        // @ts-ignore
        response = res
      })
      .catch(err => {
        response = err
      })

    return response
  }
  // endregion

  // region Login Handle
  const handleConfirm = (value: { methodSendOTP: any }) => {
    const paramSendOTP = Cookies.get(PARAM_SEND_OTP_COOKIE_NAME)

    if (paramSendOTP && JSON.parse(paramSendOTP).hash) {
      const bodySendOTP = {
        ...JSON.parse(paramSendOTP),
        sendType: value.methodSendOTP
      }

      Cookies.set(PARAM_SEND_OTP_COOKIE_NAME, JSON.stringify(bodySendOTP))
      mutationSendOTP.mutate({
        ...bodySendOTP,
        username: form.getFieldValue('username')
      })
    } else {
      const bodySendLinkActivate = {
        username: form.getFieldValue('username'),
        redirectUrl: isIotPortal ? `${API_ROOT}/iot-portal/login` : `${API_ROOT}/sme-portal/login`
      }

      mutationSendLinkActivate.mutate(bodySendLinkActivate)
    }
  }

  // Kích hoạt tài khoản
  const handleReactive = () => {
    const bodySendLinkActivate = {
      username: form.getFieldValue('username'),
      redirectUrl: isIotPortal ? `${API_ROOT}/iot-portal/login` : `${API_ROOT}/sme-portal/login`
    }

    mutationSendLinkReActivate.mutate(bodySendLinkActivate)
  }
  // endregion

  // region Mutation
  const loginMutation = useMutation({
    mutationFn: getTokenByUsernamePasswordForSme,
    onSuccess: (res: any) => {
      if (res?.password_issue === PASSWORD_ISSUE.PASSWORD_EXPIRED) {
        setOpenExpiredPasswordModal(true)
        localStorage.setItem('uuid', res?.user_uuid)

        return
      }

      if (res?.password_change_reminder) {
        localStorage.setItem('uuid', res?.user_uuid)
        localStorage.setItem('old_password', form.getFieldValue('password'))
        localStorage.setItem('password_issue', res?.password_issue)

        router.push(isIotPortal ? '/iot-portal/update-password' : DXPortal.sme.createPath('/update-password'))

        return
      }

      // needActive là cần kích hoạt tài khoản
      if (res?.needActive) {
        const paramSendOTP = {
          username: form.getFieldValue('username') + ' ' + TAB_CONVERT[currentTab],
          hash: res.hash,
          actionType: ACTION_TYPE.ACTIVATE
        }

        Cookies.set(PARAM_SEND_OTP_COOKIE_NAME, JSON.stringify(paramSendOTP))

        // hash là có bật OTP hay không
        if (res?.hash) {
          // Nếu có thì nhập OTP để kiểm tra
          setVisibleModalNotification(true)
          setInfoModal({
            iconType: 'WARNING',
            title: 'Tài khoản của quý khách chưa được kích hoạt',
            textButton: 'Xác nhận',
            subTitle: 'Chúng tôi sẽ gửi mã kích hoạt đến email hoặc số điện thoại của quý khách đã đăng ký',
            methodOTP: {
              emailMask: res.emailMask,
              phoneMask: res.phoneMask
            },
            isCloseModal: true,
            handleConfirm
          })
        } else {
          // Nếu không có hash thì sẽ gửi link active về mail
          setVisibleModalNotification(true)
          setInfoModal({
            iconType: 'WARNING',
            title: 'Tài khoản của quý khách chưa được kích hoạt',
            subTitle: `Chúng tôi sẽ gửi mã kích hoạt đến email ${res.emailMask} của quý khách`,
            textButton: 'Xác nhận',
            isCloseModal: true,
            handleConfirm
          })
        }
      } else if (res?.hash) {
        // Không active mà có hash thì login bằng OTP
        const cookieValue = Cookies.get(PARAM_SEND_OTP_COOKIE_NAME)

        if (cookieValue) {
          const paramSendOTP = {
            username: JSON.parse(cookieValue),
            hash: res.hash,
            actionType: ACTION_TYPE.LOGIN
          }

          Cookies.set(PARAM_SEND_OTP_COOKIE_NAME, JSON.stringify(paramSendOTP))
        }

        setVisibleModalNotification(true)
        setInfoModal({
          iconType: 'WARNING',
          title: 'Xác thực mã OTP',
          textButton: 'Xác nhận',
          subTitle: 'Vui lòng chọn hình thức gửi mã OTP xác thực',
          methodOTP: { emailMask: res.emailMask, phoneMask: res.phoneMask },
          isCloseModal: true,
          handleConfirm
        })
      } else {
        setToken(res)
        handleLoginSuccess()
      }
    },
    onError: (e: any) => {
      // Reset mã captcha
      form.setFieldsValue({ captchaToken: null })
      setCaptchaToken(null)

      // Kiểm tra lỗi nhập captcha, mật khẩu không chính xác
      if (e.errorCode === 'error.invalid.captcha.token') {
        message.error('Lỗi nhập Captcha')
      } else if (
        e.errorCode === 'error.user.disable' ||
        e.error_code === 'error.user.inactive' ||
        e.error_description === 'User is disabled'
      ) {
        setVisibleModalNotification(true)
        setInfoModal({
          iconType: 'ERROR',
          title: 'Tài khoản đã bị vô hiệu hoá',
          subTitle: 'Liên hệ với quản trị viên để được hỗ trợ',
          textButton: 'Đóng',
          typeButton: 'secondary',
          // thông tin isContact luôn đi kèm cùng thông tin customerType để khi gửi liên hệ thì biết của loại KH nào
          isContact: true,
          customerType: TAB_CONVERT[currentTab]
        })
      } else if (e.errorCode === 'error.data.exists.not.active') {
        const email = e.field.split('-')[1]

        setValueReactive(email)
        setVisibleModalNotification(true)
        setInfoModal({
          iconType: 'WARNING',
          title: 'Tài khoản chưa được kích hoạt',
          textButton: 'Kích hoạt',
          subTitle: 'Vui lòng kích hoạt tài khoản hoặc liên hê quản trị viên để được hỗ trợ!',
          haveHotline: false,
          isCloseModal: true,
          handleConfirm: handleReactive
        })
      } else if (!e.dontCatchError) {
        if (e.error_description === 'Bad credentials') {
          // Lỗi nhập mật khẩu sai dưới 5 lần
          setShowWarningModal(true)
          setWarningType('warning')
        } else if (e.error_description.includes('WRONG PASSWORD LIMITED')) {
          const splitDes = e?.error_description?.split('-')

          // Lỗi nhập mật khẩu sai quá 5 lần
          setShowWarningModal(true)
          // @ts-ignore
          setWarningType(parseInt(splitDes[1], 10))
          localStorage.setItem('uuid', e?.error_description.split(':')[1])
        } else {
          // Tài khoản không chính xác
          setError('Tên đăng nhập hoặc mật khẩu không chính xác')
        }
      }
    }
  })

  const recoveryAccountMutation = useMutation({
    mutationFn: Users.recoveryAccount,
    onSuccess: () => {
      setShowWarningModal(false)
      setOpenSendEmailSuccess(true)
      localStorage.removeItem('uuid')
    },
    onError: () => {
      message.error('Đã có lỗi xảy ra vui lòng thử lại')
    }
  })

  const handleLogin = (data: any, parentId = -1) => {
    setInforConfirmPolicy({
      ...inforConfirmPolicy,
      username: data?.username,
      password: data?.password
    })

    // parent_id: Id chủ doanh nghiệp của nhân viên đang đăng nhập
    data.parent_id = parentId
    Cookies.set(PARAM_SEND_OTP_COOKIE_NAME, JSON.stringify(`${data.username} ${TAB_CONVERT[currentTab]}`))
    data.username = `${data.username} ${TAB_CONVERT[currentTab]}`
    loginMutation.mutate(data)
  }

  const handleRecoveryAccount = () => {
    const userUuid = localStorage.getItem('uuid')

    recoveryAccountMutation.mutate({
      userUuid,
      portal: isIotPortal ? 'IOT_PORTAL' : 'SME'
    })
  }

  // Xử lý submit đăng nhập
  const handleSubmitLogin = async (data: ILoginForm) => {
    // @ts-ignore
    captchaRef?.current?.reset()
    const ipAddress = await ClientIp.getClientIp()

    if (isConfirmPolicy) {
      data.ipAddress = ipAddress
    }

    handleValidateEnterprise().then((res: any) => {
      // Lọc danh sách tài khoản theo customerType nếu có
      const enterpriseLstTemp = res?.body?.filter((item: any) => item.customerType === customerTypeConvert[currentTab])

      if (enterpriseLstTemp?.length > 0) {
        if (enterpriseLstTemp?.length === 1) {
          handleLogin(data, enterpriseLstTemp[0]?.id)
        } else {
          setEnterpriseAccLst(enterpriseLstTemp)
          setOpenChooseEnterpriseModal(true)
        }
      } else {
        handleLogin(data)

        // TODO: Lấy code VNPT SDK Tracking bên source cũ
      }
    })

    setError('')
  }

  // Sau khi xác nhận đồng ý với điều khoản sẽ gửi thêm địa chỉ IP và đăng nhập lại
  useEffect(() => {
    if (isConfirmPolicy) {
      form.submit()
      setIsConfirmPolicy(false)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isConfirmPolicy])

  // endregion

  // region VNPT ID
  const handleLoginVNPT = () => {
    window.location.href = `${SANDBOX}/oauth2/authorize?response_type=code&client_id=${CLIENT_ID}&
		redirect_uri=${window.location.origin}${NEXT_PUBLIC_AUTH_SERVER}/sso/callback&scope=openid`
  }

  // endregion

  // region Return
  return (
    <AuthLayout
      goBackText='Trang chủ'
      title='Đăng nhập'
      description=''
      onGoBack={() => router.push(isIotPortal ? '/iot-portal/home' : '/home')}
      customerType={TAB_CONVERT[currentTab]}
      footer={footer}
    >
      <>
        <div className='flex items-center justify-center'>
          <div className='flex w-full flex-col'>
            {/* Tabs */}
            <CustomTab
              onChange={onChangeTab}
              activeKey={currentTab || 'ENTERPRISE'}
              items={tabs}
              className='mt-2 w-full text-body-14 font-medium'
            />

            {/* Alert */}
            {error && <Alert message={error} type='error' showIcon className='mb-6 mt-2 w-full' />}

            {/* Form */}
            <Form form={form} onFinish={handleSubmitLogin} layout='vertical'>
              {/* Tài khoản */}
              {currentTab === CUSTOMER_TYPE.PERSONAL && (
                <Form.Item
                  label='Email'
                  name='username'
                  rules={[
                    validateRequireInput('Email hoặc số điện thoại không được bỏ trống'),
                    validateEmail('Sai định dạng email')
                  ]}
                  className='sm:mb-4'
                  normalize={trim}
                >
                  <Input maxLength={100} autoFocus placeholder='Nhập email hoặc số điện thoại' />
                </Form.Item>
              )}
              {currentTab === CUSTOMER_TYPE.ENTERPRISE && (
                <Form.Item
                  label={
                    <div className='items-enter flex gap-1 text-text-neutral-strong'>
                      <div className='text-caption-12 font-medium'>Tên đăng nhập</div>
                      <Tooltip title='Tên đăng nhập của admin doanh nghiệp là mã số thuế, tên đăng nhập của nhân viên doanh nghiệp là email'>
                        <i className='onedx-information size-4' />
                      </Tooltip>
                    </div>
                  }
                  name='username'
                  rules={[
                    validateRequireInput('Tên đăng nhập không được bỏ trống'),
                    validateCustomPattern(REGEX_MAIL_OR_TAX_CODE, 'Sai định dạng tên đăng nhập')
                  ]}
                  className='sm:mb-4'
                  normalize={trim}
                >
                  <Input maxLength={100} autoFocus placeholder='Mã số thuế/ email để đăng nhập' />
                </Form.Item>
              )}
              {currentTab === CUSTOMER_TYPE.HOUSE_HOLD && (
                <Form.Item
                  label={
                    <div className='items-enter flex gap-1 text-text-neutral-strong'>
                      <div className='text-caption-12 font-medium'>Tên đăng nhập</div>
                      <Tooltip title='Tên đăng nhập của admin doanh nghiệp là mã số thuế, số chứng thực, tên đăng nhập của nhân viên doanh nghiệp là email'>
                        <i className='onedx-information size-4' />
                      </Tooltip>
                    </div>
                  }
                  name='username'
                  rules={[
                    validateRequireInput('Tên đăng nhập không được bỏ trống'),
                    validateCustomPattern(REGEX_MAIL_OR_TAX_CODE_OR_CERT, 'Sai định dạng số chứng thực')
                  ]}
                  className='sm:mb-4'
                  normalize={trim}
                >
                  <Input maxLength={13} autoFocus placeholder='Mã số thuế/ Số chứng thực/ email để đăng nhập' />
                </Form.Item>
              )}
              {/* Mật khẩu */}
              <Form.Item
                label={<div className='text-caption-12 font-medium text-text-neutral-strong'>Mật khẩu</div>}
                name='password'
                rules={[validateRequireInput('Mật khẩu không được bỏ trống')]}
                className='sm:mb-4'
              >
                <Input.Password type='password' placeholder='Mật khẩu đăng nhập' />
              </Form.Item>
              {/* Hiển thị captcha */}
              {dataConfig?.captchaConfig?.isNewSme && (
                <div className='mb-4'>
                  <Form.Item name='captchaToken' noStyle hidden />
                  <ReCAPTCHA
                    ref={captchaRef}
                    sitekey={process.env.NEXT_PUBLIC_RECAPCHA_KEY || '6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI'}
                    hl='vi'
                    onChange={val => {
                      form.setFieldsValue({ captchaToken: val })
                      setCaptchaToken(val as any)
                    }}
                  />
                </div>
              )}
              {/* Đăng nhập */}
              <Button
                type='primary'
                htmlType='submit'
                className='mb-4 w-full sm:mb-3'
                loading={loginMutation.isPending}
                disabled={!captchaToken && dataConfig?.captchaConfig?.isNewSme}
              >
                Đăng nhập
              </Button>
              {/* Đăng nhập bằng VNPT ID */}
              <Button
                type='primary'
                icon={<i className='onedx-vnpt-icon size-5' />}
                className='mb-4 w-full bg-bg-primary-sme-orange-default'
                onClick={handleLoginVNPT}
              >
                Đăng nhập bằng VNPT ID
              </Button>
            </Form>
          </div>
        </div>
        {/* Thông báo khi có lỗi hoặc thành công */}
        {visibleModalNotification && infoModal && (
          <ModalNotification
            visibleModal={visibleModalNotification}
            setVisibleModal={setVisibleModalNotification}
            infoModal={infoModal}
            // @ts-ignore
            layoutLogin={infoModal?.typeEmail}
          />
        )}
        {/* Modal Chọn doanh nghiệp đăng nhập */}
        {openChooseEnterpriseModal && (
          <LoginChooseEnterpriseModal
            enterpriseList={enterpriseAccLst}
            setOpenModal={setOpenChooseEnterpriseModal}
            onSubmit={(parentId: number) => handleLogin(form.getFieldsValue(true), parentId)}
          />
        )}
        {/* Modal cảnh báo captcha */}
        {warningModal && (
          <LoginCaptchaWarningModal
            contentType={warningType}
            visibleModal={warningModal}
            setVisibleModal={setShowWarningModal}
            failLoginLimit={dataConfig?.failedLogin?.limit}
            onOk={handleRecoveryAccount}
          />
        )}
        <WarningModal
          openModal={openExpiredPasswordModal}
          setOpenModal={setOpenExpiredPasswordModal}
          onOk={handleRecoveryAccount}
          content={EXPIRED_PASSWORD_CONTENT}
          okText='Khôi phục tài khoản'
          title='Tài khoản đã bị khóa'
          loading={recoveryAccountMutation.isPending}
        />
        <SuccessModal
          openModal={openSendEmailSuccess}
          setOpenModal={setOpenSendEmailSuccess}
          content={SENDMAIL_SUCCESS_CONTENT}
          title='Gửi email khôi phục tài khoản'
        />
      </>
    </AuthLayout>
  )

  // endregion
}

export default LoginForm

// region Styled
export const CustomTab = styled(Tabs)`
  .ant-tabs-nav-list {
    inline-size: 100%;
    .ant-tabs-tab {
      inline-size: ${100 / 3}%;
    }
    .ant-tabs-ink-bar {
      inline-size: ${100 / 3}%;
    }
  }
  .ant-tabs-tab-btn {
    margin-block: 0;
    margin-inline: auto;
    font-weight: 500;
  }
  .ant-tabs-nav .ant-tabs-nav-operations {
    display: none !important;
  }

  .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
    font-weight: 500;
  }
`
// endregion
