import DXPortal from '@/models/DXPortal'

interface CheckboxesState {
  firstTerm: boolean
  secondTerm: boolean
  thirdTerm: boolean
  fourthTerm: boolean
  fifthTerm?: boolean
  sixthTerm: boolean
  seventhTerm: boolean
  eightTerm: boolean
  ninthTerm: boolean
}

/** tên param lưu độ dài của OTP được lưu trên cookie */
export const PARAM_LENGTH_OTP_COOKIE_NAME = 'lengthOTP'

/** Loại action ở màn Nhập mã xác thực OTP: dùng để xem đang là action nào thì gọi api xác thực tương ứng sau khi nhập OTP */
export const ACTION_TYPE = {
  LOGIN: 'LOGIN',
  ACTIVATE: 'ACTIVATE',
  CHANGE_PASSWORD: 'CHANGE_PASSWORD',
  SWITCH_ACCOUNT: 'SWITCH_ACCOUNT'
}

/** tên param lưu thông tin gửi OTP được lưu trên cookie */
export const PARAM_SEND_OTP_COOKIE_NAME = 'paramSendOTP'

/** Thông tin đổi tài khoản */
export const INFO_ACCOUNT_SWITCH_COOKIE_NAME = 'infoSwitchAccount'

/** Lỗi đã tồn tại */
export const EXIST_ERROR = 'exists'

/** Mã lỗi không tìm thấy object */
export const NOT_FOUND_ERROR = 'error.object.not.found'

export const REGEX_MAIL_OR_TAX_CODE = /^(?:\d{10}|\d{13}|[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})$/
export const REGEX_MAIL_OR_TAX_CODE_OR_CERT =
  /^(?:\d{10}|\d{13}|\d{6,13}|[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})$/
export const REGEX_SPACE = /\s/

/** Định nghĩa loại khách hàng tiếng Anh sang tiếng Việt */
export const CUSTOMER_TYPE_CONVERT: Record<string, string> = {
  ENTERPRISE: 'KHDN',
  HOUSE_HOLD: 'HKD',
  PERSONAL: 'CN'
}

export const getDX = (customerType: any) => {
  switch (customerType) {
    case 'ENTERPRISE':
      return DXPortal.smeEnterprise
    case 'HOUSEHOLD':
      return DXPortal.smeHouseHold
    case 'HOUSE_HOLD':
      return DXPortal.smeHouseHold
    case 'PERSONAL':
      return DXPortal.smePersonal
    default:
      return DXPortal.sme
  }
}

// Mảng lưu key của các term thuộc personal
export const personalTermKeys = ['firstTerm', 'secondTerm', 'thirdTerm', 'fourthTerm', 'fifthTerm']

// Mảng lưu toàn bộ key term
export const allTerms = [...personalTermKeys, 'sixthTerm', 'seventhTerm', 'eightTerm', 'ninthTerm']

// Logic kiểm tra điều khoản khi tất cả checkbox được chọn
export const checkAll = (isPersonal: boolean, checkboxes: any) => {
  const termsToCheck = isPersonal
    ? personalTermKeys // Lấy 5 term đầu tiên
    : allTerms // Lấy tất cả 9 term

  return termsToCheck.every(key => checkboxes[key as keyof CheckboxesState])
}

export const checkSome = (isPersonal: boolean, checkboxes: any) => {
  const termsToCheck = isPersonal
    ? personalTermKeys // Lấy 5 term đầu tiên
    : allTerms // Lấy tất cả 9 term

  return termsToCheck.some(key => checkboxes[key as keyof CheckboxesState])
}

export const passwordCriteria = [
  {
    key: 'length',
    label: 'Chứa 8 - 16 ký tự',
    test: (pw: string) => pw.length >= 8 && pw.length <= 16
  },
  {
    key: 'lower',
    label: 'Chữ thường (a-z)',
    test: (pw: string) => /[a-z]/.test(pw)
  },
  {
    key: 'upper',
    label: 'Chữ hoa (A-Z)',
    test: (pw: string) => /[A-Z]/.test(pw)
  },
  {
    key: 'special',
    label: 'Ký tự đặc biệt: !@#$%^&*+=',
    test: (pw: string) => /[!@#$%^&*+=]/.test(pw)
  },
  {
    key: 'digit',
    label: 'Ký tự số',
    test: (pw: string) => /[0-9]/.test(pw)
  },
  {
    key: 'notCommon',
    label: 'Không chứa ký tự phổ biến',
    test: (pw: string) => !['123456', 'password', 'qwerty', '111111'].some(val => pw.includes(val))
  },
  {
    key: 'notSeq',
    label: 'Không chứa các chuỗi ký tự liên tiếp',
    test: (pw: string) =>
      !/(012|123|234|345|456|567|678|789|890|abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz)/i.test(
        pw
      )
  }
]

export const getPasswordStrength = (criteriaResult: boolean[]) => {
  const passed = criteriaResult.filter(Boolean).length

  if (passed <= 3) return { level: 'Yếu', color: '#EB4542', idx: 1 }
  if (passed <= 5) return { level: 'Trung bình', color: '#FAAD14', idx: 2 }

  return { level: 'Mạnh', color: '#52C41A', idx: 3 }
}
