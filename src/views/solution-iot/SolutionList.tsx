'use client'

import React, { useState } from 'react'

import { Input, Spin } from 'antd'

import ProductFilter from '@/components/product/ProductFilter'
import ProductFilterMobile from '@/components/product/ProductFilterMobile'
import { orderOptionsSolution } from '@/constants/products'
import { useResponsive } from '@/hooks'
import { useDebounceValue } from '@/hooks/useDebounceValue'
import BecomePartnerBanner from '../sme-portal/sections/BecomePartnerBanner'
import type { Filter } from '@/components/product/ProductFilterMobile'
import { useGetSolutionList } from '@/hooks/product.hooks'
import ProductListByCategory from '../products/ProductListByCategory'

interface Props {
  customerType: string
}

const SolutionList = ({ customerType }: Props) => {
  const { isMobile, isDesktop } = useResponsive()
  const [openMobileFilter, setOpenMobileFilter] = useState<boolean>(false)
  const [searchInputValue, setSearchInputValue] = useState<string>('')
  const searchStr = useDebounceValue(searchInputValue, 300)
  const [categoryOptions, setCategoryOptions] = useState([])

  const [filterParams, setFilterParams] = useState({
    order: undefined,
    domainIds: []
  })

  const filterList = [
    {
      index: isDesktop ? 0 : 1,
      value: 'domainIds',
      label: 'Lĩnh vực',
      options: categoryOptions,
      type: 'select'
    },
    {
      index: isDesktop ? 1 : 0,
      value: 'order',
      label: 'Sắp xếp theo',
      options: orderOptionsSolution,
      type: 'singleSelect'
    }
  ]

  const handleAfterResProducts = (res: any) => {
    if (!categoryOptions?.length) {
      const allCategories = res.map((item: any) => ({
        label: `${item.domainName} (${item.sizeSolution})`,
        key: item.domainId
      }))

      setCategoryOptions(allCategories)
    }
  }

  const { solutionLstWithCategory, isLoading } = useGetSolutionList(
    filterParams,
    searchStr,
    customerType,
    handleAfterResProducts
  )

  return (
    <Spin spinning={isLoading}>
      <div className='my-4 flex gap-3 sm:my-2 sm:px-4'>
        <Input
          onChange={e => setSearchInputValue(e.target.value)}
          className='flex-1 rounded-xl px-3 py-[5px] text-gray-6'
          prefix={<i className='onedx-search size-5 text-gray-6' />}
          placeholder='Tìm kiếm sản phẩm'
          allowClear
        />
        {isMobile ? (
          <button
            type='button'
            onClick={() => setOpenMobileFilter(true)}
            className='flex cursor-pointer items-center justify-center rounded-xl border border-solid border-[#d9d9d9] bg-inherit p-[10px]'
          >
            <i className='onedx-filter size-5 text-gray-6' />
          </button>
        ) : (
          <>
            {filterList
              .sort((a, b) => a.index - b.index)
              .map((filter: Filter) => (
                <ProductFilter
                  key={filter.value}
                  field={filter.value}
                  placeholder={filter.label}
                  checkBoxOptions={filter.options}
                  filterParams={filterParams}
                  setFilterParams={setFilterParams}
                  isSingleSelect={filter.type === 'singleSelect'}
                />
              ))}
          </>
        )}
      </div>

      {!solutionLstWithCategory?.length && (
        <div className='mb-24 flex flex-col items-center justify-center p-10'>
          <i className='onedx-empty size-30' />
          {searchStr?.length > 0 ? (
            <div className='mt-1 whitespace-normal font-normal'>{`Không có kết quả với từ khoá “${searchStr}”`}</div>
          ) : (
            <div className='mt-1 whitespace-normal font-normal'>Hiện tại chưa có sản phẩm nào</div>
          )}
        </div>
      )}
      <div className='flex flex-col'>
        {!!solutionLstWithCategory?.length &&
          solutionLstWithCategory
            .slice(0, 2)
            .map((category: any) => (
              <ProductListByCategory
                key={category.id}
                name={category.name}
                filePath={category.filePath}
                sizeService={category.sizeService}
                listService={category.listService}
                categoryId={category.id}
                isSolution
              />
            ))}
        {isDesktop && <BecomePartnerBanner />}
      </div>
      <div className='flex flex-col py-5'>
        {!!solutionLstWithCategory?.slice(2)?.length &&
          solutionLstWithCategory
            .slice(2)
            .map((category: any) => (
              <ProductListByCategory
                key={category.id}
                name={category.name}
                filePath={category.filePath}
                sizeService={category.sizeService}
                listService={category.listService}
                categoryId={category.id}
                isSolution
              />
            ))}
      </div>
      {isMobile && (
        <ProductFilterMobile
          openFilter={openMobileFilter}
          setOpenFilter={setOpenMobileFilter}
          filterList={filterList}
          filterParams={filterParams}
          setFilterParams={setFilterParams}
        />
      )}
    </Spin>
  )
}

export default SolutionList
