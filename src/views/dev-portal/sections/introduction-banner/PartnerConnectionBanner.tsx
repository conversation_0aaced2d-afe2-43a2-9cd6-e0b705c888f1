'use client'

import React from 'react'

import Image from 'next/image'

import PartnerConnectionImage from '@public/assets/images/pages/dev-portal/banner-partner-connection.png'

export const PartnerConnectionBanner = () => {
  return (
    <div className='relative mb-[60px] w-full'>
      <div className='relative flex h-[480px] w-full items-center justify-center'>
        <Image src={PartnerConnectionImage} alt='Dev Partner Connection' className='size-full object-cover' />

        <div className='absolute inset-0 flex items-center justify-start'>
          <div className='w-[970px] pl-[158px]'>
            <div className='font-inter text-[48px] font-semibold leading-[72px] tracking-normal text-white'>
              Kết nối đối tác - Mở rộng thị trường
            </div>
            <div className='font-inter mt-[15px] text-base font-normal leading-[24px] tracking-[0.5px] text-white'>
              oneSME kết nối bạn với hàng nghìn doanh nghiệp, tối ưu chuỗi cung ứng và mở rộng hợp tác. Quản lý sản
              phẩm, đơn hàng, vận chuyển và thanh toán dễ dàng trên một nền tảng duy nhất. Gia nhập ngay để tăng trưởng
              doanh thu bền vững!
            </div>
            <div
              onClick={() => (window.location.href = '/partner-portal/register')}
              className='font-inter mt-[49px] flex h-[40px] w-[303px] cursor-pointer items-center justify-center bg-white text-center text-sm font-medium leading-[20px] tracking-[0.5px] text-[#0A6FD0]'
            >
              Trở thành đối tác của oneSME ngay
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
