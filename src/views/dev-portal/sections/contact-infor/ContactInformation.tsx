'use client'

import React from 'react'

import Image from 'next/image'

import { useMutation } from '@tanstack/react-query'
import { Button, Input, Select, Form } from 'antd'
import { styled } from '@mui/material'

import { useUser } from '@/hooks'
import { showMessage } from '@components/notification/NotificationProvider'
import SmeContact from '@/models/SmeContact'
import { CUSTOMER_TYPE } from '@/constants/custom-field'
import { useNewLocation } from '@/hooks/useNewLocation'

import {
  validateEmail,
  validateKeyboardCharacters,
  validatePhoneNumber2,
  validateRequireInput,
  validateSpecialCharacters
} from '@/validator'

import ContactBanner from '@public/assets/images/pages/dev-portal/contact-banner.png'

const { TextArea } = Input

const PARTNER = 'PARTNER'

export const CustomSelect = styled(Select)`
  &.ant-select-single .ant-select-selector {
    border-radius: 0 !important;
  }
`

export const ContactInformation = () => {
  const [form] = Form.useForm()
  const { user } = useUser()
  const { provinceList, loadingProvince } = useNewLocation('register-dev')

  const sendContactApi = useMutation({
    mutationKey: ['sendContact'],
    mutationFn: async (body: any) => {
      const data = {
        fullName: body.fullName,
        phoneNum: body.phoneNum,
        email: body.email,
        message: body.message,
        createdSource: PARTNER,
        customerType: user?.customerType ?? CUSTOMER_TYPE.KHDN // Assuming customerType is fixed as 'KHDN'
      }

      await SmeContact.sendContact({ id: body.provinceId, data })
    },
    onSuccess: () => {
      showMessage.success('Gửi thông tin liên hệ thành công')
      form.resetFields()
    },
    onError: () => {
      showMessage.error('Đã có lỗi xảy ra, vui lòng thử lại sau!')
    }
  })

  const handleSubmit = (values: any) => {
    sendContactApi.mutate(values)
  }

  return (
    <div className='relative mb-[60px] w-full'>
      <div className='relative h-[750px] w-full items-center justify-center'>
        <div className='h-[375px] w-full bg-white'></div>
        <div className='h-[375px] w-full bg-[#F7F7F7]'></div>

        <div className='absolute inset-0 flex items-center justify-center'>
          <div className='flex h-[600px] w-[1320px] shadow-[0_4px_60.8px_0_rgba(0,0,0,0.04)]'>
            <div className='h-[600px] w-1/2 items-center justify-center bg-white p-[60px]'>
              <div className='font-inter mb-[5px] text-center text-lg font-medium leading-[24px] tracking-normal text-[#0548BB]'>
                Liên hệ với chúng tôi
              </div>
              <div className='font-inter ml-[25px] w-[490px] text-center text-[33px] font-semibold leading-[48px] tracking-wide'>
                Chúng tôi sẵn sàng tư vấn cho bạn
              </div>
              <div className='mt-[24px]'>
                <Form className='w-full' form={form} onFinish={handleSubmit}>
                  <div className='flex gap-[24px]'>
                    <Form.Item
                      name='fullName'
                      className='h-[40px] w-1/2'
                      rules={[
                        validateRequireInput('Họ và tên không được bỏ trống'),
                        validateKeyboardCharacters('Không cho phép nhập ký tự đặc biệt'),
                        validateSpecialCharacters('Không cho phép nhập ký tự đặc biệt')
                      ]}
                    >
                      <Input className='h-[40px] rounded-none' placeholder='Họ và tên' maxLength={100} />
                    </Form.Item>
                    <Form.Item
                      name='phoneNum'
                      className='h-[40px] w-1/2'
                      rules={[
                        validateRequireInput('Số điện thoại không được bỏ trống'),
                        validatePhoneNumber2('Số điện thoại không hợp lệ')
                      ]}
                    >
                      <Input className='h-[40px] rounded-none' placeholder='Số điện thoại' maxLength={11} />
                    </Form.Item>
                  </div>
                  <div className='flex gap-[24px]'>
                    <Form.Item
                      name='email'
                      className='w-1/2'
                      validateTrigger='onBlur'
                      validateFirst
                      rules={[validateRequireInput('Email không được bỏ trống'), validateEmail('Sai định dạng email')]}
                    >
                      <Input className='h-[40px] rounded-none' placeholder='Email' maxLength={100} />
                    </Form.Item>
                    <Form.Item
                      className='w-1/2'
                      name='provinceId'
                      rules={[validateRequireInput('Thành phố không được bỏ trống')]}
                    >
                      <CustomSelect
                        showSearch
                        allowClear
                        disabled={loadingProvince}
                        className='h-[40px] rounded-none'
                        placeholder='Thành phố'
                        options={provinceList}
                        filterOption={(input, option) => {
                          const label = option?.label

                          if (typeof label === 'string') {
                            return label?.trim().toLowerCase().includes(input.toLowerCase())
                          }

                          return false
                        }}
                      />
                    </Form.Item>
                  </div>
                  <Form.Item name='message' rules={[validateRequireInput('Nội dung liên hệ không được bỏ trống')]}>
                    <TextArea
                      placeholder='Nhập nội dung'
                      maxLength={500}
                      className='rounded-none'
                      rows={5}
                      autoSize={{ minRows: 5, maxRows: 5 }}
                    />
                  </Form.Item>

                  <Button type='primary' htmlType='submit' className='h-[40px] w-full rounded-none'>
                    Gửi
                  </Button>
                </Form>
              </div>
            </div>
            <div className='w-1/2'>
              <Image src={ContactBanner} alt='Contact Banner' className='w-full' />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
