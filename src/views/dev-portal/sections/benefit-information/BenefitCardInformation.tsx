'use client'

import React, { useState } from 'react'

interface CardInfo {
  benefitsInfor: any
}

export const BenefitCardInformation: React.FC<CardInfo> = ({ benefitsInfor }) => {
  const [hoveredItem, setHoveredItem] = useState<number | null>(null)

  const handleMouseEnter = (index: number) => {
    setHoveredItem(index)
  }

  const handleMouseLeave = () => {
    setHoveredItem(null)
  }

  return (
    <div className='mx-auto flex w-[1320px] flex-wrap items-center justify-center gap-[24px]'>
      {benefitsInfor?.map((benefit: any, index: number) => (
        <div
          key={index}
          className='mb-[60px] flex h-[88px] w-[424px]'
          onMouseEnter={() => handleMouseEnter(index)}
          onMouseLeave={handleMouseLeave}
        >
          <div
            className={`${hoveredItem === index ? 'bg-[#0A6FD0]' : 'bg-[#F5F7FA]'} flex size-[88px] items-center justify-center rounded-r-lg rounded-bl-lg rounded-tl-4xl`}
          >
            <img src={hoveredItem === index ? benefit.iconHover : benefit.icon} alt={benefit.title} />
          </div>
          <div className='h-[88px] w-[336px] bg-transparent pl-[24px]'>
            <div
              className={`font-inter text-xl font-semibold leading-[32px] ${hoveredItem === index && 'text-[#0A6FD0]'}`}
            >
              {benefit?.title}
            </div>
            <div className='font-inter text-base font-normal leading-[24px] text-neutral-120'>
              {benefit?.description}
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}
