'use client'

import { BenefitCardInformation } from '@views/dev-portal/sections/benefit-information/BenefitCardInformation'

export const BenefitInformation = () => {
  const benefits = [
    {
      title: '<PERSON><PERSON> tăng doanh thu',
      description: 'Mở rộng kênh bán hàng, tối ưu lợi nhuận',
      icon: '/assets/images/pages/dev-portal/increase-revenue-icon/increase-revenue-icon.png',
      iconHover: '/assets/images/pages/dev-portal/increase-revenue-icon/increase-revenue-icon-hover.png'
    },
    {
      title: 'Tiếp cận đa dạng khách hàng',
      description: 'Kết nối trực tiếp với hàng nghìn doanh nghiệp và cá nhân có nhu cầu',
      icon: '/assets/images/pages/dev-portal/customer-diversity-icon/customer-diversity-icon.png',
      iconHover: '/assets/images/pages/dev-portal/customer-diversity-icon/customer-diversity-icon-hover.png'
    },
    {
      title: 'Quản lý dễ dàng',
      description: '<PERSON> hà<PERSON>, doanh thu và phân tích của khách hàng trên một nền tảng duy nhất',
      icon: '/assets/images/pages/dev-portal/management-icon/manage-icon.png',
      iconHover: '/assets/images/pages/dev-portal/management-icon/manage-icon-hover.png'
    },
    {
      title: 'Thanh toán minh bạch',
      description: 'Quy trình thanh toán nhanh chóng, rõ ràng',
      icon: '/assets/images/pages/dev-portal/payment-icon/payment-icon.png',
      iconHover: '/assets/images/pages/dev-portal/payment-icon/payment-icon-hover.png'
    },
    {
      title: 'Tối ưu vận hành',
      description: 'Tích hợp công cụ hỗ trợ quản lý sản phẩm, dịch vụ thông minh, tiện lợi',
      icon: '/assets/images/pages/dev-portal/optimize-operations/optimize-operations-icon.png',
      iconHover: '/assets/images/pages/dev-portal/optimize-operations/optimize-operations-icon-hover.png'
    },
    {
      title: 'Không mất phí đăng ký',
      description: 'Dễ dàng tham gia mà không cần vốn đầu tư ban đầu',
      icon: '/assets/images/pages/dev-portal/registration-free-icon/registration-free-icon.png',
      iconHover: '/assets/images/pages/dev-portal/registration-free-icon/registration-free-icon-hover.png'
    }
  ]

  return (
    <div className='relative w-full'>
      <div className='mb-[40px] w-full items-center justify-center'>
        <div className='font-inter mb-[5px] text-center text-lg font-medium leading-[24px] tracking-normal text-[#0548BB]'>
          Lợi ích và ưu đãi
        </div>
        <div className='mb-[20px] flex flex-col items-center'>
          <div className='font-inter text-center text-[33px] font-semibold leading-[48px] tracking-normal'>
            Chúng tôi giúp doanh nghiệp của bạn phát triển hơn
          </div>
        </div>
        <div className='flex flex-col items-center'>
          <div className='mx-auto max-w-[705px] text-center text-base font-normal leading-[24px] tracking-wide text-[#6B7585]'>
            Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the
            industry&apos;s standard dummy text ever.
          </div>
        </div>
      </div>

      <BenefitCardInformation benefitsInfor={benefits} />
    </div>
  )
}
