'use client'

import React from 'react'

import Image from 'next/image'

import PartnerCommissionImage from '@public/assets/images/pages/dev-portal/banner-partner-commission.png'

export const PartnerCommissionBanner = () => {
  const scrollToGuideSection = (element: string) => {
    const guideSection = document.getElementById(element)

    if (guideSection) {
      guideSection.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      })
    }
  }

  return (
    <div className='relative w-full'>
      <div className='relative flex h-[480px] w-full items-center justify-center'>
        <Image src={PartnerCommissionImage} alt='Dev Partner Connection' className='size-full object-cover' />

        <div className='absolute inset-0 flex items-center justify-start'>
          <div className='w-[1110px] pl-[400px]'>
            <div className='font-montserrat text-[47px] font-semibold leading-[72px] tracking-normal text-white'>
              Nhận hoa hồng hấp dẫn lên tới <span className='text-[70px] leading-[84px] text-[#EFBD6C]'>20%</span> trên
              mỗi đơn hàng
            </div>
            <div className='mt-[35px] flex gap-[24px]'>
              <button
                onClick={() => (window.location.href = '/partner-portal/register')}
                className='flex h-[60px] w-[259px] cursor-pointer items-center justify-center bg-white text-[#0A6FD0] '
              >
                Đăng ký trở thành đối tác
              </button>
              <button
                onClick={() => scrollToGuideSection('lien-he-section')}
                className='flex h-[60px] w-[200px] cursor-pointer items-center justify-center border-2 border-white bg-transparent text-[white]'
              >
                Nhận tư vấn ngay
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
