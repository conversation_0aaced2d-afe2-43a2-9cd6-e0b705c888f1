'use client'

import React from 'react'

import Image from 'next/image'

import { CheckCircleFilled } from '@ant-design/icons'

interface Info {
  title: string
  description: string
  image: any
  benefit: string[]
  link: string
}

interface CardInfo {
  introduceInfor: Info[]
}

export const CardInformation: React.FC<CardInfo> = ({ introduceInfor }) => {
  return (
    <div className='flex w-full items-center justify-center gap-[24px]'>
      {introduceInfor?.map((infor: Info, index: number) => (
        <div
          key={index}
          className='flex h-[631px] w-[424px] flex-col justify-between rounded-2xl border border-gray-100 bg-white p-[30px] shadow-lg'
        >
          <div>
            <Image src={infor?.image} alt={infor?.title} className='mb-[15px] h-[217px] w-[364px]' />
            <div className='flex flex-col'>
              <div className='mb-[15px] place-self-center text-xl font-semibold leading-[32px]'>{infor?.title}</div>
              <div className='mb-[10px] place-self-center text-center text-base font-medium leading-[24px] tracking-wide text-[#0A6FD0]'>
                {infor?.description}
              </div>
              {infor?.benefit?.map((item: string, index: number) => (
                <div key={index} className='mb-[10px] flex w-full items-center'>
                  <CheckCircleFilled className='size-[15px] text-[#0F7450]' />
                  <div className='ml-[10px] text-base font-normal leading-[24px] tracking-wide text-neutral-120'>
                    {item?.trim()}
                  </div>
                </div>
              ))}
            </div>
          </div>

          <button
            onClick={() => (window.location.href = infor?.link)}
            className='flex h-[40px] w-full cursor-pointer items-center justify-center border-2 border-[#0A6FD0] bg-white text-[#0A6FD0] transition-colors duration-200 hover:bg-[#0A6FD0] hover:text-white'
          >
            Đăng ký ngay
          </button>
        </div>
      ))}
    </div>
  )
}
