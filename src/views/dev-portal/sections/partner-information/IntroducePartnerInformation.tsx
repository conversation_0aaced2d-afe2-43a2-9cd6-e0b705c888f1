'use client'

import { CardInformation } from '@views/dev-portal/sections/partner-information/CardInformation'

import Supplier from '@public/assets/images/pages/dev-portal/introduce-1.png'
import Provider from '@public/assets/images/pages/dev-portal/introduce-2.png'
import Sale from '@public/assets/images/pages/dev-portal/introduce-3.png'

export const IntroducePartnerInformation = () => {
  const introduceInfor = [
    {
      title: 'Nhà cung cấp hàng hóa',
      description: '<PERSON><PERSON>a sản phẩm đến hàng ngàn khách hàng tiềm năng',
      image: Supplier,
      benefit: [
        'Tiếp cận nhiều doanh nghiệp & khách hàng cá nhân.',
        'Quản lý kho vận & vận hành đơn giản.',
        'Định gi<PERSON> sản phẩm & quảng bá dễ dàng.'
      ],
      link: '/partner-portal/register'
    },
    {
      title: '<PERSON><PERSON><PERSON> tác cung cấp dịch vụ',
      description: 'Cung cấp giải pháp hỗ trợ doanh nghiệp phát triển',
      image: Provider,
      benefit: [
        'Hợp tác cung cấp giải pháp vận chuyển, lắp đặt',
        'Hệ thống kết nối tự động, dễ dàng quản lý',
        'Nhận được mức phí dịch vụ hấp dẫn từ nền tảng'
      ],
      link: '/partner-portal/register'
    },
    {
      title: 'Đại lý bán hàng',
      description: 'Trở thành đại lý & phát triển nguồn thu nhập',
      image: Sale,
      benefit: [
        'Chọn sản phẩm uy tín từ nhà cung cấp trên nền tảng',
        'Hoa hồng hấp dẫn, chính sách minh bạch',
        'Công cụ hỗ trợ bán hàng online, quản lý đơn hàng tiện lợi'
      ],
      link: '/partner-portal/register'
    }
  ]

  return (
    <div className='relative mb-[60px] w-full'>
      <div className='mb-[40px] w-full items-center justify-center'>
        <div className='font-montserrat mb-[5px] text-center text-base font-medium leading-[24px] tracking-normal text-[#0548BB]'>
          Dịch vụ của chúng tôi
        </div>
        <div className='mb-[20px] flex flex-col items-center'>
          <div className='font-inter text-center text-[32px] font-semibold leading-[48px] tracking-normal'>
            Đối tác trên nền tảng <span className='text-blue-600'>oneSME</span>
          </div>
        </div>
        <div className='flex flex-col items-center'>
          <p className='mx-auto max-w-[1180px] text-center text-base font-normal leading-[24px] tracking-wide text-[#6B7585]'>
            oneSME kết nối nhiều loại đối tác để xây dựng hệ sinh thái kinh doanh bền vững. Chúng tôi mang đến cơ hội
            hợp tác cho doanh nghiệp cung cấp hàng hóa, dịch vụ và đại lý bán hàng, giúp tối ưu lợi nhuận và mở rộng thị
            trường
          </p>
        </div>
      </div>

      <CardInformation introduceInfor={introduceInfor} />
    </div>
  )
}
