'use client'

import React from 'react'

export const FeaturedPartner = () => {
  const partnerLogo = [
    {
      title: 'Spotify',
      logo: '/assets/images/pages/dev-portal/partner-logo/spotify.png'
    },
    {
      title: 'Microsoft',
      logo: '/assets/images/pages/dev-portal/partner-logo/microsoft.png'
    },
    {
      title: 'Google',
      logo: '/assets/images/pages/dev-portal/partner-logo/google.png'
    },
    {
      title: 'Shopify',
      logo: '/assets/images/pages/dev-portal/partner-logo/shopify.png'
    },
    {
      title: 'SpaceX',
      logo: '/assets/images/pages/dev-portal/partner-logo/space-x.png'
    },
    {
      title: 'Tesla',
      logo: '/assets/images/pages/dev-portal/partner-logo/tesla.png'
    },
    {
      title: 'Upwork',
      logo: '/assets/images/pages/dev-portal/partner-logo/upwork.png'
    },
    {
      title: 'Trello',
      logo: '/assets/images/pages/dev-portal/partner-logo/trello.png'
    },
    {
      title: 'Webflow',
      logo: '/assets/images/pages/dev-portal/partner-logo/webflow.png'
    },
    {
      title: 'Zoom',
      logo: '/assets/images/pages/dev-portal/partner-logo/zoom.png'
    },
    {
      title: 'Himalayas',
      logo: '/assets/images/pages/dev-portal/partner-logo/himalayas.png'
    },
    {
      title: 'Toggl',
      logo: '/assets/images/pages/dev-portal/partner-logo/toggl.png'
    }
  ]

  return (
    <div className='relative w-full bg-[#F7F7F7] py-[60px]'>
      <div className='mb-[40px] w-full items-center justify-center'>
        <div className='font-inter mb-[5px] text-center text-lg font-medium leading-[24px] tracking-normal text-[#0548BB]'>
          Khách hàng của chúng tôi
        </div>
        <div className='flex flex-col items-center'>
          <div className='font-inter text-center text-[33px] font-semibold leading-[48px] tracking-normal'>
            Những đối tác tiêu biểu
          </div>
        </div>
      </div>

      <div className='mx-auto flex w-[1320px] flex-wrap items-center justify-center gap-[24px]'>
        {partnerLogo?.map((partner, index) => (
          <div key={index} className='flex h-[54px] w-[200px] items-center justify-center bg-white'>
            <img src={partner?.logo} alt={partner.title} />
          </div>
        ))}
      </div>
    </div>
  )
}
