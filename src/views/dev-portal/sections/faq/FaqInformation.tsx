'use client'

import React from 'react'

import { Collapse } from 'antd'

export const FaqInformation = () => {
  const faqData = [
    {
      label: 'oneSME hợp tác với những loại đối tác cung cấp sản phẩm/dịch vụ nào?',
      content: (
        <div>
          Nền tảng hợp tác với tất cả các doanh nghiệp, đơn vị phát triển có sản phẩm, dị<PERSON> vụ, gi<PERSON>i pháp số uy tín,
          chất lượng dành cho đa dạng phân khúc khách hàng như doanh nghiệp vừa và nhỏ (SME), <PERSON><PERSON>h doanh, <PERSON><PERSON> nhân.
          Các sản phẩm này cần phù hợp với hệ sinh thái chuyển đổi số của oneSME, ví dụ như phần mềm quản trị, giải pháp
          marketing, bảo mật, hạ tầng số, các gi<PERSON>i pháp IoT v.v.
        </div>
      )
    },
    {
      label: 'Để trở thành đại lý bán hàng cho các dịch vụ của oneSME cần những yêu cầu gì?',
      content: (
        <div>
          Để trở thành đại lý phân phối các sản phẩm cốt lõi của oneSME, bạn chỉ cần đăng ký thông tin. Nền tảng sẽ cung
          cấp đầy đủ công cụ bán hàng, tài liệu hướng dẫn, các chương trình đào tạo chuyên sâu và một chính sách chiết
          khấu hoa hồng cực kỳ hấp dẫn để bạn có thể tự tin phát triển kinh doanh.
        </div>
      )
    },
    {
      label: 'Khách hàng mục tiêu của oneSME là ai và làm thế nào sản phẩm của đối tác có thể tiếp cận họ?',
      content: (
        <div>
          oneSME sở hữu một hệ sinh thái rộng lớn với hàng triệu khách hàng là các doanh nghiệp vừa và nhỏ, hộ kinh
          doanh, cá nhân trên khắp cả nước. Khi trở thành đối tác, sản phẩm của bạn sẽ được giới thiệu và quảng bá trên
          các kênh bán đa dạng, từ nền tảng trực tuyến onesme.vn đến đội ngũ kinh doanh trực tiếp, giúp bạn mở rộng thị
          trường và tiếp cận đúng đối tượng khách hàng tiềm năng.
        </div>
      )
    },
    {
      label: 'Chính sách thương mại và chia sẻ lợi nhuận cho đối tác được xây dựng như thế nào?',
      content: (
        <div>
          Chúng tôi xây dựng chính sách hợp tác linh hoạt dựa trên nguyên tắc đôi bên cùng có lợi. Tùy thuộc vào mô hình
          hợp tác (cung cấp sản phẩm hay đại lý bán hàng), hai bên sẽ cùng nhau thống nhất một cơ chế chia sẻ doanh thu
          minh bạch, cạnh tranh và hấp dẫn, đảm bảo quyền lợi và tạo động lực phát triển cho đối tác.
        </div>
      )
    },
    {
      label: 'Tích hợp sản phẩm dịch vụ với nền tảng oneSME cần những gì?',
      content: (
        <div>
          oneSME đã xây dựng sẵn các giao thức kết nối (API) mạnh mẽ và linh hoạt để phục vụ việc tích hợp. Đội ngũ kỹ
          thuật của chúng tôi sẽ làm việc trực tiếp, hỗ trợ, cung cấp tài liệu hướng dẫn chi tiết và hỗ trợ 1-1 trong
          toàn bộ quá trình để đảm bảo sản phẩm của bạn nhanh chóng sẵn sàng trên nền tảng.
        </div>
      )
    },
    {
      label: 'Làm thế nào để tôi có thể theo dõi hiệu quả kinh doanh và doanh thu của mình trên oneSME?',
      content: (
        <div>
          Mỗi đối tác khi truy cập vào tài khoản của mình sẽ được cung cấp công cụ Partner Dashboard để có thể dễ dàng
          theo dõi các chỉ số kinh doanh theo thời gian thực như số lượt tiếp cận, số lượng đơn hàng, doanh thu, và các
          báo cáo chi tiết khác, giúp bạn nắm bắt hiệu quả và có kế hoạch phát triển phù hợp.
        </div>
      )
    }
  ]

  const items = faqData.map((item, index) => ({
    key: String(index + 1),
    label: `${index + 1}. ${item.label}`,
    children: item.content
  }))

  const customExpandIcon = ({ isActive }: any) => (
    <div className='flex size-6 items-center justify-center'>
      {isActive ? <i className='onedx-arrow-icon size-6 rotate-90' /> : <i className='onedx-arrow-icon size-6' />}
    </div>
  )

  return (
    <div className='relative w-full bg-white'>
      <div className='flex w-full items-center justify-center pb-[60px]'>
        <div className='flex w-[1320px] items-center'>
          <div className='w-[30%]'>
            <div className='font-inter mb-[5px] text-lg font-medium leading-[24px] tracking-normal text-[#0548BB]'>
              Câu hỏi thường gặp
            </div>
            <div className='mb-[20px] flex flex-col'>
              <div className='font-inter text-[33px] font-semibold leading-[48px] tracking-normal'>
                Bạn đang thắc mắc về dịch vụ ?
              </div>
            </div>
          </div>
          <div className='w-[70%]'>
            <style jsx global>{`
              .custom-faq-collapse .ant-collapse {
                background: transparent;
                border: none;
              }

              .custom-faq-collapse .ant-collapse-item-active .ant-collapse-content-box {
                padding: 0 !important;
                padding-block: 0 !important;
                padding-inline-start: 0 !important;
              }

              .custom-faq-collapse .ant-collapse-content-box {
                padding-block: 0;
                margin-bottom: 12px;
                font-size: 16px;
                line-height: 24px;
                letter-spacing: 0.5px;
                font-weight: 400;
                color: #555f70 !important;
              }

              .custom-faq-collapse .ant-collapse-item {
                border: none;
                border-bottom: 1px solid #c5cbd6;
                padding-bottom: 0;
              }

              .custom-faq-collapse .ant-collapse-item:last-child {
                border: none;
                border-bottom: 1px solid #c5cbd6;
                margin-bottom: 0;
              }

              .custom-faq-collapse .ant-collapse-header {
                background: transparent !important;
                border: none !important;
                font-size: 20px;
                line-height: 32px;
                letter-spacing: 0;
                padding: 23px 0 20px !important;
                font-weight: 500;
                color: black;
                align-items: center;
              }

              .custom-faq-collapse .ant-collapse-header:hover {
                background: transparent !important;
              }

              .custom-faq-collapse .ant-collapse-header-text {
                flex: 1;
              }

              .custom-faq-collapse .ant-collapse-expand-icon {
                position: static !important;
                order: 2;
                margin-left: auto;
                margin-right: 0;
                padding: 0;
              }

              .custom-faq-collapse .ant-collapse-content {
                background: transparent;
                border: none;
              }

              .custom-faq-collapse .ant-collapse-content-box {
                padding: 0;
                color: black;
                line-height: 1.6;
                padding-block: 0;
              }

              .custom-faq-collapse .ant-collapse-item-active .ant-collapse-header {
                color: #0a6fd0;
                padding: 23px 0 20px;
              }
            `}</style>
            <div className='custom-faq-collapse'>
              <Collapse ghost expandIcon={customExpandIcon} items={items} />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
