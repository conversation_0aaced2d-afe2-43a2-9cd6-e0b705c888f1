'use client'

import React from 'react'

import { Divider } from 'antd'

import { ArrowRightOutlined } from '@ant-design/icons'

interface StepInfo {
  stepInfor: any
}

export const Step: React.FC<StepInfo> = ({ stepInfor }) => {
  return (
    <div className='w-fit place-self-center rounded-2xl bg-white'>
      <div className='flex items-center'>
        {stepInfor.map((step: any, index: number) => (
          <div key={index} className='flex'>
            <div className='h-[247px] w-[440px] px-[35px] py-[30px]'>
              <div className='flex-1 px-4 text-center'>
                <div className='mb-4 flex justify-center'>
                  <img src={step.icon} alt={step.title} />
                </div>

                <div className='font-inter mb-[10px] text-xl font-semibold leading-[32px]'>{step.title}</div>

                {/* Description */}
                <div className='font-inter mb-[10px] text-base font-normal leading-[24px]'>{step.description}</div>

                {step.linkText && (
                  <div
                    className='font-inter cursor-pointer text-base font-normal text-[#0A6FD0] underline'
                    onClick={() => step?.link && (window.location.href = step?.link)}
                  >
                    {step.linkText}
                  </div>
                )}
              </div>
            </div>
            {index < stepInfor.length - 1 && (
              <div className='flex items-center justify-center'>
                <Divider type='vertical' style={{ height: '247px', borderColor: '#EBEFF5', borderWidth: '1px' }} />
                <div className='absolute flex size-[50px] items-center justify-center rounded-full bg-[#0A6FD0]'>
                  <ArrowRightOutlined className='text-white' />
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  )
}
