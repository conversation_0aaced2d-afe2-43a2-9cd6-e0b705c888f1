'use client'

import React from 'react'

import Image from 'next/image'

import Steps from '@public/assets/images/pages/dev-portal/banner-partner.png'

import { Step } from '@views/dev-portal/sections/step-becoming-partner/Step'

export const PartnerStepsBanner = () => {
  const stepInfor = [
    {
      icon: '/assets/images/pages/dev-portal/add-account-icon.png',
      title: 'Tạo một tài khoản',
      description: 'Đăng ký tài khoản miễn phí để bắt đầu hành trình trở thành đối tác oneSME.',
      linkText: 'Tạo ngay',
      link: '/partner-portal/register'
    },
    {
      icon: '/assets/images/pages/dev-portal/provide-information-icon.png',
      title: 'Cung cấp thông tin chi tiết',
      description: 'Chia sẻ thông tin cơ bản về đối tác, lĩnh vực kinh doanh và sản phẩm/dịch vụ cung cấp.'
    },
    {
      icon: '/assets/images/pages/dev-portal/verify-information-icon.png',
      title: '<PERSON><PERSON><PERSON> thực thông tin đăng ký',
      description: '<PERSON><PERSON><PERSON> tất quá trình xác thực để chính thức trở thành đối tác và bắt đầu kinh doanh trên nền tảng.'
    }
  ]

  return (
    <div className='relative mb-[60px] w-full'>
      <div className='relative flex h-[485px] w-full items-center justify-center'>
        <Image src={Steps} alt='Dev Partner Connection' className='size-full object-cover' />

        <div className='absolute inset-0 items-center py-[60px]'>
          <div className='font-inter mb-[5px] place-self-center text-lg font-medium leading-[24px] tracking-normal text-[#A7C6FD]'>
            Bắt Đầu ngay
          </div>
          <div className='font-inter mb-[40px] place-self-center text-[33px] font-semibold leading-[48px] tracking-normal text-white'>
            Dễ dàng trở thành đối tác của chúng tôi
          </div>
          <Step stepInfor={stepInfor} />
        </div>
      </div>
    </div>
  )
}
