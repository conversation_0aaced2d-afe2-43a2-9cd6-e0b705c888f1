'use client'

import { BenefitInformation } from '@views/dev-portal/sections/benefit-information/BenefitInformation'
import { ContactInformation } from '@views/dev-portal/sections/contact-infor/ContactInformation'
import { FaqInformation } from '@views/dev-portal/sections/faq/FaqInformation'
import { FeaturedPartner } from '@views/dev-portal/sections/featured-partner/FeaturedPartner'
import { PartnerConnectionBanner } from '@views/dev-portal/sections/introduction-banner/PartnerConnectionBanner'
import { PartnerCommissionBanner } from '@views/dev-portal/sections/partner-commission-banner/PartnerCommissionBanner'
import { IntroducePartnerInformation } from '@views/dev-portal/sections/partner-information/IntroducePartnerInformation'
import { PartnerStepsBanner } from '@views/dev-portal/sections/step-becoming-partner/PartnerStepsBanner'

export const HomePage = () => {
  return (
    <>
      {/* Home Banner giới thiệu */}
      <section id='trang-chu-section'>
        <PartnerConnectionBanner />
      </section>

      {/* Giới thiệu thông tin đối tác trên oneSME */}
      <section id='gioi-thieu-section'>
        <IntroducePartnerInformation />
      </section>

      {/* Các bước trở thành đối tác */}
      <section id='huong-dan-section'>
        <PartnerStepsBanner />
      </section>

      {/* Lợi ích và ưu đãi */}
      <section id='loi-ich-section'>
        <BenefitInformation />
      </section>

      {/* Hoa hồng đối tác */}
      <section id='hoa-hong-section'>
        <PartnerCommissionBanner />
      </section>

      {/* Đối tác tiêu biểu */}
      <section id='doi-tac-section'>
        <FeaturedPartner />
      </section>

      {/* Liên hệ */}
      <section id='lien-he-section'>
        <ContactInformation />
      </section>

      {/* Đối tác tiêu biểu */}
      <section id='faq-section'>
        <FaqInformation />
      </section>
    </>
  )
}
