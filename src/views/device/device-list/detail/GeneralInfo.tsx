import React from 'react'

import { useRouter } from 'next/navigation'

import dayjs from 'dayjs'

import { Collapse, Tag } from 'antd'

import { styled } from 'styled-components'

import { tagStatus } from '@components/device/device-list/common/constant'

import type { AttributeInformation, DeviceDetailInfo, StatusInfoItem } from '@/types/device/device-list/deviceList'

import { ContentRow } from '@/components/device/device-list/detail/ContentRow'
import { ContentRender } from '@views/order/detail/tabs/common/components'

interface Props {
  deviceDetail: DeviceDetailInfo
}

const { Panel } = Collapse

const StyledCollapse = styled(Collapse)`
  .ant-collapse-content-box {
    padding-top: 0 !important;
  }
  .ant-collapse-header-text {
    font-size: 16px;
  }
  .ant-collapse-header.ant-collapse-collapsible-icon {
    display: flex;
    align-items: center;
  }
  background-color: white;
`

type TagStatusKey = keyof typeof tagStatus

const renderTagStatus = (value: TagStatusKey) => {
  const tagInfo = tagStatus[value]

  return (
    <Tag color={tagInfo?.color}>
      <div className='w-[100px] text-center' style={{ color: `${tagInfo.textColor}` }}>
        {tagInfo?.text}
      </div>
    </Tag>
  )
}

export const GeneralInfo = ({ deviceDetail }: Props) => {
  const router = useRouter()

  const currentStatus = [
    { label: 'Trạng thái kết nối', content: renderTagStatus(deviceDetail?.currentStatus?.status as TagStatusKey) },
    {
      label: 'Thời gian kết nối gần nhất',
      content: dayjs(deviceDetail?.currentStatus?.lastConnectionTime).format('DD/MM/YYYY - HH:mm:ss')
    }
    // { label: 'Thông tin trạng thái', content: '', hideEmpty: true }
  ]

  const parsedStatus = JSON.parse(deviceDetail?.currentStatus?.statusInformation || '[]') as StatusInfoItem[]

  const statusInfo = parsedStatus.map((item: StatusInfoItem) => {
    const content = item.value ? `${item.value} ${item.unit || ''}` : `--${item.unit ? item.unit : ''}`

    let label = item.displayName || ''

    if (label) {
      label = label.charAt(0).toUpperCase() + label.slice(1)
    }

    return {
      label,
      content
    }
  })

  const businessStatus = [
    { label: 'Khách hàng', content: deviceDetail?.businessStatus?.clientName },
    {
      label: 'Đơn hàng',
      content: deviceDetail?.businessStatus?.orderCode ? (
        <span
          className='text-primary-blue underline hover:cursor-pointer'
          onClick={() => router.push(`/order/detail/${deviceDetail.subscriptionId}`)}
        >
          {deviceDetail.businessStatus.orderCode}
        </span>
      ) : (
        ''
      )
    }
  ]

  const indentificationInfo = [
    { label: 'Tên thiết bị', content: deviceDetail?.detailedInformation?.deviceName },
    { label: 'Model', content: deviceDetail?.detailedInformation?.deviceModel },
    { label: 'Nhà cung cấp', content: deviceDetail?.detailedInformation?.supplier }
  ]

  const parsedAttribute = JSON.parse(
    deviceDetail?.detailedInformation?.attributeInformation || '[]'
  ) as AttributeInformation[]

  const atrributeInfo = parsedAttribute.map((item: AttributeInformation) => {
    const content = item.value ? `${item.value}` : ''

    let label = item.displayName || ''

    if (label) {
      label = label.charAt(0).toUpperCase() + label.slice(1)
    }

    return {
      label,
      content
    }
  })

  const identifierCode = [
    { label: 'ID trên BOS', content: deviceDetail?.identifier?.idBOS },
    { label: 'ID trên Platform', content: deviceDetail?.identifier?.idPlatform },
    { label: 'ID trên Đối tác', content: deviceDetail?.identifier?.idPartner }
  ]

  return (
    <div className='flex gap-4'>
      <div className='w-1/3'>
        {/* Trạng thái hiện tại */}
        <StyledCollapse
          className='mb-2 bg-white'
          collapsible='icon'
          expandIconPosition='end'
          expandIcon={() => null}
          ghost
          defaultActiveKey={['1']}
        >
          <Panel
            key='1'
            header={
              <div className='mt-1 flex justify-between border-l-4 border-solid border-yellow-6'>
                <div className='pl-2 font-semibold'>Trạng thái hiện tại</div>
              </div>
            }
          >
            <div className='border-t border-solid border-slate-200'></div>
            <div className='flex flex-col gap-2 pb-1 pt-4'>
              {currentStatus?.map(field => (
                <>
                  <ContentRow label={field.label} content={field.content} />
                </>
              ))}

              {statusInfo.length > 0 && (
                <div className='flex flex-col gap-2'>
                  <div className='text-text-neutral-medium'>Thông tin trạng thái</div>
                  <div className='rounded-lg bg-gray-12 p-3'>
                    {statusInfo?.map((field, index) => (
                      <ContentRow key={index} label={field.label} content={field.content} />
                    ))}
                  </div>
                </div>
              )}
            </div>
          </Panel>
        </StyledCollapse>

        {/* Trạng thái kinh doanh */}
        <StyledCollapse
          className='mb-2 bg-white'
          collapsible='icon'
          expandIconPosition='end'
          expandIcon={() => null}
          ghost
          defaultActiveKey={['1']}
        >
          <Panel
            key='1'
            header={
              <div className='mt-1 flex justify-between border-l-4 border-solid border-yellow-6'>
                <div className='pl-2 font-semibold'>Trạng thái kinh doanh</div>
              </div>
            }
          >
            <div className='border-t border-solid border-slate-200'></div>
            <div className='flex flex-col gap-2 pb-1 pt-4'>
              {businessStatus?.map(field => (
                <>
                  <ContentRow label={field.label} content={field.content} />
                </>
              ))}
            </div>
          </Panel>
        </StyledCollapse>
      </div>
      <div className='w-2/3'>
        {/*  Thông tin chi tiết */}
        <StyledCollapse
          className='mb-2 bg-white'
          collapsible='icon'
          expandIconPosition='end'
          expandIcon={() => null}
          ghost
          defaultActiveKey={['1']}
        >
          <Panel
            key='1'
            header={
              <div className='mt-1 flex justify-between border-l-4 border-solid border-yellow-6'>
                <div className='pl-2 font-semibold'>Thông tin chi tiết</div>
              </div>
            }
          >
            <div className='border-t border-solid border-slate-200'></div>
            <div className='flex items-center gap-2 pt-5'>
              <span className='size-2 rounded-full bg-green-6'></span>
              <span className='font-medium text-gray-600'>Thông tin nhận dạng</span>
            </div>
            <div className='mt-3 grid grid-cols-3 rounded-lg bg-gray-12 p-3'>
              {indentificationInfo?.map(field => (
                <>
                  <ContentRender label={field.label} content={field.content} />
                </>
              ))}
            </div>

            {atrributeInfo.length > 0 && (
              <>
                <div className='flex items-center gap-2 pt-2'>
                  <span className='size-2 rounded-full bg-green-6'></span>
                  <span className='font-medium text-gray-600'>Thông tin thuộc tính</span>
                </div>
                <div className='mt-3 grid grid-cols-3 rounded-lg bg-gray-12 p-3'>
                  {atrributeInfo?.map(field => (
                    <>
                      <ContentRender label={field.label} content={field.content} />
                    </>
                  ))}
                </div>
              </>
            )}
          </Panel>
        </StyledCollapse>

        {/*  Mã định danh */}
        <StyledCollapse
          className='mb-2 bg-white'
          collapsible='icon'
          expandIconPosition='end'
          expandIcon={() => null}
          ghost
          defaultActiveKey={['1']}
        >
          <Panel
            key='1'
            header={
              <div className='mt-1 flex justify-between border-l-4 border-solid border-yellow-6'>
                <div className='pl-2 font-semibold'>Mã định danh</div>
              </div>
            }
          >
            <div className='border-t border-solid border-slate-200'></div>
            <div className='grid grid-cols-3 gap-2 pb-1 pt-4'>
              {identifierCode?.map(field => (
                <>
                  <ContentRender label={field.label} content={field.content} />
                </>
              ))}
            </div>
          </Panel>
        </StyledCollapse>
      </div>
    </div>
  )
}
