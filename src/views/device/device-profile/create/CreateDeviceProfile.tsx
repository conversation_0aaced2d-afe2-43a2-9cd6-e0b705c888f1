'use client'

import React from 'react'

import { useRouter } from 'next/navigation'

import { useMutation } from '@tanstack/react-query'

import { Button, Form, Spin } from 'antd'

import deviceInstance from '@/models/Device'

import { message } from '@components/notification'

import TelemetryStructure from '@components/device/device-profile/create/TelemetryStructure'
import DeviceProperties from '@components/device/device-profile/create/DeviceProperties'

import ControlCommand from '@components/device/device-profile/create/ControlCommand'
import GeneralInfo from '@components/device/device-profile/create/GeneralInfo'

import HeaderSection from '@components/solution-iot/service/create/HeaderSection'

export const CreateDeviceProfile = () => {
  const [form] = Form.useForm()

  const router = useRouter()

  const handleBack = () => {
    router.back()
  }

  const createDeviceProfile = useMutation({
    mutationFn: (data: any) => deviceInstance.createDeviceProfile(data),
    onSuccess: () => {
      message.success('<PERSON><PERSON><PERSON> <PERSON>ồ sơ thiết bị thành công')
      router.push('/device/device-profile/list')
    },
    onError: (error: any) => {
      if (error.errorCode === 'error.profile.model.exist') {
        message.error('Mã thiết bị đã tồn tại')
      } else if (error.errorCode === 'error.profile.name.exist') {
        message.error('Tên hồ sơ đã tồn tại')
      } else {
        message.error('Có lỗi xảy ra khi tạo hồ sơ thiết bị')
      }
    }
  })

  const handleSubmit = () => {
    form
      .validateFields()
      .then(() => {
        const values = form.getFieldsValue()

        const newValues = {
          ...values,
          deviceAttributes: values.deviceAttributes?.map((attr: any) => ({
            ...attr,
            name: '1111'
          }))
        }

        delete newValues?.params

        createDeviceProfile.mutate(newValues)
      })
      .catch(error => {
        if (error?.errorFields) {
          error.errorFields.forEach((fieldError: any) => {
            if (fieldError.errors && fieldError.errors.length > 0) {
              fieldError.errors.forEach((errMsg: string) => {
                message.error(errMsg)
              })
            }
          })
        } else if (typeof error === 'string') {
          message.error(error)
        } else {
          message.error('Đã xảy ra lỗi. Vui lòng kiểm tra lại.')
        }
      })
  }

  return (
    <Spin spinning={false}>
      <Form form={form} layout='vertical' onFinish={handleSubmit}>
        <div className='my-3 flex flex-1 p-2'>
          <HeaderSection title='Tạo hồ sơ thiết bị' onBack={handleBack} />
        </div>
        <div className='m-4 flex flex-row gap-4'>
          <div className='basis-1/3 space-y-6'>
            <GeneralInfo />
            <DeviceProperties form={form} />
          </div>
          <div className='basis-2/3 space-y-6'>
            <ControlCommand form={form} />
            <TelemetryStructure form={form} />
          </div>
        </div>
        <div className='h-16'></div>
        <div className='fixed bottom-0 left-0 z-50 w-[95%]'>
          <div className='flex w-full items-center justify-between gap-4 rounded-2xl bg-white p-4 shadow-[0_-4px_12px_rgba(0,0,0,0.1)]'>
            <div className='flex items-center gap-4'></div>
            <div className='flex items-center gap-4'>
              <Button color='primary' variant='outlined' onClick={handleBack}>
                Hủy
              </Button>
              <Button type='primary' onClick={() => handleSubmit()}>
                Lưu
              </Button>
            </div>
          </div>
        </div>
      </Form>
    </Spin>
  )
}
