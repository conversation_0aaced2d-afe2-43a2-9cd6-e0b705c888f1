import moment from 'moment'

export const checkboxOptions = [
  { label: 'Thông tin thuê bao', value: 'subCode', status: 'disabled' },
  { label: 'Thôn tin khách hàng', value: 'userName', status: 'disabled' },
  { label: 'Sản phẩm', value: 'orderItems', status: 'disabled' },
  { label: 'Trạng thái tiến trình', value: 'totalProgressStatusEnum', status: 'disabled' },
  { label: 'Thành tiền', value: 'billTotal' },
  { label: 'Ngày tạo', value: 'createdAt' },
  { label: 'Nguồn tạo', value: 'createSource' },
  { label: 'Nhân sự phụ trách', value: 'assigneeName' },
  { label: 'Nhà cung cấp', value: 'providerName' }
]

export const PROGRESS = {
  RECEIVED: 'RECEIVED',
  PROCESSING: 'PROCESSING',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED'
}

export const progressOptions = [
  { label: 'Tiếp nhận đơn hàng', value: PROGRESS.RECEIVED },
  { label: 'Đang xử lý', value: PROGRESS.PROCESSING },
  { label: 'Đã hoàn tất', value: PROGRESS.COMPLETED },
  { label: 'Đã hủy', value: PROGRESS.CANCELLED }
]

export const PAGE_SIZE = 10

export const isCartCode = (orderCode: string) => {
  return /[a-zA-Z]/.test(orderCode)
}

export const formatDate = (date: string | undefined): string | undefined => {
  if (!date) {
    return undefined
  }

  return moment(date, 'DD/MM/YYYY').format('DD/MM/YYYY')
}

export const quickSearchOptions = [
  { label: 'Tên sản phẩm dịch vụ', key: 'isServiceName' },
  { label: 'Tên khách hàng', key: 'isName' },
  { label: 'Số điện thoại', key: 'isPhone' },
  { label: 'Email', key: 'isEmail' },
  { label: 'Mã đơn hàng', key: 'isSubCode' }
]

export const filterOptions = [
  {
    name: 'categoriesId',
    label: 'Tìm kiếm theo',
    options: []
  },
  {
    name: 'categoriesId',
    label: 'Loại dịch vụ',
    options: []
  },
  {
    name: 'categoriesId',
    label: 'Đối tượng khách hàng',
    options: []
  },
  {
    name: 'categoriesId',
    label: 'Trạng thái cài đặt',
    options: []
  },
  {
    name: 'categoriesId',
    label: 'Trạng thái thuê bao',
    options: []
  },
  {
    name: 'manufactureId',
    label: 'Trạng thái đơn hàng',
    options: []
  }
]

export const TYPE_OF_SERVICE = [
  {
    value: 'ALL',
    label: 'Tất cả'
  },
  {
    value: 'PHYSICAL_PRODUCT',
    label: 'Hàng hóa vật lý'
  },
  {
    value: 'PACKAGE_BUNDLING',
    label: 'Gói dịch vụ'
  }
]

export const optionCreateSource = [
  {
    value: '1',
    label: 'Admin'
  },
  {
    value: '2',
    label: 'Developer'
  },
  {
    value: '3',
    label: 'Khách hàng'
  },
  {
    value: '4',
    label: 'Affiliate'
  }
]

// Xử lý giá trị null, chuỗi rỗng, chuỗi chỉ chứa dấu phẩy, hoặc giá trị 'ALL'
export const isEmptyOrInvalid = (value: any) => {
  if (value === null || value === undefined || value === -1) return true
  if (value === 'ALL') return true // Xử lý giá trị 'ALL' đặc biệt cho option "Tất cả"
  if (typeof value === 'string' && (value === '' || value === ',' || value === 'null')) return true
  if (Array.isArray(value) && value.length === 0) return true

  return false
}
