'use client'

import { useMemo, useState } from 'react'

import { useRouter } from 'next/navigation'

import { Button, Pagination, Select, Spin, Tag, Tooltip } from 'antd'

import { useQuery } from '@tanstack/react-query'

import { EditTableColumn, EmptyTableOrder, OrderProductList } from '../common'
import {
  checkboxOptions,
  formatDate,
  isEmptyOrInvalid,
  optionCreateSource,
  PAGE_SIZE,
  quickSearchOptions,
  TYPE_OF_SERVICE
} from '../constants'

import { useUser } from '@/hooks'
import SettingInput from '@/components/filter/SettingInput'
import { CustomPopover, CustomTable } from '../common/style'
import { OrderOverviewDetail } from '../order-overview'
import OrderManagement from '@/models/OrderManagement'
import CategoriesInstance from '@/models/Categories'
import SelectFilterPopup from '../common/SelectFilterPopup'
import Address from '@/models/Address'
import SubscriptionSme from '@/models/SubscriptionSme'
import { MultiCheckboxSelect } from '@/components/filter/MultiCheckboxSelect'
import { FormattedCurrency } from '@/views/order/detail/tabs/common/components/FormattedCurrency'
import { pageSizeOptions } from '@/views/product-catalog/constants/constants'

interface FilterParams {
  processStatus: null | string | number
  productItemTypes: null | string | string[]
  lstCategoryId: null | string | string[]
  startDate: null | string
  endDate: null | string
  lstProvince: null | string | string[]
  lstProvider: null | string | string[]
  invoiceAddress: null | string
  address: null | string
  assigneeName: null | string
  createSource: null | string
  lstMst: null | string | string[]
  lstTotalProgress: null | any[]
  workflowStepIds: null | any[]
}

export const OrderList = () => {
  const { user } = useUser()

  const router = useRouter()

  const isAdmin = user?.portalType === 'ADMIN'

  const portalType = isAdmin ? 'admin' : 'dev'

  // region state
  const [page, setPage] = useState(1)
  const [pageSize, setPageSize] = useState(PAGE_SIZE)
  const [openSummaryDetail, setOpenSummaryDetail] = useState(false)
  const [orderCode, setOrderCode] = useState(null)
  const [subCode, setSubCode] = useState(null)
  const [searchValue, setSearchValue] = useState<string | null>(null)

  const [checkedFilter, setCheckedFilter] = useState<any>({
    isServiceName: true,
    isName: true,
    isPhone: true,
    isEmail: true,
    isSubCode: true
  })

  const [selectedColumns, setSelectedColumns] = useState<string[]>([
    'subCode',
    'userName',
    'orderItems',
    'workflowStepName',
    'billTotal',
    'createdAt',
    'createSource',
    'assigneeName',
    'providerName'
  ])

  const [filterParams, setFilterParams] = useState<FilterParams>({
    processStatus: null,
    productItemTypes: 'ALL', // Mặc định chọn "Tất cả"
    lstCategoryId: null,
    startDate: null,
    endDate: null,
    lstProvince: null,
    lstProvider: null,
    invoiceAddress: null,
    address: null,
    assigneeName: null,
    createSource: null,
    lstMst: null,
    lstTotalProgress: null,
    workflowStepIds: null
  })

  // region params
  const params = useMemo(() => {
    const {
      processStatus,
      productItemTypes,
      lstCategoryId,
      startDate,
      endDate,
      lstProvince,
      lstProvider,
      invoiceAddress,
      address,
      assigneeName,
      createSource,
      lstMst,
      lstTotalProgress,
      workflowStepIds
    } = filterParams

    const defaultParams = {
      page: page - 1,
      size: pageSize
    }

    // Chuyển đổi các mảng giá trị thành chuỗi phân tách bằng dấu phẩy khi không phải null
    const formattedProductItemTypes = isEmptyOrInvalid(productItemTypes)
      ? null
      : Array.isArray(productItemTypes)
        ? (productItemTypes as string[]).filter(Boolean).join(',')
        : productItemTypes

    const formattedLstCategoryId = isEmptyOrInvalid(lstCategoryId)
      ? null
      : Array.isArray(lstCategoryId)
        ? (lstCategoryId as string[]).filter(Boolean).join(',')
        : lstCategoryId

    const formattedLstProvince = isEmptyOrInvalid(lstProvince)
      ? null
      : Array.isArray(lstProvince)
        ? (lstProvince as string[]).filter(Boolean).join(',')
        : lstProvince

    const formattedLstMst = isEmptyOrInvalid(lstMst)
      ? null
      : Array.isArray(lstMst)
        ? (lstMst as string[]).filter(Boolean).join(',')
        : lstMst

    const formattedLstProvider = isEmptyOrInvalid(lstProvider)
      ? null
      : Array.isArray(lstProvider)
        ? (lstProvider as string[]).filter(Boolean).join(',')
        : lstProvider

    const formattedLstTotalProgress = isEmptyOrInvalid(lstTotalProgress)
      ? null
      : Array.isArray(lstTotalProgress)
        ? (lstTotalProgress as string[]).filter(Boolean).join(',')
        : lstTotalProgress

    const formattedWorkflowSteps = isEmptyOrInvalid(workflowStepIds)
      ? null
      : Array.isArray(workflowStepIds)
        ? (workflowStepIds as string[]).filter(Boolean).join(',')
        : workflowStepIds

    if (searchValue) {
      return {
        ...defaultParams,
        isServiceName: checkedFilter.isServiceName ? 1 : 0,
        isName: checkedFilter.isName ? 1 : 0,
        isPhone: checkedFilter.isPhone ? 1 : 0,
        isEmail: checkedFilter.isEmail ? 1 : 0,
        isSubCode: checkedFilter.isSubCode ? 1 : 0,
        value: searchValue,
        ...(processStatus !== null && { processStatus }),
        ...(formattedProductItemTypes !== null && { productItemTypes: formattedProductItemTypes }),
        ...(formattedLstCategoryId !== null && { lstCategoryId: formattedLstCategoryId }),
        ...(startDate !== null && { startDate: startDate }),
        ...(endDate !== null && { endDate: endDate }),
        ...(formattedLstProvince !== null && { lstProvince: formattedLstProvince }),
        ...(formattedLstProvider !== null && { lstProvider: formattedLstProvider }),
        ...(invoiceAddress !== null && { invoiceAddress }),
        ...(address !== null && { address }),
        ...(assigneeName !== null && { assigneeName }),
        ...(createSource !== null && { createSource }),
        ...(formattedLstMst !== null && { lstMst: formattedLstMst }),
        ...(formattedLstTotalProgress !== null && { lstTotalProgress: formattedLstTotalProgress }),
        ...(formattedWorkflowSteps !== null && { workflowStepIds: formattedWorkflowSteps })
      }
    }

    return {
      ...defaultParams,
      ...(processStatus !== null && { processStatus }),
      ...(formattedProductItemTypes !== null && { productItemTypes: formattedProductItemTypes }),
      ...(formattedLstCategoryId !== null && { lstCategoryId: formattedLstCategoryId }),
      ...(startDate !== null && { startDate: startDate }),
      ...(endDate !== null && { endDate: endDate }),
      ...(formattedLstProvince !== null && { lstProvince: formattedLstProvince }),
      ...(formattedLstProvider !== null && { lstProvider: formattedLstProvider }),
      ...(invoiceAddress !== null && { invoiceAddress }),
      ...(address !== null && { address }),
      ...(assigneeName !== null && { assigneeName }),
      ...(createSource !== null && { createSource }),
      ...(formattedLstMst !== null && { lstMst: formattedLstMst }),
      ...(formattedLstTotalProgress !== null && { lstTotalProgress: formattedLstTotalProgress }),
      ...(formattedWorkflowSteps !== null && { workflowStepIds: formattedWorkflowSteps })
    }
  }, [
    checkedFilter.isEmail,
    checkedFilter.isName,
    checkedFilter.isPhone,
    checkedFilter.isServiceName,
    checkedFilter.isSubCode,
    filterParams,
    page,
    pageSize,
    searchValue
  ])

  // region api
  const { data, isLoading } = useQuery({
    queryKey: ['getListOrder', params, portalType],
    queryFn: async () => {
      const res = await OrderManagement.getListOrder(params, portalType)

      return res
    }
  })

  const { data: dataCategory } = useQuery({
    queryKey: ['getListCategory'],
    queryFn: async () => {
      const res =
        portalType === 'admin'
          ? await CategoriesInstance.getAllAdminCategories()
          : await CategoriesInstance.getAllDevCategories()

      return [
        { label: 'Tất cả', value: -1 },
        ...res.map((e: any) => ({
          label: e.name,
          value: e.id
        }))
      ]
    }
  })

  const { data: listProvinceSelect } = useQuery({
    queryKey: ['getAllProvincePotential'],
    queryFn: async () => {
      const res = await Address.getAllProvince({ size: 100 })

      return [
        ...res.map((e: any) => ({
          label: e.name,
          value: e.id
        }))
      ]
    },

    initialData: []
  })

  const { data: optionMst } = useQuery({
    queryKey: ['getListMst'],
    queryFn: async () => {
      const res = await OrderManagement.getListMst({
        size: 100,
        portal: 'DEV'
      })

      return [
        ...res.content.map((e: any) => ({
          label: e.label,
          value: e.value
        }))
      ]
    },
    initialData: []
  })

  const { data: optionProvider } = useQuery({
    queryKey: ['getListSubProvider'],
    queryFn: async () => {
      const res = await SubscriptionSme.getListSubProvider({
        size: 9999
      })

      return [
        ...res.content.map((e: any) => ({
          label: e.value,
          value: e.id
        }))
      ]
    },
    initialData: []
  })

  /** Gọi API lấy chi tiết khách hàng */
  const { data: stepOptions } = useQuery({
    queryKey: ['getStepOptions'],
    queryFn: async () => {
      const res = await OrderManagement.getStepOptions({ size: 500, page: 0 })

      return res?.content?.map((item: any) => ({ value: item?.id, label: item?.name }))
    },
    initialData: []
  })

  // State riêng để quản lý các giá trị đã chọn trong MultiCheckboxSelect
  const selectedProgress = useMemo(() => {
    return stepOptions?.map((step: any) => step.value)
  }, [stepOptions])

  // region columns
  const columns: any = [
    {
      title: <div className='text-caption-12 font-semibold text-text-neutral-medium'>STT</div>,
      key: 1,
      render: (value: any, item: any, index: number) => {
        if (!item.serviceId && !item.childName) return <div>{(page - 1) * pageSize + index + 1}</div>

        return <></>
      },
      width: 60
    },
    // Mặc định
    {
      title: <div className='text-caption-12 font-semibold text-text-neutral-medium'>Thông tin thuê bao</div>,
      dataIndex: 'subCode',
      key: 'subCode',
      width: 164,
      sorter: (a: any, b: any) => a.subCode?.localeCompare(b.subCode),
      ellipsis: true,
      render: (value: any, record: any) => (
        <div
          className='cursor-pointer truncate text-primary'
          onClick={() => {
            setOrderCode(record?.isCart ? record?.subCode : record?.subId)
            setSubCode(record?.subCode)
            setOpenSummaryDetail(true)
          }}
        >
          {value}
        </div>
      ),
      checked: true,
      disabled: true
    },
    {
      title: <div className='text-caption-12 font-semibold text-text-neutral-medium'>Thông tin khách hàng</div>,
      dataIndex: 'userName',
      key: 'userName',
      width: 271,
      ellipsis: true,
      render: (value: any, record: any) => (
        <Tooltip title={value} placement='topLeft'>
          <div style={{ display: 'flex' }} className='w-full'>
            <div className='mr-3 flex size-6 shrink-0 items-center justify-center rounded-sm bg-white text-text-primary-default'>
              {record.customerType === 'ENTERPRISE' && (
                <i className='onedx-business size-5 text-icon-primary-default' />
              )}
              {record.customerType === 'HOUSEHOLD' && <i className='onedx-business size-5 text-icon-primary-default' />}
              {record.customerType === 'PERSONAL' && <i className='onedx-user1 size-5 text-icon-primary-default' />}
            </div>
            <div className='truncate'>{value}</div>
          </div>
        </Tooltip>
      )
    },
    // Mặc định
    {
      title: <div className='text-caption-12 font-semibold text-text-neutral-medium'>Sản phẩm</div>,
      dataIndex: 'orderItems',
      key: 'orderItems',
      width: 201,
      render: (value: any, record: any) => {
        // Nội dung hiển thị trong Popover luôn là orderItems
        const popoverContent = <OrderProductList orderItems={value} />

        // Ưu tiên hiển thị theo thứ tự: solutionName -> packageName -> orderItems
        const displayName = record.packageName || value?.[0]?.serviceName

        if (!displayName) return null

        let displayContent = (
          <div className='inline-block max-w-[165px] cursor-pointer truncate rounded-md bg-bg-neutral-lighter px-2 py-[2px]'>
            <span className='text-caption-12 font-medium text-text-neutral-medium' title={displayName}>
              {displayName}
            </span>
          </div>
        )

        if (!record.packageName && value?.length > 1) {
          displayContent = (
            <div className='flex cursor-pointer items-center gap-1'>
              <div className='inline-block max-w-[123px] truncate rounded-md bg-bg-neutral-lighter px-2 py-[2px]'>
                <span className='text-caption-12 font-medium text-text-neutral-medium'>{value[0].serviceName}</span>
              </div>
              <div className='inline-block rounded-md bg-bg-neutral-lighter px-2 py-[2px]'>
                <span className='text-caption-12 font-medium text-text-neutral-medium'>+{value.length - 1}</span>
              </div>
            </div>
          )
        }

        return (
          <CustomPopover content={popoverContent} placement='bottomLeft' trigger='hover' arrow={false}>
            {displayContent}
          </CustomPopover>
        )
      }
    },
    {
      title: <div className='text-caption-12 font-semibold text-text-neutral-medium'>Trạng thái tiến trình</div>,
      dataIndex: 'workflowStepName',
      key: 'workflowStepName',
      width: 170,
      render: (value: any, record: any) => {
        if (!value) {
          return ''
        }

        return (
          <Tag
            style={{
              backgroundColor: record?.workflowStepColor || '#CDE4FE',
              color: 'white',
              border: 'none',
              padding: '2px 8px',
              width: '100%',
              textAlign: 'center'
            }}
          >
            {value}
          </Tag>
        )
      }
    },
    {
      title: <div className='text-caption-12 font-semibold text-text-neutral-medium'>Thành tiền</div>,
      dataIndex: 'billTotal',
      key: 'billTotal',
      render: (value: any) => <FormattedCurrency value={value} />,
      width: 139
    },
    {
      title: <div className='text-caption-12 font-semibold text-text-neutral-medium'>Ngày tạo</div>,
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (value: any) => <div className='truncate'>{formatDate(value)}</div>,
      width: 114
    },
    {
      title: <div className='text-caption-12 font-semibold text-text-neutral-medium'>Nguồn tạo</div>,
      dataIndex: 'createSource',
      key: 'createSource',
      render: (value: any) => (
        <Tooltip title={value} placement='topLeft'>
          <div className='truncate'>{value}</div>
        </Tooltip>
      ),
      width: 113
    },
    {
      title: <div className='text-caption-12 font-semibold text-text-neutral-medium'>Nhân sự phụ trách</div>,
      dataIndex: 'assigneeName',
      key: 'assigneeName',
      width: 185,
      ellipsis: true,
      render: (value: any) => (
        <Tooltip title={value} placement='topLeft'>
          <div className='truncate'>{value}</div>
        </Tooltip>
      )
    },
    {
      title: <div className='text-caption-12 font-semibold text-text-neutral-medium'>Nhà cung cấp</div>,
      dataIndex: 'providerName',
      key: 'providerName',
      width: 164,
      render: (value: any) => (
        <Tooltip title={value} placement='topLeft'>
          <div className='max-w-40 truncate'>{value}</div>
        </Tooltip>
      )
    }
  ]

  const filteredColumns = columns.filter(
    (col: any) => !col.dataIndex || selectedColumns.includes(col.dataIndex.toString())
  )

  const listFilter: any[] = [
    {
      value: 'productItemTypes',
      name: 'productItemTypes',
      label: 'Loại dịch vụ',
      options: TYPE_OF_SERVICE,
      type: 'singleSelect'
    },
    {
      value: 'lstCategoryId',
      name: 'lstCategoryId',
      label: 'Danh mục',
      options: dataCategory || [],
      type: 'select'
    },
    {
      value: 'lstProvince',
      name: 'lstProvince',
      label: 'Tỉnh/Thành phố',
      options: listProvinceSelect || [],
      type: 'select',
      emptySearchText: 'Không có dữ liệu tìm kiếm'
    },
    {
      value: 'startDate,endDate',
      name: 'startDate,endDate',
      label: 'Khoảng thời gian tạo',
      options: [],
      type: 'rangePicker'
    },
    {
      value: 'lstProvider',
      name: 'lstProvider',
      label: 'Nhà cung cấp',
      options: optionProvider || [],
      type: 'select',
      emptySearchText: 'Không có dữ liệu tìm kiếm'
    },
    {
      value: 'invoiceAddress',
      name: 'invoiceAddress',
      label: 'Địa chỉ xuất hóa đơn',
      type: 'input',
      placeHolder: 'Nhập địa chỉ xuất hóa đơn'
    },
    {
      value: 'address',
      name: 'address',
      label: 'Địa chỉ giao hàng',
      type: 'input',
      placeHolder: 'Nhập địa chỉ giao hàng'
    },
    {
      value: 'assigneeName',
      name: 'assigneeName',
      label: 'Nhân sự phụ trách',
      type: 'input',
      placeHolder: 'Nhập nhân sự phụ trách'
    },
    {
      value: 'createSource',
      name: 'createSource',
      label: 'Nguồn tạo',
      options: optionCreateSource,
      type: 'singleSelect'
    },
    {
      value: 'lstMst',
      name: 'lstMst',
      label: 'Số chứng thực',
      options: optionMst || [],
      type: 'select',
      emptySearchText: 'Không có dữ liệu tìm kiếm'
    }
  ]

  const renderTable = () => (
    <div className='w-full overflow-hidden'>
      <div className='right-0 float-right grid w-[200%] translate-x-1/2 grid-cols-2 transition duration-300'>
        <Spin spinning={isLoading}>
          <CustomTable
            columns={filteredColumns}
            dataSource={data?.content}
            pagination={false}
            scroll={{ x: '1200', y: 'calc(100vh - 300px)' }}
            locale={{
              emptyText: <EmptyTableOrder />
            }}
            tableLayout='fixed'
          />
        </Spin>
      </div>
    </div>
  )

  const pagination = useMemo(
    () => ({
      position: ['none', 'bottomCenter'] as any,
      current: page,
      pageSize: pageSize,
      total: data?.total,
      onChange: (page: number, pageSize: number) => {
        setPage(page)
        setPageSize(pageSize)
      }
    }),
    [page, pageSize, data?.total]
  )

  const renderPagination = () => (
    <div className='mt-4 flex w-full justify-between'>
      <div />
      <Pagination {...pagination} showSizeChanger={false} />
      <Select defaultValue={10} options={pageSizeOptions} onChange={value => setPageSize(value)} />
    </div>
  )

  // region return
  return (
    <div className='size-full bg-bg-surface-secondary p-6'>
      <div className='flex items-center justify-between bg-bg-surface p-4'>
        <div className='flex items-center gap-3'>
          <div className='h-5 w-1 rounded-sm bg-icon-neutral-lighter' />
          <div className='text-headline-16 font-semibold text-text-neutral-strong'>Danh sách đơn hàng</div>
        </div>
        <Button
          type='primary'
          icon={<i className='onedx-add' />}
          onClick={() => {
            router.push('/order/create')
          }}
        >
          Tạo mới
        </Button>
      </div>

      <div className='mt-2 bg-bg-surface p-4'>
        <div className='mb-4 flex items-center gap-2'>
          <SettingInput
            placeholder='Tìm kiếm theo mã, tên khách hàng'
            styles={{ width: '100%' }}
            checked={checkedFilter}
            setChecked={setCheckedFilter}
            onKeyDown={event => {
              if (event.key === 'Enter') {
                event.preventDefault()
                setSearchValue(event.currentTarget.value?.trim())
                setPage(1)
              }
            }}
            checkBoxOptions={quickSearchOptions}
            size='small'
          />

          <MultiCheckboxSelect
            optionFilter={stepOptions}
            placeholder='Tiến trình đơn hàng'
            onChange={values => {
              setFilterParams({ ...filterParams, workflowStepIds: values.length > 0 ? values : null })
              setPage(1)
            }}
            value={selectedProgress}
          />

          <SelectFilterPopup
            setFilterParams={setFilterParams}
            filterOptions={listFilter}
            filterParams={filterParams}
            size='large'
            defaultFilter={{
              productItemTypes: 'ALL'
            }}
          />

          <EditTableColumn
            selectedColumns={selectedColumns}
            setSelectedColumns={setSelectedColumns}
            checkboxOptions={checkboxOptions}
          />
        </div>
        {renderTable()}
        {renderPagination()}
        {openSummaryDetail && (
          <OrderOverviewDetail
            openSummaryDetail={openSummaryDetail}
            setOpenSummaryDetail={setOpenSummaryDetail}
            orderCode={orderCode}
            portalType={portalType}
            subCode={subCode}
          />
        )}
      </div>
    </div>
  )
}
