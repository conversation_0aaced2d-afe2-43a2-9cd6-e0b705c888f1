import React from 'react'

import { usePathname } from 'next/navigation'

import classNames from 'classnames'

import { Avatar, Form, Table, Tooltip } from 'antd'

import { handleSrcImg } from '@/utils/string'

import { CUSTOMER_TYPE_NAME } from '@views/order-management/create/constant'

import { formatSpecialDigits } from '@/constants/common'

import SubscriptionDev from '@/models/SubscriptionDev'
import { usePageScroll } from '@/hooks/usePageScroll'

// Bảng danh sách KH
export const CustomerSelectTable = ({
  value = undefined,
  onChange = () => undefined
}: {
  value?: any
  onChange?: (value: any) => void
}) => {
  // Thông tin đường dẫn
  const portalType = usePathname()?.includes('partner-portal') ? 'dev' : 'admin'

  // Giá trị bộ lọc KH
  const filters = Form.useWatch('filters')

  // Danh sách khách hàng
  const { content, isFetching, onScroll } = usePageScroll(
    ['getListCustomerPopup'],
    SubscriptionDev.getListCustomerPopup,
    {
      portalType,
      value: formatSpecialDigits(filters?.value),
      customerType: filters?.customerType,
      provinceIds: filters?.provinceIds
    },
    { pageSize: 20 }
  )

  // Danh sách cột của bảng
  const columns = [
    {
      title: 'STT',
      dataIndex: 'key',
      render: (value: any, record: any, index: number) => index + 1,
      width: 75
    },
    {
      title: 'Tên khách hàng',
      dataIndex: 'companyName',
      width: 225,
      render: (value: string, data: any) => (
        <div className='flex items-center gap-2'>
          {data?.avatar ? (
            <Avatar className='size-7' src={handleSrcImg(data.avatar)} />
          ) : (
            <div className='flex size-7 items-center justify-center rounded-full bg-[#CDE4FE] font-medium text-primary'>
              {(data?.customerType === 'CN' ? (data?.lastName || data?.firstName)?.[0] : value?.[0]) || '?'}
            </div>
          )}
          <Tooltip
            placement='topLeft'
            title={data?.customerType === 'CN' ? `${data?.firstName} ${data?.lastName}` : value}
          >
            <div className='line-clamp-1 flex-1'>
              {data?.customerType === 'CN' ? `${data?.firstName} ${data?.lastName}` : value}
            </div>
          </Tooltip>
        </div>
      )
    },
    {
      title: 'Đối tượng khách hàng',
      dataIndex: 'customerType',
      width: 200,
      render: (value: string) => CUSTOMER_TYPE_NAME[value]
    },
    {
      title: 'Số chứng thực',
      dataIndex: 'tin',
      render: (value: string, data: any) => (data?.customerType === 'CN' ? data?.repPersonalCertNumber : value)
    },
    {
      title: 'Tỉnh thành',
      dataIndex: 'provinceName',
      width: 200
    }
  ]

  return (
    <Table
      columns={columns}
      pagination={false}
      onScroll={onScroll}
      loading={isFetching}
      dataSource={content}
      scroll={{ x: 900, y: 487 }}
      rowSelection={{ type: 'radio', onSelect: onChange, selectedRowKeys: [value?.id] }}
      rowKey='id'
      locale={{
        emptyText: (
          <div className='flex size-full flex-col items-center justify-center py-24'>
            <i className='onedx-empty-table-order size-24' />
            <div className='font-semibold text-black'>Danh sách trống</div>
            <div className='text-xs text-gray-700'>Không có dữ liệu khách hàng</div>
          </div>
        )
      }}
      className={classNames(
        'w-full rounded-lg border border-solid border-neutral-200 overflow-hidden',
        '[&_.ant-table-thead>tr>th]:!bg-bgSurfaceSecondary',
        '[&_.ant-table-thead>tr>th]:!text-text-neutral-light',
        '[&_.ant-table-thead>tr>th]:!text-xs',
        '[&_.ant-table-thead>tr>th]:!font-semibold',
        '[&_.ant-table-tbody>tr:hover>td]:!bg-bgSurfaceSecondary'
      )}
    />
  )
}
