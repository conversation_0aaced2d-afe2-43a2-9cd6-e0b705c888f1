import React from 'react'

import { usePathname, useSearchParams } from 'next/navigation'

import { Form, Spin } from 'antd'

import { cloneDeep } from 'lodash'

import { formatSpecialDigits } from '@/constants/common'
import { usePageScroll } from '@/hooks/usePageScroll'
import ShoppingCart from '@/models/ShoppingCart'
import { FORMAT_CART_SELECTION } from '@views/order-management/create/convert'

import { SubscriptionSelectService } from './SubscriptionSelectService'

// Danh sách SPDV có thể chọn
export const SubscriptionSelectList = ({
  value = undefined,
  onChange = () => undefined
}: {
  value?: any
  onChange?: (value: any) => void
}) => {
  // Giá trị bộ lọc KH
  const filters = Form.useWatch('filters')

  // Thông tin đường dẫn
  const portalType = usePathname()?.includes('partner-portal') ? 1 : 0

  // Thông tin tham số truyền theo URL
  const searchParams = useSearchParams()

  // Đối tượng KH
  const lstCustomerType = searchParams.get('customerType')

  // Id của KH
  const customerId = searchParams.get('customerId')

  // Loại của sản phẩm đã chọn đầu tiên
  const selectedProductType = value?.length
    ? value?.[0]?.productType === 'PACKAGE_BUNDLING'
      ? 'PACKAGE_BUNDLING'
      : 'PHYSICAL_PRODUCT'
    : undefined

  // Gọi API lấy danh sách SPDV
  const { content, isFetching, onScroll } = usePageScroll(
    ['getSubscriptionServicesAdmin', selectedProductType],
    ShoppingCart.getSubscriptionServicesAdmin,
    {
      portalType,
      lstCustomerType,
      customerId,
      searchProduct: 1,
      searchPricing: 1,
      // Lọc theo loại sản phẩm, ưu tiên lọc theo loại của sản phẩm đã chọn đầu tiên, nếu không có sản phẩm đã chọn thì lọc theo giá trị bộ lọc bên ngoài
      popupProductTypeEnums: selectedProductType ||
        filters?.popupProductTypeEnums || ['PHYSICAL_PRODUCT', 'PACKAGE_BUNDLING'],
      value: formatSpecialDigits(filters?.value)
    },
    {
      pageSize: 20,
      enabled: !!customerId,
      keepPreviousData: true
    }
  )

  // Xử lý chọn SPDV
  const selectSubscription = (serviceUniqueId: string, { forms }: any) => {
    // Dữ liệu các SPDV đã chọn trước đó
    const selectedService = cloneDeep(value || [])

    // Dữ liệu SPDV vừa chọn
    const service = forms?.[String(serviceUniqueId)]?.getFieldsValue(true)

    // Tổng hợp lại danh sách SPDV được chọn mới
    const newSelectedService = FORMAT_CART_SELECTION(selectedService, service, customerId)

    // Truyền danh sách SPDV được chọn mới ra ngoài
    onChange(newSelectedService)
  }

  return (
    <div className='w-full'>
      <div className='rounded-t-xl border-b border-solid border-neutral-200 bg-neutral-100 px-4 py-3 text-xs font-semibold'>
        Tên sản phẩm dịch vụ
      </div>
      <Spin spinning={isFetching}>
        <Form.Provider onFormFinish={selectSubscription}>
          <div className='beauty-scroll h-[490px] overflow-y-scroll' onScroll={onScroll}>
            {content?.length ? (
              // Trường hợp có dữ liệu SPDV trả về
              content.map((service: any) => (
                // Thẻ SPDV và danh sách gói có thể chọn
                <SubscriptionSelectService key={service?.serviceUniqueId} service={service} />
              ))
            ) : (
              // Trường hợp không có dữ liệu SPDV trả về
              <div className='flex size-full flex-col items-center justify-center'>
                <i className='onedx-empty-table-order size-24' />
                <div className='font-semibold text-black'>Danh sách trống</div>
                <div className='text-xs text-gray-700'>Không có dữ liệu sản phẩm</div>
              </div>
            )}
          </div>
        </Form.Provider>
      </Spin>
    </div>
  )
}
