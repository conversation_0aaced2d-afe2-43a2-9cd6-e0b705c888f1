import React, { useState } from 'react'

import { usePathname, useRouter, useSearchParams } from 'next/navigation'

import { useMutation, useQuery } from '@tanstack/react-query'

import { Button, Divider, Form, Modal, Spin } from 'antd'

import { FORMAT_CART_CREATE_DTO } from '@views/order-management/create/convert'

import smeSubscriptionInstance from '@/models/SmeSubscription'

import { FormItemText } from '@components/form'

import { formatCurrency } from '@/constants/commonFunction'

import ShoppingCart from '@/models/ShoppingCart'

// Component xử lý thanh toán thuê bao admin
export const SubscriptionPayment = () => {
  // Khai báo điều hướng
  const router = useRouter()

  // Thông tin đường dẫn
  const portalType = usePathname()?.includes('partner-portal') ? 'DEV' : 'ADMIN'

  // Id của KH
  const customerId = useSearchParams().get('customerId')

  // L<PERSON>y thông tin Form bọc ngoài
  const form = Form.useFormInstance()

  // Số lượng sản phẩm trong giỏ hàng
  const cartAmount = Form.useWatch('lstProduct')?.length || 0

  // Trạng thái hiển thị popup thanh toán
  const [checkoutInfo, setCheckoutInfo] = useState<any>()

  // Đóng popup thanh toán
  const closeCheckout = () => setCheckoutInfo(undefined)

  // Hàm lưu ảnh QR thanh toán
  const saveQR = () => {
    const link = document.createElement('a')

    link.download = 'QRCode.png'
    link.href = `data:image/png;base64,${checkoutInfo?.qrcodeImage}`
    link.click()
  }

  // Xử lý điều hướng sau khi thanh toán thành công
  const redirect = () => {
    const baseParams = new URLSearchParams({
      serviceId: checkoutInfo?.SERVICE_ID,
      planId: checkoutInfo?.PLAN_ID,
      orderId: checkoutInfo?.MERCHANT_ORDER_ID,
      paymentFrom: checkoutInfo?.SCREEN_TYPE,
      actionType: checkoutInfo?.ACTION_TYPE,
      subscriptionId: checkoutInfo?.SUBSCRIPTION_ID,
      billingId: checkoutInfo?.BILLING_ID,
      owner: checkoutInfo?.SERVICE_OWNER,
      cartCodeSub: checkoutInfo?.SERVICE_OWNER,
      cartCodeBill: checkoutInfo?.CART_CODE_BILL,
      subCode: checkoutInfo?.SUBSCRIPTION_CODE,
      isOn: checkoutInfo?.isON,
      serviceName: checkoutInfo?.SERVICE_NAME,
      lstSubId: checkoutInfo?.lstSubId ? checkoutInfo?.lstSubId.join(',') : null
    })

    router.push(`/sme-portal/payment-success?${baseParams.toString()}`)
  }

  // API kiểm tra trạng thái thanh toán (gọi mỗi giây kể từ khi có QR trả về)
  useQuery({
    enabled: !!checkoutInfo?.billId && checkoutInfo?.expirationTime > 0 && !checkoutInfo?.status,
    refetchInterval: 1000,
    refetchIntervalInBackground: true,
    queryKey: ['checkOrderQr', checkoutInfo?.billId?.[0]],
    queryFn: async () => {
      // Giảm thời gian hết hạn của popup thanh toán đi 1 giây
      setCheckoutInfo((prev: any) => ({ ...prev, expirationTime: prev?.expirationTime - 1 }))
      // Gọi API kiểm tra trạng thái thanh toán
      const res: any = await smeSubscriptionInstance.checkOrderQr(checkoutInfo?.billId?.[0])

      // Trường hợp thanh toán thành công
      if (res?.STATUS === 'SUCCESS') {
        setCheckoutInfo({ ...res, status: res?.STATUS })
        redirect()
      }
      // Trường hợp thanh toán thất bại
      else if (['FAIL', 'NOT_FOUND'].includes(res?.STATUS)) {
        setCheckoutInfo({ status: res?.STATUS })
      }
    }
  })

  // API tạo đơn hàng
  const checkoutCartAPI = useMutation({
    mutationFn: () =>
      form.validateFields().then(formData =>
        ShoppingCart.addCartSubscriptionAdmin(portalType, FORMAT_CART_CREATE_DTO(formData, customerId)).then(
          (res: any) => {
            // Trường hợp có mã QR trả về thì hiển thị popup thanh toán
            if (res?.qrcodeImage) setCheckoutInfo({ ...res, expirationTime: res?.expirationTime * 60 })
            // Trường hợp có URL trả về thì điều hướng theo URL thanh toán
            else if (res?.redirectURL) window.parent.postMessage({ url: res?.redirectURL, type: 'redirect' }, '*')
          }
        )
      )
  })

  // Hàm tạo đơn hàng
  const checkoutCart = () => checkoutCartAPI.mutate()

  // Hiển thị thời gian đếm ngược đến khi hết hạn QR dưới dạng string
  const expirationTimeString =
    String((checkoutInfo?.expirationTime - (checkoutInfo?.expirationTime % 60)) / 60) +
    ':' +
    String(checkoutInfo?.expirationTime % 60 > 10 ? '' : '0') +
    String(checkoutInfo?.expirationTime % 60)

  // Trạng thái mã QR đã hết hạn
  const expired = !checkoutInfo?.expirationTime

  return (
    <>
      <Button
        type='primary'
        htmlType='submit'
        disabled={!cartAmount}
        onClick={checkoutCart}
        loading={checkoutCartAPI.isPending}
      >
        Tạo
      </Button>
      <Modal
        centered
        width={!checkoutInfo?.status ? 680 : 400}
        open={!!checkoutInfo}
        onCancel={closeCheckout}
        maskClosable={false}
        footer={null}
        title={
          !checkoutInfo?.status ? (
            // Trường hợp đang chờ thanh toán
            'Thanh toán bằng QR Code'
          ) : checkoutInfo?.status === 'SUCCESS' ? (
            // Trường hợp thanh toán thành công
            <div className='flex size-10 items-center justify-center rounded-full bg-bg-success-light'>
              <i className='onedx-check size-5 text-text-success-strong' />
            </div>
          ) : (
            // Trường hợp thanh toán thất bại
            <div className='flex size-10 items-center justify-center rounded-full bg-bg-error-light'>
              <i className='onedx-warning size-5 text-text-error-strong' />
            </div>
          )
        }
      >
        {!checkoutInfo?.status ? (
          // Trường hợp đang chờ thanh toán
          <>
            <div className='flex size-full items-start py-5'>
              <div className='mr-6 w-[260px] space-y-3 border-r border-solid border-gray-alpha-3 pr-6'>
                <Spin spinning={checkoutCartAPI.isPending}>
                  <div className='relative flex w-full items-center justify-center'>
                    <img src={`data:image/png;base64,${checkoutInfo?.qrcodeImage}`} alt='' className='w-full' />
                    {expired && (
                      <div className='absolute rounded bg-white p-1'>
                        <Button size='small' type='primary' onClick={checkoutCart}>
                          Lấy mã QR mới
                        </Button>
                      </div>
                    )}
                  </div>
                </Spin>
                {expired ? (
                  <div className='text-center text-body-14 font-medium text-gray-6'>Mã QR đã hết hạn</div>
                ) : (
                  <div className='flex items-center justify-center gap-2'>
                    <div className='text-body-14 font-medium text-gray-6'>Mã QR hết hạn sau:</div>
                    <div className='text-body-14 font-medium text-red-6'>{expirationTimeString}</div>
                  </div>
                )}
              </div>
              <div className='flex-1 space-y-1'>
                <div className='py-2 text-headline-18 font-medium'>Thanh toán</div>
                <div className='flex justify-between py-2 text-body-14 font-normal'>
                  <div>Tổng đơn hàng ({cartAmount})</div>
                  <div className='flex'>
                    <div className='text-caption-12 leading-4'>₫</div>
                    <FormItemText
                      name='totalAmountPreTaxFeeFinal'
                      value={(value: any) => <div className='leading-5'>{formatCurrency(value)}</div>}
                    />
                  </div>
                </div>
                <FormItemText
                  layout='horizontal'
                  label='Thuế'
                  name='taxObject'
                  value={(value: any) => formatCurrency(value?.totalAmount)}
                />
                <FormItemText
                  layout='horizontal'
                  label='Phí lắp đặt'
                  name='feeObject'
                  value={(value: any) => formatCurrency(value?.totalAmount)}
                />
                <FormItemText layout='horizontal' label='Phí vận chuyển' name='shippingFee' value={formatCurrency} />
                <Divider
                  style={{
                    margin: '16px 0',
                    borderTop: 'none',
                    height: '1px',
                    background:
                      'repeating-linear-gradient(to right, rgba(2, 23, 60, 0.09) 0, rgba(2, 23, 60, 0.09) 8px, transparent 8px, transparent 16px)'
                  }}
                />
                <div className='flex justify-between text-headline-18 font-semibold'>
                  <div>Tổng thanh toán</div>
                  <div className='flex text-green-7'>
                    <div className='text-caption-12 leading-4'>₫</div>
                    <FormItemText
                      name='totalAmountAfterRefund'
                      value={(value: any) => <div className='leading-6 text-green-7'>{formatCurrency(value)}</div>}
                    />
                  </div>
                </div>
              </div>
            </div>
            <div className='flex w-full justify-end gap-4'>
              <Button type='primary' onClick={saveQR} disabled={expired}>
                Lưu mã QR
              </Button>
              <Button onClick={closeCheckout}>Huỷ</Button>
            </div>
          </>
        ) : checkoutInfo?.status === 'SUCCESS' ? (
          // Trường hợp thanh toán thành công
          <>
            <div className='size-full py-5'>
              <div className='mb-2 text-headline-16 font-semibold text-text-neutral-strong'>Thanh toán thành công</div>
              <div className='text-body-14 font-normal text-text-neutral-light'>
                Chúc mừng bạn đã thanh toán thành công!
              </div>
            </div>
            <div className='flex w-full justify-end gap-4'>
              <Button type='primary' onClick={redirect}>
                Xem chi tiết thuê bao
              </Button>
            </div>
          </>
        ) : (
          // Trường hợp thanh toán thất bại
          <>
            <div className='size-full py-5'>
              <div className='mb-2 text-headline-16 font-semibold text-text-neutral-strong'>Thanh toán thất bại</div>
              <div className='text-body-14 font-normal text-text-neutral-light'>Bạn có muốn thử lại không?</div>
            </div>
            <div className='flex w-full justify-end gap-4'>
              <Button type='primary' onClick={checkoutCart} loading={checkoutCartAPI.isPending}>
                Thử lại
              </Button>
              <Button onClick={closeCheckout}>Huỷ</Button>
            </div>
          </>
        )}
      </Modal>
    </>
  )
}
