import React, { useState } from 'react'

import { usePathname, useSearchParams } from 'next/navigation'

import { useMutation, useQuery } from '@tanstack/react-query'

import { Button, Checkbox, Collapse, DatePicker, Form, Input, Modal, Radio, Select, Tabs } from 'antd'

import { message } from '@components/notification'

import { FORMAT_CREATE_COUPON_DTO } from '@views/order-management/create/convert'

import Bundling from '@/models/BOS/Bundling'

import Solution from '@/models/iot/Solution'

import { normalizeNumberRange, validateRequireInput } from '@/validator'

// Popup tạo nhanh CTKM
export const SubscriptionCreateCoupon = ({
  value = undefined,
  onChange = () => undefined
}: {
  value?: any
  onChange?: (value?: any) => any
}) => {
  // Khai báo Form tổng
  const [form] = Form.useForm()

  // Thông tin đường dẫn
  const portalType = usePathname()?.includes('partner-portal') ? 'dev' : 'admin'

  // Thông tin đối tượng khách hàng
  const customerType = useSearchParams().get('customerType')

  // Thông tin ngày bắt đầu đã chọn
  const startDate = Form.useWatch('startDate', form)

  // Thông tin ngày kết thúc đã chọn
  const endDate = Form.useWatch('endDate', form)

  // Trạng thái hiển thị của popup
  const [opened, open] = useState(false)

  // Thay đổi trạng thái hiển thị của popup
  const toggleOpen = () => {
    open(prev => !prev)
  }

  // Loại khuyến mại là tiền mặt
  const discountTypePrice = Form.useWatch('discountType', form) === 'PRICE'

  // Gọi API lấy thông tin mã khuyến mại khi mở popup
  const { data: couponCode } = useQuery({
    queryKey: ['getPromotionCode'],
    queryFn: () => Solution.getPromotionCode('COUPON'),
    enabled: opened
  })

  // API tạo khuyến mại
  const createCouponAPI = useMutation({
    mutationKey: ['createCoupon'],
    mutationFn: (couponData: any) =>
      Bundling.createCouponIoT(FORMAT_CREATE_COUPON_DTO(value, couponData), portalType)
        .then((res: any) => onChange({ id: res?.id, couponName: 'Đang xử lý...' }))
        .then(toggleOpen)
        .catch(() => message.error('Tạo khuyến mại thất bại'))
  })

  return (
    <>
      <Button color='primary' variant='outlined' onClick={toggleOpen}>
        Tạo mới CTKM
      </Button>
      <Modal
        width={1200}
        open={opened}
        destroyOnClose
        title='Tạo CTKM'
        maskClosable={false}
        onCancel={toggleOpen}
        footer={null}
      >
        <Form
          form={form}
          preserve={false}
          layout='vertical'
          onFinish={createCouponAPI.mutate}
          disabled={createCouponAPI.isPending}
        >
          <div className='-mx-6 border-y border-solid border-neutral-200 p-6'>
            <Collapse
              ghost
              className='grid w-full grid-cols-2 gap-4'
              expandIconPosition='end'
              defaultActiveKey={['status', 'customer', 'info']}
              items={[
                {
                  key: 'status',
                  className: 'rounded overflow-hidden',
                  classNames: {
                    header: 'rounded-none bg-[#E6F1FE] px-4 py-3 text-primary items-center',
                    body: 'rounded-none bg-[#02173C05] px-4 py-3'
                  },
                  label: 'Trạng thái hiển thị mã khuyến mãi',
                  children: (
                    <Form.Item name='visibleStatus' initialValue={1} noStyle>
                      <Radio.Group
                        className='grid w-full grid-cols-2'
                        options={[
                          { label: 'Công khai', value: 1 },
                          { label: 'Ẩn', value: 0 }
                        ]}
                      />
                    </Form.Item>
                  )
                },
                {
                  key: 'customer',
                  className: 'rounded overflow-hidden',
                  classNames: {
                    header: 'rounded-none bg-[#E6F1FE] px-4 py-3 text-primary items-center',
                    body: 'rounded-none bg-[#02173C05] px-4 py-3'
                  },
                  label: 'Đối tượng khách hàng',
                  children: (
                    <Form.Item name='customerTypeCode' initialValue={customerType} noStyle>
                      <Checkbox.Group
                        className='grid w-full grid-cols-3'
                        options={[
                          { label: 'Doanh nghiệp', value: 'ENTERPRISE' },
                          { label: 'Hộ kinh doanh', value: 'HOUSE_HOLD' },
                          { label: 'Cá nhân', value: 'PERSONAL' }
                        ]}
                      />
                    </Form.Item>
                  )
                },
                {
                  key: 'info',
                  className: 'rounded overflow-hidden col-span-2',
                  classNames: {
                    header: 'rounded-none bg-[#E6F1FE] px-4 py-3 text-primary items-center',
                    body: 'rounded-none bg-[#02173C05] p-0 pt-3'
                  },
                  label: 'Thông tin chung',
                  children: (
                    <div className='w-full'>
                      <div className='grid w-full grid-cols-2 items-end gap-4 px-4'>
                        <Form.Item
                          label={<div className='text-xs font-medium text-black'>Tên chương trình khuyến mãi</div>}
                          rules={[validateRequireInput('Tên CTKM không được bỏ trống')]}
                          name='name'
                        >
                          <Input placeholder='Nhập tên chương trình khuyến mãi' />
                        </Form.Item>
                        <Form.Item
                          preserve={false}
                          label={<div className='text-xs font-medium text-black'>Mã khuyến mại</div>}
                          initialValue={couponCode?.code}
                          key={couponCode?.code}
                          name='code'
                        >
                          <Input placeholder='Mã khuyến mại' disabled />
                        </Form.Item>
                      </div>
                      <div className='w-full bg-gray-12 px-4'>
                        <Form.Item name='promotionType' initialValue='DISCOUNT' noStyle>
                          <Tabs
                            defaultActiveKey='DISCOUNT'
                            renderTabBar={(props, TabBar) => (
                              <div className='flex items-center justify-between'>
                                <div className='border-l-2 border-solid border-yellow-6 pl-2 text-sm font-semibold leading-4 text-black'>
                                  Hình thức khuyến mại
                                </div>
                                <TabBar {...props} />
                              </div>
                            )}
                            items={[
                              {
                                key: 'DISCOUNT',
                                label: 'Chiết khấu',
                                children: (
                                  <div className='grid w-full auto-cols-[minmax(0,2fr)] grid-flow-col items-end gap-4'>
                                    <Form.Item
                                      label={<div className='text-xs font-medium text-black'>Chiết khấu theo</div>}
                                      rules={[validateRequireInput('Giá trị chiết khấu không được bỏ trống')]}
                                      normalize={normalizeNumberRange(1, discountTypePrice ? undefined : 100)}
                                      name='discountValue'
                                    >
                                      <Input
                                        maxLength={discountTypePrice ? 10 : 3}
                                        addonBefore={
                                          <Form.Item name='discountType' initialValue='PERCENT' noStyle>
                                            <Select
                                              className='min-w-[133px]'
                                              options={[
                                                { label: 'Số tiền (VNĐ)', value: 'PRICE' },
                                                { label: 'Phần trăm (%)', value: 'PERCENT' }
                                              ]}
                                            />
                                          </Form.Item>
                                        }
                                        placeholder='Nhập giá trị chiết khấu'
                                      />
                                    </Form.Item>
                                    {discountTypePrice && (
                                      <Form.Item
                                        label={<div className='text-xs font-medium text-black'>Số tiền tối đa</div>}
                                        normalize={normalizeNumberRange(1)}
                                        name='discountAmount'
                                        preserve={false}
                                      >
                                        <Input maxLength={10} addonBefore='VNĐ' placeholder='Nhập số tiền tối đa' />
                                      </Form.Item>
                                    )}
                                  </div>
                                )
                              },
                              { key: 'PRODUCT', label: 'Miễn phí theo sản phẩm' }
                            ]}
                          />
                        </Form.Item>
                      </div>
                      <div className='mt-5 grid w-full grid-cols-3 gap-4 bg-gray-12 px-4 pt-4'>
                        <div className='col-span-3 border-l-2 border-solid border-yellow-6 pl-2 text-sm font-semibold leading-4 text-black'>
                          Hiệu lực
                        </div>
                        <Form.Item
                          label={<div className='text-xs font-medium text-black'>Tổng số lượng khuyến mãi</div>}
                          normalize={normalizeNumberRange(0)}
                          name='maxUsed'
                        >
                          <Input maxLength={10} placeholder='Nhập tổng số lượng khuyến mãi' />
                        </Form.Item>
                        <Form.Item
                          label={<div className='text-xs font-medium text-black'>Ngày bắt đầu</div>}
                          name='startDate'
                        >
                          <DatePicker
                            className='w-full'
                            format='DD/MM/YYYY'
                            placeholder='Chọn ngày bắt đầu'
                            maxDate={endDate}
                            allowClear
                          />
                        </Form.Item>
                        <Form.Item
                          label={<div className='text-xs font-medium text-black'>Ngày kết thúc</div>}
                          name='endDate'
                        >
                          <DatePicker
                            className='w-full'
                            format='DD/MM/YYYY'
                            placeholder='Chọn ngày kết thúc'
                            minDate={startDate}
                            allowClear
                          />
                        </Form.Item>
                      </div>
                    </div>
                  )
                }
              ]}
            />
          </div>
          <div className='flex justify-end gap-3 pt-5'>
            <Button color='primary' variant='outlined' onClick={toggleOpen}>
              Hủy
            </Button>
            <Button htmlType='submit' type='primary'>
              Xác nhận
            </Button>
          </div>
        </Form>
      </Modal>
    </>
  )
}
