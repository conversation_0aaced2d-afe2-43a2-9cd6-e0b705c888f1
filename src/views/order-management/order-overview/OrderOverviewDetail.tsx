import { useMemo, useState } from 'react'

import { useRouter } from 'next/navigation'

import { useQuery } from '@tanstack/react-query'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Drawer, Spin } from 'antd'

import { WorkflowTag } from '@views/order/detail/tabs/common/components/WorkflowTag'

import OrderManagement from '@/models/OrderManagement'
import { isCartCode } from '../constants'
import { handleSrcImg } from '@/utils/string'
import { CustomCollapse, ProcessBar } from '../common'
import { CYCLE_TYPE } from '@/constants/convert'
import DetailCommon from '@/views/payment/components/product-information/DetailCommon'
import { FormattedCurrency } from '@/views/order/detail/tabs/common/components/FormattedCurrency'

interface OrderSummaryDetailProps {
  orderCode?: any
  openSummaryDetail: boolean
  setOpenSummaryDetail: any
  portalType: 'admin' | 'dev'
  subCode?: string | null
}

export const OrderOverviewDetail = ({
  orderCode,
  openSummaryDetail,
  setOpenSummaryDetail,
  portalType,
  subCode
}: OrderSummaryDetailProps) => {
  const router = useRouter()

  const { data: dataOrderOverview, isLoading } = useQuery({
    queryKey: ['dataOrderOverview', orderCode],
    queryFn: async () => {
      const res = isCartCode(orderCode)
        ? await OrderManagement.getOrderOverviewCart(orderCode, portalType)
        : await OrderManagement.getOrderOverview(orderCode, portalType)

      return res
    },
    enabled: !!orderCode
  })

  const { data: orderInfo } = useQuery({
    queryKey: ['getOrderInfo', orderCode],
    queryFn: async () => {
      const res = isCartCode(orderCode)
        ? await OrderManagement.getDetailOrderCart(orderCode, portalType.toUpperCase())
        : await OrderManagement.getDetailOrder(orderCode, portalType.toUpperCase())

      res.isCartCode = isCartCode(orderCode)

      return res
    },
    enabled: !!orderCode
  })

  const cartInfo = useMemo(() => {
    return orderInfo?.shoppingCartFormulaResDTO
  }, [orderInfo])

  // State để quản lý trạng thái ẩn/hiện của AddOnInfo cho từng service
  const [expandedServices, setExpandedServices] = useState<{ [key: string]: boolean }>({})

  const toggleAddOnInfo = (serviceId: string) => {
    setExpandedServices(prev => ({
      ...prev,
      [serviceId]: !prev[serviceId]
    }))
  }

  const currentStep = useMemo(() => {
    const step = dataOrderOverview?.lstWorkflowStep?.find((step: any) => step?.id === dataOrderOverview?.workflowStepId)

    return step?.index || 1
  }, [dataOrderOverview])

  // header title
  const renderHeader = (title: string) => (
    <div className='mb-3 flex items-center gap-3'>
      <div className='h-5 w-1 rounded-sm bg-icon-warning-default' />
      <div className='text-headline-16 font-semibold text-text-neutral-strong'>{title}</div>
    </div>
  )

  const renderPrice = () => {
    return (
      <>
        <DetailCommon
          title='Phí'
          info={!orderInfo?.isCartCode ? cartInfo?.lstSubscriptionResponse[0]?.taxObject : cartInfo?.taxObject}
          type='Fees'
        />
        <DetailCommon
          title='Thuế'
          info={!orderInfo?.isCartCode ? cartInfo?.lstSubscriptionResponse[0]?.feeObject : cartInfo?.feeObject}
          type='Taxes'
        />
        <DetailCommon
          title='Khuyến mại'
          info={!orderInfo?.isCartCode ? cartInfo?.lstSubscriptionResponse[0]?.couponObject : cartInfo?.couponObject}
          type='Coupons'
        />
      </>
    )
  }

  // thông tin khách hàng
  const renderCustomerInfo = (customerDetail: any) => (
    <div className='mb-5'>
      {renderHeader('Thông tin khách hàng')}
      <div className='rounded-lg bg-bg-neutral-lightest px-4 py-3'>
        <div className='mb-2 flex items-center gap-2 text-body-14 font-semibold text-text-neutral-strong'>
          <div>{customerDetail?.customerName}</div>
          <div className='h-3 w-px rounded-sm bg-border-neutral-medium' />
          <div>{customerDetail?.phoneNumber}</div>
        </div>
        <div className='text-body-14 font-normal text-text-neutral-medium'>{customerDetail?.address}</div>
      </div>
    </div>
  )

  // thông tin add on
  const renderAddOnInfo = (item: any) => {
    return (
      <>
        <Divider
          style={{
            margin: '8px 0',
            borderTop: 'none',
            height: '1px',
            background:
              'repeating-linear-gradient(to right, #02173C17 0, #02173C17 10px, transparent 10px, transparent 20px)'
          }}
        />
        {Array.isArray(item?.addons) &&
          item.addons.map((addOn: any, index: any) => (
            <div key={index} className='flex flex-col gap-2'>
              <div className='flex flex-col gap-2'>
                <div className='flex items-center justify-between text-body-14 font-normal'>
                  <div>{addOn?.addonName}</div>
                  <FormattedCurrency value={addOn?.originPrice} />
                </div>
              </div>
            </div>
          ))}
      </>
    )
  }

  const renderItemDetail = (listDevice: any) =>
    listDevice?.map((item: any, index: any) => {
      const isExpanded = expandedServices[index]

      return (
        <>
          <div
            key={index}
            className='rounded-lg border border-solid border-border-neutral-lighter bg-bg-surface px-3 py-2'
          >
            <div className='flex gap-2'>
              <div className='size-10 rounded-lg bg-bg-neutral-lightest'>
                <img
                  src={handleSrcImg(item?.image)}
                  // alt={sub?.serviceName}
                  className='size-10 rounded-lg object-cover'
                />
              </div>
              <div className='flex w-full flex-col gap-1'>
                <div className='text-body-14 font-medium text-text-neutral-strong'>{item?.serviceName}</div>
                <div className='text-caption-12 font-medium text-text-neutral-medium'>{item?.pricingName}</div>
                <div className='flex items-center justify-between text-caption-12 font-medium text-text-neutral-medium'>
                  <div>
                    {item?.isOneTime === 0 ? (
                      <div>Thanh toán 1 lần</div>
                    ) : (
                      <div className='flex items-center gap-1'>
                        <div>{`${item?.variantName ? item?.variantName : `${item?.paymentCycle} ${CYCLE_TYPE[item?.cycleType]}`}`}</div>
                        <div hidden={item?.numberOfCycles == null}>
                          {item?.numberOfCycles === -1 || item?.numberOfCycles === null
                            ? ', Không giới hạn'
                            : `, ${item?.numberOfCycles} chu kỳ`}
                        </div>
                      </div>
                    )}
                  </div>
                  <div className='text-body-14 font-normal text-text-neutral-strong'>
                    <FormattedCurrency value={item?.totalAmount} />
                  </div>
                </div>
              </div>
            </div>

            {item?.addons?.length > 0 && isExpanded && renderAddOnInfo(item)}

            <Divider
              style={{
                margin: '8px 0',
                borderTop: 'none',
                height: '1px',
                background:
                  'repeating-linear-gradient(to right, #02173C17 0, #02173C17 10px, transparent 10px, transparent 20px)'
              }}
            />
            <div className='flex justify-between rounded-b-lg'>
              <div
                className='cursor-pointer text-caption-12 font-medium text-text-primary-default'
                onClick={() =>
                  router.push(
                    `/order/detail/${orderCode}?tab=PROGRESS&openDrawer=true&serviceId=${item?.serviceId || ''}`
                  )
                }
              >
                Xem tiến trình giao hàng
              </div>
              <div className='flex items-center'>
                <div title={item?.progressDetail?.finalStateDisplayName}>
                  <WorkflowTag
                    status={item?.progressDetail?.finalStateDisplayName}
                    bgColor={item?.progressDetail?.finalStateColorCode}
                  />
                </div>
                {item?.addons?.length > 0 && (
                  <i
                    className={`onedx-chevron-up ml-2 size-5 cursor-pointer text-icon-neutral-medium transition-transform ${!isExpanded ? 'rotate-180' : ''}`}
                    onClick={() => toggleAddOnInfo(index)}
                  />
                )}
              </div>
            </div>
          </div>
        </>
      )
    })

  const renderBundlingDetail = (item: any) => {
    return (
      <div className='mb-3'>
        <CustomCollapse
          header={
            item?.solutionId ? (
              <>
                <div className='flex gap-2'>
                  <div className='size-12 rounded-lg bg-bg-neutral-lightest'>
                    <img
                      src={handleSrcImg(item?.image)}
                      // alt={item?.serviceName}
                      className='size-12 rounded-lg object-cover'
                    />
                  </div>
                  <div className='flex flex-col gap-1'>
                    <div className='text-title-16 font-semibold text-text-primary-strong-hover'>
                      {item?.packageName}
                    </div>
                    <div className='text-caption-12 font-normal text-text-neutral-medium'>{item?.providerName}</div>
                  </div>
                </div>
                <div className='rounded-b-lg'>
                  <Divider
                    style={{
                      margin: '12px 0',
                      height: '1px'
                    }}
                  />
                  <div className='flex justify-between text-body-14 font-semibold text-text-neutral-strong'>
                    <div>Giá sản phẩm</div>
                    <FormattedCurrency value={cartInfo?.totalAmountPreTaxFeeFinal} />
                  </div>
                  <div className='mt-2 text-caption-12 font-normal text-text-neutral-medium'>
                    {item?.lstItemDetail?.length} dịch vụ
                  </div>
                </div>
              </>
            ) : (
              <div className='flex gap-2'>
                <div className='size-12 rounded-lg bg-bg-neutral-lightest'>
                  <img
                    src={handleSrcImg(item?.image)}
                    // alt={item?.serviceName}
                    className='size-12 rounded-lg object-cover'
                  />
                </div>
                <div className='flex flex-col gap-1'>
                  <div className='text-title-16 font-semibold text-text-primary-strong-hover'>{item?.packageName}</div>
                  <div className='text-caption-12 font-normal text-text-neutral-medium'>{item?.providerName}</div>
                  <div className='text-caption-12 font-normal text-text-neutral-medium'>
                    {item?.lstItemDetail?.length} dịch vụ
                  </div>
                </div>
              </div>
            )
          }
        >
          <div className='flex flex-col gap-2'>{renderItemDetail(item?.lstItemDetail)}</div>
        </CustomCollapse>
        {!item?.solutionId && (
          <div className='rounded-b-lg bg-bg-neutral-lightest px-4'>
            <Divider
              style={{
                margin: '0 0',
                borderTop: 'none',
                height: '1px',
                background:
                  'repeating-linear-gradient(to right, #02173C17 0, #02173C17 10px, transparent 10px, transparent 20px)'
              }}
            />
            <div className='flex justify-between py-3 text-body-14 font-semibold text-text-neutral-strong'>
              <div>Giá sản phẩm</div>
              <FormattedCurrency value={cartInfo?.totalAmountPreTaxFeeFinal} />
            </div>
          </div>
        )}
      </div>
    )
  }

  const renderTotalPrice = () => {
    return (
      <>
        {renderPrice()}
        <div className='mx-4'>
          <Divider
            style={{
              margin: '10px 0',
              borderTop: 'none',
              height: '1px',
              background:
                'repeating-linear-gradient(to right, #02173C17 0, #02173C17 10px, transparent 10px, transparent 20px)'
            }}
          />
          <div className='flex justify-between text-body-14 font-semibold text-text-neutral-strong'>
            <div className='text-body-14 font-medium text-text-neutral-strong'>Thành tiền</div>
            <div className='text-headline-20 font-semibold text-text-success-strong'>
              <FormattedCurrency value={Math.round(orderInfo?.shoppingCartFormulaResDTO?.totalAmountAfterTaxFinal)} />
            </div>
          </div>
        </div>
      </>
    )
  }

  const onClose = () => {
    setOpenSummaryDetail(!openSummaryDetail)
  }

  return (
    <Drawer
      title={
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-x-4 text-headline-16 font-semibold text-text-neutral-strong'>
            <div className='flex size-10 items-center justify-center rounded-full bg-bg-primary-light'>
              <i className='onedx-shipping size-6 text-text-primary-default' />
            </div>
            {`Đơn hàng ${subCode}`}
          </div>
          <i className='onedx-close-icon size-5 text-icon-neutral-medium' onClick={onClose} />
        </div>
      }
      onClose={onClose}
      closable={false}
      width={590}
      placement='right'
      open={openSummaryDetail}
      footer={
        <div className='flex justify-end gap-2'>
          <Button onClick={onClose} className='border-border-primary-default text-text-primary-default'>
            Đóng
          </Button>
          <Button type='primary' onClick={() => router.push(`/order/detail/${orderCode}`)}>
            Xem chi tiết
          </Button>
        </div>
      }
    >
      <Spin spinning={isLoading}>
        {/* Thông tin khách hàng */}
        {renderCustomerInfo(dataOrderOverview?.customerDetail)}

        {/* Tiến trình đơn hàng */}
        <div className='mb-5'>
          {renderHeader('Tiến trình đơn hàng')}
          {dataOrderOverview?.lstWorkflowStep?.length > 0 && (
            <ProcessBar steps={dataOrderOverview?.lstWorkflowStep} currentStep={currentStep} />
          )}
        </div>

        {/* Thông tin đơn hàng */}
        <div>
          {renderHeader('Thông tin đơn hàng')}
          {dataOrderOverview?.bundlingDetail && renderBundlingDetail(dataOrderOverview?.bundlingDetail)}
          {dataOrderOverview?.lstDeviceDetail && (
            <div className='flex flex-col gap-2'>{renderItemDetail(dataOrderOverview?.lstDeviceDetail)}</div>
          )}
        </div>

        {/* Thành tiền */}
        {renderTotalPrice()}
      </Spin>
    </Drawer>
  )
}
