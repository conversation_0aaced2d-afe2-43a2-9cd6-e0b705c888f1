import { handleSrcImg } from '@/utils/string'
import { WorkflowTag } from '@views/order/detail/tabs/common/components/WorkflowTag'
import { CustomTable } from './style'
import { FormattedCurrency } from '@/views/order/detail/tabs/common/components/FormattedCurrency'
import { CYCLE_TYPE } from '@/constants/convert'

const renderHeader = (title: string, count?: number) => (
  <div className='flex items-center gap-3'>
    <div className='h-5 w-1 rounded-sm bg-icon-warning-default' />
    <div className='text-headline-16 font-medium text-text-neutral-strong'>
      {title} {count && `(${count})`}
    </div>
  </div>
)

export const OrderProductList = ({ orderItems = [] }: any) => {
  // Phân loại các items dựa trên classification
  const physicalDevices = orderItems.filter(
    (item: any) => item.classification === 'PHYSICAL' || item.classification === 'DIGITAL'
  )

  const services = orderItems.filter((item: any) => item.classification === 'SERVICE')

  const sections = [
    {
      title: 'Thiết bị vật lý',
      count: physicalDevices.length,
      data: physicalDevices.map((item: any) => ({
        ...item
      }))
    },
    {
      title: 'Gói dịch vụ đi kèm',
      count: services.length,
      data: services.map((item: any) => ({
        ...item
      }))
    }
  ]

  // Chỉ hiển thị các section có dữ liệu
  const sectionsToRender = sections.filter(section => section.data.length > 0)

  const columns = (section: any) => [
    {
      title: renderHeader(section.title, section.count),
      dataIndex: 'serviceName',
      key: 'serviceName',
      render: (serviceName: string, record: any) => (
        <div className='flex gap-2'>
          <div className='size-10 rounded-lg bg-bg-neutral-lightest'>
            <img
              src={handleSrcImg(record?.serviceIconUrl)}
              // alt={record?.serviceName}
              className='size-10 rounded-lg object-cover'
            />
          </div>
          <div className='flex w-full flex-col gap-1'>
            <div className='text-body-14 font-medium text-text-neutral-strong'>{serviceName}</div>
            <div className='flex items-center gap-1 truncate text-caption-12 font-medium text-text-neutral-medium'>
              <span>{record?.isOneTime === 0 ? 'Gói cước áp dụng 1 lần' : record?.pricingName}</span>
              {record?.isOneTime !== 0 && (
                <>
                  (<span>{`${record?.paymentCycle} ${CYCLE_TYPE[record?.cycleType]}`}</span>
                  {record?.numberOfCycles != null && (
                    <span>
                      {record?.numberOfCycles === -1 || record?.numberOfCycles === null
                        ? ', Không giới hạn'
                        : `, ${record?.numberOfCycles} chu kỳ`}
                    </span>
                  )}
                  )
                </>
              )}
            </div>
            <div className='text-caption-12 font-medium text-text-neutral-medium'>{record?.variantName}</div>
          </div>
        </div>
      ),
      width: 400
    },
    {
      title: <div className='text-body-14 font-normal text-text-neutral-light'>SKU</div>,
      dataIndex: 'sku',
      key: 'sku',
      width: 100,
      render: (sku: string) => <div className='truncate'>{sku}</div>
    },
    {
      title: <div className='text-body-14 font-normal text-text-neutral-light'>Đơn giá</div>,
      dataIndex: 'unitPrice',
      key: 'unitPrice',
      width: 150,
      render: (unitPrice: number) => (
        <div className='flex items-center gap-1'>
          <FormattedCurrency value={unitPrice} />
        </div>
      )
    },
    {
      title: <div className='text-body-14 font-normal text-text-neutral-light'>SL</div>,
      dataIndex: 'quantity',
      key: 'quantity',
      width: 50,
      render: (quantity: number) => <div>{quantity}</div>
    },
    {
      title: <div className='text-body-14 font-normal text-text-neutral-light'>Trạng thái</div>,
      dataIndex: 'stateDisplayName',
      key: 'stateDisplayName',
      render: (value: string, record: any) => (
        <div title={value}>
          <WorkflowTag status={value} bgColor={record?.stateColorCode} />
        </div>
      ),
      width: 200
    }
  ]

  return (
    <div className='flex max-h-[500px] w-[888px] flex-col gap-2 overflow-y-auto'>
      {sectionsToRender.map(section => (
        <div key={section.title}>
          <CustomTable
            columns={columns(section)}
            dataSource={section.data}
            pagination={false}
            className='w-full'
            scroll={{ x: 500 }}
          />
        </div>
      ))}
    </div>
  )
}
