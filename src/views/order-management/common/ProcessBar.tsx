import { useEffect, useRef, useState } from 'react'

export interface Step {
  index: number
  name: string
  icon: string
}

interface Props {
  currentStep: number
  steps: Step[]
}

export const ProcessBar = ({ currentStep, steps }: Props) => {
  const scrollRef = useRef<HTMLDivElement>(null)
  const [canScrollLeft, setCanScrollLeft] = useState(false)
  const [canScrollRight, setCanScrollRight] = useState(false)
  const visibleSteps = steps || []

  useEffect(() => {
    const checkScroll = () => {
      const el = scrollRef.current

      if (!el) return
      setCanScrollLeft(el.scrollLeft > 0)
      setCanScrollRight(el.scrollLeft + el.clientWidth < el.scrollWidth)
    }

    checkScroll()
    const el = scrollRef.current

    if (el) {
      el.addEventListener('scroll', checkScroll)
      window.addEventListener('resize', checkScroll)
    }

    return () => {
      if (el) el.removeEventListener('scroll', checkScroll)
      window.removeEventListener('resize', checkScroll)
    }
  }, [])

  const handleScroll = (direction: 'left' | 'right') => {
    const el = scrollRef.current

    if (!el) return
    const scrollAmount = 220 // width of item + gap

    el.scrollBy({ left: direction === 'left' ? -scrollAmount : scrollAmount, behavior: 'smooth' })
  }

  return (
    <div className='relative mt-4'>
      {canScrollLeft && (
        <div
          className='absolute left-2 top-[35%] z-10 flex size-8 -translate-y-1/2 items-center justify-center rounded-full border border-border-neutral-lighter bg-white shadow'
          onClick={() => handleScroll('left')}
        >
          <i className='onedx-chevron-left size-5 text-primary' />
        </div>
      )}
      <div
        ref={scrollRef}
        className='scrollbar-hide flex items-center overflow-x-auto '
        style={{ scrollBehavior: 'smooth' }}
      >
        <div
          style={{ clipPath: 'polygon(0 0, 93% 0, 100% 50%, 93% 100%, 0 100%)', minWidth: 200 }}
          className={`flex items-center justify-center gap-2 self-stretch rounded-l-[8px] ${
            currentStep >= visibleSteps[0]?.index ? 'bg-green-6' : 'bg-bg-neutral-light'
          } flex-1 py-2 text-white`}
        >
          <i className={`${visibleSteps[0]?.icon} size-4`} />
          <div className='text-xs'>{visibleSteps[0]?.name}</div>
        </div>
        {visibleSteps.slice(1).map((step: Step) => (
          <div
            key={step.index}
            style={{
              clipPath:
                step.index === visibleSteps[visibleSteps.length - 1].index
                  ? 'polygon(0 0, 100% 0, 100% 100%, 0 100%, 7% 50%)'
                  : 'polygon(0 0, 93% 0, 100% 50%, 93% 100%, 0 100%, 7% 50%)',
              minWidth: 200
            }}
            className={`flex items-center justify-center gap-2 self-stretch border py-2 ${
              currentStep < step.index ? 'bg-bg-neutral-light' : 'bg-green-6'
            } text-white ${step.index === visibleSteps[visibleSteps.length - 1].index ? 'rounded-r-[8px]' : ''} flex-1`}
          >
            <i className={`${step?.icon} size-4`} />
            <div className='text-xs'>{step?.name}</div>
          </div>
        ))}
      </div>
      {canScrollRight && (
        <div
          className='absolute right-2 top-[35%] z-10 flex size-8 -translate-y-1/2 items-center justify-center rounded-full border border-border-neutral-lighter bg-white shadow'
          onClick={() => handleScroll('right')}
        >
          <i className='onedx-chevron-right size-5 text-primary' />
        </div>
      )}
    </div>
  )
}
