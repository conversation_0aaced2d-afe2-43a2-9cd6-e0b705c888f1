'use client'

import React, { useState } from 'react'

import { usePathname, useRouter } from 'next/navigation'

import { Input, Modal, Spin } from 'antd'

import { SwiperSlide } from 'swiper/react'

import { Autoplay, Pagination } from 'swiper/modules'

import { useQuery } from '@tanstack/react-query'

import { useDispatch } from 'react-redux'

import { PackageRegisterDetailModal } from '@views/personal/package-register'

import CardProductSim from '@/components/card/personal/CardProductSim'
import { sliderProps } from '@/views/house-hold/util'
import { PersonalCustomSlider } from '../../category-product/CategoryProduct'
import Pricing from '@/models/Pricing'
import type { IPagination } from '@/types/paginationTypes'
import type { IPackage } from '@/types/dataPackageTypes'
import { useDebounceState } from '@/hooks'
import { cardSimActions } from '@/redux-store/slices/cardSimSlice'

interface Props {
  visible: boolean
  setVisible: any
}

const ChooseSimPackageModal = ({ visible, setVisible }: Props) => {
  const route = useRouter()
  const dispatch = useDispatch()
  const pathname = usePathname()
  const [openPackageDetailModal, setOpenPackageDetailModal] = useState(false)

  const [dataPackage, setDataPackage] = useState<any>()

  const [valueSearch, setValueSearch] = useDebounceState('')

  // API
  // Danh sách gói
  const { data, isFetching } = useQuery<IPagination<IPackage>>({
    queryKey: ['data-packages', valueSearch],
    queryFn: () =>
      Pricing.getAllPricing({
        page: 0,
        size: 7,
        ...(valueSearch && { value: valueSearch }),
        categoryMigrationIds: [210],
        serviceMigrationIds: [210]
      })
  })

  // Mở Modal Xem chi tiết gói
  const viewPackageDetail = (pricing: any) => {
    if (pricing?.pricingId) {
      setOpenPackageDetailModal(true)
      setDataPackage(pricing)
    }
  }

  const handleCancel = () => {
    setVisible(false) // Click chọn icon sẽ đóng popup
    dispatch(cardSimActions.resetSimPayment())
  }

  // Xác nhận Modal Chi tiết gói
  const onSubmitDetailModal = (submitMigration: any) => {
    dispatch(cardSimActions.updatePricing({ ...dataPackage, ...submitMigration }))

    if (pathname !== '/checkout/sim') {
      route.push('/checkout/sim')
    }

    setOpenPackageDetailModal(false)
    setVisible(false)
  }

  return (
    visible && (
      <Modal
        width={1278}
        title='Chọn gói cước'
        open={visible}
        onCancel={handleCancel}
        footer={null}
        maskClosable={false} // Bắt buộc chọn
      >
        <div className='mb-4 flex gap-x-[10px]'>
          <Input
            className='h-10 rounded-xl'
            prefix={<i className='onedx-search size-5 text-text-neutral-light' />}
            placeholder='Nhập gói cước cần tìm'
            allowClear
            onChange={e => setValueSearch(e.target.value)}
          />
        </div>
        <Spin spinning={isFetching}>
          <PersonalCustomSlider
            {...sliderProps}
            autoplay={{
              delay: 2500,
              disableOnInteraction: true,
              pauseOnMouseEnter: true
            }}
            touchStartPreventDefault={false}
            simulateTouch={false}
            pagination={{
              clickable: true
            }}
            modules={[Autoplay, Pagination]}
          >
            {data?.content?.map((item, index) => (
              <SwiperSlide key={item.pricingId}>
                <CardProductSim
                  id={item.pricingDraftId}
                  name={item.pricingName}
                  price={item.price}
                  description={item.description}
                  intervalTypeEnum={item.intervalTypeEnum}
                  key={`${item.pricingId}-${index}`}
                  numSub={item.numSub}
                  buttonText='Chọn gói'
                  typeCard='DATA_PACKAGE'
                  onClickButton={() => {
                    dispatch(cardSimActions.updatePricing(item))

                    if (pathname !== '/checkout/sim') {
                      route.push('/checkout/sim')
                    }

                    setVisible(false)
                  }}
                  showChooseSIM={false}
                  tymId={item?.pricingDraftId}
                  paymentCycle={item?.paymentCycle}
                  onClickCard={() => viewPackageDetail(item)}
                />
              </SwiperSlide>
            ))}
          </PersonalCustomSlider>
        </Spin>
        {/* Modal Chi tiết gói */}
        {openPackageDetailModal && (
          <PackageRegisterDetailModal
            openModal={openPackageDetailModal}
            setOpenModal={setOpenPackageDetailModal}
            type='DATA_PACKAGE'
            pricingId={dataPackage?.pricingId}
            onSubmit={onSubmitDetailModal}
          />
        )}
      </Modal>
    )
  )
}

export default ChooseSimPackageModal
