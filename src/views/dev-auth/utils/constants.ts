import type { ThemeConfig } from 'antd'

import type { ColumnType } from 'antd/es/table'

import { baseColorLight, colors } from '@/utils/colors'

export const USER_EXCLUDE_NONE = -1

export const loginDevTheme: ThemeConfig = {
  components: {
    Form: {
      verticalLabelPadding: '0 0 8px',
      itemMarginBottom: 0,
      inlineItemMarginBottom: 20,
      fontSize: 14,
      labelFontSize: 12
    },
    Input: {
      paddingBlock: 10,
      paddingInline: 12
    },
    Select: {
      controlHeight: 40
    },
    Checkbox: {
      borderRadiusSM: 4,
      lineWidth: 1.5,
      colorBorder: baseColorLight['gray-alpha-3']
    },
    Table: {
      headerColor: baseColorLight['gray-6'],
      headerBg: baseColorLight['gray-1'],
      rowSelectedBg: baseColorLight['gray-alpha-1'],
      rowSelectedHoverBg: baseColorLight['gray-alpha-2'],
      rowHoverBg: baseColorLight['gray-1']
    }
  },
  token: {
    margin: 0,
    borderRadius: 8,
    lineHeight: 1.25,
    fontFamily: 'Inter, sans-serif',
    colorPrimary: colors.primary600,
    colorPrimaryHover: colors.primary500,
    colorPrimaryActive: colors.primary700,
    colorPrimaryBorder: colors.primary600,
    colorText: baseColorLight['gray-11'],
    colorTextDescription: baseColorLight['gray-9'],
    colorTextPlaceholder: baseColorLight['gray-6'],
    colorTextDisabled: baseColorLight['gray-6'],
    colorBorder: baseColorLight['gray-alpha-3'],
    boxShadow: '0 0 0 0 rgba(165, 163, 174, 0.00), 0 4px 16px 0 rgba(165, 163, 174, 0.30)'
  }
}

export const passwordChecks = [
  {
    label: 'Chứa 8 - 16 ký tự',
    check: (password: string) => password.length >= 8 && password.length <= 16
  },
  {
    label: 'Ký tự số',
    check: (password: string) => /[0-9]/.test(password)
  },
  {
    label: 'Chữ thường (a-z)',
    check: (password: string) => /[a-z]/.test(password)
  },
  {
    label: 'Không chứa ký tự phổ biến',
    check: (password: string) => {
      if (!password || password.trim() === '') return false
      const commonPasswords = ['password', '123456', '12345678', 'qwerty', 'abc123', '111111', '123123']

      return !commonPasswords.some(commonPw => password.toLowerCase().includes(commonPw))
    }
  },
  {
    label: 'Chữ hoa (A-Z)',
    check: (password: string) => /[A-Z]/.test(password)
  },
  {
    label: 'Không chứa các chuỗi ký tự liên tiếp',
    check: (password: string) => {
      if (!password || password.trim() === '') return false

      for (let index = 0; index < password.length - 2; index++) {
        const currentCharCode = password.charCodeAt(index)
        const nextCharCode = password.charCodeAt(index + 1)
        const thirdCharCode = password.charCodeAt(index + 2)

        if (
          (nextCharCode === currentCharCode + 1 && thirdCharCode === nextCharCode + 1) ||
          (nextCharCode === currentCharCode - 1 && thirdCharCode === nextCharCode - 1)
        ) {
          return false
        }
      }

      return true
    }
  },
  {
    label: 'Ký tự đặc biệt: !@#$%^&+=',
    check: (password: string) => /[!@#$%^&+=]/.test(password)
  }
]

export const operatingAreasColumns: ColumnType[] = [
  {
    title: 'Địa bàn',
    render: (_, record, index) => index + 1
  },
  {
    title: 'Tỉnh/thành',
    dataIndex: 'provinceName'
  },
  {
    title: 'Phường/xã',
    dataIndex: 'wardName'
  },
  {
    title: 'Phố/đường',
    dataIndex: 'streetName'
  }
]
