'use client'

import React from 'react'

import Cookies from 'js-cookie'

import DevAuth<PERSON>ageLayout from '@components/layout/auth-page/DevAuthPageLayout'
import DevLoginForm from '@views/auth/login/DevLoginForm'

const DevLogin: React.FC = () => {
  const footer = (
    <div className='w-[498px]'>
      <div className='mb-[10px] text-center'>
        <div
          className='cursor-pointer text-sm font-medium leading-[20px] text-sme-blue-8 underline'
          onClick={() => {
            Cookies.remove('infoSwitchAccount')
            window.location.href = '/partner-portal/forgot-password'
          }}
        >
          Quên mật khẩu
        </div>
      </div>
      <div className='flex justify-center gap-[8px] text-center'>
        <div className='text-xs font-normal leading-[20px] text-gray-8'>B<PERSON>n chưa có tà<PERSON> kho<PERSON>?</div>
        <div
          onClick={() => (window.location.href = '/partner-portal/register')}
          className='mt-[2px] cursor-pointer text-xs font-medium leading-[16px] text-sme-blue-8 underline'
        >
          Đăng ký ngay
        </div>
      </div>
    </div>
  )

  return <DevLoginForm AuthLayout={DevAuthPageLayout} footer={footer} />
}

export default DevLogin
