import React from 'react'

import { Radio } from 'antd'

import { baseColorLight } from '@/utils/colors'
import { passwordChecks } from '../utils/constants'

interface PasswordStrengthIndicatorProps {
  password: string
  className?: string
}

// Password strength levels constants
const PASSWORD_STRENGTH = {
  NONE: 'none',
  WEAK: 'weak',
  MEDIUM: 'medium',
  STRONG: 'strong'
} as const

type PasswordStrengthLevel = (typeof PASSWORD_STRENGTH)[keyof typeof PASSWORD_STRENGTH]

const getPasswordStrength = (
  password: string
): {
  level: PasswordStrengthLevel
  passedCount: number
  totalCount: number
} => {
  if (!password) {
    return { level: PASSWORD_STRENGTH.NONE, passedCount: 0, totalCount: passwordChecks.length }
  }

  const passedCount = passwordChecks.filter(item => item.check(password)).length
  const totalCount = passwordChecks.length

  let level: PasswordStrengthLevel = PASSWORD_STRENGTH.NONE

  if (passedCount === totalCount) {
    level = PASSWORD_STRENGTH.STRONG
  } else if (passedCount >= Math.ceil(totalCount / 2)) {
    level = PASSWORD_STRENGTH.MEDIUM
  } else if (passedCount > 0) {
    level = PASSWORD_STRENGTH.WEAK
  }

  return { level, passedCount, totalCount }
}

const getStrengthColor = (level: PasswordStrengthLevel, isActive: boolean): string => {
  if (!isActive) return 'transparent'

  switch (level) {
    case PASSWORD_STRENGTH.WEAK:
      return baseColorLight['red-6']
    case PASSWORD_STRENGTH.MEDIUM:
      return baseColorLight['yellow-6']
    case PASSWORD_STRENGTH.STRONG:
      return baseColorLight['green-6']
    default:
      return baseColorLight['red-6']
  }
}

export const PasswordStrengthIndicator: React.FC<PasswordStrengthIndicatorProps> = ({ password, className = '' }) => {
  const { level } = getPasswordStrength(password)

  const strengthBars = [
    {
      level: PASSWORD_STRENGTH.WEAK,
      isActive: level !== PASSWORD_STRENGTH.NONE
    },
    {
      level: PASSWORD_STRENGTH.MEDIUM,
      isActive: level === PASSWORD_STRENGTH.MEDIUM || level === PASSWORD_STRENGTH.STRONG
    },
    {
      level: PASSWORD_STRENGTH.STRONG,
      isActive: level === PASSWORD_STRENGTH.STRONG
    }
  ]

  return (
    <div className={`space-y-3 ${className}`}>
      <div className='space-y-2'>
        {/* Strength Bar */}
        {/* <div className='flex items-center justify-between text-sm'>
          <span className='text-neutral-800'>Độ mạnh mật khẩu:</span>
          <span style={{ color: strengthColor }} className='font-medium'>
            {strengthText}
          </span>
        </div> */}

        {/* Progress Bar */}
        <div className='flex gap-1'>
          {strengthBars.map(bar => (
            <div key={bar.level} className='h-2 flex-1 overflow-hidden bg-gray-200'>
              <div
                className='h-full transition-all duration-300 ease-in-out'
                style={{
                  backgroundColor: getStrengthColor(level, bar.isActive),
                  width: bar.isActive ? '100%' : '0%'
                }}
              />
            </div>
          ))}
        </div>
      </div>
      {/* Criteria List */}
      <div className='grid grid-cols-2 gap-2'>
        {passwordChecks.map((item, idx) => (
          <div key={idx} className='flex items-center gap-2'>
            <Radio
              checked={item.check(password || '')}
              onClick={e => {
                e.preventDefault()
              }}
            >
              <span
                className={
                  item.check(password || '')
                    ? 'caption-12-regular text-primary-blue'
                    : 'caption-12-regular text-text-neutral-medium'
                }
              >
                {item.label}
              </span>
            </Radio>
          </div>
        ))}
      </div>
    </div>
  )
}
