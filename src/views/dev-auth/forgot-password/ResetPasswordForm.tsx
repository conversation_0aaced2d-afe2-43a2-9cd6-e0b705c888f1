'use client'

import React, { useState } from 'react'

import { useRouter } from 'next/navigation'

import { Button, Form, Input } from 'antd'

import { useMutation } from '@tanstack/react-query'

import { trim } from 'lodash'

import CheckError from '@views/auth/utils/authPassword'

import User from '@/models/User'
import { validatePassword, validateRequireInput } from '@/validator'
import DevModalNotification from '@components/modal/DevModalNotification'
import type { NotificationInfoType } from '@/types/notificationModalTypes'
import DXPortal from '@/models/DXPortal'
import DevAuthPageLayout from '@/components/layout/auth-page/DevAuthPageLayout'
// import { ValidatePasswordInfor } from '@views/dev-auth/forgot-password/ValidatePasswordInfor'

interface ResetPasswordFromProps {
  id: string | string[]
  passTemp: string | string[]
}

const ResetPasswordForm = ({ id, passTemp }: ResetPasswordFromProps) => {
  const router = useRouter()

  const [visibleModal, setVisibleModal] = useState(false)

  const [infoModal, setInfoModal] = useState<NotificationInfoType>({})

  const [form] = Form.useForm()

  const successModal: NotificationInfoType = {
    iconType: 'SUCCESS',
    title: 'Đổi mật khẩu thành công',
    subTitle: 'Mật khẩu của bạn đã được xác nhận. Bạn có thể tiếp tục đăng nhập vào tài khoản của mình.',
    textButton: 'Đăng nhập',
    redirectPage: '/partner-portal/login'
  }

  const mutation = useMutation({
    mutationFn: User.resetPassword,
    onSuccess: () => {
      setVisibleModal(true)
      setInfoModal({ ...successModal, handleCancel: () => router.push(DXPortal.dev.createPath('/login', null)) })
    },
    onError: (error: any) => {
      const { field, errorMessage, openModal, iconType, title } = CheckError(error)

      if (openModal && iconType && title) {
        setVisibleModal(true)
        setInfoModal({
          iconType,
          title,
          textButton: 'Xác nhận',
          subTitle: errorMessage,
          isCloseModal: false,
          width: 450
        })
      } else
        form.setFields([
          {
            name: field,
            errors: [errorMessage]
          }
        ])
    }
  })

  const handleSubmitReset = (values: { newPassword: string }) => {
    const newPassword = {
      newPassword: values.newPassword
    }

    if (id && passTemp) {
      mutation.mutate({ id, resetToken: passTemp, newPassword })
    } else {
      console.error('Missing id or passTemp in the URL')
    }
  }

  const [password, setPassword] = useState('')

  return (
    <DevAuthPageLayout
      goBackText='Quay lại'
      title='Đặt lại mật khẩu'
      description='Đặt lại mật khẩu để tiếp tục sử dụng oneSME'
      onGoBack={() => {
        window.location.href = '/partner-portal/login'
      }}
    >
      <div className='flex items-center justify-center'>
        <div className='flex w-full flex-col'>
          <Form form={form} onFinish={handleSubmitReset} layout='vertical'>
            <div className='mb-[40px]'>
              <Form.Item
                className='mb-[10px]'
                label='Mật khẩu mới'
                name='newPassword'
                normalize={trim}
                rules={[
                  validateRequireInput('Mật khẩu mới không được bỏ trống'),
                  validatePassword(
                    'Mật khẩu phải có từ 8-16 ký tự, bao gồm ít nhất 1 chữ viết hoa, 1 chữ viết thường, 1 chữ số và 1 ký tự đặc biệt trong !@#$%^&+= (không bao gồm ký tự "space")'
                  )
                ]}
              >
                <Input.Password
                  type='password'
                  placeholder='Nhập mật khẩu mới'
                  className='rounded-none'
                  maxLength={16}
                  value={password}
                  onChange={e => setPassword(e.target.value)}
                />
              </Form.Item>
              <div className='mb-[10px]'>
                {/* Check mật khẩu và độ nhanh cu mâ khẩu */}
                {/*<ValidatePasswordInfor password={password} />*/}
                <div className='text-caption-12 font-normal'>Mật khẩu 8 - 16 ký tự, bao gồm cả chữ và số</div>
              </div>
            </div>
            <Form.Item
              label='Xác nhận mật khẩu mới'
              name='confirmPassword'
              normalize={trim}
              className='rounded-none'
              dependencies={['newPassword']}
              rules={[
                validateRequireInput('Xác nhận mật khẩu mới không được bỏ trống'),
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value || getFieldValue('newPassword') === value) {
                      return Promise.resolve()
                    }

                    return Promise.reject('Xác nhận mật khẩu mới không khớp với mật khẩu mới')
                  }
                })
              ]}
            >
              <Input.Password type='password' placeholder='Nhập xác nhận mật khẩu' maxLength={16} />
            </Form.Item>
            <Button type='primary' htmlType='submit' className='mt-[40px] w-full rounded-none bg-bg-primary-default'>
              Đặt lại mật khẩu
            </Button>
          </Form>
        </div>
      </div>
      {visibleModal && (
        <DevModalNotification visibleModal={visibleModal} setVisibleModal={setVisibleModal} infoModal={infoModal} />
      )}
    </DevAuthPageLayout>
  )
}

export default ResetPasswordForm
