import { Col, Progress, Row, Space } from 'antd'

interface ValidatePasswordProps {
  password: string
}

export const ValidationItem = ({ isValid, text }: { isValid: boolean; text: string }) => (
  <div className='mb-[2px] flex items-center gap-2'>
    <div
      className='flex size-4 items-center justify-center rounded-full'
      style={{
        border: '2px solid #d9d9d9',
        backgroundColor: isValid ? '#52c41a' : 'transparent',
        borderColor: isValid ? '#52c41a' : '#d9d9d9'
      }}
    >
      {isValid && (
        <div
          style={{
            width: '6px',
            height: '6px',
            backgroundColor: 'white',
            borderRadius: '50%'
          }}
        />
      )}
    </div>
    <div className='font-sans text-xs font-normal not-italic leading-4 tracking-wide text-slate-600'>{text}</div>
  </div>
)

export const ValidatePasswordInfor = ({ password }: ValidatePasswordProps) => {
  const hasValidLength = !!password && password.length >= 8 && password.length <= 16
  const hasLowercase = !!password && /[a-z]/.test(password)
  const hasUppercase = !!password && /[A-Z]/.test(password)
  const hasSpecialChars = !!password && /[!@#$%^&+=]/.test(password)
  const hasNumber = !!password && /\d/.test(password)
  const hasNoConsecutiveChars = !!password && !/(.)\1{2,}/.test(password)

  const calculateStrength = () => {
    let score = 0

    if (hasValidLength) score++
    if (hasLowercase) score++
    if (hasUppercase) score++
    if (hasNumber) score++
    if (hasSpecialChars) score++
    if (hasNoConsecutiveChars) score++

    return (score / 6) * 100
  }

  const strength = calculateStrength()

  return (
    <>
      <div className='mb-[10px]'>
        <Progress
          percent={strength}
          showInfo={false}
          strokeColor={{
            '0%': strength < 40 ? '#ff4d4f' : strength < 70 ? '#faad14' : '#52c41a',
            '100%': strength < 40 ? '#ff4d4f' : strength < 70 ? '#faad14' : '#52c41a'
          }}
          trailColor='#f0f0f0'
          strokeWidth={6}
        />
      </div>
      <div>
        <Row gutter={[24, 0]}>
          <Col span={12}>
            <Space direction='vertical' style={{ width: '100%' }}>
              <ValidationItem isValid={hasValidLength} text='Chứa 8 - 16 ký tự' />
              <ValidationItem isValid={hasLowercase} text='Chữ thường (a-z)' />
              <ValidationItem isValid={hasUppercase} text='Chữ hoa (A-Z)' />
              <ValidationItem isValid={hasSpecialChars} text='Ký tự đặc biệt: !@#$%^&+=' />
            </Space>
          </Col>

          <Col span={12}>
            <Space direction='vertical' style={{ width: '100%' }}>
              <ValidationItem isValid={hasNumber} text='Ký tự số' />
              <ValidationItem isValid={true} text='Không chứa ký tự phổ biến' />
              <ValidationItem isValid={hasNoConsecutiveChars} text='Không chứa các chuỗi ký tự liên tiếp' />
            </Space>
          </Col>
        </Row>
      </div>
    </>
  )
}
