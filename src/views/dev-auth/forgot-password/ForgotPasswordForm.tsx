'use client'

import React from 'react'

import { Button, Form, Input } from 'antd'

import { trim } from 'lodash'

import DevAuthPageLayout from '@/components/layout/auth-page/DevAuthPageLayout'
import { validateEmail, validateRequireInput } from '@/validator'

import { useForgotPassword } from '@/hooks/useForgotPassword'
import { ModalNotification } from '@/components/modal'

const ForgotPasswordForm: React.FC = () => {
  const [form] = Form.useForm()

  const { visibleModalNotification, setVisibleModalNotification, infoNotification, mutation, handleSubmit } =
    useForgotPassword({
      form,
      portalType: 'DEV'
    })

  return (
    <DevAuthPageLayout
      title='Quên mật khẩu'
      footer={null}
      goBackText='Quay lại'
      onGoBack={() => {
        window.location.href = '/partner-portal/login'
      }}
    >
      <Form form={form} onFinish={handleSubmit} layout='vertical'>
        {/* Tà<PERSON> kho<PERSON>n */}
        <Form.Item
          label={<div className='text-xs font-medium leading-[16px]'>Email</div>}
          name='username'
          rules={[validateRequireInput('Email không được bỏ trống'), validateEmail('Sai định dạng email')]}
          className='mb-[40px]'
          normalize={trim}
        >
          <Input className='h-[40px] rounded-none' maxLength={100} autoFocus placeholder='Nhập email' />
        </Form.Item>
      </Form>

      <Button
        type='primary'
        htmlType='submit'
        onClick={() => form.submit()}
        loading={mutation.isPending}
        className='h-10 w-full'
      >
        Tiếp tục
      </Button>

      {visibleModalNotification && (
        <ModalNotification
          visibleModal={visibleModalNotification}
          setVisibleModal={setVisibleModalNotification}
          infoModal={infoNotification}
        />
      )}
    </DevAuthPageLayout>
  )
}

export default ForgotPasswordForm
