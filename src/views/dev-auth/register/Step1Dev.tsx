import { Form, Input } from 'antd'

import { validateEmail, validatePassword, validatePhoneNumber1, validateRequire } from '@/validator'
import { validateUserEmailDuplicate } from '@/components/partner-management'
// import { PasswordStrengthIndicator } from '../components/PasswordStrengthIndicator'
import { regexEmail } from '@/views/management/wallet-mngt/constant'

const Step1Dev = () => {
  // const password = Form.useWatch('password')

  return (
    <div className='flex flex-col gap-5'>
      <Form.Item
        name='email'
        label='Email đăng nhập'
        validateFirst
        rules={[
          validateRequire('Email đăng nhập không được bỏ trống'),
          validateEmail('Sai định dạng email', regexEmail),
          validateUserEmailDuplicate
        ]}
      >
        <Input placeholder='Nhập email' maxLength={100} />
      </Form.Item>
      <Form.Item
        name='phoneNumber'
        label='<PERSON><PERSON> điện thoại'
        rules={[
          validateRequire('<PERSON><PERSON> điện thoại không được bỏ trống!'),
          validatePhoneNumber1('Sai định dạng số điện thoại', 10)
        ]}
      >
        <Input type='tel' placeholder='Nhập số điện thoại' />
      </Form.Item>
      <Form.Item
        name='password'
        label='Mật khẩu'
        rules={[validateRequire('Mật khẩu không được bỏ trống'), validatePassword()]}
        hasFeedback
      >
        <Input.Password placeholder='Nhập mật khẩu' />
      </Form.Item>
      {/* <PasswordStrengthIndicator password={password || ''} /> */}
      <Form.Item
        name='confirmPassword'
        label='Xác nhận mật khẩu'
        dependencies={['password']}
        hasFeedback
        rules={[
          validateRequire('Xác nhận mật khẩu không được bỏ trống'),
          ({ getFieldValue }) => ({
            validator(_, value) {
              if (!value || getFieldValue('password') === value) {
                return Promise.resolve()
              }

              return Promise.reject(new Error('Xác nhận mật khẩu không khớp với Mật khẩu.'))
            }
          })
        ]}
      >
        <Input.Password placeholder='Nhập lại mật khẩu' />
      </Form.Item>
    </div>
  )
}

export { Step1Dev }
