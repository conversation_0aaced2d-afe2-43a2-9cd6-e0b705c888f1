import { useMemo, useState } from 'react'

import { Button, Form, Input, Select, Table } from 'antd'

import classNames from 'classnames'

import {
  customerTypeOptions,
  representativeTypeOptions,
  serviceCapabilityOptions
} from '@/views/partner-management/common'
import { validateCode, validateRequire, validateStrLength } from '@/validator'
import { useNewLocation } from '@/hooks/useNewLocation'
import { filterOption } from '@/utils/string'
import { operatingAreasColumns } from '../utils/constants'
import ModalOperatingAreas from './ModalOperatingAreas'
import type { OperatingArea } from '@/types/partner-portal/partner'

const Step2Dev = () => {
  const form = Form.useFormInstance()
  const customerType = Form.useWatch('customerType', form)
  const isEnterprise = useMemo(() => customerType === 'ENTERPRISE', [customerType])
  const [openModal, setOpenModal] = useState(false)
  const initialCreateOperatingArea = [{}]
  const [initialOperatingAreas, setInitialOperatingAreas] = useState<any[]>(initialCreateOperatingArea)
  const { provinceList, loadingProvince } = useNewLocation('register-dev')
  const operatingAreas: OperatingArea[] = Form.useWatch('operatingAreas', form) || []

  const handleOpenModal = (initialValues: any) => {
    setInitialOperatingAreas(initialValues || initialCreateOperatingArea)
    setOpenModal(true)
  }

  const handleAddOperatingAreas = (values: { operatingAreas: OperatingArea[] }) => {
    form.setFieldsValue(values)
    setOpenModal(false)
  }

  return (
    <div className='flex flex-col gap-5'>
      <div
        className={classNames('gap-5', {
          'grid grid-cols-2': isEnterprise,
          'flex flex-col': !isEnterprise
        })}
      >
        <Form.Item
          name='customerType'
          label='Loại hình đối tác'
          rules={[validateRequire('Loại hình đối tác không được bỏ trống')]}
          initialValue='ENTERPRISE'
        >
          <Select placeholder='Chọn loại hình đối tác' options={customerTypeOptions} />
        </Form.Item>
        {isEnterprise ? (
          <>
            <Form.Item name='name' label={'Tên công ty'} rules={[validateRequire('Tên công ty không được bỏ trống')]}>
              <Input placeholder='Nhập tên công ty' maxLength={300} />
            </Form.Item>
            <Form.Item
              name='taxCode'
              label='Mã số thuế'
              validateFirst
              rules={[
                validateRequire('Mã số thuế không được bỏ trống'),
                validateStrLength(10, 13, 'Sai định dạng mã số thuế'),
                validateCode('Sai định dạng mã số thuế', /^[a-zA-Z0-9]+$/)
              ]}
            >
              <Input placeholder='Nhập mã số thuế' maxLength={13} />
            </Form.Item>
          </>
        ) : (
          <>
            <Form.Item name='name' label={'Họ & tên'} rules={[validateRequire('Họ và tên không được bỏ trống')]}>
              <Input placeholder='Nhập họ & tên' maxLength={100} />
            </Form.Item>

            <Form.Item
              name='repPersonalCertType'
              className='col-span-2'
              label='Loại giấy chứng thực'
              rules={[validateRequire('Giấy chứng thực không được bỏ trống')]}
            >
              <Select placeholder='Chọn loại giấy chứng thực' options={representativeTypeOptions} />
            </Form.Item>

            <Form.Item
              name='repPersonalCertNumber'
              label='Số giấy chứng thực'
              rules={[validateRequire('Số giấy chứng thực không được bỏ trống')]}
            >
              <Input placeholder='Nhập số giấy chứng thực' />
            </Form.Item>
          </>
        )}
        <Form.Item name='provinceId' label='Tỉnh/thành' rules={[validateRequire('Tỉnh/thành không được bỏ trống')]}>
          <Select
            placeholder='Chọn tỉnh/thành'
            loading={loadingProvince}
            options={provinceList}
            filterOption={filterOption}
          />
        </Form.Item>
        {isEnterprise ? (
          <Form.Item
            name='businessRegistrationAddress'
            label='Địa chỉ đăng ký kinh doanh'
            className='col-span-2'
            rules={[validateRequire('Địa chỉ đăng ký kinh doanh không được bỏ trống')]}
          >
            <Input placeholder='Nhập địa chỉ đăng ký kinh doanh' maxLength={500} />
          </Form.Item>
        ) : (
          <Form.Item
            name='residentAddress'
            label='Địa chỉ thường trú'
            // rules={[validateRequire('Địa chỉ thường trú không được bỏ trống')]}
          >
            <Input placeholder='Nhập địa chỉ thường trú' maxLength={500} />
          </Form.Item>
        )}
      </div>
      <Form.Item
        name='capabilities'
        label='Dịch vụ cung cấp'
        rules={[validateRequire('Dịch vụ cung cấp không được bỏ trống')]}
      >
        <Select
          mode='multiple'
          placeholder='Chọn một hoặc nhiều dịch vụ cung cấp'
          options={serviceCapabilityOptions}
          allowClear
          showSearch={false}
        />
      </Form.Item>
      <Form.Item
        label={
          <div className='flex w-full items-center justify-between'>
            <span>Địa bàn hoạt động</span>
            {operatingAreas?.length > 0 && (
              <Button onClick={() => handleOpenModal(operatingAreas)} type='text' className='text-sm text-primary-blue'>
                <i className='onedx-edit size-4' />
                Cập nhật
              </Button>
            )}
          </div>
        }
        name='operatingAreas'
        rules={[validateRequire('Địa bàn hoạt động không được bỏ trống')]}
      >
        {operatingAreas?.length > 0 ? (
          <Table dataSource={operatingAreas} columns={operatingAreasColumns} pagination={false} />
        ) : (
          <div className='flex items-center gap-3 border border-solid border-border-neutral-light px-3 py-2'>
            <Button
              onClick={() => handleOpenModal(initialCreateOperatingArea)}
              type='text'
              className='rounded-none bg-[#E6F1FE] text-primary-blue'
            >
              <i className='onedx-plus size-5' />
              Thêm địa bàn
            </Button>
          </div>
        )}
      </Form.Item>
      <ModalOperatingAreas
        open={openModal}
        setOpen={setOpenModal}
        handleAdd={handleAddOperatingAreas}
        initialValues={initialOperatingAreas}
      />
    </div>
  )
}

export { Step2Dev }
