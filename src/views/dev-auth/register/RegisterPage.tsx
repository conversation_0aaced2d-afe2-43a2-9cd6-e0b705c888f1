'use client'
import React from 'react'

import { ConfigProvider } from 'antd'

import { RegisterDevForm } from './RegisterDevForm'
import { loginDevTheme } from '../utils/constants'

const RegisterPage: React.FC = () => {
  return (
    <ConfigProvider theme={loginDevTheme}>
      <div className='relative grid h-screen grid-cols-12'>
        <div className='col-span-5 flex flex-col items-center justify-center'>
          <div className='relative mt-[60px] h-full w-[498px]'>
            <RegisterDevForm />
            <div className='absolute bottom-5 left-0 w-full text-center text-sm text-gray-500'>
              Bạn đã có tài khoản?{' '}
              <a href='/partner-portal/login' className='text-primary-blue underline'>
                Đăng nhập ngay
              </a>
            </div>
          </div>
        </div>

        {/* Ảnh nền bên phải */}
        <div className='col-span-7 h-full sm:hidden'>
          <img
            src='/assets/images/pages/dev-portal/auth/authentication-bg.png'
            alt='Authentication Background'
            className='size-full'
            draggable={false}
          />
        </div>
      </div>
    </ConfigProvider>
  )
}

export default RegisterPage
