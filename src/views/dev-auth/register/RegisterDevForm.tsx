import { useState } from 'react'

import { useRouter } from 'next/navigation'

import { Button, Checkbox, Form } from 'antd'

import { useMutation } from '@tanstack/react-query'

import { Step1Dev, Step2Dev } from '.'
import partnerManagementInstance from '@/models/PartnerManagement'
import { message } from '@/components/notification'
import { If } from '@/components/common'
import { STATUS_ENUM } from '@/views/partner-management/common'
import { validateRequireCheckbox } from '@/validator'

const StepRender = ({ step }: { step: number }) => {
  switch (step) {
    case 1:
      return <div className='body-14-regular'>Bước 1/2: Thông tin đăng nhập</div>
    case 2:
      return <div className='body-14-regular'>Bước 2/2: Thông tin cá nhân</div>
  }
}

const RegisterDevForm = () => {
  const [form] = Form.useForm()
  const [currentStep, setCurrentStep] = useState(1)
  const route = useRouter()
  const lastStep = 2

  const handleNext = () => {
    form
      .validateFields()
      .then(() => {
        if (currentStep === lastStep) {
          form.submit()
        } else {
          setCurrentStep(prev => prev + 1)
        }
      })
      .catch(() => {})
  }

  const handleBack = () => {
    if (currentStep === 1) {
      route.push('/partner-portal/login')
    } else {
      setCurrentStep(prev => prev - 1)
    }
  }

  const registerMutation = useMutation({
    mutationKey: ['createPartner'],
    mutationFn: partnerManagementInstance.devCreatePartner,
    onSuccess: () => {
      message.success('Đăng ký thành công')
      setTimeout(() => {
        route.push('/partner-portal/login')
      }, 2000)
    },
    onError: () => {
      // handle error
      message.error('Tạo đối tác thất bại')
    }
  })

  const handleSubmit = () => {
    // Prepare data
    const values = form.getFieldsValue(true)

    // Validate

    const submitValues = {
      ...values,
      status: STATUS_ENUM.INACTIVE
    }

    registerMutation.mutate(submitValues)
  }

  return (
    <Form form={form} layout='vertical' onFinish={() => handleSubmit()} autoComplete='off'>
      <div className='flex flex-col gap-10 text-text-neutral-medium'>
        {/* Step and Title */}
        <div className='flex flex-col gap-5'>
          <StepRender step={currentStep} />
          <div className='title-28-semibold'>Đăng ký tài khoản</div>
        </div>
        {/* Form */}
        <div aria-hidden={currentStep !== 1} className='aria-hidden:hidden'>
          <Step1Dev />
        </div>
        <If condition={currentStep === 2}>
          <Step2Dev />
        </If>
        {/* Footer */}
        <div className='flex flex-col justify-between gap-2.5'>
          <If condition={currentStep === lastStep}>
            <Form.Item
              name='agreeTerms'
              valuePropName='checked'
              initialValue={false}
              rules={[validateRequireCheckbox('Bạn phải đồng ý với Điều khoản Dịch vụ để tiếp tục')]}
              className='mb-2'
            >
              <Checkbox>
                <div className='flex items-start gap-2'>
                  <span className='body-12-regular text-text-neutral-low'>
                    Tôi đã đọc và đồng ý với{' '}
                    <a
                      href='/partner-portal/register'
                      target='_blank'
                      rel='noopener noreferrer'
                      className='text-primary-blue'
                    >
                      Điều khoản Dịch vụ.
                    </a>
                  </span>
                </div>
              </Checkbox>
            </Form.Item>
          </If>
          <Button
            block
            className='body-14-medium h-auto px-3 py-2.5'
            type='primary'
            onClick={handleNext}
            loading={registerMutation.isPending && currentStep === lastStep}
            disabled={registerMutation.isPending && currentStep === lastStep}
          >
            {currentStep === lastStep ? 'Đăng ký' : 'Tiếp tục'}
          </Button>
          <Button block className='body-14-medium h-auto px-3 py-2.5 text-primary-blue' onClick={handleBack}>
            Quay lại
          </Button>
        </div>
      </div>
    </Form>
  )
}

export { RegisterDevForm }
