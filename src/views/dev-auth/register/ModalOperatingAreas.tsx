import { useEffect } from 'react'

import { Form } from 'antd'

import { PartnerAddressList } from '@/components/partner-management'
import type { OperatingArea } from '@/types/partner-portal/partner'
import { PartnerButton, PartnerModal } from '@/components/partner-management/common'

interface Props {
  open: boolean
  setOpen: (open: boolean) => void
  handleAdd: ({ operatingAreas }: { operatingAreas: OperatingArea[] }) => void
  initialValues?: any
}

const ModalOperatingAreas = ({ open, setOpen, handleAdd, initialValues }: Props) => {
  const [form] = Form.useForm()

  useEffect(() => {
    if (initialValues) {
      form.setFieldsValue({
        operatingAreas: initialValues
      })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [initialValues])

  return (
    <Form layout='vertical' form={form} preserve onFinish={handleAdd}>
      <PartnerModal
        open={open}
        width={680}
        setOpen={() => setOpen(false)}
        controlTitle={{
          wrapperClassName: 'justify-between mb-4',
          iconClassName: 'hidden',
          titleText: 'Địa bàn hoạt động'
        }}
        footer={{
          wrapperClassName: 'w-full flex justify-between mt-4',
          render: (
            <>
              <PartnerButton
                type='primary'
                variant='outlined'
                onClick={() => form.setFieldsValue({ operatingAreas: [...form.getFieldValue('operatingAreas'), {}] })}
              >
                <i className='onedx-plus size-5' />
                Thêm địa bàn
              </PartnerButton>
              <div className='flex gap-3'>
                <PartnerButton type='primary' variant='outlined' onClick={() => setOpen(false)}>
                  Hủy
                </PartnerButton>
                <PartnerButton type='primary' onClick={() => form.submit()}>
                  Xác nhận
                </PartnerButton>
              </div>
            </>
          )
        }}
      >
        <PartnerAddressList name='operatingAreas' />
      </PartnerModal>
    </Form>
  )
}

export default ModalOperatingAreas
