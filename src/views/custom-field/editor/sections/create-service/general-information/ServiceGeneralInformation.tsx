'use client'
import { useEffect, useMemo, useState } from 'react'

import dynamic from 'next/dynamic'

import { useParams } from 'next/navigation'

import { useDispatch, useSelector } from 'react-redux'

import { Checkbox, Form, Input, Select, Tooltip } from 'antd'

import { debounce, trim } from 'lodash'

import { useQuery } from '@tanstack/react-query'

import classNames from 'classnames'

import { InfoCircleOutlined } from '@ant-design/icons'

import { updateInfoCustomField, selectProviderDTO } from '@/redux-store/slices/customFieldSlice'

import { If } from '@/components/common'
import { validateMaxLengthEditor } from '@/components/common/CKEditorWrapper'
import { WrapSection } from '@/components/custom-field/common'
import { UploadFile } from '@/components/custom-field/editor/CustomComponents/ModalCreateVariant/components'
import { useUser } from '@/hooks'
import AccountDev from '@/models/AccountDev'
import CategoryPortal from '@/models/CategoryPortal'
import ServiceInstance from '@/models/CommonService'
import { filterOptionTrim, replaceSpecialCharacters } from '@/utils/string'
import {
  validateCode,
  validateLink,
  validateMaxLengthStr,
  validateNonVietnamese,
  validateNumberInput,
  validateRequire,
  validateRequireInput,
  validateVietnamese
} from '@/validator'
import { SelectManufacturer } from './SelectManufacturer'

// 👇 Import động để tránh SSR
const CKEditorWrapper = dynamic(() => import('@/components/common/CKEditorWrapper'), {
  ssr: false
})

const languageOptions = [
  { label: 'Tiếng Việt', value: '0' },
  { label: 'Tiếng Anh', value: '1' }
]

const urlServiceStatusOptions = [
  { label: 'Luôn hiển thị', value: 0 },
  { label: 'Hiển thị khi khách hàng đăng nhập', value: 1 }
]

const PROVIDER_TYPE = {
  VNPT: 0,
  VNPT_PARTNER: 1,
  THIRD_PARTY: 2
}

export const ServiceGeneralInformation = () => {
  const { id, draftId } = useParams<{ id: string; draftId: string }>()

  const form = Form.useFormInstance()
  const classification = Form.useWatch('classification', form)
  const providerType = Form.useWatch('providerType', form)
  const onOsType = Form.useWatch('onOsType', form)

  const label = useMemo(() => (classification === 'PHYSICAL' ? 'sản phẩm' : 'dịch vụ'), [classification])

  const dispatch = useDispatch()

  const [serviceDescription, setServiceDescription] = useState('<p></p>')

  const { user } = useUser()

  /* Check account là admin hay dev */
  const isDeveloper = user?.portalType === 'DEV'

  const { data: categoryOptions, isFetching: isFetchingCategories } = useQuery({
    queryKey: ['categories-list', classification],
    queryFn: async () => {
      const res: {
        id: number
        name: string
      }[] = await CategoryPortal.getAllForDropdownList({
        type: classification === 'PHYSICAL' ? 'DEVICE' : 'SERVICE'
      })

      return res.map((e: any) => ({ label: e.name, value: e.id }))
    },
    initialData: [],
    retry: 0,
    enabled: !!classification
    // staleTime: Infinity, // Giữ dữ liệu luôn "fresh", không refetch
    // gcTime: Infinity // Không xóa cache, giữ dữ liệu mãi mãi
  })

  /** Danh sách nhà cung cấp */
  const [searchText, setSearchText] = useState('')

  const { data: providerList, isFetching } = useQuery({
    queryKey: ['provider-list', searchText],
    queryFn: async ({ queryKey }) => {
      if (isDeveloper) {
        return [{ value: user?.id, label: user?.name }]
      }

      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const [queryName, searchValue] = queryKey

      const res = await AccountDev.getList({
        page: 0,
        size: 10,
        value: searchValue || '',
        isEmail: 0,
        isName: 1,
        isPhoneNumber: 0,
        isOnlyParent: 1,
        status: 1
      })

      return res?.content?.map((item: any) => ({ label: item.name, value: item.id })) || []
    },
    enabled: !!user
  })

  const debouncedSearch = useMemo(() => debounce((value: string) => setSearchText(value), 300), [])

  /** Tự động điền ID nhà cung cấp khi user là DEV */
  useEffect(() => {
    if (user?.portalType === 'DEV' && user?.id) {
      form.setFieldsValue({
        providerId: user.id,
        providerName: user.name,
        providerDTO: {
          id: user.id,
          name: user.name
        }
      })
    }
  }, [form, user])

  /** Set giá trị mặc định cho Select nhà cung cấp từ providerDTO */
  useEffect(() => {
    const providerDTO = form.getFieldValue('providerDTO')

    if (providerDTO?.id && providerDTO?.name) {
      form.setFieldsValue({
        providerId: providerDTO.id,
        providerName: providerDTO.name
      })
    }
  }, [form])

  /** Lấy thông tin nhà cung cấp hiện tại để hiển thị đúng */
  const reduxProviderDTO = useSelector(selectProviderDTO)
  const formProviderDTO = Form.useWatch('providerDTO', form)

  // Ưu tiên dữ liệu từ Redux store, fallback về form
  const currentProviderDTO = reduxProviderDTO || formProviderDTO

  /** Sync providerDTO từ Redux store với form khi có thay đổi */
  useEffect(() => {
    if (reduxProviderDTO?.id && reduxProviderDTO?.name) {
      form.setFieldsValue({
        providerId: reduxProviderDTO.id,
        providerName: reduxProviderDTO.name,
        providerDTO: reduxProviderDTO
      })
    }
  }, [form, reduxProviderDTO])

  const debouncedCheckSKU = useMemo(
    () =>
      debounce(
        (value: string, callback: (error?: string) => void) => {
          ServiceInstance.validateSKU({
            sku: value,
            ...(!!id || !!draftId ? { serviceId: id || draftId } : {})
          })
            .then(res => {
              if (res === 'false') {
                callback('SKU sản phẩm đã tồn tại trong hệ thống')
              } else {
                callback()
              }
            })
            .catch(() => {
              // callback('Lỗi kiểm tra SKU')
              callback()
            })
        },
        500 // ⏱ delay debounce 500ms
      ),
    [id, draftId]
  )

  // Hiển thị mã loại dịch vụ
  const isShowProviderType = useMemo(
    () =>
      (providerType === PROVIDER_TYPE.VNPT || providerType === PROVIDER_TYPE.VNPT_PARTNER) &&
      (classification === 'SERVICE' || classification === 'DIGITAL'),
    [classification, providerType]
  )

  // Ẩn các trường URL khi chọn Order service + Third party
  const shouldHideUrlFields = useMemo(
    () => onOsType === 1 && providerType === PROVIDER_TYPE.THIRD_PARTY,
    [onOsType, providerType]
  )

  // region return
  return (
    <WrapSection title='Thông tin chung'>
      <div
        className={classNames(
          'grid gap-4',
          classification === 'PHYSICAL' || isShowProviderType ? 'grid-cols-5' : 'grid-cols-4'
        )}
      >
        {/* Tên sản phẩm/ dịch vụ */}
        <Form.Item
          name='name'
          label={<div className='caption-12-medium text-text-neutral-strong'>Tên {label}</div>}
          rules={[
            validateRequireInput(`Tên ${label} không được bỏ trống`),
            validateMaxLengthStr(50, 'Nhập tối đa 50 ký tự'),
            validateVietnamese()
          ]}
          className='relative'
        >
          <Input placeholder={`Nhập tên ${label}`} maxLength={50} />
        </Form.Item>
        {/* Danh mục */}
        {isFetchingCategories ? (
          <div className='mb-6'>
            <div className='caption-12-medium mb-2 mt-1 text-text-neutral-strong'>
              <span className='text-red-500'>*</span> Danh mục
            </div>
            <div className='h-8 w-full animate-pulse rounded bg-gray-200'></div>
          </div>
        ) : (
          <Form.Item
            name='categoriesId'
            label={<div className='caption-12-medium text-text-neutral-strong'>Danh mục</div>}
            rules={[validateRequire('Danh mục không được bỏ trống')]}
            initialValue={[]}
          >
            <Select
              mode='multiple'
              placeholder='Chọn danh mục'
              options={categoryOptions}
              filterOption={filterOptionTrim}
              maxTagCount='responsive'
              maxTagPlaceholder={omittedValues => (
                <Tooltip
                  styles={{ root: { pointerEvents: 'none' } }}
                  title={omittedValues.map(({ label }) => label).join(', ')}
                >
                  <span>+{omittedValues.length}</span>
                </Tooltip>
              )}
              onChange={value => {
                dispatch(
                  updateInfoCustomField({
                    categoryIds: value
                  })
                )
              }}
              notFoundContent='Không có dữ liệu'
            />
          </Form.Item>
        )}
        {/* SKU Sản phẩm */}
        {classification === 'PHYSICAL' ? (
          <Form.Item
            name='sku'
            normalize={value => {
              const normalized = replaceSpecialCharacters(value || '')

              return normalized.slice(0, 20)
            }}
            validateFirst
            label={<div className='caption-12-medium text-text-neutral-strong'>SKU Sản phẩm</div>}
            required
            rules={[
              validateRequire('SKU sản phẩm không được bỏ trống'),
              validateVietnamese(),
              validateNonVietnamese('SKU sản phẩm không hợp lệ'),
              {
                validator: async (_, value) => {
                  if (!value) return Promise.resolve()

                  const vietnameseRegex = /[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ\s]/i

                  if (vietnameseRegex.test(value)) {
                    return Promise.reject(new Error('SKU sản phẩm không hợp lệ'))
                  }

                  // ✅ Dùng debounce
                  return new Promise((resolve, reject) => {
                    debouncedCheckSKU(value, error => {
                      if (error) {
                        reject(error)
                      } else {
                        resolve(undefined)
                      }
                    })
                  })
                }
              }
            ]}
          >
            <Input placeholder='Nhập mã sản phẩm' />
          </Form.Item>
        ) : (
          <Form.Item
            // Mã dịch vụ
            name='serviceCode'
            label={<div className='caption-12-medium text-text-neutral-strong'>Mã dịch vụ</div>}
            required={providerType === PROVIDER_TYPE.VNPT_PARTNER}
            rules={[
              ...(providerType === PROVIDER_TYPE.VNPT_PARTNER
                ? [validateRequireInput('Mã dịch vụ không được bỏ trống')]
                : []),
              validateMaxLengthStr(100, 'Vui lòng nhập tối đa 100 ký tự'),
              validateCode('Mã dịch vụ không đúng định dạng', /^[a-zA-Z0-9]+$/)
            ]}
          >
            <Input
              placeholder='Nhập mã dịch vụ'
              maxLength={100}
              // Chặn dấu cách
              onKeyDown={e => {
                if (e.key === ' ') e.preventDefault()
              }}
            />
          </Form.Item>
        )}

        {/* Nhà sản xuất */}
        {classification === 'PHYSICAL' && <SelectManufacturer />}
        {/* Nhà cung cấp */}
        <Form.Item
          name='providerId'
          label={<div className='caption-12-medium text-text-neutral-strong'>Nhà cung cấp</div>}
          initialValue={isDeveloper ? user?.id : currentProviderDTO?.id}
          required={user?.portalType === 'ADMIN'}
          rules={[{ required: user?.portalType === 'ADMIN', message: 'Nhà cung cấp không được bỏ trống' }]}
        >
          <Select
            showSearch
            allowClear
            placeholder='Chọn nhà cung cấp'
            options={(() => {
              const options: { label: string; value: number }[] = []

              // Luôn thêm option từ providerDTO nếu có (để hiển thị đúng khi quay lại bước 1)
              if (currentProviderDTO?.id && currentProviderDTO?.name) {
                // Kiểm tra xem có phù hợp với search text không
                const matchesSearch =
                  !searchText || currentProviderDTO.name.toLowerCase().includes(searchText.toLowerCase())

                if (matchesSearch) {
                  options.push({
                    label: currentProviderDTO.name,
                    value: currentProviderDTO.id
                  })
                }
              }

              // Thêm các options từ API (tránh duplicate)
              if (providerList) {
                providerList.forEach((item: { label: string; value: number }) => {
                  // Chỉ thêm nếu chưa có trong options
                  if (!options.find(opt => opt.value === item.value)) {
                    options.push(item)
                  }
                })
              }

              return options
            })()}
            disabled={isDeveloper}
            notFoundContent={isFetching ? 'Đang tìm kiếm...' : 'Không có dữ liệu'}
            filterOption={false}
            loading={isFetching}
            onSearch={value => {
              // Reset providerList khi search
              if (!value) {
                setSearchText('')

                return
              }

              debouncedSearch(value)
            }}
            onChange={(value, option) => {
              // Get provider name from option and ensure it's a string
              const providerName = value ? String(Array.isArray(option) ? option?.[0]?.label : option?.label) : ''

              // Update providerDTO in Redux store
              dispatch(
                updateInfoCustomField({
                  providerDTO: value
                    ? {
                        id: value,
                        name: providerName
                      }
                    : undefined
                })
              )

              // Set provider name in form
              form.setFieldValue('providerName', providerName)
            }}
          />
        </Form.Item>
        {/* Mã loại dịch vụ */}
        {isShowProviderType && (
          <Form.Item
            name='serviceTypeId'
            normalize={trim}
            validateFirst
            label={<div className='caption-12-medium text-text-neutral-strong'>Mã loại dịch vụ</div>}
            rules={[
              validateRequireInput('Mã loại dịch vụ không được bỏ trống'),
              validateCode('Mã loại dịch vụ không đúng định dạng', /^[a-zA-Z0-9]+$/),
              validateNumberInput('Mã loại dịch vụ chỉ được gồm kí tự số')
            ]}
          >
            <Input placeholder='Nhập mã loại dịch vụ' maxLength={100} />
          </Form.Item>
        )}
      </div>

      <div className='grid grid-cols-2 gap-4'>
        {/* Hình ảnh sản phẩm */}
        <Form.Item
          name='icon'
          required
          rules={[
            {
              required: true,
              message:
                classification === 'PHYSICAL'
                  ? `Hình ảnh sản phẩm không được bỏ trống`
                  : `Ảnh đại diện dịch vụ không được bỏ trống`
            }
          ]}
        >
          <UploadFile
            name='icon'
            type='image'
            required
            externalForm={form}
            label={classification === 'PHYSICAL' ? `Hình ảnh sản phẩm` : `Ảnh đại diện dịch vụ`}
            allowImgType={['image/jpeg', 'image/png', 'image/jpg', 'image/webp']}
            maxFileSize={1}
            errorMessage='File được upload phải ở định dạng JPEG, PNG, JPG, WEBP'
            canUploadMany={classification === 'PHYSICAL'}
            acceptAllUrl
          />
        </Form.Item>
        {classification === 'PHYSICAL' ? (
          <Form.Item name='video'>
            {/* Video sản phẩm */}
            <UploadFile
              type='video'
              label='Video sản phẩm'
              canUploadMany
              maxFileSize={100}
              allowVideoType={[
                'video/mp4',
                'video/quicktime',
                'video/x-matroska',
                'video/x-msvideo',
                'video/x-flv',
                'video/x-ms-wmv',
                'video/mpeg',
                'video/mkv',
                'video/mov'
              ]}
              errorMessage='File được upload phải ở định dạng MP4, AVI, FLV, MKV, MOV, WMV, VOB'
            />
          </Form.Item>
        ) : (
          <Form.Item name='banner' required rules={[{ required: true, message: 'Banner không được bỏ trống' }]}>
            <UploadFile
              type='image'
              required
              label='Banner sản phẩm'
              allowImgType={['image/jpeg', 'image/webp']}
              maxFileSize={1}
              acceptAllUrl
            />
          </Form.Item>
        )}
      </div>

      {/* Hàng hóa Kỹ thuật số + Sản phẩm dịch vụ */}
      {classification !== 'PHYSICAL' && (
        <>
          <div className='grid grid-cols-4 gap-4'>
            {/* Ngôn ngữ hỗ trợ */}
            <Form.Item
              name='language'
              label={<div className='caption-12-medium text-text-neutral-strong'>Ngôn ngữ hỗ trợ</div>}
              tooltip='Chọn ít nhất một ngôn ngữ'
              required
              rules={[{ required: true, message: 'Vui lòng chọn ít nhất 1 ngôn ngữ' }]}
              initialValue={[]}
            >
              <Select
                options={languageOptions}
                placeholder='Chọn ngôn ngữ'
                mode='multiple'
                maxTagCount='responsive'
                maxTagPlaceholder={omittedValues => (
                  <Tooltip
                    styles={{ root: { pointerEvents: 'none' } }}
                    title={omittedValues.map(({ label }) => label).join(', ')}
                  >
                    <span>+{omittedValues.length}</span>
                  </Tooltip>
                )}
              />
            </Form.Item>
            {/* URL dịch vụ */}
            {!shouldHideUrlFields && (
              <Form.Item
                name='url'
                label={
                  <div className='caption-12-medium flex items-center gap-1 text-text-neutral-strong'>
                    <div>URL dịch vụ</div>
                    <Tooltip title='Ví dụ: https://example.com'>
                      <InfoCircleOutlined />
                    </Tooltip>
                  </div>
                }
                required
                rules={[
                  validateRequireInput('URL dịch vụ không được bỏ trống'),
                  validateLink('Sai định dạng URL dịch vụ')
                ]}
              >
                <Input placeholder='Nhập URL' maxLength={200} />
              </Form.Item>
            )}
            {/* Hiển thị URL dịch vụ */}
            <Form.Item
              name='urlServiceStatus'
              label={<div className='caption-12-medium text-text-neutral-strong'>Hiển thị URL dịch vụ</div>}
              initialValue={urlServiceStatusOptions[0].value}
            >
              <Select options={urlServiceStatusOptions} />
            </Form.Item>
            {/* Pre-order URL */}
            <Form.Item
              name='urlPreOrder'
              label={<div className='caption-12-medium text-text-neutral-strong'>Pre-order URL</div>}
              normalize={trim}
              rules={[validateLink('Sai định dạng Pre-order URL')]}
            >
              <Input placeholder='Nhập URL' />
            </Form.Item>
            {/* Đơn vị phát triển là Third party thì hiển thị */}
            <If condition={providerType === PROVIDER_TYPE.THIRD_PARTY && !shouldHideUrlFields}>
              <>
                {/* URL cài đặt */}
                <Form.Item
                  name='urlSetup'
                  label={<div className='caption-12-medium text-text-neutral-strong'>URL cài đặt</div>}
                  required
                  rules={[
                    validateRequireInput('URL cài đặt không được bỏ trống'),
                    validateLink('Sai định dạng URL cài đặt')
                  ]}
                >
                  <Input placeholder='Nhập URL' />
                </Form.Item>
                {/* Token URL cài đặt */}
                <Form.Item
                  name='tokenSPDV'
                  label={<div className='caption-12-medium text-text-neutral-strong'>Token URL cài đặt</div>}
                  required
                  rules={[
                    validateRequireInput('Token URL cài đặt không được bỏ trống'),
                    validateMaxLengthStr(100, 'Vui lòng nhập tối đa 100 ký tự')
                  ]}
                >
                  <Input placeholder='Nhập URL' maxLength={100} />
                </Form.Item>
              </>
            </If>
          </div>

          <Form.Item
            name='registerEcontract'
            valuePropName='checked'
            getValueProps={value => ({ checked: Boolean(value) })}
            normalize={value => (value ? 1 : 0)}
            initialValue={0}
            className='!mt-0'
          >
            <Checkbox>Khách hàng đăng ký hợp đồng điện tử</Checkbox>
          </Form.Item>
        </>
      )}

      {/* Hàng hóa vật lý - Mô tả sản phẩm */}
      {classification === 'PHYSICAL' && (
        <Form.Item
          name='description'
          label={<div className='caption-12-medium text-text-neutral-strong'>Mô tả sản phẩm</div>}
          required
          className='mt-4'
          rules={[
            { required: true, message: 'Mô tả sản phẩm không được bỏ trống' },
            validateMaxLengthEditor(1000, 'Mô tả sản phẩm không nhập quá 1000 ký tự')
          ]}
        >
          <CKEditorWrapper value={serviceDescription} onChange={setServiceDescription} maxLength={1000} />
        </Form.Item>
      )}
    </WrapSection>
  )
  // end region
}

ServiceGeneralInformation.craft = {
  displayName: 'Thông tin chung',
  props: {},
  rules: {
    canMoveIn: () => false
  },
  custom: {
    isDeletable: false,
    sectionDefault: true
  }
}
