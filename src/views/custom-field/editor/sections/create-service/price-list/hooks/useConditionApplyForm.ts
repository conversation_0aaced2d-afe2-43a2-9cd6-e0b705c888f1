import { useState } from 'react'

import { Form } from 'antd'

import type { DefaultOptionType } from 'antd/es/select'

// eslint-disable-next-line import/named
import { useLocationRegionForMultiple } from '@/hooks'
import type { IRenderAddress } from '@/types/personalTypes'

export interface IConditionApplyFormState {
  selectedProvince: number[] | null
  selectedWard: string[] | null
  renderAddress: IRenderAddress
  activeRenderAddress: number
}

/**
 * Custom hook for managing location selection form state
 * Handles province, ward and street selection with address rendering
 */
export const useConditionApplyForm = () => {
  const form = Form.useFormInstance()

  const {
    updateAddress,
    provinceList,
    wardList,
    streetList,
    loadingProvince,
    loadingStreet,
    loadingWard,
    setProvinceSearch,
    setWardSearch,
    setStreetSearch,
    lastIdValue
  } = useLocationRegionForMultiple()

  const extractIds = (input: any) =>
    Array.isArray(input) && input.length > 0 && typeof input[0] === 'object' && 'id' in input[0]
      ? input.map((item: any) => item.id)
      : input

  // hàm update lại thông tin có sẵn
  const updateValueFromForm = (province: any, ward: any, street: any) => {
    let provinceIdSelect = null
    let wardSelect = null
    let streetSelect = null

    if (province) provinceIdSelect = extractIds(province)
    if (ward) wardSelect = extractIds(ward)
    if (street) streetSelect = extractIds(street)

    setState(prev => ({
      ...prev,
      selectedProvince: provinceIdSelect || [],
      selectedWard: wardSelect || []
    }))

    updateAddress('provinceId', provinceIdSelect, null, wardSelect, streetSelect)
  }

  const [state, setState] = useState<IConditionApplyFormState>({
    selectedProvince: [],
    selectedWard: [],
    renderAddress: {
      province: '',
      ward: '',
      street: '',
      homeNumber: ''
    },
    activeRenderAddress: 1
  })

  const setNewProvinceValue = (
    value: number[],
    option: DefaultOptionType | DefaultOptionType[] | undefined,
    namePrefix: (string | number)[] | undefined
  ) => {
    const getAllValue = form.getFieldValue([namePrefix])
    const getWardIds = getAllValue?.wardId?.filter((e: any) => value.includes(Number(lastIdValue(e?.id)))) || []
    const getStreetIds = getAllValue?.streetId?.filter((e: any) => value.includes(Number(lastIdValue(e?.id)))) || []

    setState(prev => ({
      ...prev,
      selectedProvince: value,
      selectedWard: getWardIds?.map((e: any) => e.id),
      renderAddress: {
        ...prev.renderAddress,
        province: Array.isArray(option)
          ? option.map(opt => String(opt.label || '')).join(', ')
          : String(option?.label || ''),
        ward: '',
        street: ''
      },
      activeRenderAddress: prev.activeRenderAddress + 1
    }))

    setProvinceSearch('')

    updateAddress(
      'provinceId',
      value,
      null,
      getWardIds?.map((e: any) => e.id),
      getStreetIds?.map((e: any) => e.id)
    )

    const fieldUpdate = {
      ...getAllValue,
      wardId: getWardIds,
      streetId: getStreetIds
    }

    form.setFieldValue(namePrefix, fieldUpdate)
  }

  const setNewWardValue = (value: string[], namePrefix: (string | number)[] | undefined) => {
    const getAllValue = form.getFieldValue([namePrefix])
    const wardValueSelected = value?.map((item: string) => Number(lastIdValue(item))) || []

    const getStreetIds =
      getAllValue?.streetId?.filter((e: any) => wardValueSelected.includes(Number(lastIdValue(e?.id)))) || []

    const wardLabels = value.map(v => wardList.find((item: DefaultOptionType) => item.value === v)?.label || '')

    setState(prev => ({
      ...prev,
      selectedWard: value,
      renderAddress: {
        ...prev.renderAddress,
        ward: wardLabels.join(', '),
        street: ''
      },
      activeRenderAddress: prev.activeRenderAddress + 1
    }))

    setWardSearch('')

    updateAddress(
      'wardId',
      value,
      null,
      null,
      getStreetIds?.map((e: any) => e.id)
    )

    const fieldUpdate = {
      ...getAllValue,
      streetId: getStreetIds
    }

    form.setFieldValue(namePrefix, fieldUpdate)
  }

  const setNewStreetValue = (value: number[]) => {
    const streetLabels = value.map(v => streetList.find((item: DefaultOptionType) => item.value === v)?.label || '')

    setState(prev => ({
      ...prev,
      renderAddress: {
        ...prev.renderAddress,
        street: streetLabels.join(', ')
      },
      activeRenderAddress: prev.activeRenderAddress + 1
    }))

    setStreetSearch('')

    updateAddress('streetId', value)
  }

  return {
    ...state,
    provinceList,
    wardList,
    streetList,
    loadingProvince,
    loadingStreet,
    loadingWard,
    setNewProvinceValue,
    setNewWardValue,
    setNewStreetValue,
    setProvinceSearch,
    setWardSearch,
    setStreetSearch,
    updateValueFromForm
  }
}
