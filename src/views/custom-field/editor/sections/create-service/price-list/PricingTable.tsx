'use client'

import React, { createContext, useContext, useEffect, useMemo, useState } from 'react'

import { useParams, usePathname, useRouter, useSearchParams } from 'next/navigation'

import { HolderOutlined, MoreOutlined } from '@ant-design/icons'

import type { DragEndEvent, DragOverEvent } from '@dnd-kit/core'
import { closestCenter, DndContext, PointerSensor, useSensor, useSensors } from '@dnd-kit/core'
import { arrayMove, SortableContext, useSortable, verticalListSortingStrategy } from '@dnd-kit/sortable'

import { useMutation, useQuery } from '@tanstack/react-query'

import type { TableProps } from 'antd'
import { Button, Form, Input, Modal, Pagination, Popover, Radio, Select, Switch, Tag } from 'antd'
import { useDispatch } from 'react-redux'

import { styled } from 'styled-components'

import Pricing from '@/models/Pricing'
import DevService from '@/models/DevService'
import CommonService from '@/models/CommonService'
import { useUser } from '@/hooks'
import { showMessage } from '@/components/notification/NotificationProvider'
import { updateInfoCustomField } from '@/redux-store/slices/customFieldSlice'
import { FilterModal } from '@/components/filter'
import { EmptyData } from '@views/management/current/EmptyData'

import {
  ExpandIconWrapper,
  PackageNameCell,
  PackageNameContent,
  StyledExpandIcon,
  StyledTable,
  StyledTag
} from './PriceListStyled'
import { TagIcon } from '@/assets/builder-icons'
import type { PricingPackage } from '@/types/pricing'
import { pageSizeOptions } from '@/views/product-catalog/constants/constants'
import { APPROVAL_STATUS, CUSTOMER_TYPE_OPTION, DISPLAY_STATUS_OPTION } from '@/constants/custom-field'
import { formatSpecialDigits } from '@/constants/common'
import { generatePlanName, PRICING_TYPE } from './utils'
import { convertPricingList } from '@/utils/custom-field/service'

const DragHandle = styled.div`
  cursor: move;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  inline-size: 20px;
  block-size: 20px;
  color: #999;

  &:hover {
    color: #666;
  }
`

interface DragState {
  active: string
  over?: string
}

const DragContext = createContext<DragState>({ active: '', over: undefined })

interface RowProps extends React.HTMLAttributes<HTMLTableRowElement> {
  'data-row-key': string
}

export const ApprovalStatusTag = styled(Tag)<{ $status: PricingPackage['approvalStatus'] }>`
  border: none;

  ${props => {
    switch (props.$status) {
      case 'approved':
        return `
          background-color: #D2F9E2;
          color: #07945F;
        `
      case 'awaiting_approval':
        return `
          background-color: #CDE4FE;
          color: #2A6AEB;
        `
      case 'rejected':
        return `
          background-color: #FFE1E0;
          color: #D82D2A;
        `
      case 'unapproved':
        return `
          background-color: #FFF6B3;
          color: #E28800;
        `
      default:
        return ''
    }
  }}
`

export const getApprovalStatusText = (status: PricingPackage['approvalStatus']) => {
  switch (status) {
    case 'approved':
      return 'Đã duyệt'
    case 'awaiting_approval':
      return 'Chờ duyệt'
    case 'rejected':
      return 'Từ chối'
    case 'unapproved':
      return 'Chưa duyệt'
    default:
      return ''
  }
}

const StyledTableRow = styled.tr`
  &:hover {
    .three-dot-icon {
      display: block !important;
    }
  }
`

const ActionButton = styled.div`
  padding-block: 8px;
  padding-inline: 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;

  &:hover {
    background: #f5f5f5;
  }
`

const ThreeDotWrapper = styled.div`
  position: absolute;
  inset-inline-end: 16px;
  inset-block-start: 50%;
  transform: translateY(-50%);

  .anticon {
    font-size: 16px;
    color: #999;
    cursor: pointer;
    padding: 4px;

    &:hover {
      color: #666;
    }
  }
`

const TableCellWrapper = styled.div`
  position: relative;
  inline-size: 100%;
  block-size: 100%;
  min-block-size: 40px;
  display: flex;
  align-items: center;
`

const TableRow = ({ children, ...props }: RowProps) => {
  const dragState = useContext(DragContext)

  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id: props['data-row-key']?.toString()
  })

  const style: React.CSSProperties = {
    ...props.style,
    transform: transform ? `translate3d(0, ${transform.y}px, 0)` : undefined,
    transition,
    ...(isDragging ? { position: 'relative', zIndex: 9999, backgroundColor: '#fafafa' } : {}),
    ...(dragState.over === props['data-row-key']
      ? {
          borderTop: '2px dashed #1677ff',
          backgroundColor: '#f0f8ff'
        }
      : {})
  }

  return (
    <StyledTableRow {...props} ref={setNodeRef} style={style} {...attributes}>
      {React.Children.map(children, child => {
        if (!React.isValidElement(child)) return child

        if (child.key === 'key-0') {
          return React.cloneElement(child as React.ReactElement<any>, {
            children: (
              <div className='justify-between' style={{ display: 'flex', alignItems: 'center' }}>
                <div style={{ width: '20px' }}>
                  <DragHandle {...listeners}>
                    <HolderOutlined />
                  </DragHandle>
                </div>
                <div>{child}</div>
                <div style={{ width: '20px' }} />
              </div>
            )
          })
        }

        return child
      })}
    </StyledTableRow>
  )
}

/**
 *
 * @param props
 * @returns
 */
export const PricingTable = React.memo((props: any) => {
  const { user } = useUser()

  const dispatch = useDispatch()

  const { setIsAddPrice, isCreate, pricingList, setPricingList, setIdPackageEdit } = props

  // Khai báo điều hướng
  const router = useRouter()

  // Lấy đường dẫn
  const pathName = usePathname()

  // Lấy tham số truyền vào từ URL
  const searchParams = useSearchParams()

  const isUpdate = pathName.includes('/update')
  const isDetail = pathName.includes('/detail')
  const form = Form.useFormInstance()

  const { id } = useParams<{ id: string }>()

  const [packages, setPackages] = useState<PricingPackage[]>([])
  const [deletedPricing, setDeletedPricing] = useState<number[]>([])
  const [expandedRowKeys, setExpandedRowKeys] = useState<any[]>([])
  const [selectedRows, setSelectedRows] = useState<(string | number)[]>([])
  const [dragState, setDragState] = useState<DragState>({ active: '', over: undefined })

  // Modal states
  const [isApprovalModalOpen, setIsApprovalModalOpen] = useState(false)
  const [isDefaultModalOpen, setIsDefaultModalOpen] = useState(false)
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false)
  const [selectedPackage, setSelectedPackage] = useState<PricingPackage | null>(null)
  const [isRecommendModalOpen, setIsRecommendModalOpen] = useState(false)

  const [hoveredId, setHoveredId] = useState<number | string | null | undefined>(null)

  const [pricingName, setPricingName] = useState<string | null>(null)

  const portal = user?.portalType?.toLowerCase()

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 1,
        delay: 100
      }
    })
  )

  const [filterParam, setFilterParam] = useState({})
  const [page, setPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)

  const { data, refetch } = useQuery({
    queryKey: ['pricingList', id, portal, pricingName, filterParam],
    queryFn: async () => {
      if (!portal) return []

      const res = await Pricing.getListPricingForService(portal)(Number(id), {
        name: pricingName,
        ...filterParam
      })

      const mappedData: PricingPackage[] = Array.isArray(res)
        ? res.map((item: any) => ({
            ...item,
            id: item.id,
            name: item.pricingName,
            isDefault: item.isDefault,
            isActive: item.isSold === 'NOT_SOLD_YET',
            isDisplayed: item.status === 'VISIBLE',
            isRecommended: item.recommendedStatus === 'RECOMMENDED',
            approvalStatus: item.approve?.toLowerCase() || 'draft',
            lastUpdated: item.modifiedAt || new Date(item.createdAt).toLocaleString(),
            pricingStrategies: item.pricingStrategies?.map((strategy: any) => ({
              id: strategy.id,
              pricingId: item.id,
              name:
                strategy?.planName ||
                generatePlanName({ strategy: strategy, isOneTime: item.isOneTime ?? PRICING_TYPE.ONE_TIME }),
              pricingModel: strategy?.pricingPlan,
              isDisplayed: strategy.displayStatus === 1,
              isRecommended: false,
              approvalStatus: strategy.approveStatus?.toLowerCase() || 'draft',
              lastUpdated: new Date(strategy.createdAt).toLocaleString(),
              defaultCircle: strategy?.defaultCircle,
              ...strategy
            })),
            createdBy: item?.createdBy
          }))
        : []

      if (isUpdate) {
        const initPricingList = form.getFieldValue('pricingReqDTO') || []

        const data = [...mappedData, ...initPricingList]

        const pricingList = Array.from(new Map(data.map(item => [item.id, item])).values()).map((e: any) => ({
          ...e,
          pricingStrategies: e.pricingStrategies.map((strategy: any) => ({
            ...strategy,
            pricingId: e.id
          }))
        }))

        setPricingList(pricingList)

        setPackages(convertPricingList(pricingList))
      }

      return mappedData || []
    },
    enabled: !!id && !!portal
  })

  const pagedData = useMemo(() => {
    const start = (page - 1) * pageSize
    const end = start + pageSize

    return data?.slice(start, end) || []
  }, [data, page, pageSize])

  useEffect(() => {
    if (isDetail) {
      setPackages(pagedData as PricingPackage[])
    }
  }, [pagedData, isDetail])

  useEffect(() => {
    setPage(1)
  }, [pricingName, filterParam])

  const approveProduct = useMutation({
    mutationKey: ['approveProduct'],
    mutationFn: DevService.approveProduct,
    onSuccess: () => {
      showMessage.success('Gửi yêu cầu phê duyệt thành công!')
      setIsApprovalModalOpen(false)
      setSelectedRows([])
      refetch()
    },
    onError: () => {
      showMessage.error('Gửi yêu cầu phê duyệt thất bại!')
    }
  })

  const adminApproveProduct = useMutation({
    mutationKey: ['adminApproveProduct'],
    mutationFn: DevService.adminApproveProduct,
    onSuccess: () => {
      showMessage.success('Phê duyệt thành công!')
      setIsApprovalModalOpen(false)
      setSelectedRows([])
      refetch()
    },
    onError: () => {
      showMessage.error('Phê duyệt thất bại!')
    }
  })

  const adminApproveMultiProduct = useMutation({
    mutationKey: ['adminApproveProduct'],
    mutationFn: DevService.adminApproveMultiProduct,
    onSuccess: () => {
      showMessage.success('Phê duyệt thành công!')
      setIsApprovalModalOpen(false)
      setSelectedRows([])
      refetch()
    },
    onError: () => {
      showMessage.error('Phê duyệt thất bại!')
    }
  })

  const deleteProduct = useMutation({
    mutationKey: ['deletdProduct'],
    mutationFn: DevService.deleteProduct(!user.portalType ? 'admin' : user.portalType === 'ADMIN' ? 'admin' : 'dev'),
    onSuccess: () => {
      showMessage.success('Xóa gói dịch vụ thành công!')
      setIsDeleteModalOpen(false)
      refetch()
    },
    onError: (res: any) => {
      if (res?.errorCode === 'error.pricing.still.used') {
        showMessage.error('Không thể xóa gói do đang có thuê bao tồn tại.')
      } else {
        showMessage.error('Xóa gói thất bại!')
      }
    }
  })

  const deleteMultiProduct = useMutation({
    mutationKey: ['deletdProducts'],
    mutationFn: CommonService.deletePricing(!user.portalType ? 'admin' : user.portalType === 'ADMIN' ? 'admin' : 'dev'),
    onSuccess: () => {
      showMessage.success('Xóa gói dịch vụ thành công!')
      setIsDeleteModalOpen(false)
      refetch()
    },
    onError: (res: any) => {
      if (res?.errorCode === 'error.pricing.still.used') {
        showMessage.error('Không thể xóa gói do đang có thuê bao tồn tại.')
      } else {
        showMessage.error('Xóa gói thất bại!')
      }
    }
  })

  const settingPricingDefault = useMutation({
    mutationKey: ['settingDefault'],
    mutationFn: CommonService.changePricingDefault(
      !user.portalType ? 'admin' : user.portalType === 'ADMIN' ? 'admin' : 'dev'
    ),
    onSuccess: () => {
      showMessage.success('Thay đổi gói cước mặc định thành công!')
      setIsApprovalModalOpen(false)
      refetch()
    },
    onError: () => {
      showMessage.error('Thay đổi gói cước mặc định thất bại!')
    }
  })

  const settingPricingDisplay = useMutation({
    mutationKey: ['settingPricingDisplay'],
    mutationFn: CommonService.changePricingDisplay(
      !user.portalType ? 'admin' : user.portalType === 'ADMIN' ? 'admin' : 'dev'
    ),
    onSuccess: () => {
      showMessage.success('Cập nhật trạng thái hiển thị thành công')
      refetch()
    },
    onError: () => {
      showMessage.error('Cập nhật trạng thái hiển thị thất bại')
    }
  })

  const settingMultipPlanDisplay = useMutation({
    mutationKey: ['settingMultipPlanDisplay'],
    mutationFn: CommonService.changeMultipPlanDisplay(
      !user.portalType ? 'admin' : user.portalType === 'ADMIN' ? 'admin' : 'dev'
    ),
    onSuccess: () => {
      showMessage.success('Cập nhật trạng thái của chu kỳ thành công')
      refetch()
    },
    onError: () => {
      showMessage.error('Cập nhật trạng thái của chu kỳ thất bại')
    }
  })

  const settingPricingPriorityAndRecommended = useMutation({
    mutationKey: ['settingPricingPriorityAndRecommended'],
    mutationFn: CommonService.changePricingPriorityAndRecommended(
      !user.portalType ? 'admin' : user.portalType === 'ADMIN' ? 'admin' : 'dev'
    ),
    onSuccess: () => {
      showMessage.success('Cập nhật thành công!')
      refetch()
    },
    onError: () => {
      showMessage.error('Cập nhật thất bại!')
    }
  })

  const handleToggleDisplay = (displayed: boolean, infor: any) => {
    if (!isCreate && !isUpdate) {
      if (infor?.pricingModel) {
        settingMultipPlanDisplay.mutate({
          id: infor?.id,
          status: infor?.isDisplayed ? 0 : 1
        })
      } else {
        settingPricingDisplay.mutate({
          id: infor?.id,
          status: infor?.isDisplayed ? 'INVISIBLE' : 'VISIBLE'
        })
      }
    } else {
      // Logic mới cho chế độ tạo mới
      let updatedList = []

      if (!!infor.draftPricingId || !!infor.pricingId) {
        updatedList = pricingList.map((item: any) => {
          if ((!!item.draftId && item.draftId === infor.draftPricingId) || (!!item.id && item.id === infor.pricingId)) {
            const updatePricingStrategies = item.pricingStrategies.map((strategy: any) => {
              if (
                (!!strategy.draftId && strategy.draftId === infor.draftId) ||
                (!!strategy.id && strategy.id === infor.id)
              ) {
                return {
                  ...strategy,
                  isDisplayed: displayed
                }
              }

              return strategy
            })

            return { ...item, pricingStrategies: updatePricingStrategies }
          }

          return item
        })
      } else {
        updatedList = pricingList.map((item: any) => {
          if ((!!item.draftId && item.draftId === infor.draftId) || (!!item.id && item.id === infor.id)) {
            return {
              ...item,
              isDisplayed: displayed
            }
          }

          return item
        })
      }

      setPricingList(updatedList)
      setPackages(updatedList)
      form.setFieldsValue({
        pricingReqDTO: updatedList
      })
    }
  }

  const handleToggleRecommended = (infor: any) => {
    if (!isCreate && !isUpdate) {
      const updatePricing = packages.map((pkg, index) => {
        if (pkg.id === infor?.id) {
          return { displayedOrder: index + 1, id: pkg?.id, recommendedStatus: 'RECOMMENDED' }
        } else {
          return { displayedOrder: index + 1, id: pkg?.id, recommendedStatus: 'UN_RECOMMENDED' }
        }
      })

      settingPricingPriorityAndRecommended.mutate({
        id: Number(id),
        detail: updatePricing
      })
    } else {
      const updatedList = pricingList.map((item: any) => {
        if (item?.id) {
          return {
            ...item,
            isRecommended: item.id === infor?.id
          }
        }

        if (item?.draftId) {
          return {
            ...item,
            isRecommended: item.draftId === infor?.draftId ? 1 : 0
          }
        }

        return item
      })

      setPricingList(updatedList)
      setPackages(updatedList)
    }
  }

  const getRecordKey = (record: PricingPackage): string =>
    isCreate || isUpdate ? record?.draftId?.toString() || record?.id?.toString() : record?.id?.toString()

  const handleExpand = (record: PricingPackage) => {
    const id = isCreate || isUpdate ? record?.draftId || record?.id : record?.id
    const actualKey = id?.toString()

    if (!actualKey) return

    setExpandedRowKeys(prev =>
      prev.includes(actualKey) ? prev.filter(keyId => keyId !== actualKey) : [...prev, actualKey]
    )
  }

  const handleDragEnd = ({ active, over }: DragEndEvent) => {
    if (over && active.id !== over.id) {
      const oldIndex = packages.findIndex(item =>
        isUpdate
          ? item?.draftId?.toString() === active.id || item?.id?.toString() === active.id
          : item?.id?.toString() === active.id
      )

      const newIndex = packages.findIndex(item =>
        isUpdate
          ? item?.draftId?.toString() === over.id || item?.id?.toString() === over.id
          : item?.id?.toString() === over.id
      )

      const reorderedItems = arrayMove(packages, oldIndex, newIndex)

      // Update pricingOrder for each item
      const updatedItems = reorderedItems.map((item, index) => ({
        displayedOrder: index + 1,
        id: item?.id,
        recommendedStatus: item?.isRecommended ? 'RECOMMENDED' : 'UN_RECOMMENDED'
      }))

      if (!isCreate && !isUpdate) {
        settingPricingPriorityAndRecommended.mutate({
          id: Number(id),
          detail: updatedItems
        })
      }

      const newDate = reorderedItems.map((item, index) => ({
        ...item,
        pricingOrder: index + 1
      }))

      setPackages(newDate)
      setPricingList(newDate)

      // TODO: Add API call to update pricing order
    }

    setDragState({ active: '', over: undefined })
  }

  const handleDragOver = ({ active, over }: DragOverEvent) => {
    setDragState({
      active: active.id?.toString(),
      over: over?.id?.toString()
    })
  }

  const handleActionClick = (action: string, record: PricingPackage) => {
    setSelectedPackage(record)

    switch (action) {
      case 'approve':
        setIsApprovalModalOpen(true)
        break
      case 'setDefault':
        setIsDefaultModalOpen(true)
        break
      case 'delete':
        setIsDeleteModalOpen(true)

        break
    }
  }

  const renderActionPopover = (record: PricingPackage) => (
    <div style={{ minWidth: '200px' }}>
      {record?.approvalStatus !== 'approved' &&
        record?.approvalStatus !== 'awaiting_approval' &&
        !isCreate &&
        !isUpdate && (
          <ActionButton onClick={() => handleActionClick('approve', record)} style={{ color: '#2A6AEB' }}>
            <i className='onedx-send ' />
            Gửi yêu cầu phê duyệt
          </ActionButton>
        )}
      {record?.approvalStatus === 'approved' && record?.isDisplayed && (
        <ActionButton onClick={() => handleActionClick('setDefault', record)} style={{ color: '#2A6AEB' }}>
          <i className='onedx-setting tẽ' />
          Thiết lập gói mặc định
        </ActionButton>
      )}
      <ActionButton onClick={() => handleActionClick('delete', record)} style={{ color: '#ff4d4f' }}>
        <i className='onedx-delete' />
        Xoá
      </ActionButton>
    </div>
  )

  // Xử lý điều hướng đến gói tương ứng
  const handlePricingRedirect = (pricingId: any) => {
    // Trường hợp đang ở màn tạo - sửa dịch vụ
    if (pathName.includes('update') || pathName.includes('create')) {
      // Bật màn tạo - sửa gói
      setIsAddPrice((prev: boolean) => !prev)
      setIdPackageEdit(pricingId)
    }
    // Trường hợp đang ở màn chi tiết dịch vụ
    else {
      // Danh sách tham số mới của URL
      const newParams = new URLSearchParams(searchParams)

      // Thay đổi tham số type của params ở URL
      newParams.set('pricingId', String(pricingId))

      // Điều hướng URL theo tham số mới
      router.push(`${pathName}?${newParams}`)
    }
  }

  const listFilter: any = [
    {
      value: 'customerTypeCode',
      label: 'Đối tượng khách hàng',
      type: 'singleSelect',
      listOptions: CUSTOMER_TYPE_OPTION,
      isShow: true,
      typeSearch: 'local'
    },
    {
      value: 'displayStatus',
      label: 'Trạng thái hiển thị',
      type: 'singleSelect',
      listOptions: DISPLAY_STATUS_OPTION,
      isShow: true,
      typeSearch: 'local'
    },
    {
      value: 'approveStatus',
      label: 'Trạng thái duyệt',
      type: 'singleSelect',
      listOptions: APPROVAL_STATUS,
      isShow: true,
      typeSearch: 'local'
    }
  ]

  const mainColumns: TableProps<PricingPackage>['columns'] = [
    {
      title: 'STT',
      dataIndex: 'pricingOrder',
      key: 'pricingOrder',
      render: (_: any, _record: any, index: number) => <div className='pl-5'>{(page - 1) * pageSize + index + 1}</div>,
      width: 80
    },
    {
      title: 'Tên gói',
      dataIndex: 'name',
      key: 'name',
      width: 400,
      render: (text: string, record: PricingPackage) => (
        <PackageNameCell>
          <PackageNameContent>
            <div className='name-section'>
              {record?.pricingStrategies && record?.pricingStrategies.length > 0 && (
                <ExpandIconWrapper
                  onClick={e => {
                    e.stopPropagation()
                    handleExpand(record)
                  }}
                >
                  <StyledExpandIcon className='text-black' $expanded={expandedRowKeys.includes(getRecordKey(record))} />
                </ExpandIconWrapper>
              )}
              <span
                className='name-text cursor-pointer hover:text-primary'
                onClick={() => {
                  const redirectId = isCreate || isUpdate ? record?.draftId || record?.id : record?.id

                  if (redirectId) handlePricingRedirect(redirectId)
                }}
              >
                {text}
              </span>
            </div>
            <div className='tags-section'>
              {record.isDefault && <StyledTag $type='default'>Mặc định</StyledTag>}
              {record.isActive && <StyledTag $type='active'>Đang bán</StyledTag>}
            </div>
          </PackageNameContent>
        </PackageNameCell>
      )
    },
    ...(!isDetail
      ? [
          {
            title: 'Loại mô hình định giá',
            dataIndex: 'isOneTime',
            key: 'isOneTime',
            render: (value: number) => (value ? 'Cho thuê' : 'Bán đứt'),
            width: 180
          }
        ]
      : []),
    {
      title: 'Hiển thị',
      dataIndex: 'isDisplayed',
      key: 'isDisplayed',
      width: 100,
      render: (isDisplayed: boolean, record: PricingPackage) => (
        <>
          {isCreate || isUpdate ? (
            <Switch
              checked={isDisplayed}
              onChange={checked => handleToggleDisplay(checked, record)}
              aria-label={`Hiển thị ${record.name}`}
              style={{ backgroundColor: isDisplayed ? '#14C780' : undefined }}
            />
          ) : (
            <Switch
              checked={isDisplayed}
              onChange={checked => handleToggleDisplay(checked, record)}
              aria-label={`Hiển thị ${record.name}`}
              style={{ backgroundColor: isDisplayed ? '#14C780' : undefined }}
              disabled={
                record?.defaultCircle === 'YES' || record?.approvalStatus !== 'approved' || record?.isDefault === true
              }
            />
          )}
        </>
      )
    },
    ...(isDetail
      ? [
          {
            title: 'Trạng thái duyệt',
            dataIndex: 'approvalStatus',
            key: 'approvalStatus',
            width: 150,
            render: (status: PricingPackage['approvalStatus']) => (
              <ApprovalStatusTag $status={status}>{getApprovalStatusText(status)}</ApprovalStatusTag>
            )
          },
          {
            title: 'Thời gian cập nhật',
            dataIndex: 'lastUpdated',
            key: 'lastUpdated',
            width: 180
          }
        ]
      : []),
    {
      title: 'Khuyên dùng',
      dataIndex: 'isRecommended',
      key: 'isRecommended',
      width: 150,
      align: 'center',
      render: (isRecommended: boolean, record: PricingPackage) => (
        <Radio
          className='circle-checkbox'
          checked={isRecommended}
          onChange={() => {
            setSelectedPackage(record)
            setIsRecommendModalOpen(true)
          }}
        />
      )
    },
    {
      key: 'actions',
      width: 50,
      render: (_: any, record: PricingPackage) =>
        !record?.isDefault && (
          <TableCellWrapper>
            <ThreeDotWrapper
              onMouseEnter={() => {
                const hoveredId = isCreate || isUpdate ? record?.draftId || record?.id : record?.id

                setHoveredId(hoveredId)
              }}
              // onMouseLeave={() => setHoveredId(null)}
              className='three-dot-icon'
              style={{ display: 'none' }}
            >
              <Popover placement='bottomRight' content={() => renderActionPopover(record)} trigger='click'>
                <MoreOutlined />
              </Popover>
            </ThreeDotWrapper>
          </TableCellWrapper>
        )
    }
  ]

  const subColumns = mainColumns.map(column => {
    if (
      column.key === 'pricingOrder' ||
      column.key === 'isOneTime' ||
      column.key === 'lastUpdated' ||
      column.key === 'isRecommended' ||
      column.key === 'actions'
    ) {
      return {
        ...column,
        render: () => <></> // Không set width: 0
      }
    }

    if (column.key === 'name') {
      return {
        ...column,
        title: '',
        width: 400,
        render: (text: string, record: any) => (
          <PackageNameCell>
            <PackageNameContent>
              <div className='name-section pl-5'>
                <span className='name-text'>{text || record?.planName}</span>
              </div>
            </PackageNameContent>
          </PackageNameCell>
        )
      }
    }

    if (column.key === 'isDisplayed') {
      return {
        ...column,
        render: (isDisplayed: boolean, record: PricingPackage) => (
          <>
            {isCreate ? (
              <Switch
                checked={isDisplayed}
                onChange={checked => handleToggleDisplay(checked, record)}
                aria-label={`Hiển thị ${record.name}`}
                style={{ backgroundColor: isDisplayed ? '#14C780' : undefined }}
                disabled={record?.defaultCircle === 'YES'}
              />
            ) : (
              <Switch
                checked={isDisplayed}
                onChange={checked => handleToggleDisplay(checked, record)}
                aria-label={`Hiển thị ${record.name}`}
                style={{ backgroundColor: isDisplayed ? '#14C780' : undefined }}
                disabled={record?.defaultCircle === 'YES'}
              />
            )}
          </>
        )
      }
    }

    return {
      ...column,
      title: ''
    }
  })

  // Kiểm tra các gói được chọn có bao gồm trạng thái Chờ duyệt không
  const hasAwaitingApprovalPackages = () => {
    if (!selectedRows?.length) return false

    const selectedPackages = packages.filter(pkg => {
      return selectedRows.includes(pkg.id)
    })

    return selectedPackages.some(pkg => {
      return pkg.approvalStatus === 'awaiting_approval' || pkg.approvalStatus === 'approved'
    })
  }

  const navigateCreatePricing = () => {
    router.push(`/product-catalog/pricing/create/${id}`)
  }

  const content = (
    <div className='flex flex-col'>
      {!isCreate && (
        <Button
          disabled={hasAwaitingApprovalPackages()}
          color='primary'
          variant='text'
          onClick={() => setIsApprovalModalOpen(true)}
          className='flex justify-start'
        >
          <i className='onedx-send ' /> Yêu cầu phê duyệt
        </Button>
      )}
      <Button color='danger' variant='text' onClick={() => setIsDeleteModalOpen(true)} className='flex justify-start'>
        <i className='onedx-delete' /> Xoá
      </Button>
    </div>
  )

  return (
    <div className='flex w-full flex-col'>
      <div className='mb-4 flex justify-between'>
        <span className='text-base font-semibold'>Bảng giá</span>
        <Button
          type='primary'
          onClick={() => {
            if (isDetail) {
              navigateCreatePricing()
            } else {
              dispatch(
                updateInfoCustomField({
                  isServiceCreatePricing: true
                })
              )
              setIsAddPrice((prev: boolean) => !prev)
            }
          }}
        >
          <span className='mt-0.5'>
            <i className='onedx-add pt-1 font-bold' />
          </span>
          {isDetail ? 'Tạo gói dịch vụ' : 'Thêm bảng giá'}
        </Button>
      </div>

      {isDetail && (
        <div className='flex justify-between gap-4'>
          <Input
            prefix={<i className='onedx-search size-5 text-gray-6' />}
            className='my-4 h-10'
            onChange={e => {
              setPricingName(formatSpecialDigits(e.target.value))
              refetch()
            }}
            placeholder='Tìm kiếm theo tên gói dịch vụ'
          />
          <FilterModal
            listFilter={listFilter}
            filterParam={filterParam}
            changeFilterParam={value => {
              setFilterParam(value)
              refetch()
            }}
            disableAutofill
            method='post'
            startPage={0}
            icon='onedx-filter-traffic'
          />
        </div>
      )}
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={handleDragEnd}
        onDragOver={handleDragOver}
      >
        <DragContext.Provider value={dragState}>
          <SortableContext items={packages?.map(obj => getRecordKey(obj))} strategy={verticalListSortingStrategy}>
            <StyledTable<PricingPackage>
              className='custom-pricing-table'
              columns={mainColumns}
              dataSource={isCreate ? pricingList : packages}
              rowKey={obj => getRecordKey(obj)}
              onRow={() => ({
                onClick: e => {
                  // Ngăn row expansion khi click checkbox
                  if ((e.target as HTMLElement).closest('.ant-checkbox-wrapper')) {
                    e.stopPropagation()
                  }
                }
              })}
              pagination={false}
              rowSelection={{
                columnWidth: 140,
                selectedRowKeys: selectedRows,
                onChange: (selectedRowKeys: any) => {
                  setSelectedRows(selectedRowKeys)
                },
                onSelectAll: (selected, selectedRows) => {
                  if (selected) {
                    const allIds = selectedRows.reduce((acc: (string | number)[], pkg) => {
                      acc.push(pkg.id)

                      if (pkg.pricingStrategies) {
                        pkg.pricingStrategies.forEach(strategy => {
                          acc.push(strategy.id)
                        })
                      }

                      return acc
                    }, [])

                    setSelectedRows(allIds)
                  } else {
                    setSelectedRows([])
                  }
                }
              }}
              components={{
                body: {
                  row: TableRow
                }
              }}
              expandable={{
                expandedRowKeys,
                expandIconColumnIndex: -1,
                showExpandColumn: false,
                expandRowByClick: false,
                onExpand: (_, record) => handleExpand(record),
                expandedRowRender: (record: PricingPackage) => {
                  if (!record.pricingStrategies) return null

                  return (
                    <StyledTable<any>
                      columns={subColumns}
                      dataSource={record.pricingStrategies}
                      rowKey='id'
                      pagination={false}
                      className={`sub-items-table ${isDetail ? 'pl-12' : ''}`}
                      showHeader={false}
                    />
                  )
                }
              }}
              locale={{ emptyText: <EmptyData label='Không có dữ liệu' /> }}
            />
          </SortableContext>
        </DragContext.Provider>
      </DndContext>

      {((pricingList && pricingList.length > 0) || (packages && packages.length > 0)) && (
        <>
          <div className='my-4 flex items-center justify-between gap-4'>
            <div className='flex items-center gap-4'>
              <span>Đã chọn: {selectedRows?.length}</span>
              {!isUpdate ? (
                <Popover content={content} trigger='click'>
                  <Button disabled={selectedRows?.length === 0} color='primary' variant='outlined'>
                    Tùy chọn <i className='onedx-chevron-down size-4' />
                  </Button>
                </Popover>
              ) : (
                <Button
                  disabled={selectedRows?.length === 0}
                  color='danger'
                  variant='outlined'
                  onClick={() => setIsDeleteModalOpen(true)}
                >
                  <i className='onedx-delete' /> Xoá
                </Button>
              )}
            </div>
            <Pagination
              current={page}
              pageSize={pageSize}
              total={data?.length}
              onChange={newPage => setPage(newPage)}
              showSizeChanger={false}
              onShowSizeChange={(current, size) => {
                setPageSize(size)
                setPage(1)
              }}
            />
            <Select
              className='w-30'
              defaultValue={10}
              options={pageSizeOptions}
              onChange={value => setPageSize(value)}
            />
          </div>
        </>
      )}

      {/* Approval Modal */}
      <Modal
        title={
          <div className='flex items-center gap-4 pr-8'>
            <div className='size-10 rounded-full bg-blue-100 p-2'>
              <i className='onedx-send size-6 text-blue-500' />
            </div>
            <span className='text-base font-medium'>
              {portal === 'admin'
                ? 'Bạn có chắc chắn muốn phê duyệt sản phẩm dịch vụ?'
                : selectedRows?.length > 1
                  ? `Bạn có chắc chắn muốn gửi yêu cầu phê duyệt cho ${selectedRows?.length} gói dịch vụ?`
                  : 'Bạn có chắc chắn muốn gửi yêu cầu phê duyệt?'}
            </span>
          </div>
        }
        open={isApprovalModalOpen}
        onCancel={() => setIsApprovalModalOpen(false)}
        footer={
          <div className='mt-10 flex justify-end gap-4'>
            <Button
              color='primary'
              variant='outlined'
              key='cancel'
              className='w-40'
              onClick={() => setIsApprovalModalOpen(false)}
            >
              Huỷ
            </Button>
            <Button
              key='submit'
              type='primary'
              className='w-40'
              loading={approveProduct.isPending}
              onClick={() => {
                if (selectedPackage?.id) {
                  if (portal === 'admin') {
                    adminApproveProduct.mutate({
                      id: hoveredId,
                      status: 'APPROVED'
                    })
                  } else {
                    approveProduct.mutate({
                      ids: [hoveredId],
                      serviceId: Number(id),
                      status: 1
                    })
                  }
                }

                if (selectedRows?.length) {
                  if (portal === 'admin') {
                    adminApproveMultiProduct.mutate({
                      ids: selectedRows,
                      status: 'APPROVED',
                      cause: null
                    })
                  } else {
                    approveProduct.mutate({
                      ids: selectedRows,
                      serviceId: Number(id),
                      status: 1
                    })
                  }
                }
              }}
            >
              Xác nhận
            </Button>
          </div>
        }
      />

      {/* Default Package Modal */}
      <Modal
        title={
          <div className='flex items-center gap-4'>
            <div className='size-10 rounded-full bg-blue-100 p-2'>
              <TagIcon />
            </div>
            <span className='text-base font-medium'>Thiết lập gói cước mặc định</span>
          </div>
        }
        open={isDefaultModalOpen}
        onCancel={() => setIsDefaultModalOpen(false)}
        footer={
          <div className='flex justify-end gap-4'>
            <Button
              color='primary'
              variant='outlined'
              key='cancel'
              className='btn btn-secondary'
              onClick={() => setIsDefaultModalOpen(false)}
            >
              Huỷ
            </Button>
            <Button
              key='submit'
              type='primary'
              onClick={() => {
                settingPricingDefault.mutate({
                  serviceId: Number(id),
                  pricingId: hoveredId
                })

                setIsDefaultModalOpen(false)
              }}
            >
              Xác nhận
            </Button>
          </div>
        }
      >
        <p>Bạn có chắc muốn thiết lập gói cước này làm mặc định?</p>
      </Modal>

      {/* Delete Modal */}
      <Modal
        title={
          <div className='flex items-center gap-4'>
            <div className='size-10 rounded-full bg-red-1 p-2'>
              <i className='onedx-delete size-6 text-red-7' />
            </div>
            <span className='text-base font-medium'>{`Bạn có chắc chắn muốn xóa ${selectedRows?.length ? selectedRows?.length : ''} gói dịch vụ?`}</span>
          </div>
        }
        open={isDeleteModalOpen}
        onCancel={() => {
          setIsDeleteModalOpen(false)
        }}
        footer={
          <div className='flex justify-center gap-4'>
            <Button color='primary' variant='outlined' className='w-40' onClick={() => setIsDeleteModalOpen(false)}>
              Huỷ
            </Button>
            <Button
              color='danger'
              variant='filled'
              className='w-40'
              onClick={() => {
                if (isCreate || isUpdate) {
                  if (isUpdate) {
                    const getPricingDeleted = hoveredId
                      ? pricingList.filter((item: any) => item.id === hoveredId)?.map((e: any) => e.id)
                      : pricingList.filter((item: any) => selectedRows.includes(item.id))?.map((e: any) => e.id)

                    let deleted

                    if (deletedPricing.length === 0) {
                      deleted = getPricingDeleted
                    } else {
                      deleted = [...deletedPricing, ...getPricingDeleted]
                    }

                    form.setFieldValue('pricingIdsDelete', deleted)
                    setDeletedPricing(deleted)
                  }

                  const newPricingList =
                    selectedRows && selectedRows.length > 0
                      ? pricingList.filter(
                          (item: any) => !selectedRows.includes(item.draftId) && !selectedRows.includes(item.id)
                        )
                      : pricingList.filter((item: any) => item.draftId !== hoveredId && item.id !== hoveredId)

                  setPricingList(newPricingList)
                  setPackages(newPricingList)
                  setSelectedRows([])
                  if (newPricingList.length === 0) setIsAddPrice((prev: boolean) => !prev)
                } else {
                  selectedRows?.length > 0
                    ? deleteMultiProduct.mutate({ ids: selectedRows })
                    : deleteProduct.mutate({ ids: hoveredId })
                }

                setIsDeleteModalOpen(false)
              }}
            >
              Xoá
            </Button>
          </div>
        }
      />
      {/* Recommend Modal */}
      <Modal
        width={400}
        title={
          <div className='flex items-center gap-2'>
            <div className='flex size-10 rounded-full bg-blue-100 p-2'>
              <i className='onedx-money-invoice size-6 bg-blue-600' />
            </div>
            <span className='font-weight text-base '>Lựa chọn gói khuyên dùng</span>
          </div>
        }
        styles={{
          body: {
            minHeight: 80
          }
        }}
        open={isRecommendModalOpen}
        onCancel={() => {
          setIsRecommendModalOpen(false)
        }}
        footer={
          <div className='flex justify-end  gap-3'>
            <Button className='w-20 border-blue-500 text-blue-500 ' onClick={() => setIsRecommendModalOpen(false)}>
              Đóng
            </Button>

            <Button
              type='primary'
              className='w-20 border-blue-500'
              onClick={() => {
                handleToggleRecommended(selectedPackage)
                setIsRecommendModalOpen(false)
              }}
            >
              Xác nhận
            </Button>
          </div>
        }
      >
        <div className='text-left text-base' style={{ marginTop: '30px' }}>
          <span>Bạn có muốn lưu lựa chọn gói khuyên dùng không?</span>
        </div>
      </Modal>
    </div>
  )
})
