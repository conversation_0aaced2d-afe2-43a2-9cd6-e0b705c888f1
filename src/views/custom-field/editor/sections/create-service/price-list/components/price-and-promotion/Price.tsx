import React, { useMemo, useState } from 'react'

import type { FormListFieldData } from 'antd'
import { Form, Collapse, Input, Select, Button, Empty, Switch, Modal } from 'antd'
import { useWatch } from 'antd/es/form/Form'

import { isEmpty, isNil } from 'lodash'

import { styled } from 'styled-components'

import {
  validateCode,
  validateRequireInput,
  validateMaxLengthStr,
  normalizeInputNumber,
  validateMinInputNumber,
  formatNormalizeNumberOtherZero,
  formatAndNormalizeMax3DigitsInput,
  validatePriceRequired,
  validateInputValueRange
} from '@/validator'
import { CustomSelect } from '../../PriceListStyled'
import { dateOptions, getNamePath, PRICING_TYPE, generatePlanName, InputMoney } from '../../utils'
import Pricing from '@/models/Pricing'

import { PricingPlanRange } from './PricingPlanRange'
import { PRICING_PLAN, pricingPlanOptions } from '@/utils/products/pricing'
import { CYCLE_FREQUENCY_MAPPING } from '@/views/product-catalog/constants/constants'

const { Panel } = Collapse

const StyledSwitch = styled(Switch)`
  &.ant-switch-checked {
    background-color: #22c55e;

    &:hover {
      background-color: #22c55e !important;
    }
  }

  &:not(.ant-switch-checked) {
    background-color: #d1d5db;

    &:hover {
      background-color: #d1d5db !important;
    }
  }
`

interface PriceProps {
  form: any
  listCurrency: any
  listUnit?: any
  onRemove: (func: any, key: any) => void
}

interface PricingPlanComponentProps {
  planPricing: string
  isOneTime: number
  currencyLabel: string
  listUnit?: any
  field: FormListFieldData
  form: any
}

interface AutoPlanNameProps {
  index: number
  currencyLabel: string
}

export const AutoPlanName = ({ index, currencyLabel }: AutoPlanNameProps) => {
  return (
    <Form.Item
      noStyle
      shouldUpdate={(prevValues, currentValues) => {
        const prev = prevValues.pricingStrategies?.[index]
        const curr = currentValues.pricingStrategies?.[index]

        // Check if relevant fields changed
        return (
          prev?.pricingPlan !== curr?.pricingPlan ||
          prev?.price !== curr?.price ||
          prev?.unitId !== curr?.unitId ||
          prev?.paymentCycle !== curr?.paymentCycle ||
          prev?.cycleType !== curr?.cycleType ||
          prev?.unitLimitedList !== curr?.unitLimitedList
        )
      }}
    >
      {form => {
        const strategy = form.getFieldValue(['pricingStrategies', index])
        const isOneTime = form.getFieldValue('isOneTime')

        // Auto update planName when strategy changes
        if (strategy) {
          const newPlanName = generatePlanName({ strategy, currency: currencyLabel, isOneTime })

          if (newPlanName !== strategy.planName) {
            form.setFieldValue(['pricingStrategies', index, 'planName'], newPlanName)
          }
        }

        return null // This component doesn't render anything
      }}
    </Form.Item>
  )
}

export const PricingFields = ({
  type,
  name,
  extraProps
}: {
  type: string
  name: string | number | (string | number)[]
  extraProps?: any
}) => {
  const form = Form.useFormInstance()
  const namePath = getNamePath(name)

  switch (type) {
    case 'price':
      return (
        <Form.Item
          name={[...namePath, 'price']}
          initialValue={0}
          label='Đơn giá'
          normalize={normalizeInputNumber}
          rules={[validatePriceRequired(), validateInputValueRange(0, 9999999999, 'Đơn giá không hợp lệ')]}
          {...extraProps.formItemProps}
        >
          <InputMoney
            customLength={10}
            placeholder='Chọn đơn giá'
            addonAfter={extraProps.currencyLabel}
            {...extraProps.inputProps}
          />
        </Form.Item>
      )

    case 'unit':
      return (
        <Form.Item
          name={[...namePath, 'unitId']}
          label='Đơn vị tính'
          rules={[validateRequireInput('Đơn vị tính không được bỏ trống')]}
        >
          <Select
            options={extraProps.listUnit}
            placeholder='Chọn đơn vị tính'
            onChange={(value, option: any) => {
              form.setFields([
                {
                  name: ['pricingStrategies', ...namePath, 'unitName'],
                  value: option?.label
                },
                {
                  name: ['pricingStrategies', ...namePath, 'unitId'],
                  value: value
                }
              ])
            }}
          />
        </Form.Item>
      )

    case 'minimumQuantity':
      return (
        <Form.Item
          name={[...namePath, 'minimumQuantity']}
          label='Số lượng tối thiểu'
          normalize={normalizeInputNumber}
          rules={[validateMinInputNumber(1, 'Giá trị nhập phải lớn hơn 0')]}
        >
          <Input
            maxLength={10}
            placeholder='Nhập số lượng tối thiểu'
            onChange={e => {
              const value = normalizeInputNumber(e.target.value)

              form.setFieldValue(['pricingStrategies', ...namePath, 'minimumQuantity'], value)
              // Sau khi setFieldValue xong, validate lại maximumQuantity
              form.validateFields([['pricingStrategies', ...namePath, 'maximumQuantity']])
            }}
          />
        </Form.Item>
      )

    case 'maximumQuantity':
      return (
        <Form.Item
          name={[...namePath, 'maximumQuantity']}
          label='Số lượng tối đa'
          normalize={normalizeInputNumber}
          rules={[
            validateMinInputNumber(1, 'Giá trị nhập phải lớn hơn 0'),
            ({ getFieldValue }) => ({
              validator(_, value) {
                const minQuantity = getFieldValue(['pricingStrategies', ...namePath, 'minimumQuantity'])

                if (value && minQuantity && value < minQuantity) {
                  return Promise.reject('Giá trị chặn trên không được nhỏ hơn giá trị chặn dưới')
                }

                return Promise.resolve()
              }
            })
          ]}
        >
          <Input maxLength={10} placeholder='Nhập số lượng tối đa' />
        </Form.Item>
      )

    case 'freeQuantity':
      return (
        <Form.Item
          name={[...namePath, 'freeQuantity']}
          label='Số lượng miễn phí'
          normalize={normalizeInputNumber}
          // rules={[validateMinInputNumber(1, 'Giá trị nhập phải lớn hơn 0')]}
        >
          <Input maxLength={10} placeholder='Nhập số lượng miễn phí' />
        </Form.Item>
      )
    default:
      return <></>
  }
}

const SubscriptionPricingFields = (props: { name: number; form?: any; fieldKey?: number }) => {
  const { name: prefixName, form, fieldKey } = props
  const [showCycleChangeModal, setShowCycleChangeModal] = useState(false)

  // States để control giá trị và lưu giá trị cũ
  const [oldValues, setOldValues] = useState<{ paymentCycle?: string; cycleType?: number }>({})

  // Giá trị tạm thời để lưu thay đổi chu kỳ thanh toán hoặc loại chu kỳ
  const [pendingCycleChange, setPendingCycleChange] = useState<{
    field: string
    value: any
  } | null>(null)

  const getName = (name: string) => {
    if (!isNil(prefixName)) {
      return [...getNamePath(prefixName), name]
    }

    return name
  }

  const validatePaymentCycle = (fieldKey?: number) => {
    return {
      validator: async (_: any, value: string) => {
        if (!value || !fieldKey) return Promise.resolve()

        if (parseInt(value, 10) <= 0) {
          return Promise.reject('Chu kỳ thanh toán không được bỏ trống')
        }

        // Validate against existing cycles in the form
        const existingCycles = form?.getFieldValue('pricingStrategies') || []
        const currentCycleType = form?.getFieldValue(['pricingStrategies', fieldKey, 'cycleType']) || 0

        const duplicateCycle = existingCycles.find(
          (item: any, index: number) =>
            item.paymentCycle === value && item.cycleType === currentCycleType && index !== fieldKey
        )

        if (duplicateCycle) {
          return Promise.reject('Chu kỳ thanh toán phải là duy nhất')
        }

        return Promise.resolve()
      }
    }
  }

  const validatePeriodCode = async (_: any, value: string) => {
    try {
      if (!value) return Promise.resolve() // Get all existing cycle codes in current service package (exclude current field)
      const pricingStrategies = form?.getFieldValue('pricingStrategies') || []

      const existingCycleCodes = pricingStrategies
        .filter((_: any, index: number) => index !== fieldKey)
        .map((strategy: any) => strategy.cycleCode)
        .filter((code: string) => code && code.trim() !== '')

      // Check for duplicates within service package
      if (!isEmpty(existingCycleCodes) && existingCycleCodes.includes(value)) {
        return Promise.reject('Mã chu kỳ đã tồn tại trong gói dịch vụ')
      }

      const res = await Pricing.validateCode({
        type: 'PERIOD_CODE',
        code: value,
        pricingDraftId: form?.getFieldValue('id') || -1
      })

      if ((typeof res === 'string' && res === 'true') || (typeof res === 'boolean' && res)) {
        return Promise.reject('Mã chu kỳ đã tồn tại trong hệ thống')
      }

      return Promise.resolve()
    } catch (error) {
      console.error('Period code validation error:', error)

      return Promise.reject('Lỗi kiểm tra mã chu kỳ')
    }
  }

  const hasAffectedAddons = () => {
    const pricingStrategies = form?.getFieldValue('pricingStrategies') || []
    const currentStrategy = isNil(fieldKey) ? null : pricingStrategies[fieldKey]

    // Kiểm tra xem có dịch vụ bổ sung nào đã được chọn trong chiến lược hiện tại
    return currentStrategy?.addonList && currentStrategy.addonList.length > 0
  }

  // Thêm hàm để xử lý thay đổi chu kỳ với xác nhận
  const handleCycleChange = (field: string, value: any) => {
    if (hasAffectedAddons()) {
      // Nếu có dịch vụ bổ sung đã chọn
      setPendingCycleChange({ field, value })
      setShowCycleChangeModal(true)
    } else {
      applyCycleChange(field, value)
    }
  }

  const reValidateStrategies = (field: string) => {
    const formValue = form.getFieldValue(['pricingStrategies'])

    if (formValue && formValue?.length > 0) {
      formValue?.forEach((_: any, key: any) => {
        form.validateFields([['pricingStrategies', key, field]])
      })
    }
  }

  // Thay đổi giá trị chu kỳ thanh toán hoặc loại chu kỳ và xóa danh sách dịch vụ bổ sung
  const applyCycleChange = (field: string, value: any) => {
    if (field === 'paymentCycle') {
      form.setFieldValue(['pricingStrategies', fieldKey, 'paymentCycle'], value)
    } else if (field === 'cycleType') {
      form.setFieldValue(['pricingStrategies', fieldKey, 'cycleType'], value)
    }

    const pricingStrategies = form?.getFieldValue('pricingStrategies') || []

    const updatedStrategies = pricingStrategies.map((strategy: any, index: number) => {
      if (index === fieldKey) {
        return {
          ...strategy,
          addonList: [] // Clear addon list
        }
      }

      return strategy
    })

    form.setFieldValue('pricingStrategies', updatedStrategies)
  }

  return (
    <>
      <Form.Item noStyle shouldUpdate>
        {({ getFieldValue }) => {
          const pricingCode = getFieldValue('pricingCode')

          return (
            <Form.Item
              name={getName('cycleCode')}
              label='Mã chu kỳ'
              normalize={value => value?.trim()}
              validateFirst
              dependencies={['pricingCode']}
              validateTrigger='onBlur'
              rules={[
                validateRequireInput('Bắt buộc nhập mã gói cước hoặc mã chu kỳ để tạo gói', !pricingCode),
                validateCode(
                  'Định dạng mã chu kỳ chỉ được gồm: Chữ cái hoa/thường không dấu, ký tự _ và chữ số',
                  /^[a-zA-Z0-9_]+$/
                ),
                validateMaxLengthStr(30, 'Mã chu kỳ không được vượt quá 30 ký tự'),
                { validator: validatePeriodCode }
              ]}
            >
              <Input placeholder='Nhập mã chu kỳ' maxLength={30} />
            </Form.Item>
          )
        }}
      </Form.Item>

      <Form.Item
        name={getName('paymentCycle')}
        validateFirst
        label='Chu kỳ thanh toán'
        validateTrigger='onBlur'
        dependencies={[getName('cycleType')]}
        rules={[validateRequireInput('Chu kỳ thanh toán không được bỏ trống'), validatePaymentCycle(fieldKey)]}
      >
        <Input
          className='rounded-r-none'
          placeholder='Nhập chu kỳ thanh toán'
          onFocus={() => {
            // Lưu giá trị cũ khi focus vào input
            const currentValue = form.getFieldValue(['pricingStrategies', fieldKey, 'paymentCycle'])

            setOldValues(prev => ({ ...prev, paymentCycle: currentValue }))
          }}
          onChange={e => {
            const value = formatNormalizeNumberOtherZero(e.target.value, 'normal')

            handleCycleChange('paymentCycle', value)
            reValidateStrategies('paymentCycle')
          }}
          addonAfter={
            <Form.Item
              name={getName('cycleType')}
              initialValue={2}
              noStyle
              normalize={value => value}
              getValueFromEvent={value => value}
              getValueProps={value => ({
                // Transform giá trị MONTHLY/DAILY... thành số khi render
                value:
                  typeof value === 'string'
                    ? CYCLE_FREQUENCY_MAPPING[value as 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'YEARLY']
                    : value
              })}
            >
              <Select
                className='min-w-16'
                options={dateOptions}
                onFocus={() => {
                  // Lưu giá trị cũ khi focus vào select
                  const currentValue = form.getFieldValue(['pricingStrategies', fieldKey, 'cycleType'])

                  setOldValues(prev => ({ ...prev, cycleType: currentValue }))
                }}
                onChange={value => {
                  handleCycleChange('cycleType', value)
                  reValidateStrategies('paymentCycle')
                }}
              />
            </Form.Item>
          }
          maxLength={3}
        />
      </Form.Item>
      <Form.Item
        name={getName('numberOfCycles')}
        label='Số chu kỳ'
        normalize={value => formatAndNormalizeMax3DigitsInput(value, 3)}
        getValueProps={value => ({
          value: value === -1 ? undefined : value
        })}
      >
        <Input allowClear placeholder='Không giới hạn' />
      </Form.Item>
      <Modal
        title='Xác nhận thay đổi'
        open={showCycleChangeModal}
        onCancel={() => {
          setShowCycleChangeModal(false)
          setPendingCycleChange(null)

          // Khôi phục giá trị cũ
          if (oldValues.paymentCycle !== undefined) {
            form.setFieldValue(['pricingStrategies', fieldKey, 'paymentCycle'], oldValues.paymentCycle)
          }

          if (oldValues.cycleType !== undefined) {
            form.setFieldValue(['pricingStrategies', fieldKey, 'cycleType'], oldValues.cycleType)
          }

          // Reset old values
          setOldValues({})
          form.validateFields()
        }}
        onOk={() => {
          if (pendingCycleChange) {
            applyCycleChange(pendingCycleChange.field, pendingCycleChange.value)
          }

          setShowCycleChangeModal(false)
          setPendingCycleChange(null)
          setOldValues({}) // Reset old values
        }}
        okText='Xác nhận'
        cancelText='Hủy'
      >
        <p>
          Thay đổi Chu kỳ thanh toán thì danh sách dịch vụ bổ sung sẽ phải chọn lại. Bạn có chắc chắn muốn thay đổi?
        </p>
      </Modal>
    </>
  )
}

const FlatRatePricing = ({
  planPricing,
  isOneTime,
  currencyLabel,
  form,
  field
}: {
  planPricing: string
  isOneTime: number
  currencyLabel: string
  form: any
  field: FormListFieldData
}) => {
  if (planPricing !== 'FLAT_RATE') {
    return <div aria-hidden='true' className='hidden'></div>
  }

  if (isOneTime === PRICING_TYPE.SUBSCRIPTION) {
    return (
      <div className='grid grid-cols-4 gap-4'>
        <SubscriptionPricingFields form={form} name={field.name} fieldKey={field.fieldKey} />
        <PricingFields type='price' name={field.name} extraProps={{ currencyLabel }} />
      </div>
    )
  }

  return <PricingFields type='price' name={field.name} extraProps={{ currencyLabel }} />
}

const UnitPricing = ({
  planPricing,
  isOneTime,
  currencyLabel,
  listUnit,
  form,
  field
}: {
  planPricing: string
  isOneTime: number
  listUnit?: any
  currencyLabel: string
  form: any
  field: FormListFieldData
}) => {
  if (planPricing !== 'UNIT') {
    return <div aria-hidden='true' className='hidden'></div>
  }

  if (isOneTime === PRICING_TYPE.SUBSCRIPTION) {
    return (
      <div className='flex flex-col gap-5'>
        <div className='grid grid-cols-4 gap-4'>
          <SubscriptionPricingFields form={form} name={field.name} fieldKey={field.fieldKey} />
          <PricingFields type='unit' name={field.name} extraProps={{ listUnit }} />
        </div>
        <div className='grid grid-cols-4 gap-4 rounded-lg border border-dashed border-[#BCBCBC] bg-bg-neutral-lightest p-3'>
          <PricingFields type='minimumQuantity' name={field.name} />
          <PricingFields type='maximumQuantity' name={field.name} />
          <PricingFields type='price' name={field.name} extraProps={{ currencyLabel }} />
          <PricingFields type='freeQuantity' name={field.name} />
        </div>
      </div>
    )
  }

  return (
    <div className='flex flex-col gap-5'>
      <div className='grid grid-cols-2 gap-4'>
        <PricingFields type='unit' name={field.name} extraProps={{ listUnit }} />
        <PricingFields type='price' name={field.name} extraProps={{ currencyLabel }} />
      </div>
      <div className='grid grid-cols-3 gap-4 rounded-lg border border-dashed border-[#BCBCBC] bg-bg-neutral-lightest p-3'>
        <PricingFields type='minimumQuantity' name={field.name} />
        <PricingFields type='maximumQuantity' name={field.name} />
        <PricingFields type='freeQuantity' name={field.name} />
      </div>
    </div>
  )
}

const RangePricing = ({
  planPricing,
  isOneTime,
  currencyLabel,
  listUnit,
  field,
  form
}: {
  planPricing: string
  isOneTime: number
  currencyLabel: string
  listUnit?: any
  field: FormListFieldData
  form: any
}) => {
  if (!['TIER', 'VOLUME', 'STAIR_STEP'].includes(planPricing)) {
    return <div aria-hidden='true' className='hidden'></div>
  }

  if (isOneTime === PRICING_TYPE.SUBSCRIPTION) {
    return (
      <div className='flex flex-col gap-5'>
        <div className='grid grid-cols-4 gap-4'>
          <SubscriptionPricingFields form={form} name={field.name} fieldKey={field.fieldKey} />
          <PricingFields type='unit' name={field.name} extraProps={{ listUnit }} />
        </div>
        <div className='rounded-lg border border-dashed border-[#BCBCBC] bg-bg-neutral-lightest p-3'>
          <PricingPlanRange prefixName={[field.name]} rootName='pricingStrategies' currencyLabel={currencyLabel} />
          <></>
        </div>
      </div>
    )
  }

  return (
    <div className='flex flex-col gap-5'>
      <PricingFields type='unit' name={field.name} extraProps={{ listUnit }} />
      <div className='rounded-lg border border-dashed border-[#BCBCBC] bg-bg-neutral-lightest p-3'>
        <PricingPlanRange prefixName={[field.name]} rootName='pricingStrategies' currencyLabel={currencyLabel} />
      </div>
    </div>
  )
}

export const PricingPlanComponent = (props: PricingPlanComponentProps) => {
  const { planPricing } = props

  switch (planPricing) {
    case PRICING_PLAN.FLAT_RATE:
      return <FlatRatePricing {...props} />
    case PRICING_PLAN.UNIT:
      return <UnitPricing {...props} />
    case PRICING_PLAN.TIER:
    case PRICING_PLAN.VOLUME:
    case PRICING_PLAN.STAIR_STEP:
      return <RangePricing {...props} />
    default:
      return <FlatRatePricing {...props} />
  }
}

// region Main Component
export const Price = ({ form, listCurrency, listUnit, onRemove }: PriceProps) => {
  const isOneTime = Form.useWatch('isOneTime', form)
  // const currencyId = Form.useWatch('currencyId', form)

  const [activeKeyTime, setActiveKeyTime] = useState<Record<number, string[]>>({})
  const [switchStates, setSwitchStates] = useState<Record<string | number, boolean>>({})

  const handlePricingPlanChange = (index: number) => {
    // Reset các trường trong SubscriptionPricingFields
    form.setFieldsValue({
      ['pricingStrategies']: form.getFieldValue('pricingStrategies').map((strategy: any, idx: number) => {
        if (idx === index) {
          return {
            ...strategy,
            price: 0,
            cycleCode: undefined,
            paymentCycle: undefined,
            cycleType: 2,
            numberOfCycles: undefined,
            unitId: undefined,
            unitLimitedList: [
              {
                unitFrom: 1,
                unitTo: null
              }
            ]
          }
        }

        return strategy
      })
    })
  }

  // Tabs Biến thể áp dụng
  const handleSwitchChange = (key: React.Key, checked: boolean) => {
    // @ts-ignore
    setSwitchStates(prev => ({ ...prev, [key]: checked }))
  }

  // const objectCheck = {} as any
  const currencyId = useWatch('currencyId', form)

  const currencyLabel = useMemo(() => {
    const currency = listCurrency.find((item: { id: number }) => item.id === currencyId)

    return currency?.label || 'VND'
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currencyId, listCurrency])

  return (
    <>
      <Form.List
        name='pricingStrategies'
        initialValue={[
          {
            pricingPlan: 'FLAT_RATE',
            displayStatus: 1,
            defaultCircle: 'NO'
          }
        ]}
      >
        {(fields, { add, remove }) => (
          <>
            {fields.map((field, index) => {
              const { key, name, ...restField } = field
              const isOpenTime = activeKeyTime[index]?.includes('1')
              const displayStatus = switchStates[name]

              return (
                <Collapse
                  key={key}
                  ghost
                  defaultActiveKey={['1']}
                  activeKey={activeKeyTime[index]}
                  onChange={key => {
                    setActiveKeyTime((prev: any) => ({
                      ...prev,
                      [index]: key
                    }))
                  }}
                  className={`site-collapse-custom-collapse -mx-4 grid rounded-lg border border-gray-200 bg-bg-neutral-lightest ${index > 0 && 'my-6'}`}
                  expandIcon={() => null}
                >
                  <Panel
                    collapsible='icon'
                    header={
                      <div className='relative flex items-center justify-between'>
                        <div className='flex cursor-pointer items-center gap-2'>
                          <span className='pt-0.5 text-lg font-bold text-orange-400'>|</span>
                          <Form.Item
                            {...restField}
                            key={key}
                            name={[name, 'pricingPlan']}
                            className='mb-0'
                            initialValue='FLAT_RATE'
                          >
                            <CustomSelect
                              suffixIcon={
                                <i
                                  className={`onedx-chevron-down size-5 transition-transform duration-200 ${
                                    activeKeyTime[index]?.includes('1') ? 'rotate-180' : ''
                                  }`}
                                />
                              }
                              className='w-[180px]'
                              onClick={e => {
                                e.stopPropagation()
                              }}
                              onChange={() => handlePricingPlanChange(index)}
                              options={pricingPlanOptions}
                            />
                          </Form.Item>
                        </div>
                        <div className='flex items-center gap-4'>
                          {fields?.length > 1 && (
                            <>
                              <i
                                className='onedx-delete size-5'
                                onClick={e => {
                                  e.stopPropagation()
                                  onRemove(remove, index)
                                }}
                              />
                              <div className='h-3 w-0 outline outline-1 outline-offset-[-0.50px] outline-border-neutral-medium/20' />
                            </>
                          )}
                          <div
                            onClick={e => {
                              e.stopPropagation()
                            }}
                          >
                            <Form.Item
                              key={key}
                              name={[name, 'displayStatus']}
                              className='mb-0'
                              valuePropName='checked'
                              initialValue={1}
                            >
                              <StyledSwitch
                                checked={displayStatus}
                                onChange={checked => handleSwitchChange(name, checked)}
                              />
                            </Form.Item>
                          </div>
                        </div>

                        {isOpenTime && <div className='absolute -bottom-3 left-0 h-px w-full bg-gray-300' />}
                      </div>
                    }
                    key='1'
                  >
                    {/* Component con riêng để theo dõi và tự động cập nhật planName */}
                    <AutoPlanName index={index} currencyLabel={currencyLabel} />

                    <Form.Item
                      noStyle
                      shouldUpdate={(prevValues, currentValues) => {
                        const prev = prevValues.pricingStrategies?.[index]
                        const curr = currentValues.pricingStrategies?.[index]

                        // Check if relevant fields changed
                        return prev !== curr
                      }}
                    >
                      {form => {
                        const planPricing =
                          form.getFieldValue(['pricingStrategies', index, 'pricingPlan']) || 'FLAT_RATE'

                        return (
                          <PricingPlanComponent
                            planPricing={planPricing}
                            isOneTime={isOneTime}
                            currencyLabel={currencyLabel}
                            listUnit={listUnit}
                            field={field}
                            form={form}
                          />
                        )
                      }}
                    </Form.Item>
                  </Panel>
                </Collapse>
              )
            })}

            {isOneTime === PRICING_TYPE.SUBSCRIPTION && (
              <div className='flex justify-center pt-6'>
                <Button
                  className='h-8'
                  onClick={() => {
                    add({
                      pricingPlan: 'FLAT_RATE',
                      displayStatus: 1,
                      defaultCircle: 'NO'
                    })
                  }}
                >
                  <i className='onedx-add size-5' /> Thêm mô hình định giá
                </Button>
              </div>
            )}
          </>
        )}
      </Form.List>
      {isOneTime === PRICING_TYPE.SUBSCRIPTION && (
        <Form.Item
          noStyle
          shouldUpdate={(prevValues, currentValues) => prevValues.pricingStrategies !== currentValues.pricingStrategies}
        >
          {({ getFieldValue }) => {
            const pricingStrategies = getFieldValue('pricingStrategies') || []

            return (
              <Form.Item
                name='defaultCircle'
                label='Chu kỳ mặc định'
                className='col-span-6'
                required
                rules={[{ required: true, message: 'Chọn chu kỳ mặc định không được bỏ trống' }]}
              >
                <Select
                  placeholder={'Chọn chu kỳ mặc định'}
                  notFoundContent={<Empty description='Không có dữ liệu' />}
                  options={
                    pricingStrategies
                      ?.map((e: any, index: number) => ({
                        ...e,
                        value: index,
                        label: e.planName
                      }))
                      .filter((e: any) => e.label) || []
                  }
                />
              </Form.Item>
            )
          }}
        </Form.Item>
      )}
    </>
  )
}
// endregion

export default Price
