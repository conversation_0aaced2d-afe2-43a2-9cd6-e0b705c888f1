import React, { useMemo, useState } from 'react'

import { Form, Select, Empty, Spin, Tooltip, Tag } from 'antd'

import type { DefaultOptionType } from 'antd/es/select'

import { useSelector } from 'react-redux'

import { useDebounce } from 'react-use'

import type { RootState } from '@/redux-store'

import { useConditionApplyForm } from '../hooks/useConditionApplyForm'

const { Item } = Form

interface LocationSelectorProps {
  namePrefix?: (string | number)[]
  filterOption: (input: string, option: DefaultOptionType | undefined) => boolean
  isFormatData?: boolean
}

export const LocationSelector: React.FC<LocationSelectorProps> = ({
  filterOption,
  namePrefix,
  isFormatData = false
}) => {
  const {
    selectedProvince,
    selectedWard,
    provinceList,
    wardList,
    streetList,
    loadingProvince,
    loadingWard,
    loadingStreet,
    setNewProvinceValue,
    setNewWardValue,
    setNewStreetValue,
    setProvinceSearch,
    setWardSearch,
    setStreetSearch,
    updateValueFromForm
  } = useConditionApplyForm()

  const form = Form.useFormInstance()

  const conditionsApply = useSelector((state: RootState) => state.customFieldReducer.conditionsApply)

  const [searchValue, setSearchValue] = useState({
    value: '',
    key: ''
  })

  // Lấy danh sách khi ực đã chọn (khi chọn option không có trong danh sách gốc sẽ lưu lại và hiển thị nững option đó ra)
  const [valueProvince, setValueProvince] = useState<any>([])
  const [valueWard, setValueWard] = useState<any>([])
  const [valueStreet, setValueStreet] = useState<any>([])

  useMemo(() => {
    // set lại thông tin khi chỉnh sửa dữ liệu
    if (conditionsApply) {
      const province = conditionsApply?.provinceId || conditionsApply?.province
      const ward = conditionsApply?.wardId || conditionsApply?.ward
      const street = conditionsApply?.streetId || conditionsApply?.street

      // update và gọi lại api quận/huyện Phường/xã Phố/Đường
      updateValueFromForm(province, ward, street)

      // update danh sách đã chọn
      if (province) {
        setValueProvince(province?.map((e: any) => ({ label: e?.name, value: e?.id })))
      }

      if (ward) {
        setValueWard(ward?.map((e: any) => ({ label: e?.name, value: e?.id })))
      }

      if (street) {
        setValueStreet(street?.map((e: any) => ({ label: e?.name, value: e?.id })))
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [conditionsApply])

  const renderEmptyState = () => <Empty description='Không có dữ liệu' image={Empty.PRESENTED_IMAGE_SIMPLE} />

  const renderLoadingState = () => (
    <div className='flex justify-center p-4'>
      <Spin size='small' />
    </div>
  )

  useDebounce(
    () => {
      if (searchValue?.key === 'province') setProvinceSearch(searchValue?.value)
      if (searchValue?.key === 'ward') setWardSearch(searchValue?.value)
      if (searchValue?.key === 'street') setStreetSearch(searchValue?.value)
    },
    200,
    [searchValue]
  )

  const renderTag = (props: any, formValues: any) => {
    const { value, closable, onClose } = props

    if (value) {
      const findOption = formValues.find((item: any) => item?.id === value)
      const currentIndex = formValues.indexOf(findOption)

      if (currentIndex < 2) {
        return (
          <Tooltip title={findOption?.name} placement='bottom'>
            <Tag closable={closable} onClose={onClose} style={{ marginRight: 3 }}>
              {findOption?.name}
            </Tag>
          </Tooltip>
        )
      }

      if (currentIndex === 2) {
        const hiddenCount = formValues.length - 2
        const hiddenItems = formValues.slice(2)
        const hiddenNames = hiddenItems.map((e: any) => e?.name).join(', ')

        return (
          <Tooltip title={hiddenNames} placement='bottom'>
            <Tag closable={false} style={{ marginRight: 3 }}>
              +{hiddenCount}
            </Tag>
          </Tooltip>
        )
      }
    }

    return <></>
  }

  return (
    <div className='grid grid-cols-3 gap-4'>
      <div className='flex flex-col'>
        <Item
          name={namePrefix ? [...namePrefix, 'provinceId'] : 'provinceId'}
          label={
            <span className='text-xs' style={{ color: '#0F1319', fontFamily: 'Inter' }}>
              Tỉnh/Thành phố
            </span>
          }
          {...(isFormatData && {
            getValueFromEvent: (_: any, option: any) => {
              return option?.map((item: any) => ({
                id: item.value,
                name: item.label
              }))
            },
            getValueProps: value => ({
              value: value?.flatMap((item: any) => item.id)
            })
          })}
          className='min-w-250 col-span-6'
        >
          <Select
            mode='multiple'
            allowClear
            showSearch
            options={Array.from(new Map([...provinceList, ...valueProvince].map(item => [item.value, item])).values())} // lọc những giá trị bị trùng
            filterOption={filterOption}
            autoClearSearchValue={false}
            onSearch={(value: string) => setSearchValue({ value, key: 'province' })}
            onChange={(value: number[], option) => {
              setNewProvinceValue(value, option, namePrefix)

              if (option) {
                setValueProvince(option)
              } else {
                setValueProvince([])
              }

              setSearchValue({
                value: '',
                key: 'province'
              })
            }}
            onBlur={() => setProvinceSearch('')}
            loading={loadingProvince}
            placeholder='Chọn thành phố/tỉnh'
            tagRender={props => {
              const formValues = form.getFieldValue(namePrefix ? [...namePrefix, 'provinceId'] : 'provinceId')

              return renderTag(props, formValues)
            }}
          />
        </Item>
      </div>
      <div className='flex flex-col'>
        <Item
          name={namePrefix ? [...namePrefix, 'wardId'] : 'wardId'}
          label={
            <span className='text-xs' style={{ color: '#0F1319', fontFamily: 'Inter' }}>
              Phường/xã
            </span>
          }
          {...(isFormatData && {
            getValueFromEvent: (_: any, option: any) => {
              return option?.map((item: any) => ({
                id: item.value,
                name: item.label
              }))
            },
            getValueProps: value => ({
              value: value?.flatMap((item: any) => item.id)
            })
          })}
          className='col-span-6'
        >
          <Select
            mode='multiple'
            allowClear
            showSearch
            options={Array.from(new Map([...wardList, ...valueWard].map(item => [item.value, item])).values())} // lọc những giá trị bị trùng
            filterOption={filterOption}
            onSearch={(value: string) => setSearchValue({ value, key: 'ward' })}
            onChange={(value: string[], option) => {
              setNewWardValue(value, namePrefix)

              if (option) {
                setValueWard(option)
              } else {
                setValueWard([])
              }

              setSearchValue({
                value: '',
                key: 'ward'
              })
            }}
            onBlur={() => setWardSearch('')}
            loading={loadingWard}
            placeholder='Chọn phường/xã'
            disabled={!selectedProvince || !selectedProvince?.length}
            tagRender={props => {
              const formValues = form.getFieldValue(namePrefix ? [...namePrefix, 'wardId'] : 'wardId')

              return renderTag(props, formValues)
            }}
            notFoundContent={loadingWard ? renderLoadingState() : renderEmptyState()}
          />
        </Item>
      </div>

      <div className='flex flex-col'>
        <Item
          name={namePrefix ? [...namePrefix, 'streetId'] : 'streetId'}
          label={
            <span className='text-xs' style={{ color: '#0F1319', fontFamily: 'Inter' }}>
              Phố/Đường
            </span>
          }
          {...(isFormatData && {
            getValueFromEvent: (_: any, option: any) => {
              return option?.map((item: any) => ({
                id: item.value,
                name: item.label
              }))
            },
            getValueProps: value => ({
              value: value?.flatMap((item: any) => item.id)
            })
          })}
          className='col-span-6'
        >
          <Select
            mode='multiple'
            allowClear
            showSearch
            options={Array.from(new Map([...streetList, ...valueStreet].map(item => [item.value, item])).values())} // lọc những giá trị bị trùng
            filterOption={filterOption}
            onSearch={(value: string) => setSearchValue({ value, key: 'street' })}
            onChange={(value: number[], option) => {
              setNewStreetValue(value)

              if (option) {
                setValueStreet(option)
              } else {
                setValueStreet([])
              }

              setSearchValue({
                value: '',
                key: 'street'
              })
            }}
            onBlur={() => setStreetSearch('')}
            loading={loadingStreet}
            placeholder='Chọn phố/đường'
            disabled={!selectedWard || !selectedWard?.length}
            tagRender={props => {
              const formValues = form.getFieldValue(namePrefix ? [...namePrefix, 'streetId'] : 'streetId')

              return renderTag(props, formValues)
            }}
            notFoundContent={loadingStreet ? renderLoadingState() : renderEmptyState()}
          />
        </Item>
      </div>
    </div>
  )
}
