import React, { useEffect } from 'react'

import { usePathname } from 'next/navigation'

import { Button, ConfigProvider, Form } from 'antd'
import type { UserComponent } from '@craftjs/core'
import { useNode } from '@craftjs/core'
import { useDispatch } from 'react-redux'

import { useQuery } from '@tanstack/react-query'

import DevPricing from '@/models/DevPricing'

import type { PricingPlan, PricingPlanFormInput } from '@/types/pricing'

import {
  AddionalServiceSection,
  CommonObject,
  ConditionApply,
  PriceAndPromotion
} from '@views/custom-field/editor/sections/create-service/price-list/index'
import { Fee } from '@views/custom-field/editor/sections/create-service/price-list/Fee'
import { convertSubmitStep3, convertToFormInput, convertToFormInputFromApi } from './utils'
import { baseColorLight } from '@/utils/colors'
import { useGetUnit } from '@/components/custom-field/hooks/useGetUnit'
import { updateInfoCustomField } from '@/redux-store/slices/customFieldSlice'
import { useUser } from '@/hooks'

/**
 * @component Component form để thêm mới danh sách giá
 * @param {string} props.pageType - Loại trang hiện tại: 'create' | 'edit' | 'create-pricing' | 'edit-pricing'
 * @param {Function} props.setOpen - Hàm để mở hoặc đóng form thêm bảng giá
 * @param {Array} props.pricingList - Mảng các mục trong danh sách giá hiện có
 * @param {Function} props.setFormValues - Hàm để cập nhật giá trị của form bên ngoài
 * @returns {React.ReactElement} Form với các tùy chọn cấu hình giá
 */
export const AddPriceList: UserComponent = (props: any) => {
  const { pageType, setOpen, pricingList, setPricingList, setFormValues, initialValues, setIdPackageEdit } = props

  const dispatch = useDispatch()
  const pathname = usePathname()
  const { user } = useUser()

  const [form] = Form.useForm<PricingPlanFormInput>()

  const changeReduxStatus = () => {
    dispatch(
      updateInfoCustomField({
        isServiceCreatePricing: false
      })
    )
  }

  const {
    connectors: { connect, drag }
  } = useNode()

  const handleFinish = () => {
    const values = form.getFieldsValue(true)
    const newPricing = convertSubmitStep3(values)

    let updatedList = []

    if (initialValues?.id || initialValues?.draftId) {
      // Update phần tử
      updatedList = pricingList.map((item: any) => (item.draftId === initialValues.draftId ? newPricing : item))
    } else {
      // Thêm mới
      updatedList = [...pricingList, newPricing]
    }

    setPricingList(updatedList)

    if (setFormValues) {
      setFormValues({
        pricingReqDTO: updatedList
      })
    }

    // Toggle trạng thái thêm bảng giá
    setOpen((prev: boolean) => !prev)
    changeReduxStatus()
    setIdPackageEdit(null)

    form.resetFields()
  }

  // Khởi tạo giá trị ban đầu của form với pricingId (InitialValues.id) nếu có
  useQuery({
    queryKey: ['pricing', initialValues?.id],
    queryFn: async () => {
      const portal = ((pathname || '').split('/').find(item => item === 'admin-portal') ||
        `${user?.portalType?.toLowerCase() || 'dev'}-portal`) as 'admin-portal' | 'partner-portal'

      const response = await DevPricing.getPricing(portal, initialValues?.id, 'PROCESSING')
      const formInput = convertToFormInputFromApi(response)
      const initialValuesInput = convertToFormInputFromApi(initialValues)

      if (!!initialValues?.draftId) {
        form.setFieldsValue({
          ...response,
          ...formInput,
          ...initialValuesInput,
          customerTypeCode: initialValues.customerTypeCode,
          pageType: pageType
        })
        // lưu thông tin Áp dụng điều kiện
        dispatch(
          updateInfoCustomField({
            conditionsApply: initialValuesInput?.pricingConfigDTO
          })
        )
      } else {
        form.setFieldsValue({
          ...response,
          ...initialValuesInput,
          ...formInput,
          customerTypeCode: initialValues.customerTypeCode,
          pageType: pageType
        })
        // lưu thông tin Áp dụng điều kiện
        dispatch(
          updateInfoCustomField({
            conditionsApply: formInput?.pricingConfigDTO
          })
        )
      }

      return response
    },
    enabled: pageType === 'edit' && !!initialValues?.id
  })

  useEffect(() => {
    // ? Đặt giá trị khởi tạo trong useEffect để tránh lỗi khi form đã khởi tạo rồi nhưng chưa có dữ liệu
    if (initialValues && !initialValues?.id) {
      const initData = convertToFormInput(initialValues)

      // ! Lưu ý: Giá trị initial ban đầu của form sẽ được thay thế
      form.setFieldsValue({
        ...initData,
        customerTypeCode: initialValues.customerTypeCode,
        pageType: pageType
      })

      // lưu thông tin Áp dụng điều kiện
      dispatch(
        updateInfoCustomField({
          conditionsApply: initData?.pricingConfigDTO
        })
      )
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [initialValues])

  return (
    <div
      className='w-full'
      ref={(ref: HTMLDivElement | null) => {
        if (ref) {
          connect(drag(ref))
        }
      }}
    >
      <ConfigProvider
        theme={{
          token: {
            colorPrimary: baseColorLight['primary']
          },
          components: {
            Form: {
              itemMarginBottom: 0
            }
          }
        }}
      >
        <Form
          form={form}
          initialValues={{
            pageType: pageType
          }}
          name='pricingReqDTO'
          layout='vertical'
          className='w-full'
          onFinish={handleFinish}
          onFinishFailed={error => {
            console.log('Validate Failed:', error)
          }}
        >
          <AddPriceComponent
            pricingList={pricingList}
            handleCancel={() => {
              setOpen((prev: any) => !prev)
              changeReduxStatus()
              setIdPackageEdit(null)
              form.resetFields()
            }}
            handleSubmit={() => {
              form.submit()
            }}
          />
        </Form>
      </ConfigProvider>
    </div>
  )
}

/** ! Lưu ý: Giao diện cấu hình bảng giá, với màn product-catalog/pricing/{mode} thì không dùng AddPriceList mà dùng Form gốc
 * @returns {React.ReactElement} Giao diện không có Form Wrapper
 */

export const AddPriceComponent = ({
  pricingList,
  hiddenButton = false,
  handleCancel,
  handleSubmit
}: {
  pricingList: PricingPlan[]
  hiddenButton?: boolean
  handleCancel: () => void
  handleSubmit?: () => void
}) => {
  const { listCurrency, listTax, listUnit } = useGetUnit()

  const newListTax = listTax.map(item => ({
    value: item.value,
    label: `${item.label} (%)`
  }))

  return (
    <>
      <CommonObject pricingList={pricingList} />
      <ConditionApply />
      <PriceAndPromotion listCurrency={listCurrency} listTax={newListTax} listUnit={listUnit} />
      <Fee listTax={newListTax} listCurrency={listCurrency} />
      <AddionalServiceSection />

      {hiddenButton ? null : (
        <div className='flex justify-end gap-4 px-6 py-4'>
          <Button
            onClick={e => {
              e.stopPropagation()
              // Toggle trạng thái thêm bảng giá
              handleCancel()
              // form.resetFields()
            }}
          >
            Hủy
          </Button>
          <Button type='primary' onClick={handleSubmit}>
            Xác nhận
          </Button>
        </div>
      )}
    </>
  )
}
