'use client'

import React, { useEffect, useState } from 'react'

import { useParams, usePathname, useRouter, useSearchParams } from 'next/navigation'

import { useQuery } from '@tanstack/react-query'

import { useNode } from '@craftjs/core'

import { Form, Spin, Tabs, Tag } from 'antd'

import { tagStatus } from '@views/product-catalog/constants/constants'

import { FormItemText } from '@components/form'

import DevPricing from '@/models/DevPricing'
import { useUser } from '@/hooks'

import {
  PricingInfoApprove,
  PricingInfoTab
} from '@views/custom-field/editor/sections/service-detail/pricing-detail/pricing-info'

import { PriceListHistory } from '../price-list/history'
import { message } from '@/components/notification'

// Màn chi tiết gói - Tab bảng giá - Chi tiết dịch vụ
export const PricingInfoScreen = () => {
  const {
    connectors: { connect, drag }
  } = useNode()

  // Khai báo form trong màn
  const [form] = Form.useForm()

  // Thông tin user
  const { user } = useUser()

  // Điều hướng
  const router = useRouter()

  // Thông tin đường dẫn
  const pathname = usePathname()

  // Hàm thoát màn chi tiết - chỉnh sửa
  const exit = () => {
    // Điều hướng URL theo tham số mới
    router.push(pathname)
  }

  const searchParams = useSearchParams()

  const { id: serviceId } = useParams()

  // ID của gói dịch vụ lấy từ URL
  const id = searchParams.get('pricingId')

  // Tab hiện tại lấy từ URL
  const tabPricing = searchParams.get('tabPricing')

  // Id history pricing
  const historyId = searchParams.get('historyId')

  // Cổng hiện tại của trang (admin hay dev)
  const portal = (user?.portalType?.toLowerCase() + '-portal') as 'admin-portal' | 'partner-portal'

  // Tab hiện tại - khởi tạo từ tabPricing nếu có, nếu không thì mặc định là PROCESSING
  const [tab, changeTab] = useState<'PROCESSING' | 'APPROVED' | 'HISTORY'>(
    tabPricing && ['PROCESSING', 'APPROVED', 'HISTORY'].includes(tabPricing)
      ? (tabPricing as 'PROCESSING' | 'APPROVED' | 'HISTORY')
      : 'PROCESSING'
  )

  // Trạng thái phê duyệt của gói
  const approveStatus = Form.useWatch('approveStatus', form)

  // Trạng thái gói đã được duyệt
  const approved = approveStatus === 'APPROVED'

  // Trạng thái hiển thị nút phê duyệt gói
  const approving = approveStatus === 'AWAITING_APPROVAL' && portal === 'admin-portal' && tab === 'PROCESSING'

  // Gọi API lấy chi tiết gói dịch vụ dựa theo ID và trạng thái lấy được
  const { isFetching } = useQuery({
    queryKey: ['getPricingDetail', tab, portal, id],
    queryFn: async () => {
      try {
        historyId
          ? await DevPricing.getDetailPricingHistory(portal, historyId, 'APPROVED').then(form.setFieldsValue)
          : await DevPricing.getPricing(portal, id, tab).then(form.setFieldsValue)
      } catch (e: any) {
        if (e.errorCode === 'error.object.not.found') {
          message.error('Thông tin lịch sử không thuộc dịch vụ này')
          setTimeout(() => {
            router.push(`/product-catalog/detail/${serviceId}?tab=2`)
          }, 500)
        }

        return e
      }
    },

    enabled: (!!id || !!historyId) && ['PROCESSING', 'APPROVED'].includes(tab)
  })

  // Hàm chuyển tab với cập nhật URL
  const handleTabChange = (newTab: string) => {
    changeTab(newTab as 'PROCESSING' | 'APPROVED' | 'HISTORY')

    const params = new URLSearchParams(searchParams.toString())

    params.set('tabPricing', newTab)
    router.push(`${pathname}?${params.toString()}`)
  }

  // Chuyển tab khi click vào phiên bản lịch sử
  useEffect(() => {
    if (tabPricing && ['PROCESSING', 'APPROVED', 'HISTORY'].includes(tabPricing) && tabPricing !== tab) {
      changeTab(tabPricing as 'PROCESSING' | 'APPROVED' | 'HISTORY')
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tabPricing])

  return (
    <div
      className='w-full'
      ref={(ref: HTMLDivElement | null) => {
        if (ref) {
          connect(drag(ref))
        }
      }}
    >
      <Spin spinning={isFetching}>
        <Form layout='vertical' form={form}>
          {/* Tiêu đề gói */}
          <div className='mb-3 flex items-center gap-3'>
            <i className='onedx-chevron-left size-5 cursor-pointer transition hover:text-primary' onClick={exit} />
            <FormItemText name='pricingName' />
            <FormItemText
              name='approveStatus'
              value={(value: keyof typeof tagStatus) => (
                <Tag color={tagStatus[value]?.color}>{tagStatus[value]?.text}</Tag>
              )}
            />
          </div>
          {/* Tab thông tin gói */}
          <Tabs
            activeKey={tab}
            onChange={handleTabChange}
            items={
              [
                {
                  key: 'PROCESSING',
                  forceRender: true,
                  label: 'Thông tin đang xử lý',
                  children: <PricingInfoTab status='PROCESSING' editable />
                },
                approved && [
                  {
                    key: 'APPROVED',
                    forceRender: true,
                    label: 'Thông tin đã duyệt',
                    children: <PricingInfoTab status='APPROVED' />
                  },
                  {
                    key: 'HISTORY',
                    label: 'Lịch sử hoạt động',
                    children: <PriceListHistory />
                  }
                ]
              ].flat() as any[]
            }
          />
          {/* Phê duyệt gói */}
          {approving && <PricingInfoApprove />}
        </Form>
      </Spin>
    </div>
  )
}

PricingInfoScreen.craft = {
  props: {},
  rules: {
    canMoveIn: () => false
  },
  custom: {
    isDeletable: false
  }
}
