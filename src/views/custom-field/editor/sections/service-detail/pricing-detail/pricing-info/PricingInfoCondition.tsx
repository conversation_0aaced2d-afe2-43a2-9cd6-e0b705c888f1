import React from 'react'

import { Collapse, Tooltip } from 'antd'

import { CUSTOMER_TYPE_NAME, CYCLE_TYPE } from '@/constants/convert'

import { FormItemText } from '@components/form'

import { PricingInfoFrame } from '@views/custom-field/editor/sections/service-detail/pricing-detail/pricing-info/PricingInfoFrame'

// Điều kiện áp dụng - Chi tiết gói dịch vụ
export const PricingInfoCondition = ({ editable }: { editable?: boolean }) => {
  return (
    <PricingInfoFrame name='Đ<PERSON><PERSON> tượng áp dụng' editable={editable}>
      <FormItemText
        label='Đ<PERSON>i tượng khách hàng'
        name='customerTypeCode'
        value={(value: string[]) => value?.map(item => CUSTOMER_TYPE_NAME[item]).join(', ')}
      />
      <Collapse
        ghost
        className='space-y-4'
        expandIconPosition='end'
        defaultActiveKey={['condition', 'duration']}
        items={[
          {
            key: 'condition',
            className: 'rounded-lg bg-[#02173C06]',
            classNames: { header: 'text-sm font-semibold text-gray-8', body: 'pt-0' },
            label: 'Áp dụng điều kiện bổ sung',
            children: (
              <FormItemText
                name='pricingConfig'
                value={(value: any) => (
                  <div className='grid w-full grid-cols-3 gap-4 border-t border-solid border-neutral-200 pt-4'>
                    <FormItemText
                      label='Tỉnh thành'
                      value={
                        <Tooltip
                          placement='topLeft'
                          title={value?.provinceId?.map((item: any) => item?.name).join(', ')}
                        >
                          <div className='line-clamp-2'>
                            {value?.provinceId?.length
                              ? value?.provinceId?.map((item: any) => item?.name).join(', ')
                              : 'Chưa có thông tin'}
                          </div>
                        </Tooltip>
                      }
                    />
                    <FormItemText
                      label='Phường/Xã'
                      value={
                        <Tooltip placement='topLeft' title={value?.wardId?.map((item: any) => item?.name).join(', ')}>
                          <div className='line-clamp-2'>
                            {value?.wardId?.length
                              ? value?.wardId?.map((item: any) => item?.name).join(', ')
                              : 'Chưa có thông tin'}
                          </div>
                        </Tooltip>
                      }
                    />
                    <FormItemText
                      label='Phố/đường'
                      value={
                        <Tooltip placement='topLeft' title={value?.streetId?.map((item: any) => item?.name).join(', ')}>
                          <div className='line-clamp-2'>
                            {value?.streetId?.length
                              ? value?.streetId?.map((item: any) => item?.name).join(', ')
                              : 'Chưa có thông tin'}
                          </div>
                        </Tooltip>
                      }
                    />
                  </div>
                )}
              />
            )
          },
          {
            key: 'duration',
            className: 'rounded-lg bg-[#02173C06]',
            classNames: { header: 'text-sm font-semibold text-gray-8', body: 'pt-0' },
            label: 'Thời gian cam kết tối thiểu',
            children: (
              <FormItemText
                name='pricingCommitmentTime'
                value={(value: any) => (
                  <div className='grid w-full grid-cols-4 gap-4 border-t border-solid border-neutral-200 pt-4'>
                    <FormItemText
                      label='Thời hạn cam kết'
                      value={
                        value?.intervalValue
                          ? value?.intervalValue + ' ' + CYCLE_TYPE[String(value?.intervalType)]
                          : 'Chưa có thông tin'
                      }
                    />
                    <FormItemText
                      label='Quy tắc chấm dứt phí'
                      value={
                        <Tooltip placement='topLeft' title={value?.terminationFee}>
                          <div className='line-clamp-2'>{value?.terminationFee || 'Chưa có thông tin'}</div>
                        </Tooltip>
                      }
                    />
                    <div className='col-span-2'>
                      <FormItemText
                        label='Chính sách gia hạn sau cam kết'
                        value={
                          <Tooltip placement='topLeft' title={value?.renewalPolicy}>
                            <div className='line-clamp-2'>{value?.renewalPolicy || 'Chưa có thông tin'}</div>
                          </Tooltip>
                        }
                      />
                    </div>
                  </div>
                )}
              />
            )
          }
        ]}
      />
    </PricingInfoFrame>
  )
}

PricingInfoCondition.craft = {
  props: {},
  rules: {
    canMoveIn: () => false
  },
  custom: {
    isDeletable: false
  }
}
