import React from 'react'

import { Form, Radio, Input, Select, Checkbox } from 'antd'
import { useNode } from '@craftjs/core'

import classNames from 'classnames'

import { isArray } from 'lodash'

import { CustomCollapse } from '@/components/custom-field/common/CustomCollapse'

import { normalizeInputNumber } from '@/validator'
import type { SetupInformationFormData } from '@/types/custom-field/pricing'
import { DURATION_TYPE_CONVERT } from '@/constants/convert'

enum PricingType {
  PREPAY = 'PREPAY',
  POSTPAID = 'POSTPAID'
}

enum YesNo {
  YES = 'YES',
  NO = 'NO'
}

enum ChangeQuantityType {
  INCREASE = 'INCREASE',
  DECREASE = 'DECREASE',
  ALL = 'ALL',
  NONE = 'NONE'
}

enum TimingType {
  NOW = 'NOW',
  END_OF_PERIOD = 'END_OF_PERIOD'
}

enum DurationType {
  DAY = 0,
  WEEK = 1,
  MONTH = 2,
  YEAR = 3,
  QUARTER = 4
}

enum InstallationType {
  HOME_INSTALL = 'homeInstall',
  NO_INSTALL = 'noInstall'
}

enum PaymentCycleType {
  CHANGE = 'CHANGE',
  CONTINUE = 'CONTINUE'
}

export const configurationDefault: SetupInformationFormData = {
  pricingType: PricingType.PREPAY,
  hasChangePrice: YesNo.YES,
  hasChangeQuantity: ChangeQuantityType.NONE,
  hasRefund: YesNo.NO,
  hasRenew: YesNo.YES,
  activeDate: -1,
  changePricingPaymentTime: undefined,
  durationType: DurationType.DAY,
  cancelDate: TimingType.END_OF_PERIOD,
  updateSubscriptionDate: TimingType.END_OF_PERIOD,
  changePricingDate: TimingType.END_OF_PERIOD,
  installationConfig: InstallationType.HOME_INSTALL,
  typeActiveInPaymentType: PaymentCycleType.CHANGE,
  paymentRequest: YesNo.YES
}

export const convertConfiguration = (data: any): SetupInformationFormData => {
  const getChangeQuantity = (): ChangeQuantityType => {
    if (isArray(data.hasChangeQuantity)) {
      if (data.hasChangeQuantity.length === 0) {
        return ChangeQuantityType.NONE
      } else if (data.hasChangeQuantity.length > 1) {
        return ChangeQuantityType.ALL
      } else {
        return data.hasChangeQuantity[0]
      }
    }

    return ChangeQuantityType.NONE
  }

  const durationType =
    typeof data.durationType === 'string' && data.durationType in DURATION_TYPE_CONVERT
      ? DURATION_TYPE_CONVERT[data.durationType as keyof typeof DURATION_TYPE_CONVERT]
      : data.durationType || configurationDefault.durationType

  return {
    pricingType: data.pricingType || configurationDefault.pricingType,
    hasChangePrice: data.hasChangePrice || configurationDefault.hasChangePrice,
    hasChangeQuantity: getChangeQuantity(),
    hasRefund: data.hasRefund || configurationDefault.hasRefund,
    hasRenew: data.hasRenew || configurationDefault.hasRenew,
    activeDate: data.activeDate || configurationDefault.activeDate,
    changePricingPaymentTime: data.changePricingPaymentTime || configurationDefault.changePricingPaymentTime,
    durationType,
    cancelDate: data.cancelDate || configurationDefault.cancelDate,
    updateSubscriptionDate: data.updateSubscriptionDate || configurationDefault.updateSubscriptionDate,
    changePricingDate: data.changePricingDate || configurationDefault.changePricingDate,
    installationConfig: data.installationConfig || configurationDefault.installationConfig,
    typeActiveInPaymentType: data.typeActiveInPaymentType || configurationDefault.typeActiveInPaymentType,
    paymentRequest: data.paymentRequest || configurationDefault.paymentRequest
  }
}

export const SetupInformation = () => {
  const form = Form.useFormInstance()

  const typeActiveInPaymentType = Form.useWatch('typeActiveInPaymentType', form)

  const {
    connectors: { connect, drag }
  } = useNode()

  const isOneTime = form.getFieldValue('isOneTime')

  return (
    <div
      className='m-2'
      ref={(ref: HTMLDivElement | null) => {
        if (ref) {
          connect(drag(ref))
        }
      }}
    >
      <CustomCollapse
        label='Thông tin thiết lập'
        className='w-full'
        styles={{
          body: {
            backgroundColor: '#fff'
          }
        }}
      >
        <div className='grid grid-cols-2 gap-4'>
          {isOneTime ? (
            <>
              {/* Cột trái */}
              <div className='flex flex-col gap-5 rounded-lg bg-bg-gray-alpha-12 p-3'>
                <Form.Item
                  name='pricingType'
                  label='Thời điểm thanh toán'
                  className='mb-0'
                  rules={[{ required: true, message: 'Vui lòng chọn thời điểm thanh toán' }]}
                  required
                  initialValue={configurationDefault.pricingType}
                >
                  <Radio.Group className='grid w-full grid-cols-2'>
                    <Radio value={PricingType.PREPAY}>Trả trước</Radio>
                    <Radio value={PricingType.POSTPAID}>Trả sau</Radio>
                  </Radio.Group>
                </Form.Item>

                <Form.Item
                  name='hasChangePrice'
                  label='Cho phép thay đổi giá'
                  className='mb-0'
                  rules={[{ required: true, message: 'Vui lòng chọn' }]}
                  required
                  initialValue={configurationDefault.hasChangePrice}
                >
                  <Radio.Group className='grid w-full grid-cols-2'>
                    <Radio value={YesNo.YES}>Có</Radio>
                    <Radio value={YesNo.NO}>Không</Radio>
                  </Radio.Group>
                </Form.Item>

                <Form.Item name='hasChangeQuantity' label='Cho phép thay đổi số lượng giữa kỳ' className='mb-0'>
                  <Checkbox.Group>
                    <Checkbox value='INCREASE'>Cho phép tăng</Checkbox>
                    <Checkbox value='DECREASE'>Cho phép giảm</Checkbox>
                  </Checkbox.Group>
                </Form.Item>

                <Form.Item
                  name='hasRefund'
                  label='Cho phép hoàn trả'
                  className='mb-0'
                  rules={[{ required: true, message: 'Vui lòng chọn' }]}
                  required
                  initialValue={configurationDefault.hasRefund}
                >
                  <Radio.Group className='grid w-full grid-cols-2'>
                    <Radio value={YesNo.YES}>Có</Radio>
                    <Radio value={YesNo.NO}>Không</Radio>
                  </Radio.Group>
                </Form.Item>

                <Form.Item
                  name='hasRenew'
                  label='Cho phép gia hạn'
                  className='mb-0'
                  rules={[{ required: true, message: 'Vui lòng chọn' }]}
                  required
                  initialValue={configurationDefault.hasRenew}
                >
                  <Radio.Group className='grid w-full grid-cols-2'>
                    <Radio value={YesNo.YES}>Có</Radio>
                    <Radio value={YesNo.NO}>Không</Radio>
                  </Radio.Group>
                </Form.Item>

                <div className='grid grid-cols-1 gap-2.5'>
                  <Form.Item
                    name='activeDate'
                    label='Thời gian được kích hoạt lại sau huỷ'
                    className='mb-0'
                    rules={[{ required: true, message: 'Vui lòng chọn' }]}
                    required
                    initialValue={configurationDefault.activeDate}
                  >
                    <Radio.Group className='grid w-full grid-cols-2'>
                      <Radio value={-1}>Vĩnh viễn</Radio>
                      <Radio value={1}>Thời gian cho phép</Radio>
                    </Radio.Group>
                  </Form.Item>

                  <Form.Item
                    noStyle
                    shouldUpdate={(prevValues, currentValues) => prevValues.activeDate !== currentValues.activeDate}
                  >
                    {({ getFieldValue }) =>
                      getFieldValue('activeDate') === 1 && (
                        <Form.Item
                          name='changePricingPaymentTime'
                          className='flex-1'
                          normalize={value => normalizeInputNumber(value)}
                          required
                          rules={[
                            { required: true, message: 'Thời gian được kích hoạt lại sau hủy không được bỏ trống' }
                          ]}
                        >
                          <Input
                            className='w-full rounded-lg'
                            placeholder='Nhập thời gian'
                            maxLength={3}
                            addonBefore={
                              <Form.Item
                                name='durationType'
                                noStyle
                                initialValue={configurationDefault.durationType}
                                getValueFromEvent={value => value}
                                getValueProps={value => ({
                                  // Transform giá trị MONTHLY/DAILY... thành số khi render
                                  value:
                                    typeof value === 'string'
                                      ? DURATION_TYPE_CONVERT[value as 'DAY' | 'MONTH' | 'YEAR']
                                      : value
                                })}
                              >
                                <Select
                                  options={
                                    [
                                      { value: DurationType.DAY, label: 'Ngày' },
                                      { value: DurationType.WEEK, label: 'Tuần' },
                                      { value: DurationType.MONTH, label: 'Tháng' },
                                      { value: DurationType.YEAR, label: 'Năm' }
                                    ] as { value: number; label: string }[]
                                  }
                                />
                              </Form.Item>
                            }
                          />
                        </Form.Item>
                      )
                    }
                  </Form.Item>
                </div>
              </div>

              {/* Cột phải */}
              <div className='flex flex-col gap-5 rounded-lg p-3'>
                <Form.Item
                  name='cancelDate'
                  label='Thời điểm huỷ sau khi thao tác huỷ'
                  className='mb-0'
                  rules={[{ required: true, message: 'Vui lòng chọn' }]}
                  required
                  initialValue={configurationDefault.cancelDate}
                >
                  <Radio.Group className='grid w-full grid-cols-2'>
                    <Radio value={TimingType.END_OF_PERIOD}>Hết chu kỳ</Radio>
                    <Radio value={TimingType.NOW}>Ngay lập tức</Radio>
                  </Radio.Group>
                </Form.Item>

                <Form.Item
                  name='updateSubscriptionDate'
                  label='Thời điểm áp dụng chỉnh sửa thuê bao'
                  className='mb-0'
                  rules={[{ required: true, message: 'Vui lòng chọn' }]}
                  required
                  initialValue={configurationDefault.updateSubscriptionDate}
                >
                  <Radio.Group className='grid w-full grid-cols-2'>
                    <Radio value={TimingType.END_OF_PERIOD}>Hết chu kỳ</Radio>
                    <Radio value={TimingType.NOW}>Ngay lập tức</Radio>
                  </Radio.Group>
                </Form.Item>

                <Form.Item
                  name='changePricingDate'
                  label='Thời điểm áp dụng đổi gói'
                  className='mb-0'
                  rules={[{ required: true, message: 'Vui lòng chọn' }]}
                  required
                  initialValue={configurationDefault.changePricingDate}
                >
                  <Radio.Group className='grid w-full grid-cols-2'>
                    <Radio value={TimingType.END_OF_PERIOD}>Hết chu kỳ</Radio>
                    <Radio value={TimingType.NOW}>Ngay lập tức</Radio>
                  </Radio.Group>
                </Form.Item>

                {/*<Form.Item*/}
                {/*  name='installationConfig'*/}
                {/*  label='Cấu hình lắp đặt'*/}
                {/*  className='mb-0'*/}
                {/*  rules={[{ required: true, message: 'Vui lòng chọn' }]}*/}
                {/*  required*/}
                {/*  initialValue={configurationDefault.installationConfig}*/}
                {/*>*/}
                {/*  <Radio.Group className='grid w-full grid-cols-2'>*/}
                {/*    <Radio value={InstallationType.HOME_INSTALL}>Lắp đặt tại nhà</Radio>*/}
                {/*    <Radio value={InstallationType.NO_INSTALL}>Không lắp đặt</Radio>*/}
                {/*  </Radio.Group>*/}
                {/*</Form.Item>*/}

                <Form.Item
                  name='typeActiveInPaymentType'
                  label='Kích hoạt lại khi dịch vụ đang trong chu kỳ thanh toán'
                  className='mb-0'
                  required
                  initialValue={configurationDefault.typeActiveInPaymentType}
                  rules={[{ required: true, message: 'Vui lòng chọn' }]}
                >
                  <Radio.Group className='grid w-full grid-cols-2'>
                    <Radio value={PaymentCycleType.CHANGE}>
                      <span className='text-sm leading-tight'>
                        Chu kỳ thanh toán mới được bắt đầu từ ngày kích hoạt
                      </span>
                    </Radio>
                    <Radio value={PaymentCycleType.CONTINUE}>Sử dụng chu kỳ thanh toán cũ</Radio>
                  </Radio.Group>
                </Form.Item>
                <Form.Item
                  name='paymentRequest'
                  initialValue={configurationDefault.paymentRequest}
                  label='Yêu cầu thanh toán chu kỳ tiếp theo'
                  className={classNames('mb-0', {
                    hidden: typeActiveInPaymentType === PaymentCycleType.CHANGE
                  })}
                >
                  <Radio.Group className='grid w-full grid-cols-2'>
                    <Radio value={YesNo.YES}>Có</Radio>
                    <Radio value={YesNo.NO}>Không</Radio>
                  </Radio.Group>
                </Form.Item>
              </div>
            </>
          ) : (
            <>
              <Form.Item name='hasChangeQuantity' label='Cho phép thay đổi số lượng giữa kỳ' className='mb-0'>
                <Checkbox.Group>
                  <Checkbox value='INCREASE'>Cho phép tăng</Checkbox>
                  <Checkbox value='DECREASE'>Cho phép giảm</Checkbox>
                </Checkbox.Group>
              </Form.Item>

              <Form.Item
                name='hasRefund'
                label='Cho phép hoàn trả'
                className='mb-0'
                rules={[{ required: true, message: 'Vui lòng chọn' }]}
                required
                initialValue={configurationDefault.hasRefund}
              >
                <Radio.Group className='grid w-full grid-cols-2'>
                  <Radio value={YesNo.YES}>Có</Radio>
                  <Radio value={YesNo.NO}>Không</Radio>
                </Radio.Group>
              </Form.Item>
            </>
          )}
        </div>
      </CustomCollapse>
    </div>
  )
}
