'use client'

import React from 'react'

import { useParams } from 'next/navigation'

import { Modal, type TableProps, Tag } from 'antd'

import { useQuery } from '@tanstack/react-query'

import { useSelector } from 'react-redux'

import { tagStockStatus } from '@components/inventory/constants/constants'

import Warehouse from '@/models/Warehouse'

import { selectActiveKey } from '@/redux-store/slices/customFieldSlice'

import { WrapSection } from '@components/custom-field/common'

import { StyledTable } from '@views/custom-field/editor/sections/create-service/price-list'

import { EmptyData } from '@views/management/current/EmptyData'

export const Inventory: React.FC = () => {
  const { id } = useParams<{ id: string }>()

  const [isModalVisible, setIsModalVisible] = React.useState(false)
  const [dataSetModal, setDataSetModal] = React.useState<any>([])

  // selected tab
  const activeKey = useSelector(selectActiveKey)

  const { data: warehouseList } = useQuery<any>({
    queryKey: ['getInventoryByProductId', activeKey],
    queryFn: async () => {
      return await Warehouse.getInventoryByProductId(Number(id))
    },
    enabled: !!id
  })

  const mainColumns: TableProps['columns'] = [
    {
      title: 'STT',
      dataIndex: 'index',
      key: 'index',
      render: (_: any, _record: any, index: number) => <div className='pl-5'>{index + 1}</div>,
      width: 80
    },
    {
      title: 'Tên kho',
      dataIndex: 'warehouses_name',
      key: 'warehouses_name',
      width: 100,
      render: (text: string, record: any) => (
        <div
          className='hover:cursor-pointer hover:text-primary'
          onClick={() => {
            setIsModalVisible(true)
            setDataSetModal((JSON.parse(record.stock_list) as any[]) || record.stock_list)
          }}
        >
          {text}
        </div>
      )
    },
    {
      title: 'Tồn kho thực tế',
      dataIndex: 'sum_available_stock',
      key: 'sum_available_stock',
      width: 100
    },
    {
      title: 'Hàng hóa được giữ',
      dataIndex: 'sum_reserved_stock',
      key: 'sum_reserved_stock',
      width: 100
    },
    {
      title: 'Tổng sản phẩm',
      dataIndex: 'sum_total_stock',
      key: 'sum_total_stock',
      width: 100
    }
  ]

  const modalColumns: TableProps['columns'] = [
    {
      title: 'STT',
      dataIndex: 'index',
      key: 'index',
      render: (_: any, _record: any, index: number) => <div className='pl-5'>{index + 1}</div>,
      width: 80
    },
    {
      title: 'Tên biến thể',
      dataIndex: 'name_variant',
      key: 'warehouses_name',
      width: 100
    },
    {
      title: 'Trạng thái',
      dataIndex: 'stock_status',
      key: 'stock_status',
      width: 100,
      render: (value: any) => {
        // @ts-ignore
        const tagInfo = tagStockStatus[value]

        return (
          <Tag color={tagInfo?.color}>
            <div style={{ color: `${tagInfo?.textColor}` }}>{tagInfo?.text}</div>
          </Tag>
        )
      }
    },
    {
      title: 'SL có sẵn',
      dataIndex: 'available_stock',
      key: 'available_stock',
      width: 100
    },
    {
      title: 'SL được giữ',
      dataIndex: 'reserved_stock',
      key: 'reserved_stock',
      width: 100
    },
    {
      title: 'Tổng SP',
      dataIndex: 'total_stock',
      key: 'total_stock',
      width: 100
    }
  ]

  return (
    <div>
      <WrapSection id='warehouse-list' title='Tồn kho'>
        <div className='overflow-x-auto'>
          <StyledTable
            className='custom-warehouse-table'
            columns={mainColumns}
            dataSource={warehouseList}
            rowKey='id'
            pagination={false}
            locale={{ emptyText: <EmptyData label='Không có dữ liệu theo thông tin tìm kiếm' /> }}
          />
        </div>
        <Modal
          title='Danh sách hàng tồn kho theo biến thể'
          open={isModalVisible}
          onCancel={() => setIsModalVisible(false)}
          width={1000}
        >
          <div className='overflow-x-auto'>
            <StyledTable
              className='custom-warehouse-table'
              columns={modalColumns}
              dataSource={dataSetModal}
              rowKey='id'
              onRow={() => ({
                onClick: e => {
                  // Ngăn row expansion khi click checkbox
                  if ((e.target as HTMLElement).closest('.ant-checkbox-wrapper')) {
                    e.stopPropagation()
                  }
                }
              })}
              locale={{ emptyText: <EmptyData label='Không có dữ liệu theo thông tin tìm kiếm' /> }}
            />
          </div>
        </Modal>
      </WrapSection>
    </div>
  )
}
