import React, { useEffect, useMemo, useState } from 'react'

import { useParams, useSearchParams } from 'next/navigation'

import { Element, useNode } from '@craftjs/core'

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'

import { useDispatch, useSelector } from 'react-redux'

import { Switch } from 'antd'

import type { IServiceBasicInfo } from '@/types/custom-field'

import { selectActiveKey, updateInfoCustomField } from '@/redux-store/slices/customFieldSlice'

import { useGetServicesByCategoryId } from '@/hooks/useGetServicesByCategoryId'

import {
  ClassifyInfo,
  ConfigInfo,
  FeaturesInfo,
  GeneralInfo,
  LayoutsInfo,
  OutstandingTechnologyInfo,
  PaymentMethodInfo,
  ProductSuggestionsInfo,
  SEOConfigInfo,
  TopicInfo,
  UpdateReason,
  Inventory
} from '../../sections/service-detail'

import {
  Installation,
  PriceList,
  ShippingAndDelivery,
  TechnicalSpecification,
  VariantAttribute,
  WarrantyReturnPolicy
} from '@views/custom-field/editor/sections/service-detail/processing-information'

import { useUser } from '@/hooks'
import DevService from '@/models/DevService'

import styles from '@views/custom-field/editor/sections/service-detail/processing-information/priceList.module.css'
import { showMessage } from '@/components/notification/NotificationProvider'
import DXPortal from '@/models/DXPortal'

export const ServiceInfo = () => {
  const { id } = useParams<{ id: string }>()

  const { user } = useUser()

  const queryClient = useQueryClient()

  const searchParams = useSearchParams()

  const serviceDraftId = searchParams.get('draftId')

  const isDraft = searchParams.get('isDraft') === 'true'

  const {
    connectors: { connect, drag }
  } = useNode()

  const activeKey = useSelector(selectActiveKey)
  const dispatch = useDispatch()

  const [isChecked, setIsChecked] = useState(false)

  const isAdmin = user?.portalType === 'ADMIN'

  const CAN_UPDATE = useMemo(() => {
    return DXPortal.canAccessFeature(isAdmin ? 'admin/update-service' : 'dev/update-service', user.permissions)
  }, [user, isAdmin])

  const { data } = useQuery<IServiceBasicInfo>({
    queryKey: ['getServiceInfo', id, activeKey],
    // Không cần queryFn vì component cha đã thực hiện fetch
    // Chỉ cần sử dụng cache
    enabled: false // để không refetch
  })

  const { data: dataDraft } = useQuery<IServiceBasicInfo>({
    queryKey: ['getServiceInfoDraft', id, activeKey, serviceDraftId],
    // Không cần queryFn vì component cha đã thực hiện fetch
    // Chỉ cần sử dụng cache
    enabled: false // để không refetch
  })

  const serviceData = isDraft ? dataDraft : data

  const { data: serviceConfig } = useGetServicesByCategoryId(id)

  useEffect(() => {
    if (serviceData?.displayed) {
      setIsChecked(serviceData.displayed === 'VISIBLE')
    }

    if (serviceData?.classification) {
      dispatch(
        updateInfoCustomField({
          classification: serviceData?.classification,
          providerDTO: serviceData?.providerDTO,
          canUpdate: CAN_UPDATE
        })
      )
    }
  }, [serviceData]) // eslint-disable-line react-hooks/exhaustive-deps

  const changeDisplayStatus = useMutation({
    mutationFn: DevService.updateDisplayStatus,
    onSuccess: () => {
      showMessage.success('Cập nhật trạng thái hoạt động thành công')
      // Xóa cache để gọi lại API chi tiết
      queryClient.removeQueries({ queryKey: ['getServiceInfo', id, 'APPROVED'] })
      queryClient.removeQueries({ queryKey: ['getServiceInfo', id, 'PROCESSING'] })
    },
    onError: () => {
      showMessage.error('Cập nhật trạng thái hoạt động thất bại')
    }
  })

  return (
    <div
      className='w-full'
      ref={(ref: HTMLDivElement | null) => {
        if (ref) {
          connect(drag(ref))
        }
      }}
    >
      <div className='mb-5 flex items-center justify-between gap-3'>
        <div className='flex items-center gap-3'>
          <div className='h-5 w-1 rounded-sm bg-icon-neutral-lighter' />
          <div className='headline-16-semibold text-text-neutral-strong'>
            {activeKey === 'APPROVED' ? 'Thông tin đã duyệt' : 'Thông tin đang xử lý'}
          </div>
        </div>
        <div className='flex items-center gap-3'>
          <div className='headline-14-semibold text-text-neutral-strong'>Trạng thái hoạt động</div>
          <div className={styles.pricingTableSwitch}>
            <Switch
              checked={isChecked}
              onChange={e => {
                setIsChecked(e)
                changeDisplayStatus.mutate({
                  portal: !user.portalType ? 'admin' : user.portalType === 'ADMIN' ? 'admin' : 'dev',
                  id: Number(id),
                  status: e ? 'VISIBLE' : 'INVISIBLE'
                })
              }}
              style={{ backgroundColor: isChecked ? '#14C780' : undefined }}
              disabled={activeKey !== 'APPROVED'}
            />
          </div>
        </div>
      </div>
      <>
        <Element id='classify-info' is={ClassifyInfo} />
        <Element id='general-info' is={GeneralInfo} />
        {serviceConfig?.classification === 'PHYSICAL' ? (
          <>
            <Element id='technical-specification-detail' is={TechnicalSpecification} />
            <Element id='variant-attribute-detail' is={VariantAttribute} />
            <Element id='price-list-detail' is={PriceList} />
            <Element id='inventory-detail' is={Inventory} />
            <Element id='feature-detail' is={FeaturesInfo} />
            <Element id='shipping-delivery-detail' is={ShippingAndDelivery} />
            <Element id='warranty-return-policy-detail' is={WarrantyReturnPolicy} />
            <div className='grid grid-cols-3 gap-6'>
              <Element id='installation-detail' is={Installation} />
              <Element id='config-info' is={ConfigInfo} />
              <Element id='payment-method-info' is={PaymentMethodInfo} />
            </div>
          </>
        ) : (
          <>
            <div>
              <Element id='layouts-info' is={LayoutsInfo} />
              <Element id='outstanding-technology-info' is={OutstandingTechnologyInfo} />
              <Element id='feature-detail' is={FeaturesInfo} />
            </div>
            <div className='grid grid-cols-2 gap-4'>
              <Element id='config-info' is={ConfigInfo} />
              <Element id='payment-method-info' is={PaymentMethodInfo} />
            </div>
          </>
        )}

        <Element id='topic-info' is={TopicInfo} />
        <Element id='seo-info' is={SEOConfigInfo} />
        <Element id='product-suggestions-info' is={ProductSuggestionsInfo} />
        {activeKey === 'PROCESSING' && <Element id='update-reason' is={UpdateReason} />}
      </>
    </div>
  )
}
