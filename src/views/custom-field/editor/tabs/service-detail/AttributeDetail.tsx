import React, { useState } from 'react'

import { useParams } from 'next/navigation'

import { DeleteOutlined } from '@ant-design/icons'
import { useNode } from '@craftjs/core'
import { useMutation, useQuery } from '@tanstack/react-query'
import { Button, Col, Row, Spin, Switch, Table } from 'antd'

import { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors } from '@dnd-kit/core'

import type { DragEndEvent } from '@dnd-kit/core'

import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
  useSortable
} from '@dnd-kit/sortable'

import { CSS as DndCSS } from '@dnd-kit/utilities'

import { CustomCollapse } from '@/components/custom-field/common/CustomCollapse'
import {
  attributeDetailFields,
  attributes,
  convertAttributeApiList
} from '@/constants/custom-field/custom-conponents/variant'
import attributeInstance from '@/models/Attribute'
import type { Attribute } from '@/types/custom-field/custom-conponents/attribute'
import { baseColorLight } from '@/utils/colors'
import PreviewAttribute from '../../sections/create-service/variant/PreviewAttribute'
import { DetailField } from '../../sections/service-detail/variant/DetailField'
import { EmptyAttribute, TableAttribute } from '../../sections/create-service/variant'
import { ConfirmModal } from '@/views/custom-field/editor/sections/create-service/variant/modal/ConfirmModal'
import ModalAddAttributes from '../../sections/create-service/variant/modal/ModalAddAttributes'
import { useUser } from '@/hooks'
import { showMessage } from '@/components/notification/NotificationProvider'

interface SortableItemProps {
  item: any
  index: number
  attributeDetailFields: any
  baseColorLight: { [key: string]: string }
  deleteAttribute: any
  id: string
  ConfirmModal: any
  CustomCollapse: any
  DetailField: any
  PreviewAttribute: any
}

const SortableAttributeItem: React.FC<SortableItemProps> = ({
  item,
  index,
  attributeDetailFields,
  baseColorLight,
  deleteAttribute,
  id,
  ConfirmModal,
  CustomCollapse,
  DetailField,
  PreviewAttribute
}) => {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({ id: item.nameId })

  const style = {
    transform: DndCSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1
  }

  return (
    <div ref={setNodeRef} style={style}>
      <CustomCollapse
        visibleToolTip={false}
        key={item.nameId}
        label={
          <div className='flex items-center gap-2'>
            <div
              {...attributes}
              {...listeners}
              className='drag-handle flex cursor-grab items-center rounded p-1 hover:bg-gray-100 active:cursor-grabbing '
            >
              <i className='onedx-drag size-4' />
            </div>
            <span>{item.name}</span>
          </div>
        }
        defaultActiveKey={index === 0 ? ['1'] : ['0']}
        styles={{
          body: { padding: '16px', backgroundColor: baseColorLight['gray-alpha-12'] },
          header: {
            backgroundColor: baseColorLight['gray-alpha-12']
          }
        }}
      >
        <Row gutter={24}>
          <Col span={12}>
            <DetailField
              label={attributeDetailFields.generalInfo.title}
              fields={attributeDetailFields.generalInfo.fields}
              data={item}
            />
          </Col>
          <Col span={12}>
            <DetailField
              label={attributeDetailFields.displayInfo.title}
              fields={attributeDetailFields.displayInfo.fields}
              data={item}
            >
              {/* Preview */}
              <div className='grid grid-cols-2'>
                <div />
                <div className='flex items-center rounded-lg bg-bg-canvas p-3'>
                  <PreviewAttribute
                    attributeName={item.name || ''}
                    attributeDisplayType={item.attributeDisplayType}
                    lstValueAttribute={item.lstValueAttribute}
                    showLabel={false}
                  />
                </div>
              </div>
            </DetailField>
          </Col>
        </Row>

        <Row gutter={24} style={{ marginTop: 16 }}>
          <Col span={24}>
            <DetailField label={attributeDetailFields.valueInfo.title}>
              <Table
                className='custom-table'
                dataSource={item.lstValueAttribute.map((value: any, valueIndex: number) => ({
                  ...value,
                  stt: valueIndex + 1,
                  key: valueIndex
                }))}
                columns={[
                  {
                    title: 'STT',
                    dataIndex: 'stt',
                    key: 'stt',
                    className: 'body-14-regular'
                  },
                  {
                    title: 'Tên giá trị thuộc tính',
                    dataIndex: 'valueName',
                    key: 'valueName',
                    className: 'body-14-medium'
                  },
                  {
                    title: 'Hiển thị',
                    dataIndex: 'status',
                    key: 'status',
                    width: '98px',
                    render: (status: number) => (
                      <Switch
                        checked={status === 1}
                        onChange={() => {
                          // Handle switch change
                        }}
                        size='small'
                        className='custom-switch'
                      />
                    )
                  }
                ]}
                pagination={false}
                bordered={false}
                rowClassName={() => 'custom-table-row'}
              />
            </DetailField>
          </Col>
        </Row>

        <Row justify='center' style={{ marginTop: 16 }}>
          <Button
            danger
            icon={<DeleteOutlined />}
            className='custom-delete-button'
            onClick={() => {
              ConfirmModal.config({
                title: 'Xoá thuộc tính',
                description: (
                  <p className='py-4 text-body-14 text-text-neutral-strong'>
                    Bạn có chắn chắn muốn xoá thuộc tính &quot;<strong>{item?.name}</strong>&quot;
                  </p>
                ),
                iconType: 'DELETE',
                confirmButtonText: 'Xoá',
                onConfirm: () => {
                  deleteAttribute.mutate({
                    serviceId: id,
                    attributeId: item.attributeId ?? null
                  })
                },
                onCancel: () => {
                  // Handle cancel action
                }
              })
            }}
          >
            Xoá thuộc tính
          </Button>
        </Row>
      </CustomCollapse>
    </div>
  )
}

export const AttributeDetail = () => {
  const { id } = useParams<{ id: string }>()
  const { user } = useUser()
  const [data, setData] = useState<Attribute[]>([])
  const [updateData, setUpdateData] = useState<Attribute[]>([])

  const [isUpdateMode, setIsUpdateMode] = useState(false)

  const [openModal, setOpenModal] = useState<'create' | 'update' | false>(false)

  const {
    connectors: { connect, drag }
  } = useNode()

  const { refetch, isFetching } = useQuery({
    queryKey: ['getAttributeDetail'],
    queryFn: async () => {
      const res = await attributeInstance.getAttributesInService(id)
      const convertedData = convertAttributeApiList(res)

      setData(convertedData)

      return convertedData
      // return convertAttributeApiList(res)
    },
    enabled: !!id
  })

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates
    })
  )

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event

    if (active.id !== over?.id) {
      setData(items => {
        const oldIndex = items.findIndex(item => item.nameId === active.id)
        const newIndex = items.findIndex(item => item.nameId === over?.id)

        return arrayMove(items, oldIndex, newIndex)
      })

      reorderAttribute.mutate({
        serviceId: id,
        attributeOrder: data.map((item, index) => ({ id: Number(item?.attributeId), priority: index + 1 }))
      })
    }
  }

  const deleteAttribute = useMutation({
    mutationFn: attributeInstance.deleteAttributeInService,
    onSuccess: () => {
      showMessage.success('Xoá thuộc tính thành công')

      refetch()
    },
    onError: () => {
      showMessage.error('Xóa thuộc tính thất bại')
    }
  })

  const reorderAttribute = useMutation({
    mutationFn: attributeInstance.reorderAttributeInService,
    onError: () => {
      refetch()
    }
  })

  const updateAttribute = useMutation({
    mutationFn: attributeInstance.updateListAttribute,
    onSuccess: () => {
      showMessage.success('Cập nhật thuộc tính thành công')

      setTimeout(() => {
        refetch()
      }, 1000)
    },
    onError: () => {
      showMessage.error('Cập nhật thuộc tính thất bại')
    }
  })

  const handleSwitchUpdateMode = (mode: boolean) => {
    if (Boolean(mode)) {
      setUpdateData(data)
    } else {
      setUpdateData([])
    }

    setIsUpdateMode(mode)
  }

  return (
    <div
      ref={(ref: HTMLDivElement | null) => {
        if (ref) {
          connect(drag(ref))
        }
      }}
      className='w-full'
    >
      <Spin spinning={isFetching}>
        {isUpdateMode ? (
          <div className='flex flex-col gap-4'>
            <div className='flex items-center justify-between gap-2'>
              <div className='flex items-center gap-3'>
                <div className='h-5 w-1 rounded-sm bg-icon-neutral-lighter' />
                <div className='headline-16-semibold text-text-neutral-strong'>Cập nhật thuộc tính</div>
              </div>
              <div className='flex items-center gap-2'>
                <Button
                  type='default'
                  className='body-14-medium text-text-primary-default'
                  onClick={() => handleSwitchUpdateMode(false)}
                >
                  <i className='onedx-arrow-left size-4' />
                  <span>Quay lại</span>
                </Button>
                <Button
                  type='default'
                  className='body-14-medium text-text-primary-default'
                  onClick={() => {
                    updateAttribute.mutate({
                      serviceId: id,
                      body: attributes({
                        attributes: updateData
                      }),
                      portalType: user?.portalType || 'DEV'
                    })
                  }}
                >
                  <i className='onedx-edit size-4' />
                  <span>Lưu</span>
                </Button>
              </div>
            </div>
            {/* Nội dung form cập nhật thuộc tính sẽ được thêm vào đây sau */}
            <TableAttribute data={updateData} setData={setUpdateData} />
          </div>
        ) : (
          <div className='flex flex-col gap-4'>
            <div className='flex items-center justify-between gap-2'>
              <div className='flex items-center gap-3'>
                <div className='h-5 w-1 rounded-sm bg-icon-neutral-lighter' />
                <div className='headline-16-semibold text-text-neutral-strong'>Danh sách thuộc tính</div>
              </div>
              <div className='flex items-center gap-2'>
                <Button
                  type='default'
                  className='body-14-medium text-text-primary-default'
                  onClick={() => {
                    setOpenModal('create')
                  }}
                >
                  <i className='onedx-plus size-4' />
                  <span>Thêm thuộc tính</span>
                </Button>
                {data.length > 0 && (
                  <Button
                    type='default'
                    className='body-14-medium text-text-primary-default'
                    onClick={() => {
                      handleSwitchUpdateMode(true)
                      // router.push(`/product-catalog/update/${id}?step=1&section=variant-attribute`)
                    }}
                  >
                    <i className='onedx-edit size-4' />
                    <span>Cập nhật thuộc tính</span>
                  </Button>
                )}
              </div>
            </div>
            <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
              <SortableContext items={data.map(item => item.nameId)} strategy={verticalListSortingStrategy}>
                {data.length > 0 ? (
                  data.map((item: Attribute, index: number) => (
                    <SortableAttributeItem
                      key={item.nameId}
                      item={item}
                      index={index}
                      attributeDetailFields={attributeDetailFields}
                      baseColorLight={baseColorLight}
                      deleteAttribute={deleteAttribute}
                      id={id}
                      ConfirmModal={ConfirmModal}
                      CustomCollapse={CustomCollapse}
                      DetailField={DetailField}
                      PreviewAttribute={PreviewAttribute}
                    />
                  ))
                ) : (
                  <EmptyAttribute hiddenButton={true} />
                )}
              </SortableContext>
            </DndContext>
          </div>
        )}
        <ModalAddAttributes
          open={openModal}
          onClose={() => setOpenModal(false)}
          selectedData={data}
          onAddAttribute={(newAttribute, action) => {
            let newAttributeList = []

            if (action === 'addNew') {
              newAttributeList = [...newAttribute, ...data]
            } else if (action === 'update') {
              newAttributeList = data.map(attribute => {
                if (attribute.nameId === newAttribute[0].nameId) {
                  return { ...newAttribute[0], ...attribute }
                }

                return attribute
              })
            } else {
              const existingAttributes = data.filter(attribute => attribute.isNew)

              newAttributeList = [...newAttribute, ...existingAttributes]
            }

            updateAttribute.mutate({
              serviceId: id,
              body: attributes({
                attributes: newAttributeList
              }),
              portalType: user?.portalType || 'DEV'
            })

            setOpenModal(false)
          }}
        />
      </Spin>
    </div>
  )
}
