import type {
  CustomerType,
  CertificateType,
  CapabilityType,
  PartnerStatus,
  CreatePartnerRequest
} from '@/types/partner-portal/partner'

export const STATUS_ENUM = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE'
}

export const statusOptions: {
  label: string
  value: PartnerStatus
}[] = [
  { label: 'Hoạt động', value: 'ACTIVE' },
  { label: 'Không hoạt động', value: 'INACTIVE' }
]

export const customerTypeOptions: { value: CustomerType; label: string }[] = [
  { value: 'PERSONAL', label: 'Cá nhân' },
  { value: 'ENTERPRISE', label: '<PERSON><PERSON>h nghiệp' }
]
export const representativeTypeOptions: { value: CertificateType; label: string }[] = [
  { value: 'CMTND_CCCD', label: 'CMND' },
  { value: 'PASSPORT', label: 'Hộ chiếu' },
  { value: 'CCCD', label: '<PERSON>ăn cước công dân' },
  { value: 'OTHER', label: '<PERSON>h<PERSON><PERSON>' },
  { value: 'ALL', label: 'Tất cả' }
]

export const serviceCapabilityOptions: { value: CapabilityType; label: string }[] = [
  { value: 'EQUIPMENT', label: 'Cung cấp thiết bị' },
  { value: 'INSTALLATION_SERVICE', label: 'Dịch vụ lắp đặt' },
  { value: 'MAINTENANCE_SERVICE', label: 'Dịch vụ bảo trì, sửa chữa' },
  { value: 'SHIPPING_SERVICE', label: 'Dịch vụ vận chuyển' },
  { value: 'SOFTWARE', label: 'Phát triển phần mềm, ứng dụng' },
  { value: 'IOT_CONSULTING', label: 'Tư vấn giải pháp IoT' }
]

export const initialValuesCreate: Partial<CreatePartnerRequest> = {
  status: 'ACTIVE',
  customerType: 'ENTERPRISE',
  secondaryContacts: [{}],
  welcomeEmail: false
}
