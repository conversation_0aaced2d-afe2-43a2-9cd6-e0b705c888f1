import type { FormInstance } from 'antd'

export const convertSubmitData = (data: any): any => {
  // Perform the necessary data transformation here
  return {
    ...data,
    // Example transformation
    status: data.status.toUpperCase()
  }
}

export const addFormListItem = (form: FormInstance, fieldName: string, initialValue: any = {}) => {
  const existingItems = form.getFieldValue(fieldName) || []

  form.setFieldsValue({
    [fieldName]: [...existingItems, initialValue]
  })
}
