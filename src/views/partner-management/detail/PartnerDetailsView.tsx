'use client'

import React from 'react'

import { useParams } from 'next/navigation'

import { PartnerDetail } from '@/components/partner-management'
import { useUser } from '@/hooks'

// Lưu ý: sau khi ghép api, hãy đổi tên thư mục từ detail thành [id]
const PartnerDetailsView = ({ isDashboard = false }: { isDashboard?: boolean }) => {
  const { id } = useParams<{ id: string }>()

  const { user } = useUser()

  const userId = !!id ? id : user.id

  return (
    <div>
      <PartnerDetail id={userId} isDashboard={isDashboard} />
    </div>
  )
}

export default PartnerDetailsView
