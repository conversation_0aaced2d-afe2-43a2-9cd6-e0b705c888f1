'use client'

import React from 'react'

import { useRouter } from 'next/navigation'

import { But<PERSON> } from 'antd'
import { PlusOutlined } from '@ant-design/icons'

import { PartnersList } from '@/components/partner-management'

const PartnersView: React.FC = () => {
  const router = useRouter()

  return (
    <>
      {/* Header */}
      <div className='max-w-full overflow-hidden rounded-[12px] bg-[#F2F4f9]'>
        <div className='mb-2 flex w-full items-center justify-between bg-white p-4'>
          <div className='flex items-center gap-3 text-xl font-semibold text-black'>
            <div className='h-5 w-1 rounded-sm bg-gray-3'></div>
            Quản lý đối tác
          </div>
          <div className='flex gap-3'>
            <Button
              size='large'
              icon={<PlusOutlined className='text-white' />}
              type='primary'
              onClick={() => router.push('/partner-management/create')}
            >
              Tạo đối tác mới
            </Button>
          </div>
        </div>
      </div>
      <PartnersList />
    </>
  )
}

export default PartnersView
