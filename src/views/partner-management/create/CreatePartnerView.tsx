'use client'

import React from 'react'

import { useRouter } from 'next/navigation'

import { useMutation, useQueries } from '@tanstack/react-query'

import { Spin } from 'antd'

import { PartnerForm } from '@/components/partner-management'
import { PartnerTitle } from '@/components/partner-management/common'
import { initialValuesCreate } from '../common'
import partnerManagementInstance from '@/models/PartnerManagement'
import { message } from '@/components/notification'

interface CreatePartnerViewProps {
  id?: number
}

const CreatePartnerView: React.FC<CreatePartnerViewProps> = ({ id }) => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const isEditMode = Boolean(id)

  const router = useRouter()

  const createMutation = useMutation({
    mutationKey: ['createPartner'],
    mutationFn: partnerManagementInstance.adminCreatePartner,
    onSuccess: () => {
      message.success('Tạo đối tác thành công')
      setTimeout(() => {
        router.push('/partner-management/list')
      }, 2000)
    },
    onError: () => {
      // handle error
      message.error('Tạo đối tác thất bại')
    }
  })

  const [{ data: partnerDetail, isLoading: isLoadingPartnerDetail }] = useQueries({
    queries: [
      {
        queryKey: ['getPartner', id],
        queryFn: async () => {
          const partnerDetail = await partnerManagementInstance.getPartnerDetailInfo(String(id))

          return {
            ...partnerDetail,
            primaryEmail: partnerDetail.email
          }
        },
        enabled: isEditMode
      }
      // {
      //   queryKey: ['getPartnerServices', id],
      //   queryFn: () => partnerManagementInstance.getDocumentsList(String(id)),
      //   enabled: isEditMode
      // }
    ]
  })

  return (
    <Spin spinning={isLoadingPartnerDetail}>
      <div className='flex flex-col gap-2'>
        <PartnerTitle navigation={{ goBack: () => router.push('/partner-management/list') }}>
          {isEditMode ? 'Chỉnh sửa đối tác' : 'Tạo đối tác'}
        </PartnerTitle>

        {/* TODO: Implement partner create/edit form functionality */}
        <PartnerForm
          onSubmitAction={values => createMutation.mutate(values)}
          initialValues={isEditMode ? partnerDetail : initialValuesCreate}
          mode={isEditMode ? 'edit' : 'create'}
          id={id || -1} // Case Tạo để id = -1
        />
      </div>
    </Spin>
  )
}

export default CreatePartnerView
