import React from 'react'

import { useRouter } from 'next/navigation'

import { Button } from 'antd'

interface IServiceProvider {
  isEnterprise?: boolean
}

/** Nhà cung cấp dịch vụ */
export default function ServiceProvider({ isEnterprise = false }: IServiceProvider) {
  const router = useRouter()

  // region Buttons
  const ServiceProviderButtons = () => (
    <>
      <Button
        type='primary'
        className='h-10 w-full rounded-10'
        onClick={() => {
          router.push('https://onesme.vn/partner-portal/register')
        }}
      >
        Tìm hiểu thêm
      </Button>
      <Button
        type='primary'
        className='h-10 w-full rounded-10 bg-bg-primary-sme-orange-fixed'
        onClick={() => {
          router.push('https://onesme.vn/partner-portal/register')
        }}
      >
        Đăng kí ngay
      </Button>
    </>
  )
  // endregion

  // region Mobile
  const ServiceProviderMobile = () => (
    <div className='hidden bg-bgPrimary pb-8 text-white sm:block'>
      <div className='w-full'>
        <img src='/assets/images/pages/home/<USER>' className='size-full object-cover' />
      </div>
      <div className='container mx-auto mt-14 px-4'>
        <h2 className='text-center text-title-24 font-semibold sm:text-start'>
          Trở thành Đối tác cung cấp sản phẩm dịch vụ
        </h2>
        <div className='mt-4 text-body-14 font-normal'>
          Trở thành đối tác kinh doanh trên oneSME – Cơ Hội Mở Rộng Kinh Doanh và Tăng Trưởng Doanh Thu Với nền tảng
          công nghệ hiện đại và hệ sinh thái đa dạng, oneSME mang đến cơ hội tiếp cận hàng nghìn khách hàng tiềm năng,
          mở rộng thị trường và gia tăng doanh thu một cách đột phá. Tham gia ngay để được hỗ trợ toàn diện từ quảng bá
          sản phẩm, quản lý bán hàng đến chăm sóc khách hàng, giúp doanh nghiệp bứt phá và thành công bền vững trong môi
          trường kinh doanh số.
        </div>
        {/* Buttons */}
        <div className='mt-8 flex flex-col gap-2'>
          <ServiceProviderButtons />
        </div>
      </div>
    </div>
  )

  // endregion

  // region Desktop
  const ServiceProviderDesktop = () => (
    <div className='bg-bgPrimary sm:hidden'>
      <div className='relative mx-auto max-w-[1440px]'>
        <img
          width={720}
          height={531}
          className='absolute left-[-117px] top-0 z-10'
          loading='lazy'
          src='/assets/images/pages/home/<USER>'
        />
        <div className='container z-20 mx-auto flex py-[60px]'>
          <div className='z-30 w-[549px] rounded-lg bg-white p-[30px]'>
            <h2 className='text-title-32 font-semibold'>
              Trở thành Đối tác cung cấp <br /> sản phẩm dịch vụ
            </h2>
            <div className='mt-10 text-body-14'>
              Trở thành đối tác kinh doanh trên oneSME - Cơ Hội Mở Rộng Kinh Doanh và Tăng Trưởng Doanh Thu Với nền tảng
              công nghệ hiện đại và hệ sinh thái đa dạng, oneSME mang đến cơ hội tiếp cận hàng nghìn khách hàng tiềm
              năng, mở rộng thị trường và gia tăng doanh thu một cách đột phá. Tham gia ngay để được hỗ trợ toàn diện từ
              quảng bá sản phẩm, quản lý bán hàng đến chăm sóc khách hàng, giúp doanh nghiệp bứt phá và thành công bền
              vững trong môi trường kinh doanh số.
            </div>
            <div className='mt-[35px] flex items-center justify-between gap-[35px]'>
              <ServiceProviderButtons />
            </div>
          </div>
        </div>
        <img
          width={703}
          height={531}
          className='absolute right-0 top-0 z-10'
          loading='lazy'
          src={`/assets/images/pages/home/<USER>'dvdn' : 'dv'}-right.webp`}
        />
      </div>
    </div>
  )

  // endregion

  return (
    <>
      <ServiceProviderMobile />
      <ServiceProviderDesktop />
    </>
  )
}
