/**
 * ListTransitionView - Component hiển thị danh sách cấu hình chuyển đổi trạng thái dưới dạng bảng
 * Hỗ trợ tìm kiếm, lọ<PERSON>, phân trang và tùy chỉnh cột
 */
'use client'

import { useState, useEffect, useMemo } from 'react'

import { useRouter } from 'next/navigation'

import type { ColumnsType } from 'antd/es/table'
import { DatePicker, Tag, Select, Button } from 'antd'
import { styled } from 'styled-components'

import { PlusOutlined } from '@ant-design/icons'

import SettingInput, { type CheckedState } from '@/components/filter/SettingInput'
import { SEARCH_CONFIG_CHECKBOX_OPTIONS } from '@/constants/workflow/stateTransition'
import { TRANSITION_OBJECT_CHECKBOX_OPTIONS } from '@/constants/workflow/transition'
import { DetailState } from '@/components/workflow/state-transition'
import ModalActionDelete from '@/components/workflow/shared/popover/ModalActionDelete'
import { useStateTransitionManagement } from '@/hooks/workflow/useStateTransitionManagement'
import { WorkflowHeader } from '@/components/workflow/shared/header'
import { WorkflowFilterBar } from '@/components/workflow/shared/filter'
import { WorkflowTable } from '@/components/workflow/shared/table'

// CustomSelect component với styling tùy chỉnh
const CustomSelect = styled(Select)`
  .ant-select-selection-overflow {
    flex-wrap: nowrap !important;
    overflow-x: auto !important;
    overflow-y: hidden;
    white-space: nowrap;
  }
  .ant-select-selection-item,
  .ant-select-selection-overflow-item {
    display: flex;
    align-items: center;
  }
  .ant-select-selection-wrap {
    inline-size: 150px;
  }
  .ant-select-selector .ant-select-selection-wrap .ant-select-selection-overflow {
    display: flex;
    flex-wrap: nowrap !important;
    gap: 8px;
  }
`

export const ListTransitionView = () => {
  const { messageApi, comboboxCategory } = useStateTransitionManagement()
  const router = useRouter()
  // State cho selection và tìm kiếm
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([])
  const [searchInput, setSearchInput] = useState('')
  const [search, setSearch] = useState('')

  // State cho phân trang
  const [page, setPage] = useState(0)
  const [pageSize, setPageSize] = useState(10)

  // State cho filter trạng thái và khoảng thời gian
  const [statusFilter, setStatusFilter] = useState<string | undefined>(undefined)
  const [lstObjectType, setLstObjectType] = useState<string[]>([])
  const [dateRange, setDateRange] = useState<[any, any] | null>(null)

  // State cho drawer chi tiết
  const [detailDrawerOpen, setDetailDrawerOpen] = useState(false)
  const [selectedStateId, setSelectedStateId] = useState<string | null>(null)

  // State cho modal xác nhận xóa
  const [deleteModalOpen, setDeleteModalOpen] = useState(false)
  const [deleteTarget, setDeleteTarget] = useState<any>(null)

  const [checkedFilter, setCheckedFilter] = useState<CheckedState>({
    isName: true
  })

  // Debounce search
  useEffect(() => {
    const handler = setTimeout(() => {
      setSearch(searchInput)
    }, 300)

    return () => clearTimeout(handler)
  }, [searchInput])

  // Xây dựng params cho API
  const params = useMemo(() => {
    return {
      page: page ?? 0,
      size: pageSize ?? 10,
      search,
      lstObjectType: lstObjectType.length > 0 ? lstObjectType.join(',') : 'ALL',
      lstCategoryId: statusFilter,
      startTime: dateRange?.[0]?.format('YYYY-MM-DD'),
      endTime: dateRange?.[1]?.format('YYYY-MM-DD')
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [page, pageSize, search, statusFilter, lstObjectType, dateRange, checkedFilter])

  // Xử lý drawer xem chi tiết
  const showDetailDrawer = (stateId: string) => {
    setSelectedStateId(stateId)
    setDetailDrawerOpen(true)
  }

  const closeDetailDrawer = () => {
    setDetailDrawerOpen(false)
  }

  // Hàm mở modal xóa
  const handleOpenDeleteModal = (record: any) => {
    setDeleteTarget(record)
    setDeleteModalOpen(true)
  }

  // Hàm xác nhận xóa
  const handleConfirmDelete = () => {
    if (selectedRowKeys.length > 0) {
      // Xóa nhiều
      setSelectedRowKeys([])
    } else if (deleteTarget) {
      // Xóa 1
    }

    setDeleteModalOpen(false)
    setDeleteTarget(null)
  }

  // Hàm đóng modal
  const handleCloseDeleteModal = () => {
    setDeleteModalOpen(false)
    setDeleteTarget(null)
  }

  // Tạo danh sách columns động giống UI mẫu
  const columns: ColumnsType<any> = [
    {
      title: 'Tên cấu hình',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: any) => (
        <span className='cursor-pointer font-medium' onClick={() => showDetailDrawer(record.id)}>
          {text}
        </span>
      ),
      width: 320
    },
    {
      title: 'Đối tượng',
      dataIndex: 'objectType',
      key: 'objectType',
      width: 180
    },
    {
      title: 'Danh mục',
      dataIndex: 'lstCategoryName',
      key: 'lstCategoryName',
      render: (lstCategoryName: string[]) => {
        if (!lstCategoryName || lstCategoryName.length === 0) return null
        const showTags = lstCategoryName.slice(0, 2)
        const moreCount = lstCategoryName.length - showTags.length

        return (
          <div className='flex flex-wrap gap-1'>
            {showTags.map((cat, idx) => (
              <Tag
                key={cat + idx}
                className='rounded-md border-none bg-bg-neutral-lighter px-2 py-0.5 text-text-on-brights'
              >
                {cat}
              </Tag>
            ))}
            {moreCount > 0 && (
              <Tag className='border-1 flex items-center justify-center rounded-md border-[#D7DAE0] bg-bg-surface text-text-neutral-strong'>
                +{moreCount}
              </Tag>
            )}
          </div>
        )
      },
      width: 220
    },
    {
      title: 'Thời gian cập nhật',
      dataIndex: 'modifiedAt',
      key: 'modifiedAt',
      width: 180
    },
    {
      title: '',
      key: 'actions',
      fixed: 'right',
      width: 48,
      render: (_: any, record: any) => (
        <div
          className='flex cursor-pointer items-center justify-center opacity-0 group-hover:opacity-100'
          onClick={() => handleOpenDeleteModal(record)}
        >
          <i className='onedx-delete size-4' />
        </div>
      )
    }
  ]

  const { getTransitionList: listData, getTransitionLoading: listLoading } = useStateTransitionManagement(params)

  const dataSource = (listData as any)?.content || []
  const total = (listData as any)?.totalElements || 0
  const current = (listData as any)?.pageable ? (listData as any).pageable.pageNumber + 1 : 1
  const pageSz = (listData as any)?.pageable ? (listData as any).pageable.pageSize : pageSize

  // Nội dung bên trái của filter bar
  const filterBarLeftContent = (
    <SettingInput
      checked={checkedFilter}
      setChecked={setCheckedFilter}
      value={searchInput}
      onChange={setSearchInput}
      placeholder='Tìm kiếm theo tên cấu hình'
      checkBoxOptions={SEARCH_CONFIG_CHECKBOX_OPTIONS}
    />
  )

  // Nội dung bên phải của filter bar
  const filterBarRightContent = (
    <>
      <CustomSelect
        mode='multiple'
        allowClear
        size='large'
        placeholder='Đối tượng'
        className='h-full w-[400px] rounded-xl'
        maxTagCount={1}
        value={lstObjectType}
        onChange={(value: unknown) => setLstObjectType(value as string[])}
        options={TRANSITION_OBJECT_CHECKBOX_OPTIONS}
      />
      <CustomSelect
        mode='multiple'
        allowClear
        size='large'
        placeholder='Danh mục'
        className='flex h-full w-2/5 items-center rounded-xl border border-gray-3 bg-white text-gray-7'
        maxTagCount={1}
        value={statusFilter}
        onChange={(value: unknown) => setStatusFilter(value as string)}
        options={comboboxCategory?.map((item: any) => ({
          label: item.name,
          value: item.id
        }))}
      />
      <DatePicker.RangePicker
        allowClear
        size='large'
        format='DD/MM/YYYY'
        className='flex h-full w-3/5 items-center rounded-xl border border-gray-3 bg-white text-gray-7'
        placeholder={['Từ ngày', 'Đến ngày']}
        value={dateRange}
        onChange={val => setDateRange(val)}
      />
    </>
  )

  return (
    <div className='flex h-full flex-col bg-white'>
      {/* Header */}
      <WorkflowHeader
        title='Danh sách cấu hình chuyển đổi trạng thái'
        actions={
          <Button
            type='primary'
            icon={<PlusOutlined />}
            className='flex h-10 items-center rounded-2xl bg-text-info-default px-5'
            onClick={() => router.push('/workflow/state-transition/transition-management/create')}
          >
            Tạo trạng thái
          </Button>
        }
      />

      {/* Nội dung chính */}
      <div className='flex-1 bg-white p-4'>
        {/* Filter bar */}
        <WorkflowFilterBar leftContent={filterBarLeftContent} rightContent={filterBarRightContent} />

        {/* Table */}
        <WorkflowTable
          typeOfTable='TRANSITION'
          dataSource={dataSource}
          columns={columns}
          total={total}
          current={current}
          pageSize={pageSz}
          loading={listLoading}
          selectedRowKeys={selectedRowKeys}
          onChangePage={setPage}
          onChangePageSize={setPageSize}
          onChangeSelectedRows={setSelectedRowKeys}
          onDelete={handleOpenDeleteModal}
          emptyDescription='Không có dữ liệu cấu hình chuyển đổi'
        />

        {/* Drawer xem chi tiết */}
        {selectedStateId && (
          <DetailState
            stateId={selectedStateId}
            onClose={closeDetailDrawer}
            open={detailDrawerOpen}
            messageApi={messageApi}
          />
        )}
        {/* Modal xác nhận xóa */}
        <ModalActionDelete
          open={deleteModalOpen}
          title={
            <div className='mb-4 flex w-full items-center'>
              <div className='mr-4 flex size-12 items-center justify-center rounded-full bg-red-50'>
                <i className='onedx-delete size-6 text-icon-error-strong' />
              </div>
              <span className='text-lg font-semibold text-gray-11'>Xóa cấu hình chuyển đổi</span>
            </div>
          }
          description={
            <span className='body-14-regular text-black'>Bạn có chắc chắn muốn xóa cấu hình chuyển đổi đang chọn?</span>
          }
          onCancel={handleCloseDeleteModal}
          onClose={handleCloseDeleteModal}
          onConfirm={handleConfirmDelete}
        />
      </div>
    </div>
  )
}
