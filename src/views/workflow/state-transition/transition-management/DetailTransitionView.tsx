'use client'

import { useState, useMemo, useCallback } from 'react'

import Link from 'next/link'

import { LeftOutlined } from '@ant-design/icons'
import type { TabsProps } from 'antd'
import { Button } from 'antd'

import { WorkflowTabs } from '@/components/workflow/shared/tabs/WorkflowTabs'
import {
  ConfigurationSection,
  GeneralInfoReadonlySection,
  HistorySection,
  OrderApplyConfiguration
} from '@/components/workflow/transition-management/state-transition-sections'
import { TAB_KEYS, type TabKey } from '@/constants/workflow/stateTransition'

export const DetailTransitionView = () => {
  const [activeTab, setActiveTab] = useState<TabKey>(TAB_KEYS.OVERVIEW)
  const [generalCollapsed, setGeneralCollapsed] = useState(false)
  const [configCollapsed, setConfigCollapsed] = useState(false)

  // Xử lý button chỉnh sửa
  const handleEdit = useCallback(() => {
    console.log('Chỉnh sửa cấu hình')
  }, [])

  // Tab items
  const tabItems: TabsProps['items'] = useMemo(
    () => [
      {
        key: TAB_KEYS.OVERVIEW,
        label: <span className='text-sm'>Tổng quan</span>
      },
      {
        key: TAB_KEYS.HISTORY,
        label: <span className='text-sm'>Lịch sử cấu hình trạng thái</span>
      },
      {
        key: TAB_KEYS.APPLIED,
        label: <span className='text-sm'>Đơn hàng áp dụng cấu hình</span>
      }
    ],
    []
  )

  // Render overview tab content
  const renderOverviewContent = useCallback(
    () => (
      <div className='space-y-5'>
        <GeneralInfoReadonlySection
          generalCollapsed={generalCollapsed}
          setGeneralCollapsed={setGeneralCollapsed}
          onEdit={handleEdit}
        />
        <ConfigurationSection
          configCollapsed={configCollapsed}
          setConfigCollapsed={setConfigCollapsed}
          readOnly={true}
          showTabs={true}
        />
      </div>
    ),
    [generalCollapsed, configCollapsed, handleEdit]
  )

  // Tab content mapping
  const tabContentMap = useMemo(
    () => ({
      [TAB_KEYS.OVERVIEW]: renderOverviewContent,
      [TAB_KEYS.HISTORY]: () => <HistorySection />,
      [TAB_KEYS.APPLIED]: () => <OrderApplyConfiguration />
    }),
    [renderOverviewContent]
  )

  // Render tab content
  const renderTabContent = useCallback(() => {
    const ContentComponent = tabContentMap[activeTab]

    return ContentComponent ? <ContentComponent /> : null
  }, [activeTab, tabContentMap])

  return (
    <div className='flex h-full flex-col bg-gray-1'>
      {/* Header */}
      <header className='mb-0 flex h-[68px] items-center justify-between border-b border-gray-200 bg-white p-4'>
        <div className='flex items-center gap-3'>
          <Link href='/workflow/state-transition/transition-management/list'>
            <Button
              icon={<LeftOutlined />}
              type='text'
              className='flex items-center justify-center rounded-lg text-gray-8'
              aria-label='Quay lại danh sách'
            />
          </Link>
          <h1 className='headline-16-semibold text-gray-11'>Chi tiết cấu hình</h1>
        </div>
      </header>

      {/* Tab navigation */}
      <nav className='mt-2 bg-white px-4'>
        <WorkflowTabs activeKey={activeTab} onChange={key => setActiveTab(key as TabKey)} items={tabItems} />
      </nav>

      {/* Main content */}
      <main className='mt-2 flex-1'>{renderTabContent()}</main>
    </div>
  )
}
