'use client'

import { useEffect, useState } from 'react'

import { useRouter } from 'next/navigation'

import { LeftOutlined } from '@ant-design/icons'
import { Button, Form, message } from 'antd'

import { GeneralInfoSection } from '@/components/workflow/transition-management/state-transition-sections/GeneralInfoSection'
import { useCreateStateTransitionConfig } from '@/hooks/workflow'
import type { TransitionFormData } from '@/types/workflow/transition'
import { checkFormValidity } from '@/utils/workflow/transition'
import { ConfigurationSectionCreate } from '@/components/workflow/transition-management/state-transition-sections/configuration/components/state-transition-create'

export const CreateTransitionView = () => {
  const [form] = Form.useForm<TransitionFormData>()
  const [selectedObject, setSelectedObject] = useState<string | undefined>()
  const [generalCollapsed, setGeneralCollapsed] = useState(false)
  const [configCollapsed, setConfigCollapsed] = useState(false)
  // State để theo dõi loại cấu hình (table/diagram) - được sử dụng bởi ConfigurationSection
  // const [configType, setConfigType] = useState<'table' | 'diagram' | null>(null)
  const [formIsValid, setFormIsValid] = useState(false)
  const [dataConfigTable, setDataConfigTable] = useState<any>(null)
  const router = useRouter()
  // Tạo cấu hình chuyển đổi trạng thái
  const createStateTransitionConfigMutation = useCreateStateTransitionConfig()
  // Theo dõi giá trị của các trường form
  const configName = Form.useWatch('configName', form)
  const targetObject = Form.useWatch('targetObject', form)
  const description = Form.useWatch('description', form)
  const [messageApi, contextHolder] = message.useMessage()

  // Kiểm tra tính hợp lệ của form
  useEffect(() => {
    const values = { configName, targetObject, description }
    const valid = checkFormValidity(values) && !form.getFieldsError().some(({ errors }) => errors.length)

    setFormIsValid(valid)
  }, [configName, targetObject, description, form])

  // Xử lý submit form
  const handleSubmit = () => {
    form
      .validateFields()
      .then((values: TransitionFormData) => {
        // Kiểm tra xem có dataConfigTable không
        if (!dataConfigTable) {
          messageApi.error('Vui lòng cấu hình trạng thái trước khi tạo')

          return
        }

        // Chuẩn bị dữ liệu cho API
        const requestData = {
          name: values.configName,
          objectType: values.targetObject,
          description: values.description || '',
          status: 1, // Mặc định là active
          items: dataConfigTable.items || []
        }

        // Gọi API create state-transition config
        createStateTransitionConfigMutation.mutate(requestData, {
          onSuccess: () => {
            messageApi.success('Tạo cấu hình thành công')
            router.push('/workflow/state-transition/transition-management/list')
          }
        })
      })
      .catch(() => {
        // Xử lý khi validate thất bại
      })
  }

  // Xử lý quay lại hoặc hủy
  const handleNavigateBack = () => {
    // router.push('/workflow/state-transition/transition-management/list')
  }

  return (
    <>
      {contextHolder}
      <div className='flex h-full flex-col bg-gray-1'>
        {/* Header */}
        <div className='mb-0 flex h-[68px] items-center justify-between border-b border-gray-200 bg-white p-4'>
          <div className='flex items-center justify-center gap-3'>
            <button
              onClick={handleNavigateBack}
              className='flex size-8 items-center justify-center rounded-lg bg-white hover:bg-gray-100'
            >
              <LeftOutlined className='text-gray-8' />
            </button>
            <h1 className='headline-16-semibold text-gray-11'>Tạo cấu hình chuyển đổi trạng thái</h1>
          </div>
        </div>

        <div className='mt-2 flex-1'>
          <Form form={form} layout='vertical' onFinish={handleSubmit} requiredMark={false} className='space-y-2'>
            {/* Thông tin chung */}
            <GeneralInfoSection
              form={form}
              generalCollapsed={generalCollapsed}
              setGeneralCollapsed={setGeneralCollapsed}
              selectedObject={selectedObject}
              setSelectedObject={setSelectedObject}
            />

            {/* Cấu hình trạng thái */}
            <ConfigurationSectionCreate
              configCollapsed={configCollapsed}
              setConfigCollapsed={setConfigCollapsed}
              // setConfigType={setConfigType}
              setDataConfig={setDataConfigTable}
              onDeleteItems={(codes: string[]) => {
                setDataConfigTable((prev: any) => {
                  if (!prev || !Array.isArray(prev.items)) return prev

                  return {
                    ...prev,
                    items: prev.items.filter((it: any) => {
                      const code = it?.stateCode || it?.code

                      return !codes.includes(code)
                    })
                  }
                })
              }}
            />
          </Form>
        </div>

        {/* Footer Actions */}
        <div className='mt-2 flex justify-end space-x-3 border-t border-gray-200 bg-white p-6'>
          <Button onClick={handleNavigateBack} className='h-10 px-4'>
            Huỷ
          </Button>
          <Button
            type='primary'
            onClick={handleSubmit}
            loading={createStateTransitionConfigMutation.isPending}
            disabled={!formIsValid || createStateTransitionConfigMutation.isPending}
            className='h-10 px-4'
          >
            Tạo
          </Button>
        </div>
      </div>
    </>
  )
}
