import { useParams, usePathname, useRouter, useSearchParams } from 'next/navigation'

import { useMutation } from '@tanstack/react-query'

import { Form } from 'antd'

import Workflow from '@/models/Workflow'
import { message } from '@components/notification'
import { CONVERT_PROCESS_DATA } from '@views/workflow/process-management/create/convert'

// Hook gồm các hàm xử lý tạo - sửa tiến trình
export const useUpdateProcess = () => {
  // Điều hướng
  const router = useRouter()

  // Tham số bổ sung của đường dẫn
  const searchParams = useSearchParams()

  // Id của tiến trình
  const { id } = useParams<{ id: string }>()

  // Trạng thái đang chỉnh sửa tiến trình
  const isEdit = usePathname().includes('/edit')

  // Lấy form tổng chứa thông tin toàn bộ tiến trình
  const form = Form.useFormInstance()

  // Xử lý gọi API tạo - sửa tiến trình
  const createEditProcessAPI = useMutation({
    mutationFn: async (formValue?: any | undefined) => {
      // Tổng hợp lại thông tin tiến trình dựa theo dữ liệu mới
      const newProcessData = CONVERT_PROCESS_DATA(
        Object.assign({}, formValue || form.getFieldsValue(true), { id: isEdit ? id : undefined })
      )

      // Gọi API sửa - tạo tiến trình dựa theo dữ liệu mới vừa tổng hợp
      return isEdit ? Workflow.editProcess(newProcessData) : Workflow.createProcess(newProcessData)
    },
    onError: () => message.error(`${isEdit ? 'Sửa' : 'Tạo'} tiến trình thất bại, vui lòng thử lại sau`),
    onSuccess: () => {
      message.success(`${isEdit ? 'Sửa' : 'Tạo'} tiến trình thành công`)
      router.push('/workflow/process-management/list')
    }
  })

  // Xử lý gọi API sửa nhanh tiến trình
  const quickEditProcessAPI = useMutation({
    mutationFn: async (formValue?: any | undefined) => {
      // Gọi API chi tiết tiến trình
      const processData = await Workflow.detailProcess(id)

      // Tổng hợp lại thông tin tiến trình dựa theo thông tin bước tiến trình mới
      const newProcessData = CONVERT_PROCESS_DATA(
        Object.assign({}, processData, formValue || form.getFieldsValue(true), { id })
      )

      // Gọi API cập nhật tiến trình theo thông tin đã tổng hợp lại
      return Workflow.editProcess(newProcessData)
    },
    onError: () => message.error('Sửa tiến trình thất bại, vui lòng thử lại sau'),
    onSuccess: newId => {
      message.success('Sửa tiến trình thành công')
      router.push(`/workflow/process-management/detail/${newId}?${searchParams.toString()}`)
    }
  })

  // Hàm tạo - sửa tiến trình
  const createEditProcess = (value?: any) => createEditProcessAPI.mutate(value)

  // Hàm sửa nhanh tiến trình
  const quickEditProcess = (value?: any) => quickEditProcessAPI.mutate(value)

  // Trạng thái đang gọi API sửa - tạo tiến trình
  const editingProcess = createEditProcessAPI.isPending || quickEditProcessAPI.isPending

  return {
    createEditProcess,
    quickEditProcess,
    editingProcess
  }
}
