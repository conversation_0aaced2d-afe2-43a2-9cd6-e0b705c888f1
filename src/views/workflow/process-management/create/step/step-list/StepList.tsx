'use client'
import React from 'react'

import { usePathname } from 'next/navigation'

import { toBlob } from 'html-to-image'

import { Background, ReactFlow } from '@xyflow/react'

import { Button, Collapse, Form, Spin } from 'antd'

import { DragDropContext, Droppable } from '@hello-pangea/dnd'

import { useUpdateProcess } from '@views/workflow/process-management/create/hook/useUpdateProcess'

import { preventExcessAction } from '@/constants/common'

import { StepItem } from '@views/workflow/process-management/create/step/step-list/StepItem'

import StepCreate from '@views/workflow/process-management/create/step/step-create/StepCreate'

import '@xyflow/react/dist/style.css'

// Danh sách bước tiến trình
export const StepList = () => {
  // Lấy form tổng chứa thông tin toàn bộ tiến trình
  const form = Form.useFormInstance()

  // Trạng thái đang ở màn chi tiết tiến trình
  const isDetail = usePathname().includes('detail')

  // Xử lý gọi API sửa tiến trình
  const { quickEditProcess, editingProcess } = useUpdateProcess()

  // Thêm bước tiến trình
  const addStep = (newStep: any) => {
    // Tổng hợp danh sách tiến trình mới kết hợp từ danh sách tiến trình hiện tại và dữ liệu của bước tiến trình mới thêm
    const newStepList = form
      .getFieldValue('workflowStepsDTO')
      .concat(newStep)
      .filter((step: any) => !!step)
      .map((step: any, index: number) => ({ ...step, index }))

    // Đặt lại dữ liệu danh sách tiến trình trong Form theo danh sách tiến trình mới
    form.setFieldValue('workflowStepsDTO', newStepList)
  }

  // Thay đổi vị trí bước tiến trình
  const moveStep = (position: any) => {
    // Chỉ xử lý tiếp trong trường hợp có thông tin vị trí cũ mới của bước tiến trình vừa kéo thả, và vị trí cũ mới đó khác nhau
    if (position?.destination && position?.source?.index !== position?.destination?.index) {
      // Tổng hợp danh sách tiến trình mới bằng cách xóa bước tiến trình ở vị trí cũ và thêm vào ở vị trí mới
      const newStepList = form
        .getFieldValue('workflowStepsDTO')
        .toSpliced(position?.source?.index, 1)
        .toSpliced(position?.destination?.index, 0, form.getFieldValue(['workflowStepsDTO', position?.source?.index]))
        .filter((step: any) => !!step)
        .map((step: any, index: number) => ({ ...step, index }))

      // Đặt lại dữ liệu danh sách tiến trình trong Form theo danh sách tiến trình mới
      form.setFieldValue('workflowStepsDTO', newStepList)

      // Trường hợp đang ở màn chi tiết tiến trình thì gọi thêm API cập nhật tiến trình theo dữ liệu mới
      if (isDetail) quickEditProcess()
    }
  }

  // Xử lý lấy ảnh sơ đồ tiến trình
  const getImage = async () => {
    // Lấy component hiển thị QR hiện tại
    const qrElement = document.getElementById('stepList')

    // Nếu có component QR đang hiển thị thì xử lý tiếp
    if (qrElement) {
      // convert component QR tương ứng thành file Blob
      return toBlob(qrElement, { quality: 1, pixelRatio: 5 })
    }

    return Promise.reject()
  }

  // Xử lý export ảnh sơ đồ tiến trình
  const exportImage = async () => {
    // Lấy thông tin ảnh QR
    const blob = await getImage()

    // Xử lý tải xuống ảnh QR
    if (blob) {
      const reader = new FileReader()

      reader.onload = () => {
        const url = URL.createObjectURL(blob)
        const link = document.createElement('a')

        link.href = url
        link.download = `Affiliate_QR.png`
        link.click()
        URL.revokeObjectURL(url)
      }

      reader.readAsDataURL(blob)
    }
  }

  return (
    <Spin spinning={editingProcess}>
      <Form.List name='workflowStepsDTO' initialValue={[]}>
        {fields => (
          <Collapse
            ghost
            expandIconPosition='end'
            className='h-fit rounded-none bg-white'
            defaultActiveKey={['stepList']}
            items={[
              {
                key: 'stepList',
                label: (
                  <div className='border-l-2 border-solid border-sme-orange-7 px-2 text-sm font-semibold leading-5'>
                    Danh sách bước
                  </div>
                ),
                extra: (
                  <div onClick={preventExcessAction}>
                    <StepCreate onChange={addStep}>
                      <div className='flex cursor-pointer items-center justify-center gap-1 font-semibold text-primary'>
                        <i className='onedx-add size-4' />
                        Thêm bước
                      </div>
                    </StepCreate>
                  </div>
                ),
                children: (
                  <div
                    style={{ width: '100%', height: 'calc(100vh - 185px)', paddingBottom: 24, position: 'relative' }}
                  >
                    <Button className='absolute z-[1] m-4 flex items-center gap-2 text-primary' onClick={exportImage}>
                      <i className='onedx-download size-5' />
                      Tải sơ đồ
                    </Button>
                    <div className='pointer-events-none absolute size-full p-4 pb-12'>
                      <ReactFlow>
                        <Background />
                      </ReactFlow>
                    </div>
                    <div className='beauty-scroll size-full overflow-y-scroll rounded border border-solid border-neutral-200'>
                      {!fields?.length ? (
                        <div className='flex size-full items-center justify-center'>
                          <StepCreate onChange={addStep}>
                            <Button icon={<i className='onedx-add' />} type='primary'>
                              Thêm bước
                            </Button>
                          </StepCreate>
                        </div>
                      ) : (
                        <div
                          id='stepList'
                          className='flex min-h-full w-full flex-col items-center justify-center bg-white py-6'
                        >
                          <div className='overflow-hidden'>
                            <DragDropContext onDragEnd={moveStep}>
                              <Droppable droppableId='stepList'>
                                {drop => (
                                  <div ref={drop?.innerRef} {...drop?.droppableProps} className='-mt-24'>
                                    {fields?.map((field, index) => (
                                      <Form.Item key={index} name={index} noStyle>
                                        <StepItem />
                                      </Form.Item>
                                    ))}
                                    {drop.placeholder}
                                  </div>
                                )}
                              </Droppable>
                            </DragDropContext>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )
              }
            ]}
          />
        )}
      </Form.List>
    </Spin>
  )
}
