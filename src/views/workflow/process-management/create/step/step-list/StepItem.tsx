import React from 'react'

import { usePathname } from 'next/navigation'

import { Draggable } from '@hello-pangea/dnd'

import { StepTrigger } from '@views/workflow/process-management/create/step/step-trigger/StepTrigger'

import StepCreate from '@views/workflow/process-management/create/step/step-create/StepCreate'

import StepDetail from '@views/workflow/process-management/create/step/step-detail/StepDetail'

export const StepItem = ({ value, onChange = () => undefined }: { value?: any; onChange?: (value?: any) => void }) => {
  // Trạng thái chỉ hiển thị chi tiết của bước
  const isDetail = usePathname()?.includes('detail')

  // Xử lý xóa dữ liệu bước hiện tại
  const remove = () => onChange(undefined)

  return (
    !!value && (
      <Draggable index={value?.index} draggableId={value?.code} key={value?.code}>
        {drag => (
          <div ref={drag.innerRef} {...drag.draggableProps} className='flex flex-col'>
            <div
              {...drag.dragHandleProps}
              className='peer z-[1] w-[378px] overflow-hidden rounded-lg border border-solid'
              style={{ backgroundColor: value?.color || '#57A184', borderColor: value?.color || '#57A184' }}
            >
              <div className='flex justify-between p-3 text-white'>
                <div className='hover:cursor-pointer'>{value?.name}</div>
                <div className='flex items-center gap-4'>
                  <StepCreate value={value} onChange={onChange}>
                    <i className='onedx-edit size-5 hover:cursor-pointer' />
                  </StepCreate>
                  {isDetail ? (
                    <StepDetail stepData={value}>
                      <i className='onedx-chevron-right size-5 hover:cursor-pointer' />
                    </StepDetail>
                  ) : (
                    <i onClick={remove} className='onedx-delete order-first size-5 hover:cursor-pointer' />
                  )}
                </div>
              </div>
              <div className='bg-white p-3'>Mã: {value?.code}</div>
            </div>
            <div className='z-0 order-first transition peer-active:translate-y-full'>
              <StepTrigger value={value} onChange={onChange} />
            </div>
          </div>
        )}
      </Draggable>
    )
  )
}
