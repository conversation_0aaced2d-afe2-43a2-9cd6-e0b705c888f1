import React, { createElement, useMemo, useState } from 'react'

import { Button, Input, Select } from 'antd'

import * as antdIcons from '@ant-design/icons'

import * as customIcons from '@assets/builder-icons'

export const StepIcon = ({ value, onChange = () => undefined }: { value?: any; onChange?: (value?: any) => void }) => {
  const [search, setSearch] = useState('')
  const [page, setPage] = useState(1)
  const PAGE_SIZE = 24

  // lấy ra listIcon từ antd
  const listIcon = useMemo(() => {
    const allIcons = Object.entries(antdIcons)
      .filter(([name]: any[]) => name.endsWith('Outlined') || name.endsWith('Filled'))
      .concat(Object.entries(customIcons) as any[])
      .map(([name, icon]) => ({
        displayName: name,
        component: icon as any
      }))

    if (search.trim()) {
      // <PERSON>ại bỏ khoảng trắng đầu cuối khi tìm kiếm
      const searchTerm = search.trim().toUpperCase()

      return allIcons.filter(icon => icon.displayName?.toUpperCase().includes(searchTerm))
    }

    return allIcons
  }, [search])

  const renderValue = (iconValue: any) => {
    const IconComponent = Object.assign({}, antdIcons as any, customIcons as any)?.[iconValue]

    return IconComponent ? (
      <div className='flex items-center gap-x-2 text-text-neutral-strong'>
        <IconComponent style={{ fontSize: '14px' }} />
        <div className='max-w-[120px] truncate'>{iconValue}</div>
      </div>
    ) : (
      'Chọn Icon'
    )
  }

  const maxPage = Math.ceil(listIcon.length / PAGE_SIZE)

  const changePage = (direction: 'previous' | 'next') =>
    setPage(currentPage => {
      if (direction === 'previous' && currentPage > 1) {
        return currentPage - 1
      }

      if (direction === 'next' && page < maxPage) {
        return currentPage + 1
      }

      return currentPage
    })

  return (
    <Select
      className='w-full'
      placeholder={renderValue(value)}
      dropdownRender={() => (
        <div className='w-full space-y-3 p-3'>
          <Input
            suffix={<i className='onedx-search' />}
            onChange={e => setSearch(e.target.value.trim())}
            placeholder='Tìm icon'
          />
          <div className='text-base'>{search ? 'Kết quả tìm kiếm' : 'Gợi ý'}</div>
          <div className='grid grid-cols-6'>
            {listIcon.slice((page - 1) * PAGE_SIZE, page * PAGE_SIZE).map((icon, index) => (
              <div
                key={index}
                title={icon.displayName}
                className='flex cursor-pointer items-center justify-center rounded py-2 transition hover:bg-neutral-100 active:bg-neutral-200'
                onClick={() => onChange(icon.displayName)}
              >
                {createElement(icon.component)}
              </div>
            ))}
          </div>
          <div className='w-full text-center'>
            <Button disabled={page <= 1} type='text' onClick={() => changePage('previous')}>
              <i className='onedx-chevron-left size-6' />
            </Button>
            <Button disabled={page >= maxPage} type='text' onClick={() => changePage('next')}>
              <i className='onedx-chevron-right size-6' />
            </Button>
          </div>
        </div>
      )}
    />
  )
}
