import React, { useState } from 'react'

import { usePathname } from 'next/navigation'

import classNames from 'classnames'

import { useQuery } from '@tanstack/react-query'

import { DownOutlined } from '@ant-design/icons'

import { Button, Drawer, Form, Input, Popover } from 'antd'

import { useUpdateProcess } from '@views/workflow/process-management/create/hook/useUpdateProcess'

import Workflow from '@/models/Workflow'

import { StepIcon } from '@views/workflow/process-management/create/step/step-create/StepIcon'

import { validateRequireInput } from '@/validator'

import { SUGGESTED_COLORS } from '@/constants/workflow'

// Màn tạo - sửa bước tiến trình
export default function StepCreate({
  value,
  onChange = () => undefined,
  children
}: {
  value?: any
  onChange?: (value?: any) => void
  children?: any
}) {
  // Trạng thái đang ở màn chi tiết tiến trình
  const isDetail = usePathname().includes('detail')

  // <PERSON><PERSON> báo dữ liệu của Form
  const [form] = Form.useForm()

  // Trạng thái hiển thị của màn
  const [opened, open] = useState(false)

  // Thay đổi trạng thái hiển thị của màn
  const toggleOpen = () =>
    open(prev => {
      // Xử lý đặt lại dữ liệu của Form trong quá trình bật trạng thái hiển thị của màn
      !prev &&
        form.setFieldsValue({
          code: value?.code,
          color: value?.color,
          icon: value?.icon,
          name: value?.name,
          description: value?.description
        })

      // Trả về trạng thái hiển thị mới ngược lại với trạng thái hiển thị hiện tại
      return !prev
    })

  // Màu đã chọn của bước tiến trình
  const selectedColor = Form.useWatch('color', form) || SUGGESTED_COLORS[0]

  // Xử lý sinh mã bước tiến trình và gán vào trường dữ liệu tương ứng của Form
  useQuery({
    queryKey: ['generateCodeProcessStep'],
    queryFn: () => Workflow.generateStepCode().then(code => form.setFieldValue('code', code)),
    enabled: opened && !value
  })

  // Xử lý gọi API sửa tiến trình
  const { quickEditProcess } = useUpdateProcess()

  // Xử lý gán dữ liệu mới cho bước
  const submitProcessStep = (formData: any) => {
    // Truyền dữ liệu bước tiến trình mới cho danh sách tiến trình hiện tại
    onChange(formData)
    // Trường hợp là màn chi tiết tiến trình thì gọi thêm API chỉnh sửa tiến trình theo dữ liệu mới
    if (isDetail) quickEditProcess()
    // Trường hợp không phải màn chi tiết tiến trình thì chỉ đóng popup
    else toggleOpen()
  }

  // Xử lý xóa dữ liệu của bước
  const removeProcessStep = () => {
    // Xóa dữ liệu của bước tiến trình hiện tại
    onChange(undefined)
    // Trường hợp là màn chi tiết tiến trình thì gọi thêm API chỉnh sửa tiến trình theo dữ liệu mới
    if (isDetail) quickEditProcess()
    // Trường hợp không phải màn chi tiết tiến trình thì chỉ đóng popup
    else toggleOpen()
  }

  return (
    <>
      <div className='flex items-center justify-center' onClick={toggleOpen}>
        {children || 'Thêm bước'}
      </div>
      <Drawer
        width={590}
        open={opened}
        closeIcon={null}
        onClose={toggleOpen}
        destroyOnClose
        title={
          <div className='flex flex-1 items-center gap-5'>
            <div className='flex size-12 items-center justify-center rounded-full bg-[#CDE4FE]'>
              <i className={classNames('size-8 text-primary', value ? 'onedx-edit' : 'onedx-add')} />
            </div>
            <div className='flex-1 font-bold text-black'>{value ? 'Chỉnh sửa' : 'Thêm'} bước</div>
            <i className='onedx-close-icon size-7 cursor-pointer' onClick={toggleOpen} />
          </div>
        }
        footer={
          <div className='flex justify-between gap-2'>
            {!!value && (
              <Button className='flex items-center gap-2' color='danger' variant='outlined' onClick={removeProcessStep}>
                <i className='onedx-delete' />
                Xóa
              </Button>
            )}
            <div className='flex flex-1 justify-end gap-2'>
              <Button onClick={toggleOpen}>Hủy</Button>
              <Button type='primary' onClick={form.submit}>
                Xác nhận
              </Button>
            </div>
          </div>
        }
      >
        <Form
          layout='vertical'
          form={form}
          onFinish={submitProcessStep}
          scrollToFirstError={{ behavior: 'smooth', block: 'start' }}
        >
          <div className='flex w-full items-end gap-2'>
            <Form.Item
              rules={[validateRequireInput('Tên bước không được bỏ trống')]}
              className='w-full'
              label='Tên bước'
              name='name'
            >
              <Input placeholder='Nhập tên bước' />
            </Form.Item>
            <Form.Item name='color' initialValue={selectedColor}>
              <Popover
                placement='bottomLeft'
                trigger='click'
                content={
                  <div className='grid grid-cols-3 gap-4'>
                    {SUGGESTED_COLORS.map(item => (
                      <div
                        key={item}
                        style={{ backgroundColor: item, color: selectedColor === item ? 'white' : item }}
                        className='flex size-10 cursor-pointer items-center justify-center rounded-full border-2'
                        onClick={() => form.setFieldValue('color', item)}
                      >
                        <i className='onedx-check size-6' />
                      </div>
                    ))}
                  </div>
                }
              >
                <div className='flex h-10 w-[56px] cursor-pointer items-center justify-between rounded-lg border border-solid border-neutral-200 bg-bg-surface px-2'>
                  <div className='size-6 rounded-full' style={{ backgroundColor: selectedColor }} />
                  <DownOutlined className='size-4 text-gray-4' />
                </div>
              </Popover>
            </Form.Item>
          </div>
          <Form.Item rules={[validateRequireInput('Icon không được bỏ trống')]} label='Icon' name='icon'>
            <StepIcon />
          </Form.Item>
          <Form.Item label='Mã bước' name='code' rules={[validateRequireInput('Mã bước không được bỏ trống')]}>
            <Input placeholder='Đang lấy mã bước...' disabled />
          </Form.Item>
          <Form.Item label='Mô tả bước' name='description'>
            <Input.TextArea placeholder='Nhập mô tả bước' maxLength={200} showCount />
          </Form.Item>
        </Form>
      </Drawer>
    </>
  )
}
