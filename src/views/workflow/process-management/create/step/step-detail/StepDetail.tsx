import React, { useMemo, useState } from 'react'

import * as antdIcons from '@ant-design/icons'

import { <PERSON><PERSON>, Drawer } from 'antd'

import * as customIcons from '@assets/builder-icons'

import { ContentRender } from '@views/order/detail/tabs/common/components'

interface Props {
  children?: any
  stepData?: any
}

export default function StepDetail({ ...props }: Props) {
  const { children, stepData } = props
  const [open, setOpen] = useState(false)

  const renderIconValue = (value: any) => {
    if (!value) return null
    // @ts-ignore
    // eslint-disable-next-line import/namespace
    const IconComponent = antdIcons[value as string] || customIcons[value as string]

    return (
      <div className='flex items-center gap-x-2 text-text-neutral-strong'>
        {IconComponent && <IconComponent style={{ fontSize: '18px' }} />}
      </div>
    )
  }

  const detailField = useMemo(() => {
    return [
      {
        label: '<PERSON><PERSON><PERSON> b<PERSON>ớ<PERSON>',
        content: (
          <div className='flex gap-2'>
            {stepData?.name}
            <div style={{ backgroundColor: stepData?.colorCode }} className='size-5 rounded-full' />
          </div>
        )
      },
      { label: 'Icon', content: renderIconValue(stepData?.icon) },
      { label: 'Mô tả bước', content: stepData?.description },
      {
        label: 'Mô tả',
        content: 'Thiết bị, gói bundling'
      }
    ]
  }, [stepData])

  const onClose = () => {
    setOpen(false)
  }

  return (
    <div>
      <div className='flex items-center justify-center' onClick={() => setOpen(true)}>
        {children}
      </div>

      <Drawer
        title={
          <div className='flex items-center justify-between'>
            <div className='flex items-center gap-4'>
              <div className='flex size-10 items-center justify-center rounded-full bg-[#CDE4FE]'>
                <i className='onedx-contact size-6 p-2 text-primary' />
              </div>
              <div>Chi tiết bước</div>
            </div>
            <div className='hover:cursor-pointer' onClick={onClose}>
              <i className='onedx-close-icon size-6' />
            </div>
          </div>
        }
        width={590}
        closable={false}
        open={open}
        footer={
          <div className='flex justify-end'>
            <Button className='border border-solid border-primary text-primary' onClick={onClose}>
              Đóng
            </Button>
          </div>
        }
      >
        <div className='grid grid-cols-2 gap-4'>
          {detailField?.map(field => (
            <>
              <ContentRender label={field.label} content={field.content} />
            </>
          ))}
        </div>
      </Drawer>
    </div>
  )
}
