import { Form, Select } from 'antd'

import { SendNotificationFrequency } from './SendNotificationFrequency'
import {
  ACTION_OPTIONS,
  CustomFormItem,
  NotificationCollapse
} from '../../../../../../../components/workflow/process-management/common'
import { RequiredDotLabel } from '@/components/label/RequiredDotLabel'
import { SendNotification } from './SendNotification'

export const NotSuccessAction = () => {
  const form = Form.useFormInstance()
  const actionType = Form.useWatch('postActionType', form)

  return (
    <NotificationCollapse className='bg-bg-neutral-lightest' label='Hành động nếu không đạt'>
      {/* Hành động */}
      <CustomFormItem name='postActionType' label={<RequiredDotLabel label='Hành động' />}>
        <Select options={ACTION_OPTIONS} placeholder='Chọn hành động' value={actionType} />
      </CustomFormItem>

      {actionType === 'NOTIFICATION' && (
        <div>
          {/* Gửi thông báo */}
          <SendNotification />

          {/* Tần suất gửi thông báo */}
          <SendNotificationFrequency />
        </div>
      )}
    </NotificationCollapse>
  )
}
