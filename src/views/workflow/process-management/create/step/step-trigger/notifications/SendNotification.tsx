import { Checkbox, Col, Form, Input, Radio, Row } from 'antd'

import { RequiredDotLabel } from '@/components/label/RequiredDotLabel'
import {
  CustomFormItem,
  NotificationCollapse
} from '../../../../../../../components/workflow/process-management/common'
import { UploadTemplateEmail } from './UploadTemplateEmail'

export const SendNotification = () => {
  const form = Form.useFormInstance()
  const notificationType = Form.useWatch('notificationType', form) || ['EMAIL']
  const contentType = Form.useWatch('contentType', form)

  const renderCustomizeEmail = () => (
    <div className='mt-[10px]'>
      <CustomFormItem name='emailTitle' label={<RequiredDotLabel label='Tiêu đề Email' />}>
        <Input placeholder='Nhập tiêu đề email' />
      </CustomFormItem>
      <CustomFormItem
        name='emailContent'
        label={<div className='text-xs font-medium'>Nội dung email</div>}
        className='mt-5'
      >
        <Input.TextArea placeholder='Nhập nội dung email' />
      </CustomFormItem>
    </div>
  )

  const renderEmailNotification = () => (
    <div className='mt-[10px] rounded-lg border border-solid border-border-neutral-lighter bg-bg-neutral-lightest p-3'>
      <CustomFormItem
        name='contentType'
        label={<div className='text-xs font-medium'>Email</div>}
        initialValue={'TEMPLATE'}
      >
        <Radio.Group className='w-full'>
          <Row className='w-full'>
            <Col span={12}>
              <Radio value='TEMPLATE'>File mẫu</Radio>
            </Col>
            <Col span={12}>
              <Radio value='CUSTOM'>Tùy chỉnh</Radio>
            </Col>
          </Row>
        </Radio.Group>
      </CustomFormItem>
      {contentType === 'TEMPLATE' && (
        <CustomFormItem name='templateCode' className='mt-[10px]'>
          <UploadTemplateEmail />
        </CustomFormItem>
      )}
      {contentType === 'CUSTOM' && renderCustomizeEmail()}
    </div>
  )

  const renderNotification = () => (
    <div className='mt-[10px] rounded-lg border border-solid border-border-neutral-lighter bg-bg-neutral-lightest p-3'>
      <div className='text-xs font-medium'>Notification</div>
      <CustomFormItem
        name='notificationTitle'
        label={<RequiredDotLabel label='Tiêu đề Notification' />}
        className='mt-5'
      >
        <Input placeholder='Nhập tiêu đề notification' />
      </CustomFormItem>
      <CustomFormItem
        name='notificationContent'
        label={<div className='text-xs font-medium'>Nội dung</div>}
        className='mt-5'
      >
        <Input.TextArea placeholder='Nhập nội dung' />
      </CustomFormItem>
    </div>
  )

  return (
    <NotificationCollapse
      label='Gửi thông báo'
      className='mt-5 border border-solid border-border-neutral-medium bg-white'
    >
      <CustomFormItem
        name='notificationType'
        label={<RequiredDotLabel label='Phương thức thông báo' />}
        initialValue={['EMAIL']}
      >
        <Checkbox.Group className='w-full'>
          <Row className='w-full'>
            <Col span={12}>
              <Checkbox value='EMAIL'>Email</Checkbox>
            </Col>
            <Col span={12}>
              <Checkbox value='NOTIFICATION'>Notification</Checkbox>
            </Col>
          </Row>
        </Checkbox.Group>
      </CustomFormItem>
      <div className='mt-5 text-xs font-medium text-text-neutral-strong'>Nội dung thông báo</div>

      {/* Email */}
      {notificationType.includes('EMAIL') && renderEmailNotification()}

      {/* Notification */}
      {notificationType.includes('NOTIFICATION') && renderNotification()}
    </NotificationCollapse>
  )
}
