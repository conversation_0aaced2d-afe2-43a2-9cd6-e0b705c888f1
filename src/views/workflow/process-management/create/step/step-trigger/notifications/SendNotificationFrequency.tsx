import { useCallback, useState } from 'react'

import { DatePicker, Form, Select, Space, Tag } from 'antd'
import dayjs from 'dayjs'

import { CloseOutlined } from '@ant-design/icons'

import { RequiredDotLabel } from '@/components/label/RequiredDotLabel'
import { validateRequire } from '../../../../../../../validator'
import {
  DAY_IN_MONTH,
  FREQUENCY_TYPE_MONTH_VALUES,
  FREQUENCY_VALUES,
  NotificationCollapse,
  TYPE_VALUES,
  WEEK_OF_MONTH_VALUES,
  WEEK_VALUES
} from '../../../../../../../components/workflow/process-management/common'

export interface ScheduleData {
  type: 'ONCE' | 'DAILY' | 'WEEKLY' | 'MONTHLY'
  intervalType:
    | 'ONCE'
    | 'EVERY_15_MIN'
    | 'EVERY_30_MIN'
    | 'EVERY_1_HOUR'
    | 'EVERY_2_HOURS'
    | 'EVERY_3_HOURS'
    | 'MONTHLY_FIXED_DATE'
    | 'MONTHLY_FIXED_WEEKDAY'
  startTime: string // HH:mm
  endTime: string // HH:mm
  startDate: string // dd/MM/yyyy
  dayOfWeek: string[] // MON, TUE, WED, THU, FRI, SAT, SUN
  dayOfMonth: number[]
  weekOfMonth: number[]
}

export const SendNotificationFrequency = () => {
  const [form] = Form.useForm()
  const [sendNotificationType, setSendNotificationType] = useState<string>('ONCE')
  const [frequencyTypeDaily, setFrequencyTypeDaily] = useState<string>('ONCE')
  const [frequencyTypeWeek, setFrequencyTypeWeek] = useState<string>('ONCE')
  const [frequencyTypeMonth, setFrequencyTypeMonth] = useState<string>('FIXED_DATE')
  const [selectedWeekDays, setSelectedWeekDays] = useState<string[]>(['MON', 'TUE', 'WED'])
  // const [selectedDayOfWeek, setSelectedDayOfWeek] = useState<number>(1)
  // const [selectedWeekOfMonth, setSelectedWeekOfMonth] = useState<number>(1)

  // Generic handler creator
  const createFieldHandler = useCallback(
    (fieldName: string, setter?: (value: any) => void) => (value: any) => {
      form.setFieldsValue({ [fieldName]: value })
      if (setter) setter(value)
    },
    [form]
  )

  // Các handlers được tạo từ generic function
  const handleSendNotificationTypeChange = createFieldHandler('sendNotificationType', setSendNotificationType)
  const handleWeekDaysChange = createFieldHandler('weekDays', setSelectedWeekDays)
  const handleFrequencyDailyChange = useCallback((value: string) => setFrequencyTypeDaily(value), [])
  const handleFrequencyWeekChange = useCallback((value: string) => setFrequencyTypeWeek(value), [])
  const handleFrequencyMonthChange = useCallback((value: string) => setFrequencyTypeMonth(value), [])
  // const handleDayOfWeekChange = useCallback((value: number) => setSelectedDayOfWeek(value), [])
  // const handleWeekChange = useCallback((value: number) => setSelectedWeekOfMonth(value), [])

  // Time/Date handlers
  const handleSyncTimeDailyRangeChange = createFieldHandler('syncTimeDailyRange')
  const handleSyncTimeWeeklyRangeChange = createFieldHandler('syncTimeDayInWeek')
  const handleDayOfMonthChange = createFieldHandler('dayOfMonth')

  // Reusable form item component
  const FormField = ({
    name,
    label,
    children,
    className = 'col-span-1 mb-0',
    initialValue = undefined
  }: {
    name: string
    label: string | React.ReactNode
    children: React.ReactNode
    className?: string
    initialValue?: any
  }) => (
    <Form.Item name={name} label={label} className={className} initialValue={initialValue}>
      {children}
    </Form.Item>
  )

  // Reusable time picker component
  const TimePicker = ({
    name,
    label,
    initialValue = '17:30',
    className = 'col-span-1 m-0'
  }: {
    name: any
    label: string
    initialValue?: any
    className?: string
  }) => (
    <Form.Item
      label={<div className='text-xs font-medium text-text-neutral-strong'>{label}</div>}
      name={name}
      className={className}
      initialValue={initialValue}
      normalize={timeValue => (timeValue ? timeValue.format('HH:mm') : timeValue)}
      getValueProps={timeValue => (timeValue ? { value: dayjs(timeValue, 'HH:mm') } : timeValue)}
      rules={[validateRequire(`${label} không được bỏ trống`)]}
    >
      <DatePicker.TimePicker format='HH:mm' placeholder='Chọn giờ' className='w-full' needConfirm={false} />
    </Form.Item>
  )

  // Reusable date picker component
  const DateField = ({
    name,
    label,
    className = 'col-span-1 mb-0'
  }: {
    name: any
    label: string
    className?: string
  }) => (
    <Form.Item
      label={<div className='text-xs font-medium text-text-neutral-strong'>{label}</div>}
      name={name}
      className={className}
      normalize={dateValue => (dateValue ? dateValue.format('DD/MM/YYYY') : dateValue)}
      getValueProps={dateValue => (dateValue ? { value: dayjs(dateValue, 'DD/MM/YYYY') } : dateValue)}
    >
      <DatePicker placeholder='Chọn ngày' format='DD/MM/YYYY' className='w-full' />
    </Form.Item>
  )

  // Một lần
  const renderOnceSchedule = () => (
    <div className='grid grid-cols-3 gap-4'>
      <FormField name='sendNotificationType' label={<RequiredDotLabel label='Thời điểm đồng bộ' />}>
        <Select
          value={sendNotificationType}
          defaultValue='ONCE'
          onChange={handleSendNotificationTypeChange}
          options={TYPE_VALUES}
        />
      </FormField>

      <TimePicker name='syncTimeOnce' label='Thời gian đồng bộ' />

      <DateField name='startDateOnce' label='Thời điểm bắt đầu' />
    </div>
  )

  // Hàng ngày
  const renderDailySchedule = () => (
    <div className='grid grid-cols-2 gap-4'>
      <FormField name='sendNotificationType' label={<RequiredDotLabel label='Thời điểm đồng bộ' />}>
        <Select
          value={sendNotificationType}
          defaultValue='DAILY'
          onChange={handleSendNotificationTypeChange}
          options={TYPE_VALUES}
        />
      </FormField>

      <FormField name='frequencyDaily' label={<RequiredDotLabel label='Tần suất' />}>
        <Select
          defaultValue='ONCE'
          value={frequencyTypeDaily}
          onChange={handleFrequencyDailyChange}
          options={FREQUENCY_VALUES}
        />
      </FormField>

      {frequencyTypeDaily === 'ONCE' ? (
        <TimePicker name='syncTimeDaily' label='Thời gian đồng bộ' />
      ) : (
        <FormField
          name='syncTimeDailyRange'
          label={<RequiredDotLabel label='Thời gian đồng bộ' />}
          initialValue={[dayjs().hour(1).minute(0), dayjs().hour(1).minute(0)]}
        >
          <DatePicker.RangePicker
            picker='time'
            format='HH:mm'
            placeholder={['Từ', 'Đến']}
            className='w-full'
            showNow={false}
            onChange={handleSyncTimeDailyRangeChange}
          />
        </FormField>
      )}

      <DateField name='startDateDaily' label='Thời điểm bắt đầu' />
    </div>
  )

  // Ngày trong tuần
  const renderWeeklySchedule = () => (
    <div>
      <div className='grid grid-cols-2 gap-4'>
        <FormField name='sendNotificationType' label={<RequiredDotLabel label='Thời điểm đồng bộ' />}>
          <Select
            defaultValue='WEEKLY'
            value={sendNotificationType}
            onChange={handleSendNotificationTypeChange}
            options={TYPE_VALUES}
          />
        </FormField>

        <FormField name='weekDays' label={<RequiredDotLabel label='Chọn ngày' />}>
          <Select
            mode='multiple'
            placeholder='Chọn ngày'
            value={selectedWeekDays}
            onChange={handleWeekDaysChange}
            maxTagCount={2}
            maxTagPlaceholder={omittedValues => `+${omittedValues.length}`}
            options={WEEK_VALUES}
            tagRender={props => (
              <Tag
                className='rounded-md bg-primary-blue py-0.5 text-white'
                closable
                onClose={props.onClose}
                closeIcon={<CloseOutlined className='ml-1 text-white' />}
              >
                {props.label}
              </Tag>
            )}
          />
        </FormField>

        <FormField name='frequencyDayInWeek' label={<RequiredDotLabel label='Tần suất' />}>
          <Select
            defaultValue='ONCE'
            value={frequencyTypeWeek}
            onChange={handleFrequencyWeekChange}
            options={FREQUENCY_VALUES}
          />
        </FormField>

        {frequencyTypeWeek === 'ONCE' ? (
          <TimePicker name='syncTimeDayInWeek' label='Thời gian đồng bộ' />
        ) : (
          <FormField
            name='syncTimeDayInWeek'
            label={<RequiredDotLabel label='Thời gian đồng bộ' />}
            initialValue={[dayjs().hour(1).minute(0), dayjs().hour(1).minute(0)]}
          >
            <DatePicker.RangePicker
              picker='time'
              format='HH:mm'
              placeholder={['Từ', 'Đến']}
              className='w-full'
              showNow={false}
              onChange={handleSyncTimeWeeklyRangeChange}
            />
          </FormField>
        )}
      </div>

      <div className='mt-4 w-full'>
        <DateField name='startDateDayInWeek' label='Thời điểm bắt đầu' />
      </div>
    </div>
  )

  // Ngày trong tháng
  const renderMonthlySchedule = () => (
    <div>
      <div className='grid grid-cols-2 gap-4'>
        <FormField name='sendNotificationType' label={<RequiredDotLabel label='Thời điểm đồng bộ' />}>
          <Select
            defaultValue='MONTHLY'
            value={sendNotificationType}
            onChange={handleSendNotificationTypeChange}
            options={TYPE_VALUES}
          />
        </FormField>

        <FormField name='frequencyDayInMonth' label={<RequiredDotLabel label='Tần suất' />}>
          <Select
            defaultValue='FIXED_DATE'
            value={frequencyTypeMonth}
            onChange={handleFrequencyMonthChange}
            options={FREQUENCY_TYPE_MONTH_VALUES}
          />
        </FormField>

        {frequencyTypeMonth === 'FIXED_DATE' ? (
          <FormField name='dayOfMonth' label={<RequiredDotLabel label='Chọn ngày' />}>
            <Select placeholder='Chọn ngày' options={DAY_IN_MONTH} onChange={handleDayOfMonthChange} />
          </FormField>
        ) : (
          <FormField name='weekOfMonth' label={<RequiredDotLabel label='Chọn thứ ... của ... tuần' />} className='mb-0'>
            <Space.Compact className='w-full'>
              <Select
                allowClear
                notFoundContent='Không có dữ liệu'
                placeholder='Chọn thứ'
                className='w-1/2'
                options={WEEK_VALUES}
              />
              <Select
                allowClear
                notFoundContent='Không có dữ liệu'
                placeholder='Chọn tuần'
                className='w-1/2'
                options={WEEK_OF_MONTH_VALUES}
              />
            </Space.Compact>
          </FormField>
        )}
        <TimePicker name='syncTime' label='Thời gian đồng bộ' />
      </div>

      <div className='mt-4 w-full'>
        <DateField name='startDateDayInMonth' label='Thời điểm bắt đầu' />
      </div>
    </div>
  )

  const renderSendNotificationContent = () => {
    const sendNotificationRenderers = {
      ONCE: renderOnceSchedule,
      DAILY: renderDailySchedule,
      WEEKLY: renderWeeklySchedule,
      MONTHLY: renderMonthlySchedule
    }

    return (
      sendNotificationRenderers[sendNotificationType as keyof typeof sendNotificationRenderers]?.() || (
        <div className='grid grid-cols-4 gap-4'>
          <FormField name='sendNotificationType' label={<RequiredDotLabel label='Thời điểm đồng bộ' />}>
            <Select
              placeholder='Chọn loại lịch trình'
              onChange={handleSendNotificationTypeChange}
              value={sendNotificationType}
              options={TYPE_VALUES}
            />
          </FormField>
        </div>
      )
    )
  }

  return (
    <NotificationCollapse
      className='mt-5 border border-solid border-border-neutral-medium bg-white'
      label='Tần suất gửi thông báo'
    >
      {renderSendNotificationContent()}
    </NotificationCollapse>
  )
}
