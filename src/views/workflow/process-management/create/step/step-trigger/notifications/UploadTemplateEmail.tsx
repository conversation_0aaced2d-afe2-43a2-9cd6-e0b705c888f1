import { useEffect, useMemo, useRef, useState } from 'react'

import { useQuery } from '@tanstack/react-query'

import { Empty, Input, Modal, Spin, Tabs } from 'antd'

import { preventExcessAction } from '@/constants/common'

import { processManagement } from '@/models/workflow/processManagement'

interface MailTemplateItem {
  id: number
  code: string
  name: string
  titleDefault?: string
  contentHtml?: string
  emailType?: number // 1: Doanh nghiệp/HKD, 0: <PERSON><PERSON> nhân (theo thực tế payload)
}

export const UploadTemplateEmail = ({
  value = undefined,
  onChange = () => undefined
}: {
  value?: any
  onChange?: (value?: any) => void
}) => {
  const [activeTab, setActiveTab] = useState('1')
  const [searchText, setSearchText] = useState('')
  const [selected, setSelected] = useState<MailTemplateItem | undefined>()
  const [openTemplateModal, setOpenTemplateModal] = useState(false)

  // Gọi API lấy danh sách mẫu email
  const {
    data: templates,
    isFetching,
    error
  } = useQuery({
    queryKey: ['getListTemplateEmail'],
    queryFn: async () => {
      const res: any = await processManagement.getListTemplateEmail({
        emailType: -1,
        actionType: '',
        lstObjectApply: 'WORKFLOW'
      })

      // Đặt lại giá trị email đã chọn dựa theo biến value đầu vào
      setSelected(res?.find((item: any) => item?.code === value))

      return res
    }
  })

  // Xác nhận chọn mẫu email
  const confirmTemplate = () => {
    onChange(selected?.code || value)
    setOpenTemplateModal(false)
  }

  // Hủy chọn mẫu email
  const cancelTemplate = () => {
    setSelected(templates?.find((item: any) => item?.code === value))
    setOpenTemplateModal(false)
  }

  // Bỏ chọn mẫu email
  const removeTemplate = (event: any) => {
    preventExcessAction(event)
    onChange(undefined)
    setSelected(undefined)
  }

  // Danh sách mẫu email sau khi đã lọc
  const filtered = useMemo(
    () =>
      templates?.filter(
        (item: any) =>
          [Number(activeTab), -1, undefined].includes(item?.emailType) &&
          (!searchText || `${item?.name} ${item?.code}`.toLowerCase().includes(searchText.toLowerCase().trim()))
      ),
    [templates, activeTab, searchText]
  )

  // Danh sách tab
  const tabItems = [
    { key: '1', label: <span className='text-sm'>Doanh nghiệp/ HKD</span> },
    { key: '0', label: <span className='text-sm'>Cá nhân</span> }
  ]

  const TemplateThumb: React.FC<{ html?: string }> = ({ html }) => {
    const wrapperRef = useRef<HTMLDivElement | null>(null)
    const contentRef = useRef<HTMLDivElement | null>(null)

    useEffect(() => {
      const adjust = () => {
        const wrapper = wrapperRef.current
        const content = contentRef.current

        if (!wrapper || !content) return

        const cw = content.scrollWidth
        const ch = content.scrollHeight

        if (cw === 0 || ch === 0) return
      }

      // Defer to next paint so DOM has size
      const id = requestAnimationFrame(adjust)

      return () => cancelAnimationFrame(id)
    }, [html])

    return (
      <div ref={wrapperRef} className='flex h-[400px] w-auto overflow-hidden rounded border bg-transparent'>
        <div
          ref={contentRef}
          className='ck-content'
          style={{
            transform: `scale(0.42)`,
            transformOrigin: 'top left',
            maxWidth: '100%'
          }}
          dangerouslySetInnerHTML={{ __html: html || '' }}
        />
      </div>
    )
  }

  return (
    <>
      <Spin spinning={isFetching}>
        <div
          className='group relative w-full cursor-pointer rounded-2xl border border-dashed border-border-primary-default bg-white p-4'
          onClick={() => setOpenTemplateModal(true)}
        >
          <div className='flex items-center gap-6'>
            <div className='flex items-center justify-center rounded-2xl bg-bg-primary-lighter p-[10px]'>
              <i className='onedx-document size-8 text-text-primary-default' />
            </div>
            <div className='text-body-14 font-medium'>{selected?.name || 'Chọn file mẫu'}</div>
          </div>

          {selected && (
            <>
              <div className='pointer-events-none absolute inset-0 rounded-2xl bg-black/0 transition group-hover:bg-black/10' />
              <div
                className='absolute right-2 top-2 hidden size-7 items-center justify-center rounded-full bg-white text-text-neutral-strong shadow group-hover:flex'
                onClick={removeTemplate}
              >
                <i className='onedx-close-icon size-4' />
              </div>
            </>
          )}
        </div>
      </Spin>
      <Modal
        centered
        destroyOnClose
        open={openTemplateModal}
        onCancel={cancelTemplate}
        onOk={confirmTemplate}
        title={<span>Chọn file mẫu</span>}
        width={1200}
        okText='Xác nhận'
        cancelText='Hủy'
      >
        <div className='flex h-[70vh] w-full gap-4'>
          {/* Preview bên trái */}
          <div className='w-1/2 overflow-hidden rounded border border-gray-3 bg-white'>
            <div className='size-full overflow-auto p-6'>
              {selected?.contentHtml ? (
                <div dangerouslySetInnerHTML={{ __html: selected.contentHtml }} />
              ) : isFetching ? (
                <div className='flex h-full items-center justify-center'>
                  <Spin />
                </div>
              ) : (
                <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description='Chưa chọn template' />
              )}
            </div>
          </div>

          {/* Sidebar bên phải */}
          <div className='w-1/2 overflow-hidden rounded border border-gray-3 bg-white'>
            <div className='px-4 pt-3'>
              <Tabs activeKey={activeTab} onChange={setActiveTab} items={tabItems} />
              <Input.Search
                placeholder='Tìm kiếm'
                allowClear
                onSearch={setSearchText}
                onChange={e => setSearchText(e.target.value)}
                className='mb-3'
              />
            </div>
            <div className='h-[calc(70vh-110px)] overflow-auto p-3'>
              {isFetching ? (
                <div className='flex h-full items-center justify-center'>
                  <Spin />
                </div>
              ) : error ? (
                <div className='p-4 text-red-500'>{error.message}</div>
              ) : filtered.length === 0 ? (
                <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description='Không có template' />
              ) : (
                <div className='grid grid-cols-2 gap-4'>
                  {filtered?.map((item: any) => (
                    <button
                      key={item.id}
                      className={`cursor-pointer rounded-lg border p-3 text-left transition-colors ${
                        selected?.id === item.id
                          ? 'border-primary-blue ring-2 ring-primary-blue/20'
                          : 'border-gray-3 hover:border-gray-4'
                      }`}
                      onClick={() => setSelected(item)}
                    >
                      <div className='mb-2'>
                        <div className='truncate text-sm font-medium'>{item.name}</div>
                      </div>
                      <TemplateThumb html={item.contentHtml} />
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </Modal>
    </>
  )
}
