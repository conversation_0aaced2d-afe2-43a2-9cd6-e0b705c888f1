import React from 'react'

import { Form, Collapse, Select } from 'antd'

import { ScheduleAction } from '@views/workflow/process-management/create/step/step-trigger/action/ScheduleAction'

import { validateRequire } from '@/validator'

import { ACTION_OPTIONS } from '@components/workflow/process-management/common'

import { PostAction } from '@views/workflow/process-management/create/step/step-trigger/action/PostAction'

export const StepAction = () => {
  return (
    <Collapse
      ghost
      expandIconPosition='end'
      defaultActiveKey={['postActionType']}
      className='w-full space-y-4 bg-bg-neutral-lightest'
      items={[
        {
          key: 'postActionType',
          classNames: {
            body: 'space-y-4 px-0 mx-4 border-t border-solid border-neutral-200'
          },
          label: <div className='flex justify-between font-medium'>Hành động nếu không đạt</div>,
          children: (
            <Form.List name='postActions'>
              {() => (
                <>
                  {/* Hành động */}
                  <Form.Item
                    className='m-0'
                    name='postActionType'
                    label='Hành động'
                    initialValue='NOTIFICATION'
                    rules={[validateRequire('Hành động không được bỏ trống')]}
                  >
                    <Select options={ACTION_OPTIONS} placeholder='Chọn hành động' />
                  </Form.Item>
                  {/* Gửi thông báo */}
                  <PostAction />
                  {/* Tần suất gửi thông báo */}
                  <ScheduleAction />
                </>
              )}
            </Form.List>
          )
        }
      ]}
    />
  )
}
