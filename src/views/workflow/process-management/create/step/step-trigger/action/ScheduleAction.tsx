import React from 'react'

import { Collapse, Form } from 'antd'

import { ScheduleScanCondition } from '@views/workflow/process-management/create/step/step-trigger/condition/ScheduleScanCondition'

export const ScheduleAction = () => {
  return (
    <Collapse
      ghost
      expandIconPosition='end'
      defaultActiveKey={['scheduleAction']}
      className='w-full space-y-4 border border-solid border-neutral-200 bg-white'
      items={[
        {
          key: 'scheduleAction',
          classNames: {
            body: 'space-y-4 px-0 mx-4 border-t border-solid border-neutral-200'
          },
          label: <div className='flex justify-between font-medium'>T<PERSON><PERSON> su<PERSON>t g<PERSON>i thông báo</div>,
          children: <Form.List name='schedule'>{() => <ScheduleScanCondition />}</Form.List>
        }
      ]}
    />
  )
}
