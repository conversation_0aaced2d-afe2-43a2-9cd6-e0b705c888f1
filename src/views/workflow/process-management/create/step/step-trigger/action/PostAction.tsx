import React from 'react'

import { Checkbox, Collapse, Form } from 'antd'

import { validateRequire } from '@/validator'

import { NotificationAction } from '@views/workflow/process-management/create/step/step-trigger/action/NotificationAction'

import { EmailAction } from '@views/workflow/process-management/create/step/step-trigger/action/EmailAction'

export const PostAction = () => {
  return (
    <Collapse
      ghost
      expandIconPosition='end'
      defaultActiveKey={['postAction']}
      className='w-full space-y-4 border border-solid border-neutral-200 bg-white'
      items={[
        {
          key: 'postAction',
          classNames: {
            body: 'space-y-4 px-0 mx-4 border-t border-solid border-neutral-200'
          },
          label: <div className='flex justify-between font-medium'>G<PERSON>i thông báo</div>,
          children: (
            <>
              <Form.Item
                className='m-0'
                name='notificationType'
                label='Phương thức thông báo'
                initialValue={['EMAIL']}
                rules={[validateRequire('Phương thức thông báo không được bỏ trống')]}
              >
                <Checkbox.Group className='grid w-full grid-cols-2'>
                  <Checkbox value='EMAIL'>Email</Checkbox>
                  <Checkbox value='NOTIFICATION'>Notification</Checkbox>
                </Checkbox.Group>
              </Form.Item>
              <div>Nội dung thông báo</div>
              <Form.Item shouldUpdate noStyle>
                {({ prefixName, getFieldValue }: any) => (
                  <>
                    {/* Email */}
                    {getFieldValue(prefixName.concat('notificationType'))?.includes('EMAIL') && <EmailAction />}
                    {/* Notification */}
                    {getFieldValue(prefixName.concat('notificationType'))?.includes('NOTIFICATION') && (
                      <NotificationAction />
                    )}
                  </>
                )}
              </Form.Item>
            </>
          )
        }
      ]}
    />
  )
}
