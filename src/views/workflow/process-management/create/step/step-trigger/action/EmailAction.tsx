import React from 'react'

import { Form, Radio, Input } from 'antd'

import { UploadTemplateEmail } from '@views/workflow/process-management/create/step/step-trigger/notifications'

import { validateRequireInput } from '@/validator'

export const EmailAction = () => {
  return (
    <Form.List name='EMAIL'>
      {() => (
        <div className='space-y-4 rounded-lg border border-solid border-border-neutral-lighter bg-bg-neutral-lightest p-4'>
          <Form.Item className='m-0' name='contentType' label='Email' initialValue={'TEMPLATE'}>
            <Radio.Group className='grid w-full grid-cols-2'>
              <Radio value='TEMPLATE'>File mẫu</Radio>
              <Radio value='CUSTOM'>Tùy chỉnh</Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item shouldUpdate noStyle>
            {({ prefixName, getFieldValue }: any) =>
              getFieldValue(prefixName.concat('contentType')) === 'TEMPLATE' ? (
                <Form.Item className='m-0' name='templateCode'>
                  <UploadTemplateEmail />
                </Form.Item>
              ) : (
                <>
                  <Form.Item
                    className='m-0'
                    name='title'
                    label='Tiêu đề Email'
                    rules={[validateRequireInput('Tiêu đề Email không được bỏ trống')]}
                  >
                    <Input placeholder='Nhập tiêu đề email' />
                  </Form.Item>
                  <Form.Item className='m-0' name='content' label='Nội dung email'>
                    <Input.TextArea placeholder='Nhập nội dung email' />
                  </Form.Item>
                </>
              )
            }
          </Form.Item>
        </div>
      )}
    </Form.List>
  )
}
