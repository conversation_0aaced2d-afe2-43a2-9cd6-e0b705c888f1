import React from 'react'

import { Form, Input } from 'antd'

import { validateRequireInput } from '@/validator'

export const NotificationAction = () => {
  return (
    <Form.List name='NOTIFICATION'>
      {() => (
        <div className='space-y-4 rounded-lg border border-solid border-border-neutral-lighter bg-bg-neutral-lightest p-4'>
          <div>Notification</div>
          <Form.Item
            className='m-0'
            name='title'
            label='Tiêu đề Notification'
            rules={[validateRequireInput('Tiêu đề Notification không được bỏ trống')]}
          >
            <Input placeholder='Nhập tiêu đề notification' />
          </Form.Item>
          <Form.Item className='m-0' name='content' label='Nội dung'>
            <Input.TextArea placeholder='Nhập nội dung' />
          </Form.Item>
        </div>
      )}
    </Form.List>
  )
}
