import React, { useState } from 'react'

import { usePathname } from 'next/navigation'

import classNames from 'classnames'

import { Button, Drawer, Form, Radio, Spin } from 'antd'

import { useUpdateProcess } from '@views/workflow/process-management/create/hook/useUpdateProcess'

import { ConditionDetail } from '@components/workflow/process-management/common'

import { StepCondition } from '@views/workflow/process-management/create/step/step-trigger/condition/StepCondition'

import { StepAction } from '@views/workflow/process-management/create/step/step-trigger/action/StepAction'

// Màn tạo - sửa - chi tiết điều kiện chuyển tiếp của bước tiến trình
export const StepTrigger = ({
  value,
  onChange = () => undefined
}: {
  value?: any
  onChange?: (value?: any) => void
}) => {
  // Khai báo dữ liệu của Form
  const [form] = Form.useForm()

  // Trạng thái đang ở màn chi tiết tiến trình
  const isDetail = usePathname().includes('detail')

  // Trạng thái hiển thị của màn tạo - sửa
  const [opened, open] = useState<boolean>(false)

  // Trạng thái hiển thị của màn chi tiết
  const [openDetailCondition, setOpenDetailCondition] = useState(false)

  // Thay đổi trạng thái hiển thị của màn tạo - sửa
  const toggleOpen = () =>
    open(prev => {
      // Xử lý đặt lại dữ liệu của Form trong quá trình bật trạng thái hiển thị của màn
      !prev &&
        form.setFieldsValue({
          triggerLogic: value?.triggerLogic || undefined,
          trigger: value?.trigger || undefined,
          postActions: value?.postActions || undefined
        })

      // Trả về trạng thái hiển thị mới ngược lại với trạng thái hiển thị hiện tại
      return !prev
    })

  // Thay đổi trạng thái hiển thị của màn chi tiết
  const toggleOpenDetail = () => setOpenDetailCondition(true)

  // Xử lý gọi API sửa tiến trình
  const { quickEditProcess, editingProcess } = useUpdateProcess()

  // Xử lý đặt lại dữ liệu mới cho điều kiện chuyển tiếp của bước tiến trình
  const submitCondition = (formValue: any) => {
    // Truyền dữ liệu điều kiện chuyển tiếp mới cho bước tiến trình hiện tại
    onChange(Object.assign(value, formValue))

    // Trường hợp là màn chi tiết tiến trình thì gọi thêm API chỉnh sửa tiến trình theo dữ liệu mới
    if (isDetail) quickEditProcess()
    // Trường hợp không phải màn chi tiết tiến trình thì chỉ đóng popup
    else toggleOpen()
  }

  // Xử lý xóa dữ liệu của điều kiện chuyển tiếp
  const removeCondition = () => {
    // Xóa dữ liệu điều kiện chuyển tiếp của bước tiến trình hiện tại
    onChange(
      Object.assign(value, {
        triggerLogic: undefined,
        trigger: undefined,
        postActions: undefined
      })
    )

    // Trường hợp là màn chi tiết tiến trình thì gọi thêm API chỉnh sửa tiến trình theo dữ liệu mới
    if (isDetail) quickEditProcess()
    // Trường hợp không phải màn chi tiết tiến trình thì chỉ đóng popup
    else toggleOpen()
  }

  return (
    <>
      <div className='relative flex h-24 w-full items-center justify-center'>
        <i className='onedx-connect-arrow absolute size-24' />
        {value?.trigger?.length ? (
          <Button color='gold' variant='filled' className='flex items-center gap-2 border-yellow-400'>
            {value?.trigger?.length} điều kiện
            {isDetail ? (
              <Button
                color='gold'
                variant='outlined'
                className='order-last size-6 rounded-full p-0'
                onClick={toggleOpenDetail}
              >
                <i className='onedx-chevron-right size-4' />
              </Button>
            ) : (
              <Button color='gold' variant='outlined' className='size-6 rounded-full p-0' onClick={removeCondition}>
                <i className='onedx-delete size-4' />
              </Button>
            )}
            <Button color='gold' variant='outlined' className='size-6 rounded-full p-0' onClick={toggleOpen}>
              <i className='onedx-edit size-4' />
            </Button>
          </Button>
        ) : (
          <Button color='gold' variant='solid' className='flex items-center' onClick={toggleOpen}>
            <i className='onedx-add' /> Thêm điều kiện
          </Button>
        )}
      </div>
      <Drawer
        width={700}
        open={opened}
        closeIcon={null}
        onClose={toggleOpen}
        destroyOnClose
        title={
          <Form.Item shouldUpdate noStyle>
            {({ prefixName, getFieldValue }: any) =>
              getFieldValue(prefixName)?.map(
                (item: any, index: any) =>
                  item?.code === value?.code && (
                    <div key={item?.code} className='flex items-center gap-5'>
                      <div className='flex size-12 items-center justify-center rounded-full bg-[#CDE4FE]'>
                        <i
                          className={classNames(
                            'size-8 text-primary',
                            value?.trigger?.length ? 'onedx-edit' : 'onedx-progress'
                          )}
                        />
                      </div>
                      <div className='flex-1 space-y-1'>
                        <div className='font-bold text-black'>
                          {value?.trigger?.length ? 'Chỉnh sửa' : 'Thiết lập'} điều kiện chuyển tiếp
                        </div>
                        <div key={item?.code} className='text-sm font-normal text-gray-8'>
                          Từ: <strong>{getFieldValue(prefixName)?.[index - 1]?.name}</strong> sang{' '}
                          <strong>{item?.name}</strong>
                        </div>
                      </div>
                      <i className='onedx-close-icon size-7 cursor-pointer' onClick={toggleOpen} />
                    </div>
                  )
              )
            }
          </Form.Item>
        }
        footer={
          <div className='flex justify-between gap-2'>
            {value?.trigger?.length > 0 && (
              <Button className='flex items-center gap-2' color='danger' variant='outlined' onClick={removeCondition}>
                <i className='onedx-delete' />
                Xóa
              </Button>
            )}
            <div className='flex flex-1 justify-end gap-2'>
              <Button onClick={toggleOpen}>Hủy</Button>
              <Button type='primary' onClick={form.submit}>
                Xác nhận
              </Button>
            </div>
          </div>
        }
      >
        <Spin spinning={editingProcess}>
          <Form
            layout='vertical'
            form={form}
            onFinish={submitCondition}
            scrollToFirstError={{ behavior: 'smooth', block: 'start' }}
          >
            <div className='w-full space-y-4'>
              <Form.Item name='triggerLogic' label='Logic kết hợp các điều kiện' initialValue='AND' required>
                <Radio.Group
                  size='small'
                  options={[
                    { label: 'Tất cả phải đúng', value: 'AND' },
                    { label: 'Ít nhất 1 điều kiện đúng', value: 'OR' }
                  ]}
                />
              </Form.Item>
              <StepCondition />
              <StepAction />
            </div>
          </Form>
        </Spin>
      </Drawer>
      {openDetailCondition && (
        <ConditionDetail open={openDetailCondition} setOpen={setOpenDetailCondition} conditionDetail={value} />
      )}
    </>
  )
}
