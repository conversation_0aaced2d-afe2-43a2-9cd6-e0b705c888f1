import React from 'react'

import { Form, Input, Select, Space } from 'antd'

import { formatNormalizeNumber } from '@/validator'
import { DATE_UNIT } from '@components/workflow/process-management/common'

export const ScheduleExtraCondition = () => {
  return (
    <div className='flex flex-1 flex-wrap gap-4'>
      <Form.Item name='operandId' className='m-0 flex-1'>
        <Select placeholder='Chọn điều kiện' options={[{ label: 'Thời gian tạo', value: 1018 }]} />
      </Form.Item>
      <Form.Item name='operator' className='m-0 flex-1' initialValue={1}>
        <Select
          options={[
            { label: 'Bằng', value: 1 },
            { label: 'Lớn hơn', value: 12 },
            { label: 'Trong khoảng', value: 15 }
          ]}
        />
      </Form.Item>
      <Form.Item shouldUpdate noStyle>
        {({ prefixName, getFieldValue }: any) =>
          getFieldValue(prefixName.concat('operator')) === 15 ? (
            <Space.Compact className='w-full'>
              <Form.Item name={['data', 'valueFrom']} normalize={formatNormalizeNumber} noStyle>
                <Input addonBefore='Từ' placeholder='Giá trị bắt đầu' />
              </Form.Item>
              <Form.Item name={['data', 'valueTo']} normalize={formatNormalizeNumber} noStyle>
                <Input addonBefore='Đến' placeholder='Giá trị kết thúc' />
              </Form.Item>
              <Form.Item name={['data', 'unit']} initialValue={0} noStyle>
                <Select placeholder='Đơn vị' options={DATE_UNIT} />
              </Form.Item>
            </Space.Compact>
          ) : (
            <Space.Compact className='flex-1'>
              <Form.Item name={['data', 'value']} normalize={formatNormalizeNumber} noStyle>
                <Input placeholder='Giá trị' />
              </Form.Item>
              <Form.Item name={['data', 'unit']} initialValue={0} noStyle>
                <Select placeholder='Đơn vị' options={DATE_UNIT} />
              </Form.Item>
            </Space.Compact>
          )
        }
      </Form.Item>
    </div>
  )
}
