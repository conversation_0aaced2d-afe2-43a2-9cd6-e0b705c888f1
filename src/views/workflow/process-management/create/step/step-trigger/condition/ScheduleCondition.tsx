import React from 'react'

import { Button, Form } from 'antd'

import { ScheduleExtraCondition } from '@views/workflow/process-management/create/step/step-trigger/condition/ScheduleExtraCondition'

import { ScheduleScanCondition } from '@views/workflow/process-management/create/step/step-trigger/condition/ScheduleScanCondition'

export const ScheduleCondition = () => {
  return (
    <>
      <ScheduleScanCondition />
      <Form.List name='conditions' initialValue={[{}]}>
        {(conditions, { add: addCondition, remove: removeCondition }) => (
          <div className='space-y-4'>
            <div className='flex justify-between font-medium'>
              <PERSON><PERSON><PERSON><PERSON> kiện
              <div className='flex cursor-pointer items-center gap-1 text-primary' onClick={() => addCondition()}>
                <i className='onedx-add size-4' />
                Thêm điều kiện thời gian
              </div>
            </div>
            <div className='space-y-4 rounded-lg border border-solid border-neutral-200 bg-bg-neutral-lightest p-4'>
              {conditions.map(({ name: condition }) => (
                <Form.List key={condition} name={[condition, 'ifconds']} initialValue={[{}]}>
                  {(rules, { add: addRule, remove: removeRule }) => (
                    <div className='space-y-4 overflow-hidden'>
                      {rules.map(({ name: rule }) => (
                        <div key={rule} className='relative flex gap-4'>
                          {rule > 0 ? (
                            <div className='size-[32px]' />
                          ) : (
                            <Button
                              type='primary'
                              className='z-[1] flex size-[32px] items-center justify-center p-0'
                              onClick={() => addRule()}
                            >
                              <i className='onedx-add size-6 text-white' />
                            </Button>
                          )}
                          <div className='absolute left-[15px] top-[-83.5px] z-0 size-[100px] rounded-b border-b border-l border-dashed border-primary' />
                          <Form.List name={rule}>{() => <ScheduleExtraCondition />}</Form.List>
                          {(conditions?.length > 1 || rules?.length > 1) && (
                            <div className='flex h-[32px] items-center justify-center'>
                              <i
                                className='onedx-delete size-6 cursor-pointer transition hover:text-error'
                                onClick={() => (rules?.length > 1 ? removeRule(rule) : removeCondition(condition))}
                              />
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </Form.List>
              ))}
            </div>
          </div>
        )}
      </Form.List>
    </>
  )
}
