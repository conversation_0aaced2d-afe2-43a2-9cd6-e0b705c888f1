import React, { useState } from 'react'

import { useSearchParams } from 'next/navigation'

import { useQuery } from '@tanstack/react-query'

import { Form, Select } from 'antd'

import Workflow from '@/models/Workflow'

import { validateRequire } from '@/validator'

export const StateCondition = () => {
  // Danh sách thành phần cần kiểm tra có thể chọn
  const productTypeOptions = useSearchParams().get('productType')?.includes('PACKAGE')
    ? [
        { label: 'Hàng hóa vật lý', value: 'PHYSICAL' },
        { label: 'Hàng hóa kỹ thuật số', value: 'DIGITAL' },
        { label: 'Sản phẩm vật lý', value: 'SERVICE' }
      ]
    : [{ label: 'Hàng hóa vật lý', value: 'PHYSICAL' }]

  // Loại sản phẩm cần kiểm tra đã chọn
  const [productType, setProductType] = useState('')

  // API lấy danh sách trạng thái có thể chọn của loại thành phần cần kiểm tra
  const { data: productStatus } = useQuery({
    queryKey: ['getProductStates'],
    queryFn: () => Workflow.productStates({ objectType: productType }),
    enabled: !!productType
  })

  // Xử lý gọi API lấy danh sách trạng thái có thể chọn
  const getProductStatus = (value: any) => {
    // Đặt lại giá trị sản phẩm cần kiểm tra đã chọn
    setProductType(value)

    // Trả về dữ liệu cho trường Form.Item tương ứng
    return { value }
  }

  return (
    <div className='grid w-full auto-cols-[minmax(1,2fr)] grid-flow-col gap-4'>
      <Form.Item
        name='productType'
        className='m-0'
        label='Thành phần cần kiểm tra'
        rules={[validateRequire('Thành phần cần kiểm tra không được bỏ trống')]}
        getValueProps={getProductStatus}
      >
        <Select placeholder='Chọn thành phần' options={productTypeOptions} />
      </Form.Item>
      {productType && (
        <Form.Item
          name={productType.toLowerCase()}
          className='m-0'
          label='Trạng thái yêu cầu'
          rules={[validateRequire('Trạng thái yêu cầu không được bỏ trống')]}
          required
        >
          <Select
            placeholder='Chọn trạng thái'
            mode='multiple'
            maxTagCount='responsive'
            showSearch={false}
            options={productStatus}
            fieldNames={{ label: 'name', value: 'id' }}
          />
        </Form.Item>
      )}
    </div>
  )
}
