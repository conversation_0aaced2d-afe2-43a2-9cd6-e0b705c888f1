import React from 'react'

import { Form, Select } from 'antd'

import { validateRequire } from '@/validator'

export const ManualCondition = () => {
  return (
    <div className='grid w-full grid-cols-2 gap-4'>
      <Form.Item
        name='agentTypes'
        className='m-0'
        label='Tá<PERSON> nhân thực hiện'
        rules={[validateRequire('Tác nhân thực hiện không được bỏ trống')]}
      >
        <Select
          placeholder='Chọn tác nhân'
          mode='multiple'
          maxTagCount='responsive'
          showSearch={false}
          options={[
            { label: 'Admin', value: 'ADMIN' },
            { label: 'Đối tác', value: 'USER' }
          ]}
        />
      </Form.Item>
      <Form.Item name='roles' className='m-0' label='Vai trò' rules={[validateRequire('Vai trò không được bỏ trống')]}>
        <Select
          placeholder='Chọn thành phần'
          mode='multiple'
          maxTagCount='responsive'
          showSearch={false}
          options={[]}
        />
      </Form.Item>
    </div>
  )
}
