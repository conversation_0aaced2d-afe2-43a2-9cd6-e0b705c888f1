import React from 'react'

import { Form, Collapse } from 'antd'

import { TypeCondition } from '@views/workflow/process-management/create/step/step-trigger/condition/TypeCondition'
import { preventExcessAction } from '@/constants/common'

export const StepCondition = () => {
  return (
    <Form.List name='trigger' initialValue={[{}]}>
      {(triggers, { add: addTrigger, remove: removeTrigger }) => (
        <Collapse
          ghost
          expandIconPosition='end'
          defaultActiveKey={['trigger']}
          className='w-full space-y-4 bg-bg-neutral-lightest'
          items={[
            {
              key: 'trigger',
              classNames: {
                body: 'space-y-4 px-0 mx-4 border-t border-solid border-neutral-200'
              },
              label: (
                <div className='flex justify-between font-medium'>
                  Danh sách điều kiện
                  <div
                    className='flex cursor-pointer items-center gap-1 border-r border-solid border-neutral-200 pr-4 text-primary'
                    onClick={event => {
                      preventExcessAction(event)
                      addTrigger()
                    }}
                  >
                    <i className='onedx-add size-4' />
                    Thêm đi<PERSON> kiện
                  </div>
                </div>
              ),
              children: triggers.map((field, triggerIndex) => (
                <Collapse
                  ghost
                  key={triggerIndex}
                  expandIconPosition='end'
                  defaultActiveKey={['conditions']}
                  className='w-full space-y-4 border border-solid border-neutral-200 bg-white'
                  items={[
                    {
                      key: 'conditions',
                      classNames: {
                        body: 'px-0 mx-4 border-t border-solid border-neutral-200'
                      },
                      label: (
                        <div className='flex justify-between font-medium'>
                          Điều kiện {triggerIndex + 1}
                          {triggers?.length > 1 && (
                            <div className='h-6 border-r border-solid border-neutral-200 pr-4'>
                              <i
                                className='onedx-delete size-6 cursor-pointer transition hover:text-error'
                                onClick={event => {
                                  preventExcessAction(event)
                                  removeTrigger(triggerIndex)
                                }}
                              />
                            </div>
                          )}
                        </div>
                      ),
                      children: <Form.List name={triggerIndex}>{() => <TypeCondition />}</Form.List>
                    }
                  ]}
                />
              ))
            }
          ]}
        />
      )}
    </Form.List>
  )
}
