import React from 'react'

import { Button, Form, Select } from 'antd'

import { usePageScroll } from '@/hooks/usePageScroll'

import AddressCategory from '@/models/AddressCategory'

export const DataCondition = () => {
  const { content: addressList, onScroll } = usePageScroll([], AddressCategory.getAddressList, { status: 1 })

  return (
    <Form.List name='conditions' initialValue={[{}]}>
      {(conditions, { add: addCondition, remove: removeCondition }) => (
        <div className='space-y-4'>
          <div className='flex justify-between font-medium'>
            Đ<PERSON><PERSON><PERSON> kiện
            <div className='flex cursor-pointer items-center gap-1 text-primary' onClick={() => addCondition()}>
              <i className='onedx-add size-4' />
              Thêm điều kiện dữ liệu
            </div>
          </div>
          <div className='space-y-4 rounded-lg border border-solid border-neutral-200 bg-bg-neutral-lightest p-4'>
            {conditions.map(({ name: condition }) => (
              <Form.List key={condition} name={[condition, 'ifconds']} initialValue={[{}]}>
                {(rules, { add: addRule, remove: removeRule }) => (
                  <div className='space-y-4 overflow-hidden'>
                    {rules.map(({ name: rule }) => (
                      <div key={rule} className='relative flex gap-4'>
                        {rule > 0 ? (
                          <div className='size-[32px]' />
                        ) : (
                          <Button
                            type='primary'
                            className='z-[1] flex size-[32px] items-center justify-center p-0'
                            onClick={() => addRule()}
                          >
                            <i className='onedx-add size-6 text-white' />
                          </Button>
                        )}
                        <div className='absolute left-[15px] top-[-83.5px] z-0 size-[100px] rounded-b border-b border-l border-dashed border-primary' />
                        <Form.List name={rule}>
                          {() => (
                            <div className='flex flex-1 flex-wrap gap-4'>
                              <Form.Item name='operandId' className='m-0 flex-1'>
                                <Select
                                  placeholder='Chọn điều kiện'
                                  options={[{ label: 'Địa chỉ gán với đơn hàng', value: 1062 }]}
                                />
                              </Form.Item>
                              <Form.Item name='operator' className='m-0 flex-1' initialValue={1}>
                                <Select options={[{ label: 'Bằng', value: 1 }]} />
                              </Form.Item>
                              <Form.Item name={['data', 'value']} className='m-0 flex-1'>
                                <Select
                                  placeholder='Chọn giá trị'
                                  mode='multiple'
                                  maxTagCount='responsive'
                                  showSearch={false}
                                  options={addressList}
                                  onPopupScroll={onScroll}
                                  fieldNames={{ label: 'name', value: 'id' }}
                                />
                              </Form.Item>
                            </div>
                          )}
                        </Form.List>
                        {(conditions?.length > 1 || rules?.length > 1) && (
                          <div className='flex h-[32px] items-center justify-center'>
                            <i
                              className='onedx-delete size-6 cursor-pointer transition hover:text-error'
                              onClick={() => (rules?.length > 1 ? removeRule(rule) : removeCondition(condition))}
                            />
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </Form.List>
            ))}
          </div>
        </div>
      )}
    </Form.List>
  )
}
