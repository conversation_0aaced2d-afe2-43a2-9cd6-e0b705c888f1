import React from 'react'

import { Form, Select } from 'antd'

import { validateRequire } from '@/validator'
import { FREQUENCY_VALUES } from '@components/workflow/process-management/common'

export const ScheduleDailyCondition = () => {
  return (
    <Form.Item
      name='intervalType'
      className='m-0'
      label='Tần suất'
      rules={[validateRequire('Tần suất không được bỏ trống')]}
    >
      <Select placeholder='Chọn tần suất' options={FREQUENCY_VALUES} />
    </Form.Item>
  )
}
