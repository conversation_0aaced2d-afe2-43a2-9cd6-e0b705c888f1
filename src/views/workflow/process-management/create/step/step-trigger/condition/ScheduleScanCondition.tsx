import React from 'react'

import { SwapRightOutlined } from '@ant-design/icons'

import { DatePicker, Form, Select, Space, TimePicker } from 'antd'

import dayjs from 'dayjs'

import { isString } from 'lodash'

import { validateRequire } from '@/validator'
import { TYPE_VALUES } from '@components/workflow/process-management/common'
import { ScheduleDailyCondition } from '@views/workflow/process-management/create/step/step-trigger/condition/ScheduleDailyCondition'
import { ScheduleMonthlyCondition } from '@views/workflow/process-management/create/step/step-trigger/condition/ScheduleMonthlyCondition'
import { ScheduleWeeklyCondition } from '@views/workflow/process-management/create/step/step-trigger/condition/ScheduleWeeklyCondition'

export const ScheduleScanCondition = () => {
  const extraCondition: { [key: string]: any } = {
    DAILY: <ScheduleDailyCondition />,
    WEEKLY: <ScheduleWeeklyCondition />,
    MONTHLY: <ScheduleMonthlyCondition />
  }

  const dateConvert = (dateValue: any) => {
    return dateValue
      ? isString(dateValue)
        ? { value: dayjs(dateValue, 'DD/MM/YYYY') }
        : dateValue.format('DD/MM/YYYY')
      : dateValue
  }

  const timeConvert = (timeValue: any) => {
    return timeValue
      ? isString(timeValue)
        ? { value: dayjs(timeValue, 'HH:mm') }
        : timeValue.format('HH:mm')
      : timeValue
  }

  return (
    <div className='w-full space-y-4'>
      <Form.Item shouldUpdate noStyle>
        {({ prefixName, getFieldValue, setFieldValue }: any) => (
          <div className='grid auto-cols-[minmax(0,3fr)] grid-flow-col gap-4'>
            <Form.Item
              name='type'
              className='m-0'
              label='Thời điểm quét'
              rules={[validateRequire('Thời điểm quét không được bỏ trống')]}
              initialValue='DAILY'
            >
              <Select
                options={TYPE_VALUES}
                onSelect={() => setFieldValue(prefixName.concat('intervalType'), undefined)}
              />
            </Form.Item>
            {extraCondition[getFieldValue(prefixName.concat('type'))]}
          </div>
        )}
      </Form.Item>
      <div className='grid grid-cols-2 gap-4'>
        <Form.Item shouldUpdate noStyle>
          {({ prefixName, getFieldValue }: any) =>
            getFieldValue(prefixName.concat('type')) !== 'MONTHLY' &&
            getFieldValue(prefixName.concat('intervalType')) !== 'ONCE' &&
            !!getFieldValue(prefixName.concat('intervalType')) ? (
              <Form.Item className='m-0' label='Thời gian đồng bộ'>
                <Space.Compact block>
                  <Form.Item
                    name='startTime'
                    rules={[validateRequire('Thời gian đồng bộ không được bỏ trống')]}
                    normalize={timeConvert}
                    getValueProps={timeConvert}
                    initialValue='17:30'
                    noStyle
                  >
                    <TimePicker
                      className='w-full border-r-0'
                      format='HH:mm'
                      placeholder='Từ'
                      needConfirm={false}
                      suffixIcon={<SwapRightOutlined />}
                    />
                  </Form.Item>
                  <Form.Item
                    name='endTime'
                    rules={[validateRequire('Thời gian đồng bộ không được bỏ trống')]}
                    normalize={timeConvert}
                    getValueProps={timeConvert}
                    noStyle
                  >
                    <TimePicker className='w-full border-l-0' format='HH:mm' placeholder='Đến' needConfirm={false} />
                  </Form.Item>
                </Space.Compact>
              </Form.Item>
            ) : (
              <Form.Item
                name='startTime'
                className='m-0'
                label='Thời gian đồng bộ'
                rules={[validateRequire('Thời gian đồng bộ không được bỏ trống')]}
                normalize={timeConvert}
                getValueProps={timeConvert}
                initialValue='17:30'
              >
                <TimePicker className='w-full' format='HH:mm' placeholder='Chọn giờ' needConfirm={false} />
              </Form.Item>
            )
          }
        </Form.Item>
        <Form.Item
          name='startDate'
          className='m-0'
          label='Thời điểm bắt đầu'
          normalize={dateConvert}
          getValueProps={dateConvert}
        >
          <DatePicker className='w-full' format='DD/MM/YYYY' placeholder='Chọn ngày' />
        </Form.Item>
      </div>
    </div>
  )
}
