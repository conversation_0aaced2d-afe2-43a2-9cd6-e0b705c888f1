import React from 'react'

import { Form, Select } from 'antd'

import { validateRequire } from '@/validator'
import { FREQUENCY_VALUES, WEEK_VALUES } from '@components/workflow/process-management/common'

export const ScheduleWeeklyCondition = () => {
  return (
    <>
      <Form.Item
        name='intervalType'
        className='m-0'
        label='Tần suất'
        rules={[validateRequire('Tần suất không được bỏ trống')]}
      >
        <Select placeholder='Chọn tần suất' options={FREQUENCY_VALUES} />
      </Form.Item>
      <Form.Item
        name='dayOfWeek'
        className='m-0'
        label='Chọn ngày'
        rules={[validateRequire('Chọn ít nhất 1 ngày trong tuần')]}
      >
        <Select
          placeholder='Chọn ngày trong tuần'
          mode='multiple'
          maxTagCount='responsive'
          showSearch={false}
          options={WEEK_VALUES}
        />
      </Form.Item>
    </>
  )
}
