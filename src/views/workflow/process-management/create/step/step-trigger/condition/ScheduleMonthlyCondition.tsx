import React from 'react'

import { Form, Select, Space } from 'antd'

import { validateRequire } from '@/validator'
import {
  DAY_IN_MONTH,
  FREQUENCY_TYPE_MONTH_VALUES,
  WEEK_OF_MONTH_VALUES,
  WEEK_VALUES
} from '@components/workflow/process-management/common'

export const ScheduleMonthlyCondition = () => {
  return (
    <Form.Item shouldUpdate noStyle>
      {({ prefixName, getFieldValue }: any) => (
        <>
          <Form.Item
            name='intervalType'
            className='m-0'
            label='Tần suất'
            rules={[validateRequire('Tần suất không được bỏ trống')]}
          >
            <Select placeholder='Chọn tần suất' options={FREQUENCY_TYPE_MONTH_VALUES} />
          </Form.Item>
          {getFieldValue(prefixName.concat('intervalType')) === 'MONTHLY_FIXED_WEEKDAY' ? (
            <Form.Item label='Chọn thứ ... của ... tuần' className='m-0' required>
              <Space.Compact block>
                <Form.Item name='dayOfWeek' noStyle rules={[validateRequire('Ngày không được bỏ trống')]}>
                  <Select
                    placeholder='Thứ'
                    mode='multiple'
                    maxTagCount='responsive'
                    showSearch={false}
                    options={WEEK_VALUES}
                  />
                </Form.Item>
                <Form.Item name='weekOfMonth' noStyle rules={[validateRequire('Tuần không được bỏ trống')]}>
                  <Select
                    placeholder='Tuần'
                    mode='multiple'
                    maxTagCount='responsive'
                    showSearch={false}
                    options={WEEK_OF_MONTH_VALUES}
                  />
                </Form.Item>
              </Space.Compact>
            </Form.Item>
          ) : (
            <Form.Item
              name='dayOfMonth'
              className='m-0'
              label='Chọn ngày'
              rules={[validateRequire('Chọn ít nhất 1 ngày trong tuần')]}
            >
              <Select placeholder='Chọn ngày trong tháng' options={DAY_IN_MONTH} />
            </Form.Item>
          )}
        </>
      )}
    </Form.Item>
  )
}
