import React from 'react'

import { Form, Select } from 'antd'

import { validateRequire } from '@/validator'

import { DataCondition } from '@views/workflow/process-management/create/step/step-trigger/condition/DataCondition'

import { ManualCondition } from '@views/workflow/process-management/create/step/step-trigger/condition/ManualCondition'

import { ScheduleCondition } from '@views/workflow/process-management/create/step/step-trigger/condition/ScheduleCondition'

import { StateCondition } from '@views/workflow/process-management/create/step/step-trigger/condition/StateCondition'

export const TypeCondition = () => {
  const conditionList: { [key: string]: any } = {
    STATE: <StateCondition />,
    RULE_ENGINE: <DataCondition />,
    SCHEDULE: <ScheduleCondition />,
    MANUAL: <ManualCondition />
  }

  const conditionTypeOptions = [
    { label: 'Điều kiện trạng thái', value: 'STATE' },
    { label: 'Đi<PERSON>u kiện dữ liệu', value: 'RULE_ENGINE' },
    { label: 'Điề<PERSON> kiện thời gian', value: 'SCHEDULE' },
    { label: 'Đi<PERSON><PERSON> kiện thủ công', value: 'MANUAL' }
  ]

  return (
    <Form.Item shouldUpdate noStyle>
      {({ prefixName, getFieldValue, resetFields }: any) => (
        <div className='space-y-4'>
          <Form.Item
            name='type'
            className='m-0'
            label='Loại điều kiện'
            rules={[validateRequire('Loại điều kiện không được bỏ trống')]}
            initialValue='STATE'
          >
            <Select
              options={conditionTypeOptions}
              onSelect={() =>
                resetFields(
                  conditionTypeOptions.map(item => prefixName.concat(item?.value?.toLowerCase()?.replace('_e', 'E')))
                )
              }
            />
          </Form.Item>
          <Form.List name={getFieldValue(prefixName.concat('type'))?.toLowerCase()?.replace('_e', 'E')}>
            {() => conditionList[getFieldValue(prefixName.concat('type'))]}
          </Form.List>
        </div>
      )}
    </Form.Item>
  )
}
