'use client'
import React from 'react'

import { useParams, usePathname, useRouter, useSearchParams } from 'next/navigation'

import { useQuery } from '@tanstack/react-query'

import { Form, Spin, Steps } from 'antd'

import { ProcessSubmit } from '@views/workflow/process-management/create/footer/ProcessSubmit'

import { ProcessCancel } from '@views/workflow/process-management/create/footer/ProcessCancel'

import { CONVERT_PROCESS_DATA } from '@views/workflow/process-management/create/convert'

import Workflow from '@/models/Workflow'

import { StepList } from '@views/workflow/process-management/create/step/step-list/StepList'

import { ProcessInfo } from '@views/workflow/process-management/create/general/ProcessInfo'

// Màn tạo - sửa tiến trình
export const ProcessCreate = () => {
  // Danh sách các bước của màn tạo - sửa tiến trình
  const steps = [
    {
      title: 'Thông tin chung',
      children: <ProcessInfo />
    },
    {
      title: 'Thi<PERSON><PERSON> kế bước',
      children: <StepList />
    }
  ]

  // Id của tiến trình
  const { id } = useParams()

  // Khai báo dữ liệu của Form
  const [form] = Form.useForm()

  // Bước hiện tại của màn
  const step = Number(useSearchParams().get('step') || 0)

  // Kiểm tra chế độ nhân bản (duplicate). Khi ở đường dẫn duplicate thì vẫn lấy dữ liệu theo id để fill form,
  const isEdit = usePathname().includes('/edit')

  // Khai báo điều hướng
  const router = useRouter()

  // Xử lý điều hướng sang bước khác của màn
  const changeStep = (newStep: number) =>
    form.validateFields().then(() => {
      // Lấy thông tin loại sản phẩm đã chọn
      const productType = form.getFieldsValue(true)?.productType

      // Điều hướng URL kèm tham số của step (bước hiện tại) và productType (loại sản phẩm đã chọn)
      router.push(`/workflow/process-management/create?step=${newStep}&productType=${productType}`)
    })

  // Gọi API lấy chi tiết tiến trình trong trường hợp có id tiến trình
  const { isFetching: loadingProcessDetail } = useQuery({
    queryKey: ['processDetail', id],
    queryFn: () =>
      Workflow.detailProcess(id)
        .then(CONVERT_PROCESS_DATA)
        .then((data: any) => form.setFieldsValue({ ...data, code: isEdit ? data?.code : undefined })),
    enabled: !!id
  })

  return (
    <Form form={form} layout='vertical' scrollToFirstError={{ behavior: 'smooth', block: 'start' }}>
      <Spin spinning={loadingProcessDetail}>
        <div className='h-screen w-screen bg-gray-200'>
          <div className='size-full space-y-4'>
            <div className='flex items-center justify-between bg-white p-4'>
              <div className='flex items-center gap-2'>
                <i className='onedx-chevron-left size-7 cursor-pointer' />
                <div className='text-xl font-bold text-black'>{isEdit ? 'Chỉnh sửa tiến trình' : 'Tạo tiến trình'}</div>
              </div>
              <Steps size='small' className='w-[350px]' current={step} onChange={changeStep} items={steps} />
            </div>
            {steps[step].children}
          </div>
          <div className='fixed bottom-0 left-0 z-50 flex w-full items-center justify-end gap-4 rounded-2xl bg-white p-4 shadow-[0_-4px_12px_rgba(0,0,0,0.1)]'>
            <ProcessCancel />
            <ProcessSubmit />
          </div>
        </div>
      </Spin>
    </Form>
  )
}
