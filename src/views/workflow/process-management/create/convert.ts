import { isArray } from 'lodash'

// X<PERSON> lý dữ liệu của hành động nếu không đạt trong điều kiện chuyển tiếp bước tiến trình
export const CONVERT_POSTACTIONS = (data: any) => {
  return isArray(data)
    ? data?.reduce(
        (result: any, action: any) => ({
          ...result,
          postActionType: 'NOTIFICATION',
          schedule: action?.notifications?.schedule,
          notificationType: (result?.notificationType || []).concat(action?.notifications?.notificationType),
          [action?.notifications?.notificationType]: {
            contentType: action?.notifications?.contentType,
            templateCode: action?.notifications?.templateCode,
            title: action?.notifications?.customTitle,
            content: action?.notifications?.customContent
          }
        }),
        {}
      )
    : data?.notificationType?.map((type: any) => ({
        webhook: null,
        postActionType: data?.postActionType,
        notifications: {
          notificationType: type,
          schedule: data?.schedule,
          contentType: data?.[type]?.contentType,
          templateCode: data?.[type]?.templateCode,
          customTitle: data?.[type]?.title,
          customContent: data?.[type]?.content
        }
      }))
}

// Xử lý DTO của API tạo tiến trình và response của API chi tiết tiến trình
export const CONVERT_PROCESS_DATA = (data: any) => {
  return {
    // Dữ liệu chung của tiến trình
    ...data,
    // Dữ liệu bước của tiến trình
    workflowStepsDTO: data?.workflowStepsDTO
      // Lọc bỏ các bước trống hoặc đã xóa khỏi danh sách
      ?.filter((step: any) => !!step)
      ?.sort((a: any, b: any) => a?.index - b?.index)
      ?.map((step: any, index: number) =>
        index > 0
          ? {
              ...step,
              index,
              postActions: CONVERT_POSTACTIONS(step?.postActions)
            }
          : {
              index,
              code: step?.code,
              color: step?.color,
              icon: step?.icon,
              name: step?.name,
              description: step?.description
            }
      )
  }
}
