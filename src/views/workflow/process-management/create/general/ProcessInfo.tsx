'use client'
import React from 'react'

import { useParams, usePathname } from 'next/navigation'

import { useQuery } from '@tanstack/react-query'

import { Collapse, Form, Input, Select, Switch } from 'antd'

import Workflow from '@/models/Workflow'

import { validateRequireInput } from '@/validator'

export const ProcessInfo = () => {
  const { id } = useParams()
  const pathName = usePathname()
  const isDuplicate = pathName?.includes('/duplicate')

  const form = Form.useFormInstance()

  const productTypes = Form.useWatch('productTypes') || []

  const status = Form.useWatch('status')

  useQuery({
    queryKey: ['generateCodeProcess'],
    queryFn: () => Workflow.generateCode().then(code => form.setFieldValue('code', code)),
    // Ở màn nhân bản: luôn coi như tạo mới để sinh mã mới
    enabled: (!id || isDuplicate) && !form.getFieldValue('code')
  })

  const { data: physicalStates } = useQuery({
    queryKey: ['physicalProductStates'],
    queryFn: () => Workflow.productTransitionStates({ lstObjectType: 'PHYSICAL' }),
    enabled: productTypes?.includes('PHYSICAL') || productTypes?.includes('PACKAGE')
  })

  const { data: digitalStates } = useQuery({
    queryKey: ['digitalProductStates'],
    queryFn: () => Workflow.productTransitionStates({ lstObjectType: 'DIGITAL' }),
    enabled: productTypes?.includes('PACKAGE')
  })

  const { data: serviceStates } = useQuery({
    queryKey: ['serviceProductStates'],
    queryFn: () => Workflow.productTransitionStates({ lstObjectType: 'SERVICE' }),
    enabled: productTypes?.includes('PACKAGE')
  })

  return (
    <Collapse
      ghost
      className='w-full space-y-4 rounded-none'
      expandIconPosition='end'
      defaultActiveKey={['general', 'classify']}
      items={[
        {
          key: 'general',
          className: 'bg-white rounded-none',
          label: (
            <div className='border-l-2 border-solid border-sme-orange-7 px-2 text-sm font-semibold leading-5'>
              Thông tin chung
            </div>
          ),
          children: (
            <div className='grid grid-cols-3 gap-x-4'>
              <Form.Item
                label='Tên tiến trình'
                name='name'
                rules={[validateRequireInput('Tên tiến trình không được bỏ trống')]}
              >
                <Input placeholder='Nhập tên giải pháp' maxLength={250} />
              </Form.Item>
              <Form.Item
                label='Mã tiến trình'
                name='code'
                rules={[validateRequireInput('Mã tiến trình không được bỏ trống')]}
              >
                <Input disabled />
              </Form.Item>
              <div>
                <div>
                  <span className='text-red-6'>*</span> Trạng thái hoạt động
                </div>
                <div className='mt-2 flex h-10 items-center gap-2 rounded-md border border-solid border-gray-300 px-3'>
                  <Form.Item
                    name='status'
                    noStyle
                    initialValue='INACTIVE'
                    normalize={statusValue => (statusValue ? 'ACTIVE' : 'INACTIVE')}
                    getValueProps={statusValue => ({ value: statusValue === 'ACTIVE' })}
                  >
                    <Switch className='[&.ant-switch-checked]:!bg-[#14c780]' />
                  </Form.Item>
                  {status === 'ACTIVE' ? 'Hoạt động' : 'Không hoạt động'}
                </div>
              </div>
              <Form.Item label='Ghi chú' name='note' className='col-span-full'>
                <Input.TextArea showCount maxLength={1000} />
              </Form.Item>
            </div>
          )
        },
        {
          key: 'classify',
          className: 'bg-white rounded-none',
          label: (
            <div className='border-l-2 border-solid border-sme-orange-7 px-2 text-sm font-semibold leading-5'>
              Thông tin phân loại
            </div>
          ),
          children: (
            <div className='grid grid-cols-3 gap-x-4 pb-2'>
              <Form.Item label='Đối tượng áp dụng' name='objectType' initialValue='ORDER' required>
                <Select placeholder='Chọn đối tượng áp dụng' options={[{ value: 'ORDER', label: 'Đơn hàng' }]} />
              </Form.Item>
              <Form.Item
                label='Loại sản phẩm'
                name='productTypes'
                rules={[validateRequireInput('Loại sản phẩm bắt buộc chọn')]}
              >
                <Select
                  placeholder='Chọn loại sản phẩm'
                  mode='multiple'
                  maxTagCount='responsive'
                  showSearch={false}
                  options={[
                    { value: 'PHYSICAL', label: 'Hàng hóa vật lý' },
                    { value: 'PACKAGE', label: 'Giải pháp - Gói dịch vụ' }
                  ]}
                />
              </Form.Item>
              <Form.Item label='Nhãn dán phân loại' name='tags'>
                <Select
                  placeholder='Chọn nhãn dán phân loại'
                  mode='multiple'
                  maxTagCount='responsive'
                  showSearch={false}
                />
              </Form.Item>
              {productTypes?.length > 0 && (
                <div className='col-span-full space-y-4 rounded-xl bg-[#F9F9FA] p-4 pb-0'>
                  <div>Cấu hình chuyển đổi trạng thái</div>
                  <div className='grid grid-cols-3 gap-4'>
                    <Form.Item
                      rules={[validateRequireInput('Vui lòng chọn thông tin')]}
                      label='Hàng hóa vật lý'
                      name='physicalStateTransitionId'
                    >
                      <Select
                        placeholder='Chọn cấu hình'
                        options={physicalStates}
                        fieldNames={{ label: 'name', value: 'id' }}
                      />
                    </Form.Item>
                    {productTypes?.includes('PACKAGE') && (
                      <>
                        <Form.Item
                          rules={[validateRequireInput('Vui lòng chọn thông tin')]}
                          label='Hàng hóa kỹ thuật số'
                          name='digitalStateTransitionId'
                        >
                          <Select
                            placeholder='Chọn cấu hình'
                            options={digitalStates}
                            fieldNames={{ label: 'name', value: 'id' }}
                          />
                        </Form.Item>

                        <Form.Item
                          rules={[validateRequireInput('Vui lòng chọn thông tin')]}
                          label='Sản phẩm dịch vụ'
                          name='serviceStateTransitionId'
                        >
                          <Select
                            placeholder='Chọn cấu hình'
                            options={serviceStates}
                            fieldNames={{ label: 'name', value: 'id' }}
                          />
                        </Form.Item>
                      </>
                    )}
                  </div>
                </div>
              )}
            </div>
          )
        }
      ]}
    />
  )
}
