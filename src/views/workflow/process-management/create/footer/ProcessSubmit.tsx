import React from 'react'

import { useRouter } from 'next/navigation'

import { Button, Form } from 'antd'

import { message } from '@components/notification'
import { useUpdateProcess } from '@views/workflow/process-management/create/hook/useUpdateProcess'

// Nút xác nhận tạo tiến trình
export const ProcessSubmit = () => {
  // Khai báo điều hướng
  const router = useRouter()

  // Lấy form tổng chứa thông tin toàn bộ tiến trình
  const form = Form.useFormInstance()

  // Xử lý gọi API tạo - sửa tiến trình
  const { createEditProcess, editingProcess } = useUpdateProcess()

  // Xử lý truyền dữ liệu mới cho tiến trình
  const submitProcess = async () => {
    // Validate dữ liệu của Form trước khi xử lý tiếp
    await form.validateFields()
    // Tr<PERSON>ờ<PERSON> hợp chưa có thông tin của bước tiến trình thì điều hướng sang màn tạo bước tiến trình
    if (!form.getFieldValue('workflowStepsDTO')?.length) {
      message.error('Vui lòng điền đầy đủ thông tin bước tiến trình')
      // Điều hướng URL sang màn tạo bước tiến trình kèm tham số của productType (loại sản phẩm đã chọn)
      router.push(`/workflow/process-management/create?step=1&productType=${form.getFieldValue('productType')}`)
    }
    // Trường hợp đã có đầy đủ thông tin thì gọi API tạo - sửa dữ liệu tiến trình
    else createEditProcess()
  }

  return (
    <Button type='primary' onClick={submitProcess} loading={editingProcess}>
      Lưu
    </Button>
  )
}
