import React, { useState } from 'react'

import { useRouter } from 'next/navigation'

import { useMutation } from '@tanstack/react-query'

import { Button, Form, Modal } from 'antd'

import Workflow from '@/models/Workflow'
import { message } from '@components/notification'

// Nút hủy tạo tiến trình
export const ProcessCancel = () => {
  // Khai báo điều hướng
  const router = useRouter()

  // Lấy thông tin Form của màn
  const form = Form.useFormInstance()

  // Trạng thái hiển thị popup xác nhận
  const [opened, open] = useState(false)

  // Thay đổi trạng thái hiển thị popup
  const toggleOpen = () => open(prev => !prev)

  // Xử lý điều hướng về màn danh sách tiến trình
  const toProgressList = () => router.push('/workflow/process-management/list')

  // <PERSON>ử lý gọi API lưu tiến trình tạm thời
  const savepointAPI = useMutation({
    mutationFn: Workflow.createSavepoint,
    onError: () => message.error('<PERSON><PERSON><PERSON> bản nháp thất bại, vui lòng thử lại sau'),
    onSuccess: () => {
      message.success('Lưu bản nháp thành công')
      toProgressList()
    }
  })

  // Xử lý lưu tiến trình tạm thời
  const createSavepoint = () => {
    // Lấy dữ liệu đã điền của Form
    const formValue = form.getFieldsValue(true)

    // Gọi API lưu tiến trình tạm thời dựa theo dữ liệu lấy được
    savepointAPI.mutate(formValue)
  }

  return (
    <>
      <Button color='primary' variant='outlined' onClick={toggleOpen}>
        Hủy
      </Button>
      <Modal
        width={380}
        open={opened}
        onCancel={toggleOpen}
        footer={null}
        title={
          <div className='flex items-center gap-4'>
            <div className='flex size-11 items-center justify-center rounded-full bg-[#FFF6B3]'>
              <i className='onedx-information size-7 text-yellow-6' />
            </div>
            Tiến trình chưa hoàn tất
          </div>
        }
      >
        <div className='py-4 text-black'>Bạn có thể lưu bản nháp và mở lại sau để hoàn tất tạo tiến trình</div>
        <div className='flex items-center justify-end gap-4'>
          <Button onClick={toProgressList}>Hủy</Button>
          <Button type='primary' onClick={createSavepoint} loading={savepointAPI.isPending}>
            Lưu bản nháp
          </Button>
        </div>
      </Modal>
    </>
  )
}
