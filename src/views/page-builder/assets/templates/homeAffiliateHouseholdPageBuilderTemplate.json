{"ROOT": {"type": {"resolvedName": "Container"}, "isCanvas": true, "props": {"flexDirection": "column", "alignItems": "flex-start", "justifyContent": "flex-start", "padding": ["0", "0", "0", "0"], "margin": ["0", "0", "0", "0"], "background": "#FFFFFF", "color": {"r": 0, "g": 0, "b": 0, "a": 1}, "shadow": 0, "radius": 0, "width": "100%", "height": "auto"}, "displayName": "Container", "custom": {"displayName": "App"}, "hidden": false, "nodes": ["rgZx1xTZmH", "uU23T2RddF"], "linkedNodes": {}}, "rgZx1xTZmH": {"type": {"resolvedName": "<PERSON><PERSON><PERSON>"}, "isCanvas": false, "props": {"schema": {"@context": "https://schema.org", "@type": "Organization", "name": "oneSME", "url": "https://onesme.vn/", "logo": "https://onesme.vn/resources/upload/file/services/images/07072022/2022070708287006587e-a8de-449a-b286-6cc5127acc85.JPG", "description": "<PERSON><PERSON>n tảng chuyển đổi số dành cho doanh nghiệp của VNPT", "address": {"@type": "PostalAddress", "streetAddress": "124 Hoang Quoc Viet", "addressLocality": "<PERSON><PERSON>", "addressRegion": "HN", "postalCode": "100000", "addressCountry": "VN"}, "contactPoint": {"@type": "ContactPoint", "telephone": "02437480921", "contactType": "Customer Service", "areaServed": "VN", "availableLanguage": ["Vietnamese", "English"]}, "sameAs": ["https://www.facebook.com/OneSMEShop/", "https://www.youtube.com/c/oneSMEbyVNPT"]}}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "ROOT", "hidden": false, "nodes": [], "linkedNodes": {}}, "uU23T2RddF": {"type": {"resolvedName": "HomeAffiliateHouseholdPageBuilder"}, "isCanvas": false, "props": {}, "displayName": "HomeAffiliateHouseholdPageBuilder", "custom": {}, "parent": "ROOT", "hidden": false, "nodes": [], "linkedNodes": {"container": "Kk9Y7Diyq1"}}, "Kk9Y7Diyq1": {"type": {"resolvedName": "Container"}, "isCanvas": true, "props": {"flexDirection": "column", "alignItems": "flex-start", "justifyContent": "flex-start", "padding": ["0", "0", "0", "0"], "margin": ["0", "0", "0", "0"], "background": "#FFFFFF", "color": {"r": 0, "g": 0, "b": 0, "a": 1}, "shadow": 0, "radius": 0, "width": "100%", "height": "auto"}, "displayName": "Container", "custom": {"displayName": "HomeAffiliateHousehold"}, "parent": "uU23T2RddF", "hidden": false, "nodes": ["Vws6ymlE0K", "8gLe5dUG18", "UPaMThe5xN", "Mkofn5BR50", "VYfMiRpw7N", "LVzadYzK8O", "zdid6kiHPD", "JoYgF4jr03", "-ZQtN2FA5d"], "linkedNodes": {}}, "Vws6ymlE0K": {"type": {"resolvedName": "HeaderBuilderHousehold"}, "isCanvas": false, "props": {"logo": {"desktop": {"imgSrc": "/assets/images/logo.svg", "width": "auto", "height": 40, "className": "h-8 md:h-10"}, "tablet": {"imgSrc": "/assets/images/logo.svg", "width": "auto", "height": 40, "className": "h-8 md:h-10"}, "mobile": {"imgSrcMobile": "/assets/images/logo.svg", "width": "auto", "height": 32, "className": "h-8 md:h-10"}}, "expertAvatar": {"desktop": {"imgSrc": "https://images.unsplash.com/photo-1599566150163-29194dcaad36?q=80&w=1887&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D", "width": 48, "height": 48, "border": {"radius": 24}, "imageFit": "fill", "align": "center", "className": "w-12 h-12 rounded-full object-cover"}, "tablet": {"imgSrc": "https://images.unsplash.com/photo-1599566150163-29194dcaad36?q=80&w=1887&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D", "width": 48, "height": 48, "border": {"radius": 24}, "imageFit": "fill", "align": "center", "className": "w-12 h-12 rounded-full object-cover"}, "mobile": {"imgSrc": "https://images.unsplash.com/photo-1599566150163-29194dcaad36?q=80&w=1887&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D", "width": 40, "height": 40, "border": {"radius": 20}, "imageFit": "fill", "align": "center", "className": "w-10 h-10 rounded-full object-cover"}}, "expertName": {"desktop": {"text": "<PERSON><PERSON><PERSON><PERSON>", "fontsize": 14, "fontWeight": 700, "color": "#00529c", "textAlign": "left", "className": "font-bold text-lg text-primary-blue"}, "tablet": {"text": "<PERSON><PERSON><PERSON><PERSON>", "fontsize": 14, "fontWeight": 700, "color": "#00529c", "textAlign": "left", "className": "font-bold text-lg text-primary-blue"}, "mobile": {"text": "<PERSON><PERSON><PERSON><PERSON>", "fontSize": 16, "fontWeight": 700, "color": "#00529c", "textAlign": "left", "className": "font-bold text-base text-primary-blue"}}, "expertTitle": {"desktop": {"text": "Chuyên gia Tổ Ấm Số", "fontSize": 14, "fontWeight": 400, "color": "#6b7280", "textAlign": "left", "className": "text-sm text-gray-500"}, "tablet": {"text": "Chuyên gia Tổ Ấm Số", "fontSize": 14, "fontWeight": 400, "color": "#6b7280", "textAlign": "left", "className": "text-sm text-gray-500"}, "mobile": {"text": "Chuyên gia Tổ Ấm Số", "fontSize": 12, "fontWeight": 400, "color": "#6b7280", "textAlign": "left", "className": "text-xs text-gray-500"}}, "contactSocialMedia": {"desktop": {"fontSize": 14, "fontWeight": 400, "textAlign": "left", "color": "#000000", "backgroundColor": "#ffffff", "text": "Text", "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "size": 14, "iconAlign": "start", "socialList": [{"name": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "icon": "PhoneFilled", "iconColor": "#6b7280", "url": "", "displayType": "NAME"}, {"name": "Email", "icon": "MailFilled", "iconColor": "#6b7280", "url": "", "displayType": "NAME"}], "fontFamily": "Inter, sans-serif"}, "tablet": {"fontSize": 14, "fontWeight": 400, "textAlign": "left", "color": "#000000", "backgroundColor": "#ffffff", "text": "Text", "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "size": 14, "iconAlign": "start", "socialList": [{"name": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "icon": "PhoneFilled", "iconColor": "#6b7280", "url": "", "displayType": "NAME"}, {"name": "Email", "icon": "MailFilled", "iconColor": "#6b7280", "url": "", "displayType": "NAME"}], "fontFamily": "Inter, sans-serif"}, "mobile": {"fontSize": 14, "fontWeight": 400, "textAlign": "left", "color": "#000000", "backgroundColor": "#ffffff", "text": "Text", "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "size": 14, "iconAlign": "start", "socialList": [{"name": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "icon": "PhoneFilled", "iconColor": "#6b7280", "url": "", "displayType": "ICON"}, {"name": "Email", "icon": "MailFilled", "iconColor": "#6b7280", "url": "", "displayType": "ICON"}], "fontFamily": "Inter, sans-serif"}}, "socialMediaLinks": {"desktop": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "backgroundColor": "#ffffff", "text": "Text", "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "size": 14, "iconAlign": "start", "socialList": [{"name": "Facebook", "icon": "FacebookIcon2", "iconColor": "#6b7280", "url": "https://facebook.com", "displayType": "ICON"}]}, "tablet": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "backgroundColor": "#ffffff", "text": "Text", "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "size": 14, "iconAlign": "start", "socialList": [{"name": "Facebook", "icon": "FacebookIcon2", "iconColor": "#6b7280", "url": "https://facebook.com", "displayType": "ICON"}]}, "mobile": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "backgroundColor": "#ffffff", "text": "Text", "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "size": 16, "iconAlign": "start", "socialList": [{"name": "Facebook", "icon": "FacebookIcon2", "iconColor": "#6b7280", "url": "https://facebook.com", "displayType": "ICON"}]}}, "zaloLink": {"desktop": {"icon": "MessageFilled", "iconColor": "#6b7280", "title": "", "fontsize": 14, "fontWeight": 400, "color": "#6b7280", "href": "#", "gap": 0, "className": "hover:text-green-500 text-lg", "showIcon": {"desktop": true, "tablet": true, "mobile": true}, "showTitle": {"desktop": false, "tablet": false, "mobile": false}}, "tablet": {"icon": "MessageFilled", "iconColor": "#6b7280", "title": "", "fontsize": 14, "fontWeight": 400, "color": "#6b7280", "href": "#", "gap": 0, "className": "hover:text-green-500 text-lg", "showIcon": {"desktop": true, "tablet": true, "mobile": true}, "showTitle": {"desktop": false, "tablet": false, "mobile": false}}, "mobile": {"icon": "MessageFilled", "iconColor": "#6b7280", "title": "", "fontSize": 16, "fontWeight": 400, "color": "#6b7280", "href": "#", "gap": 0, "className": "hover:text-green-500 text-lg", "showIcon": {"desktop": true, "tablet": true, "mobile": true}, "showTitle": {"desktop": false, "tablet": false, "mobile": false}}}, "id": "header"}, "displayName": "HeaderBuilderHousehold", "custom": {}, "parent": "Kk9Y7Diyq1", "hidden": false, "nodes": [], "linkedNodes": {"logo": "8K_9pLRK0G", "expert-avatar": "y45_ZiWU8A", "expert-name": "FO1bQkz5s_", "expert-title": "V_yz_l1rh1", "contact-social-media": "ANDR_0VCC1", "social-media-links": "AeIhaSmDEE"}}, "8gLe5dUG18": {"type": {"resolvedName": "BannerListIot"}, "isCanvas": false, "props": {"desktop": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "backgroundColor": "#EF9304", "text": "Text", "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "autoPlaySpeed": 5000, "width": "100%", "hasButton": true, "currentSlide": 0, "buttonType": "default", "buttonAlign": "center", "size": "large", "href": "", "hasIcon": false, "icon": "", "iconAlign": "start", "iconColor": "#ffffff", "isButtonWorkplace": false, "slidesPerSection": 1, "images": [{"id": "image1", "backgroundColor": "#6e82c7", "imageAlign": "center", "imageFit": "fill", "contentAlign": "center", "url": "", "urlMobile": "", "urlLibrary": "", "urlLibraryMobile": "", "imageSelectType": "library", "imgSrc": "", "fontWeightTitle": 600, "fontWeightDescription": 400, "fontSizeTitle": 32, "fontSizeDescription": 14, "colorTitle": "#ffffff", "colorDescription": "#ffffff", "textAlignTitle": "center", "textAlignDescription": "center", "overlayColor": "rgba(30, 64, 175, 0.8)", "overlayOpacity": 0.6, "paddingButton": {"top": 10, "right": 16, "bottom": 10, "left": 16}, "marginButton": {"top": 10, "bottom": 10}, "titleText": "Internet & TV Cho <PERSON>", "descriptionText": "<PERSON><PERSON><PERSON><PERSON> trí bất tận, kết n<PERSON>i không gi<PERSON>i hạn.", "buttonText": "<PERSON><PERSON>", "buttonBackgroundColor": "#ffffff", "buttonTextColor": "#ffffff", "buttonStyle": "default", "buttonSize": "medium", "buttonFontSize": 14, "buttonFontWeight": 500, "hasIcon": false, "icon": "", "iconAlign": "start", "iconColor": "#ffffff", "showTitle": {"desktop": true, "mobile": true, "tablet": true}, "showDescription": {"desktop": true, "mobile": true, "tablet": true}, "showButton": {"desktop": false, "mobile": false, "tablet": false}, "href": "", "isShow": {"desktop": true, "mobile": true, "tablet": true}, "isAdded": true}, {"id": "image2", "backgroundColor": "#caa69d", "imageAlign": "center", "imageFit": "fill", "contentAlign": "center", "url": "", "urlMobile": "", "urlLibrary": "", "urlLibraryMobile": "", "imageSelectType": "library", "imgSrc": "", "fontWeightTitle": 600, "fontWeightDescription": 400, "fontSizeTitle": 32, "fontSizeDescription": 14, "colorTitle": "#ffffff", "colorDescription": "#ffffff", "textAlignTitle": "center", "textAlignDescription": "center", "overlayColor": "rgba(194, 65, 12, 0.8)", "overlayOpacity": 0.6, "paddingButton": {"top": 10, "right": 16, "bottom": 10, "left": 16}, "marginButton": {"top": 10, "bottom": 10}, "titleText": "Home Camera - <PERSON> Gia Đình", "descriptionText": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>t mọ<PERSON> l<PERSON>, an tâm mọi n<PERSON>i.", "buttonText": "<PERSON><PERSON>", "buttonBackgroundColor": "#ffffff", "buttonTextColor": "#ffffff", "buttonStyle": "default", "buttonSize": "medium", "buttonFontSize": 14, "buttonFontWeight": 500, "hasIcon": false, "icon": "", "iconAlign": "start", "iconColor": "#ffffff", "showTitle": {"desktop": true, "mobile": true, "tablet": true}, "showDescription": {"desktop": true, "mobile": true, "tablet": true}, "showButton": {"desktop": false, "mobile": false, "tablet": false}, "href": "", "isShow": {"desktop": true, "mobile": true, "tablet": true}, "isAdded": true}, {"id": "image3", "backgroundColor": "#699998", "imageAlign": "center", "imageFit": "fill", "contentAlign": "center", "url": "", "urlMobile": "", "urlLibrary": "", "urlLibraryMobile": "", "imageSelectType": "library", "imgSrc": "", "fontWeightTitle": 600, "fontWeightDescription": 400, "fontSizeTitle": 32, "fontSizeDescription": 14, "colorTitle": "#ffffff", "colorDescription": "#ffffff", "textAlignTitle": "center", "textAlignDescription": "center", "overlayColor": "rgba(21, 128, 61, 0.8)", "overlayOpacity": 0.6, "paddingButton": {"top": 10, "right": 16, "bottom": 10, "left": 16}, "marginButton": {"top": 10, "bottom": 10}, "titleText": "Smart Home - <PERSON><PERSON><PERSON>c Sống", "descriptionText": "Tận hưởng không gian sống hiện đại, tiệ<PERSON> nghi.", "buttonText": "<PERSON><PERSON>", "buttonBackgroundColor": "#ffffff", "buttonTextColor": "#ffffff", "buttonStyle": "default", "buttonSize": "medium", "buttonFontSize": 14, "buttonFontWeight": 500, "hasIcon": false, "icon": "", "iconAlign": "start", "iconColor": "#ffffff", "showTitle": {"desktop": true, "mobile": true, "tablet": true}, "showDescription": {"desktop": true, "mobile": true, "tablet": true}, "showButton": {"desktop": false, "mobile": false, "tablet": false}, "href": "", "isShow": {"desktop": true, "mobile": true, "tablet": true}, "isAdded": true}]}, "tablet": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "backgroundColor": "#EF9304", "text": "Text", "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "autoPlaySpeed": 5000, "width": "100%", "hasButton": true, "currentSlide": 0, "buttonType": "default", "buttonAlign": "center", "size": "large", "href": "", "hasIcon": false, "icon": "", "iconAlign": "start", "iconColor": "#ffffff", "isButtonWorkplace": false, "slidesPerSection": 1, "images": [{"id": "image1", "backgroundColor": "#6e82c7", "imageAlign": "center", "imageFit": "fill", "contentAlign": "center", "url": "", "urlMobile": "", "urlLibrary": "", "urlLibraryMobile": "", "imageSelectType": "library", "imgSrc": "", "fontWeightTitle": 600, "fontWeightDescription": 400, "fontSizeTitle": 32, "fontSizeDescription": 14, "colorTitle": "#ffffff", "colorDescription": "#ffffff", "textAlignTitle": "center", "textAlignDescription": "center", "overlayColor": "rgba(30, 64, 175, 0.8)", "overlayOpacity": 0.6, "paddingButton": {"top": 10, "right": 16, "bottom": 10, "left": 16}, "marginButton": {"top": 10, "bottom": 10}, "titleText": "Internet & TV Cho <PERSON>", "descriptionText": "<PERSON><PERSON><PERSON><PERSON> trí bất tận, kết n<PERSON>i không gi<PERSON>i hạn.", "buttonText": "<PERSON><PERSON>", "buttonBackgroundColor": "#ffffff", "buttonTextColor": "#ffffff", "buttonStyle": "default", "buttonSize": "medium", "buttonFontSize": 14, "buttonFontWeight": 500, "hasIcon": false, "icon": "", "iconAlign": "start", "iconColor": "#ffffff", "showTitle": {"desktop": true, "mobile": true, "tablet": true}, "showDescription": {"desktop": true, "mobile": true, "tablet": true}, "showButton": {"desktop": false, "mobile": false, "tablet": false}, "href": "", "isShow": {"desktop": true, "mobile": true, "tablet": true}, "isAdded": true}, {"id": "image2", "backgroundColor": "#caa69d", "imageAlign": "center", "imageFit": "fill", "contentAlign": "center", "url": "", "urlMobile": "", "urlLibrary": "", "urlLibraryMobile": "", "imageSelectType": "library", "imgSrc": "", "fontWeightTitle": 600, "fontWeightDescription": 400, "fontSizeTitle": 32, "fontSizeDescription": 14, "colorTitle": "#ffffff", "colorDescription": "#ffffff", "textAlignTitle": "center", "textAlignDescription": "center", "overlayColor": "rgba(194, 65, 12, 0.8)", "overlayOpacity": 0.6, "paddingButton": {"top": 10, "right": 16, "bottom": 10, "left": 16}, "marginButton": {"top": 10, "bottom": 10}, "titleText": "Home Camera - <PERSON> Gia Đình", "descriptionText": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>t mọ<PERSON> l<PERSON>, an tâm mọi n<PERSON>i.", "buttonText": "<PERSON><PERSON>", "buttonBackgroundColor": "#ffffff", "buttonTextColor": "#ffffff", "buttonStyle": "default", "buttonSize": "medium", "buttonFontSize": 14, "buttonFontWeight": 500, "hasIcon": false, "icon": "", "iconAlign": "start", "iconColor": "#ffffff", "showTitle": {"desktop": true, "mobile": true, "tablet": true}, "showDescription": {"desktop": true, "mobile": true, "tablet": true}, "showButton": {"desktop": false, "mobile": false, "tablet": false}, "href": "", "isShow": {"desktop": true, "mobile": true, "tablet": true}, "isAdded": true}, {"id": "image3", "backgroundColor": "#699998", "imageAlign": "center", "imageFit": "fill", "contentAlign": "center", "url": "", "urlMobile": "", "urlLibrary": "", "urlLibraryMobile": "", "imageSelectType": "library", "imgSrc": "", "fontWeightTitle": 600, "fontWeightDescription": 400, "fontSizeTitle": 32, "fontSizeDescription": 14, "colorTitle": "#ffffff", "colorDescription": "#ffffff", "textAlignTitle": "center", "textAlignDescription": "center", "overlayColor": "rgba(21, 128, 61, 0.8)", "overlayOpacity": 0.6, "paddingButton": {"top": 10, "right": 16, "bottom": 10, "left": 16}, "marginButton": {"top": 10, "bottom": 10}, "titleText": "Smart Home - <PERSON><PERSON><PERSON>c Sống", "descriptionText": "Tận hưởng không gian sống hiện đại, tiệ<PERSON> nghi.", "buttonText": "<PERSON><PERSON>", "buttonBackgroundColor": "#ffffff", "buttonTextColor": "#ffffff", "buttonStyle": "default", "buttonSize": "medium", "buttonFontSize": 14, "buttonFontWeight": 500, "hasIcon": false, "icon": "", "iconAlign": "start", "iconColor": "#ffffff", "showTitle": {"desktop": true, "mobile": true, "tablet": true}, "showDescription": {"desktop": true, "mobile": true, "tablet": true}, "showButton": {"desktop": false, "mobile": false, "tablet": false}, "href": "", "isShow": {"desktop": true, "mobile": true, "tablet": true}, "isAdded": true}]}, "mobile": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "backgroundColor": "#EF9304", "text": "Text", "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "autoPlaySpeed": 5000, "width": "100%", "hasButton": true, "currentSlide": 0, "buttonType": "default", "buttonAlign": "center", "size": "large", "href": "", "hasIcon": false, "icon": "", "iconAlign": "start", "iconColor": "#ffffff", "isButtonWorkplace": false, "slidesPerSection": 1, "images": [{"id": "image1", "backgroundColor": "#6e82c7", "imageAlign": "center", "imageFit": "fill", "contentAlign": "center", "url": "", "urlMobile": "", "urlLibrary": "", "urlLibraryMobile": "", "imageSelectType": "library", "imgSrc": "", "fontWeightTitle": 600, "fontWeightDescription": 400, "fontSizeTitle": 32, "fontSizeDescription": 14, "colorTitle": "#ffffff", "colorDescription": "#ffffff", "textAlignTitle": "center", "textAlignDescription": "center", "overlayColor": "rgba(30, 64, 175, 0.8)", "overlayOpacity": 0.6, "paddingButton": {"top": 10, "right": 16, "bottom": 10, "left": 16}, "marginButton": {"top": 10, "bottom": 10}, "titleText": "Internet & TV Cho <PERSON>", "descriptionText": "<PERSON><PERSON><PERSON><PERSON> trí bất tận, kết n<PERSON>i không gi<PERSON>i hạn.", "buttonText": "<PERSON><PERSON>", "buttonBackgroundColor": "#ffffff", "buttonTextColor": "#ffffff", "buttonStyle": "default", "buttonSize": "medium", "buttonFontSize": 14, "buttonFontWeight": 500, "hasIcon": false, "icon": "", "iconAlign": "start", "iconColor": "#ffffff", "showTitle": {"desktop": true, "mobile": true, "tablet": true}, "showDescription": {"desktop": true, "mobile": true, "tablet": true}, "showButton": {"desktop": false, "mobile": false, "tablet": false}, "href": "", "isShow": {"desktop": true, "mobile": true, "tablet": true}, "isAdded": true}, {"id": "image2", "backgroundColor": "#caa69d", "imageAlign": "center", "imageFit": "fill", "contentAlign": "center", "url": "", "urlMobile": "", "urlLibrary": "", "urlLibraryMobile": "", "imageSelectType": "library", "imgSrc": "", "fontWeightTitle": 600, "fontWeightDescription": 400, "fontSizeTitle": 32, "fontSizeDescription": 14, "colorTitle": "#ffffff", "colorDescription": "#ffffff", "textAlignTitle": "center", "textAlignDescription": "center", "overlayColor": "rgba(194, 65, 12, 0.8)", "overlayOpacity": 0.6, "paddingButton": {"top": 10, "right": 16, "bottom": 10, "left": 16}, "marginButton": {"top": 10, "bottom": 10}, "titleText": "Home Camera - <PERSON> Gia Đình", "descriptionText": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>t mọ<PERSON> l<PERSON>, an tâm mọi n<PERSON>i.", "buttonText": "<PERSON><PERSON>", "buttonBackgroundColor": "#ffffff", "buttonTextColor": "#ffffff", "buttonStyle": "default", "buttonSize": "medium", "buttonFontSize": 14, "buttonFontWeight": 500, "hasIcon": false, "icon": "", "iconAlign": "start", "iconColor": "#ffffff", "showTitle": {"desktop": true, "mobile": true, "tablet": true}, "showDescription": {"desktop": true, "mobile": true, "tablet": true}, "showButton": {"desktop": false, "mobile": false, "tablet": false}, "href": "", "isShow": {"desktop": true, "mobile": true, "tablet": true}, "isAdded": true}, {"id": "image3", "backgroundColor": "#699998", "imageAlign": "center", "imageFit": "fill", "contentAlign": "center", "url": "", "urlMobile": "", "urlLibrary": "", "urlLibraryMobile": "", "imageSelectType": "library", "imgSrc": "", "fontWeightTitle": 600, "fontWeightDescription": 400, "fontSizeTitle": 32, "fontSizeDescription": 14, "colorTitle": "#ffffff", "colorDescription": "#ffffff", "textAlignTitle": "center", "textAlignDescription": "center", "overlayColor": "rgba(21, 128, 61, 0.8)", "overlayOpacity": 0.6, "paddingButton": {"top": 10, "right": 16, "bottom": 10, "left": 16}, "marginButton": {"top": 10, "bottom": 10}, "titleText": "Smart Home - <PERSON><PERSON><PERSON>c Sống", "descriptionText": "Tận hưởng không gian sống hiện đại, tiệ<PERSON> nghi.", "buttonText": "<PERSON><PERSON>", "buttonBackgroundColor": "#ffffff", "buttonTextColor": "#ffffff", "buttonStyle": "default", "buttonSize": "medium", "buttonFontSize": 14, "buttonFontWeight": 500, "hasIcon": false, "icon": "", "iconAlign": "start", "iconColor": "#ffffff", "showTitle": {"desktop": true, "mobile": true, "tablet": true}, "showDescription": {"desktop": true, "mobile": true, "tablet": true}, "showButton": {"desktop": false, "mobile": false, "tablet": false}, "href": "", "isShow": {"desktop": true, "mobile": true, "tablet": true}, "isAdded": true}]}}, "displayName": "<PERSON><PERSON> banner", "custom": {}, "parent": "Kk9Y7Diyq1", "hidden": false, "nodes": [], "linkedNodes": {}}, "UPaMThe5xN": {"type": {"resolvedName": "ExpertIntroHouseholdBuilder"}, "isCanvas": false, "props": {"container": {"height": "auto", "background": "#FFFFFF", "padding": ["0", "0", "0", "0"]}, "expertIntroRow": {"desktop": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 64, "bottom": 64, "left": 16, "right": 16}, "colWidths": [4, 8], "gap": 20, "height": "auto", "alignItems": "center"}, "tablet": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 64, "bottom": 64, "left": 16, "right": 16}, "colWidths": [4, 8], "gap": 20, "height": "auto", "alignItems": "center"}, "mobile": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 48, "bottom": 48, "left": 16, "right": 16}, "isBreakLine": true, "gap": 20, "height": "auto", "alignItems": "center"}}, "expertImageColumn": {"desktop": {"display": "flex", "justifyContent": "center", "alignItems": "center", "contentAlign": "start"}, "tablet": {"display": "flex", "justifyContent": "center", "alignItems": "center", "contentAlign": "start"}, "mobile": {"display": "flex", "justifyContent": "center", "alignItems": "center", "contentAlign": "start"}}, "expertContentColumn": {"desktop": {"display": "flex", "justifyContent": "center", "alignItems": "center", "contentAlign": "start"}, "tablet": {"display": "flex", "justifyContent": "center", "alignItems": "center", "contentAlign": "start"}, "mobile": {"display": "flex", "justifyContent": "center", "alignItems": "center", "contentAlign": "start"}}, "expertImage": {"desktop": {"imgSrc": "https://images.unsplash.com/photo-1557862921-37829c790f19?q=80&w=2071&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D", "alt": "<PERSON><PERSON><PERSON><PERSON> gia <PERSON>", "width": 480, "height": 320, "border": {"radius": 8}, "align": "center", "imageFit": "fill"}, "tablet": {"imgSrc": "https://images.unsplash.com/photo-1557862921-37829c790f19?q=80&w=2071&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D", "alt": "<PERSON><PERSON><PERSON><PERSON> gia <PERSON>", "width": 250, "height": 330, "border": {"radius": 8}, "align": "center", "imageFit": "fill"}, "mobile": {"imgSrc": "https://images.unsplash.com/photo-1557862921-37829c790f19?q=80&w=2071&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D", "alt": "<PERSON><PERSON><PERSON><PERSON> gia <PERSON>", "width": 200, "height": 260, "border": {"radius": 8}, "align": "center", "imageFit": "fill"}}, "expertTitle": {"desktop": {"text": "Chuyên gia tư vấ<PERSON>", "fontSize": 30, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}, "tablet": {"text": "Chuyên gia tư vấ<PERSON>", "fontSize": 28, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}, "mobile": {"text": "Chuyên gia tư vấ<PERSON>", "fontSize": 24, "fontWeight": 700, "color": "#00529c", "textAlign": "center"}}, "expertSpacer": {"desktop": {"height": 16}, "tablet": {"height": 16}, "mobile": {"height": 16}}, "expertDescription": {"desktop": {"text": "V<PERSON>i mong muốn mang đến những giải pháp công nghệ tốt nhất cho mỗi gia đình Việt, tôi luôn lắng nghe và tư vấn tận tình để giúp bạn xây dựng một \"tổ ấm số\" hiện đại, an toàn và tiện nghi. Hãy để tôi đồng hành cùng gia đình bạn!", "fontSize": 16, "fontWeight": 400, "color": "#6b7280", "textAlign": "left"}, "tablet": {"text": "V<PERSON>i mong muốn mang đến những giải pháp công nghệ tốt nhất cho mỗi gia đình Việt, tôi luôn lắng nghe và tư vấn tận tình để giúp bạn xây dựng một \"tổ ấm số\" hiện đại, an toàn và tiện nghi. Hãy để tôi đồng hành cùng gia đình bạn!", "fontSize": 16, "fontWeight": 400, "color": "#6b7280", "textAlign": "left"}, "mobile": {"text": "V<PERSON>i mong muốn mang đến những giải pháp công nghệ tốt nhất cho mỗi gia đình Việt, tôi luôn lắng nghe và tư vấn tận tình để giúp bạn xây dựng một \"tổ ấm số\" hiện đại, an toàn và tiện nghi. Hãy để tôi đồng hành cùng gia đình bạn!", "fontSize": 14, "fontWeight": 400, "color": "#6b7280", "textAlign": "center"}}, "id": "expert-intro"}, "displayName": "ExpertIntroHouseholdBuilder", "custom": {}, "parent": "Kk9Y7Diyq1", "hidden": false, "nodes": [], "linkedNodes": {"container": "TmQBQYODVh"}}, "Mkofn5BR50": {"type": {"resolvedName": "FlashSaleHouseholdBuilder"}, "isCanvas": false, "props": {"flashSaleTitle": {"desktop": {"text": "FLASH SALE", "fontSize": 18, "fontWeight": 700, "color": "#dc2626", "textAlign": "left", "className": "font-bold text-red-600 text-lg"}, "tablet": {"text": "FLASH SALE", "fontSize": 18, "fontWeight": 700, "color": "#dc2626", "textAlign": "left", "className": "font-bold text-red-600 text-lg"}, "mobile": {"text": "FLASH SALE", "fontSize": 16, "fontWeight": 700, "color": "#dc2626", "textAlign": "left", "className": "font-bold text-red-600 text-lg"}}, "flashSaleDescriptionPrefix": {"desktop": {"text": "<PERSON> an ninh chỉ từ ", "fontSize": 14, "fontWeight": 400, "color": "#374151", "textAlign": "left", "className": "inline"}, "tablet": {"text": "<PERSON> an ninh chỉ từ ", "fontSize": 14, "fontWeight": 400, "color": "#374151", "textAlign": "left", "className": "inline"}, "mobile": {"text": "<PERSON> an ninh chỉ từ ", "fontSize": 12, "fontWeight": 400, "color": "#374151", "textAlign": "left", "className": "inline"}}, "flashSaleDescriptionPrice": {"desktop": {"text": "499.000đ", "fontSize": 14, "fontWeight": 700, "color": "#dc2626", "textAlign": "left", "className": "inline text-red-600 font-bold"}, "tablet": {"text": "499.000đ", "fontSize": 14, "fontWeight": 700, "color": "#dc2626", "textAlign": "left", "className": "inline text-red-600 font-bold"}, "mobile": {"text": "499.000đ", "fontSize": 12, "fontWeight": 700, "color": "#dc2626", "textAlign": "left", "className": "inline text-red-600 font-bold"}}, "flashSaleButton": {"desktop": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 14, "fontWeight": 700, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#dc2626", "radius": 8, "size": "large", "width": "auto", "padding": {"top": 8, "right": 24, "bottom": 8, "left": 24}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}}, "tablet": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 14, "fontWeight": 700, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#dc2626", "radius": 8, "size": "large", "width": "auto", "padding": {"top": 8, "right": 24, "bottom": 8, "left": 24}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}}, "mobile": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 12, "fontWeight": 700, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#dc2626", "radius": 8, "size": "large", "width": "100%", "padding": {"top": 8, "right": 24, "bottom": 8, "left": 24}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}}}, "id": "flash-sale"}, "displayName": "FlashSaleHouseholdBuilder", "custom": {}, "parent": "Kk9Y7Diyq1", "hidden": false, "nodes": [], "linkedNodes": {"flash-sale-title": "I-Yf5CxmiQ", "flash-sale-description-prefix": "SibpZM9t_K", "flash-sale-description-price": "ZhAlW_3Hk6", "flash-sale-button": "lOBmcMwd6w"}}, "VYfMiRpw7N": {"type": {"resolvedName": "PromotionsHouseholdBuilder"}, "isCanvas": false, "props": {"container": {"height": "auto", "background": "#FFFFFF", "padding": ["0", "0", "0", "0"]}, "promotionsContainer": {"desktop": {"backgroundColor": "#F0F5FA", "backgroundType": "COLOR", "padding": {"top": 64, "bottom": 40, "left": 16, "right": 16}, "colWidths": [12], "gap": 20, "height": "auto", "alignItems": "center", "justifyContent": "center", "width": "100%"}, "tablet": {"backgroundColor": "#FOF5FA", "backgroundType": "COLOR", "padding": {"top": 48, "bottom": 24, "left": 16, "right": 16}, "colWidths": [12], "gap": 20, "height": "auto", "alignItems": "center", "justifyContent": "center", "width": "100%"}, "mobile": {"backgroundColor": "#F0F5FA", "backgroundType": "COLOR", "padding": {"top": 32, "bottom": 8, "left": 16, "right": 16}, "colWidths": [12], "gap": 16, "height": "auto", "alignItems": "center", "justifyContent": "center", "width": "100%"}}, "promotionsTitleSection": {"desktop": {"display": "flex", "justifyContent": "center", "alignItems": "center", "flexDirection": "column", "margin": {"top": 40, "bottom": 40, "left": 0, "right": 0}, "gap": 0}, "tablet": {"display": "flex", "justifyContent": "center", "alignItems": "center", "flexDirection": "column", "margin": {"top": 40, "bottom": 40, "left": 0, "right": 0}, "gap": 0}, "mobile": {"display": "flex", "justifyContent": "center", "alignItems": "center", "flexDirection": "column", "margin": {"top": 40, "bottom": 40, "left": 0, "right": 0}, "gap": 0}}, "promotionsTitle": {"desktop": {"text": "Ưu đãi tháng 7 cho Gia Đình", "fontSize": 32, "fontWeight": 700, "color": "#004E95", "textAlign": "center"}, "tablet": {"text": "Ưu đãi tháng 7 cho Gia Đình", "fontSize": 28, "fontWeight": 700, "color": "#004E95", "textAlign": "center"}, "mobile": {"text": "Ưu đãi tháng 7 cho Gia Đình", "fontSize": 24, "fontWeight": 700, "color": "#004E95", "textAlign": "center"}}, "promotionsCardsRow": {"custom": {"displayName": "Promotions Card Section"}, "desktop": {"colWidths": [4, 4, 4], "padding": {"top": 0, "bottom": 24, "left": 0, "right": 0}, "backgroundColor": "#F0F5FA", "backgroundType": "COLOR", "gap": 6, "height": "auto", "alignItems": "stretch", "justifyContent": "center", "width": "100%"}, "tablet": {"colWidths": [6, 6], "padding": {"top": 0, "bottom": 16, "left": 0, "right": 0}, "backgroundColor": "#F0F5FA", "backgroundType": "COLOR", "gap": 6, "height": "auto", "alignItems": "stretch", "justifyContent": "center", "width": "100%", "isBreakLine": true}, "mobile": {"colWidths": [12], "padding": {"top": 0, "bottom": 16, "left": 0, "right": 0}, "backgroundColor": "#F0F5FA", "backgroundType": "COLOR", "gap": 6, "height": "auto", "alignItems": "stretch", "justifyContent": "center", "width": "100%", "isBreakLine": true}}, "promotionCard1": {"desktop": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "width": "100%", "border": {"radius": 8, "width": 1, "color": "#e5e7eb", "style": "solid"}, "shadow": 8}, "tablet": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "width": "100%", "border": {"radius": 8, "width": 1, "color": "#e5e7eb", "style": "solid"}, "shadow": 8}, "mobile": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "width": "100%", "border": {"radius": 8, "width": 1, "color": "#e5e7eb", "style": "solid"}, "shadow": 8}}, "card1Image": {"desktop": {"imgSrc": "https://placehold.co/600x300/E3F2FD/0277BD?text=Mua+6+Tặng+2", "width": "100%", "height": 128, "border": {"radius": 0, "width": 0, "color": "transparent", "style": "none"}, "imageFit": "fill", "align": "center"}, "tablet": {"imgSrc": "https://placehold.co/600x300/E3F2FD/0277BD?text=Mua+6+Tặng+2", "width": "100%", "height": 128, "border": {"radius": 0, "width": 0, "color": "transparent", "style": "none"}, "imageFit": "fill", "align": "center"}, "mobile": {"imgSrcMobile": "https://placehold.co/600x300/E3F2FD/0277BD?text=Mua+6+Tặng+2", "width": "100%", "height": 128, "border": {"radius": 0, "width": 0, "color": "transparent", "style": "none"}, "imageFit": "fill", "align": "center"}}, "card1Content": {"desktop": {"padding": {"top": 16, "bottom": 16, "left": 16, "right": 16}, "gap": 8, "height": "auto", "alignItems": "stretch", "flexDirection": "column"}, "tablet": {"padding": {"top": 16, "bottom": 16, "left": 16, "right": 16}, "gap": 8, "height": "auto", "alignItems": "stretch", "flexDirection": "column"}, "mobile": {"padding": {"top": 16, "bottom": 16, "left": 16, "right": 16}, "gap": 8, "height": "auto", "alignItems": "stretch", "flexDirection": "column"}}, "card1Title": {"desktop": {"text": "Mua 6 Tặng 2", "fontSize": 18, "fontWeight": 700, "color": "#0F1319", "textAlign": "left"}, "tablet": {"text": "Mua 6 Tặng 2", "fontSize": 18, "fontWeight": 700, "color": "#0F1319", "textAlign": "left"}, "mobile": {"text": "Mua 6 Tặng 2", "fontSize": 18, "fontWeight": 700, "color": "#0F1319", "textAlign": "left"}}, "card1Description": {"desktop": {"text": "<PERSON><PERSON><PERSON> ký <PERSON> 6 tháng, <PERSON><PERSON><PERSON><PERSON> ngay 2 tháng cư<PERSON><PERSON> mi<PERSON>n ph<PERSON>.", "fontSize": 14, "fontWeight": 400, "color": "#6b7280", "textAlign": "left"}, "tablet": {"text": "<PERSON><PERSON><PERSON> ký <PERSON> 6 tháng, <PERSON><PERSON><PERSON><PERSON> ngay 2 tháng cư<PERSON><PERSON> mi<PERSON>n ph<PERSON>.", "fontSize": 14, "fontWeight": 400, "color": "#6b7280", "textAlign": "left"}, "mobile": {"text": "<PERSON><PERSON><PERSON> ký <PERSON> 6 tháng, <PERSON><PERSON><PERSON><PERSON> ngay 2 tháng cư<PERSON><PERSON> mi<PERSON>n ph<PERSON>.", "fontSize": 14, "fontWeight": 400, "color": "#6b7280", "textAlign": "left"}}, "card1Condition": {"desktop": {"text": "<PERSON>p dụng: <PERSON><PERSON><PERSON><PERSON> hàng mới.", "fontSize": 12, "fontWeight": 400, "color": "#9ca3af", "textAlign": "left", "icon": "CheckCircleFilled", "iconColor": "#10b981", "hasIcon": true}, "tablet": {"text": "<PERSON>p dụng: <PERSON><PERSON><PERSON><PERSON> hàng mới.", "fontSize": 12, "fontWeight": 400, "color": "#9ca3af", "textAlign": "left", "icon": "CheckCircleFilled", "iconColor": "#10b981", "hasIcon": true}, "mobile": {"text": "<PERSON>p dụng: <PERSON><PERSON><PERSON><PERSON> hàng mới.", "fontSize": 12, "fontWeight": 400, "color": "#9ca3af", "textAlign": "left", "icon": "CheckCircleFilled", "iconColor": "#10b981", "hasIcon": true}}, "card1Time": {"desktop": {"text": "Th<PERSON>i gian: 01/07 - 31/07/2025.", "fontSize": 12, "fontWeight": 400, "color": "#9ca3af", "textAlign": "left", "icon": "CalendarFilled", "iconColor": "#10b981", "hasIcon": true}, "tablet": {"text": "Th<PERSON>i gian: 01/07 - 31/07/2025.", "fontSize": 12, "fontWeight": 400, "color": "#9ca3af", "textAlign": "left", "icon": "CalendarFilled", "iconColor": "#10b981", "hasIcon": true}, "mobile": {"text": "Th<PERSON>i gian: 01/07 - 31/07/2025.", "fontSize": 12, "fontWeight": 400, "color": "#9ca3af", "textAlign": "left", "icon": "CalendarFilled", "iconColor": "#10b981", "hasIcon": true}}, "card1Buttons": {"desktop": {"colWidths": [1, 1], "gap": 8, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 16, "bottom": 0, "left": 0, "right": 0}}, "tablet": {"colWidths": [1, 1], "gap": 8, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 16, "bottom": 0, "left": 0, "right": 0}}, "mobile": {"colWidths": [1, 1], "gap": 8, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 16, "bottom": 0, "left": 0, "right": 0}}}, "card1DetailBtn": {"desktop": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}}, "card1ApplyBtn": {"desktop": {"text": "<PERSON><PERSON>", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#ef9304", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON><PERSON>", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#ef9304", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON><PERSON>", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#ef9304", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}}, "promotionCard2": {"desktop": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "width": "100%", "border": {"radius": 8, "width": 1, "color": "#e5e7eb", "style": "solid"}, "shadow": 8}, "tablet": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "width": "100%", "border": {"radius": 8, "width": 1, "color": "#e5e7eb", "style": "solid"}, "shadow": 8}, "mobile": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "width": "100%", "border": {"radius": 8, "width": 1, "color": "#e5e7eb", "style": "solid"}, "shadow": 8}}, "card2Image": {"desktop": {"imgSrc": "https://placehold.co/600x300/FFF3E0/E65100?text=Tặng+Camera", "width": "100%", "height": 128, "border": {"radius": 0, "width": 0, "color": "transparent", "style": "none"}, "imageFit": "fill", "align": "center"}, "tablet": {"imgSrc": "https://placehold.co/600x300/FFF3E0/E65100?text=Tặng+Camera", "width": "100%", "height": 128, "border": {"radius": 0, "width": 0, "color": "transparent", "style": "none"}, "imageFit": "fill", "align": "center"}, "mobile": {"imgSrcMobile": "https://placehold.co/600x300/FFF3E0/E65100?text=Tặng+Camera", "width": "100%", "height": 128, "border": {"radius": 0, "width": 0, "color": "transparent", "style": "none"}, "imageFit": "fill", "align": "center"}}, "card2Content": {"desktop": {"padding": {"top": 16, "bottom": 16, "left": 16, "right": 16}, "gap": 8, "height": "auto", "alignItems": "stretch", "flexDirection": "column"}, "tablet": {"padding": {"top": 16, "bottom": 16, "left": 16, "right": 16}, "gap": 8, "height": "auto", "alignItems": "stretch", "flexDirection": "column"}, "mobile": {"padding": {"top": 16, "bottom": 16, "left": 16, "right": 16}, "gap": 8, "height": "auto", "alignItems": "stretch", "flexDirection": "column"}}, "card2Title": {"desktop": {"text": "Lắp Internet Tặng Camera", "fontSize": 18, "fontWeight": 700, "color": "#0F1319", "textAlign": "left"}, "tablet": {"text": "Lắp Internet Tặng Camera", "fontSize": 18, "fontWeight": 700, "color": "#0F1319", "textAlign": "left"}, "mobile": {"text": "Lắp Internet Tặng Camera", "fontSize": 18, "fontWeight": 700, "color": "#0F1319", "textAlign": "left"}}, "card2Description": {"desktop": {"text": "<PERSON><PERSON> bị miễn phí camera giám sát an toàn cho gia đình bạn.", "fontSize": 14, "fontWeight": 400, "color": "#6b7280", "textAlign": "left"}, "tablet": {"text": "<PERSON><PERSON> bị miễn phí camera giám sát an toàn cho gia đình bạn.", "fontSize": 14, "fontWeight": 400, "color": "#6b7280", "textAlign": "left"}, "mobile": {"text": "<PERSON><PERSON> bị miễn phí camera giám sát an toàn cho gia đình bạn.", "fontSize": 14, "fontWeight": 400, "color": "#6b7280", "textAlign": "left"}}, "card2Condition": {"desktop": {"text": "<PERSON><PERSON> dụng: <PERSON><PERSON><PERSON> Home Mesh 3+.", "fontSize": 12, "fontWeight": 400, "color": "#9ca3af", "textAlign": "left", "icon": "CheckCircleFilled", "iconColor": "#10b981", "hasIcon": true}, "tablet": {"text": "<PERSON><PERSON> dụng: <PERSON><PERSON><PERSON> Home Mesh 3+.", "fontSize": 12, "fontWeight": 400, "color": "#9ca3af", "textAlign": "left", "icon": "CheckCircleFilled", "iconColor": "#10b981", "hasIcon": true}, "mobile": {"text": "<PERSON><PERSON> dụng: <PERSON><PERSON><PERSON> Home Mesh 3+.", "fontSize": 12, "fontWeight": 400, "color": "#9ca3af", "textAlign": "left", "icon": "CheckCircleFilled", "iconColor": "#10b981", "hasIcon": true}}, "card2Time": {"desktop": {"text": "Th<PERSON>i gian: 01/07 - 31/07/2025.", "fontSize": 12, "fontWeight": 400, "color": "#9ca3af", "textAlign": "left", "icon": "CalendarFilled", "iconColor": "#10b981", "hasIcon": true}, "tablet": {"text": "Th<PERSON>i gian: 01/07 - 31/07/2025.", "fontSize": 12, "fontWeight": 400, "color": "#9ca3af", "textAlign": "left"}, "mobile": {"text": "Th<PERSON>i gian: 01/07 - 31/07/2025.", "fontSize": 12, "fontWeight": 400, "color": "#9ca3af", "textAlign": "left"}}, "card2Buttons": {"desktop": {"colWidths": [1, 1], "gap": 8, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 16, "bottom": 0, "left": 0, "right": 0}}, "tablet": {"colWidths": [1, 1], "gap": 8, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 16, "bottom": 0, "left": 0, "right": 0}}, "mobile": {"colWidths": [1, 1], "gap": 8, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 16, "bottom": 0, "left": 0, "right": 0}}}, "card2DetailBtn": {"desktop": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}}, "card2ApplyBtn": {"desktop": {"text": "<PERSON><PERSON>", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#ef9304", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON><PERSON>", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#ef9304", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON><PERSON>", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#ef9304", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle"}}, "promotionCard3": {"desktop": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "width": "100%", "border": {"radius": 8, "width": 1, "color": "#e5e7eb", "style": "solid"}, "shadow": 8}, "tablet": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "width": "100%", "border": {"radius": 8, "width": 1, "color": "#e5e7eb", "style": "solid"}, "shadow": 8}, "mobile": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "width": "100%", "border": {"radius": 8, "width": 1, "color": "#e5e7eb", "style": "solid"}, "shadow": 8}}, "card3Image": {"desktop": {"imgSrc": "https://placehold.co/600x300/E8F5E9/1B5E20?text=Tặng+Data", "width": "100%", "height": 128, "border": {"radius": 0, "width": 0, "color": "transparent", "style": "none"}, "imageFit": "fill", "align": "center"}, "tablet": {"imgSrc": "https://placehold.co/600x300/E8F5E9/1B5E20?text=Tặng+Data", "width": "100%", "height": 128, "border": {"radius": 0, "width": 0, "color": "transparent", "style": "none"}, "imageFit": "fill", "align": "center"}, "mobile": {"imgSrcMobile": "https://placehold.co/600x300/E8F5E9/1B5E20?text=Tặng+Data", "width": "100%", "height": 128, "border": {"radius": 0, "width": 0, "color": "transparent", "style": "none"}, "imageFit": "fill", "align": "center"}}, "card3Content": {"desktop": {"padding": {"top": 16, "bottom": 16, "left": 16, "right": 16}, "gap": 8, "height": "auto", "alignItems": "stretch", "flexDirection": "column"}, "tablet": {"padding": {"top": 16, "bottom": 16, "left": 16, "right": 16}, "gap": 8, "height": "auto", "alignItems": "stretch", "flexDirection": "column"}, "mobile": {"padding": {"top": 16, "bottom": 16, "left": 16, "right": 16}, "gap": 8, "height": "auto", "alignItems": "stretch", "flexDirection": "column"}}, "card3Title": {"desktop": {"text": "Combo Gia Đ<PERSON>", "fontSize": 18, "fontWeight": 700, "color": "#0F1319", "textAlign": "left"}, "tablet": {"text": "Combo Gia Đ<PERSON>", "fontSize": 18, "fontWeight": 700, "color": "#0F1319", "textAlign": "left"}, "mobile": {"text": "Combo Gia Đ<PERSON>", "fontSize": 18, "fontWeight": 700, "color": "#0F1319", "textAlign": "left"}}, "card3Description": {"desktop": {"text": "Giảm 20% khi đăng ký trọn bộ Internet - Truyền hình - Di động.", "fontSize": 14, "fontWeight": 400, "color": "#6b7280", "textAlign": "left"}, "tablet": {"text": "Giảm 20% khi đăng ký trọn bộ Internet - Truyền hình - Di động.", "fontSize": 14, "fontWeight": 400, "color": "#6b7280", "textAlign": "left"}, "mobile": {"text": "Giảm 20% khi đăng ký trọn bộ Internet - Truyền hình - Di động.", "fontSize": 14, "fontWeight": 400, "color": "#6b7280", "textAlign": "left"}}, "card3Condition": {"desktop": {"text": "Áp dụng: Gói Home Combo.", "fontSize": 12, "fontWeight": 400, "color": "#9ca3af", "textAlign": "left", "icon": "CheckCircleFilled", "iconColor": "#10b981", "hasIcon": true}, "tablet": {"text": "Áp dụng: Gói Home Combo.", "fontSize": 12, "fontWeight": 400, "color": "#9ca3af", "textAlign": "left", "icon": "CheckCircleFilled", "iconColor": "#10b981", "hasIcon": true}, "mobile": {"text": "Áp dụng: Gói Home Combo.", "fontSize": 12, "fontWeight": 400, "color": "#9ca3af", "textAlign": "left", "icon": "CheckCircleFilled", "iconColor": "#10b981", "hasIcon": true}}, "card3Time": {"desktop": {"text": "Th<PERSON>i gian: 01/07 - 31/07/2025.", "fontSize": 12, "fontWeight": 400, "color": "#9ca3af", "textAlign": "left", "icon": "CalendarFilled", "iconColor": "#10b981", "hasIcon": true}, "tablet": {"text": "Th<PERSON>i gian: 01/07 - 31/07/2025.", "fontSize": 12, "fontWeight": 400, "color": "#9ca3af", "textAlign": "left", "icon": "CalendarFilled", "iconColor": "#10b981", "hasIcon": true}, "mobile": {"text": "Th<PERSON>i gian: 01/07 - 31/07/2025.", "fontSize": 12, "fontWeight": 400, "color": "#9ca3af", "textAlign": "left", "icon": "CalendarFilled", "iconColor": "#10b981", "hasIcon": true}}, "card3Buttons": {"desktop": {"colWidths": [1, 1], "gap": 8, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 16, "bottom": 0, "left": 0, "right": 0}}, "tablet": {"colWidths": [1, 1], "gap": 8, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 16, "bottom": 0, "left": 0, "right": 0}}, "mobile": {"colWidths": [1, 1], "gap": 8, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 16, "bottom": 0, "left": 0, "right": 0}}}, "card3DetailBtn": {"desktop": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#ef9304", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}}, "card3ApplyBtn": {"desktop": {"text": "<PERSON><PERSON>", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#ef9304", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON><PERSON>", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#ef9304", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON><PERSON>", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#ea580c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}}, "id": "promotions"}, "displayName": "PromotionsHouseholdBuilder", "custom": {}, "parent": "Kk9Y7Diyq1", "hidden": false, "nodes": [], "linkedNodes": {"container": "BBk5wRvCcr"}}, "LVzadYzK8O": {"type": {"resolvedName": "BestSellingProductsHouseholdBuilder"}, "isCanvas": false, "props": {"container": {"height": "auto", "background": "#FFFFFF", "padding": ["0", "0", "0", "0"]}, "productsContainer": {"desktop": {"padding": {"top": 64, "bottom": 64, "left": 16, "right": 16}, "colWidths": [12], "gap": 6, "height": "auto", "alignItems": "center", "contentAlign": "start", "justifyContent": "center", "width": "100%"}, "tablet": {"padding": {"top": 48, "bottom": 48, "left": 16, "right": 16}, "colWidths": [12], "gap": 6, "height": "auto", "alignItems": "center", "contentAlign": "start", "justifyContent": "center", "width": "100%"}, "mobile": {"padding": {"top": 32, "bottom": 32, "left": 16, "right": 16}, "colWidths": [12], "gap": 6, "height": "auto", "alignItems": "center", "contentAlign": "start", "justifyContent": "center", "width": "100%"}}, "productsTitleSection": {"desktop": {"display": "flex", "justifyContent": "center", "alignItems": "center", "flexDirection": "column", "gap": 0, "margin": {"top": 0, "bottom": 40, "left": 0, "right": 0}}, "tablet": {"display": "flex", "justifyContent": "center", "alignItems": "center", "flexDirection": "column", "gap": 0, "margin": {"top": 0, "bottom": 40, "left": 0, "right": 0}}, "mobile": {"display": "flex", "justifyContent": "center", "alignItems": "center", "flexDirection": "column", "gap": 0, "margin": {"top": 0, "bottom": 40, "left": 0, "right": 0}}}, "productsTitle": {"desktop": {"text": "G<PERSON><PERSON><PERSON> ph<PERSON><PERSON> đ<PERSON> nhiều gia đình lựa chọn", "fontSize": 32, "fontWeight": 700, "color": "#00529c", "textAlign": "center"}, "tablet": {"text": "G<PERSON><PERSON><PERSON> ph<PERSON><PERSON> đ<PERSON> nhiều gia đình lựa chọn", "fontSize": 28, "fontWeight": 700, "color": "#00529c", "textAlign": "center"}, "mobile": {"text": "G<PERSON><PERSON><PERSON> ph<PERSON><PERSON> đ<PERSON> nhiều gia đình lựa chọn", "fontSize": 24, "fontWeight": 700, "color": "#00529c", "textAlign": "center"}}, "productsCardsRow": {"desktop": {"colWidths": [3, 3, 3, 3], "gap": 6, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%"}, "tablet": {"colWidths": [6, 6], "gap": 6, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "isBreakLine": true}, "mobile": {"colWidths": [12], "gap": 6, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "isBreakLine": true}}, "productColumnWrapper": {"desktop": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "100%", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "tablet": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "100%", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "mobile": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}, "gap": 0, "height": "100%", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}}, "productCard": {"desktop": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "tablet": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "mobile": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}}, "card1Image": {"desktop": {"imgSrc": "https://placehold.co/600x400/e2e8f0/334155?text=Home+Mesh+2", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "tablet": {"imgSrc": "https://placehold.co/600x400/e2e8f0/334155?text=Home+Mesh+2", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "mobile": {"imgSrcMobile": "https://placehold.co/600x400/e2e8f0/334155?text=Home+Mesh+2", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}}, "card1Title": {"desktop": {"text": "Home Mesh 2+", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}, "tablet": {"text": "Home Mesh 2+", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}, "mobile": {"text": "Home Mesh 2+", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}}, "card1Description": {"desktop": {"text": "Internet & TV cho gia đình", "fontSize": 14, "fontWeight": 400, "color": "#6b7280", "textAlign": "left"}, "tablet": {"text": "Internet & TV cho gia đình", "fontSize": 14, "fontWeight": 400, "color": "#6b7280", "textAlign": "left"}, "mobile": {"text": "Internet & TV cho gia đình", "fontSize": 14, "fontWeight": 400, "color": "#6b7280", "textAlign": "left"}}, "card1Promotion": {"desktop": {"text": "<div style=\"background-color: #f0fdf4; padding: 8px; border-radius: 4px; margin: 8px 0;\"><p style=\"font-size: 12px; font-weight: 700; color: #16a34a; margin: 0;\">KM: Tặng thêm 2 tháng khi đóng trước 6 tháng.</p></div>", "fontSize": 12, "fontWeight": 700, "color": "#16a34a", "textAlign": "left", "padding": {"top": 8, "bottom": 8, "left": 0, "right": 0}, "backgroundColor": "#f0fdf4", "border": {"radius": 4}}, "tablet": {"text": "<div style=\"background-color: #f0fdf4; padding: 8px; border-radius: 4px; margin: 8px 0;\"><p style=\"font-size: 12px; font-weight: 700; color: #16a34a; margin: 0;\">KM: Tặng thêm 2 tháng khi đóng trước 6 tháng.</p></div>", "fontSize": 12, "fontWeight": 700, "color": "#16a34a", "textAlign": "left", "padding": {"top": 8, "bottom": 8, "left": 0, "right": 0}, "backgroundColor": "#f0fdf4", "border": {"radius": 4}}, "mobile": {"text": "<div style=\"background-color: #f0fdf4; padding: 8px; border-radius: 4px; margin: 8px 0;\"><p style=\"font-size: 12px; font-weight: 700; color: #16a34a; margin: 0;\">KM: Tặng thêm 2 tháng khi đóng trước 6 tháng.</p></div>", "fontSize": 12, "fontWeight": 700, "color": "#16a34a", "textAlign": "left", "padding": {"top": 8, "bottom": 8, "left": 0, "right": 0}, "backgroundColor": "#f0fdf4", "border": {"radius": 4}}}, "card1Price": {"desktop": {"text": "245.000đ<span style=\"font-size: 16px; font-weight: 400;\">/tháng</span>", "fontSize": 24, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}, "tablet": {"text": "245.000đ<span style=\"font-size: 16px; font-weight: 400;\">/tháng</span>", "fontSize": 24, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}, "mobile": {"text": "245.000đ<span style=\"font-size: 16px; font-weight: 400;\">/tháng</span>", "fontSize": 24, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}}, "card1Buttons": {"desktop": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "tablet": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "mobile": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}}, "card1DetailBtn": {"desktop": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}}, "card1BuyBtn": {"desktop": {"text": "<PERSON><PERSON> ngay", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON><PERSON> ngay", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON><PERSON> ngay", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}}, "card2Image": {"desktop": {"imgSrc": "https://placehold.co/600x400/e2e8f0/334155?text=Home+Camera", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "tablet": {"imgSrc": "https://placehold.co/600x400/e2e8f0/334155?text=Home+Camera", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "mobile": {"imgSrcMobile": "https://placehold.co/600x400/e2e8f0/334155?text=Home+Camera", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}}, "card2Content": {"desktop": {"padding": {"top": 16, "bottom": 16, "left": 16, "right": 16}, "gap": 8, "height": "100%", "alignItems": "stretch", "justifyContent": "space-between", "flexDirection": "column", "display": "flex", "flex": "grow"}, "tablet": {"padding": {"top": 16, "bottom": 16, "left": 16, "right": 16}, "gap": 8, "height": "100%", "alignItems": "stretch", "justifyContent": "space-between", "flexDirection": "column", "display": "flex", "flex": "grow"}, "mobile": {"padding": {"top": 16, "bottom": 16, "left": 16, "right": 16}, "gap": 8, "height": "100%", "alignItems": "stretch", "justifyContent": "space-between", "flexDirection": "column", "display": "flex", "flex": "grow"}}, "card2Title": {"desktop": {"text": "Home Camera", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}, "tablet": {"text": "Home Camera", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}, "mobile": {"text": "Home Camera", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}}, "card2Description": {"desktop": {"text": "Internet & Camera an ninh", "fontSize": 14, "fontWeight": 400, "color": "#6b7280", "textAlign": "left"}, "tablet": {"text": "Internet & Camera an ninh", "fontSize": 14, "fontWeight": 400, "color": "#6b7280", "textAlign": "left"}, "mobile": {"text": "Internet & Camera an ninh", "fontSize": 14, "fontWeight": 400, "color": "#6b7280", "textAlign": "left"}}, "card2Promotion": {"desktop": {"text": "<div style=\"background-color: #fff7ed; padding: 8px; border-radius: 4px; margin: 8px 0;\"><p style=\"font-size: 12px; font-weight: 700; color: #ea580c; margin: 0;\">KM: Tặng 01 Camera khi lắp mới.</p></div>", "fontSize": 12, "fontWeight": 700, "color": "#ea580c", "textAlign": "left", "padding": {"top": 8, "bottom": 8, "left": 0, "right": 0}, "backgroundColor": "#fff7ed", "border": {"radius": 4}}, "tablet": {"text": "<div style=\"background-color: #fff7ed; padding: 8px; border-radius: 4px; margin: 8px 0;\"><p style=\"font-size: 12px; font-weight: 700; color: #ea580c; margin: 0;\">KM: Tặng 01 Camera khi lắp mới.</p></div>", "fontSize": 12, "fontWeight": 700, "color": "#ea580c", "textAlign": "left", "padding": {"top": 8, "bottom": 8, "left": 0, "right": 0}, "backgroundColor": "#fff7ed", "border": {"radius": 4}}, "mobile": {"text": "<div style=\"background-color: #fff7ed; padding: 8px; border-radius: 4px; margin: 8px 0;\"><p style=\"font-size: 12px; font-weight: 700; color: #ea580c; margin: 0;\">KM: Tặng 01 Camera khi lắp mới.</p></div>", "fontSize": 12, "fontWeight": 700, "color": "#ea580c", "textAlign": "left", "padding": {"top": 8, "bottom": 8, "left": 0, "right": 0}, "backgroundColor": "#fff7ed", "border": {"radius": 4}}}, "card2Price": {"desktop": {"text": "210.000đ<span style=\"font-size: 16px; font-weight: 400;\">/tháng</span>", "fontSize": 24, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}, "tablet": {"text": "210.000đ<span style=\"font-size: 16px; font-weight: 400;\">/tháng</span>", "fontSize": 24, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}, "mobile": {"text": "210.000đ<span style=\"font-size: 16px; font-weight: 400;\">/tháng</span>", "fontSize": 24, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}}, "card2Buttons": {"desktop": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "tablet": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "mobile": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}}, "card2DetailBtn": {"desktop": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}}, "card2BuyBtn": {"desktop": {"text": "<PERSON><PERSON> ngay", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON><PERSON> ngay", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON><PERSON> ngay", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}}, "card3Image": {"desktop": {"imgSrc": "https://placehold.co/600x400/e2e8f0/334155?text=Home+Combo", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "tablet": {"imgSrc": "https://placehold.co/600x400/e2e8f0/334155?text=Home+Combo", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "mobile": {"imgSrcMobile": "https://placehold.co/600x400/e2e8f0/334155?text=Home+Combo", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}}, "card3Content": {"desktop": {"padding": {"top": 16, "bottom": 16, "left": 16, "right": 16}, "gap": 8, "height": "100%", "alignItems": "stretch", "justifyContent": "space-between", "flexDirection": "column", "display": "flex"}, "tablet": {"padding": {"top": 16, "bottom": 16, "left": 16, "right": 16}, "gap": 8, "height": "100%", "alignItems": "stretch", "justifyContent": "space-between", "flexDirection": "column", "display": "flex"}, "mobile": {"padding": {"top": 16, "bottom": 16, "left": 16, "right": 16}, "gap": 8, "height": "100%", "alignItems": "stretch", "justifyContent": "space-between", "flexDirection": "column", "display": "flex"}}, "card3Title": {"desktop": {"text": "Home Combo", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}, "tablet": {"text": "Home Combo", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}, "mobile": {"text": "Home Combo", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}}, "card3Description": {"desktop": {"text": "Trọn gói Internet - TV - Di động", "fontSize": 14, "fontWeight": 400, "color": "#6b7280", "textAlign": "left"}, "tablet": {"text": "Trọn gói Internet - TV - Di động", "fontSize": 14, "fontWeight": 400, "color": "#6b7280", "textAlign": "left"}, "mobile": {"text": "Trọn gói Internet - TV - Di động", "fontSize": 14, "fontWeight": 400, "color": "#6b7280", "textAlign": "left"}}, "card3Promotion": {"desktop": {"text": "<div style=\"background-color: #eff6ff; padding: 8px; border-radius: 4px; margin: 8px 0;\"><p style=\"font-size: 12px; font-weight: 700; color: #2563eb; margin: 0;\">KM: <PERSON><PERSON><PERSON><PERSON> kiệm đến 50% so với dùng lẻ.</p></div>", "fontSize": 12, "fontWeight": 700, "color": "#2563eb", "textAlign": "left", "padding": {"top": 8, "bottom": 8, "left": 0, "right": 0}, "backgroundColor": "#eff6ff", "border": {"radius": 4}}, "tablet": {"text": "<div style=\"background-color: #eff6ff; padding: 8px; border-radius: 4px; margin: 8px 0;\"><p style=\"font-size: 12px; font-weight: 700; color: #2563eb; margin: 0;\">KM: <PERSON><PERSON><PERSON><PERSON> kiệm đến 50% so với dùng lẻ.</p></div>", "fontSize": 12, "fontWeight": 700, "color": "#2563eb", "textAlign": "left", "padding": {"top": 8, "bottom": 8, "left": 0, "right": 0}, "backgroundColor": "#eff6ff", "border": {"radius": 4}}, "mobile": {"text": "<div style=\"background-color: #eff6ff; padding: 8px; border-radius: 4px; margin: 8px 0;\"><p style=\"font-size: 12px; font-weight: 700; color: #2563eb; margin: 0;\">KM: <PERSON><PERSON><PERSON><PERSON> kiệm đến 50% so với dùng lẻ.</p></div>", "fontSize": 12, "fontWeight": 700, "color": "#2563eb", "textAlign": "left", "padding": {"top": 8, "bottom": 8, "left": 0, "right": 0}, "backgroundColor": "#eff6ff", "border": {"radius": 4}}}, "card3Price": {"desktop": {"text": "Từ 239.000đ", "fontSize": 20, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}, "tablet": {"text": "Từ 239.000đ", "fontSize": 20, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}, "mobile": {"text": "Từ 239.000đ", "fontSize": 20, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}}, "card3Buttons": {"desktop": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "tablet": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "mobile": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}}, "card3DetailBtn": {"desktop": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}}, "card3ConsultBtn": {"desktop": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}}, "card4Image": {"desktop": {"imgSrc": "https://placehold.co/600x400/e2e8f0/334155?text=Smart+Home", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "tablet": {"imgSrc": "https://placehold.co/600x400/e2e8f0/334155?text=Smart+Home", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "mobile": {"imgSrcMobile": "https://placehold.co/600x400/e2e8f0/334155?text=Smart+Home", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}}, "card4Content": {"desktop": {"padding": {"top": 16, "bottom": 16, "left": 16, "right": 16}, "gap": 8, "height": "100%", "alignItems": "stretch", "justifyContent": "space-between", "flexDirection": "column", "display": "flex"}, "tablet": {"padding": {"top": 16, "bottom": 16, "left": 16, "right": 16}, "gap": 8, "height": "100%", "alignItems": "stretch", "justifyContent": "space-between", "flexDirection": "column", "display": "flex"}, "mobile": {"padding": {"top": 16, "bottom": 16, "left": 16, "right": 16}, "gap": 8, "height": "100%", "alignItems": "stretch", "justifyContent": "space-between", "flexDirection": "column", "display": "flex"}}, "card4Title": {"desktop": {"text": "VNPT SmartHome", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}, "tablet": {"text": "VNPT SmartHome", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}, "mobile": {"text": "VNPT SmartHome", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}}, "card4Description": {"desktop": {"text": "<PERSON><PERSON><PERSON> thông minh trong tầm tay", "fontSize": 14, "fontWeight": 400, "color": "#6b7280", "textAlign": "left"}, "tablet": {"text": "<PERSON><PERSON><PERSON> thông minh trong tầm tay", "fontSize": 14, "fontWeight": 400, "color": "#6b7280", "textAlign": "left"}, "mobile": {"text": "<PERSON><PERSON><PERSON> thông minh trong tầm tay", "fontSize": 14, "fontWeight": 400, "color": "#6b7280", "textAlign": "left"}}, "card4Empty": {"desktop": {"text": "", "fontSize": 12, "fontWeight": 400, "color": "transparent", "textAlign": "left", "padding": {"top": 52, "bottom": 0, "left": 0, "right": 0}}, "tablet": {"text": "", "fontSize": 12, "fontWeight": 400, "color": "transparent", "textAlign": "left", "padding": {"top": 52, "bottom": 0, "left": 0, "right": 0}}, "mobile": {"text": "", "fontSize": 12, "fontWeight": 400, "color": "transparent", "textAlign": "left", "padding": {"top": 52, "bottom": 0, "left": 0, "right": 0}}}, "card4Contact": {"desktop": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}, "tablet": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}, "mobile": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}}, "card4Buttons": {"desktop": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "tablet": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "mobile": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}}, "card4DetailBtn": {"desktop": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}}, "card4ConsultBtn": {"desktop": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}}, "id": "best-selling-products"}, "displayName": "BestSellingProductsHouseholdBuilder", "custom": {}, "parent": "Kk9Y7Diyq1", "hidden": false, "nodes": [], "linkedNodes": {"container": "xqrzsGNYti"}}, "zdid6kiHPD": {"type": {"resolvedName": "AllSolutionsHouseholdBuilder"}, "isCanvas": false, "props": {"container": {"height": "auto", "background": "#FFFFFF", "padding": ["0", "0", "0", "0"]}, "allSolutionsContainer": {"desktop": {"padding": {"top": 64, "bottom": 64, "left": 16, "right": 16}, "colWidths": [12], "gap": 6, "height": "auto", "alignItems": "center", "contentAlign": "start", "justifyContent": "center", "width": "100%"}, "tablet": {"padding": {"top": 48, "bottom": 48, "left": 16, "right": 16}, "colWidths": [12], "gap": 6, "height": "auto", "alignItems": "center", "contentAlign": "start", "justifyContent": "center", "width": "100%"}, "mobile": {"padding": {"top": 32, "bottom": 32, "left": 16, "right": 16}, "colWidths": [12], "gap": 6, "height": "auto", "alignItems": "center", "contentAlign": "start", "justifyContent": "center", "width": "100%"}}, "allSolutionsTitle": {"desktop": {"text": "<PERSON><PERSON><PERSON> c<PERSON> pháp cho Gia đình", "fontSize": 32, "fontWeight": 700, "color": "#00529c", "textAlign": "center"}, "tablet": {"text": "<PERSON><PERSON><PERSON> c<PERSON> pháp cho Gia đình", "fontSize": 28, "fontWeight": 700, "color": "#00529c", "textAlign": "center"}, "mobile": {"text": "<PERSON><PERSON><PERSON> c<PERSON> pháp cho Gia đình", "fontSize": 24, "fontWeight": 700, "color": "#00529c", "textAlign": "center"}}, "sectionTitle": {"desktop": {"display": "flex", "justifyContent": "center", "alignItems": "center", "flexDirection": "column", "gap": 0, "margin": {"top": 0, "bottom": 40, "left": 0, "right": 0}}, "tablet": {"display": "flex", "justifyContent": "center", "alignItems": "center", "flexDirection": "column", "gap": 0, "margin": {"top": 0, "bottom": 40, "left": 0, "right": 0}}, "mobile": {"display": "flex", "justifyContent": "center", "alignItems": "center", "flexDirection": "column", "gap": 0, "margin": {"top": 0, "bottom": 40, "left": 0, "right": 0}}}, "id": "all-solutions"}, "displayName": "<PERSON><PERSON><PERSON> c<PERSON> pháp cho Gia đình", "custom": {}, "parent": "Kk9Y7Diyq1", "hidden": false, "nodes": [], "linkedNodes": {"container": "9fokxySWtq"}}, "JoYgF4jr03": {"type": {"resolvedName": "WhyChooseMeHouseholdBuilder"}, "isCanvas": false, "props": {"container": {"height": "auto", "background": "#FFFFFF", "padding": ["0", "0", "0", "0"]}, "titleRow": {"desktop": {"backgroundColor": "#f1f5f9", "backgroundType": "COLOR", "padding": {"top": 64, "bottom": 40, "left": 16, "right": 16}, "height": "auto", "justifyContent": "center"}, "tablet": {"backgroundColor": "#f1f5f9", "backgroundType": "COLOR", "padding": {"top": 64, "bottom": 40, "left": 16, "right": 16}, "height": "auto", "justifyContent": "center"}, "mobile": {"backgroundColor": "#f1f5f9", "backgroundType": "COLOR", "padding": {"top": 48, "bottom": 32, "left": 16, "right": 16}, "height": "auto", "justifyContent": "center"}}, "reasonsRow": {"desktop": {"backgroundColor": "#f1f5f9", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 64, "left": 16, "right": 16}, "colWidths": [4, 4, 4], "gap": 8, "height": "auto"}, "tablet": {"backgroundColor": "#f1f5f9", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 64, "left": 16, "right": 16}, "colWidths": [4, 4, 4], "gap": 8, "height": "auto"}, "mobile": {"backgroundColor": "#f1f5f9", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 48, "left": 16, "right": 16}, "isBreakLine": true, "gap": 8, "height": "auto"}}, "reasonColumn": {"desktop": {"justifyContent": "center", "alignItems": "center", "contentAlign": "center"}, "tablet": {"justifyContent": "center", "alignItems": "center", "contentAlign": "center"}, "mobile": {"justifyContent": "center", "alignItems": "center", "contentAlign": "center"}}, "sectionTitle": {"desktop": {"text": "Tại sao chọn tôi?", "fontSize": 32, "fontWeight": 700, "color": "#00529c", "textAlign": "center", "marginBottom": 40}, "tablet": {"text": "Tại sao chọn tôi?", "fontSize": 28, "fontWeight": 700, "color": "#00529c", "textAlign": "center", "marginBottom": 32}, "mobile": {"text": "Tại sao chọn tôi?", "fontSize": 24, "fontWeight": 700, "color": "#00529c", "textAlign": "center", "marginBottom": 24}}, "icon1": {"desktop": {"icon": "BulbFilled", "iconColor": "#00529c", "size": 24, "bgIconColor": "#dbeafe", "borderIcon": {"radius": 50}, "paddingIcon": {"top": 20, "bottom": 20, "left": 20, "right": 20}, "width": 64, "height": 64, "showIcon": {"desktop": true, "tablet": true, "mobile": true}, "showTitle": {"desktop": false, "tablet": false, "mobile": false}, "showDesc": {"desktop": false, "tablet": false, "mobile": false}, "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "margin": {"top": 0, "bottom": 0}, "title": "", "desc": "", "backgroundType": "NONE"}, "tablet": {"icon": "BulbFilled", "iconColor": "#00529c", "size": 24, "bgIconColor": "#dbeafe", "borderIcon": {"radius": 50}, "paddingIcon": {"top": 20, "bottom": 20, "left": 20, "right": 20}, "width": 64, "height": 64, "showIcon": {"desktop": true, "tablet": true, "mobile": true}, "showTitle": {"desktop": false, "tablet": false, "mobile": false}, "showDesc": {"desktop": false, "tablet": false, "mobile": false}, "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "margin": {"top": 0, "bottom": 0}, "title": "", "desc": "", "backgroundType": "NONE"}, "mobile": {"icon": "BulbFilled", "iconColor": "#00529c", "size": 24, "bgIconColor": "#dbeafe", "borderIcon": {"radius": 50}, "paddingIcon": {"top": 20, "bottom": 20, "left": 20, "right": 20}, "width": 64, "height": 64, "showIcon": {"desktop": true, "tablet": true, "mobile": true}, "showTitle": {"desktop": false, "tablet": false, "mobile": false}, "showDesc": {"desktop": false, "tablet": false, "mobile": false}, "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "margin": {"top": 0, "bottom": 0}, "title": "", "desc": "", "backgroundType": "NONE"}}, "title1": {"desktop": {"text": "<PERSON><PERSON> vấn đúng nhu cầu", "fontSize": 20, "fontWeight": 700, "color": "#000000", "textAlign": "center"}, "tablet": {"text": "<PERSON><PERSON> vấn đúng nhu cầu", "fontSize": 20, "fontWeight": 700, "color": "#000000", "textAlign": "center"}, "mobile": {"text": "<PERSON><PERSON> vấn đúng nhu cầu", "fontSize": 20, "fontWeight": 700, "color": "#000000", "textAlign": "center"}}, "desc1": {"desktop": {"text": "<PERSON><PERSON><PERSON> lắng nghe và phân tích để đưa ra gi<PERSON>i pháp phù hợp nhất, không tư vấn thừa.", "fontSize": 16, "fontWeight": 400, "color": "#6b7280", "textAlign": "center"}, "tablet": {"text": "<PERSON><PERSON><PERSON> lắng nghe và phân tích để đưa ra gi<PERSON>i pháp phù hợp nhất, không tư vấn thừa.", "fontSize": 16, "fontWeight": 400, "color": "#6b7280", "textAlign": "center"}, "mobile": {"text": "<PERSON><PERSON><PERSON> lắng nghe và phân tích để đưa ra gi<PERSON>i pháp phù hợp nhất, không tư vấn thừa.", "fontSize": 14, "fontWeight": 400, "color": "#6b7280", "textAlign": "center"}}, "icon2": {"desktop": {"icon": "TagsFilled", "iconColor": "#f7941e", "size": 24, "bgIconColor": "#fed7aa", "borderIcon": {"radius": 50}, "paddingIcon": {"top": 20, "bottom": 20, "left": 20, "right": 20}, "width": 64, "height": 64, "showIcon": {"desktop": true, "tablet": true, "mobile": true}, "showTitle": {"desktop": false, "tablet": false, "mobile": false}, "showDesc": {"desktop": false, "tablet": false, "mobile": false}, "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "margin": {"top": 0, "bottom": 0}, "title": "", "desc": "", "backgroundType": "NONE"}, "tablet": {"icon": "TagsFilled", "iconColor": "#f7941e", "size": 24, "bgIconColor": "#fed7aa", "borderIcon": {"radius": 50}, "paddingIcon": {"top": 20, "bottom": 20, "left": 20, "right": 20}, "width": 64, "height": 64, "showIcon": {"desktop": true, "tablet": true, "mobile": true}, "showTitle": {"desktop": false, "tablet": false, "mobile": false}, "showDesc": {"desktop": false, "tablet": false, "mobile": false}, "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "margin": {"top": 0, "bottom": 0}, "title": "", "desc": "", "backgroundType": "NONE"}, "mobile": {"icon": "TagsFilled", "iconColor": "#f7941e", "size": 24, "bgIconColor": "#fed7aa", "borderIcon": {"radius": 50}, "paddingIcon": {"top": 20, "bottom": 20, "left": 20, "right": 20}, "width": 64, "height": 64, "showIcon": {"desktop": true, "tablet": true, "mobile": true}, "showTitle": {"desktop": false, "tablet": false, "mobile": false}, "showDesc": {"desktop": false, "tablet": false, "mobile": false}, "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "margin": {"top": 0, "bottom": 0}, "title": "", "desc": "", "backgroundType": "NONE"}}, "title2": {"desktop": {"text": "<PERSON><PERSON><PERSON> & Ưu đãi tốt nhất", "fontSize": 20, "fontWeight": 700, "color": "#000000", "textAlign": "center"}, "tablet": {"text": "<PERSON><PERSON><PERSON> & Ưu đãi tốt nhất", "fontSize": 20, "fontWeight": 700, "color": "#000000", "textAlign": "center"}, "mobile": {"text": "<PERSON><PERSON><PERSON> & Ưu đãi tốt nhất", "fontSize": 20, "fontWeight": 700, "color": "#000000", "textAlign": "center"}}, "desc2": {"desktop": {"text": "<PERSON><PERSON><PERSON><PERSON> bạn tiếp cận các chương trình khuyến mại và gói cước tiết kiệm nhất từ VNPT.", "fontSize": 16, "fontWeight": 400, "color": "#6b7280", "textAlign": "center"}, "tablet": {"text": "<PERSON><PERSON><PERSON><PERSON> bạn tiếp cận các chương trình khuyến mại và gói cước tiết kiệm nhất từ VNPT.", "fontSize": 16, "fontWeight": 400, "color": "#6b7280", "textAlign": "center"}, "mobile": {"text": "<PERSON><PERSON><PERSON><PERSON> bạn tiếp cận các chương trình khuyến mại và gói cước tiết kiệm nhất từ VNPT.", "fontSize": 14, "fontWeight": 400, "color": "#6b7280", "textAlign": "center"}}, "icon3": {"desktop": {"icon": "CustomerServiceFilled", "iconColor": "#16a34a", "size": 24, "bgIconColor": "#bbf7d0", "borderIcon": {"radius": 50}, "paddingIcon": {"top": 20, "bottom": 20, "left": 20, "right": 20}, "width": 64, "height": 64, "showIcon": {"desktop": true, "tablet": true, "mobile": true}, "showTitle": {"desktop": false, "tablet": false, "mobile": false}, "showDesc": {"desktop": false, "tablet": false, "mobile": false}, "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "margin": {"top": 0, "bottom": 0}, "title": "", "desc": "", "backgroundType": "NONE"}, "tablet": {"icon": "CustomerServiceFilled", "iconColor": "#16a34a", "size": 24, "bgIconColor": "#bbf7d0", "borderIcon": {"radius": 50}, "paddingIcon": {"top": 20, "bottom": 20, "left": 20, "right": 20}, "width": 64, "height": 64, "showIcon": {"desktop": true, "tablet": true, "mobile": true}, "showTitle": {"desktop": false, "tablet": false, "mobile": false}, "showDesc": {"desktop": false, "tablet": false, "mobile": false}, "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "margin": {"top": 0, "bottom": 0}, "title": "", "desc": "", "backgroundType": "NONE"}, "mobile": {"icon": "CustomerServiceFilled", "iconColor": "#16a34a", "size": 24, "bgIconColor": "#bbf7d0", "borderIcon": {"radius": 50}, "paddingIcon": {"top": 20, "bottom": 20, "left": 20, "right": 20}, "width": 64, "height": 64, "showIcon": {"desktop": true, "tablet": true, "mobile": true}, "showTitle": {"desktop": false, "tablet": false, "mobile": false}, "showDesc": {"desktop": false, "tablet": false, "mobile": false}, "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "margin": {"top": 0, "bottom": 0}, "title": "", "desc": "", "backgroundType": "NONE"}}, "title3": {"desktop": {"text": "Hỗ trợ trọn đời", "fontSize": 20, "fontWeight": 700, "color": "#000000", "textAlign": "center"}, "tablet": {"text": "Hỗ trợ trọn đời", "fontSize": 20, "fontWeight": 700, "color": "#000000", "textAlign": "center"}, "mobile": {"text": "Hỗ trợ trọn đời", "fontSize": 20, "fontWeight": 700, "color": "#000000", "textAlign": "center"}}, "desc3": {"desktop": {"text": "<PERSON><PERSON>ng hành cùng bạn trong suốt quá trình sử dụng dịch vụ, hỗ trợ 24/7 khi cần.", "fontSize": 16, "fontWeight": 400, "color": "#6b7280", "textAlign": "center"}, "tablet": {"text": "<PERSON><PERSON>ng hành cùng bạn trong suốt quá trình sử dụng dịch vụ, hỗ trợ 24/7 khi cần.", "fontSize": 16, "fontWeight": 400, "color": "#6b7280", "textAlign": "center"}, "mobile": {"text": "<PERSON><PERSON>ng hành cùng bạn trong suốt quá trình sử dụng dịch vụ, hỗ trợ 24/7 khi cần.", "fontSize": 14, "fontWeight": 400, "color": "#6b7280", "textAlign": "center"}}, "spacer": {"desktop": {"height": 16}, "tablet": {"height": 16}, "mobile": {"height": 16}}, "id": "why-choose-me"}, "displayName": "WhyChooseMeHouseholdBuilder", "custom": {}, "parent": "Kk9Y7Diyq1", "hidden": false, "nodes": [], "linkedNodes": {"container": "Ds3SdQibje"}}, "-ZQtN2FA5d": {"type": {"resolvedName": "IoTFooterBuilder"}, "isCanvas": false, "props": {"footerMainRow": {"desktop": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "backgroundColor": "#374151", "text": "Text", "padding": {"top": 32, "bottom": 32, "left": 16, "right": 16}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "imgSrc": null, "externalLink": null, "imgSrcMobile": null, "externalLinkMobile": null, "typeUpload": "EXIST", "imageFit": "fit", "shadow": 0, "width": "100%", "height": "auto", "border": {"style": "none", "color": "#212D6E", "thickness": 0, "radius": 0}, "backgroundType": "COLOR", "borderStyle": "none", "contentAlign": "start", "justifyContent": "center", "align": "center", "colWidths": [4, 4, 4], "gap": 20, "isBreakLine": false, "hide": false}, "tablet": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "backgroundColor": "#374151", "text": "Text", "padding": {"top": 32, "bottom": 32, "left": 16, "right": 16}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "imgSrc": null, "externalLink": null, "imgSrcMobile": null, "externalLinkMobile": null, "typeUpload": "EXIST", "imageFit": "fit", "shadow": 0, "width": "100%", "height": "auto", "border": {"style": "none", "color": "#212D6E", "thickness": 0, "radius": 0}, "backgroundType": "COLOR", "borderStyle": "none", "contentAlign": "start", "justifyContent": "center", "align": "center", "colWidths": [4, 4, 4], "gap": 20, "isBreakLine": false, "hide": false}, "mobile": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "backgroundColor": "#374151", "text": "Text", "padding": {"top": 32, "bottom": 32, "left": 16, "right": 16}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "imgSrc": null, "externalLink": null, "imgSrcMobile": null, "externalLinkMobile": null, "typeUpload": "EXIST", "imageFit": "fit", "shadow": 0, "width": "100%", "height": "auto", "border": {"style": "none", "color": "#212D6E", "thickness": 0, "radius": 0}, "backgroundType": "COLOR", "borderStyle": "none", "contentAlign": "start", "justifyContent": "center", "align": "center", "colWidths": [6], "gap": 20, "isBreakLine": true, "hide": false}}, "footerBottomRow": {"desktop": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "backgroundColor": "#374151", "text": "Text", "padding": {"top": 24, "bottom": 24, "left": 16, "right": 16}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "imgSrc": null, "externalLink": null, "imgSrcMobile": null, "externalLinkMobile": null, "typeUpload": "EXIST", "imageFit": "fit", "shadow": 0, "width": "100%", "height": "auto", "border": {"style": "none", "color": "#212D6E", "thickness": 0, "radius": 0}, "backgroundType": "COLOR", "borderStyle": "none", "contentAlign": "start", "justifyContent": "center", "align": "center", "colWidths": [8, 4], "gap": 0, "isBreakLine": false, "hide": false}, "tablet": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "backgroundColor": "#374151", "text": "Text", "padding": {"top": 24, "bottom": 24, "left": 16, "right": 16}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "imgSrc": null, "externalLink": null, "imgSrcMobile": null, "externalLinkMobile": null, "typeUpload": "EXIST", "imageFit": "fit", "shadow": 0, "width": "100%", "height": "auto", "border": {"style": "none", "color": "#212D6E", "thickness": 0, "radius": 0}, "backgroundType": "COLOR", "borderStyle": "none", "contentAlign": "start", "justifyContent": "center", "align": "center", "colWidths": [8, 4], "gap": 0, "isBreakLine": false, "hide": false}, "mobile": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "backgroundColor": "#374151", "text": "Text", "padding": {"top": 24, "bottom": 24, "left": 16, "right": 16}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "imgSrc": null, "externalLink": null, "imgSrcMobile": null, "externalLinkMobile": null, "typeUpload": "EXIST", "imageFit": "fit", "shadow": 0, "width": "100%", "height": "auto", "border": {"style": "none", "color": "#212D6E", "thickness": 0, "radius": 0}, "backgroundType": "COLOR", "borderStyle": "none", "contentAlign": "start", "justifyContent": "center", "align": "center", "colWidths": [6], "gap": 0, "isBreakLine": true, "hide": false}}, "infoColumn": {"desktop": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "text": "Text", "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "imgSrc": null, "externalLink": null, "imgSrcMobile": null, "externalLinkMobile": null, "typeUpload": "EXIST", "imageFit": "fill", "shadow": 0, "width": "100%", "height": "auto", "border": {"style": "none", "color": "#212D6E", "thickness": 0, "radius": 0}, "backgroundType": "", "borderStyle": "none", "contentAlign": "start", "justifyContent": "center", "align": "center", "colWidths": [12], "gap": 4, "isBreakLine": false, "hide": false}, "tablet": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "text": "Text", "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "imgSrc": null, "externalLink": null, "imgSrcMobile": null, "externalLinkMobile": null, "typeUpload": "EXIST", "imageFit": "fill", "shadow": 0, "width": "100%", "height": "auto", "border": {"style": "none", "color": "#212D6E", "thickness": 0, "radius": 0}, "backgroundType": "", "borderStyle": "none", "contentAlign": "start", "justifyContent": "center", "align": "center", "colWidths": [12], "gap": 4, "isBreakLine": false, "hide": false}, "mobile": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "text": "Text", "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "imgSrc": null, "externalLink": null, "imgSrcMobile": null, "externalLinkMobile": null, "typeUpload": "EXIST", "imageFit": "fill", "shadow": 0, "width": "100%", "height": "auto", "border": {"style": "none", "color": "#212D6E", "thickness": 0, "radius": 0}, "backgroundType": "", "borderStyle": "none", "contentAlign": "start", "justifyContent": "center", "align": "center", "colWidths": [6], "gap": 4, "isBreakLine": false, "hide": false}}, "linksColumn": {"desktop": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "text": "Text", "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "imgSrc": null, "externalLink": null, "imgSrcMobile": null, "externalLinkMobile": null, "typeUpload": "EXIST", "imageFit": "fill", "shadow": 0, "width": "100%", "height": "auto", "border": {"style": "none", "color": "#212D6E", "thickness": 0, "radius": 0}, "backgroundType": "", "borderStyle": "none", "contentAlign": "start", "justifyContent": "center", "align": "center", "colWidths": [12], "gap": 4, "isBreakLine": false, "hide": false}, "tablet": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "text": "Text", "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "imgSrc": null, "externalLink": null, "imgSrcMobile": null, "externalLinkMobile": null, "typeUpload": "EXIST", "imageFit": "fill", "shadow": 0, "width": "100%", "height": "auto", "border": {"style": "none", "color": "#212D6E", "thickness": 0, "radius": 0}, "backgroundType": "", "borderStyle": "none", "contentAlign": "start", "justifyContent": "center", "align": "center", "colWidths": [12], "gap": 4, "isBreakLine": false, "hide": false}, "mobile": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "text": "Text", "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "imgSrc": null, "externalLink": null, "imgSrcMobile": null, "externalLinkMobile": null, "typeUpload": "EXIST", "imageFit": "fill", "shadow": 0, "width": "100%", "height": "auto", "border": {"style": "none", "color": "#212D6E", "thickness": 0, "radius": 0}, "backgroundType": "", "borderStyle": "none", "contentAlign": "start", "justifyContent": "center", "align": "center", "colWidths": [6], "gap": 4, "isBreakLine": false, "hide": false}}, "socialColumn": {"desktop": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "text": "Text", "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "imgSrc": null, "externalLink": null, "imgSrcMobile": null, "externalLinkMobile": null, "typeUpload": "EXIST", "imageFit": "fill", "shadow": 0, "width": "100%", "height": "auto", "border": {"style": "none", "color": "#212D6E", "thickness": 0, "radius": 0}, "backgroundType": "", "borderStyle": "none", "contentAlign": "start", "justifyContent": "center", "align": "center", "colWidths": [12], "gap": 4, "isBreakLine": false, "hide": false}, "tablet": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "text": "Text", "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "imgSrc": null, "externalLink": null, "imgSrcMobile": null, "externalLinkMobile": null, "typeUpload": "EXIST", "imageFit": "fill", "shadow": 0, "width": "100%", "height": "auto", "border": {"style": "none", "color": "#212D6E", "thickness": 0, "radius": 0}, "backgroundType": "", "borderStyle": "none", "contentAlign": "start", "justifyContent": "center", "align": "center", "colWidths": [12], "gap": 4, "isBreakLine": false, "hide": false}, "mobile": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "text": "Text", "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "imgSrc": null, "externalLink": null, "imgSrcMobile": null, "externalLinkMobile": null, "typeUpload": "EXIST", "imageFit": "fill", "shadow": 0, "width": "100%", "height": "auto", "border": {"style": "none", "color": "#212D6E", "thickness": 0, "radius": 0}, "backgroundType": "", "borderStyle": "none", "contentAlign": "start", "justifyContent": "center", "align": "center", "colWidths": [6], "gap": 4, "isBreakLine": false, "hide": false}}, "copyrightColumn": {"desktop": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "text": "Text", "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "imgSrc": null, "externalLink": null, "imgSrcMobile": null, "externalLinkMobile": null, "typeUpload": "EXIST", "imageFit": "fill", "shadow": 0, "width": "100%", "height": "auto", "border": {"style": "none", "color": "#212D6E", "thickness": 0, "radius": 0}, "backgroundType": "", "borderStyle": "none", "contentAlign": "start", "justifyContent": "center", "align": "center", "colWidths": [12], "gap": 4, "isBreakLine": false, "hide": false}, "tablet": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "text": "Text", "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "imgSrc": null, "externalLink": null, "imgSrcMobile": null, "externalLinkMobile": null, "typeUpload": "EXIST", "imageFit": "fill", "shadow": 0, "width": "100%", "height": "auto", "border": {"style": "none", "color": "#212D6E", "thickness": 0, "radius": 0}, "backgroundType": "", "borderStyle": "none", "contentAlign": "start", "justifyContent": "center", "align": "center", "colWidths": [12], "gap": 4, "isBreakLine": false, "hide": false}, "mobile": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "text": "Text", "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "imgSrc": null, "externalLink": null, "imgSrcMobile": null, "externalLinkMobile": null, "typeUpload": "EXIST", "imageFit": "fill", "shadow": 0, "width": "100%", "height": "auto", "border": {"style": "none", "color": "#212D6E", "thickness": 0, "radius": 0}, "backgroundType": "", "borderStyle": "none", "contentAlign": "start", "justifyContent": "center", "align": "center", "colWidths": [6], "gap": 4, "isBreakLine": false, "hide": false}}, "logosColumn": {"desktop": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "text": "Text", "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "imgSrc": null, "externalLink": null, "imgSrcMobile": null, "externalLinkMobile": null, "typeUpload": "EXIST", "imageFit": "fill", "shadow": 0, "width": "100%", "height": "auto", "border": {"style": "none", "color": "#212D6E", "thickness": 0, "radius": 0}, "backgroundType": "", "borderStyle": "none", "contentAlign": "start", "justifyContent": "center", "align": "center", "colWidths": [12], "gap": 4, "isBreakLine": false, "hide": false}, "tablet": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "text": "Text", "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "imgSrc": null, "externalLink": null, "imgSrcMobile": null, "externalLinkMobile": null, "typeUpload": "EXIST", "imageFit": "fill", "shadow": 0, "width": "100%", "height": "auto", "border": {"style": "none", "color": "#212D6E", "thickness": 0, "radius": 0}, "backgroundType": "", "borderStyle": "none", "contentAlign": "start", "justifyContent": "center", "align": "center", "colWidths": [12], "gap": 4, "isBreakLine": false, "hide": false}, "mobile": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "text": "Text", "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "imgSrc": null, "externalLink": null, "imgSrcMobile": null, "externalLinkMobile": null, "typeUpload": "EXIST", "imageFit": "fill", "shadow": 0, "width": "100%", "height": "auto", "border": {"style": "none", "color": "#212D6E", "thickness": 0, "radius": 0}, "backgroundType": "", "borderStyle": "none", "contentAlign": "start", "justifyContent": "center", "align": "center", "colWidths": [6], "gap": 4, "isBreakLine": false, "hide": false}}, "expertName": {"desktop": {"text": "<PERSON><PERSON><PERSON><PERSON>", "fontSize": 18, "fontWeight": 700, "color": "#ffffff"}, "tablet": {"text": "<PERSON><PERSON><PERSON><PERSON>", "fontSize": 18, "fontWeight": 700, "color": "#ffffff"}, "mobile": {"text": "<PERSON><PERSON><PERSON><PERSON>", "fontSize": 18, "fontWeight": 700, "color": "#ffffff", "textAlign": "center"}}, "description": {"desktop": {"text": "<PERSON><PERSON><PERSON> tác bán hàng ch<PERSON>h thức của oneSME by VNPT. <PERSON> kết mang đến dịch vụ và hỗ trợ tốt nhất.", "fontSize": 16, "fontWeight": 400, "color": "#9ca3af"}, "tablet": {"text": "<PERSON><PERSON><PERSON> tác bán hàng ch<PERSON>h thức của oneSME by VNPT. <PERSON> kết mang đến dịch vụ và hỗ trợ tốt nhất.", "fontSize": 16, "fontWeight": 400, "color": "#9ca3af"}, "mobile": {"text": "<PERSON><PERSON><PERSON> tác bán hàng ch<PERSON>h thức của oneSME by VNPT. <PERSON> kết mang đến dịch vụ và hỗ trợ tốt nhất.", "fontSize": 14, "fontWeight": 400, "color": "#9ca3af", "textAlign": "center"}}, "linksTitle": {"desktop": {"text": "<PERSON><PERSON><PERSON>h", "fontSize": 18, "fontWeight": 700, "color": "#ffffff"}, "tablet": {"text": "<PERSON><PERSON><PERSON>h", "fontSize": 18, "fontWeight": 700, "color": "#ffffff"}, "mobile": {"text": "<PERSON><PERSON><PERSON>h", "fontSize": 18, "fontWeight": 700, "color": "#ffffff", "textAlign": "center"}}, "aboutLink": {"desktop": {"text": "<PERSON><PERSON> chúng tôi", "fontSize": 16, "fontWeight": 400, "color": "#9ca3af"}, "tablet": {"text": "<PERSON><PERSON> chúng tôi", "fontSize": 16, "fontWeight": 400, "color": "#9ca3af"}, "mobile": {"text": "<PERSON><PERSON> chúng tôi", "fontSize": 14, "fontWeight": 400, "color": "#9ca3af", "textAlign": "center"}}, "servicesLink": {"desktop": {"text": "<PERSON><PERSON><PERSON> v<PERSON>", "fontSize": 16, "fontWeight": 400, "color": "#9ca3af", "margin": {"top": 4, "bottom": 0}}, "tablet": {"text": "<PERSON><PERSON><PERSON> v<PERSON>", "fontSize": 16, "fontWeight": 400, "color": "#9ca3af", "margin": {"top": 4, "bottom": 0}}, "mobile": {"text": "<PERSON><PERSON><PERSON> v<PERSON>", "fontSize": 14, "fontWeight": 400, "color": "#9ca3af", "textAlign": "center", "margin": {"top": 4, "bottom": 0}}}, "policyLink": {"desktop": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 16, "fontWeight": 400, "color": "#9ca3af", "margin": {"top": 4, "bottom": 0}}, "tablet": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 16, "fontWeight": 400, "color": "#9ca3af", "margin": {"top": 4, "bottom": 0}}, "mobile": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 14, "fontWeight": 400, "color": "#9ca3af", "textAlign": "center", "margin": {"top": 4, "bottom": 0}}}, "socialTitle": {"desktop": {"text": "<PERSON><PERSON><PERSON> n<PERSON>i với tôi", "fontSize": 18, "fontWeight": 700, "color": "#ffffff"}, "tablet": {"text": "<PERSON><PERSON><PERSON> n<PERSON>i với tôi", "fontSize": 18, "fontWeight": 700, "color": "#ffffff"}, "mobile": {"text": "<PERSON><PERSON><PERSON> n<PERSON>i với tôi", "fontSize": 18, "fontWeight": 700, "color": "#ffffff", "textAlign": "center"}}, "socialMediaLinks": {"desktop": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "backgroundColor": "#ffffff", "text": "Text", "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "size": 24, "iconAlign": "start", "socialList": [{"name": "Facebook", "icon": "FacebookIcon2", "iconColor": "#9CA3AF", "url": "", "displayType": "ICON"}, {"name": "Email", "icon": "MailFilled", "iconColor": "#9CA3AF", "url": "", "displayType": "ICON"}]}, "tablet": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "backgroundColor": "#ffffff", "text": "Text", "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "size": 24, "iconAlign": "start", "socialList": [{"name": "Facebook", "icon": "FacebookIcon2", "iconColor": "#9CA3AF", "url": "", "displayType": "ICON"}, {"name": "Email", "icon": "MailFilled", "iconColor": "#9CA3AF", "url": "", "displayType": "ICON"}]}, "mobile": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "backgroundColor": "#ffffff", "text": "Text", "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "size": 24, "iconAlign": "start", "socialList": [{"name": "Facebook", "icon": "FacebookIcon2", "iconColor": "#9CA3AF", "url": "", "displayType": "ICON"}, {"name": "Email", "icon": "MailFilled", "iconColor": "#9CA3AF", "url": "", "displayType": "ICON"}]}}, "copyrightText": {"desktop": {"text": "© 2025. <PERSON><PERSON> tư vấn c<PERSON><PERSON>.", "fontSize": 14, "fontWeight": 400, "color": "#6b7280"}, "tablet": {"text": "© 2025. <PERSON><PERSON> tư vấn c<PERSON><PERSON>.", "fontSize": 14, "fontWeight": 400, "color": "#6b7280"}, "mobile": {"text": "© 2025. <PERSON><PERSON> tư vấn c<PERSON><PERSON>.", "fontSize": 14, "fontWeight": 400, "color": "#6b7280", "textAlign": "center"}}, "onesmeLogo": {"desktop": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "backgroundColor": "#ffffff", "text": "Text", "padding": {"top": 0, "right": 24, "bottom": 0, "left": 24}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "hide": false, "link": "", "alt": "", "imgSrc": "https://onesme.vn/assets/images/logo-footer.svg", "imgSrcMobile": "", "externalLink": "", "externalLinkMobile": "", "typeUpload": "EXIST", "imageFit": "fit", "align": "start", "border": {"style": "none", "color": "", "thickness": 0, "radius": 0}, "width": "", "height": "36px", "hasOverlay": false, "imgSrcFallBack": "/assets/images/noImage.svg"}, "tablet": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "backgroundColor": "#ffffff", "text": "Text", "padding": {"top": 0, "right": 24, "bottom": 0, "left": 24}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "hide": false, "link": "", "alt": "", "imgSrc": "https://onesme.vn/assets/images/logo-footer.svg", "imgSrcMobile": "", "externalLink": "", "externalLinkMobile": "", "typeUpload": "EXIST", "imageFit": "fit", "align": "start", "border": {"style": "none", "color": "", "thickness": 0, "radius": 0}, "width": "", "height": "36px", "hasOverlay": false, "imgSrcFallBack": "/assets/images/noImage.svg"}, "mobile": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "backgroundColor": "#ffffff", "text": "Text", "padding": {"top": 0, "right": 24, "bottom": 0, "left": 24}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "hide": false, "link": "", "alt": "", "imgSrc": "https://onesme.vn/assets/images/logo-footer.svg", "imgSrcMobile": "", "externalLink": "", "externalLinkMobile": "", "typeUpload": "EXIST", "imageFit": "fit", "align": "start", "border": {"style": "none", "color": "", "thickness": 0, "radius": 0}, "width": "", "height": "36px", "hasOverlay": false, "imgSrcFallBack": "/assets/images/noImage.svg"}}, "vnptLogo": {"desktop": {"imgSrc": "https://www.vnpt.com.vn/img/logo-vnpt.svg"}, "tablet": {"imgSrc": "https://www.vnpt.com.vn/img/logo-vnpt.svg"}, "mobile": {"imgSrcMobile": "https://www.vnpt.com.vn/img/logo-vnpt.svg"}}, "spacer1": {"desktop": {"height": 16}, "tablet": {"height": 16}, "mobile": {"height": 16}}, "spacer2": {"desktop": {"height": 16}, "tablet": {"height": 16}, "mobile": {"height": 16}}, "spacer3": {"desktop": {"height": 16}, "tablet": {"height": 16}, "mobile": {"height": 16}}, "id": "footer"}, "displayName": "FooterBuilder", "custom": {}, "parent": "Kk9Y7Diyq1", "hidden": false, "nodes": [], "linkedNodes": {"container": "OOEo5T8WEN"}}, "8K_9pLRK0G": {"type": {"resolvedName": "Image"}, "isCanvas": true, "props": {"desktop": {"imgSrc": "/assets/images/logo.svg", "width": "auto", "height": 40, "className": "h-8 md:h-10"}, "tablet": {"imgSrc": "/assets/images/logo.svg", "width": "auto", "height": 40, "className": "h-8 md:h-10"}, "mobile": {"imgSrcMobile": "/assets/images/logo.svg", "width": "auto", "height": 32, "className": "h-8 md:h-10"}}, "displayName": "Ảnh", "custom": {}, "parent": "Vws6ymlE0K", "hidden": false, "nodes": [], "linkedNodes": {}}, "y45_ZiWU8A": {"type": {"resolvedName": "Image"}, "isCanvas": true, "props": {"desktop": {"imgSrc": "https://images.unsplash.com/photo-1599566150163-29194dcaad36?q=80&w=1887&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D", "width": 48, "height": 48, "border": {"radius": 24}, "imageFit": "fill", "align": "center", "className": "w-12 h-12 rounded-full object-cover"}, "tablet": {"imgSrc": "https://images.unsplash.com/photo-1599566150163-29194dcaad36?q=80&w=1887&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D", "width": 48, "height": 48, "border": {"radius": 24}, "imageFit": "fill", "align": "center", "className": "w-12 h-12 rounded-full object-cover"}, "mobile": {"imgSrc": "https://images.unsplash.com/photo-1599566150163-29194dcaad36?q=80&w=1887&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D", "width": 40, "height": 40, "border": {"radius": 20}, "imageFit": "fill", "align": "center", "className": "w-10 h-10 rounded-full object-cover"}, "className": "focus:outline-none"}, "displayName": "Ảnh", "custom": {}, "parent": "Vws6ymlE0K", "hidden": false, "nodes": [], "linkedNodes": {}}, "FO1bQkz5s_": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON><PERSON><PERSON>", "fontsize": 14, "fontWeight": 700, "color": "#00529c", "textAlign": "left", "className": "font-bold text-lg text-primary-blue"}, "tablet": {"text": "<PERSON><PERSON><PERSON><PERSON>", "fontsize": 14, "fontWeight": 700, "color": "#00529c", "textAlign": "left", "className": "font-bold text-lg text-primary-blue"}, "mobile": {"text": "<PERSON><PERSON><PERSON><PERSON>", "fontSize": 16, "fontWeight": 700, "color": "#00529c", "textAlign": "left", "className": "font-bold text-base text-primary-blue"}, "className": "focus:outline-none"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "Vws6ymlE0K", "hidden": false, "nodes": [], "linkedNodes": {}}, "V_yz_l1rh1": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "Chuyên gia Tổ Ấm Số", "fontSize": 14, "fontWeight": 400, "color": "#6b7280", "textAlign": "left", "className": "text-sm text-gray-500"}, "tablet": {"text": "Chuyên gia Tổ Ấm Số", "fontSize": 14, "fontWeight": 400, "color": "#6b7280", "textAlign": "left", "className": "text-sm text-gray-500"}, "mobile": {"text": "Chuyên gia Tổ Ấm Số", "fontSize": 12, "fontWeight": 400, "color": "#6b7280", "textAlign": "left", "className": "text-xs text-gray-500"}, "className": "focus:outline-none"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "Vws6ymlE0K", "hidden": false, "nodes": [], "linkedNodes": {}}, "ANDR_0VCC1": {"type": {"resolvedName": "SocialMedia"}, "isCanvas": true, "props": {"desktop": {"fontSize": 14, "fontWeight": 400, "textAlign": "left", "color": "#000000", "backgroundColor": "#ffffff", "text": "Text", "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "size": 14, "iconAlign": "start", "socialList": [{"name": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "icon": "PhoneFilled", "iconColor": "#6b7280", "url": "", "displayType": "NAME"}, {"name": "Email", "icon": "MailFilled", "iconColor": "#6b7280", "url": "", "displayType": "NAME"}], "fontFamily": "Inter, sans-serif"}, "tablet": {"fontSize": 14, "fontWeight": 400, "textAlign": "left", "color": "#000000", "backgroundColor": "#ffffff", "text": "Text", "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "size": 14, "iconAlign": "start", "socialList": [{"name": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "icon": "PhoneFilled", "iconColor": "#6b7280", "url": "", "displayType": "NAME"}, {"name": "Email", "icon": "MailFilled", "iconColor": "#6b7280", "url": "", "displayType": "NAME"}], "fontFamily": "Inter, sans-serif"}, "mobile": {"fontSize": 14, "fontWeight": 400, "textAlign": "left", "color": "#000000", "backgroundColor": "#ffffff", "text": "Text", "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "size": 14, "iconAlign": "start", "socialList": [{"name": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "icon": "PhoneFilled", "iconColor": "#6b7280", "url": "", "displayType": "ICON"}, {"name": "Email", "icon": "MailFilled", "iconColor": "#6b7280", "url": "", "displayType": "ICON"}], "fontFamily": "Inter, sans-serif"}}, "displayName": "Mạng xã hội", "custom": {}, "parent": "Vws6ymlE0K", "hidden": false, "nodes": [], "linkedNodes": {}}, "AeIhaSmDEE": {"type": {"resolvedName": "SocialMedia"}, "isCanvas": true, "props": {"desktop": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "backgroundColor": "#ffffff", "text": "Text", "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "size": 14, "iconAlign": "start", "socialList": [{"name": "Facebook", "icon": "FacebookIcon2", "iconColor": "#6b7280", "url": "https://facebook.com", "displayType": "ICON"}]}, "tablet": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "backgroundColor": "#ffffff", "text": "Text", "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "size": 14, "iconAlign": "start", "socialList": [{"name": "Facebook", "icon": "FacebookIcon2", "iconColor": "#6b7280", "url": "https://facebook.com", "displayType": "ICON"}]}, "mobile": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "backgroundColor": "#ffffff", "text": "Text", "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "size": 16, "iconAlign": "start", "socialList": [{"name": "Facebook", "icon": "FacebookIcon2", "iconColor": "#6b7280", "url": "https://facebook.com", "displayType": "ICON"}]}}, "displayName": "Mạng xã hội", "custom": {}, "parent": "Vws6ymlE0K", "hidden": false, "nodes": [], "linkedNodes": {}}, "TmQBQYODVh": {"type": {"resolvedName": "Container"}, "isCanvas": true, "props": {"flexDirection": "column", "alignItems": "flex-start", "justifyContent": "flex-start", "padding": ["0", "0", "0", "0"], "margin": ["0", "0", "0", "0"], "background": "#FFFFFF", "color": {"r": 0, "g": 0, "b": 0, "a": 1}, "shadow": 0, "radius": 0, "width": "100%", "height": "auto"}, "displayName": "Container", "custom": {}, "parent": "UPaMThe5xN", "hidden": false, "nodes": ["bGAcKHSl9x"], "linkedNodes": {}}, "bGAcKHSl9x": {"type": {"resolvedName": "Row"}, "isCanvas": true, "props": {"desktop": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 64, "bottom": 64, "left": 16, "right": 16}, "colWidths": [4, 8], "gap": 20, "height": "auto", "alignItems": "center"}, "tablet": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 64, "bottom": 64, "left": 16, "right": 16}, "colWidths": [4, 8], "gap": 20, "height": "auto", "alignItems": "center"}, "mobile": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 48, "bottom": 48, "left": 16, "right": 16}, "isBreakLine": true, "gap": 20, "height": "auto", "alignItems": "center"}, "id": "expert-intro-row"}, "displayName": "Row", "custom": {}, "parent": "TmQBQYODVh", "hidden": false, "nodes": ["sPRoa_4FI2", "AMklHOUpb-"], "linkedNodes": {}}, "sPRoa_4FI2": {"type": {"resolvedName": "Column"}, "isCanvas": true, "props": {"desktop": {"display": "flex", "justifyContent": "center", "alignItems": "center", "contentAlign": "start"}, "tablet": {"display": "flex", "justifyContent": "center", "alignItems": "center", "contentAlign": "start"}, "mobile": {"display": "flex", "justifyContent": "center", "alignItems": "center", "contentAlign": "start"}, "id": "expert-image-column"}, "displayName": "Column", "custom": {}, "parent": "bGAcKHSl9x", "hidden": false, "nodes": ["3HgJvqjtNu"], "linkedNodes": {}}, "3HgJvqjtNu": {"type": {"resolvedName": "Image"}, "isCanvas": true, "props": {"desktop": {"imgSrc": "https://images.unsplash.com/photo-1557862921-37829c790f19?q=80&w=2071&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D", "alt": "<PERSON><PERSON><PERSON><PERSON> gia <PERSON>", "width": 480, "height": 320, "border": {"radius": 8}, "align": "center", "imageFit": "fill"}, "tablet": {"imgSrc": "https://images.unsplash.com/photo-1557862921-37829c790f19?q=80&w=2071&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D", "alt": "<PERSON><PERSON><PERSON><PERSON> gia <PERSON>", "width": 250, "height": 330, "border": {"radius": 8}, "align": "center", "imageFit": "fill"}, "mobile": {"imgSrc": "https://images.unsplash.com/photo-1557862921-37829c790f19?q=80&w=2071&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D", "alt": "<PERSON><PERSON><PERSON><PERSON> gia <PERSON>", "width": 200, "height": 260, "border": {"radius": 8}, "align": "center", "imageFit": "fill"}, "id": "expert-image"}, "displayName": "Ảnh", "custom": {}, "parent": "sPRoa_4FI2", "hidden": false, "nodes": [], "linkedNodes": {}}, "AMklHOUpb-": {"type": {"resolvedName": "Column"}, "isCanvas": true, "props": {"desktop": {"display": "flex", "justifyContent": "center", "alignItems": "center", "contentAlign": "start"}, "tablet": {"display": "flex", "justifyContent": "center", "alignItems": "center", "contentAlign": "start"}, "mobile": {"display": "flex", "justifyContent": "center", "alignItems": "center", "contentAlign": "start"}, "id": "expert-content-column"}, "displayName": "Column", "custom": {}, "parent": "bGAcKHSl9x", "hidden": false, "nodes": ["VnGZEAMY1G", "EWEiK6ByiG", "6Jl_Xrt-s8"], "linkedNodes": {}}, "VnGZEAMY1G": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "Chuyên gia tư vấ<PERSON>", "fontSize": 30, "fontWeight": 700, "color": "#00529c"}, "tablet": {"text": "Chuyên gia tư vấ<PERSON>", "fontSize": 28, "fontWeight": 700, "color": "#00529c"}, "mobile": {"text": "Chuyên gia tư vấ<PERSON>", "fontSize": 24, "fontWeight": 700, "color": "#00529c"}, "id": "expert-title"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "AMklHOUpb-", "hidden": false, "nodes": [], "linkedNodes": {}}, "EWEiK6ByiG": {"type": {"resolvedName": "Spacer"}, "isCanvas": true, "props": {"desktop": {"height": 16}, "tablet": {"height": 16}, "mobile": {"height": 16}, "id": "expert-spacer"}, "displayName": "<PERSON><PERSON><PERSON><PERSON> trắng", "custom": {}, "parent": "AMklHOUpb-", "hidden": false, "nodes": [], "linkedNodes": {}}, "6Jl_Xrt-s8": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "V<PERSON>i mong muốn mang đến những giải pháp công nghệ tốt nhất cho mỗi gia đình Việt, tôi luôn lắng nghe và tư vấn tận tình để giúp bạn xây dựng một \"tổ ấm số\" hiện đại, an toàn và tiện nghi. Hãy để tôi đồng hành cùng gia đình bạn!", "fontSize": 16, "fontWeight": 400, "color": "#6b7280"}, "tablet": {"text": "V<PERSON>i mong muốn mang đến những giải pháp công nghệ tốt nhất cho mỗi gia đình Việt, tôi luôn lắng nghe và tư vấn tận tình để giúp bạn xây dựng một \"tổ ấm số\" hiện đại, an toàn và tiện nghi. Hãy để tôi đồng hành cùng gia đình bạn!", "fontSize": 16, "fontWeight": 400, "color": "#6b7280"}, "mobile": {"text": "V<PERSON>i mong muốn mang đến những giải pháp công nghệ tốt nhất cho mỗi gia đình Việt, tôi luôn lắng nghe và tư vấn tận tình để giúp bạn xây dựng một \"tổ ấm số\" hiện đại, an toàn và tiện nghi. Hãy để tôi đồng hành cùng gia đình bạn!", "fontSize": 14, "fontWeight": 400, "color": "#6b7280"}, "id": "expert-description"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "AMklHOUpb-", "hidden": false, "nodes": [], "linkedNodes": {}}, "I-Yf5CxmiQ": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "FLASH SALE", "fontSize": 18, "fontWeight": 700, "color": "#dc2626", "textAlign": "left", "className": "font-bold text-red-600 text-lg"}, "tablet": {"text": "FLASH SALE", "fontSize": 18, "fontWeight": 700, "color": "#dc2626", "textAlign": "left", "className": "font-bold text-red-600 text-lg"}, "mobile": {"text": "FLASH SALE", "fontSize": 16, "fontWeight": 700, "color": "#dc2626", "textAlign": "left", "className": "font-bold text-red-600 text-lg"}}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "Mkofn5BR50", "hidden": false, "nodes": [], "linkedNodes": {}}, "SibpZM9t_K": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON> an ninh chỉ từ ", "fontSize": 14, "fontWeight": 400, "color": "#374151", "textAlign": "left", "className": "inline"}, "tablet": {"text": "<PERSON> an ninh chỉ từ ", "fontSize": 14, "fontWeight": 400, "color": "#374151", "textAlign": "left", "className": "inline"}, "mobile": {"text": "<PERSON> an ninh chỉ từ ", "fontSize": 12, "fontWeight": 400, "color": "#374151", "textAlign": "left", "className": "inline"}}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "Mkofn5BR50", "hidden": false, "nodes": [], "linkedNodes": {}}, "ZhAlW_3Hk6": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "499.000đ", "fontSize": 14, "fontWeight": 700, "color": "#dc2626", "textAlign": "left", "className": "inline text-red-600 font-bold"}, "tablet": {"text": "499.000đ", "fontSize": 14, "fontWeight": 700, "color": "#dc2626", "textAlign": "left", "className": "inline text-red-600 font-bold"}, "mobile": {"text": "499.000đ", "fontSize": 12, "fontWeight": 700, "color": "#dc2626", "textAlign": "left", "className": "inline text-red-600 font-bold"}}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "Mkofn5BR50", "hidden": false, "nodes": [], "linkedNodes": {}}, "lOBmcMwd6w": {"type": {"resolvedName": "<PERSON><PERSON>"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 14, "fontWeight": 700, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#dc2626", "radius": 8, "size": "large", "width": "auto", "padding": {"top": 8, "right": 24, "bottom": 8, "left": 24}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}}, "tablet": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 14, "fontWeight": 700, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#dc2626", "radius": 8, "size": "large", "width": "auto", "padding": {"top": 8, "right": 24, "bottom": 8, "left": 24}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}}, "mobile": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 12, "fontWeight": 700, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#dc2626", "radius": 8, "size": "large", "width": "100%", "padding": {"top": 8, "right": 24, "bottom": 8, "left": 24}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}}}, "displayName": "<PERSON><PERSON>", "custom": {}, "parent": "Mkofn5BR50", "hidden": false, "nodes": [], "linkedNodes": {}}, "BBk5wRvCcr": {"type": {"resolvedName": "Container"}, "isCanvas": true, "props": {"flexDirection": "column", "alignItems": "flex-start", "justifyContent": "flex-start", "padding": ["0", "0", "0", "0"], "margin": ["0", "0", "0", "0"], "background": "#FFFFFF", "color": {"r": 0, "g": 0, "b": 0, "a": 1}, "shadow": 0, "radius": 0, "width": "100%", "height": "auto"}, "displayName": "Container", "custom": {}, "parent": "VYfMiRpw7N", "hidden": false, "nodes": ["kV08LZT-0P"], "linkedNodes": {}}, "kV08LZT-0P": {"type": {"resolvedName": "Row"}, "isCanvas": true, "props": {"desktop": {"backgroundColor": "#F0F5FA", "backgroundType": "COLOR", "padding": {"top": 64, "bottom": 40, "left": 16, "right": 16}, "colWidths": [12], "gap": 20, "height": "auto", "alignItems": "center", "justifyContent": "center", "width": "100%"}, "tablet": {"backgroundColor": "#FOF5FA", "backgroundType": "COLOR", "padding": {"top": 48, "bottom": 24, "left": 16, "right": 16}, "colWidths": [12], "gap": 20, "height": "auto", "alignItems": "center", "justifyContent": "center", "width": "100%"}, "mobile": {"backgroundColor": "#F0F5FA", "backgroundType": "COLOR", "padding": {"top": 32, "bottom": 8, "left": 16, "right": 16}, "colWidths": [12], "gap": 16, "height": "auto", "alignItems": "center", "justifyContent": "center", "width": "100%"}, "id": "promotions-container"}, "displayName": "Row", "custom": {}, "parent": "BBk5wRvCcr", "hidden": false, "nodes": ["2YMi0JHvmD", "oxS1CqmAOT"], "linkedNodes": {}}, "2YMi0JHvmD": {"type": {"resolvedName": "Column"}, "isCanvas": true, "props": {"desktop": {"display": "flex", "justifyContent": "center", "alignItems": "center", "flexDirection": "column", "margin": {"top": 40, "bottom": 40, "left": 0, "right": 0}, "gap": 0}, "tablet": {"display": "flex", "justifyContent": "center", "alignItems": "center", "flexDirection": "column", "margin": {"top": 40, "bottom": 40, "left": 0, "right": 0}, "gap": 0}, "mobile": {"display": "flex", "justifyContent": "center", "alignItems": "center", "flexDirection": "column", "margin": {"top": 40, "bottom": 40, "left": 0, "right": 0}, "gap": 0}, "id": "promotions-title-section"}, "displayName": "Column", "custom": {}, "parent": "kV08LZT-0P", "hidden": false, "nodes": ["53gjf<PERSON><PERSON><PERSON>"], "linkedNodes": {}}, "53gjfGAluz": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "Ưu đãi tháng 7 cho Gia Đình", "fontSize": 32, "fontWeight": 700, "color": "#004E95", "textAlign": "center"}, "tablet": {"text": "Ưu đãi tháng 7 cho Gia Đình", "fontSize": 28, "fontWeight": 700, "color": "#004E95", "textAlign": "center"}, "mobile": {"text": "Ưu đãi tháng 7 cho Gia Đình", "fontSize": 24, "fontWeight": 700, "color": "#004E95", "textAlign": "center"}, "id": "promotions-title"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "2YMi0JHvmD", "hidden": false, "nodes": [], "linkedNodes": {}}, "oxS1CqmAOT": {"type": {"resolvedName": "Container"}, "isCanvas": true, "props": {"flexDirection": "column", "alignItems": "flex-start", "justifyContent": "flex-start", "padding": ["0", "0", "0", "0"], "margin": ["0", "0", "0", "0"], "background": "#FFFFFF", "color": {"r": 0, "g": 0, "b": 0, "a": 1}, "shadow": 0, "radius": 0, "width": "100%", "height": "auto", "id": "container-promotions-cards"}, "displayName": "Container", "custom": {}, "parent": "kV08LZT-0P", "hidden": false, "nodes": ["J0El4A2RAf"], "linkedNodes": {}}, "J0El4A2RAf": {"type": {"resolvedName": "Row"}, "isCanvas": true, "props": {"desktop": {"colWidths": [4, 4, 4], "padding": {"top": 0, "bottom": 24, "left": 0, "right": 0}, "backgroundColor": "#F0F5FA", "backgroundType": "COLOR", "gap": 6, "height": "auto", "alignItems": "stretch", "justifyContent": "center", "width": "100%"}, "tablet": {"colWidths": [6, 6], "padding": {"top": 0, "bottom": 16, "left": 0, "right": 0}, "backgroundColor": "#F0F5FA", "backgroundType": "COLOR", "gap": 6, "height": "auto", "alignItems": "stretch", "justifyContent": "center", "width": "100%", "isBreakLine": true}, "mobile": {"colWidths": [12], "padding": {"top": 0, "bottom": 16, "left": 0, "right": 0}, "backgroundColor": "#F0F5FA", "backgroundType": "COLOR", "gap": 6, "height": "auto", "alignItems": "stretch", "justifyContent": "center", "width": "100%", "isBreakLine": true}, "id": "promotions-cards-row"}, "displayName": "Row", "custom": {"displayName": "Promotions Card Section"}, "parent": "oxS1CqmAOT", "hidden": false, "nodes": ["Hsh8846ggl", "FRaB3yNzL2", "jYIlfTPIsT"], "linkedNodes": {}}, "Hsh8846ggl": {"type": {"resolvedName": "Column"}, "isCanvas": true, "props": {"desktop": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "width": "100%", "border": {"radius": 8, "width": 1, "color": "#e5e7eb", "style": "solid"}, "shadow": 8}, "tablet": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "width": "100%", "border": {"radius": 8, "width": 1, "color": "#e5e7eb", "style": "solid"}, "shadow": 8}, "mobile": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "width": "100%", "border": {"radius": 8, "width": 1, "color": "#e5e7eb", "style": "solid"}, "shadow": 8}, "id": "promotion-card-1"}, "displayName": "Column", "custom": {}, "parent": "J0El4A2RAf", "hidden": false, "nodes": ["t0CIenWfL1"], "linkedNodes": {}}, "t0CIenWfL1": {"type": "div", "isCanvas": false, "props": {"className": "flex h-full flex-col overflow-hidden rounded-lg bg-white shadow-lg"}, "displayName": "div", "custom": {}, "parent": "Hsh8846ggl", "hidden": false, "nodes": ["rpiSZDagaM", "apcjuAifLh"], "linkedNodes": {}}, "rpiSZDagaM": {"type": "div", "isCanvas": false, "props": {"className": "overflow-hidden rounded-t-lg bg-white"}, "displayName": "div", "custom": {}, "parent": "t0CIenWfL1", "hidden": false, "nodes": ["WA0U470-IG"], "linkedNodes": {}}, "WA0U470-IG": {"type": {"resolvedName": "Image"}, "isCanvas": true, "props": {"desktop": {"imgSrc": "https://placehold.co/600x300/E3F2FD/0277BD?text=Mua+6+Tặng+2", "width": "100%", "height": 128, "border": {"radius": 0, "width": 0, "color": "transparent", "style": "none"}, "imageFit": "fill", "align": "center"}, "tablet": {"imgSrc": "https://placehold.co/600x300/E3F2FD/0277BD?text=Mua+6+Tặng+2", "width": "100%", "height": 128, "border": {"radius": 0, "width": 0, "color": "transparent", "style": "none"}, "imageFit": "fill", "align": "center"}, "mobile": {"imgSrcMobile": "https://placehold.co/600x300/E3F2FD/0277BD?text=Mua+6+Tặng+2", "width": "100%", "height": 128, "border": {"radius": 0, "width": 0, "color": "transparent", "style": "none"}, "imageFit": "fill", "align": "center"}, "id": "card-1-image"}, "displayName": "Ảnh", "custom": {}, "parent": "rpiSZDagaM", "hidden": false, "nodes": [], "linkedNodes": {}}, "apcjuAifLh": {"type": {"resolvedName": "Column"}, "isCanvas": true, "props": {"desktop": {"padding": {"top": 16, "bottom": 16, "left": 16, "right": 16}, "gap": 8, "height": "auto", "alignItems": "stretch", "flexDirection": "column"}, "tablet": {"padding": {"top": 16, "bottom": 16, "left": 16, "right": 16}, "gap": 8, "height": "auto", "alignItems": "stretch", "flexDirection": "column"}, "mobile": {"padding": {"top": 16, "bottom": 16, "left": 16, "right": 16}, "gap": 8, "height": "auto", "alignItems": "stretch", "flexDirection": "column"}, "id": "card-1-content"}, "displayName": "Column", "custom": {}, "parent": "t0CIenWfL1", "hidden": false, "nodes": ["JSTOSSDRlT", "WylVLF0XjS", "MfOmv1W-w5", "SjW7O5qg7L", "ybGvAI5qZ4"], "linkedNodes": {}}, "JSTOSSDRlT": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "Mua 6 Tặng 2", "fontSize": 18, "fontWeight": 700, "color": "#0F1319"}, "tablet": {"text": "Mua 6 Tặng 2", "fontSize": 18, "fontWeight": 700, "color": "#0F1319"}, "mobile": {"text": "Mua 6 Tặng 2", "fontSize": 18, "fontWeight": 700, "color": "#0F1319"}, "id": "card-1-title"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "apcjuAifLh", "hidden": false, "nodes": [], "linkedNodes": {}}, "WylVLF0XjS": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON><PERSON> ký <PERSON> 6 tháng, <PERSON><PERSON><PERSON><PERSON> ngay 2 tháng cư<PERSON><PERSON> mi<PERSON>n ph<PERSON>.", "fontSize": 14, "fontWeight": 400, "color": "#6b7280"}, "tablet": {"text": "<PERSON><PERSON><PERSON> ký <PERSON> 6 tháng, <PERSON><PERSON><PERSON><PERSON> ngay 2 tháng cư<PERSON><PERSON> mi<PERSON>n ph<PERSON>.", "fontSize": 14, "fontWeight": 400, "color": "#6b7280"}, "mobile": {"text": "<PERSON><PERSON><PERSON> ký <PERSON> 6 tháng, <PERSON><PERSON><PERSON><PERSON> ngay 2 tháng cư<PERSON><PERSON> mi<PERSON>n ph<PERSON>.", "fontSize": 14, "fontWeight": 400, "color": "#6b7280"}, "id": "card-1-description"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "apcjuAifLh", "hidden": false, "nodes": [], "linkedNodes": {}}, "MfOmv1W-w5": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON>p dụng: <PERSON><PERSON><PERSON><PERSON> hàng mới.", "fontSize": 12, "fontWeight": 400, "color": "#9ca3af", "icon": "CheckCircleFilled", "iconColor": "#10b981", "hasIcon": true}, "tablet": {"text": "<PERSON>p dụng: <PERSON><PERSON><PERSON><PERSON> hàng mới.", "fontSize": 12, "fontWeight": 400, "color": "#9ca3af", "icon": "CheckCircleFilled", "iconColor": "#10b981", "hasIcon": true}, "mobile": {"text": "<PERSON>p dụng: <PERSON><PERSON><PERSON><PERSON> hàng mới.", "fontSize": 12, "fontWeight": 400, "color": "#9ca3af", "icon": "CheckCircleFilled", "iconColor": "#10b981", "hasIcon": true}, "id": "card-1-condition"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "apcjuAifLh", "hidden": false, "nodes": [], "linkedNodes": {}}, "SjW7O5qg7L": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "Th<PERSON>i gian: 01/07 - 31/07/2025.", "fontSize": 12, "fontWeight": 400, "color": "#9ca3af", "icon": "CalendarFilled", "iconColor": "#10b981", "hasIcon": true}, "tablet": {"text": "Th<PERSON>i gian: 01/07 - 31/07/2025.", "fontSize": 12, "fontWeight": 400, "color": "#9ca3af", "icon": "CalendarFilled", "iconColor": "#10b981", "hasIcon": true}, "mobile": {"text": "Th<PERSON>i gian: 01/07 - 31/07/2025.", "fontSize": 12, "fontWeight": 400, "color": "#9ca3af", "icon": "CalendarFilled", "iconColor": "#10b981", "hasIcon": true}, "id": "card-1-time"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "apcjuAifLh", "hidden": false, "nodes": [], "linkedNodes": {}}, "ybGvAI5qZ4": {"type": {"resolvedName": "Row"}, "isCanvas": true, "props": {"desktop": {"colWidths": [1, 1], "gap": 8, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 16, "bottom": 0, "left": 0, "right": 0}}, "tablet": {"colWidths": [1, 1], "gap": 8, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 16, "bottom": 0, "left": 0, "right": 0}}, "mobile": {"colWidths": [1, 1], "gap": 8, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 16, "bottom": 0, "left": 0, "right": 0}}, "id": "card-1-buttons"}, "displayName": "Row", "custom": {}, "parent": "apcjuAifLh", "hidden": false, "nodes": ["3yADASPgwq", "pkGqpcKikm"], "linkedNodes": {}}, "3yADASPgwq": {"type": {"resolvedName": "<PERSON><PERSON>"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "id": "card-1-detail-btn"}, "displayName": "<PERSON><PERSON>", "custom": {}, "parent": "ybGvAI5qZ4", "hidden": false, "nodes": [], "linkedNodes": {}}, "pkGqpcKikm": {"type": {"resolvedName": "<PERSON><PERSON>"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON>", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#ef9304", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON><PERSON>", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#ef9304", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON><PERSON>", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#ef9304", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "id": "card-1-apply-btn"}, "displayName": "<PERSON><PERSON>", "custom": {}, "parent": "ybGvAI5qZ4", "hidden": false, "nodes": [], "linkedNodes": {}}, "FRaB3yNzL2": {"type": {"resolvedName": "Column"}, "isCanvas": true, "props": {"desktop": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "width": "100%", "border": {"radius": 8, "width": 1, "color": "#e5e7eb", "style": "solid"}, "shadow": 8}, "tablet": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "width": "100%", "border": {"radius": 8, "width": 1, "color": "#e5e7eb", "style": "solid"}, "shadow": 8}, "mobile": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "width": "100%", "border": {"radius": 8, "width": 1, "color": "#e5e7eb", "style": "solid"}, "shadow": 8}, "id": "promotion-card-2"}, "displayName": "Column", "custom": {}, "parent": "J0El4A2RAf", "hidden": false, "nodes": ["1oIPrDBy9t"], "linkedNodes": {}}, "1oIPrDBy9t": {"type": "div", "isCanvas": false, "props": {"className": "flex h-full flex-col overflow-hidden rounded-lg bg-white shadow-lg"}, "displayName": "div", "custom": {}, "parent": "FRaB3yNzL2", "hidden": false, "nodes": ["VG0l2YCO0-", "Yrc__-hQUx"], "linkedNodes": {}}, "VG0l2YCO0-": {"type": "div", "isCanvas": false, "props": {"className": "overflow-hidden rounded-t-lg bg-white"}, "displayName": "div", "custom": {}, "parent": "1oIPrDBy9t", "hidden": false, "nodes": ["p36XiqhUeD"], "linkedNodes": {}}, "p36XiqhUeD": {"type": {"resolvedName": "Image"}, "isCanvas": true, "props": {"desktop": {"imgSrc": "https://placehold.co/600x300/FFF3E0/E65100?text=Tặng+Camera", "width": "100%", "height": 128, "border": {"radius": 0, "width": 0, "color": "transparent", "style": "none"}, "imageFit": "fill", "align": "center"}, "tablet": {"imgSrc": "https://placehold.co/600x300/FFF3E0/E65100?text=Tặng+Camera", "width": "100%", "height": 128, "border": {"radius": 0, "width": 0, "color": "transparent", "style": "none"}, "imageFit": "fill", "align": "center"}, "mobile": {"imgSrcMobile": "https://placehold.co/600x300/FFF3E0/E65100?text=Tặng+Camera", "width": "100%", "height": 128, "border": {"radius": 0, "width": 0, "color": "transparent", "style": "none"}, "imageFit": "fill", "align": "center"}, "id": "card-2-image"}, "displayName": "Ảnh", "custom": {}, "parent": "VG0l2YCO0-", "hidden": false, "nodes": [], "linkedNodes": {}}, "Yrc__-hQUx": {"type": {"resolvedName": "Column"}, "isCanvas": true, "props": {"desktop": {"padding": {"top": 16, "bottom": 16, "left": 16, "right": 16}, "gap": 8, "height": "auto", "alignItems": "stretch", "flexDirection": "column"}, "tablet": {"padding": {"top": 16, "bottom": 16, "left": 16, "right": 16}, "gap": 8, "height": "auto", "alignItems": "stretch", "flexDirection": "column"}, "mobile": {"padding": {"top": 16, "bottom": 16, "left": 16, "right": 16}, "gap": 8, "height": "auto", "alignItems": "stretch", "flexDirection": "column"}, "id": "card-2-content"}, "displayName": "Column", "custom": {}, "parent": "1oIPrDBy9t", "hidden": false, "nodes": ["pmsOYJ9yzU", "td_hp4_h8g", "iQUQEUYNJt", "qQ7LeUBtMV", "SIibGpxSsH"], "linkedNodes": {}}, "pmsOYJ9yzU": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "Lắp Internet Tặng Camera", "fontSize": 18, "fontWeight": 700, "color": "#0F1319"}, "tablet": {"text": "Lắp Internet Tặng Camera", "fontSize": 18, "fontWeight": 700, "color": "#0F1319"}, "mobile": {"text": "Lắp Internet Tặng Camera", "fontSize": 18, "fontWeight": 700, "color": "#0F1319"}, "id": "card-2-title"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "Yrc__-hQUx", "hidden": false, "nodes": [], "linkedNodes": {}}, "td_hp4_h8g": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON> bị miễn phí camera giám sát an toàn cho gia đình bạn.", "fontSize": 14, "fontWeight": 400, "color": "#6b7280"}, "tablet": {"text": "<PERSON><PERSON> bị miễn phí camera giám sát an toàn cho gia đình bạn.", "fontSize": 14, "fontWeight": 400, "color": "#6b7280"}, "mobile": {"text": "<PERSON><PERSON> bị miễn phí camera giám sát an toàn cho gia đình bạn.", "fontSize": 14, "fontWeight": 400, "color": "#6b7280"}, "id": "card-2-description"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "Yrc__-hQUx", "hidden": false, "nodes": [], "linkedNodes": {}}, "iQUQEUYNJt": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON> dụng: <PERSON><PERSON><PERSON> Home Mesh 3+.", "fontSize": 12, "fontWeight": 400, "color": "#9ca3af", "icon": "CheckCircleFilled", "iconColor": "#10b981", "hasIcon": true}, "tablet": {"text": "<PERSON><PERSON> dụng: <PERSON><PERSON><PERSON> Home Mesh 3+.", "fontSize": 12, "fontWeight": 400, "color": "#9ca3af", "icon": "CheckCircleFilled", "iconColor": "#10b981", "hasIcon": true}, "mobile": {"text": "<PERSON><PERSON> dụng: <PERSON><PERSON><PERSON> Home Mesh 3+.", "fontSize": 12, "fontWeight": 400, "color": "#9ca3af", "icon": "CheckCircleFilled", "iconColor": "#10b981", "hasIcon": true}, "id": "card-2-condition"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "Yrc__-hQUx", "hidden": false, "nodes": [], "linkedNodes": {}}, "qQ7LeUBtMV": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "Th<PERSON>i gian: 01/07 - 31/07/2025.", "fontSize": 12, "fontWeight": 400, "color": "#9ca3af", "icon": "CalendarFilled", "iconColor": "#10b981", "hasIcon": true}, "tablet": {"text": "Th<PERSON>i gian: 01/07 - 31/07/2025.", "fontSize": 12, "fontWeight": 400, "color": "#9ca3af"}, "mobile": {"text": "Th<PERSON>i gian: 01/07 - 31/07/2025.", "fontSize": 12, "fontWeight": 400, "color": "#9ca3af"}, "id": "card-2-time"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "Yrc__-hQUx", "hidden": false, "nodes": [], "linkedNodes": {}}, "SIibGpxSsH": {"type": {"resolvedName": "Row"}, "isCanvas": true, "props": {"desktop": {"colWidths": [1, 1], "gap": 8, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 16, "bottom": 0, "left": 0, "right": 0}}, "tablet": {"colWidths": [1, 1], "gap": 8, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 16, "bottom": 0, "left": 0, "right": 0}}, "mobile": {"colWidths": [1, 1], "gap": 8, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 16, "bottom": 0, "left": 0, "right": 0}}, "id": "card-2-buttons"}, "displayName": "Row", "custom": {}, "parent": "Yrc__-hQUx", "hidden": false, "nodes": ["JQ4ULHvNQt", "G411cfT02i"], "linkedNodes": {}}, "JQ4ULHvNQt": {"type": {"resolvedName": "<PERSON><PERSON>"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "id": "card-2-detail-btn"}, "displayName": "<PERSON><PERSON>", "custom": {}, "parent": "SIibGpxSsH", "hidden": false, "nodes": [], "linkedNodes": {}}, "G411cfT02i": {"type": {"resolvedName": "<PERSON><PERSON>"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON>", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#ef9304", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON><PERSON>", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#ef9304", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON><PERSON>", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#ef9304", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle"}, "id": "card-2-apply-btn"}, "displayName": "<PERSON><PERSON>", "custom": {}, "parent": "SIibGpxSsH", "hidden": false, "nodes": [], "linkedNodes": {}}, "jYIlfTPIsT": {"type": {"resolvedName": "Column"}, "isCanvas": true, "props": {"desktop": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "width": "100%", "border": {"radius": 8, "width": 1, "color": "#e5e7eb", "style": "solid"}, "shadow": 8}, "tablet": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "width": "100%", "border": {"radius": 8, "width": 1, "color": "#e5e7eb", "style": "solid"}, "shadow": 8}, "mobile": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "width": "100%", "border": {"radius": 8, "width": 1, "color": "#e5e7eb", "style": "solid"}, "shadow": 8}, "id": "promotion-card-3"}, "displayName": "Column", "custom": {}, "parent": "J0El4A2RAf", "hidden": false, "nodes": ["oI8NDL2a9W"], "linkedNodes": {}}, "oI8NDL2a9W": {"type": "div", "isCanvas": false, "props": {"className": "flex h-full flex-col overflow-hidden rounded-lg bg-white shadow-lg"}, "displayName": "div", "custom": {}, "parent": "jYIlfTPIsT", "hidden": false, "nodes": ["d8QKICno3P", "IDdiZ0IAHv"], "linkedNodes": {}}, "d8QKICno3P": {"type": "div", "isCanvas": false, "props": {"className": "overflow-hidden rounded-t-lg bg-white"}, "displayName": "div", "custom": {}, "parent": "oI8NDL2a9W", "hidden": false, "nodes": ["7T-1_hLjIr"], "linkedNodes": {}}, "7T-1_hLjIr": {"type": {"resolvedName": "Image"}, "isCanvas": true, "props": {"desktop": {"imgSrc": "https://placehold.co/600x300/E8F5E9/1B5E20?text=Tặng+Data", "width": "100%", "height": 128, "border": {"radius": 0, "width": 0, "color": "transparent", "style": "none"}, "imageFit": "fill", "align": "center"}, "tablet": {"imgSrc": "https://placehold.co/600x300/E8F5E9/1B5E20?text=Tặng+Data", "width": "100%", "height": 128, "border": {"radius": 0, "width": 0, "color": "transparent", "style": "none"}, "imageFit": "fill", "align": "center"}, "mobile": {"imgSrcMobile": "https://placehold.co/600x300/E8F5E9/1B5E20?text=Tặng+Data", "width": "100%", "height": 128, "border": {"radius": 0, "width": 0, "color": "transparent", "style": "none"}, "imageFit": "fill", "align": "center"}, "id": "card-3-image"}, "displayName": "Ảnh", "custom": {}, "parent": "d8QKICno3P", "hidden": false, "nodes": [], "linkedNodes": {}}, "IDdiZ0IAHv": {"type": {"resolvedName": "Column"}, "isCanvas": true, "props": {"desktop": {"padding": {"top": 16, "bottom": 16, "left": 16, "right": 16}, "gap": 8, "height": "auto", "alignItems": "stretch", "flexDirection": "column"}, "tablet": {"padding": {"top": 16, "bottom": 16, "left": 16, "right": 16}, "gap": 8, "height": "auto", "alignItems": "stretch", "flexDirection": "column"}, "mobile": {"padding": {"top": 16, "bottom": 16, "left": 16, "right": 16}, "gap": 8, "height": "auto", "alignItems": "stretch", "flexDirection": "column"}, "id": "card-3-content"}, "displayName": "Column", "custom": {}, "parent": "oI8NDL2a9W", "hidden": false, "nodes": ["3XlFio8zxx", "fOPszt3cJP", "Zt9EPVL6cM", "bk4VEoCqBC", "G-O7SZkynr"], "linkedNodes": {}}, "3XlFio8zxx": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "Combo Gia Đ<PERSON>", "fontSize": 18, "fontWeight": 700, "color": "#0F1319"}, "tablet": {"text": "Combo Gia Đ<PERSON>", "fontSize": 18, "fontWeight": 700, "color": "#0F1319"}, "mobile": {"text": "Combo Gia Đ<PERSON>", "fontSize": 18, "fontWeight": 700, "color": "#0F1319"}, "id": "card-3-title"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "IDdiZ0IAHv", "hidden": false, "nodes": [], "linkedNodes": {}}, "fOPszt3cJP": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "Giảm 20% khi đăng ký trọn bộ Internet - Truyền hình - Di động.", "fontSize": 14, "fontWeight": 400, "color": "#6b7280"}, "tablet": {"text": "Giảm 20% khi đăng ký trọn bộ Internet - Truyền hình - Di động.", "fontSize": 14, "fontWeight": 400, "color": "#6b7280"}, "mobile": {"text": "Giảm 20% khi đăng ký trọn bộ Internet - Truyền hình - Di động.", "fontSize": 14, "fontWeight": 400, "color": "#6b7280"}, "id": "card-3-description"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "IDdiZ0IAHv", "hidden": false, "nodes": [], "linkedNodes": {}}, "Zt9EPVL6cM": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "Áp dụng: Gói Home Combo.", "fontSize": 12, "fontWeight": 400, "color": "#9ca3af", "icon": "CheckCircleFilled", "iconColor": "#10b981", "hasIcon": true}, "tablet": {"text": "Áp dụng: Gói Home Combo.", "fontSize": 12, "fontWeight": 400, "color": "#9ca3af", "icon": "CheckCircleFilled", "iconColor": "#10b981", "hasIcon": true}, "mobile": {"text": "Áp dụng: Gói Home Combo.", "fontSize": 12, "fontWeight": 400, "color": "#9ca3af", "icon": "CheckCircleFilled", "iconColor": "#10b981", "hasIcon": true}, "id": "card-3-condition"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "IDdiZ0IAHv", "hidden": false, "nodes": [], "linkedNodes": {}}, "bk4VEoCqBC": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "Th<PERSON>i gian: 01/07 - 31/07/2025.", "fontSize": 12, "fontWeight": 400, "color": "#9ca3af", "icon": "CalendarFilled", "iconColor": "#10b981", "hasIcon": true}, "tablet": {"text": "Th<PERSON>i gian: 01/07 - 31/07/2025.", "fontSize": 12, "fontWeight": 400, "color": "#9ca3af", "icon": "CalendarFilled", "iconColor": "#10b981", "hasIcon": true}, "mobile": {"text": "Th<PERSON>i gian: 01/07 - 31/07/2025.", "fontSize": 12, "fontWeight": 400, "color": "#9ca3af", "icon": "CalendarFilled", "iconColor": "#10b981", "hasIcon": true}, "id": "card-3-time"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "IDdiZ0IAHv", "hidden": false, "nodes": [], "linkedNodes": {}}, "G-O7SZkynr": {"type": {"resolvedName": "Row"}, "isCanvas": true, "props": {"desktop": {"colWidths": [1, 1], "gap": 8, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 16, "bottom": 0, "left": 0, "right": 0}}, "tablet": {"colWidths": [1, 1], "gap": 8, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 16, "bottom": 0, "left": 0, "right": 0}}, "mobile": {"colWidths": [1, 1], "gap": 8, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 16, "bottom": 0, "left": 0, "right": 0}}, "id": "card-3-buttons"}, "displayName": "Row", "custom": {}, "parent": "IDdiZ0IAHv", "hidden": false, "nodes": ["uNwfeW0jku", "WGMjVziN6Q"], "linkedNodes": {}}, "uNwfeW0jku": {"type": {"resolvedName": "<PERSON><PERSON>"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#ef9304", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "id": "card-3-detail-btn"}, "displayName": "<PERSON><PERSON>", "custom": {}, "parent": "G-O7SZkynr", "hidden": false, "nodes": [], "linkedNodes": {}}, "WGMjVziN6Q": {"type": {"resolvedName": "<PERSON><PERSON>"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON>", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#ef9304", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON><PERSON>", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#ef9304", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON><PERSON>", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#ea580c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "id": "card-3-apply-btn"}, "displayName": "<PERSON><PERSON>", "custom": {}, "parent": "G-O7SZkynr", "hidden": false, "nodes": [], "linkedNodes": {}}, "xqrzsGNYti": {"type": {"resolvedName": "Container"}, "isCanvas": true, "props": {"flexDirection": "column", "alignItems": "flex-start", "justifyContent": "flex-start", "padding": ["0", "0", "0", "0"], "margin": ["0", "0", "0", "0"], "background": "#FFFFFF", "color": {"r": 0, "g": 0, "b": 0, "a": 1}, "shadow": 0, "radius": 0, "width": "100%", "height": "auto"}, "displayName": "Container", "custom": {}, "parent": "LVzadYzK8O", "hidden": false, "nodes": ["F47OhPa82P"], "linkedNodes": {}}, "F47OhPa82P": {"type": {"resolvedName": "Row"}, "isCanvas": true, "props": {"desktop": {"padding": {"top": 64, "bottom": 64, "left": 16, "right": 16}, "colWidths": [12], "gap": 6, "height": "auto", "alignItems": "center", "contentAlign": "start", "justifyContent": "center", "width": "100%"}, "tablet": {"padding": {"top": 48, "bottom": 48, "left": 16, "right": 16}, "colWidths": [12], "gap": 6, "height": "auto", "alignItems": "center", "contentAlign": "start", "justifyContent": "center", "width": "100%"}, "mobile": {"padding": {"top": 32, "bottom": 32, "left": 16, "right": 16}, "colWidths": [12], "gap": 6, "height": "auto", "alignItems": "center", "contentAlign": "start", "justifyContent": "center", "width": "100%"}, "id": "products-container", "disableSelection": true}, "displayName": "Row", "custom": {}, "parent": "xqrzsGNYti", "hidden": false, "nodes": ["iL7RagiU_l", "B0S6kN2ajW"], "linkedNodes": {}}, "iL7RagiU_l": {"type": {"resolvedName": "Column"}, "isCanvas": true, "props": {"desktop": {"display": "flex", "justifyContent": "center", "alignItems": "center", "flexDirection": "column", "gap": 0, "margin": {"top": 0, "bottom": 40, "left": 0, "right": 0}}, "tablet": {"display": "flex", "justifyContent": "center", "alignItems": "center", "flexDirection": "column", "gap": 0, "margin": {"top": 0, "bottom": 40, "left": 0, "right": 0}}, "mobile": {"display": "flex", "justifyContent": "center", "alignItems": "center", "flexDirection": "column", "gap": 0, "margin": {"top": 0, "bottom": 40, "left": 0, "right": 0}}, "id": "products-title-section"}, "displayName": "Column", "custom": {}, "parent": "F47OhPa82P", "hidden": false, "nodes": ["vcJLwM-pS8"], "linkedNodes": {}}, "vcJLwM-pS8": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "G<PERSON><PERSON><PERSON> ph<PERSON><PERSON> đ<PERSON> nhiều gia đình lựa chọn", "fontSize": 32, "fontWeight": 700, "color": "#00529c", "textAlign": "center"}, "tablet": {"text": "G<PERSON><PERSON><PERSON> ph<PERSON><PERSON> đ<PERSON> nhiều gia đình lựa chọn", "fontSize": 28, "fontWeight": 700, "color": "#00529c", "textAlign": "center"}, "mobile": {"text": "G<PERSON><PERSON><PERSON> ph<PERSON><PERSON> đ<PERSON> nhiều gia đình lựa chọn", "fontSize": 24, "fontWeight": 700, "color": "#00529c", "textAlign": "center"}, "id": "products-title"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "iL7RagiU_l", "hidden": false, "nodes": [], "linkedNodes": {}}, "B0S6kN2ajW": {"type": {"resolvedName": "Row"}, "isCanvas": true, "props": {"desktop": {"colWidths": [3, 3, 3, 3], "gap": 6, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%"}, "tablet": {"colWidths": [6, 6], "gap": 6, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "isBreakLine": true}, "mobile": {"colWidths": [12], "gap": 6, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "isBreakLine": true}, "id": "products-cards-row"}, "displayName": "Row", "custom": {}, "parent": "F47OhPa82P", "hidden": false, "nodes": ["ugrHYnw33b", "HcBv7u2Rnh", "7y_J6DgrG9", "gXAi1w6er7"], "linkedNodes": {}}, "ugrHYnw33b": {"type": {"resolvedName": "Column"}, "isCanvas": true, "props": {"desktop": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "100%", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "tablet": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "100%", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "mobile": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}, "gap": 0, "height": "100%", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "id": "product-card-1"}, "displayName": "Column", "custom": {}, "parent": "B0S6kN2ajW", "hidden": false, "nodes": ["rESftR8cFe"], "linkedNodes": {}}, "rESftR8cFe": {"type": {"resolvedName": "ProductCard"}, "isCanvas": true, "props": {"desktop": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "tablet": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "mobile": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "id": "product1"}, "displayName": "ProductCard", "custom": {}, "parent": "ugrHYnw33b", "hidden": false, "nodes": ["FjG40FapXr", "XYYjotNSlM"], "linkedNodes": {}}, "FjG40FapXr": {"type": "div", "isCanvas": false, "props": {"className": "rounded-t-lg bg-[#e2e8f0]"}, "displayName": "div", "custom": {}, "parent": "rESftR8cFe", "hidden": false, "nodes": ["48WQze8jNB"], "linkedNodes": {}}, "48WQze8jNB": {"type": {"resolvedName": "Image"}, "isCanvas": true, "props": {"desktop": {"imgSrc": "https://placehold.co/600x400/e2e8f0/334155?text=Home+Mesh+2", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "tablet": {"imgSrc": "https://placehold.co/600x400/e2e8f0/334155?text=Home+Mesh+2", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "mobile": {"imgSrcMobile": "https://placehold.co/600x400/e2e8f0/334155?text=Home+Mesh+2", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "id": "card-1-image"}, "displayName": "Ảnh", "custom": {}, "parent": "FjG40FapXr", "hidden": false, "nodes": [], "linkedNodes": {}}, "XYYjotNSlM": {"type": "div", "isCanvas": false, "props": {"className": "flex h-full flex-col justify-between p-4"}, "displayName": "div", "custom": {}, "parent": "rESftR8cFe", "hidden": false, "nodes": ["Ao-gBcPoUz", "KX0J8n0jGI"], "linkedNodes": {}}, "Ao-gBcPoUz": {"type": "div", "isCanvas": false, "props": {"className": "flex flex-col"}, "displayName": "div", "custom": {}, "parent": "XYYjotNSlM", "hidden": false, "nodes": ["M-KfkdkanD", "HO9iG009GF", "SxdEN-eIp9"], "linkedNodes": {}}, "M-KfkdkanD": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "Home Mesh 2+", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "tablet": {"text": "Home Mesh 2+", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "mobile": {"text": "Home Mesh 2+", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "id": "card-1-title"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "Ao-gBcPoUz", "hidden": false, "nodes": [], "linkedNodes": {}}, "HO9iG009GF": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "Internet & TV cho gia đình", "fontSize": 14, "fontWeight": 400, "color": "#6b7280"}, "tablet": {"text": "Internet & TV cho gia đình", "fontSize": 14, "fontWeight": 400, "color": "#6b7280"}, "mobile": {"text": "Internet & TV cho gia đình", "fontSize": 14, "fontWeight": 400, "color": "#6b7280"}, "id": "card-1-description"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "Ao-gBcPoUz", "hidden": false, "nodes": [], "linkedNodes": {}}, "SxdEN-eIp9": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<div style=\"background-color: #f0fdf4; padding: 8px; border-radius: 4px; margin: 8px 0;\"><p style=\"font-size: 12px; font-weight: 700; color: #16a34a; margin: 0;\">KM: Tặng thêm 2 tháng khi đóng trước 6 tháng.</p></div>", "fontSize": 12, "fontWeight": 700, "color": "#16a34a", "padding": {"top": 8, "bottom": 8, "left": 0, "right": 0}, "backgroundColor": "#f0fdf4", "border": {"radius": 4}}, "tablet": {"text": "<div style=\"background-color: #f0fdf4; padding: 8px; border-radius: 4px; margin: 8px 0;\"><p style=\"font-size: 12px; font-weight: 700; color: #16a34a; margin: 0;\">KM: Tặng thêm 2 tháng khi đóng trước 6 tháng.</p></div>", "fontSize": 12, "fontWeight": 700, "color": "#16a34a", "padding": {"top": 8, "bottom": 8, "left": 0, "right": 0}, "backgroundColor": "#f0fdf4", "border": {"radius": 4}}, "mobile": {"text": "<div style=\"background-color: #f0fdf4; padding: 8px; border-radius: 4px; margin: 8px 0;\"><p style=\"font-size: 12px; font-weight: 700; color: #16a34a; margin: 0;\">KM: Tặng thêm 2 tháng khi đóng trước 6 tháng.</p></div>", "fontSize": 12, "fontWeight": 700, "color": "#16a34a", "padding": {"top": 8, "bottom": 8, "left": 0, "right": 0}, "backgroundColor": "#f0fdf4", "border": {"radius": 4}}, "id": "card-1-promotion"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "Ao-gBcPoUz", "hidden": false, "nodes": [], "linkedNodes": {}}, "KX0J8n0jGI": {"type": "div", "isCanvas": false, "props": {"className": "flex flex-col"}, "displayName": "div", "custom": {}, "parent": "XYYjotNSlM", "hidden": false, "nodes": ["UoOlC9izhT", "yQGnWzOPKG"], "linkedNodes": {}}, "UoOlC9izhT": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "245.000đ<span style=\"font-size: 16px; font-weight: 400;\">/tháng</span>", "fontSize": 24, "fontWeight": 700, "color": "#00529c"}, "tablet": {"text": "245.000đ<span style=\"font-size: 16px; font-weight: 400;\">/tháng</span>", "fontSize": 24, "fontWeight": 700, "color": "#00529c"}, "mobile": {"text": "245.000đ<span style=\"font-size: 16px; font-weight: 400;\">/tháng</span>", "fontSize": 24, "fontWeight": 700, "color": "#00529c"}, "id": "card-1-price"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "KX0J8n0jGI", "hidden": false, "nodes": [], "linkedNodes": {}}, "yQGnWzOPKG": {"type": {"resolvedName": "Row"}, "isCanvas": true, "props": {"desktop": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "tablet": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "mobile": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "id": "card-1-buttons"}, "displayName": "Row", "custom": {}, "parent": "KX0J8n0jGI", "hidden": false, "nodes": ["F4JD5uNJXV", "vUmWMv61MW"], "linkedNodes": {}}, "F4JD5uNJXV": {"type": {"resolvedName": "<PERSON><PERSON>"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "id": "card-1-detail-btn"}, "displayName": "<PERSON><PERSON>", "custom": {}, "parent": "yQGnWzOPKG", "hidden": false, "nodes": [], "linkedNodes": {}}, "vUmWMv61MW": {"type": {"resolvedName": "<PERSON><PERSON>"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON> ngay", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON><PERSON> ngay", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON><PERSON> ngay", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "id": "card-1-buy-btn"}, "displayName": "<PERSON><PERSON>", "custom": {}, "parent": "yQGnWzOPKG", "hidden": false, "nodes": [], "linkedNodes": {}}, "HcBv7u2Rnh": {"type": {"resolvedName": "Column"}, "isCanvas": true, "props": {"desktop": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "100%", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "tablet": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "100%", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "mobile": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}, "gap": 0, "height": "100%", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "id": "product-card-2"}, "displayName": "Column", "custom": {}, "parent": "B0S6kN2ajW", "hidden": false, "nodes": ["f-3vQzcqwP"], "linkedNodes": {}}, "f-3vQzcqwP": {"type": {"resolvedName": "ProductCard"}, "isCanvas": true, "props": {"desktop": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "tablet": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "mobile": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "id": "product2"}, "displayName": "ProductCard", "custom": {}, "parent": "HcBv7u2Rnh", "hidden": false, "nodes": ["6EUMkNQge4", "qLIAYA0YfD"], "linkedNodes": {}}, "6EUMkNQge4": {"type": "div", "isCanvas": false, "props": {"className": "rounded-t-lg bg-[#e2e8f0]"}, "displayName": "div", "custom": {}, "parent": "f-3vQzcqwP", "hidden": false, "nodes": ["LpkpNZ-2oj"], "linkedNodes": {}}, "LpkpNZ-2oj": {"type": {"resolvedName": "Image"}, "isCanvas": true, "props": {"desktop": {"imgSrc": "https://placehold.co/600x400/e2e8f0/334155?text=Home+Camera", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "tablet": {"imgSrc": "https://placehold.co/600x400/e2e8f0/334155?text=Home+Camera", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "mobile": {"imgSrcMobile": "https://placehold.co/600x400/e2e8f0/334155?text=Home+Camera", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "id": "card-2-image"}, "displayName": "Ảnh", "custom": {}, "parent": "6EUMkNQge4", "hidden": false, "nodes": [], "linkedNodes": {}}, "qLIAYA0YfD": {"type": "div", "isCanvas": false, "props": {"className": "flex h-full flex-col justify-between p-4"}, "displayName": "div", "custom": {}, "parent": "f-3vQzcqwP", "hidden": false, "nodes": ["76Ryy6SXR0", "HItt2Vb9lU"], "linkedNodes": {}}, "76Ryy6SXR0": {"type": "div", "isCanvas": false, "props": {"className": "flex flex-col"}, "displayName": "div", "custom": {}, "parent": "qLIAYA0YfD", "hidden": false, "nodes": ["zKknmPeVKd", "VJpjtvRj0i", "weFozle9et"], "linkedNodes": {}}, "zKknmPeVKd": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "Home Camera", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "tablet": {"text": "Home Camera", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "mobile": {"text": "Home Camera", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "id": "card-2-title"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "76Ryy6SXR0", "hidden": false, "nodes": [], "linkedNodes": {}}, "VJpjtvRj0i": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "Internet & Camera an ninh", "fontSize": 14, "fontWeight": 400, "color": "#6b7280"}, "tablet": {"text": "Internet & Camera an ninh", "fontSize": 14, "fontWeight": 400, "color": "#6b7280"}, "mobile": {"text": "Internet & Camera an ninh", "fontSize": 14, "fontWeight": 400, "color": "#6b7280"}, "id": "card-2-description"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "76Ryy6SXR0", "hidden": false, "nodes": [], "linkedNodes": {}}, "weFozle9et": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<div style=\"background-color: #fff7ed; padding: 8px; border-radius: 4px; margin: 8px 0;\"><p style=\"font-size: 12px; font-weight: 700; color: #ea580c; margin: 0;\">KM: Tặng 01 Camera khi lắp mới.</p></div>", "fontSize": 12, "fontWeight": 700, "color": "#ea580c", "padding": {"top": 8, "bottom": 8, "left": 0, "right": 0}, "backgroundColor": "#fff7ed", "border": {"radius": 4}}, "tablet": {"text": "<div style=\"background-color: #fff7ed; padding: 8px; border-radius: 4px; margin: 8px 0;\"><p style=\"font-size: 12px; font-weight: 700; color: #ea580c; margin: 0;\">KM: Tặng 01 Camera khi lắp mới.</p></div>", "fontSize": 12, "fontWeight": 700, "color": "#ea580c", "padding": {"top": 8, "bottom": 8, "left": 0, "right": 0}, "backgroundColor": "#fff7ed", "border": {"radius": 4}}, "mobile": {"text": "<div style=\"background-color: #fff7ed; padding: 8px; border-radius: 4px; margin: 8px 0;\"><p style=\"font-size: 12px; font-weight: 700; color: #ea580c; margin: 0;\">KM: Tặng 01 Camera khi lắp mới.</p></div>", "fontSize": 12, "fontWeight": 700, "color": "#ea580c", "padding": {"top": 8, "bottom": 8, "left": 0, "right": 0}, "backgroundColor": "#fff7ed", "border": {"radius": 4}}, "id": "card-2-promotion"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "76Ryy6SXR0", "hidden": false, "nodes": [], "linkedNodes": {}}, "HItt2Vb9lU": {"type": "div", "isCanvas": false, "props": {"className": "flex flex-col"}, "displayName": "div", "custom": {}, "parent": "qLIAYA0YfD", "hidden": false, "nodes": ["5AA53e9lal", "8LU7tmfBK6"], "linkedNodes": {}}, "5AA53e9lal": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "210.000đ<span style=\"font-size: 16px; font-weight: 400;\">/tháng</span>", "fontSize": 24, "fontWeight": 700, "color": "#00529c"}, "tablet": {"text": "210.000đ<span style=\"font-size: 16px; font-weight: 400;\">/tháng</span>", "fontSize": 24, "fontWeight": 700, "color": "#00529c"}, "mobile": {"text": "210.000đ<span style=\"font-size: 16px; font-weight: 400;\">/tháng</span>", "fontSize": 24, "fontWeight": 700, "color": "#00529c"}, "id": "card-2-price"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "HItt2Vb9lU", "hidden": false, "nodes": [], "linkedNodes": {}}, "8LU7tmfBK6": {"type": {"resolvedName": "Row"}, "isCanvas": true, "props": {"desktop": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "tablet": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "mobile": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "id": "card-2-buttons"}, "displayName": "Row", "custom": {}, "parent": "HItt2Vb9lU", "hidden": false, "nodes": ["DMYScYFx0U", "juyVo1RlJK"], "linkedNodes": {}}, "DMYScYFx0U": {"type": {"resolvedName": "<PERSON><PERSON>"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "id": "card-2-detail-btn"}, "displayName": "<PERSON><PERSON>", "custom": {}, "parent": "8LU7tmfBK6", "hidden": false, "nodes": [], "linkedNodes": {}}, "juyVo1RlJK": {"type": {"resolvedName": "<PERSON><PERSON>"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON> ngay", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON><PERSON> ngay", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON><PERSON> ngay", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "id": "card-2-buy-btn"}, "displayName": "<PERSON><PERSON>", "custom": {}, "parent": "8LU7tmfBK6", "hidden": false, "nodes": [], "linkedNodes": {}}, "7y_J6DgrG9": {"type": {"resolvedName": "Column"}, "isCanvas": true, "props": {"desktop": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "100%", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "tablet": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "100%", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "mobile": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}, "gap": 0, "height": "100%", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "id": "product-card-3"}, "displayName": "Column", "custom": {}, "parent": "B0S6kN2ajW", "hidden": false, "nodes": ["KzAT5jaX80"], "linkedNodes": {}}, "KzAT5jaX80": {"type": {"resolvedName": "ProductCard"}, "isCanvas": true, "props": {"desktop": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "tablet": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "mobile": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "id": "product3"}, "displayName": "ProductCard", "custom": {}, "parent": "7y_J6DgrG9", "hidden": false, "nodes": ["dAYkd-5IaR", "awJmTFE0Kv"], "linkedNodes": {}}, "dAYkd-5IaR": {"type": "div", "isCanvas": false, "props": {"className": "rounded-t-lg bg-[#e2e8f0]"}, "displayName": "div", "custom": {}, "parent": "KzAT5jaX80", "hidden": false, "nodes": ["kf9DrkzyOV"], "linkedNodes": {}}, "kf9DrkzyOV": {"type": {"resolvedName": "Image"}, "isCanvas": true, "props": {"desktop": {"imgSrc": "https://placehold.co/600x400/e2e8f0/334155?text=Home+Combo", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "tablet": {"imgSrc": "https://placehold.co/600x400/e2e8f0/334155?text=Home+Combo", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "mobile": {"imgSrcMobile": "https://placehold.co/600x400/e2e8f0/334155?text=Home+Combo", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "id": "card-3-image"}, "displayName": "Ảnh", "custom": {}, "parent": "dAYkd-5IaR", "hidden": false, "nodes": [], "linkedNodes": {}}, "awJmTFE0Kv": {"type": "div", "isCanvas": false, "props": {"className": "flex h-full flex-col justify-between p-4"}, "displayName": "div", "custom": {}, "parent": "KzAT5jaX80", "hidden": false, "nodes": ["GP6Qq1-msV", "QshhZ0ged2"], "linkedNodes": {}}, "GP6Qq1-msV": {"type": "div", "isCanvas": false, "props": {"className": "flex flex-col"}, "displayName": "div", "custom": {}, "parent": "awJmTFE0Kv", "hidden": false, "nodes": ["EyKSwCJtou", "auj2zlbYdm", "MOA8kOdjUd"], "linkedNodes": {}}, "EyKSwCJtou": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "Home Combo", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "tablet": {"text": "Home Combo", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "mobile": {"text": "Home Combo", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "id": "card-3-title"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "GP6Qq1-msV", "hidden": false, "nodes": [], "linkedNodes": {}}, "auj2zlbYdm": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "Trọn gói Internet - TV - Di động", "fontSize": 14, "fontWeight": 400, "color": "#6b7280"}, "tablet": {"text": "Trọn gói Internet - TV - Di động", "fontSize": 14, "fontWeight": 400, "color": "#6b7280"}, "mobile": {"text": "Trọn gói Internet - TV - Di động", "fontSize": 14, "fontWeight": 400, "color": "#6b7280"}, "id": "card-3-description"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "GP6Qq1-msV", "hidden": false, "nodes": [], "linkedNodes": {}}, "MOA8kOdjUd": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<div style=\"background-color: #eff6ff; padding: 8px; border-radius: 4px; margin: 8px 0;\"><p style=\"font-size: 12px; font-weight: 700; color: #2563eb; margin: 0;\">KM: <PERSON><PERSON><PERSON><PERSON> kiệm đến 50% so với dùng lẻ.</p></div>", "fontSize": 12, "fontWeight": 700, "color": "#2563eb", "padding": {"top": 8, "bottom": 8, "left": 0, "right": 0}, "backgroundColor": "#eff6ff", "border": {"radius": 4}}, "tablet": {"text": "<div style=\"background-color: #eff6ff; padding: 8px; border-radius: 4px; margin: 8px 0;\"><p style=\"font-size: 12px; font-weight: 700; color: #2563eb; margin: 0;\">KM: <PERSON><PERSON><PERSON><PERSON> kiệm đến 50% so với dùng lẻ.</p></div>", "fontSize": 12, "fontWeight": 700, "color": "#2563eb", "padding": {"top": 8, "bottom": 8, "left": 0, "right": 0}, "backgroundColor": "#eff6ff", "border": {"radius": 4}}, "mobile": {"text": "<div style=\"background-color: #eff6ff; padding: 8px; border-radius: 4px; margin: 8px 0;\"><p style=\"font-size: 12px; font-weight: 700; color: #2563eb; margin: 0;\">KM: <PERSON><PERSON><PERSON><PERSON> kiệm đến 50% so với dùng lẻ.</p></div>", "fontSize": 12, "fontWeight": 700, "color": "#2563eb", "padding": {"top": 8, "bottom": 8, "left": 0, "right": 0}, "backgroundColor": "#eff6ff", "border": {"radius": 4}}, "id": "card-3-promotion"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "GP6Qq1-msV", "hidden": false, "nodes": [], "linkedNodes": {}}, "QshhZ0ged2": {"type": "div", "isCanvas": false, "props": {"className": "flex flex-col"}, "displayName": "div", "custom": {}, "parent": "awJmTFE0Kv", "hidden": false, "nodes": ["LQhivwf3Fg", "zrGy0sk42s"], "linkedNodes": {}}, "LQhivwf3Fg": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "Từ 239.000đ", "fontSize": 20, "fontWeight": 700, "color": "#00529c"}, "tablet": {"text": "Từ 239.000đ", "fontSize": 20, "fontWeight": 700, "color": "#00529c"}, "mobile": {"text": "Từ 239.000đ", "fontSize": 20, "fontWeight": 700, "color": "#00529c"}, "id": "card-3-price"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "QshhZ0ged2", "hidden": false, "nodes": [], "linkedNodes": {}}, "zrGy0sk42s": {"type": {"resolvedName": "Row"}, "isCanvas": true, "props": {"desktop": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "tablet": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "mobile": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "id": "card-3-buttons"}, "displayName": "Row", "custom": {}, "parent": "QshhZ0ged2", "hidden": false, "nodes": ["e-kwPUkDM4", "Q-1srLTfHH"], "linkedNodes": {}}, "e-kwPUkDM4": {"type": {"resolvedName": "<PERSON><PERSON>"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "id": "card-3-detail-btn"}, "displayName": "<PERSON><PERSON>", "custom": {}, "parent": "zrGy0sk42s", "hidden": false, "nodes": [], "linkedNodes": {}}, "Q-1srLTfHH": {"type": {"resolvedName": "<PERSON><PERSON>"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "id": "card-3-consult-btn"}, "displayName": "<PERSON><PERSON>", "custom": {}, "parent": "zrGy0sk42s", "hidden": false, "nodes": [], "linkedNodes": {}}, "gXAi1w6er7": {"type": {"resolvedName": "Column"}, "isCanvas": true, "props": {"desktop": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "100%", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "tablet": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "100%", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "mobile": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}, "gap": 0, "height": "100%", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "id": "product-card-4"}, "displayName": "Column", "custom": {}, "parent": "B0S6kN2ajW", "hidden": false, "nodes": ["cGMddry9i8"], "linkedNodes": {}}, "cGMddry9i8": {"type": {"resolvedName": "ProductCard"}, "isCanvas": true, "props": {"desktop": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "tablet": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "mobile": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "id": "product4"}, "displayName": "ProductCard", "custom": {}, "parent": "gXAi1w6er7", "hidden": false, "nodes": ["1Ph9l7Gqkh", "JcADqdT9-K"], "linkedNodes": {}}, "1Ph9l7Gqkh": {"type": "div", "isCanvas": false, "props": {"className": "rounded-t-lg bg-[#e2e8f0]"}, "displayName": "div", "custom": {}, "parent": "cGMddry9i8", "hidden": false, "nodes": ["DE0PV1qv8G"], "linkedNodes": {}}, "DE0PV1qv8G": {"type": {"resolvedName": "Image"}, "isCanvas": true, "props": {"desktop": {"imgSrc": "https://placehold.co/600x400/e2e8f0/334155?text=Smart+Home", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "tablet": {"imgSrc": "https://placehold.co/600x400/e2e8f0/334155?text=Smart+Home", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "mobile": {"imgSrcMobile": "https://placehold.co/600x400/e2e8f0/334155?text=Smart+Home", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "id": "card-4-image"}, "displayName": "Ảnh", "custom": {}, "parent": "1Ph9l7Gqkh", "hidden": false, "nodes": [], "linkedNodes": {}}, "JcADqdT9-K": {"type": "div", "isCanvas": false, "props": {"className": "flex h-full flex-col justify-between p-4"}, "displayName": "div", "custom": {}, "parent": "cGMddry9i8", "hidden": false, "nodes": ["2cpihtth4o", "0huRLjXQbM"], "linkedNodes": {}}, "2cpihtth4o": {"type": "div", "isCanvas": false, "props": {"className": "flex flex-col"}, "displayName": "div", "custom": {}, "parent": "JcADqdT9-K", "hidden": false, "nodes": ["eCuuM23vCA", "7Zn1rSpuHp", "COEvgm-DJE"], "linkedNodes": {}}, "eCuuM23vCA": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "VNPT SmartHome", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "tablet": {"text": "VNPT SmartHome", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "mobile": {"text": "VNPT SmartHome", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "id": "card-4-title"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "2cpihtth4o", "hidden": false, "nodes": [], "linkedNodes": {}}, "7Zn1rSpuHp": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON><PERSON> thông minh trong tầm tay", "fontSize": 14, "fontWeight": 400, "color": "#6b7280"}, "tablet": {"text": "<PERSON><PERSON><PERSON> thông minh trong tầm tay", "fontSize": 14, "fontWeight": 400, "color": "#6b7280"}, "mobile": {"text": "<PERSON><PERSON><PERSON> thông minh trong tầm tay", "fontSize": 14, "fontWeight": 400, "color": "#6b7280"}, "id": "card-4-description"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "2cpihtth4o", "hidden": false, "nodes": [], "linkedNodes": {}}, "COEvgm-DJE": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "", "fontSize": 12, "fontWeight": 400, "color": "transparent", "padding": {"top": 52, "bottom": 0, "left": 0, "right": 0}}, "tablet": {"text": "", "fontSize": 12, "fontWeight": 400, "color": "transparent", "padding": {"top": 52, "bottom": 0, "left": 0, "right": 0}}, "mobile": {"text": "", "fontSize": 12, "fontWeight": 400, "color": "transparent", "padding": {"top": 52, "bottom": 0, "left": 0, "right": 0}}, "id": "card-4-empty"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "2cpihtth4o", "hidden": false, "nodes": [], "linkedNodes": {}}, "0huRLjXQbM": {"type": "div", "isCanvas": false, "props": {"className": "flex flex-col"}, "displayName": "div", "custom": {}, "parent": "JcADqdT9-K", "hidden": false, "nodes": ["Fm9NyixDHX", "Zbbg1I5Mfg"], "linkedNodes": {}}, "Fm9NyixDHX": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#00529c"}, "tablet": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#00529c"}, "mobile": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#00529c"}, "id": "card-4-contact"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "0huRLjXQbM", "hidden": false, "nodes": [], "linkedNodes": {}}, "Zbbg1I5Mfg": {"type": {"resolvedName": "Row"}, "isCanvas": true, "props": {"desktop": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "tablet": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "mobile": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "id": "card-4-buttons"}, "displayName": "Row", "custom": {}, "parent": "0huRLjXQbM", "hidden": false, "nodes": ["Fs5sy8wRaE", "Gs29872q-s"], "linkedNodes": {}}, "Fs5sy8wRaE": {"type": {"resolvedName": "<PERSON><PERSON>"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "id": "card-4-detail-btn"}, "displayName": "<PERSON><PERSON>", "custom": {}, "parent": "Zbbg1I5Mfg", "hidden": false, "nodes": [], "linkedNodes": {}}, "Gs29872q-s": {"type": {"resolvedName": "<PERSON><PERSON>"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "id": "card-4-consult-btn"}, "displayName": "<PERSON><PERSON>", "custom": {}, "parent": "Zbbg1I5Mfg", "hidden": false, "nodes": [], "linkedNodes": {}}, "9fokxySWtq": {"type": {"resolvedName": "Container"}, "isCanvas": true, "props": {"flexDirection": "column", "alignItems": "flex-start", "justifyContent": "flex-start", "padding": ["0", "0", "0", "0"], "margin": ["0", "0", "0", "0"], "background": "#FFFFFF", "color": {"r": 0, "g": 0, "b": 0, "a": 1}, "shadow": 0, "radius": 0, "width": "100%", "height": "auto"}, "displayName": "Container", "custom": {}, "parent": "zdid6kiHPD", "hidden": false, "nodes": ["7JaWi0ge_8"], "linkedNodes": {}}, "7JaWi0ge_8": {"type": {"resolvedName": "Row"}, "isCanvas": true, "props": {"desktop": {"padding": {"top": 64, "bottom": 64, "left": 16, "right": 16}, "colWidths": [12], "gap": 6, "height": "auto", "alignItems": "center", "contentAlign": "start", "justifyContent": "center", "width": "100%"}, "tablet": {"padding": {"top": 48, "bottom": 48, "left": 16, "right": 16}, "colWidths": [12], "gap": 6, "height": "auto", "alignItems": "center", "contentAlign": "start", "justifyContent": "center", "width": "100%"}, "mobile": {"padding": {"top": 32, "bottom": 32, "left": 16, "right": 16}, "colWidths": [12], "gap": 6, "height": "auto", "alignItems": "center", "contentAlign": "start", "justifyContent": "center", "width": "100%"}, "id": "all-solutions-container", "disableSelection": true}, "displayName": "Row", "custom": {}, "parent": "9fokxySWtq", "hidden": false, "nodes": ["CZUulRc7te", "JfhAAguQal", "PdaX0mqq1P", "H8DjWTI7io", "TjMVaVHfF1"], "linkedNodes": {}}, "CZUulRc7te": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON><PERSON> c<PERSON> pháp cho Gia đình", "fontSize": 32, "fontWeight": 700, "color": "#00529c", "textAlign": "center"}, "tablet": {"text": "<PERSON><PERSON><PERSON> c<PERSON> pháp cho Gia đình", "fontSize": 28, "fontWeight": 700, "color": "#00529c", "textAlign": "center"}, "mobile": {"text": "<PERSON><PERSON><PERSON> c<PERSON> pháp cho Gia đình", "fontSize": 24, "fontWeight": 700, "color": "#00529c", "textAlign": "center"}, "id": "all-solutions-title"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "7JaWi0ge_8", "hidden": false, "nodes": [], "linkedNodes": {}}, "JfhAAguQal": {"type": {"resolvedName": "InternetAndTVSection"}, "isCanvas": false, "props": {"container": {"height": "auto", "background": "#FFFFFF", "padding": ["0", "0", "0", "0"]}, "internetTvSection": {"desktop": {"padding": {"top": 0, "bottom": 24, "left": 0, "right": 0}, "colWidths": [12], "gap": 6, "height": "auto", "alignItems": "center", "contentAlign": "start", "justifyContent": "center", "width": "100%"}, "tablet": {"padding": {"top": 0, "bottom": 24, "left": 0, "right": 0}, "colWidths": [12], "gap": 6, "height": "auto", "alignItems": "center", "contentAlign": "start", "justifyContent": "center", "width": "100%"}, "mobile": {"padding": {"top": 0, "bottom": 24, "left": 0, "right": 0}, "colWidths": [12], "gap": 6, "height": "auto", "alignItems": "center", "contentAlign": "start", "justifyContent": "center", "width": "100%"}}, "internetTvHeading": {"desktop": {"text": "#Internet & Truyền hình", "fontSize": 24, "fontWeight": 700, "color": "#374151", "textAlign": "left"}, "tablet": {"text": "#Internet & Truyền hình", "fontSize": 22, "fontWeight": 700, "color": "#374151", "textAlign": "left"}, "mobile": {"text": "#Internet & Truyền hình", "fontSize": 20, "fontWeight": 700, "color": "#374151", "textAlign": "left"}}, "sectionTitle": {"desktop": {"margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}}, "tablet": {"margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}}, "mobile": {"margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}}}, "internetTvCards": {"desktop": {"colWidths": [3, 3, 3, 3], "padding": {"top": 0, "bottom": 24, "left": 0, "right": 0}, "gap": 6, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%"}, "tablet": {"colWidths": [6, 6], "gap": 6, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "isBreakLine": true}, "mobile": {"colWidths": [12], "gap": 6, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "isBreakLine": true}}, "internetCard": {"desktop": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "tablet": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "mobile": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}}, "productCard": {"desktop": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "tablet": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "mobile": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}}, "internetCard1Image": {"desktop": {"imgSrc": "https://placehold.co/600x400/FFF3E0/E65100?text=Home+Mesh+2", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "tablet": {"imgSrc": "https://placehold.co/600x400/FFF3E0/E65100?text=Home+Mesh+2", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "mobile": {"imgSrcMobile": "https://placehold.co/600x400/FFF3E0/E65100?text=Home+Mesh+2", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}}, "internetCard1Title": {"desktop": {"text": "Home Mesh 2+", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}, "tablet": {"text": "Home Mesh 2+", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}, "mobile": {"text": "Home Mesh 2+", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}}, "internetCard1Features": {"desktop": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Tốc độ Internet 150Mbps<br>✓ <PERSON><PERSON><PERSON><PERSON>n hình MyTV Nâng cao<br>✓ Trang bị 01 Wifi Mesh</div>", "fontSize": 14, "color": "#6b7280", "textAlign": "left"}, "tablet": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Tốc độ Internet 150Mbps<br>✓ <PERSON><PERSON><PERSON><PERSON>n hình MyTV Nâng cao<br>✓ Trang bị 01 Wifi Mesh</div>", "fontSize": 14, "color": "#6b7280", "textAlign": "left"}, "mobile": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Tốc độ Internet 150Mbps<br>✓ <PERSON><PERSON><PERSON><PERSON>n hình MyTV Nâng cao<br>✓ Trang bị 01 Wifi Mesh</div>", "fontSize": 14, "color": "#6b7280", "textAlign": "left"}}, "internetCard1Price": {"desktop": {"text": "245.000đ<span style=\"font-size: 16px; font-weight: 400;\">/tháng</span>", "fontSize": 24, "fontWeight": 700, "color": "#f7941e", "textAlign": "left", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "tablet": {"text": "245.000đ<span style=\"font-size: 16px; font-weight: 400;\">/tháng</span>", "fontSize": 24, "fontWeight": 700, "color": "#f7941e", "textAlign": "left", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "mobile": {"text": "245.000đ<span style=\"font-size: 16px; font-weight: 400;\">/tháng</span>", "fontSize": 24, "fontWeight": 700, "color": "#f7941e", "textAlign": "left", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}}, "internetCard1Buttons": {"desktop": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "tablet": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "mobile": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}}, "internetCard1DetailBtn": {"desktop": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}}, "internetCard1BuyBtn": {"desktop": {"text": "<PERSON><PERSON> ngay", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON><PERSON> ngay", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON><PERSON> ngay", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}}, "internetCard2Image": {"desktop": {"imgSrc": "https://placehold.co/600x400/FFF3E0/E65100?text=Home+Net", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "tablet": {"imgSrc": "https://placehold.co/600x400/FFF3E0/E65100?text=Home+Net", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "mobile": {"imgSrcMobile": "https://placehold.co/600x400/FFF3E0/E65100?text=Home+Net", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}}, "internetCard2Title": {"desktop": {"text": "Home Net 1", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}, "tablet": {"text": "Home Net 1", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}, "mobile": {"text": "Home Net 1", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}}, "internetCard2Features": {"desktop": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Tốc độ Internet 100Mbps<br>✓ <PERSON><PERSON> hợp cho cá nhân, gia đình nhỏ</div>", "fontSize": 14, "color": "#6b7280", "textAlign": "left"}, "tablet": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Tốc độ Internet 100Mbps<br>✓ <PERSON><PERSON> hợp cho cá nhân, gia đình nhỏ</div>", "fontSize": 14, "color": "#6b7280", "textAlign": "left"}, "mobile": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Tốc độ Internet 100Mbps<br>✓ <PERSON><PERSON> hợp cho cá nhân, gia đình nhỏ</div>", "fontSize": 14, "color": "#6b7280", "textAlign": "left"}}, "internetCard2Price": {"desktop": {"text": "165.000đ<span style=\"font-size: 16px; font-weight: 400;\">/tháng</span>", "fontSize": 24, "fontWeight": 700, "color": "#f7941e", "textAlign": "left", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "tablet": {"text": "165.000đ<span style=\"font-size: 16px; font-weight: 400;\">/tháng</span>", "fontSize": 24, "fontWeight": 700, "color": "#f7941e", "textAlign": "left", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "mobile": {"text": "165.000đ<span style=\"font-size: 16px; font-weight: 400;\">/tháng</span>", "fontSize": 24, "fontWeight": 700, "color": "#f7941e", "textAlign": "left", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}}, "internetCard2Buttons": {"desktop": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "tablet": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "mobile": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}}, "internetCard2DetailBtn": {"desktop": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}}, "internetCard2BuyBtn": {"desktop": {"text": "<PERSON><PERSON> ngay", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON><PERSON> ngay", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON><PERSON> ngay", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}}, "internetCard3Image": {"desktop": {"imgSrc": "https://placehold.co/600x400/FFF3E0/E65100?text=Home+TV", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "tablet": {"imgSrc": "https://placehold.co/600x400/FFF3E0/E65100?text=Home+TV", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "mobile": {"imgSrcMobile": "https://placehold.co/600x400/FFF3E0/E65100?text=Home+TV", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}}, "internetCard3Title": {"desktop": {"text": "Home TV 1", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}, "tablet": {"text": "Home TV 1", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}, "mobile": {"text": "Home TV 1", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}}, "internetCard3Features": {"desktop": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Tốc độ Internet 80Mbps<br>✓ T<PERSON><PERSON>ền hình MyTV Chuẩn<br>✓ <PERSON><PERSON><PERSON> cước tiết kiệm</div>", "fontSize": 14, "color": "#6b7280", "textAlign": "left"}, "tablet": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Tốc độ Internet 80Mbps<br>✓ T<PERSON><PERSON>ền hình MyTV Chuẩn<br>✓ <PERSON><PERSON><PERSON> cước tiết kiệm</div>", "fontSize": 14, "color": "#6b7280", "textAlign": "left"}, "mobile": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Tốc độ Internet 80Mbps<br>✓ T<PERSON><PERSON>ền hình MyTV Chuẩn<br>✓ <PERSON><PERSON><PERSON> cước tiết kiệm</div>", "fontSize": 14, "color": "#6b7280", "textAlign": "left"}}, "internetCard3Price": {"desktop": {"text": "175.000đ<span style=\"font-size: 16px; font-weight: 400;\">/tháng</span>", "fontSize": 24, "fontWeight": 700, "color": "#f7941e", "textAlign": "left", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "tablet": {"text": "175.000đ<span style=\"font-size: 16px; font-weight: 400;\">/tháng</span>", "fontSize": 24, "fontWeight": 700, "color": "#f7941e", "textAlign": "left", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "mobile": {"text": "175.000đ<span style=\"font-size: 16px; font-weight: 400;\">/tháng</span>", "fontSize": 24, "fontWeight": 700, "color": "#f7941e", "textAlign": "left", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}}, "internetCard3Buttons": {"desktop": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "tablet": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "mobile": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}}, "internetCard3DetailBtn": {"desktop": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}}, "internetCard3BuyBtn": {"desktop": {"text": "<PERSON><PERSON> ngay", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON><PERSON> ngay", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON><PERSON> ngay", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}}, "internetCard4Image": {"desktop": {"imgSrc": "https://placehold.co/600x400/FFF3E0/E65100?text=Home+Combo", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "tablet": {"imgSrc": "https://placehold.co/600x400/FFF3E0/E65100?text=Home+Combo", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "mobile": {"imgSrcMobile": "https://placehold.co/600x400/FFF3E0/E65100?text=Home+Combo", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}}, "internetCard4Title": {"desktop": {"text": "Home Combo", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}, "tablet": {"text": "Home Combo", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}, "mobile": {"text": "Home Combo", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}}, "internetCard4Features": {"desktop": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Internet + TV + Di động<br>✓ Tiết kiệm đến 50%<br>✓ <PERSON>hiều lựa chọn gói</div>", "fontSize": 14, "color": "#6b7280", "textAlign": "left"}, "tablet": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Internet + TV + Di động<br>✓ Tiết kiệm đến 50%<br>✓ <PERSON>hiều lựa chọn gói</div>", "fontSize": 14, "color": "#6b7280", "textAlign": "left"}, "mobile": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Internet + TV + Di động<br>✓ Tiết kiệm đến 50%<br>✓ <PERSON>hiều lựa chọn gói</div>", "fontSize": 14, "color": "#6b7280", "textAlign": "left"}}, "internetCard4Price": {"desktop": {"text": "Từ 239.000đ", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "textAlign": "left", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "tablet": {"text": "Từ 239.000đ", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "textAlign": "left", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "mobile": {"text": "Từ 239.000đ", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "textAlign": "left", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}}, "internetCard4Buttons": {"desktop": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "tablet": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "mobile": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}}, "internetCard4DetailBtn": {"desktop": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}}, "internetCard4ConsultBtn": {"desktop": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}}}, "displayName": "Internet & TV Section", "custom": {}, "parent": "7JaWi0ge_8", "hidden": false, "nodes": [], "linkedNodes": {"container": "26otxoiYgM"}}, "PdaX0mqq1P": {"type": {"resolvedName": "FamilySecuritySection"}, "isCanvas": false, "props": {"container": {"height": "auto", "background": "#FFFFFF", "padding": ["0", "0", "0", "0"]}, "securitySection": {"desktop": {"padding": {"top": 0, "bottom": 24, "left": 0, "right": 0}, "colWidths": [12], "gap": 6, "height": "auto", "alignItems": "center", "justifyContent": "center", "width": "100%"}, "tablet": {"padding": {"top": 0, "bottom": 24, "left": 0, "right": 0}, "colWidths": [12], "gap": 6, "height": "auto", "alignItems": "center", "justifyContent": "center", "width": "100%"}, "mobile": {"padding": {"top": 0, "bottom": 24, "left": 0, "right": 0}, "colWidths": [12], "gap": 6, "height": "auto", "alignItems": "center", "justifyContent": "center", "width": "100%"}}, "securityHeading": {"desktop": {"text": "#An ninh <PERSON>ia đình", "fontSize": 24, "fontWeight": 700, "color": "#374151", "textAlign": "left"}, "tablet": {"text": "#An ninh <PERSON>ia đình", "fontSize": 22, "fontWeight": 700, "color": "#374151", "textAlign": "left"}, "mobile": {"text": "#An ninh <PERSON>ia đình", "fontSize": 20, "fontWeight": 700, "color": "#374151", "textAlign": "left"}}, "securityCards": {"desktop": {"colWidths": [3, 3, 3, 3], "padding": {"top": 0, "bottom": 24, "left": 0, "right": 0}, "gap": 6, "height": "auto", "alignItems": "stretch", "justifyContent": "center", "width": "100%"}, "tablet": {"colWidths": [6, 6], "gap": 6, "height": "auto", "alignItems": "stretch", "justifyContent": "center", "width": "100%", "isBreakLine": true}, "mobile": {"colWidths": [12], "gap": 6, "height": "auto", "alignItems": "stretch", "justifyContent": "center", "width": "100%", "isBreakLine": true}}, "sectionTitle": {"desktop": {"margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}}, "tablet": {"margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}}, "mobile": {"margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}}}, "securityCard": {"desktop": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "tablet": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "mobile": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}}, "productCard": {"desktop": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column"}, "tablet": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column"}, "mobile": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column"}}, "securityCard1Image": {"desktop": {"imgSrc": "https://placehold.co/600x400/E8F5E9/1B5E20?text=Home+Camera", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "tablet": {"imgSrc": "https://placehold.co/600x400/E8F5E9/1B5E20?text=Home+Camera", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "mobile": {"imgSrcMobile": "https://placehold.co/600x400/E8F5E9/1B5E20?text=Home+Camera", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}}, "securityCard1Title": {"desktop": {"text": "Home Camera", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}, "tablet": {"text": "Home Camera", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}, "mobile": {"text": "Home Camera", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}}, "securityCard1Features": {"desktop": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Internet tốc độ cao<br>✓ Tặng 01 Camera trong nhà<br>✓ <PERSON><PERSON>u trữ cloud an toàn</div>", "fontSize": 14, "color": "#6b7280", "textAlign": "left"}, "tablet": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Internet tốc độ cao<br>✓ Tặng 01 Camera trong nhà<br>✓ <PERSON><PERSON>u trữ cloud an toàn</div>", "fontSize": 14, "color": "#6b7280", "textAlign": "left"}, "mobile": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Internet tốc độ cao<br>✓ Tặng 01 Camera trong nhà<br>✓ <PERSON><PERSON>u trữ cloud an toàn</div>", "fontSize": 14, "color": "#6b7280", "textAlign": "left"}}, "securityCard1Price": {"desktop": {"text": "210.000đ<span style=\"font-size: 16px; font-weight: 400;\">/tháng</span>", "fontSize": 24, "fontWeight": 700, "color": "#f7941e", "textAlign": "left", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "tablet": {"text": "210.000đ<span style=\"font-size: 16px; font-weight: 400;\">/tháng</span>", "fontSize": 24, "fontWeight": 700, "color": "#f7941e", "textAlign": "left", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "mobile": {"text": "210.000đ<span style=\"font-size: 16px; font-weight: 400;\">/tháng</span>", "fontSize": 24, "fontWeight": 700, "color": "#f7941e", "textAlign": "left", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}}, "securityCard1Buttons": {"desktop": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "tablet": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "mobile": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}}, "securityCard1DetailBtn": {"desktop": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}}, "securityCard1BuyBtn": {"desktop": {"text": "<PERSON><PERSON> ngay", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON><PERSON> ngay", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON><PERSON> ngay", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}}, "securityCard2Image": {"desktop": {"imgSrc": "https://placehold.co/600x400/E8F5E9/1B5E20?text=SmartHome+Chung+Cư", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "tablet": {"imgSrc": "https://placehold.co/600x400/E8F5E9/1B5E20?text=SmartHome+Chung+Cư", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "mobile": {"imgSrcMobile": "https://placehold.co/600x400/E8F5E9/1B5E20?text=SmartHome+Chung+Cư", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}}, "securityCard2Title": {"desktop": {"text": "SmartHome - <PERSON><PERSON><PERSON>", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}, "tablet": {"text": "SmartHome - <PERSON><PERSON><PERSON>", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}, "mobile": {"text": "SmartHome - <PERSON><PERSON><PERSON>", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}}, "securityCard2Features": {"desktop": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Đi<PERSON>u khiển chiếu sáng, rèm<br>✓ <PERSON><PERSON><PERSON> biến cửa, an ninh<br>✓ <PERSON><PERSON><PERSON> hợp trợ lý ảo</div>", "fontSize": 14, "color": "#6b7280", "textAlign": "left"}, "tablet": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Đi<PERSON>u khiển chiếu sáng, rèm<br>✓ <PERSON><PERSON><PERSON> biến cửa, an ninh<br>✓ <PERSON><PERSON><PERSON> hợp trợ lý ảo</div>", "fontSize": 14, "color": "#6b7280", "textAlign": "left"}, "mobile": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Đi<PERSON>u khiển chiếu sáng, rèm<br>✓ <PERSON><PERSON><PERSON> biến cửa, an ninh<br>✓ <PERSON><PERSON><PERSON> hợp trợ lý ảo</div>", "fontSize": 14, "color": "#6b7280", "textAlign": "left"}}, "securityCard2Price": {"desktop": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "textAlign": "left", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "tablet": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "textAlign": "left", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "mobile": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "textAlign": "left", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}}, "securityCard2Buttons": {"desktop": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "tablet": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "mobile": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}}, "securityCard2DetailBtn": {"desktop": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}}, "securityCard2ConsultBtn": {"desktop": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}}, "securityCard3Image": {"desktop": {"imgSrc": "https://placehold.co/600x400/E8F5E9/1B5E20?text=SmartHome+Nhà+Phố", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "tablet": {"imgSrc": "https://placehold.co/600x400/E8F5E9/1B5E20?text=SmartHome+Nhà+Phố", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "mobile": {"imgSrcMobile": "https://placehold.co/600x400/E8F5E9/1B5E20?text=SmartHome+Nhà+Phố", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}}, "securityCard3Title": {"desktop": {"text": "SmartHome - Nhà phố", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}, "tablet": {"text": "SmartHome - Nhà phố", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}, "mobile": {"text": "SmartHome - Nhà phố", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}}, "securityCard3Features": {"desktop": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Giải pháp toàn diện cho nhà phố<br>✓ Điều khiển đa vùng<br>✓ <PERSON><PERSON><PERSON> hợp hệ thống an ninh</div>", "fontSize": 14, "color": "#6b7280", "textAlign": "left"}, "tablet": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Giải pháp toàn diện cho nhà phố<br>✓ Điều khiển đa vùng<br>✓ <PERSON><PERSON><PERSON> hợp hệ thống an ninh</div>", "fontSize": 14, "color": "#6b7280", "textAlign": "left"}, "mobile": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Giải pháp toàn diện cho nhà phố<br>✓ Điều khiển đa vùng<br>✓ <PERSON><PERSON><PERSON> hợp hệ thống an ninh</div>", "fontSize": 14, "color": "#6b7280", "textAlign": "left"}}, "securityCard3Price": {"desktop": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "textAlign": "left", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "tablet": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "textAlign": "left", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "mobile": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "textAlign": "left", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}}, "securityCard3Buttons": {"desktop": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "tablet": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "mobile": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}}, "securityCard3DetailBtn": {"desktop": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}}, "securityCard3ConsultBtn": {"desktop": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}}, "securityCard4Image": {"desktop": {"imgSrc": "https://placehold.co/600x400/E8F5E9/1B5E20?text=SmartHome+Biệt+Thự", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "tablet": {"imgSrc": "https://placehold.co/600x400/E8F5E9/1B5E20?text=SmartHome+Biệt+Thự", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "mobile": {"imgSrcMobile": "https://placehold.co/600x400/E8F5E9/1B5E20?text=SmartHome+Biệt+Thự", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}}, "securityCard4Title": {"desktop": {"text": "SmartHome - <PERSON><PERSON><PERSON><PERSON> thự", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}, "tablet": {"text": "SmartHome - <PERSON><PERSON><PERSON><PERSON> thự", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}, "mobile": {"text": "SmartHome - <PERSON><PERSON><PERSON><PERSON> thự", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}}, "securityCard4Features": {"desktop": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Giải pháp cao cấp, sang trọng<br>✓ Tự động hóa toàn diện<br>✓ Điều khiển bằng giọng nói</div>", "fontSize": 14, "color": "#6b7280", "textAlign": "left"}, "tablet": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Giải pháp cao cấp, sang trọng<br>✓ Tự động hóa toàn diện<br>✓ Điều khiển bằng giọng nói</div>", "fontSize": 14, "color": "#6b7280", "textAlign": "left"}, "mobile": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Giải pháp cao cấp, sang trọng<br>✓ Tự động hóa toàn diện<br>✓ Điều khiển bằng giọng nói</div>", "fontSize": 14, "color": "#6b7280", "textAlign": "left"}}, "securityCard4Price": {"desktop": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "textAlign": "left", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "tablet": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "textAlign": "left", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "mobile": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "textAlign": "left", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}}, "securityCard4Buttons": {"desktop": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "tablet": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "mobile": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}}, "securityCard4DetailBtn": {"desktop": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}}, "securityCard4ConsultBtn": {"desktop": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}}}, "displayName": "Security Section", "custom": {}, "parent": "7JaWi0ge_8", "hidden": false, "nodes": [], "linkedNodes": {"container": "7CuIk5w2OQ"}}, "H8DjWTI7io": {"type": {"resolvedName": "FamilyServiceSection"}, "isCanvas": false, "props": {"container": {"height": "auto", "background": "#FFFFFF", "padding": ["0", "0", "0", "0"]}, "familyServiceSection": {"desktop": {"padding": {"top": 0, "bottom": 24, "left": 0, "right": 0}, "colWidths": [12], "gap": 6, "height": "auto", "alignItems": "center", "justifyContent": "center", "width": "100%"}, "tablet": {"padding": {"top": 0, "bottom": 24, "left": 0, "right": 0}, "colWidths": [12], "gap": 6, "height": "auto", "alignItems": "center", "justifyContent": "center", "width": "100%"}, "mobile": {"padding": {"top": 0, "bottom": 24, "left": 0, "right": 0}, "colWidths": [12], "gap": 6, "height": "auto", "alignItems": "center", "justifyContent": "center", "width": "100%"}}, "sectionTitle": {"desktop": {"margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}}, "tablet": {"margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}}, "mobile": {"margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}}}, "familyServiceHeading": {"desktop": {"text": "#<PERSON><PERSON><PERSON> vụ Gia <PERSON>nh", "fontSize": 24, "fontWeight": 700, "color": "#374151", "textAlign": "left"}, "tablet": {"text": "#<PERSON><PERSON><PERSON> vụ Gia <PERSON>nh", "fontSize": 22, "fontWeight": 700, "color": "#374151", "textAlign": "left"}, "mobile": {"text": "#<PERSON><PERSON><PERSON> vụ Gia <PERSON>nh", "fontSize": 20, "fontWeight": 700, "color": "#374151", "textAlign": "left"}}, "familyServiceCards": {"desktop": {"colWidths": [4, 4, 4], "padding": {"top": 0, "bottom": 24, "left": 0, "right": 0}, "gap": 6, "height": "auto", "alignItems": "stretch", "justifyContent": "center", "width": "100%"}, "tablet": {"colWidths": [6, 6], "gap": 6, "height": "auto", "alignItems": "stretch", "justifyContent": "center", "width": "100%", "isBreakLine": true}, "mobile": {"colWidths": [12], "gap": 6, "height": "auto", "alignItems": "stretch", "justifyContent": "center", "width": "100%", "isBreakLine": true}}, "familyServiceCard": {"desktop": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "tablet": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "mobile": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}}, "productCard": {"desktop": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "tablet": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "mobile": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}}, "familyServiceCard1Image": {"desktop": {"imgSrc": "https://placehold.co/600x400/F3E5F5/4A148C?text=Sửa+chữa", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "tablet": {"imgSrc": "https://placehold.co/600x400/F3E5F5/4A148C?text=Sửa+chữa", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "mobile": {"imgSrcMobile": "https://placehold.co/600x400/F3E5F5/4A148C?text=Sửa+chữa", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}}, "familyServiceCard1Title": {"desktop": {"text": "<PERSON><PERSON><PERSON> v<PERSON> ch<PERSON>a", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}, "tablet": {"text": "<PERSON><PERSON><PERSON> v<PERSON> ch<PERSON>a", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}, "mobile": {"text": "<PERSON><PERSON><PERSON> v<PERSON> ch<PERSON>a", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}}, "familyServiceCard1Features": {"desktop": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ <PERSON><PERSON><PERSON> chữa, bảo trì tại nhà<br>✓ <PERSON><PERSON> dụng cho TV, m<PERSON><PERSON> lạnh, tủ lạnh...<br>✓ <PERSON><PERSON><PERSON> ngũ chuyên nghiệp, tin cậy</div>", "fontSize": 14, "color": "#6b7280", "textAlign": "left"}, "tablet": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ <PERSON><PERSON><PERSON> chữa, bảo trì tại nhà<br>✓ <PERSON><PERSON> dụng cho TV, m<PERSON><PERSON> lạnh, tủ lạnh...<br>✓ <PERSON><PERSON><PERSON> ngũ chuyên nghiệp, tin cậy</div>", "fontSize": 14, "color": "#6b7280", "textAlign": "left"}, "mobile": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ <PERSON><PERSON><PERSON> chữa, bảo trì tại nhà<br>✓ <PERSON><PERSON> dụng cho TV, m<PERSON><PERSON> lạnh, tủ lạnh...<br>✓ <PERSON><PERSON><PERSON> ngũ chuyên nghiệp, tin cậy</div>", "fontSize": 14, "color": "#6b7280", "textAlign": "left"}}, "familyServiceCard1Price": {"desktop": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "textAlign": "left", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "tablet": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "textAlign": "left", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "mobile": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "textAlign": "left", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}}, "familyServiceCard1Buttons": {"desktop": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "tablet": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "mobile": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}}, "familyServiceCard1DetailBtn": {"desktop": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}}, "familyServiceCard1RequestBtn": {"desktop": {"text": "<PERSON><PERSON><PERSON> c<PERSON>", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON><PERSON><PERSON> c<PERSON>", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON><PERSON><PERSON> c<PERSON>", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}}, "familyServiceCard2Image": {"desktop": {"imgSrc": "https://placehold.co/600x400/F3E5F5/4A148C?text=Bảo+hành", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "tablet": {"imgSrc": "https://placehold.co/600x400/F3E5F5/4A148C?text=Bảo+hành", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "mobile": {"imgSrcMobile": "https://placehold.co/600x400/F3E5F5/4A148C?text=Bảo+hành", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}}, "familyServiceCard2Title": {"desktop": {"text": "<PERSON><PERSON><PERSON> hành Mở rộng", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}, "tablet": {"text": "<PERSON><PERSON><PERSON> hành Mở rộng", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}, "mobile": {"text": "<PERSON><PERSON><PERSON> hành Mở rộng", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}}, "familyServiceCard2Features": {"desktop": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Kéo dài thời gian bảo hành<br>✓ An tâm sử dụng thiết bị<br>✓ Áp dụng cho nhiều sản phẩm</div>", "fontSize": 14, "color": "#6b7280", "textAlign": "left"}, "tablet": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Kéo dài thời gian bảo hành<br>✓ An tâm sử dụng thiết bị<br>✓ Áp dụng cho nhiều sản phẩm</div>", "fontSize": 14, "color": "#6b7280", "textAlign": "left"}, "mobile": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Kéo dài thời gian bảo hành<br>✓ An tâm sử dụng thiết bị<br>✓ Áp dụng cho nhiều sản phẩm</div>", "fontSize": 14, "color": "#6b7280", "textAlign": "left"}}, "familyServiceCard2Price": {"desktop": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "textAlign": "left", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "tablet": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "textAlign": "left", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "mobile": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "textAlign": "left", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}}, "familyServiceCard2Buttons": {"desktop": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "tablet": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "mobile": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}}, "familyServiceCard2DetailBtn": {"desktop": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}}, "familyServiceCard2ConsultBtn": {"desktop": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}}, "familyServiceCard3Image": {"desktop": {"imgSrc": "https://placehold.co/600x400/F3E5F5/4A148C?text=Bảo+hiểm", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "tablet": {"imgSrc": "https://placehold.co/600x400/F3E5F5/4A148C?text=Bảo+hiểm", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "mobile": {"imgSrcMobile": "https://placehold.co/600x400/F3E5F5/4A148C?text=Bảo+hiểm", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}}, "familyServiceCard3Title": {"desktop": {"text": "<PERSON><PERSON><PERSON> bị", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}, "tablet": {"text": "<PERSON><PERSON><PERSON> bị", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}, "mobile": {"text": "<PERSON><PERSON><PERSON> bị", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}}, "familyServiceCard3Features": {"desktop": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ B<PERSON>o vệ thiết bị khỏi rơi vỡ, v<PERSON><PERSON> nước<br>✓ <PERSON><PERSON><PERSON> tục đơn giản, <PERSON><PERSON><PERSON> chóng</div>", "fontSize": 14, "color": "#6b7280", "textAlign": "left"}, "tablet": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ B<PERSON>o vệ thiết bị khỏi rơi vỡ, v<PERSON><PERSON> nước<br>✓ <PERSON><PERSON><PERSON> tục đơn giản, <PERSON><PERSON><PERSON> chóng</div>", "fontSize": 14, "color": "#6b7280", "textAlign": "left"}, "mobile": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ B<PERSON>o vệ thiết bị khỏi rơi vỡ, v<PERSON><PERSON> nước<br>✓ <PERSON><PERSON><PERSON> tục đơn giản, <PERSON><PERSON><PERSON> chóng</div>", "fontSize": 14, "color": "#6b7280", "textAlign": "left"}}, "familyServiceCard3Price": {"desktop": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "textAlign": "left", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "tablet": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "textAlign": "left", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "mobile": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "textAlign": "left", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}}, "familyServiceCard3Buttons": {"desktop": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "tablet": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "mobile": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}}, "familyServiceCard3DetailBtn": {"desktop": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}}, "familyServiceCard3ConsultBtn": {"desktop": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}}}, "displayName": "Family Service Section", "custom": {}, "parent": "7JaWi0ge_8", "hidden": false, "nodes": [], "linkedNodes": {"container": "RrRtqqdmng"}}, "TjMVaVHfF1": {"type": {"resolvedName": "SmartDevicesSection"}, "isCanvas": false, "props": {"smartDevicesSection": {"desktop": {"padding": {"top": 0, "bottom": 24, "left": 0, "right": 0}, "colWidths": [12], "gap": 6, "height": "auto", "alignItems": "center", "justifyContent": "center", "width": "100%"}, "tablet": {"padding": {"top": 0, "bottom": 24, "left": 0, "right": 0}, "colWidths": [12], "gap": 6, "height": "auto", "alignItems": "center", "justifyContent": "center", "width": "100%"}, "mobile": {"padding": {"top": 0, "bottom": 24, "left": 0, "right": 0}, "colWidths": [12], "gap": 6, "height": "auto", "alignItems": "center", "justifyContent": "center", "width": "100%"}}, "sectionTitle": {"desktop": {"margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}}, "tablet": {"margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}}, "mobile": {"margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}}}, "smartDevicesHeading": {"desktop": {"text": "#<PERSON><PERSON><PERSON><PERSON> bị thông minh", "fontSize": 24, "fontWeight": 700, "color": "#374151", "textAlign": "left"}, "tablet": {"text": "#<PERSON><PERSON><PERSON><PERSON> bị thông minh", "fontSize": 22, "fontWeight": 700, "color": "#374151", "textAlign": "left"}, "mobile": {"text": "#<PERSON><PERSON><PERSON><PERSON> bị thông minh", "fontSize": 20, "fontWeight": 700, "color": "#374151", "textAlign": "left"}}, "smartDevicesCards": {"desktop": {"colWidths": [3, 3, 3, 3], "padding": {"top": 0, "bottom": 24, "left": 0, "right": 0}, "gap": 6, "height": "auto", "alignItems": "stretch", "justifyContent": "center", "width": "100%"}, "tablet": {"colWidths": [6, 6], "gap": 6, "height": "auto", "alignItems": "stretch", "justifyContent": "center", "width": "100%", "isBreakLine": true}, "mobile": {"colWidths": [12], "gap": 6, "height": "auto", "alignItems": "stretch", "justifyContent": "center", "width": "100%", "isBreakLine": true}}, "productCard": {"desktop": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "tablet": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "mobile": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}}, "smartDevicesCard": {"desktop": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "100%", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "tablet": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "100%", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "mobile": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}, "gap": 0, "height": "100%", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}}, "smartDevicesCard1Image": {"desktop": {"imgSrc": "https://placehold.co/600x400/F1F8E9/33691E?text=Đèn+Thông+Minh", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "tablet": {"imgSrc": "https://placehold.co/600x400/F1F8E9/33691E?text=Đèn+Thông+Minh", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "mobile": {"imgSrcMobile": "https://placehold.co/600x400/F1F8E9/33691E?text=Đèn+Thông+Minh", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}}, "smartDevicesCard1Title": {"desktop": {"text": "<PERSON><PERSON><PERSON> thông <PERSON>h", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}, "tablet": {"text": "<PERSON><PERSON><PERSON> thông <PERSON>h", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}, "mobile": {"text": "<PERSON><PERSON><PERSON> thông <PERSON>h", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}}, "smartDevicesCard1Features": {"desktop": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Thay đổi màu sắc, đ<PERSON> sáng<br>✓ <PERSON><PERSON><PERSON><PERSON> khiển qua điện thoại</div>", "fontSize": 14, "color": "#6b7280", "textAlign": "left"}, "tablet": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Thay đổi màu sắc, đ<PERSON> sáng<br>✓ <PERSON><PERSON><PERSON><PERSON> khiển qua điện thoại</div>", "fontSize": 14, "color": "#6b7280", "textAlign": "left"}, "mobile": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Thay đổi màu sắc, đ<PERSON> sáng<br>✓ <PERSON><PERSON><PERSON><PERSON> khiển qua điện thoại</div>", "fontSize": 14, "color": "#6b7280", "textAlign": "left"}}, "smartDevicesCard1Price": {"desktop": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "textAlign": "left", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "tablet": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "textAlign": "left", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "mobile": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "textAlign": "left", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}}, "smartDevicesCard1Buttons": {"desktop": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "tablet": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "mobile": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}}, "smartDevicesCard1DetailBtn": {"desktop": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}}, "smartDevicesCard1ConsultBtn": {"desktop": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}}, "smartDevicesCard2Image": {"desktop": {"imgSrc": "https://placehold.co/600x400/F1F8E9/33691E?text=Công+tắc", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "tablet": {"imgSrc": "https://placehold.co/600x400/F1F8E9/33691E?text=Công+tắc", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "mobile": {"imgSrcMobile": "https://placehold.co/600x400/F1F8E9/33691E?text=Công+tắc", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}}, "smartDevicesCard2Title": {"desktop": {"text": "<PERSON><PERSON><PERSON> tắc thông minh", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}, "tablet": {"text": "<PERSON><PERSON><PERSON> tắc thông minh", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}, "mobile": {"text": "<PERSON><PERSON><PERSON> tắc thông minh", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}}, "smartDevicesCard2Features": {"desktop": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Bật/tắt từ xa<br>✓ <PERSON><PERSON><PERSON><PERSON> kế sang trọng</div>", "fontSize": 14, "color": "#6b7280", "textAlign": "left"}, "tablet": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Bật/tắt từ xa<br>✓ <PERSON><PERSON><PERSON><PERSON> kế sang trọng</div>", "fontSize": 14, "color": "#6b7280", "textAlign": "left"}, "mobile": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Bật/tắt từ xa<br>✓ <PERSON><PERSON><PERSON><PERSON> kế sang trọng</div>", "fontSize": 14, "color": "#6b7280", "textAlign": "left"}}, "smartDevicesCard2Price": {"desktop": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "textAlign": "left", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "tablet": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "textAlign": "left", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "mobile": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "textAlign": "left", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}}, "smartDevicesCard2Buttons": {"desktop": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "tablet": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "mobile": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}}, "smartDevicesCard2DetailBtn": {"desktop": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}}, "smartDevicesCard2ConsultBtn": {"desktop": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}}, "smartDevicesCard3Image": {"desktop": {"imgSrc": "https://placehold.co/600x400/F1F8E9/33691E?text=Ổ+cắm", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "tablet": {"imgSrc": "https://placehold.co/600x400/F1F8E9/33691E?text=Ổ+cắm", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "mobile": {"imgSrcMobile": "https://placehold.co/600x400/F1F8E9/33691E?text=Ổ+cắm", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}}, "smartDevicesCard3Title": {"desktop": {"text": "Ổ cắm thông minh", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}, "tablet": {"text": "Ổ cắm thông minh", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}, "mobile": {"text": "Ổ cắm thông minh", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}}, "smartDevicesCard3Features": {"desktop": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ B<PERSON><PERSON>n thiết bị thường thành thông minh<br>✓ Hẹn giờ bật/tắt</div>", "fontSize": 14, "color": "#6b7280", "textAlign": "left"}, "tablet": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ B<PERSON><PERSON>n thiết bị thường thành thông minh<br>✓ Hẹn giờ bật/tắt</div>", "fontSize": 14, "color": "#6b7280", "textAlign": "left"}, "mobile": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ B<PERSON><PERSON>n thiết bị thường thành thông minh<br>✓ Hẹn giờ bật/tắt</div>", "fontSize": 14, "color": "#6b7280", "textAlign": "left"}}, "smartDevicesCard3Price": {"desktop": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "textAlign": "left", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "tablet": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "textAlign": "left", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "mobile": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "textAlign": "left", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}}, "smartDevicesCard3Buttons": {"desktop": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "tablet": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "mobile": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}}, "smartDevicesCard3DetailBtn": {"desktop": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}}, "smartDevicesCard3ConsultBtn": {"desktop": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}}, "smartDevicesCard4Image": {"desktop": {"imgSrc": "https://placehold.co/600x400/F1F8E9/33691E?text=Rèm+cửa", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "tablet": {"imgSrc": "https://placehold.co/600x400/F1F8E9/33691E?text=Rèm+cửa", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "mobile": {"imgSrcMobile": "https://placehold.co/600x400/F1F8E9/33691E?text=Rèm+cửa", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}}, "smartDevicesCard4Title": {"desktop": {"text": "<PERSON><PERSON><PERSON> c<PERSON>a thông minh", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}, "tablet": {"text": "<PERSON><PERSON><PERSON> c<PERSON>a thông minh", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}, "mobile": {"text": "<PERSON><PERSON><PERSON> c<PERSON>a thông minh", "fontSize": 18, "fontWeight": 700, "color": "#00529c", "textAlign": "left"}}, "smartDevicesCard4Features": {"desktop": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Tự động đóng/mở<br>✓ Điều khiển bằng giọng nói</div>", "fontSize": 14, "color": "#6b7280", "textAlign": "left"}, "tablet": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Tự động đóng/mở<br>✓ Điều khiển bằng giọng nói</div>", "fontSize": 14, "color": "#6b7280", "textAlign": "left"}, "mobile": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Tự động đóng/mở<br>✓ Điều khiển bằng giọng nói</div>", "fontSize": 14, "color": "#6b7280", "textAlign": "left"}}, "smartDevicesCard4Price": {"desktop": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "textAlign": "left", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "tablet": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "textAlign": "left", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "mobile": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "textAlign": "left", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}}, "smartDevicesCard4Buttons": {"desktop": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "tablet": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "mobile": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}}, "smartDevicesCard4DetailBtn": {"desktop": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}}, "smartDevicesCard4ConsultBtn": {"desktop": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}}}, "displayName": "Smart Devices Section", "custom": {}, "parent": "7JaWi0ge_8", "hidden": false, "nodes": [], "linkedNodes": {"container": "R0EAS92Oea"}}, "26otxoiYgM": {"type": {"resolvedName": "Container"}, "isCanvas": true, "props": {"flexDirection": "column", "alignItems": "flex-start", "justifyContent": "flex-start", "padding": ["0", "0", "0", "0"], "margin": ["0", "0", "0", "0"], "background": "#FFFFFF", "color": {"r": 0, "g": 0, "b": 0, "a": 1}, "shadow": 0, "radius": 0, "width": "100%", "height": "auto"}, "displayName": "Container", "custom": {}, "parent": "JfhAAguQal", "hidden": false, "nodes": ["qNSWdY46ZD"], "linkedNodes": {}}, "qNSWdY46ZD": {"type": {"resolvedName": "Row"}, "isCanvas": true, "props": {"desktop": {"padding": {"top": 0, "bottom": 24, "left": 0, "right": 0}, "colWidths": [12], "gap": 6, "height": "auto", "alignItems": "center", "contentAlign": "start", "justifyContent": "center", "width": "100%"}, "tablet": {"padding": {"top": 0, "bottom": 24, "left": 0, "right": 0}, "colWidths": [12], "gap": 6, "height": "auto", "alignItems": "center", "contentAlign": "start", "justifyContent": "center", "width": "100%"}, "mobile": {"padding": {"top": 0, "bottom": 24, "left": 0, "right": 0}, "colWidths": [12], "gap": 6, "height": "auto", "alignItems": "center", "contentAlign": "start", "justifyContent": "center", "width": "100%"}, "id": "internet-tv-section"}, "displayName": "Row", "custom": {}, "parent": "26otxoiYgM", "hidden": false, "nodes": ["T_hXqmDAb8", "VBXqydFw9d"], "linkedNodes": {}}, "T_hXqmDAb8": {"type": {"resolvedName": "Column"}, "isCanvas": true, "props": {"desktop": {"margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}}, "tablet": {"margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}}, "mobile": {"margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}}, "id": "internet-tv-heading"}, "displayName": "Column", "custom": {}, "parent": "qNSWdY46ZD", "hidden": false, "nodes": ["G4BsIdE4se"], "linkedNodes": {}}, "G4BsIdE4se": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "#Internet & Truyền hình", "fontSize": 24, "fontWeight": 700, "color": "#374151"}, "tablet": {"text": "#Internet & Truyền hình", "fontSize": 22, "fontWeight": 700, "color": "#374151"}, "mobile": {"text": "#Internet & Truyền hình", "fontSize": 20, "fontWeight": 700, "color": "#374151"}, "id": "internet-tv-heading"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "T_hXqmDAb8", "hidden": false, "nodes": [], "linkedNodes": {}}, "VBXqydFw9d": {"type": {"resolvedName": "Container"}, "isCanvas": true, "props": {"flexDirection": "column", "alignItems": "flex-start", "justifyContent": "flex-start", "padding": ["0", "0", "0", "0"], "margin": ["0", "0", "0", "0"], "background": "#FFFFFF", "color": {"r": 0, "g": 0, "b": 0, "a": 1}, "shadow": 0, "radius": 0, "width": "100%", "height": "auto", "id": "container-internet-tv"}, "displayName": "Container", "custom": {}, "parent": "qNSWdY46ZD", "hidden": false, "nodes": ["8m-yHFZySp"], "linkedNodes": {}}, "8m-yHFZySp": {"type": {"resolvedName": "Row"}, "isCanvas": true, "props": {"desktop": {"colWidths": [3, 3, 3, 3], "padding": {"top": 0, "bottom": 24, "left": 0, "right": 0}, "gap": 6, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%"}, "tablet": {"colWidths": [6, 6], "gap": 6, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "isBreakLine": true}, "mobile": {"colWidths": [12], "gap": 6, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "isBreakLine": true}, "id": "internet-tv-cards"}, "displayName": "Row", "custom": {}, "parent": "VBXqydFw9d", "hidden": false, "nodes": ["NHpLRIpcsN", "_m-b7FnN4X", "hFZGcFbwfK", "GMR8oLGKLy"], "linkedNodes": {}}, "NHpLRIpcsN": {"type": {"resolvedName": "Column"}, "isCanvas": true, "props": {"desktop": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "tablet": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "mobile": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "id": "internet-tv-card-1"}, "displayName": "Column", "custom": {}, "parent": "8m-yHFZySp", "hidden": false, "nodes": ["w4OrbU4wsY"], "linkedNodes": {}}, "w4OrbU4wsY": {"type": {"resolvedName": "ProductCard"}, "isCanvas": true, "props": {"desktop": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "tablet": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "mobile": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "id": "product1"}, "displayName": "ProductCard", "custom": {}, "parent": "NHpLRIpcsN", "hidden": false, "nodes": ["g-WOtdGbA0", "v04V2giT31"], "linkedNodes": {}}, "g-WOtdGbA0": {"type": "div", "isCanvas": false, "props": {"className": "rounded-t-lg bg-[#f1f8e9]"}, "displayName": "div", "custom": {}, "parent": "w4OrbU4wsY", "hidden": false, "nodes": ["eqVC7gXuz2"], "linkedNodes": {}}, "eqVC7gXuz2": {"type": {"resolvedName": "Image"}, "isCanvas": true, "props": {"desktop": {"imgSrc": "https://placehold.co/600x400/FFF3E0/E65100?text=Home+Mesh+2", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "tablet": {"imgSrc": "https://placehold.co/600x400/FFF3E0/E65100?text=Home+Mesh+2", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "mobile": {"imgSrcMobile": "https://placehold.co/600x400/FFF3E0/E65100?text=Home+Mesh+2", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "id": "internet-tv-card-1-image"}, "displayName": "Ảnh", "custom": {}, "parent": "g-WOtdGbA0", "hidden": false, "nodes": [], "linkedNodes": {}}, "v04V2giT31": {"type": "div", "isCanvas": false, "props": {"className": "flex h-full flex-col justify-between p-6"}, "displayName": "div", "custom": {}, "parent": "w4OrbU4wsY", "hidden": false, "nodes": ["QASW6biIJ7", "giu6cF8PAV"], "linkedNodes": {}}, "QASW6biIJ7": {"type": "div", "isCanvas": false, "props": {"className": "flex flex-col"}, "displayName": "div", "custom": {}, "parent": "v04V2giT31", "hidden": false, "nodes": ["ZCllTos5Mb", "xS7nIfV9pw"], "linkedNodes": {}}, "ZCllTos5Mb": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "Home Mesh 2+", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "tablet": {"text": "Home Mesh 2+", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "mobile": {"text": "Home Mesh 2+", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "id": "internet-tv-card-1-title"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "QASW6biIJ7", "hidden": false, "nodes": [], "linkedNodes": {}}, "xS7nIfV9pw": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Tốc độ Internet 150Mbps<br>✓ <PERSON><PERSON><PERSON><PERSON>n hình MyTV Nâng cao<br>✓ Trang bị 01 Wifi Mesh</div>", "fontSize": 14, "color": "#6b7280"}, "tablet": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Tốc độ Internet 150Mbps<br>✓ <PERSON><PERSON><PERSON><PERSON>n hình MyTV Nâng cao<br>✓ Trang bị 01 Wifi Mesh</div>", "fontSize": 14, "color": "#6b7280"}, "mobile": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Tốc độ Internet 150Mbps<br>✓ <PERSON><PERSON><PERSON><PERSON>n hình MyTV Nâng cao<br>✓ Trang bị 01 Wifi Mesh</div>", "fontSize": 14, "color": "#6b7280"}, "id": "internet-tv-card-1-features"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "QASW6biIJ7", "hidden": false, "nodes": [], "linkedNodes": {}}, "giu6cF8PAV": {"type": "div", "isCanvas": false, "props": {"className": "flex flex-col"}, "displayName": "div", "custom": {}, "parent": "v04V2giT31", "hidden": false, "nodes": ["0lOxX0Wyjc", "XFWzkB7k23"], "linkedNodes": {}}, "0lOxX0Wyjc": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "245.000đ<span style=\"font-size: 16px; font-weight: 400;\">/tháng</span>", "fontSize": 24, "fontWeight": 700, "color": "#f7941e", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "tablet": {"text": "245.000đ<span style=\"font-size: 16px; font-weight: 400;\">/tháng</span>", "fontSize": 24, "fontWeight": 700, "color": "#f7941e", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "mobile": {"text": "245.000đ<span style=\"font-size: 16px; font-weight: 400;\">/tháng</span>", "fontSize": 24, "fontWeight": 700, "color": "#f7941e", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "id": "internet-tv-card-1-price"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "giu6cF8PAV", "hidden": false, "nodes": [], "linkedNodes": {}}, "XFWzkB7k23": {"type": {"resolvedName": "Row"}, "isCanvas": true, "props": {"desktop": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "tablet": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "mobile": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "id": "internet-tv-card-1-buttons"}, "displayName": "Row", "custom": {}, "parent": "giu6cF8PAV", "hidden": false, "nodes": ["e0Y-p6H7Rf", "-oOBNGIzSY"], "linkedNodes": {}}, "e0Y-p6H7Rf": {"type": {"resolvedName": "<PERSON><PERSON>"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "id": "internet-tv-card-1-detail-btn"}, "displayName": "<PERSON><PERSON>", "custom": {}, "parent": "XFWzkB7k23", "hidden": false, "nodes": [], "linkedNodes": {}}, "-oOBNGIzSY": {"type": {"resolvedName": "<PERSON><PERSON>"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON> ngay", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON><PERSON> ngay", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON><PERSON> ngay", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "id": "internet-tv-card-1-buy-btn"}, "displayName": "<PERSON><PERSON>", "custom": {}, "parent": "XFWzkB7k23", "hidden": false, "nodes": [], "linkedNodes": {}}, "_m-b7FnN4X": {"type": {"resolvedName": "Column"}, "isCanvas": true, "props": {"desktop": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "tablet": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "mobile": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "id": "internet-tv-card-2"}, "displayName": "Column", "custom": {}, "parent": "8m-yHFZySp", "hidden": false, "nodes": ["iWaEvXWmg3"], "linkedNodes": {}}, "iWaEvXWmg3": {"type": {"resolvedName": "ProductCard"}, "isCanvas": true, "props": {"desktop": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "tablet": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "mobile": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "id": "product2"}, "displayName": "ProductCard", "custom": {}, "parent": "_m-b7FnN4X", "hidden": false, "nodes": ["kMmJ1JzmTf", "93arZV8zP1"], "linkedNodes": {}}, "kMmJ1JzmTf": {"type": "div", "isCanvas": false, "props": {"className": "rounded-t-lg bg-[#f1f8e9]"}, "displayName": "div", "custom": {}, "parent": "iWaEvXWmg3", "hidden": false, "nodes": ["PbjS4qtv6r"], "linkedNodes": {}}, "PbjS4qtv6r": {"type": {"resolvedName": "Image"}, "isCanvas": true, "props": {"desktop": {"imgSrc": "https://placehold.co/600x400/FFF3E0/E65100?text=Home+Net", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "tablet": {"imgSrc": "https://placehold.co/600x400/FFF3E0/E65100?text=Home+Net", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "mobile": {"imgSrcMobile": "https://placehold.co/600x400/FFF3E0/E65100?text=Home+Net", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "id": "internet-tv-card-2-image"}, "displayName": "Ảnh", "custom": {}, "parent": "kMmJ1JzmTf", "hidden": false, "nodes": [], "linkedNodes": {}}, "93arZV8zP1": {"type": "div", "isCanvas": false, "props": {"className": "flex h-full flex-col justify-between p-6"}, "displayName": "div", "custom": {}, "parent": "iWaEvXWmg3", "hidden": false, "nodes": ["lVMvDQ-Bml", "P4-tqU8sg6"], "linkedNodes": {}}, "lVMvDQ-Bml": {"type": "div", "isCanvas": false, "props": {"className": "flex flex-col"}, "displayName": "div", "custom": {}, "parent": "93arZV8zP1", "hidden": false, "nodes": ["HNzkg5LPG0", "NGFyOTHFuy"], "linkedNodes": {}}, "HNzkg5LPG0": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "Home Net 1", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "tablet": {"text": "Home Net 1", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "mobile": {"text": "Home Net 1", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "id": "internet-tv-card-2-title"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "lVMvDQ-Bml", "hidden": false, "nodes": [], "linkedNodes": {}}, "NGFyOTHFuy": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Tốc độ Internet 100Mbps<br>✓ <PERSON><PERSON> hợp cho cá nhân, gia đình nhỏ</div>", "fontSize": 14, "color": "#6b7280"}, "tablet": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Tốc độ Internet 100Mbps<br>✓ <PERSON><PERSON> hợp cho cá nhân, gia đình nhỏ</div>", "fontSize": 14, "color": "#6b7280"}, "mobile": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Tốc độ Internet 100Mbps<br>✓ <PERSON><PERSON> hợp cho cá nhân, gia đình nhỏ</div>", "fontSize": 14, "color": "#6b7280"}, "id": "internet-tv-card-2-features"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "lVMvDQ-Bml", "hidden": false, "nodes": [], "linkedNodes": {}}, "P4-tqU8sg6": {"type": "div", "isCanvas": false, "props": {"className": "flex flex-col"}, "displayName": "div", "custom": {}, "parent": "93arZV8zP1", "hidden": false, "nodes": ["g4w9kqcSwy", "BYnRIsfu2S"], "linkedNodes": {}}, "g4w9kqcSwy": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "165.000đ<span style=\"font-size: 16px; font-weight: 400;\">/tháng</span>", "fontSize": 24, "fontWeight": 700, "color": "#f7941e", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "tablet": {"text": "165.000đ<span style=\"font-size: 16px; font-weight: 400;\">/tháng</span>", "fontSize": 24, "fontWeight": 700, "color": "#f7941e", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "mobile": {"text": "165.000đ<span style=\"font-size: 16px; font-weight: 400;\">/tháng</span>", "fontSize": 24, "fontWeight": 700, "color": "#f7941e", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "id": "internet-tv-card-2-price"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "P4-tqU8sg6", "hidden": false, "nodes": [], "linkedNodes": {}}, "BYnRIsfu2S": {"type": {"resolvedName": "Row"}, "isCanvas": true, "props": {"desktop": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "tablet": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "mobile": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "id": "internet-tv-card-2-buttons"}, "displayName": "Row", "custom": {}, "parent": "P4-tqU8sg6", "hidden": false, "nodes": ["0t7v2nnSyk", "SjPykehUFz"], "linkedNodes": {}}, "0t7v2nnSyk": {"type": {"resolvedName": "<PERSON><PERSON>"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "id": "internet-tv-card-2-detail-btn"}, "displayName": "<PERSON><PERSON>", "custom": {}, "parent": "BYnRIsfu2S", "hidden": false, "nodes": [], "linkedNodes": {}}, "SjPykehUFz": {"type": {"resolvedName": "<PERSON><PERSON>"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON> ngay", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON><PERSON> ngay", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON><PERSON> ngay", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "id": "internet-tv-card-2-buy-btn"}, "displayName": "<PERSON><PERSON>", "custom": {}, "parent": "BYnRIsfu2S", "hidden": false, "nodes": [], "linkedNodes": {}}, "hFZGcFbwfK": {"type": {"resolvedName": "Column"}, "isCanvas": true, "props": {"desktop": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "tablet": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "mobile": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "id": "internet-tv-card-3"}, "displayName": "Column", "custom": {}, "parent": "8m-yHFZySp", "hidden": false, "nodes": ["f3RgJykykG"], "linkedNodes": {}}, "f3RgJykykG": {"type": {"resolvedName": "ProductCard"}, "isCanvas": true, "props": {"desktop": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "tablet": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "mobile": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "id": "product3"}, "displayName": "ProductCard", "custom": {}, "parent": "hFZGcFbwfK", "hidden": false, "nodes": ["r12C3mTfRZ", "M699CWmCJa"], "linkedNodes": {}}, "r12C3mTfRZ": {"type": "div", "isCanvas": false, "props": {"className": "rounded-t-lg bg-[#f1f8e9]"}, "displayName": "div", "custom": {}, "parent": "f3RgJykykG", "hidden": false, "nodes": ["QmVB1GSaD3"], "linkedNodes": {}}, "QmVB1GSaD3": {"type": {"resolvedName": "Image"}, "isCanvas": true, "props": {"desktop": {"imgSrc": "https://placehold.co/600x400/FFF3E0/E65100?text=Home+TV", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "tablet": {"imgSrc": "https://placehold.co/600x400/FFF3E0/E65100?text=Home+TV", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "mobile": {"imgSrcMobile": "https://placehold.co/600x400/FFF3E0/E65100?text=Home+TV", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "id": "internet-tv-card-3-image"}, "displayName": "Ảnh", "custom": {}, "parent": "r12C3mTfRZ", "hidden": false, "nodes": [], "linkedNodes": {}}, "M699CWmCJa": {"type": "div", "isCanvas": false, "props": {"className": "flex h-full flex-col justify-between p-6"}, "displayName": "div", "custom": {}, "parent": "f3RgJykykG", "hidden": false, "nodes": ["qjMrfBtXgU", "Vb1OxVliln"], "linkedNodes": {}}, "qjMrfBtXgU": {"type": "div", "isCanvas": false, "props": {"className": "flex flex-col"}, "displayName": "div", "custom": {}, "parent": "M699CWmCJa", "hidden": false, "nodes": ["GQsufgGIVY", "nefdNqHHmF"], "linkedNodes": {}}, "GQsufgGIVY": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "Home TV 1", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "tablet": {"text": "Home TV 1", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "mobile": {"text": "Home TV 1", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "id": "internet-tv-card-3-title"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "qjMrfBtXgU", "hidden": false, "nodes": [], "linkedNodes": {}}, "nefdNqHHmF": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Tốc độ Internet 80Mbps<br>✓ T<PERSON><PERSON>ền hình MyTV Chuẩn<br>✓ <PERSON><PERSON><PERSON> cước tiết kiệm</div>", "fontSize": 14, "color": "#6b7280"}, "tablet": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Tốc độ Internet 80Mbps<br>✓ T<PERSON><PERSON>ền hình MyTV Chuẩn<br>✓ <PERSON><PERSON><PERSON> cước tiết kiệm</div>", "fontSize": 14, "color": "#6b7280"}, "mobile": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Tốc độ Internet 80Mbps<br>✓ T<PERSON><PERSON>ền hình MyTV Chuẩn<br>✓ <PERSON><PERSON><PERSON> cước tiết kiệm</div>", "fontSize": 14, "color": "#6b7280"}, "id": "internet-tv-card-3-features"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "qjMrfBtXgU", "hidden": false, "nodes": [], "linkedNodes": {}}, "Vb1OxVliln": {"type": "div", "isCanvas": false, "props": {"className": "flex flex-col"}, "displayName": "div", "custom": {}, "parent": "M699CWmCJa", "hidden": false, "nodes": ["oI6Au970Qd", "dniG5vDvef"], "linkedNodes": {}}, "oI6Au970Qd": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "175.000đ<span style=\"font-size: 16px; font-weight: 400;\">/tháng</span>", "fontSize": 24, "fontWeight": 700, "color": "#f7941e", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "tablet": {"text": "175.000đ<span style=\"font-size: 16px; font-weight: 400;\">/tháng</span>", "fontSize": 24, "fontWeight": 700, "color": "#f7941e", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "mobile": {"text": "175.000đ<span style=\"font-size: 16px; font-weight: 400;\">/tháng</span>", "fontSize": 24, "fontWeight": 700, "color": "#f7941e", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "id": "internet-tv-card-3-price"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "Vb1OxVliln", "hidden": false, "nodes": [], "linkedNodes": {}}, "dniG5vDvef": {"type": {"resolvedName": "Row"}, "isCanvas": true, "props": {"desktop": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "tablet": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "mobile": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "id": "internet-tv-card-3-buttons"}, "displayName": "Row", "custom": {}, "parent": "Vb1OxVliln", "hidden": false, "nodes": ["h8B2VWk2gc", "cREErWgpFd"], "linkedNodes": {}}, "h8B2VWk2gc": {"type": {"resolvedName": "<PERSON><PERSON>"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "id": "internet-tv-card-3-detail-btn"}, "displayName": "<PERSON><PERSON>", "custom": {}, "parent": "dniG5vDvef", "hidden": false, "nodes": [], "linkedNodes": {}}, "cREErWgpFd": {"type": {"resolvedName": "<PERSON><PERSON>"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON> ngay", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON><PERSON> ngay", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON><PERSON> ngay", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "id": "internet-tv-card-3-buy-btn"}, "displayName": "<PERSON><PERSON>", "custom": {}, "parent": "dniG5vDvef", "hidden": false, "nodes": [], "linkedNodes": {}}, "GMR8oLGKLy": {"type": {"resolvedName": "Column"}, "isCanvas": true, "props": {"desktop": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "tablet": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "mobile": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "id": "internet-tv-card-4"}, "displayName": "Column", "custom": {}, "parent": "8m-yHFZySp", "hidden": false, "nodes": ["9X4GQYi80e"], "linkedNodes": {}}, "9X4GQYi80e": {"type": {"resolvedName": "ProductCard"}, "isCanvas": true, "props": {"desktop": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "tablet": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "mobile": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "id": "product4"}, "displayName": "ProductCard", "custom": {}, "parent": "GMR8oLGKLy", "hidden": false, "nodes": ["174hOYA3ye", "oXr76QReew"], "linkedNodes": {}}, "174hOYA3ye": {"type": "div", "isCanvas": false, "props": {"className": "rounded-t-lg bg-[#f1f8e9]"}, "displayName": "div", "custom": {}, "parent": "9X4GQYi80e", "hidden": false, "nodes": ["Rk8djd8vbj"], "linkedNodes": {}}, "Rk8djd8vbj": {"type": {"resolvedName": "Image"}, "isCanvas": true, "props": {"desktop": {"imgSrc": "https://placehold.co/600x400/FFF3E0/E65100?text=Home+Combo", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "tablet": {"imgSrc": "https://placehold.co/600x400/FFF3E0/E65100?text=Home+Combo", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "mobile": {"imgSrcMobile": "https://placehold.co/600x400/FFF3E0/E65100?text=Home+Combo", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "id": "internet-tv-card-4-image"}, "displayName": "Ảnh", "custom": {}, "parent": "174hOYA3ye", "hidden": false, "nodes": [], "linkedNodes": {}}, "oXr76QReew": {"type": "div", "isCanvas": false, "props": {"className": "flex h-full flex-col justify-between p-6"}, "displayName": "div", "custom": {}, "parent": "9X4GQYi80e", "hidden": false, "nodes": ["5S-Q9pwWow", "V-oSow0mdZ"], "linkedNodes": {}}, "5S-Q9pwWow": {"type": "div", "isCanvas": false, "props": {"className": "flex flex-col"}, "displayName": "div", "custom": {}, "parent": "oXr76QReew", "hidden": false, "nodes": ["CBbKq3ESaR", "uwLuaIV70U"], "linkedNodes": {}}, "CBbKq3ESaR": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "Home Combo", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "tablet": {"text": "Home Combo", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "mobile": {"text": "Home Combo", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "id": "internet-tv-card-4-title"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "5S-Q9pwWow", "hidden": false, "nodes": [], "linkedNodes": {}}, "uwLuaIV70U": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Internet + TV + Di động<br>✓ Tiết kiệm đến 50%<br>✓ <PERSON>hiều lựa chọn gói</div>", "fontSize": 14, "color": "#6b7280"}, "tablet": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Internet + TV + Di động<br>✓ Tiết kiệm đến 50%<br>✓ <PERSON>hiều lựa chọn gói</div>", "fontSize": 14, "color": "#6b7280"}, "mobile": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Internet + TV + Di động<br>✓ Tiết kiệm đến 50%<br>✓ <PERSON>hiều lựa chọn gói</div>", "fontSize": 14, "color": "#6b7280"}, "id": "internet-tv-card-4-features"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "5S-Q9pwWow", "hidden": false, "nodes": [], "linkedNodes": {}}, "V-oSow0mdZ": {"type": "div", "isCanvas": false, "props": {"className": "flex flex-col"}, "displayName": "div", "custom": {}, "parent": "oXr76QReew", "hidden": false, "nodes": ["1NU9vyS7Ud", "7iaedJVuKl"], "linkedNodes": {}}, "1NU9vyS7Ud": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "Từ 239.000đ", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "tablet": {"text": "Từ 239.000đ", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "mobile": {"text": "Từ 239.000đ", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "id": "internet-tv-card-4-price"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "V-oSow0mdZ", "hidden": false, "nodes": [], "linkedNodes": {}}, "7iaedJVuKl": {"type": {"resolvedName": "Row"}, "isCanvas": true, "props": {"desktop": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "tablet": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "mobile": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "id": "internet-tv-card-4-buttons"}, "displayName": "Row", "custom": {}, "parent": "V-oSow0mdZ", "hidden": false, "nodes": ["jyH78kV0qp", "wY_ij0fBnC"], "linkedNodes": {}}, "jyH78kV0qp": {"type": {"resolvedName": "<PERSON><PERSON>"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "id": "internet-tv-card-4-detail-btn"}, "displayName": "<PERSON><PERSON>", "custom": {}, "parent": "7iaedJVuKl", "hidden": false, "nodes": [], "linkedNodes": {}}, "wY_ij0fBnC": {"type": {"resolvedName": "<PERSON><PERSON>"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "id": "internet-tv-card-4-consult-btn"}, "displayName": "<PERSON><PERSON>", "custom": {}, "parent": "7iaedJVuKl", "hidden": false, "nodes": [], "linkedNodes": {}}, "7CuIk5w2OQ": {"type": {"resolvedName": "Container"}, "isCanvas": true, "props": {"flexDirection": "column", "alignItems": "flex-start", "justifyContent": "flex-start", "padding": ["0", "0", "0", "0"], "margin": ["0", "0", "0", "0"], "background": "#FFFFFF", "color": {"r": 0, "g": 0, "b": 0, "a": 1}, "shadow": 0, "radius": 0, "width": "100%", "height": "auto"}, "displayName": "Container", "custom": {}, "parent": "PdaX0mqq1P", "hidden": false, "nodes": ["Nh5ghRiKX3"], "linkedNodes": {}}, "Nh5ghRiKX3": {"type": {"resolvedName": "Row"}, "isCanvas": true, "props": {"desktop": {"padding": {"top": 0, "bottom": 24, "left": 0, "right": 0}, "colWidths": [12], "gap": 6, "height": "auto", "alignItems": "center", "justifyContent": "center", "width": "100%"}, "tablet": {"padding": {"top": 0, "bottom": 24, "left": 0, "right": 0}, "colWidths": [12], "gap": 6, "height": "auto", "alignItems": "center", "justifyContent": "center", "width": "100%"}, "mobile": {"padding": {"top": 0, "bottom": 24, "left": 0, "right": 0}, "colWidths": [12], "gap": 6, "height": "auto", "alignItems": "center", "justifyContent": "center", "width": "100%"}, "id": "security-section"}, "displayName": "Row", "custom": {}, "parent": "7CuIk5w2OQ", "hidden": false, "nodes": ["PidhXRUin3", "a_EI-HpqA9"], "linkedNodes": {}}, "PidhXRUin3": {"type": {"resolvedName": "Column"}, "isCanvas": true, "props": {"desktop": {"margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}}, "tablet": {"margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}}, "mobile": {"margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}}, "id": "security-heading"}, "displayName": "Column", "custom": {}, "parent": "Nh5ghRiKX3", "hidden": false, "nodes": ["oJwnnv3DLK"], "linkedNodes": {}}, "oJwnnv3DLK": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "#An ninh <PERSON>ia đình", "fontSize": 24, "fontWeight": 700, "color": "#374151"}, "tablet": {"text": "#An ninh <PERSON>ia đình", "fontSize": 22, "fontWeight": 700, "color": "#374151"}, "mobile": {"text": "#An ninh <PERSON>ia đình", "fontSize": 20, "fontWeight": 700, "color": "#374151"}, "id": "security-heading"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "PidhXRUin3", "hidden": false, "nodes": [], "linkedNodes": {}}, "a_EI-HpqA9": {"type": {"resolvedName": "Container"}, "isCanvas": true, "props": {"flexDirection": "column", "alignItems": "flex-start", "justifyContent": "flex-start", "padding": ["0", "0", "0", "0"], "margin": ["0", "0", "0", "0"], "background": "#FFFFFF", "color": {"r": 0, "g": 0, "b": 0, "a": 1}, "shadow": 0, "radius": 0, "width": "100%", "height": "auto", "id": "container-security"}, "displayName": "Container", "custom": {}, "parent": "Nh5ghRiKX3", "hidden": false, "nodes": ["nRVgztU9_V"], "linkedNodes": {}}, "nRVgztU9_V": {"type": {"resolvedName": "Row"}, "isCanvas": true, "props": {"desktop": {"colWidths": [3, 3, 3, 3], "padding": {"top": 0, "bottom": 24, "left": 0, "right": 0}, "gap": 6, "height": "auto", "alignItems": "stretch", "justifyContent": "center", "width": "100%"}, "tablet": {"colWidths": [6, 6], "gap": 6, "height": "auto", "alignItems": "stretch", "justifyContent": "center", "width": "100%", "isBreakLine": true}, "mobile": {"colWidths": [12], "gap": 6, "height": "auto", "alignItems": "stretch", "justifyContent": "center", "width": "100%", "isBreakLine": true}, "id": "security-cards"}, "displayName": "Row", "custom": {}, "parent": "a_EI-HpqA9", "hidden": false, "nodes": ["nmphAaxjjT", "IuR_5wMVAm", "kojrAntZDK", "A5L3O_Xedx"], "linkedNodes": {}}, "nmphAaxjjT": {"type": {"resolvedName": "Column"}, "isCanvas": true, "props": {"desktop": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "tablet": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "mobile": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "id": "security-card-1"}, "displayName": "Column", "custom": {}, "parent": "nRVgztU9_V", "hidden": false, "nodes": ["57tK6C3UoH"], "linkedNodes": {}}, "57tK6C3UoH": {"type": {"resolvedName": "ProductCard"}, "isCanvas": true, "props": {"desktop": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column"}, "tablet": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column"}, "mobile": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column"}, "id": "product1"}, "displayName": "ProductCard", "custom": {}, "parent": "nmphAaxjjT", "hidden": false, "nodes": ["pkKCCbejrk", "8F4m0m-E1P"], "linkedNodes": {}}, "pkKCCbejrk": {"type": "div", "isCanvas": false, "props": {"className": "rounded-t-lg bg-[#e8f5e9]"}, "displayName": "div", "custom": {}, "parent": "57tK6C3UoH", "hidden": false, "nodes": ["av_XqMTthj"], "linkedNodes": {}}, "av_XqMTthj": {"type": {"resolvedName": "Image"}, "isCanvas": true, "props": {"desktop": {"imgSrc": "https://placehold.co/600x400/E8F5E9/1B5E20?text=Home+Camera", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "tablet": {"imgSrc": "https://placehold.co/600x400/E8F5E9/1B5E20?text=Home+Camera", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "mobile": {"imgSrcMobile": "https://placehold.co/600x400/E8F5E9/1B5E20?text=Home+Camera", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "id": "security-card-1-image"}, "displayName": "Ảnh", "custom": {}, "parent": "pkKCCbejrk", "hidden": false, "nodes": [], "linkedNodes": {}}, "8F4m0m-E1P": {"type": "div", "isCanvas": false, "props": {"className": "flex h-full flex-col justify-between p-6"}, "displayName": "div", "custom": {}, "parent": "57tK6C3UoH", "hidden": false, "nodes": ["oIKSXWUjvO", "yfMtHqJUVD"], "linkedNodes": {}}, "oIKSXWUjvO": {"type": "div", "isCanvas": false, "props": {"className": "flex flex-col"}, "displayName": "div", "custom": {}, "parent": "8F4m0m-E1P", "hidden": false, "nodes": ["q1XJoTINER", "W59IZmZ-o7"], "linkedNodes": {}}, "q1XJoTINER": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "Home Camera", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "tablet": {"text": "Home Camera", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "mobile": {"text": "Home Camera", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "id": "security-card-1-title"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "oIKSXWUjvO", "hidden": false, "nodes": [], "linkedNodes": {}}, "W59IZmZ-o7": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Internet tốc độ cao<br>✓ Tặng 01 Camera trong nhà<br>✓ <PERSON><PERSON>u trữ cloud an toàn</div>", "fontSize": 14, "color": "#6b7280"}, "tablet": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Internet tốc độ cao<br>✓ Tặng 01 Camera trong nhà<br>✓ <PERSON><PERSON>u trữ cloud an toàn</div>", "fontSize": 14, "color": "#6b7280"}, "mobile": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Internet tốc độ cao<br>✓ Tặng 01 Camera trong nhà<br>✓ <PERSON><PERSON>u trữ cloud an toàn</div>", "fontSize": 14, "color": "#6b7280"}, "id": "security-card-1-features"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "oIKSXWUjvO", "hidden": false, "nodes": [], "linkedNodes": {}}, "yfMtHqJUVD": {"type": "div", "isCanvas": false, "props": {"className": "flex flex-col"}, "displayName": "div", "custom": {}, "parent": "8F4m0m-E1P", "hidden": false, "nodes": ["gLx0OcdstI", "iEgs6gFBzS"], "linkedNodes": {}}, "gLx0OcdstI": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "210.000đ<span style=\"font-size: 16px; font-weight: 400;\">/tháng</span>", "fontSize": 24, "fontWeight": 700, "color": "#f7941e", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "tablet": {"text": "210.000đ<span style=\"font-size: 16px; font-weight: 400;\">/tháng</span>", "fontSize": 24, "fontWeight": 700, "color": "#f7941e", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "mobile": {"text": "210.000đ<span style=\"font-size: 16px; font-weight: 400;\">/tháng</span>", "fontSize": 24, "fontWeight": 700, "color": "#f7941e", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "id": "security-card-1-price"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "yfMtHqJUVD", "hidden": false, "nodes": [], "linkedNodes": {}}, "iEgs6gFBzS": {"type": {"resolvedName": "Row"}, "isCanvas": true, "props": {"desktop": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "tablet": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "mobile": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "id": "security-card-1-buttons"}, "displayName": "Row", "custom": {}, "parent": "yfMtHqJUVD", "hidden": false, "nodes": ["9h-NHwl-T1", "PIKXxYVRDh"], "linkedNodes": {}}, "9h-NHwl-T1": {"type": {"resolvedName": "<PERSON><PERSON>"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "id": "security-card-1-detail-btn"}, "displayName": "<PERSON><PERSON>", "custom": {}, "parent": "iEgs6gFBzS", "hidden": false, "nodes": [], "linkedNodes": {}}, "PIKXxYVRDh": {"type": {"resolvedName": "<PERSON><PERSON>"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON> ngay", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON><PERSON> ngay", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON><PERSON> ngay", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "id": "security-card-1-buy-btn"}, "displayName": "<PERSON><PERSON>", "custom": {}, "parent": "iEgs6gFBzS", "hidden": false, "nodes": [], "linkedNodes": {}}, "IuR_5wMVAm": {"type": {"resolvedName": "Column"}, "isCanvas": true, "props": {"desktop": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "tablet": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "mobile": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "id": "security-card-2"}, "displayName": "Column", "custom": {}, "parent": "nRVgztU9_V", "hidden": false, "nodes": ["0hi13TxV8_"], "linkedNodes": {}}, "0hi13TxV8_": {"type": {"resolvedName": "ProductCard"}, "isCanvas": true, "props": {"desktop": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column"}, "tablet": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column"}, "mobile": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column"}, "id": "product2"}, "displayName": "ProductCard", "custom": {}, "parent": "IuR_5wMVAm", "hidden": false, "nodes": ["Otv_uJH2g5", "U5YMiKwswi"], "linkedNodes": {}}, "Otv_uJH2g5": {"type": "div", "isCanvas": false, "props": {"className": "rounded-t-lg bg-[#e8f5e9]"}, "displayName": "div", "custom": {}, "parent": "0hi13TxV8_", "hidden": false, "nodes": ["zExN9khEkU"], "linkedNodes": {}}, "zExN9khEkU": {"type": {"resolvedName": "Image"}, "isCanvas": true, "props": {"desktop": {"imgSrc": "https://placehold.co/600x400/E8F5E9/1B5E20?text=SmartHome+Chung+Cư", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "tablet": {"imgSrc": "https://placehold.co/600x400/E8F5E9/1B5E20?text=SmartHome+Chung+Cư", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "mobile": {"imgSrcMobile": "https://placehold.co/600x400/E8F5E9/1B5E20?text=SmartHome+Chung+Cư", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "id": "security-card-2-image"}, "displayName": "Ảnh", "custom": {}, "parent": "Otv_uJH2g5", "hidden": false, "nodes": [], "linkedNodes": {}}, "U5YMiKwswi": {"type": "div", "isCanvas": false, "props": {"className": "flex h-full flex-col justify-between p-6"}, "displayName": "div", "custom": {}, "parent": "0hi13TxV8_", "hidden": false, "nodes": ["ojl1r9xbqR", "cI9mMzTyY_"], "linkedNodes": {}}, "ojl1r9xbqR": {"type": "div", "isCanvas": false, "props": {"className": "flex flex-col"}, "displayName": "div", "custom": {}, "parent": "U5YMiKwswi", "hidden": false, "nodes": ["JGuqHPZbU5", "-31KgVOmCg"], "linkedNodes": {}}, "JGuqHPZbU5": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "SmartHome - <PERSON><PERSON><PERSON>", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "tablet": {"text": "SmartHome - <PERSON><PERSON><PERSON>", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "mobile": {"text": "SmartHome - <PERSON><PERSON><PERSON>", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "id": "security-card-2-title"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "ojl1r9xbqR", "hidden": false, "nodes": [], "linkedNodes": {}}, "-31KgVOmCg": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Đi<PERSON>u khiển chiếu sáng, rèm<br>✓ <PERSON><PERSON><PERSON> biến cửa, an ninh<br>✓ <PERSON><PERSON><PERSON> hợp trợ lý ảo</div>", "fontSize": 14, "color": "#6b7280"}, "tablet": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Đi<PERSON>u khiển chiếu sáng, rèm<br>✓ <PERSON><PERSON><PERSON> biến cửa, an ninh<br>✓ <PERSON><PERSON><PERSON> hợp trợ lý ảo</div>", "fontSize": 14, "color": "#6b7280"}, "mobile": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Đi<PERSON>u khiển chiếu sáng, rèm<br>✓ <PERSON><PERSON><PERSON> biến cửa, an ninh<br>✓ <PERSON><PERSON><PERSON> hợp trợ lý ảo</div>", "fontSize": 14, "color": "#6b7280"}, "id": "security-card-2-features"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "ojl1r9xbqR", "hidden": false, "nodes": [], "linkedNodes": {}}, "cI9mMzTyY_": {"type": "div", "isCanvas": false, "props": {"className": "flex flex-col"}, "displayName": "div", "custom": {}, "parent": "U5YMiKwswi", "hidden": false, "nodes": ["_vbMLXUnx3", "tuexSJWWka"], "linkedNodes": {}}, "_vbMLXUnx3": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "tablet": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "mobile": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "id": "security-card-2-price"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "cI9mMzTyY_", "hidden": false, "nodes": [], "linkedNodes": {}}, "tuexSJWWka": {"type": {"resolvedName": "Row"}, "isCanvas": true, "props": {"desktop": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "tablet": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "mobile": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "id": "security-card-2-buttons"}, "displayName": "Row", "custom": {}, "parent": "cI9mMzTyY_", "hidden": false, "nodes": ["YVP7cYAe-v", "fzOXonw4HW"], "linkedNodes": {}}, "YVP7cYAe-v": {"type": {"resolvedName": "<PERSON><PERSON>"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "id": "security-card-2-detail-btn"}, "displayName": "<PERSON><PERSON>", "custom": {}, "parent": "tuexSJWWka", "hidden": false, "nodes": [], "linkedNodes": {}}, "fzOXonw4HW": {"type": {"resolvedName": "<PERSON><PERSON>"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "id": "security-card-2-consult-btn"}, "displayName": "<PERSON><PERSON>", "custom": {}, "parent": "tuexSJWWka", "hidden": false, "nodes": [], "linkedNodes": {}}, "kojrAntZDK": {"type": {"resolvedName": "Column"}, "isCanvas": true, "props": {"desktop": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "tablet": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "mobile": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "id": "security-card-3"}, "displayName": "Column", "custom": {}, "parent": "nRVgztU9_V", "hidden": false, "nodes": ["-deM_od69v"], "linkedNodes": {}}, "-deM_od69v": {"type": {"resolvedName": "ProductCard"}, "isCanvas": true, "props": {"desktop": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column"}, "tablet": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column"}, "mobile": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column"}, "id": "product3"}, "displayName": "ProductCard", "custom": {}, "parent": "kojrAntZDK", "hidden": false, "nodes": ["-R5qpGyuuI", "vgloydAtf2"], "linkedNodes": {}}, "-R5qpGyuuI": {"type": "div", "isCanvas": false, "props": {"className": "rounded-t-lg bg-[#e8f5e9]"}, "displayName": "div", "custom": {}, "parent": "-deM_od69v", "hidden": false, "nodes": ["0jCUJ5_7pt"], "linkedNodes": {}}, "0jCUJ5_7pt": {"type": {"resolvedName": "Image"}, "isCanvas": true, "props": {"desktop": {"imgSrc": "https://placehold.co/600x400/E8F5E9/1B5E20?text=SmartHome+Nhà+Phố", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "tablet": {"imgSrc": "https://placehold.co/600x400/E8F5E9/1B5E20?text=SmartHome+Nhà+Phố", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "mobile": {"imgSrcMobile": "https://placehold.co/600x400/E8F5E9/1B5E20?text=SmartHome+Nhà+Phố", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "id": "security-card-3-image"}, "displayName": "Ảnh", "custom": {}, "parent": "-R5qpGyuuI", "hidden": false, "nodes": [], "linkedNodes": {}}, "vgloydAtf2": {"type": "div", "isCanvas": false, "props": {"className": "flex h-full flex-col justify-between p-6"}, "displayName": "div", "custom": {}, "parent": "-deM_od69v", "hidden": false, "nodes": ["9J2KomZsxd", "R9lK9aPC7V"], "linkedNodes": {}}, "9J2KomZsxd": {"type": "div", "isCanvas": false, "props": {"className": "flex flex-col"}, "displayName": "div", "custom": {}, "parent": "vgloydAtf2", "hidden": false, "nodes": ["z9pIOX3pQc", "W_DIiJK8KD"], "linkedNodes": {}}, "z9pIOX3pQc": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "SmartHome - Nhà phố", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "tablet": {"text": "SmartHome - Nhà phố", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "mobile": {"text": "SmartHome - Nhà phố", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "id": "security-card-3-title"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "9J2KomZsxd", "hidden": false, "nodes": [], "linkedNodes": {}}, "W_DIiJK8KD": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Giải pháp toàn diện cho nhà phố<br>✓ Điều khiển đa vùng<br>✓ <PERSON><PERSON><PERSON> hợp hệ thống an ninh</div>", "fontSize": 14, "color": "#6b7280"}, "tablet": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Giải pháp toàn diện cho nhà phố<br>✓ Điều khiển đa vùng<br>✓ <PERSON><PERSON><PERSON> hợp hệ thống an ninh</div>", "fontSize": 14, "color": "#6b7280"}, "mobile": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Giải pháp toàn diện cho nhà phố<br>✓ Điều khiển đa vùng<br>✓ <PERSON><PERSON><PERSON> hợp hệ thống an ninh</div>", "fontSize": 14, "color": "#6b7280"}, "id": "security-card-3-features"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "9J2KomZsxd", "hidden": false, "nodes": [], "linkedNodes": {}}, "R9lK9aPC7V": {"type": "div", "isCanvas": false, "props": {"className": "flex flex-col"}, "displayName": "div", "custom": {}, "parent": "vgloydAtf2", "hidden": false, "nodes": ["WSe4tepAjf", "qX-9__tUR_"], "linkedNodes": {}}, "WSe4tepAjf": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "tablet": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "mobile": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "id": "security-card-3-price"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "R9lK9aPC7V", "hidden": false, "nodes": [], "linkedNodes": {}}, "qX-9__tUR_": {"type": {"resolvedName": "Row"}, "isCanvas": true, "props": {"desktop": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "tablet": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "mobile": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "id": "security-card-3-buttons"}, "displayName": "Row", "custom": {}, "parent": "R9lK9aPC7V", "hidden": false, "nodes": ["vZxLlwNcy7", "NTrQkCIUGw"], "linkedNodes": {}}, "vZxLlwNcy7": {"type": {"resolvedName": "<PERSON><PERSON>"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "id": "security-card-3-detail-btn"}, "displayName": "<PERSON><PERSON>", "custom": {}, "parent": "qX-9__tUR_", "hidden": false, "nodes": [], "linkedNodes": {}}, "NTrQkCIUGw": {"type": {"resolvedName": "<PERSON><PERSON>"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "id": "security-card-3-consult-btn"}, "displayName": "<PERSON><PERSON>", "custom": {}, "parent": "qX-9__tUR_", "hidden": false, "nodes": [], "linkedNodes": {}}, "A5L3O_Xedx": {"type": {"resolvedName": "Column"}, "isCanvas": true, "props": {"desktop": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "tablet": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "mobile": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "id": "security-card-4"}, "displayName": "Column", "custom": {}, "parent": "nRVgztU9_V", "hidden": false, "nodes": ["z1nlKeY6Fr"], "linkedNodes": {}}, "z1nlKeY6Fr": {"type": {"resolvedName": "ProductCard"}, "isCanvas": true, "props": {"desktop": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column"}, "tablet": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column"}, "mobile": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column"}, "id": "product4"}, "displayName": "ProductCard", "custom": {}, "parent": "A5L3O_Xedx", "hidden": false, "nodes": ["x4LnryPP5g", "AZZi3IaSY4"], "linkedNodes": {}}, "x4LnryPP5g": {"type": "div", "isCanvas": false, "props": {"className": "rounded-t-lg bg-[#e8f5e9]"}, "displayName": "div", "custom": {}, "parent": "z1nlKeY6Fr", "hidden": false, "nodes": ["B0aXo5m2wX"], "linkedNodes": {}}, "B0aXo5m2wX": {"type": {"resolvedName": "Image"}, "isCanvas": true, "props": {"desktop": {"imgSrc": "https://placehold.co/600x400/E8F5E9/1B5E20?text=SmartHome+Biệt+Thự", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "tablet": {"imgSrc": "https://placehold.co/600x400/E8F5E9/1B5E20?text=SmartHome+Biệt+Thự", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "mobile": {"imgSrcMobile": "https://placehold.co/600x400/E8F5E9/1B5E20?text=SmartHome+Biệt+Thự", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "id": "security-card-4-image"}, "displayName": "Ảnh", "custom": {}, "parent": "x4LnryPP5g", "hidden": false, "nodes": [], "linkedNodes": {}}, "AZZi3IaSY4": {"type": "div", "isCanvas": false, "props": {"className": "flex h-full flex-col justify-between p-6"}, "displayName": "div", "custom": {}, "parent": "z1nlKeY6Fr", "hidden": false, "nodes": ["HHbyqU-7SG", "g__c-zq0ae"], "linkedNodes": {}}, "HHbyqU-7SG": {"type": "div", "isCanvas": false, "props": {"className": "flex flex-col"}, "displayName": "div", "custom": {}, "parent": "AZZi3IaSY4", "hidden": false, "nodes": ["awppmNH0GC", "PQJCEkVuhZ"], "linkedNodes": {}}, "awppmNH0GC": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "SmartHome - <PERSON><PERSON><PERSON><PERSON> thự", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "tablet": {"text": "SmartHome - <PERSON><PERSON><PERSON><PERSON> thự", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "mobile": {"text": "SmartHome - <PERSON><PERSON><PERSON><PERSON> thự", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "id": "security-card-4-title"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "HHbyqU-7SG", "hidden": false, "nodes": [], "linkedNodes": {}}, "PQJCEkVuhZ": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Giải pháp cao cấp, sang trọng<br>✓ Tự động hóa toàn diện<br>✓ Điều khiển bằng giọng nói</div>", "fontSize": 14, "color": "#6b7280"}, "tablet": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Giải pháp cao cấp, sang trọng<br>✓ Tự động hóa toàn diện<br>✓ Điều khiển bằng giọng nói</div>", "fontSize": 14, "color": "#6b7280"}, "mobile": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Giải pháp cao cấp, sang trọng<br>✓ Tự động hóa toàn diện<br>✓ Điều khiển bằng giọng nói</div>", "fontSize": 14, "color": "#6b7280"}, "id": "security-card-4-features"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "HHbyqU-7SG", "hidden": false, "nodes": [], "linkedNodes": {}}, "g__c-zq0ae": {"type": "div", "isCanvas": false, "props": {"className": "flex flex-col"}, "displayName": "div", "custom": {}, "parent": "AZZi3IaSY4", "hidden": false, "nodes": ["f1CKQYPzmO", "O-8pHHkuyy"], "linkedNodes": {}}, "f1CKQYPzmO": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "tablet": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "mobile": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "id": "security-card-4-price"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "g__c-zq0ae", "hidden": false, "nodes": [], "linkedNodes": {}}, "O-8pHHkuyy": {"type": {"resolvedName": "Row"}, "isCanvas": true, "props": {"desktop": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "tablet": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "mobile": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "id": "security-card-4-buttons"}, "displayName": "Row", "custom": {}, "parent": "g__c-zq0ae", "hidden": false, "nodes": ["t_0TUnZMWh", "MxLHaEBYmg"], "linkedNodes": {}}, "t_0TUnZMWh": {"type": {"resolvedName": "<PERSON><PERSON>"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "id": "security-card-4-detail-btn"}, "displayName": "<PERSON><PERSON>", "custom": {}, "parent": "O-8pHHkuyy", "hidden": false, "nodes": [], "linkedNodes": {}}, "MxLHaEBYmg": {"type": {"resolvedName": "<PERSON><PERSON>"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "id": "security-card-4-consult-btn"}, "displayName": "<PERSON><PERSON>", "custom": {}, "parent": "O-8pHHkuyy", "hidden": false, "nodes": [], "linkedNodes": {}}, "RrRtqqdmng": {"type": {"resolvedName": "Container"}, "isCanvas": true, "props": {"flexDirection": "column", "alignItems": "flex-start", "justifyContent": "flex-start", "padding": ["0", "0", "0", "0"], "margin": ["0", "0", "0", "0"], "background": "#FFFFFF", "color": {"r": 0, "g": 0, "b": 0, "a": 1}, "shadow": 0, "radius": 0, "width": "100%", "height": "auto"}, "displayName": "Container", "custom": {}, "parent": "H8DjWTI7io", "hidden": false, "nodes": ["HPNFWU9rUI"], "linkedNodes": {}}, "HPNFWU9rUI": {"type": {"resolvedName": "Row"}, "isCanvas": true, "props": {"desktop": {"padding": {"top": 0, "bottom": 24, "left": 0, "right": 0}, "colWidths": [12], "gap": 6, "height": "auto", "alignItems": "center", "justifyContent": "center", "width": "100%"}, "tablet": {"padding": {"top": 0, "bottom": 24, "left": 0, "right": 0}, "colWidths": [12], "gap": 6, "height": "auto", "alignItems": "center", "justifyContent": "center", "width": "100%"}, "mobile": {"padding": {"top": 0, "bottom": 24, "left": 0, "right": 0}, "colWidths": [12], "gap": 6, "height": "auto", "alignItems": "center", "justifyContent": "center", "width": "100%"}, "id": "family-service-section"}, "displayName": "Row", "custom": {}, "parent": "RrRtqqdmng", "hidden": false, "nodes": ["9_HPPd1mXu", "u43Jtc9tQT"], "linkedNodes": {}}, "9_HPPd1mXu": {"type": {"resolvedName": "Column"}, "isCanvas": true, "props": {"desktop": {"margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}}, "tablet": {"margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}}, "mobile": {"margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}}, "id": "family-service-heading"}, "displayName": "Column", "custom": {}, "parent": "HPNFWU9rUI", "hidden": false, "nodes": ["GAgQJxIOAd"], "linkedNodes": {}}, "GAgQJxIOAd": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "#<PERSON><PERSON><PERSON> vụ Gia <PERSON>nh", "fontSize": 24, "fontWeight": 700, "color": "#374151"}, "tablet": {"text": "#<PERSON><PERSON><PERSON> vụ Gia <PERSON>nh", "fontSize": 22, "fontWeight": 700, "color": "#374151"}, "mobile": {"text": "#<PERSON><PERSON><PERSON> vụ Gia <PERSON>nh", "fontSize": 20, "fontWeight": 700, "color": "#374151"}, "id": "family-service-heading"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "9_HPPd1mXu", "hidden": false, "nodes": [], "linkedNodes": {}}, "u43Jtc9tQT": {"type": {"resolvedName": "Container"}, "isCanvas": true, "props": {"flexDirection": "column", "alignItems": "flex-start", "justifyContent": "flex-start", "padding": ["0", "0", "0", "0"], "margin": ["0", "0", "0", "0"], "background": "#FFFFFF", "color": {"r": 0, "g": 0, "b": 0, "a": 1}, "shadow": 0, "radius": 0, "width": "100%", "height": "auto", "id": "container-family-service"}, "displayName": "Container", "custom": {}, "parent": "HPNFWU9rUI", "hidden": false, "nodes": ["4oLwwDEGJh"], "linkedNodes": {}}, "4oLwwDEGJh": {"type": {"resolvedName": "Row"}, "isCanvas": true, "props": {"desktop": {"colWidths": [4, 4, 4], "padding": {"top": 0, "bottom": 24, "left": 0, "right": 0}, "gap": 6, "height": "auto", "alignItems": "stretch", "justifyContent": "center", "width": "100%"}, "tablet": {"colWidths": [6, 6], "gap": 6, "height": "auto", "alignItems": "stretch", "justifyContent": "center", "width": "100%", "isBreakLine": true}, "mobile": {"colWidths": [12], "gap": 6, "height": "auto", "alignItems": "stretch", "justifyContent": "center", "width": "100%", "isBreakLine": true}, "id": "family-service-cards"}, "displayName": "Row", "custom": {}, "parent": "u43Jtc9tQT", "hidden": false, "nodes": ["CE-ubScZQG", "SxcroFnExc", "q3UFkUe2fl"], "linkedNodes": {}}, "CE-ubScZQG": {"type": {"resolvedName": "Column"}, "isCanvas": true, "props": {"desktop": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "tablet": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "mobile": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "id": "family-service-card-1"}, "displayName": "Column", "custom": {}, "parent": "4oLwwDEGJh", "hidden": false, "nodes": ["p324vJ7zW1"], "linkedNodes": {}}, "p324vJ7zW1": {"type": {"resolvedName": "ProductCard"}, "isCanvas": true, "props": {"desktop": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "tablet": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "mobile": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "id": "product1"}, "displayName": "ProductCard", "custom": {}, "parent": "CE-ubScZQG", "hidden": false, "nodes": ["bFFirQl6YQ", "2Z5clPnn9i"], "linkedNodes": {}}, "bFFirQl6YQ": {"type": "div", "isCanvas": false, "props": {"className": "rounded-t-lg bg-[#f3e5f5]"}, "displayName": "div", "custom": {}, "parent": "p324vJ7zW1", "hidden": false, "nodes": ["mANHjN1l_o"], "linkedNodes": {}}, "mANHjN1l_o": {"type": {"resolvedName": "Image"}, "isCanvas": true, "props": {"desktop": {"imgSrc": "https://placehold.co/600x400/F3E5F5/4A148C?text=Sửa+chữa", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "tablet": {"imgSrc": "https://placehold.co/600x400/F3E5F5/4A148C?text=Sửa+chữa", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "mobile": {"imgSrcMobile": "https://placehold.co/600x400/F3E5F5/4A148C?text=Sửa+chữa", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "id": "family-service-card-1-image"}, "displayName": "Ảnh", "custom": {}, "parent": "bFFirQl6YQ", "hidden": false, "nodes": [], "linkedNodes": {}}, "2Z5clPnn9i": {"type": "div", "isCanvas": false, "props": {"className": "flex h-full flex-col justify-between p-6"}, "displayName": "div", "custom": {}, "parent": "p324vJ7zW1", "hidden": false, "nodes": ["eKNW7xb5k-", "4ds4pCoHTa"], "linkedNodes": {}}, "eKNW7xb5k-": {"type": "div", "isCanvas": false, "props": {"className": "flex flex-col"}, "displayName": "div", "custom": {}, "parent": "2Z5clPnn9i", "hidden": false, "nodes": ["Gb3-W0ETih", "nn8Yksnh0K"], "linkedNodes": {}}, "Gb3-W0ETih": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON><PERSON> v<PERSON> ch<PERSON>a", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "tablet": {"text": "<PERSON><PERSON><PERSON> v<PERSON> ch<PERSON>a", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "mobile": {"text": "<PERSON><PERSON><PERSON> v<PERSON> ch<PERSON>a", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "id": "family-service-card-1-title"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "eKNW7xb5k-", "hidden": false, "nodes": [], "linkedNodes": {}}, "nn8Yksnh0K": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ <PERSON><PERSON><PERSON> chữa, bảo trì tại nhà<br>✓ <PERSON><PERSON> dụng cho TV, m<PERSON><PERSON> lạnh, tủ lạnh...<br>✓ <PERSON><PERSON><PERSON> ngũ chuyên nghiệp, tin cậy</div>", "fontSize": 14, "color": "#6b7280"}, "tablet": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ <PERSON><PERSON><PERSON> chữa, bảo trì tại nhà<br>✓ <PERSON><PERSON> dụng cho TV, m<PERSON><PERSON> lạnh, tủ lạnh...<br>✓ <PERSON><PERSON><PERSON> ngũ chuyên nghiệp, tin cậy</div>", "fontSize": 14, "color": "#6b7280"}, "mobile": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ <PERSON><PERSON><PERSON> chữa, bảo trì tại nhà<br>✓ <PERSON><PERSON> dụng cho TV, m<PERSON><PERSON> lạnh, tủ lạnh...<br>✓ <PERSON><PERSON><PERSON> ngũ chuyên nghiệp, tin cậy</div>", "fontSize": 14, "color": "#6b7280"}, "id": "family-service-card-1-features"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "eKNW7xb5k-", "hidden": false, "nodes": [], "linkedNodes": {}}, "4ds4pCoHTa": {"type": "div", "isCanvas": false, "props": {"className": "flex flex-col"}, "displayName": "div", "custom": {}, "parent": "2Z5clPnn9i", "hidden": false, "nodes": ["O88bO_KdjF", "2-TwuB5_DF"], "linkedNodes": {}}, "O88bO_KdjF": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "tablet": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "mobile": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "id": "family-service-card-1-price"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "4ds4pCoHTa", "hidden": false, "nodes": [], "linkedNodes": {}}, "2-TwuB5_DF": {"type": {"resolvedName": "Row"}, "isCanvas": true, "props": {"desktop": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "tablet": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "mobile": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "id": "family-service-card-1-buttons"}, "displayName": "Row", "custom": {}, "parent": "4ds4pCoHTa", "hidden": false, "nodes": ["63Q66hb0dE", "5hoRO0mglG"], "linkedNodes": {}}, "63Q66hb0dE": {"type": {"resolvedName": "<PERSON><PERSON>"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "id": "family-service-card-1-detail-btn"}, "displayName": "<PERSON><PERSON>", "custom": {}, "parent": "2-TwuB5_DF", "hidden": false, "nodes": [], "linkedNodes": {}}, "5hoRO0mglG": {"type": {"resolvedName": "<PERSON><PERSON>"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON><PERSON> c<PERSON>", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON><PERSON><PERSON> c<PERSON>", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON><PERSON><PERSON> c<PERSON>", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "id": "family-service-card-1-request-btn"}, "displayName": "<PERSON><PERSON>", "custom": {}, "parent": "2-TwuB5_DF", "hidden": false, "nodes": [], "linkedNodes": {}}, "SxcroFnExc": {"type": {"resolvedName": "Column"}, "isCanvas": true, "props": {"desktop": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "tablet": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "mobile": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "id": "family-service-card-2"}, "displayName": "Column", "custom": {}, "parent": "4oLwwDEGJh", "hidden": false, "nodes": ["v0Bw6Nj1DL"], "linkedNodes": {}}, "v0Bw6Nj1DL": {"type": {"resolvedName": "ProductCard"}, "isCanvas": true, "props": {"desktop": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "tablet": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "mobile": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "id": "product2"}, "displayName": "ProductCard", "custom": {}, "parent": "SxcroFnExc", "hidden": false, "nodes": ["0jhoCrWQGF", "L8mcGmj3th"], "linkedNodes": {}}, "0jhoCrWQGF": {"type": "div", "isCanvas": false, "props": {"className": "rounded-t-lg bg-[#f3e5f5]"}, "displayName": "div", "custom": {}, "parent": "v0Bw6Nj1DL", "hidden": false, "nodes": ["2POmUk84yR"], "linkedNodes": {}}, "2POmUk84yR": {"type": {"resolvedName": "Image"}, "isCanvas": true, "props": {"desktop": {"imgSrc": "https://placehold.co/600x400/F3E5F5/4A148C?text=Bảo+hành", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "tablet": {"imgSrc": "https://placehold.co/600x400/F3E5F5/4A148C?text=Bảo+hành", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "mobile": {"imgSrcMobile": "https://placehold.co/600x400/F3E5F5/4A148C?text=Bảo+hành", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "id": "family-service-card-2-image"}, "displayName": "Ảnh", "custom": {}, "parent": "0jhoCrWQGF", "hidden": false, "nodes": [], "linkedNodes": {}}, "L8mcGmj3th": {"type": "div", "isCanvas": false, "props": {"className": "flex h-full flex-col justify-between p-6"}, "displayName": "div", "custom": {}, "parent": "v0Bw6Nj1DL", "hidden": false, "nodes": ["1s4i3HUez-", "xVIszNAwv3"], "linkedNodes": {}}, "1s4i3HUez-": {"type": "div", "isCanvas": false, "props": {"className": "flex flex-col"}, "displayName": "div", "custom": {}, "parent": "L8mcGmj3th", "hidden": false, "nodes": ["V2N78biPdr", "bpIwQzQ7Fi"], "linkedNodes": {}}, "V2N78biPdr": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON><PERSON> hành Mở rộng", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "tablet": {"text": "<PERSON><PERSON><PERSON> hành Mở rộng", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "mobile": {"text": "<PERSON><PERSON><PERSON> hành Mở rộng", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "id": "family-service-card-2-title"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "1s4i3HUez-", "hidden": false, "nodes": [], "linkedNodes": {}}, "bpIwQzQ7Fi": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Kéo dài thời gian bảo hành<br>✓ An tâm sử dụng thiết bị<br>✓ Áp dụng cho nhiều sản phẩm</div>", "fontSize": 14, "color": "#6b7280"}, "tablet": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Kéo dài thời gian bảo hành<br>✓ An tâm sử dụng thiết bị<br>✓ Áp dụng cho nhiều sản phẩm</div>", "fontSize": 14, "color": "#6b7280"}, "mobile": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Kéo dài thời gian bảo hành<br>✓ An tâm sử dụng thiết bị<br>✓ Áp dụng cho nhiều sản phẩm</div>", "fontSize": 14, "color": "#6b7280"}, "id": "family-service-card-2-features"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "1s4i3HUez-", "hidden": false, "nodes": [], "linkedNodes": {}}, "xVIszNAwv3": {"type": "div", "isCanvas": false, "props": {"className": "flex flex-col"}, "displayName": "div", "custom": {}, "parent": "L8mcGmj3th", "hidden": false, "nodes": ["zluQc65aUQ", "4xSL5XWzg4"], "linkedNodes": {}}, "zluQc65aUQ": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "tablet": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "mobile": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "id": "family-service-card-2-price"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "xVIszNAwv3", "hidden": false, "nodes": [], "linkedNodes": {}}, "4xSL5XWzg4": {"type": {"resolvedName": "Row"}, "isCanvas": true, "props": {"desktop": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "tablet": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "mobile": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "id": "family-service-card-2-buttons"}, "displayName": "Row", "custom": {}, "parent": "xVIszNAwv3", "hidden": false, "nodes": ["Uhmq0dDemj", "CK5P9UOnT_"], "linkedNodes": {}}, "Uhmq0dDemj": {"type": {"resolvedName": "<PERSON><PERSON>"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "id": "family-service-card-2-detail-btn"}, "displayName": "<PERSON><PERSON>", "custom": {}, "parent": "4xSL5XWzg4", "hidden": false, "nodes": [], "linkedNodes": {}}, "CK5P9UOnT_": {"type": {"resolvedName": "<PERSON><PERSON>"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "id": "family-service-card-2-consult-btn"}, "displayName": "<PERSON><PERSON>", "custom": {}, "parent": "4xSL5XWzg4", "hidden": false, "nodes": [], "linkedNodes": {}}, "q3UFkUe2fl": {"type": {"resolvedName": "Column"}, "isCanvas": true, "props": {"desktop": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "tablet": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "mobile": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}, "gap": 0, "height": "auto", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "id": "family-service-card-3"}, "displayName": "Column", "custom": {}, "parent": "4oLwwDEGJh", "hidden": false, "nodes": ["aQEh2RGtmP"], "linkedNodes": {}}, "aQEh2RGtmP": {"type": {"resolvedName": "ProductCard"}, "isCanvas": true, "props": {"desktop": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "tablet": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "mobile": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "id": "product3"}, "displayName": "ProductCard", "custom": {}, "parent": "q3UFkUe2fl", "hidden": false, "nodes": ["6_5z7jomfE", "10TXEGSpJX"], "linkedNodes": {}}, "6_5z7jomfE": {"type": "div", "isCanvas": false, "props": {"className": "rounded-t-lg bg-[#f3e5f5]"}, "displayName": "div", "custom": {}, "parent": "aQEh2RGtmP", "hidden": false, "nodes": ["a0m0ocwv66"], "linkedNodes": {}}, "a0m0ocwv66": {"type": {"resolvedName": "Image"}, "isCanvas": true, "props": {"desktop": {"imgSrc": "https://placehold.co/600x400/F3E5F5/4A148C?text=Bảo+hiểm", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "tablet": {"imgSrc": "https://placehold.co/600x400/F3E5F5/4A148C?text=Bảo+hiểm", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "mobile": {"imgSrcMobile": "https://placehold.co/600x400/F3E5F5/4A148C?text=Bảo+hiểm", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "id": "family-service-card-3-image"}, "displayName": "Ảnh", "custom": {}, "parent": "6_5z7jomfE", "hidden": false, "nodes": [], "linkedNodes": {}}, "10TXEGSpJX": {"type": "div", "isCanvas": false, "props": {"className": "flex h-full flex-col justify-between p-6"}, "displayName": "div", "custom": {}, "parent": "aQEh2RGtmP", "hidden": false, "nodes": ["8I1MTRgB93", "c3qKWA3ouG"], "linkedNodes": {}}, "8I1MTRgB93": {"type": "div", "isCanvas": false, "props": {"className": "flex flex-col"}, "displayName": "div", "custom": {}, "parent": "10TXEGSpJX", "hidden": false, "nodes": ["2OeRVfVuR4", "eMZnoADSea"], "linkedNodes": {}}, "2OeRVfVuR4": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON><PERSON> bị", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "tablet": {"text": "<PERSON><PERSON><PERSON> bị", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "mobile": {"text": "<PERSON><PERSON><PERSON> bị", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "id": "family-service-card-3-title"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "8I1MTRgB93", "hidden": false, "nodes": [], "linkedNodes": {}}, "eMZnoADSea": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ B<PERSON>o vệ thiết bị khỏi rơi vỡ, v<PERSON><PERSON> nước<br>✓ <PERSON><PERSON><PERSON> tục đơn giản, <PERSON><PERSON><PERSON> chóng</div>", "fontSize": 14, "color": "#6b7280"}, "tablet": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ B<PERSON>o vệ thiết bị khỏi rơi vỡ, v<PERSON><PERSON> nước<br>✓ <PERSON><PERSON><PERSON> tục đơn giản, <PERSON><PERSON><PERSON> chóng</div>", "fontSize": 14, "color": "#6b7280"}, "mobile": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ B<PERSON>o vệ thiết bị khỏi rơi vỡ, v<PERSON><PERSON> nước<br>✓ <PERSON><PERSON><PERSON> tục đơn giản, <PERSON><PERSON><PERSON> chóng</div>", "fontSize": 14, "color": "#6b7280"}, "id": "family-service-card-3-features"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "8I1MTRgB93", "hidden": false, "nodes": [], "linkedNodes": {}}, "c3qKWA3ouG": {"type": "div", "isCanvas": false, "props": {"className": "flex flex-col"}, "displayName": "div", "custom": {}, "parent": "10TXEGSpJX", "hidden": false, "nodes": ["AB3lMnttpi", "m11yrJnw-K"], "linkedNodes": {}}, "AB3lMnttpi": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "tablet": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "mobile": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "id": "family-service-card-3-price"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "c3qKWA3ouG", "hidden": false, "nodes": [], "linkedNodes": {}}, "m11yrJnw-K": {"type": {"resolvedName": "Row"}, "isCanvas": true, "props": {"desktop": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "tablet": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "mobile": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "id": "family-service-card-3-buttons"}, "displayName": "Row", "custom": {}, "parent": "c3qKWA3ouG", "hidden": false, "nodes": ["RsmeiaelEP", "jHgZjgdW3M"], "linkedNodes": {}}, "RsmeiaelEP": {"type": {"resolvedName": "<PERSON><PERSON>"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "id": "family-service-card-3-detail-btn"}, "displayName": "<PERSON><PERSON>", "custom": {}, "parent": "m11yrJnw-K", "hidden": false, "nodes": [], "linkedNodes": {}}, "jHgZjgdW3M": {"type": {"resolvedName": "<PERSON><PERSON>"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "id": "family-service-card-3-consult-btn"}, "displayName": "<PERSON><PERSON>", "custom": {}, "parent": "m11yrJnw-K", "hidden": false, "nodes": [], "linkedNodes": {}}, "R0EAS92Oea": {"type": {"resolvedName": "Container"}, "isCanvas": true, "props": {"flexDirection": "column", "alignItems": "flex-start", "justifyContent": "flex-start", "padding": ["0", "0", "0", "0"], "margin": ["0", "0", "0", "0"], "background": "#FFFFFF", "color": {"r": 0, "g": 0, "b": 0, "a": 1}, "shadow": 0, "radius": 0, "width": "100%", "height": "auto"}, "displayName": "Container", "custom": {}, "parent": "TjMVaVHfF1", "hidden": false, "nodes": ["GE6hCFg5K_"], "linkedNodes": {}}, "GE6hCFg5K_": {"type": {"resolvedName": "Row"}, "isCanvas": true, "props": {"desktop": {"padding": {"top": 0, "bottom": 24, "left": 0, "right": 0}, "colWidths": [12], "gap": 6, "height": "auto", "alignItems": "center", "justifyContent": "center", "width": "100%"}, "tablet": {"padding": {"top": 0, "bottom": 24, "left": 0, "right": 0}, "colWidths": [12], "gap": 6, "height": "auto", "alignItems": "center", "justifyContent": "center", "width": "100%"}, "mobile": {"padding": {"top": 0, "bottom": 24, "left": 0, "right": 0}, "colWidths": [12], "gap": 6, "height": "auto", "alignItems": "center", "justifyContent": "center", "width": "100%"}, "id": "smart-devices-section"}, "displayName": "Row", "custom": {}, "parent": "R0EAS92Oea", "hidden": false, "nodes": ["tOdFaWfg3U", "lYZyTzZrnk"], "linkedNodes": {}}, "tOdFaWfg3U": {"type": {"resolvedName": "Column"}, "isCanvas": true, "props": {"desktop": {"margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}}, "tablet": {"margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}}, "mobile": {"margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}}, "id": "smart-devices-heading"}, "displayName": "Column", "custom": {}, "parent": "GE6hCFg5K_", "hidden": false, "nodes": ["BA6b1ge6zw"], "linkedNodes": {}}, "BA6b1ge6zw": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "#<PERSON><PERSON><PERSON><PERSON> bị thông minh", "fontSize": 24, "fontWeight": 700, "color": "#374151"}, "tablet": {"text": "#<PERSON><PERSON><PERSON><PERSON> bị thông minh", "fontSize": 22, "fontWeight": 700, "color": "#374151"}, "mobile": {"text": "#<PERSON><PERSON><PERSON><PERSON> bị thông minh", "fontSize": 20, "fontWeight": 700, "color": "#374151"}, "id": "smart-devices-heading"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "tOdFaWfg3U", "hidden": false, "nodes": [], "linkedNodes": {}}, "lYZyTzZrnk": {"type": {"resolvedName": "Container"}, "isCanvas": true, "props": {"flexDirection": "column", "alignItems": "flex-start", "justifyContent": "flex-start", "padding": ["0", "0", "0", "0"], "margin": ["0", "0", "0", "0"], "background": "#FFFFFF", "color": {"r": 0, "g": 0, "b": 0, "a": 1}, "shadow": 0, "radius": 0, "width": "100%", "height": "auto", "id": "container-smart-devices"}, "displayName": "Container", "custom": {}, "parent": "GE6hCFg5K_", "hidden": false, "nodes": ["Nwhx6aVoUL"], "linkedNodes": {}}, "Nwhx6aVoUL": {"type": {"resolvedName": "Row"}, "isCanvas": true, "props": {"desktop": {"colWidths": [3, 3, 3, 3], "padding": {"top": 0, "bottom": 24, "left": 0, "right": 0}, "gap": 6, "height": "auto", "alignItems": "stretch", "justifyContent": "center", "width": "100%"}, "tablet": {"colWidths": [6, 6], "gap": 6, "height": "auto", "alignItems": "stretch", "justifyContent": "center", "width": "100%", "isBreakLine": true}, "mobile": {"colWidths": [12], "gap": 6, "height": "auto", "alignItems": "stretch", "justifyContent": "center", "width": "100%", "isBreakLine": true}, "id": "smart-devices-cards"}, "displayName": "Row", "custom": {}, "parent": "lYZyTzZrnk", "hidden": false, "nodes": ["-a_LD2wfum", "RB_j85TYlF", "L9ulYrhjA0", "3soxZxK-xq"], "linkedNodes": {}}, "-a_LD2wfum": {"type": {"resolvedName": "Column"}, "isCanvas": true, "props": {"desktop": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "100%", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "tablet": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "100%", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "mobile": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}, "gap": 0, "height": "100%", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "id": "smart-devices-card-1"}, "displayName": "Column", "custom": {}, "parent": "Nwhx6aVoUL", "hidden": false, "nodes": ["6kT5dfQTuf"], "linkedNodes": {}}, "6kT5dfQTuf": {"type": {"resolvedName": "ProductCard"}, "isCanvas": true, "props": {"desktop": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "tablet": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "mobile": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "id": "product1"}, "displayName": "ProductCard", "custom": {}, "parent": "-a_LD2wfum", "hidden": false, "nodes": ["hTEFIf8pv-", "p_RU3HWaJU"], "linkedNodes": {}}, "hTEFIf8pv-": {"type": "div", "isCanvas": false, "props": {"className": "rounded-t-lg bg-[#f1f8e9]"}, "displayName": "div", "custom": {}, "parent": "6kT5dfQTuf", "hidden": false, "nodes": ["eLRo-RTAOV"], "linkedNodes": {}}, "eLRo-RTAOV": {"type": {"resolvedName": "Image"}, "isCanvas": true, "props": {"desktop": {"imgSrc": "https://placehold.co/600x400/F1F8E9/33691E?text=Đèn+Thông+Minh", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "tablet": {"imgSrc": "https://placehold.co/600x400/F1F8E9/33691E?text=Đèn+Thông+Minh", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "mobile": {"imgSrcMobile": "https://placehold.co/600x400/F1F8E9/33691E?text=Đèn+Thông+Minh", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "id": "smart-devices-card-1-image"}, "displayName": "Ảnh", "custom": {}, "parent": "hTEFIf8pv-", "hidden": false, "nodes": [], "linkedNodes": {}}, "p_RU3HWaJU": {"type": "div", "isCanvas": false, "props": {"className": "flex h-full flex-col justify-between p-6"}, "displayName": "div", "custom": {}, "parent": "6kT5dfQTuf", "hidden": false, "nodes": ["M_F6JYsBSw", "Tg9s1sHgck"], "linkedNodes": {}}, "M_F6JYsBSw": {"type": "div", "isCanvas": false, "props": {"className": "flex flex-col"}, "displayName": "div", "custom": {}, "parent": "p_RU3HWaJU", "hidden": false, "nodes": ["JMe3dzeRJI", "C5R_j_yZEy"], "linkedNodes": {}}, "JMe3dzeRJI": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON><PERSON> thông <PERSON>h", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "tablet": {"text": "<PERSON><PERSON><PERSON> thông <PERSON>h", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "mobile": {"text": "<PERSON><PERSON><PERSON> thông <PERSON>h", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "id": "smart-devices-card-1-title"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "M_F6JYsBSw", "hidden": false, "nodes": [], "linkedNodes": {}}, "C5R_j_yZEy": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Thay đổi màu sắc, đ<PERSON> sáng<br>✓ <PERSON><PERSON><PERSON><PERSON> khiển qua điện thoại</div>", "fontSize": 14, "color": "#6b7280"}, "tablet": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Thay đổi màu sắc, đ<PERSON> sáng<br>✓ <PERSON><PERSON><PERSON><PERSON> khiển qua điện thoại</div>", "fontSize": 14, "color": "#6b7280"}, "mobile": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Thay đổi màu sắc, đ<PERSON> sáng<br>✓ <PERSON><PERSON><PERSON><PERSON> khiển qua điện thoại</div>", "fontSize": 14, "color": "#6b7280"}, "id": "smart-devices-card-1-features"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "M_F6JYsBSw", "hidden": false, "nodes": [], "linkedNodes": {}}, "Tg9s1sHgck": {"type": "div", "isCanvas": false, "props": {"className": "flex flex-col"}, "displayName": "div", "custom": {}, "parent": "p_RU3HWaJU", "hidden": false, "nodes": ["y7OKKaVN33", "d89yv7eQag"], "linkedNodes": {}}, "y7OKKaVN33": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "tablet": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "mobile": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "id": "smart-devices-card-1-price"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "Tg9s1sHgck", "hidden": false, "nodes": [], "linkedNodes": {}}, "d89yv7eQag": {"type": {"resolvedName": "Row"}, "isCanvas": true, "props": {"desktop": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "tablet": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "mobile": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "id": "smart-devices-card-1-buttons"}, "displayName": "Row", "custom": {}, "parent": "Tg9s1sHgck", "hidden": false, "nodes": ["-pj8k2DK8e", "L-CnCWMUJc"], "linkedNodes": {}}, "-pj8k2DK8e": {"type": {"resolvedName": "<PERSON><PERSON>"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "id": "smart-devices-card-1-detail-btn"}, "displayName": "<PERSON><PERSON>", "custom": {}, "parent": "d89yv7eQag", "hidden": false, "nodes": [], "linkedNodes": {}}, "L-CnCWMUJc": {"type": {"resolvedName": "<PERSON><PERSON>"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "id": "smart-devices-card-1-consult-btn"}, "displayName": "<PERSON><PERSON>", "custom": {}, "parent": "d89yv7eQag", "hidden": false, "nodes": [], "linkedNodes": {}}, "RB_j85TYlF": {"type": {"resolvedName": "Column"}, "isCanvas": true, "props": {"desktop": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "100%", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "tablet": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "100%", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "mobile": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}, "gap": 0, "height": "100%", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "id": "smart-devices-card-2"}, "displayName": "Column", "custom": {}, "parent": "Nwhx6aVoUL", "hidden": false, "nodes": ["8hG3ksd2Gb"], "linkedNodes": {}}, "8hG3ksd2Gb": {"type": {"resolvedName": "ProductCard"}, "isCanvas": true, "props": {"desktop": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "tablet": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "mobile": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "id": "product2"}, "displayName": "ProductCard", "custom": {}, "parent": "RB_j85TYlF", "hidden": false, "nodes": ["JkV98EUk3_", "qJtwzYiAwZ"], "linkedNodes": {}}, "JkV98EUk3_": {"type": "div", "isCanvas": false, "props": {"className": "rounded-t-lg bg-[#f1f8e9]"}, "displayName": "div", "custom": {}, "parent": "8hG3ksd2Gb", "hidden": false, "nodes": ["QhFnolVCKQ"], "linkedNodes": {}}, "QhFnolVCKQ": {"type": {"resolvedName": "Image"}, "isCanvas": true, "props": {"desktop": {"imgSrc": "https://placehold.co/600x400/F1F8E9/33691E?text=Công+tắc", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "tablet": {"imgSrc": "https://placehold.co/600x400/F1F8E9/33691E?text=Công+tắc", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "mobile": {"imgSrcMobile": "https://placehold.co/600x400/F1F8E9/33691E?text=Công+tắc", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "id": "smart-devices-card-2-image"}, "displayName": "Ảnh", "custom": {}, "parent": "JkV98EUk3_", "hidden": false, "nodes": [], "linkedNodes": {}}, "qJtwzYiAwZ": {"type": "div", "isCanvas": false, "props": {"className": "flex h-full flex-col justify-between p-6"}, "displayName": "div", "custom": {}, "parent": "8hG3ksd2Gb", "hidden": false, "nodes": ["DjtcBfPNHk", "dl3wA8wgxG"], "linkedNodes": {}}, "DjtcBfPNHk": {"type": "div", "isCanvas": false, "props": {"className": "flex flex-col"}, "displayName": "div", "custom": {}, "parent": "qJtwzYiAwZ", "hidden": false, "nodes": ["eDCt1P742c", "_JMJDKtAyq"], "linkedNodes": {}}, "eDCt1P742c": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON><PERSON> tắc thông minh", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "tablet": {"text": "<PERSON><PERSON><PERSON> tắc thông minh", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "mobile": {"text": "<PERSON><PERSON><PERSON> tắc thông minh", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "id": "smart-devices-card-2-title"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "DjtcBfPNHk", "hidden": false, "nodes": [], "linkedNodes": {}}, "_JMJDKtAyq": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Bật/tắt từ xa<br>✓ <PERSON><PERSON><PERSON><PERSON> kế sang trọng</div>", "fontSize": 14, "color": "#6b7280"}, "tablet": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Bật/tắt từ xa<br>✓ <PERSON><PERSON><PERSON><PERSON> kế sang trọng</div>", "fontSize": 14, "color": "#6b7280"}, "mobile": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Bật/tắt từ xa<br>✓ <PERSON><PERSON><PERSON><PERSON> kế sang trọng</div>", "fontSize": 14, "color": "#6b7280"}, "id": "smart-devices-card-2-features"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "DjtcBfPNHk", "hidden": false, "nodes": [], "linkedNodes": {}}, "dl3wA8wgxG": {"type": "div", "isCanvas": false, "props": {"className": "flex flex-col"}, "displayName": "div", "custom": {}, "parent": "qJtwzYiAwZ", "hidden": false, "nodes": ["oWIR-85l-j", "fC2JPyW-i4"], "linkedNodes": {}}, "oWIR-85l-j": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "tablet": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "mobile": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "id": "smart-devices-card-2-price"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "dl3wA8wgxG", "hidden": false, "nodes": [], "linkedNodes": {}}, "fC2JPyW-i4": {"type": {"resolvedName": "Row"}, "isCanvas": true, "props": {"desktop": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "tablet": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "mobile": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "id": "smart-devices-card-2-buttons"}, "displayName": "Row", "custom": {}, "parent": "dl3wA8wgxG", "hidden": false, "nodes": ["BkCbl-1qo1", "Idk4yfCDPA"], "linkedNodes": {}}, "BkCbl-1qo1": {"type": {"resolvedName": "<PERSON><PERSON>"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "id": "smart-devices-card-2-detail-btn"}, "displayName": "<PERSON><PERSON>", "custom": {}, "parent": "fC2JPyW-i4", "hidden": false, "nodes": [], "linkedNodes": {}}, "Idk4yfCDPA": {"type": {"resolvedName": "<PERSON><PERSON>"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "id": "smart-devices-card-2-consult-btn"}, "displayName": "<PERSON><PERSON>", "custom": {}, "parent": "fC2JPyW-i4", "hidden": false, "nodes": [], "linkedNodes": {}}, "L9ulYrhjA0": {"type": {"resolvedName": "Column"}, "isCanvas": true, "props": {"desktop": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "100%", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "tablet": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "100%", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "mobile": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}, "gap": 0, "height": "100%", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "id": "smart-devices-card-3"}, "displayName": "Column", "custom": {}, "parent": "Nwhx6aVoUL", "hidden": false, "nodes": ["RWHRHKxUE-"], "linkedNodes": {}}, "RWHRHKxUE-": {"type": {"resolvedName": "ProductCard"}, "isCanvas": true, "props": {"desktop": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "tablet": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "mobile": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "id": "product3"}, "displayName": "ProductCard", "custom": {}, "parent": "L9ulYrhjA0", "hidden": false, "nodes": ["hxglT12u1x", "tJnAOnCCay"], "linkedNodes": {}}, "hxglT12u1x": {"type": "div", "isCanvas": false, "props": {"className": "rounded-t-lg bg-[#f1f8e9]"}, "displayName": "div", "custom": {}, "parent": "RWHRHKxUE-", "hidden": false, "nodes": ["LDWwcgAd3I"], "linkedNodes": {}}, "LDWwcgAd3I": {"type": {"resolvedName": "Image"}, "isCanvas": true, "props": {"desktop": {"imgSrc": "https://placehold.co/600x400/F1F8E9/33691E?text=Ổ+cắm", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "tablet": {"imgSrc": "https://placehold.co/600x400/F1F8E9/33691E?text=Ổ+cắm", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "mobile": {"imgSrcMobile": "https://placehold.co/600x400/F1F8E9/33691E?text=Ổ+cắm", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "id": "smart-devices-card-3-image"}, "displayName": "Ảnh", "custom": {}, "parent": "hxglT12u1x", "hidden": false, "nodes": [], "linkedNodes": {}}, "tJnAOnCCay": {"type": "div", "isCanvas": false, "props": {"className": "flex h-full flex-col justify-between p-6"}, "displayName": "div", "custom": {}, "parent": "RWHRHKxUE-", "hidden": false, "nodes": ["k1487g70-8", "w0SlpzHcvZ"], "linkedNodes": {}}, "k1487g70-8": {"type": "div", "isCanvas": false, "props": {"className": "flex flex-col"}, "displayName": "div", "custom": {}, "parent": "tJnAOnCCay", "hidden": false, "nodes": ["9QB-PE0jMT", "3ITMVILadl"], "linkedNodes": {}}, "9QB-PE0jMT": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "Ổ cắm thông minh", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "tablet": {"text": "Ổ cắm thông minh", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "mobile": {"text": "Ổ cắm thông minh", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "id": "smart-devices-card-3-title"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "k1487g70-8", "hidden": false, "nodes": [], "linkedNodes": {}}, "3ITMVILadl": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ B<PERSON><PERSON>n thiết bị thường thành thông minh<br>✓ Hẹn giờ bật/tắt</div>", "fontSize": 14, "color": "#6b7280"}, "tablet": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ B<PERSON><PERSON>n thiết bị thường thành thông minh<br>✓ Hẹn giờ bật/tắt</div>", "fontSize": 14, "color": "#6b7280"}, "mobile": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ B<PERSON><PERSON>n thiết bị thường thành thông minh<br>✓ Hẹn giờ bật/tắt</div>", "fontSize": 14, "color": "#6b7280"}, "id": "smart-devices-card-3-features"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "k1487g70-8", "hidden": false, "nodes": [], "linkedNodes": {}}, "w0SlpzHcvZ": {"type": "div", "isCanvas": false, "props": {"className": "flex flex-col"}, "displayName": "div", "custom": {}, "parent": "tJnAOnCCay", "hidden": false, "nodes": ["hJX03dq9_I", "BqqNnpMMsQ"], "linkedNodes": {}}, "hJX03dq9_I": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "tablet": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "mobile": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "id": "smart-devices-card-3-price"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "w0SlpzHcvZ", "hidden": false, "nodes": [], "linkedNodes": {}}, "BqqNnpMMsQ": {"type": {"resolvedName": "Row"}, "isCanvas": true, "props": {"desktop": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "tablet": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "mobile": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "id": "smart-devices-card-3-buttons"}, "displayName": "Row", "custom": {}, "parent": "w0SlpzHcvZ", "hidden": false, "nodes": ["XRtvM3vQtu", "fEBJ9rFhVC"], "linkedNodes": {}}, "XRtvM3vQtu": {"type": {"resolvedName": "<PERSON><PERSON>"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "id": "smart-devices-card-3-detail-btn"}, "displayName": "<PERSON><PERSON>", "custom": {}, "parent": "BqqNnpMMsQ", "hidden": false, "nodes": [], "linkedNodes": {}}, "fEBJ9rFhVC": {"type": {"resolvedName": "<PERSON><PERSON>"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "id": "smart-devices-card-3-consult-btn"}, "displayName": "<PERSON><PERSON>", "custom": {}, "parent": "BqqNnpMMsQ", "hidden": false, "nodes": [], "linkedNodes": {}}, "3soxZxK-xq": {"type": {"resolvedName": "Column"}, "isCanvas": true, "props": {"desktop": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "100%", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "tablet": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "gap": 0, "height": "100%", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "mobile": {"backgroundColor": "#ffffff", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "margin": {"top": 0, "bottom": 16, "left": 0, "right": 0}, "gap": 0, "height": "100%", "alignItems": "stretch", "contentAlign": "start", "justifyContent": "center", "width": "100%", "border": {"radius": 8}}, "id": "smart-devices-card-4"}, "displayName": "Column", "custom": {}, "parent": "Nwhx6aVoUL", "hidden": false, "nodes": ["ncV4LpprT5"], "linkedNodes": {}}, "ncV4LpprT5": {"type": {"resolvedName": "ProductCard"}, "isCanvas": true, "props": {"desktop": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "tablet": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "mobile": {"backgroundColor": "#ffffff", "borderRadius": 8, "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "hoverTransform": "translateY(-5px)", "hoverBoxShadow": "0 10px 25px rgba(0,0,0,0.1)", "transition": "transform 0.3s ease, box-shadow 0.3s ease", "height": "100%", "display": "flex", "flexDirection": "column", "minHeight": 400}, "id": "product4"}, "displayName": "ProductCard", "custom": {}, "parent": "3soxZxK-xq", "hidden": false, "nodes": ["G1wf2JKkWx", "m1BkpPoITo"], "linkedNodes": {}}, "G1wf2JKkWx": {"type": "div", "isCanvas": false, "props": {"className": "rounded-t-lg bg-[#f1f8e9]"}, "displayName": "div", "custom": {}, "parent": "ncV4LpprT5", "hidden": false, "nodes": ["sF_0iPteo5"], "linkedNodes": {}}, "sF_0iPteo5": {"type": {"resolvedName": "Image"}, "isCanvas": true, "props": {"desktop": {"imgSrc": "https://placehold.co/600x400/F1F8E9/33691E?text=Rèm+cửa", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "tablet": {"imgSrc": "https://placehold.co/600x400/F1F8E9/33691E?text=Rèm+cửa", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "mobile": {"imgSrcMobile": "https://placehold.co/600x400/F1F8E9/33691E?text=Rèm+cửa", "width": "100%", "height": 160, "border": {"radius": 8}, "imageFit": "fill", "align": "center"}, "id": "smart-devices-card-4-image"}, "displayName": "Ảnh", "custom": {}, "parent": "G1wf2JKkWx", "hidden": false, "nodes": [], "linkedNodes": {}}, "m1BkpPoITo": {"type": "div", "isCanvas": false, "props": {"className": "flex h-full flex-col justify-between p-6"}, "displayName": "div", "custom": {}, "parent": "ncV4LpprT5", "hidden": false, "nodes": ["IYdvjkGugt", "ZR9uT3qyYz"], "linkedNodes": {}}, "IYdvjkGugt": {"type": "div", "isCanvas": false, "props": {"className": "flex flex-col"}, "displayName": "div", "custom": {}, "parent": "m1BkpPoITo", "hidden": false, "nodes": ["avvHknVjNf", "W-DIyxorW2"], "linkedNodes": {}}, "avvHknVjNf": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON><PERSON> c<PERSON>a thông minh", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "tablet": {"text": "<PERSON><PERSON><PERSON> c<PERSON>a thông minh", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "mobile": {"text": "<PERSON><PERSON><PERSON> c<PERSON>a thông minh", "fontSize": 18, "fontWeight": 700, "color": "#00529c"}, "id": "smart-devices-card-4-title"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "IYdvjkGugt", "hidden": false, "nodes": [], "linkedNodes": {}}, "W-DIyxorW2": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Tự động đóng/mở<br>✓ Điều khiển bằng giọng nói</div>", "fontSize": 14, "color": "#6b7280"}, "tablet": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Tự động đóng/mở<br>✓ Điều khiển bằng giọng nói</div>", "fontSize": 14, "color": "#6b7280"}, "mobile": {"text": "<div class=\"text-sm text-gray-600 my-3 space-y-1 flex-grow\">✓ Tự động đóng/mở<br>✓ Điều khiển bằng giọng nói</div>", "fontSize": 14, "color": "#6b7280"}, "id": "smart-devices-card-4-features"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "IYdvjkGugt", "hidden": false, "nodes": [], "linkedNodes": {}}, "ZR9uT3qyYz": {"type": "div", "isCanvas": false, "props": {"className": "flex flex-col"}, "displayName": "div", "custom": {}, "parent": "m1BkpPoITo", "hidden": false, "nodes": ["IEYob1mc19", "bJfuN4W1Jz"], "linkedNodes": {}}, "IEYob1mc19": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "tablet": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "mobile": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 20, "fontWeight": 700, "color": "#f7941e", "margin": {"top": "auto", "bottom": 0, "left": 0, "right": 0}}, "id": "smart-devices-card-4-price"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "ZR9uT3qyYz", "hidden": false, "nodes": [], "linkedNodes": {}}, "bJfuN4W1Jz": {"type": {"resolvedName": "Row"}, "isCanvas": true, "props": {"desktop": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "tablet": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "mobile": {"colWidths": [1, 1], "gap": 2, "height": "auto", "alignItems": "stretch", "justifyContent": "space-between", "width": "100%", "margin": {"top": 8, "bottom": 0, "left": 0, "right": 0}}, "id": "smart-devices-card-4-buttons"}, "displayName": "Row", "custom": {}, "parent": "ZR9uT3qyYz", "hidden": false, "nodes": ["in_0DTMN_e", "d_3qKwRFkl"], "linkedNodes": {}}, "in_0DTMN_e": {"type": {"resolvedName": "<PERSON><PERSON>"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON>em chi tiết", "fontSize": 14, "fontWeight": 600, "color": "#374151", "buttonType": "primary", "buttonBackgroundColor": "#e5e7eb", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "id": "smart-devices-card-4-detail-btn"}, "displayName": "<PERSON><PERSON>", "custom": {}, "parent": "bJfuN4W1Jz", "hidden": false, "nodes": [], "linkedNodes": {}}, "d_3qKwRFkl": {"type": {"resolvedName": "<PERSON><PERSON>"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "tablet": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "mobile": {"text": "<PERSON><PERSON> vấn", "fontSize": 14, "fontWeight": 600, "color": "#ffffff", "buttonType": "primary", "buttonBackgroundColor": "#00529c", "radius": 8, "padding": {"top": 8, "bottom": 8, "left": 16, "right": 16}, "size": "middle", "width": "100%"}, "id": "smart-devices-card-4-consult-btn"}, "displayName": "<PERSON><PERSON>", "custom": {}, "parent": "bJfuN4W1Jz", "hidden": false, "nodes": [], "linkedNodes": {}}, "Ds3SdQibje": {"type": {"resolvedName": "Container"}, "isCanvas": true, "props": {"flexDirection": "column", "alignItems": "flex-start", "justifyContent": "flex-start", "padding": ["0", "0", "0", "0"], "margin": ["0", "0", "0", "0"], "background": "#FFFFFF", "color": {"r": 0, "g": 0, "b": 0, "a": 1}, "shadow": 0, "radius": 0, "width": "100%", "height": "auto"}, "displayName": "Container", "custom": {}, "parent": "JoYgF4jr03", "hidden": false, "nodes": ["pHgOALgKz8", "ZBcj4X5LVI"], "linkedNodes": {}}, "pHgOALgKz8": {"type": {"resolvedName": "Row"}, "isCanvas": true, "props": {"desktop": {"backgroundColor": "#f1f5f9", "backgroundType": "COLOR", "padding": {"top": 64, "bottom": 40, "left": 16, "right": 16}, "height": "auto", "justifyContent": "center"}, "tablet": {"backgroundColor": "#f1f5f9", "backgroundType": "COLOR", "padding": {"top": 64, "bottom": 40, "left": 16, "right": 16}, "height": "auto", "justifyContent": "center"}, "mobile": {"backgroundColor": "#f1f5f9", "backgroundType": "COLOR", "padding": {"top": 48, "bottom": 32, "left": 16, "right": 16}, "height": "auto", "justifyContent": "center"}, "id": "title-row"}, "displayName": "Row", "custom": {}, "parent": "Ds3SdQibje", "hidden": false, "nodes": ["zaBzHbCHU0"], "linkedNodes": {}}, "zaBzHbCHU0": {"type": {"resolvedName": "SectionTitle"}, "isCanvas": true, "props": {"desktop": {"text": "Tại sao chọn tôi?", "fontSize": 32, "fontWeight": 700, "color": "#00529c", "textAlign": "center", "marginBottom": 40}, "tablet": {"text": "Tại sao chọn tôi?", "fontSize": 28, "fontWeight": 700, "color": "#00529c", "textAlign": "center", "marginBottom": 32}, "mobile": {"text": "Tại sao chọn tôi?", "fontSize": 24, "fontWeight": 700, "color": "#00529c", "textAlign": "center", "marginBottom": 24}, "id": "section-title"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "pHgOALgKz8", "hidden": false, "nodes": [], "linkedNodes": {}}, "ZBcj4X5LVI": {"type": {"resolvedName": "Row"}, "isCanvas": true, "props": {"desktop": {"backgroundColor": "#f1f5f9", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 64, "left": 16, "right": 16}, "colWidths": [4, 4, 4], "gap": 8, "height": "auto"}, "tablet": {"backgroundColor": "#f1f5f9", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 64, "left": 16, "right": 16}, "colWidths": [4, 4, 4], "gap": 8, "height": "auto"}, "mobile": {"backgroundColor": "#f1f5f9", "backgroundType": "COLOR", "padding": {"top": 0, "bottom": 48, "left": 16, "right": 16}, "isBreakLine": true, "gap": 8, "height": "auto"}, "id": "reasons-row"}, "displayName": "Row", "custom": {}, "parent": "Ds3SdQibje", "hidden": false, "nodes": ["i633ZnYEDK", "Z-EPinm1UW", "HVcV8h3ZHf"], "linkedNodes": {}}, "i633ZnYEDK": {"type": {"resolvedName": "Column"}, "isCanvas": true, "props": {"desktop": {"justifyContent": "center", "alignItems": "center", "contentAlign": "center"}, "tablet": {"justifyContent": "center", "alignItems": "center", "contentAlign": "center"}, "mobile": {"justifyContent": "center", "alignItems": "center", "contentAlign": "center"}, "id": "reason1"}, "displayName": "Column", "custom": {}, "parent": "ZBcj4X5LVI", "hidden": false, "nodes": ["cJ6rgpaAcU", "w8gftIl7uo", "tp0BDgrPEd", "SV0_IX2eyl", "qC3dHIWRnK"], "linkedNodes": {}}, "cJ6rgpaAcU": {"type": {"resolvedName": "IconLibrary"}, "isCanvas": true, "props": {"desktop": {"icon": "BulbFilled", "iconColor": "#00529c", "size": 24, "bgIconColor": "#dbeafe", "borderIcon": {"radius": 50}, "paddingIcon": {"top": 20, "bottom": 20, "left": 20, "right": 20}, "width": 64, "height": 64, "showIcon": {"desktop": true, "tablet": true, "mobile": true}, "showTitle": {"desktop": false, "tablet": false, "mobile": false}, "showDesc": {"desktop": false, "tablet": false, "mobile": false}, "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "margin": {"top": 0, "bottom": 0}, "title": "", "desc": "", "backgroundType": "NONE"}, "tablet": {"icon": "BulbFilled", "iconColor": "#00529c", "size": 24, "bgIconColor": "#dbeafe", "borderIcon": {"radius": 50}, "paddingIcon": {"top": 20, "bottom": 20, "left": 20, "right": 20}, "width": 64, "height": 64, "showIcon": {"desktop": true, "tablet": true, "mobile": true}, "showTitle": {"desktop": false, "tablet": false, "mobile": false}, "showDesc": {"desktop": false, "tablet": false, "mobile": false}, "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "margin": {"top": 0, "bottom": 0}, "title": "", "desc": "", "backgroundType": "NONE"}, "mobile": {"icon": "BulbFilled", "iconColor": "#00529c", "size": 24, "bgIconColor": "#dbeafe", "borderIcon": {"radius": 50}, "paddingIcon": {"top": 20, "bottom": 20, "left": 20, "right": 20}, "width": 64, "height": 64, "showIcon": {"desktop": true, "tablet": true, "mobile": true}, "showTitle": {"desktop": false, "tablet": false, "mobile": false}, "showDesc": {"desktop": false, "tablet": false, "mobile": false}, "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "margin": {"top": 0, "bottom": 0}, "title": "", "desc": "", "backgroundType": "NONE"}, "id": "icon1"}, "displayName": "Icon", "custom": {}, "parent": "i633ZnYEDK", "hidden": false, "nodes": [], "linkedNodes": {}}, "w8gftIl7uo": {"type": {"resolvedName": "Spacer"}, "isCanvas": true, "props": {"desktop": {"height": 16}, "tablet": {"height": 16}, "mobile": {"height": 16}, "id": "spacer1"}, "displayName": "<PERSON><PERSON><PERSON><PERSON> trắng", "custom": {}, "parent": "i633ZnYEDK", "hidden": false, "nodes": [], "linkedNodes": {}}, "tp0BDgrPEd": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON> vấn đúng nhu cầu", "fontSize": 20, "fontWeight": 700, "color": "#000000", "textAlign": "center"}, "tablet": {"text": "<PERSON><PERSON> vấn đúng nhu cầu", "fontSize": 20, "fontWeight": 700, "color": "#000000", "textAlign": "center"}, "mobile": {"text": "<PERSON><PERSON> vấn đúng nhu cầu", "fontSize": 20, "fontWeight": 700, "color": "#000000", "textAlign": "center"}, "id": "title1"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "i633ZnYEDK", "hidden": false, "nodes": [], "linkedNodes": {}}, "SV0_IX2eyl": {"type": {"resolvedName": "Spacer"}, "isCanvas": true, "props": {"desktop": {"height": 16}, "tablet": {"height": 16}, "mobile": {"height": 16}, "id": "spacer2"}, "displayName": "<PERSON><PERSON><PERSON><PERSON> trắng", "custom": {}, "parent": "i633ZnYEDK", "hidden": false, "nodes": [], "linkedNodes": {}}, "qC3dHIWRnK": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON><PERSON> lắng nghe và phân tích để đưa ra gi<PERSON>i pháp phù hợp nhất, không tư vấn thừa.", "fontSize": 16, "fontWeight": 400, "color": "#6b7280", "textAlign": "center"}, "tablet": {"text": "<PERSON><PERSON><PERSON> lắng nghe và phân tích để đưa ra gi<PERSON>i pháp phù hợp nhất, không tư vấn thừa.", "fontSize": 16, "fontWeight": 400, "color": "#6b7280", "textAlign": "center"}, "mobile": {"text": "<PERSON><PERSON><PERSON> lắng nghe và phân tích để đưa ra gi<PERSON>i pháp phù hợp nhất, không tư vấn thừa.", "fontSize": 14, "fontWeight": 400, "color": "#6b7280", "textAlign": "center"}, "id": "desc1"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "i633ZnYEDK", "hidden": false, "nodes": [], "linkedNodes": {}}, "Z-EPinm1UW": {"type": {"resolvedName": "Column"}, "isCanvas": true, "props": {"desktop": {"justifyContent": "center", "alignItems": "center", "contentAlign": "center"}, "tablet": {"justifyContent": "center", "alignItems": "center", "contentAlign": "center"}, "mobile": {"justifyContent": "center", "alignItems": "center", "contentAlign": "center"}, "id": "reason2"}, "displayName": "Column", "custom": {}, "parent": "ZBcj4X5LVI", "hidden": false, "nodes": ["gb7BRKIoKJ", "n-q6en-rMN", "QA0udjTRKg", "8mBcB0TdC0", "1C1jxZZtUa"], "linkedNodes": {}}, "gb7BRKIoKJ": {"type": {"resolvedName": "IconLibrary"}, "isCanvas": true, "props": {"desktop": {"icon": "TagsFilled", "iconColor": "#f7941e", "size": 24, "bgIconColor": "#fed7aa", "borderIcon": {"radius": 50}, "paddingIcon": {"top": 20, "bottom": 20, "left": 20, "right": 20}, "width": 64, "height": 64, "showIcon": {"desktop": true, "tablet": true, "mobile": true}, "showTitle": {"desktop": false, "tablet": false, "mobile": false}, "showDesc": {"desktop": false, "tablet": false, "mobile": false}, "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "margin": {"top": 0, "bottom": 0}, "title": "", "desc": "", "backgroundType": "NONE"}, "tablet": {"icon": "TagsFilled", "iconColor": "#f7941e", "size": 24, "bgIconColor": "#fed7aa", "borderIcon": {"radius": 50}, "paddingIcon": {"top": 20, "bottom": 20, "left": 20, "right": 20}, "width": 64, "height": 64, "showIcon": {"desktop": true, "tablet": true, "mobile": true}, "showTitle": {"desktop": false, "tablet": false, "mobile": false}, "showDesc": {"desktop": false, "tablet": false, "mobile": false}, "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "margin": {"top": 0, "bottom": 0}, "title": "", "desc": "", "backgroundType": "NONE"}, "mobile": {"icon": "TagsFilled", "iconColor": "#f7941e", "size": 24, "bgIconColor": "#fed7aa", "borderIcon": {"radius": 50}, "paddingIcon": {"top": 20, "bottom": 20, "left": 20, "right": 20}, "width": 64, "height": 64, "showIcon": {"desktop": true, "tablet": true, "mobile": true}, "showTitle": {"desktop": false, "tablet": false, "mobile": false}, "showDesc": {"desktop": false, "tablet": false, "mobile": false}, "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "margin": {"top": 0, "bottom": 0}, "title": "", "desc": "", "backgroundType": "NONE"}, "id": "icon2"}, "displayName": "Icon", "custom": {}, "parent": "Z-EPinm1UW", "hidden": false, "nodes": [], "linkedNodes": {}}, "n-q6en-rMN": {"type": {"resolvedName": "Spacer"}, "isCanvas": true, "props": {"desktop": {"height": 16}, "tablet": {"height": 16}, "mobile": {"height": 16}, "id": "spacer3"}, "displayName": "<PERSON><PERSON><PERSON><PERSON> trắng", "custom": {}, "parent": "Z-EPinm1UW", "hidden": false, "nodes": [], "linkedNodes": {}}, "QA0udjTRKg": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON><PERSON> & Ưu đãi tốt nhất", "fontSize": 20, "fontWeight": 700, "color": "#000000", "textAlign": "center"}, "tablet": {"text": "<PERSON><PERSON><PERSON> & Ưu đãi tốt nhất", "fontSize": 20, "fontWeight": 700, "color": "#000000", "textAlign": "center"}, "mobile": {"text": "<PERSON><PERSON><PERSON> & Ưu đãi tốt nhất", "fontSize": 20, "fontWeight": 700, "color": "#000000", "textAlign": "center"}, "id": "title2"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "Z-EPinm1UW", "hidden": false, "nodes": [], "linkedNodes": {}}, "8mBcB0TdC0": {"type": {"resolvedName": "Spacer"}, "isCanvas": true, "props": {"desktop": {"height": 16}, "tablet": {"height": 16}, "mobile": {"height": 16}, "id": "spacer4"}, "displayName": "<PERSON><PERSON><PERSON><PERSON> trắng", "custom": {}, "parent": "Z-EPinm1UW", "hidden": false, "nodes": [], "linkedNodes": {}}, "1C1jxZZtUa": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON><PERSON><PERSON> bạn tiếp cận các chương trình khuyến mại và gói cước tiết kiệm nhất từ VNPT.", "fontSize": 16, "fontWeight": 400, "color": "#6b7280", "textAlign": "center"}, "tablet": {"text": "<PERSON><PERSON><PERSON><PERSON> bạn tiếp cận các chương trình khuyến mại và gói cước tiết kiệm nhất từ VNPT.", "fontSize": 16, "fontWeight": 400, "color": "#6b7280", "textAlign": "center"}, "mobile": {"text": "<PERSON><PERSON><PERSON><PERSON> bạn tiếp cận các chương trình khuyến mại và gói cước tiết kiệm nhất từ VNPT.", "fontSize": 14, "fontWeight": 400, "color": "#6b7280", "textAlign": "center"}, "id": "desc2"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "Z-EPinm1UW", "hidden": false, "nodes": [], "linkedNodes": {}}, "HVcV8h3ZHf": {"type": {"resolvedName": "Column"}, "isCanvas": true, "props": {"desktop": {"justifyContent": "center", "alignItems": "center", "contentAlign": "center"}, "tablet": {"justifyContent": "center", "alignItems": "center", "contentAlign": "center"}, "mobile": {"justifyContent": "center", "alignItems": "center", "contentAlign": "center"}, "id": "reason3"}, "displayName": "Column", "custom": {}, "parent": "ZBcj4X5LVI", "hidden": false, "nodes": ["1lhTgDI90B", "2HUuN2Nl5J", "sFMvCdfSzy", "pg_ZcRLjso", "m8X2XBIUtX"], "linkedNodes": {}}, "1lhTgDI90B": {"type": {"resolvedName": "IconLibrary"}, "isCanvas": true, "props": {"desktop": {"icon": "CustomerServiceFilled", "iconColor": "#16a34a", "size": 24, "bgIconColor": "#bbf7d0", "borderIcon": {"radius": 50}, "paddingIcon": {"top": 20, "bottom": 20, "left": 20, "right": 20}, "width": 64, "height": 64, "showIcon": {"desktop": true, "tablet": true, "mobile": true}, "showTitle": {"desktop": false, "tablet": false, "mobile": false}, "showDesc": {"desktop": false, "tablet": false, "mobile": false}, "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "margin": {"top": 0, "bottom": 0}, "title": "", "desc": "", "backgroundType": "NONE"}, "tablet": {"icon": "CustomerServiceFilled", "iconColor": "#16a34a", "size": 24, "bgIconColor": "#bbf7d0", "borderIcon": {"radius": 50}, "paddingIcon": {"top": 20, "bottom": 20, "left": 20, "right": 20}, "width": 64, "height": 64, "showIcon": {"desktop": true, "tablet": true, "mobile": true}, "showTitle": {"desktop": false, "tablet": false, "mobile": false}, "showDesc": {"desktop": false, "tablet": false, "mobile": false}, "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "margin": {"top": 0, "bottom": 0}, "title": "", "desc": "", "backgroundType": "NONE"}, "mobile": {"icon": "CustomerServiceFilled", "iconColor": "#16a34a", "size": 24, "bgIconColor": "#bbf7d0", "borderIcon": {"radius": 50}, "paddingIcon": {"top": 20, "bottom": 20, "left": 20, "right": 20}, "width": 64, "height": 64, "showIcon": {"desktop": true, "tablet": true, "mobile": true}, "showTitle": {"desktop": false, "tablet": false, "mobile": false}, "showDesc": {"desktop": false, "tablet": false, "mobile": false}, "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}, "margin": {"top": 0, "bottom": 0}, "title": "", "desc": "", "backgroundType": "NONE"}, "id": "icon3"}, "displayName": "Icon", "custom": {}, "parent": "HVcV8h3ZHf", "hidden": false, "nodes": [], "linkedNodes": {}}, "2HUuN2Nl5J": {"type": {"resolvedName": "Spacer"}, "isCanvas": true, "props": {"desktop": {"height": 16}, "tablet": {"height": 16}, "mobile": {"height": 16}, "id": "spacer5"}, "displayName": "<PERSON><PERSON><PERSON><PERSON> trắng", "custom": {}, "parent": "HVcV8h3ZHf", "hidden": false, "nodes": [], "linkedNodes": {}}, "sFMvCdfSzy": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "Hỗ trợ trọn đời", "fontSize": 20, "fontWeight": 700, "color": "#000000", "textAlign": "center"}, "tablet": {"text": "Hỗ trợ trọn đời", "fontSize": 20, "fontWeight": 700, "color": "#000000", "textAlign": "center"}, "mobile": {"text": "Hỗ trợ trọn đời", "fontSize": 20, "fontWeight": 700, "color": "#000000", "textAlign": "center"}, "id": "title3"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "HVcV8h3ZHf", "hidden": false, "nodes": [], "linkedNodes": {}}, "pg_ZcRLjso": {"type": {"resolvedName": "Spacer"}, "isCanvas": true, "props": {"desktop": {"height": 16}, "tablet": {"height": 16}, "mobile": {"height": 16}, "id": "spacer6"}, "displayName": "<PERSON><PERSON><PERSON><PERSON> trắng", "custom": {}, "parent": "HVcV8h3ZHf", "hidden": false, "nodes": [], "linkedNodes": {}}, "m8X2XBIUtX": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON>ng hành cùng bạn trong suốt quá trình sử dụng dịch vụ, hỗ trợ 24/7 khi cần.", "fontSize": 16, "fontWeight": 400, "color": "#6b7280", "textAlign": "center"}, "tablet": {"text": "<PERSON><PERSON>ng hành cùng bạn trong suốt quá trình sử dụng dịch vụ, hỗ trợ 24/7 khi cần.", "fontSize": 16, "fontWeight": 400, "color": "#6b7280", "textAlign": "center"}, "mobile": {"text": "<PERSON><PERSON>ng hành cùng bạn trong suốt quá trình sử dụng dịch vụ, hỗ trợ 24/7 khi cần.", "fontSize": 14, "fontWeight": 400, "color": "#6b7280", "textAlign": "center"}, "id": "desc3"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "HVcV8h3ZHf", "hidden": false, "nodes": [], "linkedNodes": {}}, "OOEo5T8WEN": {"type": {"resolvedName": "Container"}, "isCanvas": true, "props": {"flexDirection": "column", "alignItems": "flex-start", "justifyContent": "flex-start", "padding": ["0", "0", "0", "0"], "margin": ["0", "0", "0", "0"], "background": "#FFFFFF", "color": {"r": 0, "g": 0, "b": 0, "a": 1}, "shadow": 0, "radius": 0, "width": "100%", "height": "auto"}, "displayName": "Container", "custom": {}, "parent": "-ZQtN2FA5d", "hidden": false, "nodes": ["VYdNWbiEcq", "rZXcweTqjP"], "linkedNodes": {}}, "VYdNWbiEcq": {"type": {"resolvedName": "Row"}, "isCanvas": true, "props": {"desktop": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "backgroundColor": "#374151", "text": "Text", "padding": {"top": 32, "bottom": 32, "left": 16, "right": 16}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "imgSrc": null, "externalLink": null, "imgSrcMobile": null, "externalLinkMobile": null, "typeUpload": "EXIST", "imageFit": "fit", "shadow": 0, "width": "100%", "height": "auto", "border": {"style": "none", "color": "#212D6E", "thickness": 0, "radius": 0}, "backgroundType": "COLOR", "borderStyle": "none", "contentAlign": "start", "justifyContent": "center", "align": "center", "colWidths": [4, 4, 4], "gap": 20, "isBreakLine": false, "hide": false}, "tablet": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "backgroundColor": "#374151", "text": "Text", "padding": {"top": 32, "bottom": 32, "left": 16, "right": 16}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "imgSrc": null, "externalLink": null, "imgSrcMobile": null, "externalLinkMobile": null, "typeUpload": "EXIST", "imageFit": "fit", "shadow": 0, "width": "100%", "height": "auto", "border": {"style": "none", "color": "#212D6E", "thickness": 0, "radius": 0}, "backgroundType": "COLOR", "borderStyle": "none", "contentAlign": "start", "justifyContent": "center", "align": "center", "colWidths": [4, 4, 4], "gap": 20, "isBreakLine": false, "hide": false}, "mobile": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "backgroundColor": "#374151", "text": "Text", "padding": {"top": 32, "bottom": 32, "left": 16, "right": 16}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "imgSrc": null, "externalLink": null, "imgSrcMobile": null, "externalLinkMobile": null, "typeUpload": "EXIST", "imageFit": "fit", "shadow": 0, "width": "100%", "height": "auto", "border": {"style": "none", "color": "#212D6E", "thickness": 0, "radius": 0}, "backgroundType": "COLOR", "borderStyle": "none", "contentAlign": "start", "justifyContent": "center", "align": "center", "colWidths": [6], "gap": 20, "isBreakLine": true, "hide": false}, "id": "footer-main-row"}, "displayName": "Row", "custom": {}, "parent": "OOEo5T8WEN", "hidden": false, "nodes": ["1vMXXAdn1N", "6yU57QPmKo", "KerA8FkJLz"], "linkedNodes": {}}, "1vMXXAdn1N": {"type": {"resolvedName": "Column"}, "isCanvas": true, "props": {"desktop": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "text": "Text", "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "imgSrc": null, "externalLink": null, "imgSrcMobile": null, "externalLinkMobile": null, "typeUpload": "EXIST", "imageFit": "fill", "shadow": 0, "width": "100%", "height": "auto", "border": {"style": "none", "color": "#212D6E", "thickness": 0, "radius": 0}, "backgroundType": "", "borderStyle": "none", "contentAlign": "start", "justifyContent": "center", "align": "center", "colWidths": [12], "gap": 4, "isBreakLine": false, "hide": false}, "tablet": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "text": "Text", "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "imgSrc": null, "externalLink": null, "imgSrcMobile": null, "externalLinkMobile": null, "typeUpload": "EXIST", "imageFit": "fill", "shadow": 0, "width": "100%", "height": "auto", "border": {"style": "none", "color": "#212D6E", "thickness": 0, "radius": 0}, "backgroundType": "", "borderStyle": "none", "contentAlign": "start", "justifyContent": "center", "align": "center", "colWidths": [12], "gap": 4, "isBreakLine": false, "hide": false}, "mobile": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "text": "Text", "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "imgSrc": null, "externalLink": null, "imgSrcMobile": null, "externalLinkMobile": null, "typeUpload": "EXIST", "imageFit": "fill", "shadow": 0, "width": "100%", "height": "auto", "border": {"style": "none", "color": "#212D6E", "thickness": 0, "radius": 0}, "backgroundType": "", "borderStyle": "none", "contentAlign": "start", "justifyContent": "center", "align": "center", "colWidths": [6], "gap": 4, "isBreakLine": false, "hide": false}, "id": "info-column"}, "displayName": "Column", "custom": {}, "parent": "VYdNWbiEcq", "hidden": false, "nodes": ["Pthf17RtYU", "ggnSezHd5y", "JT_xNnltAY"], "linkedNodes": {}}, "Pthf17RtYU": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON><PERSON><PERSON>", "fontSize": 18, "fontWeight": 700, "color": "#ffffff"}, "tablet": {"text": "<PERSON><PERSON><PERSON><PERSON>", "fontSize": 18, "fontWeight": 700, "color": "#ffffff"}, "mobile": {"text": "<PERSON><PERSON><PERSON><PERSON>", "fontSize": 18, "fontWeight": 700, "color": "#ffffff", "textAlign": "center"}, "id": "expert-name"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "1vMXXAdn1N", "hidden": false, "nodes": [], "linkedNodes": {}}, "ggnSezHd5y": {"type": {"resolvedName": "Spacer"}, "isCanvas": true, "props": {"desktop": {"height": 16}, "tablet": {"height": 16}, "mobile": {"height": 16}, "id": "spacer1"}, "displayName": "<PERSON><PERSON><PERSON><PERSON> trắng", "custom": {}, "parent": "1vMXXAdn1N", "hidden": false, "nodes": [], "linkedNodes": {}}, "JT_xNnltAY": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON><PERSON> tác bán hàng ch<PERSON>h thức của oneSME by VNPT. <PERSON> kết mang đến dịch vụ và hỗ trợ tốt nhất.", "fontSize": 16, "fontWeight": 400, "color": "#9ca3af"}, "tablet": {"text": "<PERSON><PERSON><PERSON> tác bán hàng ch<PERSON>h thức của oneSME by VNPT. <PERSON> kết mang đến dịch vụ và hỗ trợ tốt nhất.", "fontSize": 16, "fontWeight": 400, "color": "#9ca3af"}, "mobile": {"text": "<PERSON><PERSON><PERSON> tác bán hàng ch<PERSON>h thức của oneSME by VNPT. <PERSON> kết mang đến dịch vụ và hỗ trợ tốt nhất.", "fontSize": 14, "fontWeight": 400, "color": "#9ca3af", "textAlign": "center"}, "id": "description"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "1vMXXAdn1N", "hidden": false, "nodes": [], "linkedNodes": {}}, "6yU57QPmKo": {"type": {"resolvedName": "Column"}, "isCanvas": true, "props": {"desktop": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "text": "Text", "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "imgSrc": null, "externalLink": null, "imgSrcMobile": null, "externalLinkMobile": null, "typeUpload": "EXIST", "imageFit": "fill", "shadow": 0, "width": "100%", "height": "auto", "border": {"style": "none", "color": "#212D6E", "thickness": 0, "radius": 0}, "backgroundType": "", "borderStyle": "none", "contentAlign": "start", "justifyContent": "center", "align": "center", "colWidths": [12], "gap": 4, "isBreakLine": false, "hide": false}, "tablet": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "text": "Text", "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "imgSrc": null, "externalLink": null, "imgSrcMobile": null, "externalLinkMobile": null, "typeUpload": "EXIST", "imageFit": "fill", "shadow": 0, "width": "100%", "height": "auto", "border": {"style": "none", "color": "#212D6E", "thickness": 0, "radius": 0}, "backgroundType": "", "borderStyle": "none", "contentAlign": "start", "justifyContent": "center", "align": "center", "colWidths": [12], "gap": 4, "isBreakLine": false, "hide": false}, "mobile": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "text": "Text", "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "imgSrc": null, "externalLink": null, "imgSrcMobile": null, "externalLinkMobile": null, "typeUpload": "EXIST", "imageFit": "fill", "shadow": 0, "width": "100%", "height": "auto", "border": {"style": "none", "color": "#212D6E", "thickness": 0, "radius": 0}, "backgroundType": "", "borderStyle": "none", "contentAlign": "start", "justifyContent": "center", "align": "center", "colWidths": [6], "gap": 4, "isBreakLine": false, "hide": false}, "id": "links-column"}, "displayName": "Column", "custom": {}, "parent": "VYdNWbiEcq", "hidden": false, "nodes": ["pMkgyGJmOV", "yhNJnBYpFb", "qCQUdMppvb", "Hx99dXz_YI", "CBu45e87J_"], "linkedNodes": {}}, "pMkgyGJmOV": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON><PERSON>h", "fontSize": 18, "fontWeight": 700, "color": "#ffffff"}, "tablet": {"text": "<PERSON><PERSON><PERSON>h", "fontSize": 18, "fontWeight": 700, "color": "#ffffff"}, "mobile": {"text": "<PERSON><PERSON><PERSON>h", "fontSize": 18, "fontWeight": 700, "color": "#ffffff", "textAlign": "center"}, "id": "links-title"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "6yU57QPmKo", "hidden": false, "nodes": [], "linkedNodes": {}}, "yhNJnBYpFb": {"type": {"resolvedName": "Spacer"}, "isCanvas": true, "props": {"desktop": {"height": 16}, "tablet": {"height": 16}, "mobile": {"height": 16}, "id": "spacer2"}, "displayName": "<PERSON><PERSON><PERSON><PERSON> trắng", "custom": {}, "parent": "6yU57QPmKo", "hidden": false, "nodes": [], "linkedNodes": {}}, "qCQUdMppvb": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON> chúng tôi", "fontSize": 16, "fontWeight": 400, "color": "#9ca3af"}, "tablet": {"text": "<PERSON><PERSON> chúng tôi", "fontSize": 16, "fontWeight": 400, "color": "#9ca3af"}, "mobile": {"text": "<PERSON><PERSON> chúng tôi", "fontSize": 14, "fontWeight": 400, "color": "#9ca3af", "textAlign": "center"}, "id": "about-link"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "6yU57QPmKo", "hidden": false, "nodes": [], "linkedNodes": {}}, "Hx99dXz_YI": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON><PERSON> v<PERSON>", "fontSize": 16, "fontWeight": 400, "color": "#9ca3af", "margin": {"top": 4, "bottom": 0}}, "tablet": {"text": "<PERSON><PERSON><PERSON> v<PERSON>", "fontSize": 16, "fontWeight": 400, "color": "#9ca3af", "margin": {"top": 4, "bottom": 0}}, "mobile": {"text": "<PERSON><PERSON><PERSON> v<PERSON>", "fontSize": 14, "fontWeight": 400, "color": "#9ca3af", "textAlign": "center", "margin": {"top": 4, "bottom": 0}}, "id": "services-link"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "6yU57QPmKo", "hidden": false, "nodes": [], "linkedNodes": {}}, "CBu45e87J_": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 16, "fontWeight": 400, "color": "#9ca3af", "margin": {"top": 4, "bottom": 0}}, "tablet": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 16, "fontWeight": 400, "color": "#9ca3af", "margin": {"top": 4, "bottom": 0}}, "mobile": {"text": "<PERSON><PERSON><PERSON>", "fontSize": 14, "fontWeight": 400, "color": "#9ca3af", "textAlign": "center", "margin": {"top": 4, "bottom": 0}}, "id": "policy-link"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "6yU57QPmKo", "hidden": false, "nodes": [], "linkedNodes": {}}, "KerA8FkJLz": {"type": {"resolvedName": "Column"}, "isCanvas": true, "props": {"desktop": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "text": "Text", "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "imgSrc": null, "externalLink": null, "imgSrcMobile": null, "externalLinkMobile": null, "typeUpload": "EXIST", "imageFit": "fill", "shadow": 0, "width": "100%", "height": "auto", "border": {"style": "none", "color": "#212D6E", "thickness": 0, "radius": 0}, "backgroundType": "", "borderStyle": "none", "contentAlign": "start", "justifyContent": "center", "align": "center", "colWidths": [12], "gap": 4, "isBreakLine": false, "hide": false}, "tablet": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "text": "Text", "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "imgSrc": null, "externalLink": null, "imgSrcMobile": null, "externalLinkMobile": null, "typeUpload": "EXIST", "imageFit": "fill", "shadow": 0, "width": "100%", "height": "auto", "border": {"style": "none", "color": "#212D6E", "thickness": 0, "radius": 0}, "backgroundType": "", "borderStyle": "none", "contentAlign": "start", "justifyContent": "center", "align": "center", "colWidths": [12], "gap": 4, "isBreakLine": false, "hide": false}, "mobile": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "text": "Text", "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "imgSrc": null, "externalLink": null, "imgSrcMobile": null, "externalLinkMobile": null, "typeUpload": "EXIST", "imageFit": "fill", "shadow": 0, "width": "100%", "height": "auto", "border": {"style": "none", "color": "#212D6E", "thickness": 0, "radius": 0}, "backgroundType": "", "borderStyle": "none", "contentAlign": "start", "justifyContent": "center", "align": "center", "colWidths": [6], "gap": 4, "isBreakLine": false, "hide": false}, "id": "social-column"}, "displayName": "Column", "custom": {}, "parent": "VYdNWbiEcq", "hidden": false, "nodes": ["wF55WUgNei", "GwZIV7Yr3J", "jmoiBEj64G"], "linkedNodes": {}}, "wF55WUgNei": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "<PERSON><PERSON><PERSON> n<PERSON>i với tôi", "fontSize": 18, "fontWeight": 700, "color": "#ffffff"}, "tablet": {"text": "<PERSON><PERSON><PERSON> n<PERSON>i với tôi", "fontSize": 18, "fontWeight": 700, "color": "#ffffff"}, "mobile": {"text": "<PERSON><PERSON><PERSON> n<PERSON>i với tôi", "fontSize": 18, "fontWeight": 700, "color": "#ffffff", "textAlign": "center"}, "id": "social-title"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "KerA8FkJLz", "hidden": false, "nodes": [], "linkedNodes": {}}, "GwZIV7Yr3J": {"type": {"resolvedName": "Spacer"}, "isCanvas": true, "props": {"desktop": {"height": 16}, "tablet": {"height": 16}, "mobile": {"height": 16}, "id": "spacer3"}, "displayName": "<PERSON><PERSON><PERSON><PERSON> trắng", "custom": {}, "parent": "KerA8FkJLz", "hidden": false, "nodes": [], "linkedNodes": {}}, "jmoiBEj64G": {"type": "div", "isCanvas": false, "props": {"className": "flex gap-2"}, "displayName": "div", "custom": {}, "parent": "KerA8FkJLz", "hidden": false, "nodes": ["avDO3QDaaP"], "linkedNodes": {}}, "avDO3QDaaP": {"type": {"resolvedName": "SocialMedia"}, "isCanvas": true, "props": {"desktop": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "backgroundColor": "#ffffff", "text": "Text", "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "size": 24, "iconAlign": "start", "socialList": [{"name": "Facebook", "icon": "FacebookIcon2", "iconColor": "#9CA3AF", "url": "", "displayType": "ICON"}, {"name": "Email", "icon": "MailFilled", "iconColor": "#9CA3AF", "url": "", "displayType": "ICON"}]}, "tablet": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "backgroundColor": "#ffffff", "text": "Text", "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "size": 24, "iconAlign": "start", "socialList": [{"name": "Facebook", "icon": "FacebookIcon2", "iconColor": "#9CA3AF", "url": "", "displayType": "ICON"}, {"name": "Email", "icon": "MailFilled", "iconColor": "#9CA3AF", "url": "", "displayType": "ICON"}]}, "mobile": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "backgroundColor": "#ffffff", "text": "Text", "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "size": 24, "iconAlign": "start", "socialList": [{"name": "Facebook", "icon": "FacebookIcon2", "iconColor": "#9CA3AF", "url": "", "displayType": "ICON"}, {"name": "Email", "icon": "MailFilled", "iconColor": "#9CA3AF", "url": "", "displayType": "ICON"}]}, "id": "social-media-links"}, "displayName": "Mạng xã hội", "custom": {}, "parent": "jmoiBEj64G", "hidden": false, "nodes": [], "linkedNodes": {}}, "rZXcweTqjP": {"type": {"resolvedName": "Row"}, "isCanvas": true, "props": {"desktop": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "backgroundColor": "#374151", "text": "Text", "padding": {"top": 24, "bottom": 24, "left": 16, "right": 16}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "imgSrc": null, "externalLink": null, "imgSrcMobile": null, "externalLinkMobile": null, "typeUpload": "EXIST", "imageFit": "fit", "shadow": 0, "width": "100%", "height": "auto", "border": {"style": "none", "color": "#212D6E", "thickness": 0, "radius": 0}, "backgroundType": "COLOR", "borderStyle": "none", "contentAlign": "start", "justifyContent": "center", "align": "center", "colWidths": [8, 4], "gap": 0, "isBreakLine": false, "hide": false}, "tablet": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "backgroundColor": "#374151", "text": "Text", "padding": {"top": 24, "bottom": 24, "left": 16, "right": 16}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "imgSrc": null, "externalLink": null, "imgSrcMobile": null, "externalLinkMobile": null, "typeUpload": "EXIST", "imageFit": "fit", "shadow": 0, "width": "100%", "height": "auto", "border": {"style": "none", "color": "#212D6E", "thickness": 0, "radius": 0}, "backgroundType": "COLOR", "borderStyle": "none", "contentAlign": "start", "justifyContent": "center", "align": "center", "colWidths": [8, 4], "gap": 0, "isBreakLine": false, "hide": false}, "mobile": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "backgroundColor": "#374151", "text": "Text", "padding": {"top": 24, "bottom": 24, "left": 16, "right": 16}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "imgSrc": null, "externalLink": null, "imgSrcMobile": null, "externalLinkMobile": null, "typeUpload": "EXIST", "imageFit": "fit", "shadow": 0, "width": "100%", "height": "auto", "border": {"style": "none", "color": "#212D6E", "thickness": 0, "radius": 0}, "backgroundType": "COLOR", "borderStyle": "none", "contentAlign": "start", "justifyContent": "center", "align": "center", "colWidths": [6], "gap": 0, "isBreakLine": true, "hide": false}, "id": "footer-bottom-row"}, "displayName": "Row", "custom": {}, "parent": "OOEo5T8WEN", "hidden": false, "nodes": ["7qpby9-_6c", "SzuYHKqXbq"], "linkedNodes": {}}, "7qpby9-_6c": {"type": {"resolvedName": "Column"}, "isCanvas": true, "props": {"desktop": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "text": "Text", "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "imgSrc": null, "externalLink": null, "imgSrcMobile": null, "externalLinkMobile": null, "typeUpload": "EXIST", "imageFit": "fill", "shadow": 0, "width": "100%", "height": "auto", "border": {"style": "none", "color": "#212D6E", "thickness": 0, "radius": 0}, "backgroundType": "", "borderStyle": "none", "contentAlign": "start", "justifyContent": "center", "align": "center", "colWidths": [12], "gap": 4, "isBreakLine": false, "hide": false}, "tablet": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "text": "Text", "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "imgSrc": null, "externalLink": null, "imgSrcMobile": null, "externalLinkMobile": null, "typeUpload": "EXIST", "imageFit": "fill", "shadow": 0, "width": "100%", "height": "auto", "border": {"style": "none", "color": "#212D6E", "thickness": 0, "radius": 0}, "backgroundType": "", "borderStyle": "none", "contentAlign": "start", "justifyContent": "center", "align": "center", "colWidths": [12], "gap": 4, "isBreakLine": false, "hide": false}, "mobile": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "text": "Text", "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "imgSrc": null, "externalLink": null, "imgSrcMobile": null, "externalLinkMobile": null, "typeUpload": "EXIST", "imageFit": "fill", "shadow": 0, "width": "100%", "height": "auto", "border": {"style": "none", "color": "#212D6E", "thickness": 0, "radius": 0}, "backgroundType": "", "borderStyle": "none", "contentAlign": "start", "justifyContent": "center", "align": "center", "colWidths": [6], "gap": 4, "isBreakLine": false, "hide": false}, "id": "copyright-column"}, "displayName": "Column", "custom": {}, "parent": "rZXcweTqjP", "hidden": false, "nodes": ["nY0BsMoYwA"], "linkedNodes": {}}, "nY0BsMoYwA": {"type": {"resolvedName": "Text"}, "isCanvas": true, "props": {"desktop": {"text": "© 2025. <PERSON><PERSON> tư vấn c<PERSON><PERSON>.", "fontSize": 14, "fontWeight": 400, "color": "#6b7280"}, "tablet": {"text": "© 2025. <PERSON><PERSON> tư vấn c<PERSON><PERSON>.", "fontSize": 14, "fontWeight": 400, "color": "#6b7280"}, "mobile": {"text": "© 2025. <PERSON><PERSON> tư vấn c<PERSON><PERSON>.", "fontSize": 14, "fontWeight": 400, "color": "#6b7280", "textAlign": "center"}, "id": "copyright-text"}, "displayName": "<PERSON><PERSON><PERSON>", "custom": {}, "parent": "7qpby9-_6c", "hidden": false, "nodes": [], "linkedNodes": {}}, "SzuYHKqXbq": {"type": {"resolvedName": "Column"}, "isCanvas": true, "props": {"desktop": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "text": "Text", "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "imgSrc": null, "externalLink": null, "imgSrcMobile": null, "externalLinkMobile": null, "typeUpload": "EXIST", "imageFit": "fill", "shadow": 0, "width": "100%", "height": "auto", "border": {"style": "none", "color": "#212D6E", "thickness": 0, "radius": 0}, "backgroundType": "", "borderStyle": "none", "contentAlign": "start", "justifyContent": "center", "align": "center", "colWidths": [12], "gap": 4, "isBreakLine": false, "hide": false}, "tablet": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "text": "Text", "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "imgSrc": null, "externalLink": null, "imgSrcMobile": null, "externalLinkMobile": null, "typeUpload": "EXIST", "imageFit": "fill", "shadow": 0, "width": "100%", "height": "auto", "border": {"style": "none", "color": "#212D6E", "thickness": 0, "radius": 0}, "backgroundType": "", "borderStyle": "none", "contentAlign": "start", "justifyContent": "center", "align": "center", "colWidths": [12], "gap": 4, "isBreakLine": false, "hide": false}, "mobile": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "text": "Text", "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "imgSrc": null, "externalLink": null, "imgSrcMobile": null, "externalLinkMobile": null, "typeUpload": "EXIST", "imageFit": "fill", "shadow": 0, "width": "100%", "height": "auto", "border": {"style": "none", "color": "#212D6E", "thickness": 0, "radius": 0}, "backgroundType": "", "borderStyle": "none", "contentAlign": "start", "justifyContent": "center", "align": "center", "colWidths": [6], "gap": 4, "isBreakLine": false, "hide": false}, "id": "logos-column"}, "displayName": "Column", "custom": {}, "parent": "rZXcweTqjP", "hidden": false, "nodes": ["LZERYID0DZ"], "linkedNodes": {}}, "LZERYID0DZ": {"type": {"resolvedName": "Image"}, "isCanvas": true, "props": {"desktop": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "backgroundColor": "#ffffff", "text": "Text", "padding": {"top": 0, "right": 24, "bottom": 0, "left": 24}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "hide": false, "link": "", "alt": "", "imgSrc": "https://onesme.vn/assets/images/logo-footer.svg", "imgSrcMobile": "", "externalLink": "", "externalLinkMobile": "", "typeUpload": "EXIST", "imageFit": "fit", "align": "start", "border": {"style": "none", "color": "", "thickness": 0, "radius": 0}, "width": "", "height": "36px", "hasOverlay": false, "imgSrcFallBack": "/assets/images/noImage.svg"}, "tablet": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "backgroundColor": "#ffffff", "text": "Text", "padding": {"top": 0, "right": 24, "bottom": 0, "left": 24}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "hide": false, "link": "", "alt": "", "imgSrc": "https://onesme.vn/assets/images/logo-footer.svg", "imgSrcMobile": "", "externalLink": "", "externalLinkMobile": "", "typeUpload": "EXIST", "imageFit": "fit", "align": "start", "border": {"style": "none", "color": "", "thickness": 0, "radius": 0}, "width": "", "height": "36px", "hasOverlay": false, "imgSrcFallBack": "/assets/images/noImage.svg"}, "mobile": {"fontSize": 16, "fontWeight": 400, "textAlign": "left", "color": "#000000", "backgroundColor": "#ffffff", "text": "Text", "padding": {"top": 0, "right": 24, "bottom": 0, "left": 24}, "margin": {"top": 0, "right": 0, "bottom": 0, "left": 0}, "responsive": {"desktop": true, "tablet": true, "mobile": true}, "hide": false, "link": "", "alt": "", "imgSrc": "https://onesme.vn/assets/images/logo-footer.svg", "imgSrcMobile": "", "externalLink": "", "externalLinkMobile": "", "typeUpload": "EXIST", "imageFit": "fit", "align": "start", "border": {"style": "none", "color": "", "thickness": 0, "radius": 0}, "width": "", "height": "36px", "hasOverlay": false, "imgSrcFallBack": "/assets/images/noImage.svg"}, "id": "onesme-logo"}, "displayName": "Ảnh", "custom": {}, "parent": "SzuYHKqXbq", "hidden": false, "nodes": [], "linkedNodes": {}}}