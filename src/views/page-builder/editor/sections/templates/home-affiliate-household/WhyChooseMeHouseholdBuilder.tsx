'use client'

import { Element, useNode, type UserComponent } from '@craftjs/core'

import { SectionTitle } from '@/components/page-builder/editor/CustomComponents/IoTTemplate'
import { Container, Column, IconLibrary, Row, Spacer, Text } from '@/components/page-builder/selectors'

export const WhyChooseMeHouseholdBuilder: UserComponent = props => {
  const {
    connectors: { connect, drag }
  } = useNode()

  return (
    <div
      className='w-full'
      ref={(ref: HTMLDivElement | null) => {
        if (ref) {
          connect(drag(ref))
        }
      }}
    >
      <Element id='container' canvas is={Container} {...props.container}>
        <Element is={Row} id='title-row' canvas {...props.titleRow}>
          <Element is={SectionTitle} id='section-title' canvas {...props.sectionTitle} />
        </Element>
        <Element is={Row} id='reasons-row' canvas {...props.reasonsRow}>
          <Element is={Column} id='reason1' canvas {...props.reasonColumn}>
            <Element is={IconLibrary} id='icon1' canvas {...props.icon1} />
            <Element is={Spacer} id='spacer1' canvas {...props.spacer} />
            <Element is={Text} id='title1' canvas {...props.title1} />
            <Element is={Spacer} id='spacer2' canvas {...props.spacer} />
            <Element is={Text} id='desc1' canvas {...props.desc1} />
          </Element>
          <Element is={Column} id='reason2' canvas {...props.reasonColumn}>
            <Element is={IconLibrary} id='icon2' canvas {...props.icon2} />
            <Element is={Spacer} id='spacer3' canvas {...props.spacer} />
            <Element is={Text} id='title2' canvas {...props.title2} />
            <Element is={Spacer} id='spacer4' canvas {...props.spacer} />
            <Element is={Text} id='desc2' canvas {...props.desc2} />
          </Element>
          <Element is={Column} id='reason3' canvas {...props.reasonColumn}>
            <Element is={IconLibrary} id='icon3' canvas {...props.icon3} />
            <Element is={Spacer} id='spacer5' canvas {...props.spacer} />
            <Element is={Text} id='title3' canvas {...props.title3} />
            <Element is={Spacer} id='spacer6' canvas {...props.spacer} />
            <Element is={Text} id='desc3' canvas {...props.desc3} />
          </Element>
        </Element>
      </Element>
    </div>
  )
}

WhyChooseMeHouseholdBuilder.craft = {
  props: {
    container: {
      height: 'auto',
      background: '#FFFFFF',
      padding: ['0', '0', '0', '0']
    },
    titleRow: {
      desktop: {
        backgroundColor: '#f1f5f9',
        backgroundType: 'COLOR',
        padding: { top: 64, bottom: 40, left: 16, right: 16 },
        height: 'auto',
        justifyContent: 'center'
      },
      tablet: {
        backgroundColor: '#f1f5f9',
        backgroundType: 'COLOR',
        padding: { top: 64, bottom: 40, left: 16, right: 16 },
        height: 'auto',
        justifyContent: 'center'
      },
      mobile: {
        backgroundColor: '#f1f5f9',
        backgroundType: 'COLOR',
        padding: { top: 48, bottom: 32, left: 16, right: 16 },
        height: 'auto',
        justifyContent: 'center'
      }
    },
    reasonsRow: {
      desktop: {
        backgroundColor: '#f1f5f9',
        backgroundType: 'COLOR',
        padding: { top: 0, bottom: 64, left: 16, right: 16 },
        colWidths: [4, 4, 4],
        gap: 8,
        height: 'auto'
      },
      tablet: {
        backgroundColor: '#f1f5f9',
        backgroundType: 'COLOR',
        padding: { top: 0, bottom: 64, left: 16, right: 16 },
        colWidths: [4, 4, 4],
        gap: 8,
        height: 'auto'
      },
      mobile: {
        backgroundColor: '#f1f5f9',
        backgroundType: 'COLOR',
        padding: { top: 0, bottom: 48, left: 16, right: 16 },
        isBreakLine: true,
        gap: 8,
        height: 'auto'
      }
    },
    reasonColumn: {
      desktop: { justifyContent: 'center', alignItems: 'center', contentAlign: 'center' },
      tablet: { justifyContent: 'center', alignItems: 'center', contentAlign: 'center' },
      mobile: { justifyContent: 'center', alignItems: 'center', contentAlign: 'center' }
    },
    sectionTitle: {
      desktop: {
        text: 'Tại sao chọn tôi?',
        fontSize: 32,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'center',
        marginBottom: 40
      },
      tablet: {
        text: 'Tại sao chọn tôi?',
        fontSize: 28,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'center',
        marginBottom: 32
      },
      mobile: {
        text: 'Tại sao chọn tôi?',
        fontSize: 24,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'center',
        marginBottom: 24
      }
    },
    icon1: {
      desktop: {
        icon: 'BulbFilled',
        iconColor: '#00529c',
        size: 24,
        bgIconColor: '#dbeafe',
        borderIcon: {
          radius: 50
        },
        paddingIcon: { top: 20, bottom: 20, left: 20, right: 20 },
        width: 64,
        height: 64,
        showIcon: { desktop: true, tablet: true, mobile: true },
        showTitle: { desktop: false, tablet: false, mobile: false },
        showDesc: { desktop: false, tablet: false, mobile: false },
        padding: { top: 0, bottom: 0, left: 0, right: 0 },
        margin: { top: 0, bottom: 0 },
        title: '',
        desc: '',
        backgroundType: 'NONE'
      },
      tablet: {
        icon: 'BulbFilled',
        iconColor: '#00529c',
        size: 24,
        bgIconColor: '#dbeafe',
        borderIcon: {
          radius: 50
        },
        paddingIcon: { top: 20, bottom: 20, left: 20, right: 20 },
        width: 64,
        height: 64,
        showIcon: { desktop: true, tablet: true, mobile: true },
        showTitle: { desktop: false, tablet: false, mobile: false },
        showDesc: { desktop: false, tablet: false, mobile: false },
        padding: { top: 0, bottom: 0, left: 0, right: 0 },
        margin: { top: 0, bottom: 0 },
        title: '',
        desc: '',
        backgroundType: 'NONE'
      },
      mobile: {
        icon: 'BulbFilled',
        iconColor: '#00529c',
        size: 24,
        bgIconColor: '#dbeafe',
        borderIcon: {
          radius: 50
        },
        paddingIcon: { top: 20, bottom: 20, left: 20, right: 20 },
        width: 64,
        height: 64,
        showIcon: { desktop: true, tablet: true, mobile: true },
        showTitle: { desktop: false, tablet: false, mobile: false },
        showDesc: { desktop: false, tablet: false, mobile: false },
        padding: { top: 0, bottom: 0, left: 0, right: 0 },
        margin: { top: 0, bottom: 0 },
        title: '',
        desc: '',
        backgroundType: 'NONE'
      }
    },
    title1: {
      desktop: { text: 'Tư vấn đúng nhu cầu', fontSize: 20, fontWeight: 700, color: '#000000', textAlign: 'center' },
      tablet: { text: 'Tư vấn đúng nhu cầu', fontSize: 20, fontWeight: 700, color: '#000000', textAlign: 'center' },
      mobile: { text: 'Tư vấn đúng nhu cầu', fontSize: 20, fontWeight: 700, color: '#000000', textAlign: 'center' }
    },
    desc1: {
      desktop: {
        text: 'Luôn lắng nghe và phân tích để đưa ra giải pháp phù hợp nhất, không tư vấn thừa.',
        fontSize: 16,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'center'
      },
      tablet: {
        text: 'Luôn lắng nghe và phân tích để đưa ra giải pháp phù hợp nhất, không tư vấn thừa.',
        fontSize: 16,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'center'
      },
      mobile: {
        text: 'Luôn lắng nghe và phân tích để đưa ra giải pháp phù hợp nhất, không tư vấn thừa.',
        fontSize: 14,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'center'
      }
    },
    icon2: {
      desktop: {
        icon: 'TagsFilled',
        iconColor: '#f7941e',
        size: 24,
        bgIconColor: '#fed7aa',
        borderIcon: {
          radius: 50
        },
        paddingIcon: { top: 20, bottom: 20, left: 20, right: 20 },
        width: 64,
        height: 64,
        showIcon: { desktop: true, tablet: true, mobile: true },
        showTitle: { desktop: false, tablet: false, mobile: false },
        showDesc: { desktop: false, tablet: false, mobile: false },
        padding: { top: 0, bottom: 0, left: 0, right: 0 },
        margin: { top: 0, bottom: 0 },
        title: '',
        desc: '',
        backgroundType: 'NONE'
      },
      tablet: {
        icon: 'TagsFilled',
        iconColor: '#f7941e',
        size: 24,
        bgIconColor: '#fed7aa',
        borderIcon: {
          radius: 50
        },
        paddingIcon: { top: 20, bottom: 20, left: 20, right: 20 },
        width: 64,
        height: 64,
        showIcon: { desktop: true, tablet: true, mobile: true },
        showTitle: { desktop: false, tablet: false, mobile: false },
        showDesc: { desktop: false, tablet: false, mobile: false },
        padding: { top: 0, bottom: 0, left: 0, right: 0 },
        margin: { top: 0, bottom: 0 },
        title: '',
        desc: '',
        backgroundType: 'NONE'
      },
      mobile: {
        icon: 'TagsFilled',
        iconColor: '#f7941e',
        size: 24,
        bgIconColor: '#fed7aa',
        borderIcon: {
          radius: 50
        },
        paddingIcon: { top: 20, bottom: 20, left: 20, right: 20 },
        width: 64,
        height: 64,
        showIcon: { desktop: true, tablet: true, mobile: true },
        showTitle: { desktop: false, tablet: false, mobile: false },
        showDesc: { desktop: false, tablet: false, mobile: false },
        padding: { top: 0, bottom: 0, left: 0, right: 0 },
        margin: { top: 0, bottom: 0 },
        title: '',
        desc: '',
        backgroundType: 'NONE'
      }
    },
    title2: {
      desktop: { text: 'Giá & Ưu đãi tốt nhất', fontSize: 20, fontWeight: 700, color: '#000000', textAlign: 'center' },
      tablet: { text: 'Giá & Ưu đãi tốt nhất', fontSize: 20, fontWeight: 700, color: '#000000', textAlign: 'center' },
      mobile: { text: 'Giá & Ưu đãi tốt nhất', fontSize: 20, fontWeight: 700, color: '#000000', textAlign: 'center' }
    },
    desc2: {
      desktop: {
        text: 'Giúp bạn tiếp cận các chương trình khuyến mại và gói cước tiết kiệm nhất từ VNPT.',
        fontSize: 16,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'center'
      },
      tablet: {
        text: 'Giúp bạn tiếp cận các chương trình khuyến mại và gói cước tiết kiệm nhất từ VNPT.',
        fontSize: 16,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'center'
      },
      mobile: {
        text: 'Giúp bạn tiếp cận các chương trình khuyến mại và gói cước tiết kiệm nhất từ VNPT.',
        fontSize: 14,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'center'
      }
    },
    icon3: {
      desktop: {
        icon: 'CustomerServiceFilled',
        iconColor: '#16a34a',
        size: 24,
        bgIconColor: '#bbf7d0',
        borderIcon: {
          radius: 50
        },
        paddingIcon: { top: 20, bottom: 20, left: 20, right: 20 },
        width: 64,
        height: 64,
        showIcon: { desktop: true, tablet: true, mobile: true },
        showTitle: { desktop: false, tablet: false, mobile: false },
        showDesc: { desktop: false, tablet: false, mobile: false },
        padding: { top: 0, bottom: 0, left: 0, right: 0 },
        margin: { top: 0, bottom: 0 },
        title: '',
        desc: '',
        backgroundType: 'NONE'
      },
      tablet: {
        icon: 'CustomerServiceFilled',
        iconColor: '#16a34a',
        size: 24,
        bgIconColor: '#bbf7d0',
        borderIcon: {
          radius: 50
        },
        paddingIcon: { top: 20, bottom: 20, left: 20, right: 20 },
        width: 64,
        height: 64,
        showIcon: { desktop: true, tablet: true, mobile: true },
        showTitle: { desktop: false, tablet: false, mobile: false },
        showDesc: { desktop: false, tablet: false, mobile: false },
        padding: { top: 0, bottom: 0, left: 0, right: 0 },
        margin: { top: 0, bottom: 0 },
        title: '',
        desc: '',
        backgroundType: 'NONE'
      },
      mobile: {
        icon: 'CustomerServiceFilled',
        iconColor: '#16a34a',
        size: 24,
        bgIconColor: '#bbf7d0',
        borderIcon: {
          radius: 50
        },
        paddingIcon: { top: 20, bottom: 20, left: 20, right: 20 },
        width: 64,
        height: 64,
        showIcon: { desktop: true, tablet: true, mobile: true },
        showTitle: { desktop: false, tablet: false, mobile: false },
        showDesc: { desktop: false, tablet: false, mobile: false },
        padding: { top: 0, bottom: 0, left: 0, right: 0 },
        margin: { top: 0, bottom: 0 },
        title: '',
        desc: '',
        backgroundType: 'NONE'
      }
    },
    title3: {
      desktop: { text: 'Hỗ trợ trọn đời', fontSize: 20, fontWeight: 700, color: '#000000', textAlign: 'center' },
      tablet: { text: 'Hỗ trợ trọn đời', fontSize: 20, fontWeight: 700, color: '#000000', textAlign: 'center' },
      mobile: { text: 'Hỗ trợ trọn đời', fontSize: 20, fontWeight: 700, color: '#000000', textAlign: 'center' }
    },
    desc3: {
      desktop: {
        text: 'Đồng hành cùng bạn trong suốt quá trình sử dụng dịch vụ, hỗ trợ 24/7 khi cần.',
        fontSize: 16,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'center'
      },
      tablet: {
        text: 'Đồng hành cùng bạn trong suốt quá trình sử dụng dịch vụ, hỗ trợ 24/7 khi cần.',
        fontSize: 16,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'center'
      },
      mobile: {
        text: 'Đồng hành cùng bạn trong suốt quá trình sử dụng dịch vụ, hỗ trợ 24/7 khi cần.',
        fontSize: 14,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'center'
      }
    },
    spacer: {
      desktop: { height: 16 },
      tablet: { height: 16 },
      mobile: { height: 16 }
    }
  },
  rules: {
    canDrag: () => true,
    canMoveIn: () => false
  }
}
