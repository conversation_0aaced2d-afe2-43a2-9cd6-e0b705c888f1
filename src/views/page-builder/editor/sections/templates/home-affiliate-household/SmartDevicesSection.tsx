'use client'

import React from 'react'

import type { UserComponent } from '@craftjs/core'
import { Element, useNode } from '@craftjs/core'

import { Container, Row, Column, Text, Button, Image } from '@/components/page-builder/selectors'
import { ProductCard } from '@/components/page-builder/editor/CustomComponents/IoTTemplate/ProductCard'

export const SmartDevicesSection: UserComponent = props => {
  const {
    connectors: { connect, drag }
  } = useNode()

  return (
    <div
      className='w-full'
      ref={(ref: HTMLDivElement | null) => {
        if (ref) {
          connect(drag(ref))
        }
      }}
    >
      {/* Smart Devices Section */}
      <Element id='container' canvas is={Container} {...props.container}>
        <Element is={Row} id='smart-devices-section' canvas {...props.smartDevicesSection}>
          <Element is={Column} id='smart-devices-heading' canvas {...props.sectionTitle}>
            <Element is={Text} id='smart-devices-heading' canvas {...props.smartDevicesHeading} />
          </Element>
          <Element id='container-smart-devices' canvas is={Container} {...props.container}>
            <Element is={Row} id='smart-devices-cards' canvas {...props.smartDevicesCards}>
              {/* Card 1: Đèn thông minh */}
              <Element is={Column} id='smart-devices-card-1' canvas {...props.smartDevicesCard}>
                <Element is={ProductCard} id='product1' canvas {...props.productCard}>
                  <div className='rounded-t-lg bg-[#f1f8e9]'>
                    <Element is={Image} id='smart-devices-card-1-image' canvas {...props.smartDevicesCard1Image} />
                  </div>
                  <div className='flex h-full flex-col justify-between p-6'>
                    <div className='flex flex-col'>
                      <Element is={Text} id='smart-devices-card-1-title' canvas {...props.smartDevicesCard1Title} />
                      <Element
                        is={Text}
                        id='smart-devices-card-1-features'
                        canvas
                        {...props.smartDevicesCard1Features}
                      />
                    </div>
                    <div className='flex flex-col'>
                      <Element is={Text} id='smart-devices-card-1-price' canvas {...props.smartDevicesCard1Price} />
                      <Element is={Row} id='smart-devices-card-1-buttons' canvas {...props.smartDevicesCard1Buttons}>
                        <Element
                          is={Button}
                          id='smart-devices-card-1-detail-btn'
                          canvas
                          {...props.smartDevicesCard1DetailBtn}
                        />
                        <Element
                          is={Button}
                          id='smart-devices-card-1-consult-btn'
                          canvas
                          {...props.smartDevicesCard1ConsultBtn}
                        />
                      </Element>
                    </div>
                  </div>
                </Element>
              </Element>

              {/* Card 2: Công tắc thông minh */}
              <Element is={Column} id='smart-devices-card-2' canvas {...props.smartDevicesCard}>
                <Element is={ProductCard} id='product2' canvas {...props.productCard}>
                  <div className='rounded-t-lg bg-[#f1f8e9]'>
                    <Element is={Image} id='smart-devices-card-2-image' canvas {...props.smartDevicesCard2Image} />
                  </div>
                  <div className='flex h-full flex-col justify-between p-6'>
                    <div className='flex flex-col'>
                      <Element is={Text} id='smart-devices-card-2-title' canvas {...props.smartDevicesCard2Title} />
                      <Element
                        is={Text}
                        id='smart-devices-card-2-features'
                        canvas
                        {...props.smartDevicesCard2Features}
                      />
                    </div>
                    <div className='flex flex-col'>
                      <Element is={Text} id='smart-devices-card-2-price' canvas {...props.smartDevicesCard2Price} />
                      <Element is={Row} id='smart-devices-card-2-buttons' canvas {...props.smartDevicesCard2Buttons}>
                        <Element
                          is={Button}
                          id='smart-devices-card-2-detail-btn'
                          canvas
                          {...props.smartDevicesCard2DetailBtn}
                        />
                        <Element
                          is={Button}
                          id='smart-devices-card-2-consult-btn'
                          canvas
                          {...props.smartDevicesCard2ConsultBtn}
                        />
                      </Element>
                    </div>
                  </div>
                </Element>
              </Element>

              {/* Card 3: Ổ cắm thông minh */}
              <Element is={Column} id='smart-devices-card-3' canvas {...props.smartDevicesCard}>
                <Element is={ProductCard} id='product3' canvas {...props.productCard}>
                  <div className='rounded-t-lg bg-[#f1f8e9]'>
                    <Element is={Image} id='smart-devices-card-3-image' canvas {...props.smartDevicesCard3Image} />
                  </div>
                  <div className='flex h-full flex-col justify-between p-6'>
                    <div className='flex flex-col'>
                      <Element is={Text} id='smart-devices-card-3-title' canvas {...props.smartDevicesCard3Title} />
                      <Element
                        is={Text}
                        id='smart-devices-card-3-features'
                        canvas
                        {...props.smartDevicesCard3Features}
                      />
                    </div>
                    <div className='flex flex-col'>
                      <Element is={Text} id='smart-devices-card-3-price' canvas {...props.smartDevicesCard3Price} />
                      <Element is={Row} id='smart-devices-card-3-buttons' canvas {...props.smartDevicesCard3Buttons}>
                        <Element
                          is={Button}
                          id='smart-devices-card-3-detail-btn'
                          canvas
                          {...props.smartDevicesCard3DetailBtn}
                        />
                        <Element
                          is={Button}
                          id='smart-devices-card-3-consult-btn'
                          canvas
                          {...props.smartDevicesCard3ConsultBtn}
                        />
                      </Element>
                    </div>
                  </div>
                </Element>
              </Element>

              {/* Card 4: Rèm cửa thông minh */}
              <Element is={Column} id='smart-devices-card-4' canvas {...props.smartDevicesCard}>
                <Element is={ProductCard} id='product4' canvas {...props.productCard}>
                  <div className='rounded-t-lg bg-[#f1f8e9]'>
                    <Element is={Image} id='smart-devices-card-4-image' canvas {...props.smartDevicesCard4Image} />
                  </div>
                  <div className='flex h-full flex-col justify-between p-6'>
                    <div className='flex flex-col'>
                      <Element is={Text} id='smart-devices-card-4-title' canvas {...props.smartDevicesCard4Title} />
                      <Element
                        is={Text}
                        id='smart-devices-card-4-features'
                        canvas
                        {...props.smartDevicesCard4Features}
                      />
                    </div>
                    <div className='flex flex-col'>
                      <Element is={Text} id='smart-devices-card-4-price' canvas {...props.smartDevicesCard4Price} />
                      <Element is={Row} id='smart-devices-card-4-buttons' canvas {...props.smartDevicesCard4Buttons}>
                        <Element
                          is={Button}
                          id='smart-devices-card-4-detail-btn'
                          canvas
                          {...props.smartDevicesCard4DetailBtn}
                        />
                        <Element
                          is={Button}
                          id='smart-devices-card-4-consult-btn'
                          canvas
                          {...props.smartDevicesCard4ConsultBtn}
                        />
                      </Element>
                    </div>
                  </div>
                </Element>
              </Element>
            </Element>
          </Element>
        </Element>
      </Element>
    </div>
  )
}

SmartDevicesSection.craft = {
  displayName: 'Smart Devices Section',
  props: {
    // Smart Devices Section
    smartDevicesSection: {
      desktop: {
        padding: { top: 0, bottom: 24, left: 0, right: 0 },
        colWidths: [12],
        gap: 6,
        height: 'auto',
        alignItems: 'center',
        justifyContent: 'center',
        width: '100%'
      },
      tablet: {
        padding: { top: 0, bottom: 24, left: 0, right: 0 },
        colWidths: [12],
        gap: 6,
        height: 'auto',
        alignItems: 'center',
        justifyContent: 'center',
        width: '100%'
      },
      mobile: {
        padding: { top: 0, bottom: 24, left: 0, right: 0 },
        colWidths: [12],
        gap: 6,
        height: 'auto',
        alignItems: 'center',
        justifyContent: 'center',
        width: '100%'
      }
    },
    sectionTitle: {
      desktop: {
        margin: { top: 0, bottom: 16, left: 0, right: 0 }
      },
      tablet: {
        margin: { top: 0, bottom: 16, left: 0, right: 0 }
      },
      mobile: {
        margin: { top: 0, bottom: 16, left: 0, right: 0 }
      }
    },
    smartDevicesHeading: {
      desktop: {
        text: '#Thiết bị thông minh',
        fontSize: 24,
        fontWeight: 700,
        color: '#374151',
        textAlign: 'left'
      },
      tablet: {
        text: '#Thiết bị thông minh',
        fontSize: 22,
        fontWeight: 700,
        color: '#374151',
        textAlign: 'left'
      },
      mobile: {
        text: '#Thiết bị thông minh',
        fontSize: 20,
        fontWeight: 700,
        color: '#374151',
        textAlign: 'left'
      }
    },
    smartDevicesCards: {
      desktop: {
        colWidths: [3, 3, 3, 3],
        padding: { top: 0, bottom: 24, left: 0, right: 0 },
        gap: 6,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'center',
        width: '100%'
      },
      tablet: {
        colWidths: [6, 6],
        gap: 6,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'center',
        width: '100%',
        isBreakLine: true
      },
      mobile: {
        colWidths: [12],
        gap: 6,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'center',
        width: '100%',
        isBreakLine: true
      }
    },
    productCard: {
      desktop: {
        backgroundColor: '#ffffff',
        borderRadius: 8,
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
        hoverTransform: 'translateY(-5px)',
        hoverBoxShadow: '0 10px 25px rgba(0,0,0,0.1)',
        transition: 'transform 0.3s ease, box-shadow 0.3s ease',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        minHeight: 400
      },
      tablet: {
        backgroundColor: '#ffffff',
        borderRadius: 8,
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
        hoverTransform: 'translateY(-5px)',
        hoverBoxShadow: '0 10px 25px rgba(0,0,0,0.1)',
        transition: 'transform 0.3s ease, box-shadow 0.3s ease',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        minHeight: 400
      },
      mobile: {
        backgroundColor: '#ffffff',
        borderRadius: 8,
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
        hoverTransform: 'translateY(-5px)',
        hoverBoxShadow: '0 10px 25px rgba(0,0,0,0.1)',
        transition: 'transform 0.3s ease, box-shadow 0.3s ease',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        minHeight: 400
      }
    },
    // Smart Devices Card 1: Đèn thông minh
    smartDevicesCard: {
      desktop: {
        backgroundColor: '#ffffff',
        backgroundType: 'COLOR',
        padding: { top: 0, bottom: 0, left: 0, right: 0 },
        gap: 0,
        height: '100%',
        alignItems: 'stretch',
        contentAlign: 'start',
        justifyContent: 'center',
        width: '100%',
        border: {
          radius: 8
        }
      },
      tablet: {
        backgroundColor: '#ffffff',
        backgroundType: 'COLOR',
        padding: { top: 0, bottom: 0, left: 0, right: 0 },
        gap: 0,
        height: '100%',
        alignItems: 'stretch',
        contentAlign: 'start',
        justifyContent: 'center',
        width: '100%',
        border: {
          radius: 8
        }
      },
      mobile: {
        backgroundColor: '#ffffff',
        backgroundType: 'COLOR',
        padding: { top: 0, bottom: 0, left: 0, right: 0 },
        margin: { top: 0, bottom: 16, left: 0, right: 0 },
        gap: 0,
        height: '100%',
        alignItems: 'stretch',
        contentAlign: 'start',
        justifyContent: 'center',
        width: '100%',
        border: {
          radius: 8
        }
      }
    },
    smartDevicesCard1Image: {
      desktop: {
        imgSrc: 'https://placehold.co/600x400/F1F8E9/33691E?text=Đèn+Thông+Minh',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      },
      tablet: {
        imgSrc: 'https://placehold.co/600x400/F1F8E9/33691E?text=Đèn+Thông+Minh',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      },
      mobile: {
        imgSrcMobile: 'https://placehold.co/600x400/F1F8E9/33691E?text=Đèn+Thông+Minh',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      }
    },
    smartDevicesCard1Title: {
      desktop: {
        text: 'Đèn thông minh',
        fontSize: 18,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'left'
      },
      tablet: {
        text: 'Đèn thông minh',
        fontSize: 18,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'left'
      },
      mobile: {
        text: 'Đèn thông minh',
        fontSize: 18,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'left'
      }
    },
    smartDevicesCard1Features: {
      desktop: {
        text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Thay đổi màu sắc, độ sáng<br>✓ Điều khiển qua điện thoại</div>',
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'left'
      },
      tablet: {
        text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Thay đổi màu sắc, độ sáng<br>✓ Điều khiển qua điện thoại</div>',
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'left'
      },
      mobile: {
        text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Thay đổi màu sắc, độ sáng<br>✓ Điều khiển qua điện thoại</div>',
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'left'
      }
    },
    smartDevicesCard1Price: {
      desktop: {
        text: 'Liên hệ',
        fontSize: 20,
        fontWeight: 700,
        color: '#f7941e',
        textAlign: 'left',
        margin: { top: 'auto', bottom: 0, left: 0, right: 0 }
      },
      tablet: {
        text: 'Liên hệ',
        fontSize: 20,
        fontWeight: 700,
        color: '#f7941e',
        textAlign: 'left',
        margin: { top: 'auto', bottom: 0, left: 0, right: 0 }
      },
      mobile: {
        text: 'Liên hệ',
        fontSize: 20,
        fontWeight: 700,
        color: '#f7941e',
        textAlign: 'left',
        margin: { top: 'auto', bottom: 0, left: 0, right: 0 }
      }
    },
    smartDevicesCard1Buttons: {
      desktop: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      },
      tablet: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      },
      mobile: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      }
    },
    smartDevicesCard1DetailBtn: {
      desktop: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      tablet: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      mobile: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      }
    },
    smartDevicesCard1ConsultBtn: {
      desktop: {
        text: 'Tư vấn',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      tablet: {
        text: 'Tư vấn',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      mobile: {
        text: 'Tư vấn',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      }
    },
    // Smart Devices Card 2: Công tắc thông minh
    smartDevicesCard2Image: {
      desktop: {
        imgSrc: 'https://placehold.co/600x400/F1F8E9/33691E?text=Công+tắc',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      },
      tablet: {
        imgSrc: 'https://placehold.co/600x400/F1F8E9/33691E?text=Công+tắc',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      },
      mobile: {
        imgSrcMobile: 'https://placehold.co/600x400/F1F8E9/33691E?text=Công+tắc',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      }
    },
    smartDevicesCard2Title: {
      desktop: { text: 'Công tắc thông minh', fontSize: 18, fontWeight: 700, color: '#00529c', textAlign: 'left' },
      tablet: { text: 'Công tắc thông minh', fontSize: 18, fontWeight: 700, color: '#00529c', textAlign: 'left' },
      mobile: { text: 'Công tắc thông minh', fontSize: 18, fontWeight: 700, color: '#00529c', textAlign: 'left' }
    },
    smartDevicesCard2Features: {
      desktop: {
        text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Bật/tắt từ xa<br>✓ Thiết kế sang trọng</div>',
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'left'
      },
      tablet: {
        text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Bật/tắt từ xa<br>✓ Thiết kế sang trọng</div>',
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'left'
      },
      mobile: {
        text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Bật/tắt từ xa<br>✓ Thiết kế sang trọng</div>',
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'left'
      }
    },
    smartDevicesCard2Price: {
      desktop: {
        text: 'Liên hệ',
        fontSize: 20,
        fontWeight: 700,
        color: '#f7941e',
        textAlign: 'left',
        margin: { top: 'auto', bottom: 0, left: 0, right: 0 }
      },
      tablet: {
        text: 'Liên hệ',
        fontSize: 20,
        fontWeight: 700,
        color: '#f7941e',
        textAlign: 'left',
        margin: { top: 'auto', bottom: 0, left: 0, right: 0 }
      },
      mobile: {
        text: 'Liên hệ',
        fontSize: 20,
        fontWeight: 700,
        color: '#f7941e',
        textAlign: 'left',
        margin: { top: 'auto', bottom: 0, left: 0, right: 0 }
      }
    },
    smartDevicesCard2Buttons: {
      desktop: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      },
      tablet: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      },
      mobile: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      }
    },
    smartDevicesCard2DetailBtn: {
      desktop: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      tablet: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      mobile: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      }
    },
    smartDevicesCard2ConsultBtn: {
      desktop: {
        text: 'Tư vấn',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      tablet: {
        text: 'Tư vấn',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      mobile: {
        text: 'Tư vấn',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      }
    },
    // Smart Devices Card 3: Ổ cắm thông minh
    smartDevicesCard3Image: {
      desktop: {
        imgSrc: 'https://placehold.co/600x400/F1F8E9/33691E?text=Ổ+cắm',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      },
      tablet: {
        imgSrc: 'https://placehold.co/600x400/F1F8E9/33691E?text=Ổ+cắm',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      },
      mobile: {
        imgSrcMobile: 'https://placehold.co/600x400/F1F8E9/33691E?text=Ổ+cắm',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      }
    },
    smartDevicesCard3Title: {
      desktop: { text: 'Ổ cắm thông minh', fontSize: 18, fontWeight: 700, color: '#00529c', textAlign: 'left' },
      tablet: { text: 'Ổ cắm thông minh', fontSize: 18, fontWeight: 700, color: '#00529c', textAlign: 'left' },
      mobile: { text: 'Ổ cắm thông minh', fontSize: 18, fontWeight: 700, color: '#00529c', textAlign: 'left' }
    },
    smartDevicesCard3Features: {
      desktop: {
        text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Biến thiết bị thường thành thông minh<br>✓ Hẹn giờ bật/tắt</div>',
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'left'
      },
      tablet: {
        text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Biến thiết bị thường thành thông minh<br>✓ Hẹn giờ bật/tắt</div>',
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'left'
      },
      mobile: {
        text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Biến thiết bị thường thành thông minh<br>✓ Hẹn giờ bật/tắt</div>',
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'left'
      }
    },
    smartDevicesCard3Price: {
      desktop: {
        text: 'Liên hệ',
        fontSize: 20,
        fontWeight: 700,
        color: '#f7941e',
        textAlign: 'left',
        margin: { top: 'auto', bottom: 0, left: 0, right: 0 }
      },
      tablet: {
        text: 'Liên hệ',
        fontSize: 20,
        fontWeight: 700,
        color: '#f7941e',
        textAlign: 'left',
        margin: { top: 'auto', bottom: 0, left: 0, right: 0 }
      },
      mobile: {
        text: 'Liên hệ',
        fontSize: 20,
        fontWeight: 700,
        color: '#f7941e',
        textAlign: 'left',
        margin: { top: 'auto', bottom: 0, left: 0, right: 0 }
      }
    },
    smartDevicesCard3Buttons: {
      desktop: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      },
      tablet: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      },
      mobile: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      }
    },
    smartDevicesCard3DetailBtn: {
      desktop: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      tablet: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      mobile: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      }
    },
    smartDevicesCard3ConsultBtn: {
      desktop: {
        text: 'Tư vấn',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      tablet: {
        text: 'Tư vấn',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      mobile: {
        text: 'Tư vấn',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      }
    },
    // Smart Devices Card 4: Rèm cửa thông minh
    smartDevicesCard4Image: {
      desktop: {
        imgSrc: 'https://placehold.co/600x400/F1F8E9/33691E?text=Rèm+cửa',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      },
      tablet: {
        imgSrc: 'https://placehold.co/600x400/F1F8E9/33691E?text=Rèm+cửa',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      },
      mobile: {
        imgSrcMobile: 'https://placehold.co/600x400/F1F8E9/33691E?text=Rèm+cửa',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      }
    },
    smartDevicesCard4Title: {
      desktop: { text: 'Rèm cửa thông minh', fontSize: 18, fontWeight: 700, color: '#00529c', textAlign: 'left' },
      tablet: { text: 'Rèm cửa thông minh', fontSize: 18, fontWeight: 700, color: '#00529c', textAlign: 'left' },
      mobile: { text: 'Rèm cửa thông minh', fontSize: 18, fontWeight: 700, color: '#00529c', textAlign: 'left' }
    },
    smartDevicesCard4Features: {
      desktop: {
        text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Tự động đóng/mở<br>✓ Điều khiển bằng giọng nói</div>',
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'left'
      },
      tablet: {
        text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Tự động đóng/mở<br>✓ Điều khiển bằng giọng nói</div>',
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'left'
      },
      mobile: {
        text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Tự động đóng/mở<br>✓ Điều khiển bằng giọng nói</div>',
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'left'
      }
    },
    smartDevicesCard4Price: {
      desktop: {
        text: 'Liên hệ',
        fontSize: 20,
        fontWeight: 700,
        color: '#f7941e',
        textAlign: 'left',
        margin: { top: 'auto', bottom: 0, left: 0, right: 0 }
      },
      tablet: {
        text: 'Liên hệ',
        fontSize: 20,
        fontWeight: 700,
        color: '#f7941e',
        textAlign: 'left',
        margin: { top: 'auto', bottom: 0, left: 0, right: 0 }
      },
      mobile: {
        text: 'Liên hệ',
        fontSize: 20,
        fontWeight: 700,
        color: '#f7941e',
        textAlign: 'left',
        margin: { top: 'auto', bottom: 0, left: 0, right: 0 }
      }
    },
    smartDevicesCard4Buttons: {
      desktop: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      },
      tablet: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      },
      mobile: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      }
    },
    smartDevicesCard4DetailBtn: {
      desktop: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      tablet: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      mobile: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      }
    },
    smartDevicesCard4ConsultBtn: {
      desktop: {
        text: 'Tư vấn',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      tablet: {
        text: 'Tư vấn',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      mobile: {
        text: 'Tư vấn',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      }
    }
  }
}
