'use client'

import { Element, useNode, type UserComponent } from '@craftjs/core'

import { Container, Column, Text, Row, Button, Image } from '@/components/page-builder/selectors'

export const PromotionsHouseholdBuilder: UserComponent = props => {
  const {
    connectors: { connect, drag }
  } = useNode()

  return (
    <div
      className='w-full'
      ref={(ref: HTMLDivElement | null) => {
        if (ref) {
          connect(drag(ref))
        }
      }}
    >
      <Element id='container' canvas is={Container} {...props.container}>
        <Element is={Row} id='promotions-container' canvas {...props.promotionsContainer}>
          <Element is={Column} id='promotions-title-section' canvas {...props.promotionsTitleSection}>
            <Element is={Text} id='promotions-title' canvas {...props.promotionsTitle} />
          </Element>
          <Element id='container-promotions-cards' canvas is={Container} {...props.container}>
            <Element is={Row} id='promotions-cards-row' canvas {...props.promotionsCardsRow}>
              <Element is={Column} id='promotion-card-1' canvas {...props.promotionCard1}>
                <div className='flex h-full flex-col overflow-hidden rounded-lg bg-white shadow-lg'>
                  <div className='overflow-hidden rounded-t-lg bg-white'>
                    <Element is={Image} id='card-1-image' canvas {...props.card1Image} />
                  </div>
                  -
                  <Element is={Column} id='card-1-content' canvas {...props.card1Content}>
                    <Element is={Text} id='card-1-title' canvas {...props.card1Title} />
                    <Element is={Text} id='card-1-description' canvas {...props.card1Description} />
                    <Element is={Text} id='card-1-condition' canvas {...props.card1Condition} />
                    <Element is={Text} id='card-1-time' canvas {...props.card1Time} />
                    <Element is={Row} id='card-1-buttons' canvas {...props.card1Buttons}>
                      <Element is={Button} id='card-1-detail-btn' canvas {...props.card1DetailBtn} />
                      <Element is={Button} id='card-1-apply-btn' canvas {...props.card1ApplyBtn} />
                    </Element>
                  </Element>
                </div>
              </Element>
              <Element is={Column} id='promotion-card-2' canvas {...props.promotionCard2}>
                <div className='flex h-full flex-col overflow-hidden rounded-lg bg-white shadow-lg'>
                  <div className='overflow-hidden rounded-t-lg bg-white'>
                    <Element is={Image} id='card-2-image' canvas {...props.card2Image} />
                  </div>
                  <Element is={Column} id='card-2-content' canvas {...props.card2Content}>
                    <Element is={Text} id='card-2-title' canvas {...props.card2Title} />
                    <Element is={Text} id='card-2-description' canvas {...props.card2Description} />
                    <Element is={Text} id='card-2-condition' canvas {...props.card2Condition} />
                    <Element is={Text} id='card-2-time' canvas {...props.card2Time} />
                    <Element is={Row} id='card-2-buttons' canvas {...props.card2Buttons}>
                      <Element is={Button} id='card-2-detail-btn' canvas {...props.card2DetailBtn} />
                      <Element is={Button} id='card-2-apply-btn' canvas {...props.card2ApplyBtn} />
                    </Element>
                  </Element>
                </div>
              </Element>
              <Element is={Column} id='promotion-card-3' canvas {...props.promotionCard3}>
                <div className='flex h-full flex-col overflow-hidden rounded-lg bg-white shadow-lg'>
                  <div className='overflow-hidden rounded-t-lg bg-white'>
                    <Element is={Image} id='card-3-image' canvas {...props.card3Image} />
                  </div>
                  <Element is={Column} id='card-3-content' canvas {...props.card3Content}>
                    <Element is={Text} id='card-3-title' canvas {...props.card3Title} />
                    <Element is={Text} id='card-3-description' canvas {...props.card3Description} />
                    <Element is={Text} id='card-3-condition' canvas {...props.card3Condition} />
                    <Element is={Text} id='card-3-time' canvas {...props.card3Time} />
                    <Element is={Row} id='card-3-buttons' canvas {...props.card3Buttons}>
                      <Element is={Button} id='card-3-detail-btn' canvas {...props.card3DetailBtn} />
                      <Element is={Button} id='card-3-apply-btn' canvas {...props.card3ApplyBtn} />
                    </Element>
                  </Element>
                </div>
              </Element>
            </Element>
          </Element>
        </Element>
      </Element>
    </div>
  )
}

PromotionsHouseholdBuilder.craft = {
  props: {
    container: {
      height: 'auto',
      background: '#FFFFFF',
      padding: ['0', '0', '0', '0']
    },
    promotionsContainer: {
      desktop: {
        backgroundColor: '#F0F5FA',
        backgroundType: 'COLOR',
        padding: { top: 64, bottom: 40, left: 16, right: 16 },
        colWidths: [12],
        gap: 20,
        height: 'auto',
        alignItems: 'center',
        justifyContent: 'center',
        width: '100%'
      },
      tablet: {
        backgroundColor: '#FOF5FA',
        backgroundType: 'COLOR',
        padding: { top: 48, bottom: 24, left: 16, right: 16 },
        colWidths: [12],
        gap: 20,
        height: 'auto',
        alignItems: 'center',
        justifyContent: 'center',
        width: '100%'
      },
      mobile: {
        backgroundColor: '#F0F5FA',
        backgroundType: 'COLOR',
        padding: { top: 32, bottom: 8, left: 16, right: 16 },
        colWidths: [12],
        gap: 16,
        height: 'auto',
        alignItems: 'center',
        justifyContent: 'center',
        width: '100%'
      }
    },
    promotionsTitleSection: {
      desktop: {
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        flexDirection: 'column',
        margin: { top: 40, bottom: 40, left: 0, right: 0 },
        gap: 0
      },
      tablet: {
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        flexDirection: 'column',
        margin: { top: 40, bottom: 40, left: 0, right: 0 },
        gap: 0
      },
      mobile: {
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        flexDirection: 'column',
        margin: { top: 40, bottom: 40, left: 0, right: 0 },
        gap: 0
      }
    },
    promotionsTitle: {
      desktop: {
        text: 'Ưu đãi tháng 7 cho Gia Đình',
        fontSize: 32,
        fontWeight: 700,
        color: '#004E95',
        textAlign: 'center'
      },
      tablet: {
        text: 'Ưu đãi tháng 7 cho Gia Đình',
        fontSize: 28,
        fontWeight: 700,
        color: '#004E95',
        textAlign: 'center'
      },
      mobile: {
        text: 'Ưu đãi tháng 7 cho Gia Đình',
        fontSize: 24,
        fontWeight: 700,
        color: '#004E95',
        textAlign: 'center'
      }
    },
    promotionsCardsRow: {
      custom: {
        displayName: 'Promotions Card Section'
      },
      desktop: {
        colWidths: [4, 4, 4],
        padding: { top: 0, bottom: 24, left: 0, right: 0 },
        backgroundColor: '#F0F5FA',
        backgroundType: 'COLOR',
        gap: 6,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'center',
        width: '100%'
      },
      tablet: {
        colWidths: [6, 6],
        padding: { top: 0, bottom: 16, left: 0, right: 0 },
        backgroundColor: '#F0F5FA',
        backgroundType: 'COLOR',
        gap: 6,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'center',
        width: '100%',
        isBreakLine: true
      },
      mobile: {
        colWidths: [12],
        padding: { top: 0, bottom: 16, left: 0, right: 0 },
        backgroundColor: '#F0F5FA',
        backgroundType: 'COLOR',
        gap: 6,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'center',
        width: '100%',
        isBreakLine: true
      }
    },
    promotionCard1: {
      desktop: {
        backgroundColor: '#ffffff',
        backgroundType: 'COLOR',
        padding: { top: 0, bottom: 0, left: 0, right: 0 },
        gap: 0,
        height: 'auto',
        alignItems: 'stretch',
        width: '100%',
        border: {
          radius: 8,
          width: 1,
          color: '#e5e7eb',
          style: 'solid'
        },
        shadow: 8
      },
      tablet: {
        backgroundColor: '#ffffff',
        backgroundType: 'COLOR',
        padding: { top: 0, bottom: 0, left: 0, right: 0 },
        gap: 0,
        height: 'auto',
        alignItems: 'stretch',
        width: '100%',
        border: {
          radius: 8,
          width: 1,
          color: '#e5e7eb',
          style: 'solid'
        },
        shadow: 8
      },
      mobile: {
        backgroundColor: '#ffffff',
        backgroundType: 'COLOR',
        padding: { top: 0, bottom: 0, left: 0, right: 0 },
        margin: { top: 0, bottom: 16, left: 0, right: 0 },
        gap: 0,
        height: 'auto',
        alignItems: 'stretch',
        width: '100%',
        border: {
          radius: 8,
          width: 1,
          color: '#e5e7eb',
          style: 'solid'
        },
        shadow: 8
      }
    },
    card1Image: {
      desktop: {
        imgSrc: 'https://placehold.co/600x300/E3F2FD/0277BD?text=Mua+6+Tặng+2',
        width: '100%',
        height: 128,
        border: {
          radius: 0,
          width: 0,
          color: 'transparent',
          style: 'none'
        },
        imageFit: 'fill',
        align: 'center'
      },
      tablet: {
        imgSrc: 'https://placehold.co/600x300/E3F2FD/0277BD?text=Mua+6+Tặng+2',
        width: '100%',
        height: 128,
        border: {
          radius: 0,
          width: 0,
          color: 'transparent',
          style: 'none'
        },
        imageFit: 'fill',
        align: 'center'
      },
      mobile: {
        imgSrcMobile: 'https://placehold.co/600x300/E3F2FD/0277BD?text=Mua+6+Tặng+2',
        width: '100%',
        height: 128,
        border: {
          radius: 0,
          width: 0,
          color: 'transparent',
          style: 'none'
        },
        imageFit: 'fill',
        align: 'center'
      }
    },
    card1Content: {
      desktop: {
        padding: { top: 16, bottom: 16, left: 16, right: 16 },
        gap: 8,
        height: 'auto',
        alignItems: 'stretch',
        flexDirection: 'column'
      },
      tablet: {
        padding: { top: 16, bottom: 16, left: 16, right: 16 },
        gap: 8,
        height: 'auto',
        alignItems: 'stretch',
        flexDirection: 'column'
      },
      mobile: {
        padding: { top: 16, bottom: 16, left: 16, right: 16 },
        gap: 8,
        height: 'auto',
        alignItems: 'stretch',
        flexDirection: 'column'
      }
    },
    card1Title: {
      desktop: {
        text: 'Mua 6 Tặng 2',
        fontSize: 18,
        fontWeight: 700,
        color: '#0F1319',
        textAlign: 'left'
      },
      tablet: {
        text: 'Mua 6 Tặng 2',
        fontSize: 18,
        fontWeight: 700,
        color: '#0F1319',
        textAlign: 'left'
      },
      mobile: {
        text: 'Mua 6 Tặng 2',
        fontSize: 18,
        fontWeight: 700,
        color: '#0F1319',
        textAlign: 'left'
      }
    },
    card1Description: {
      desktop: {
        text: 'Đăng ký Internet 6 tháng, nhận ngay 2 tháng cước miễn phí.',
        fontSize: 14,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'left'
      },
      tablet: {
        text: 'Đăng ký Internet 6 tháng, nhận ngay 2 tháng cước miễn phí.',
        fontSize: 14,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'left'
      },
      mobile: {
        text: 'Đăng ký Internet 6 tháng, nhận ngay 2 tháng cước miễn phí.',
        fontSize: 14,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'left'
      }
    },
    card1Condition: {
      desktop: {
        text: 'Áp dụng: Khách hàng mới.',
        fontSize: 12,
        fontWeight: 400,
        color: '#9ca3af',
        textAlign: 'left',
        icon: 'CheckCircleFilled',
        iconColor: '#10b981',
        hasIcon: true
      },
      tablet: {
        text: 'Áp dụng: Khách hàng mới.',
        fontSize: 12,
        fontWeight: 400,
        color: '#9ca3af',
        textAlign: 'left',
        icon: 'CheckCircleFilled',
        iconColor: '#10b981',
        hasIcon: true
      },
      mobile: {
        text: 'Áp dụng: Khách hàng mới.',
        fontSize: 12,
        fontWeight: 400,
        color: '#9ca3af',
        textAlign: 'left',
        icon: 'CheckCircleFilled',
        iconColor: '#10b981',
        hasIcon: true
      }
    },
    card1Time: {
      desktop: {
        text: 'Thời gian: 01/07 - 31/07/2025.',
        fontSize: 12,
        fontWeight: 400,
        color: '#9ca3af',
        textAlign: 'left',
        icon: 'CalendarFilled',
        iconColor: '#10b981',
        hasIcon: true
      },
      tablet: {
        text: 'Thời gian: 01/07 - 31/07/2025.',
        fontSize: 12,
        fontWeight: 400,
        color: '#9ca3af',
        textAlign: 'left',
        icon: 'CalendarFilled',
        iconColor: '#10b981',
        hasIcon: true
      },
      mobile: {
        text: 'Thời gian: 01/07 - 31/07/2025.',
        fontSize: 12,
        fontWeight: 400,
        color: '#9ca3af',
        textAlign: 'left',
        icon: 'CalendarFilled',
        iconColor: '#10b981',
        hasIcon: true
      }
    },
    card1Buttons: {
      desktop: {
        colWidths: [1, 1],
        gap: 8,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 16, bottom: 0, left: 0, right: 0 }
      },
      tablet: {
        colWidths: [1, 1],
        gap: 8,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 16, bottom: 0, left: 0, right: 0 }
      },
      mobile: {
        colWidths: [1, 1],
        gap: 8,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 16, bottom: 0, left: 0, right: 0 }
      }
    },
    card1DetailBtn: {
      desktop: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      tablet: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      mobile: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      }
    },
    card1ApplyBtn: {
      desktop: {
        text: 'Áp dụng',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#ef9304',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      tablet: {
        text: 'Áp dụng',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#ef9304',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      mobile: {
        text: 'Áp dụng',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#ef9304',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      }
    },
    promotionCard2: {
      desktop: {
        backgroundColor: '#ffffff',
        backgroundType: 'COLOR',
        padding: { top: 0, bottom: 0, left: 0, right: 0 },
        gap: 0,
        height: 'auto',
        alignItems: 'stretch',
        width: '100%',
        border: {
          radius: 8,
          width: 1,
          color: '#e5e7eb',
          style: 'solid'
        },
        shadow: 8
      },
      tablet: {
        backgroundColor: '#ffffff',
        backgroundType: 'COLOR',
        padding: { top: 0, bottom: 0, left: 0, right: 0 },
        gap: 0,
        height: 'auto',
        alignItems: 'stretch',
        width: '100%',
        border: {
          radius: 8,
          width: 1,
          color: '#e5e7eb',
          style: 'solid'
        },
        shadow: 8
      },
      mobile: {
        backgroundColor: '#ffffff',
        backgroundType: 'COLOR',
        padding: { top: 0, bottom: 0, left: 0, right: 0 },
        margin: { top: 0, bottom: 16, left: 0, right: 0 },
        gap: 0,
        height: 'auto',
        alignItems: 'stretch',
        width: '100%',
        border: {
          radius: 8,
          width: 1,
          color: '#e5e7eb',
          style: 'solid'
        },
        shadow: 8
      }
    },
    card2Image: {
      desktop: {
        imgSrc: 'https://placehold.co/600x300/FFF3E0/E65100?text=Tặng+Camera',
        width: '100%',
        height: 128,
        border: {
          radius: 0,
          width: 0,
          color: 'transparent',
          style: 'none'
        },
        imageFit: 'fill',
        align: 'center'
      },
      tablet: {
        imgSrc: 'https://placehold.co/600x300/FFF3E0/E65100?text=Tặng+Camera',
        width: '100%',
        height: 128,
        border: {
          radius: 0,
          width: 0,
          color: 'transparent',
          style: 'none'
        },
        imageFit: 'fill',
        align: 'center'
      },
      mobile: {
        imgSrcMobile: 'https://placehold.co/600x300/FFF3E0/E65100?text=Tặng+Camera',
        width: '100%',
        height: 128,
        border: {
          radius: 0,
          width: 0,
          color: 'transparent',
          style: 'none'
        },
        imageFit: 'fill',
        align: 'center'
      }
    },
    card2Content: {
      desktop: {
        padding: { top: 16, bottom: 16, left: 16, right: 16 },
        gap: 8,
        height: 'auto',
        alignItems: 'stretch',
        flexDirection: 'column'
      },
      tablet: {
        padding: { top: 16, bottom: 16, left: 16, right: 16 },
        gap: 8,
        height: 'auto',
        alignItems: 'stretch',
        flexDirection: 'column'
      },
      mobile: {
        padding: { top: 16, bottom: 16, left: 16, right: 16 },
        gap: 8,
        height: 'auto',
        alignItems: 'stretch',
        flexDirection: 'column'
      }
    },
    card2Title: {
      desktop: {
        text: 'Lắp Internet Tặng Camera',
        fontSize: 18,
        fontWeight: 700,
        color: '#0F1319',
        textAlign: 'left'
      },
      tablet: {
        text: 'Lắp Internet Tặng Camera',
        fontSize: 18,
        fontWeight: 700,
        color: '#0F1319',
        textAlign: 'left'
      },
      mobile: {
        text: 'Lắp Internet Tặng Camera',
        fontSize: 18,
        fontWeight: 700,
        color: '#0F1319',
        textAlign: 'left'
      }
    },
    card2Description: {
      desktop: {
        text: 'Trang bị miễn phí camera giám sát an toàn cho gia đình bạn.',
        fontSize: 14,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'left'
      },
      tablet: {
        text: 'Trang bị miễn phí camera giám sát an toàn cho gia đình bạn.',
        fontSize: 14,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'left'
      },
      mobile: {
        text: 'Trang bị miễn phí camera giám sát an toàn cho gia đình bạn.',
        fontSize: 14,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'left'
      }
    },
    card2Condition: {
      desktop: {
        text: 'Áp dụng: Gói Home Mesh 3+.',
        fontSize: 12,
        fontWeight: 400,
        color: '#9ca3af',
        textAlign: 'left',
        icon: 'CheckCircleFilled',
        iconColor: '#10b981',
        hasIcon: true
      },
      tablet: {
        text: 'Áp dụng: Gói Home Mesh 3+.',
        fontSize: 12,
        fontWeight: 400,
        color: '#9ca3af',
        textAlign: 'left',
        icon: 'CheckCircleFilled',
        iconColor: '#10b981',
        hasIcon: true
      },
      mobile: {
        text: 'Áp dụng: Gói Home Mesh 3+.',
        fontSize: 12,
        fontWeight: 400,
        color: '#9ca3af',
        textAlign: 'left',
        icon: 'CheckCircleFilled',
        iconColor: '#10b981',
        hasIcon: true
      }
    },
    card2Time: {
      desktop: {
        text: 'Thời gian: 01/07 - 31/07/2025.',
        fontSize: 12,
        fontWeight: 400,
        color: '#9ca3af',
        textAlign: 'left',
        icon: 'CalendarFilled',
        iconColor: '#10b981',
        hasIcon: true
      },
      tablet: {
        text: 'Thời gian: 01/07 - 31/07/2025.',
        fontSize: 12,
        fontWeight: 400,
        color: '#9ca3af',
        textAlign: 'left'
      },
      mobile: {
        text: 'Thời gian: 01/07 - 31/07/2025.',
        fontSize: 12,
        fontWeight: 400,
        color: '#9ca3af',
        textAlign: 'left'
      }
    },
    card2Buttons: {
      desktop: {
        colWidths: [1, 1],
        gap: 8,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 16, bottom: 0, left: 0, right: 0 }
      },
      tablet: {
        colWidths: [1, 1],
        gap: 8,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 16, bottom: 0, left: 0, right: 0 }
      },
      mobile: {
        colWidths: [1, 1],
        gap: 8,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 16, bottom: 0, left: 0, right: 0 }
      }
    },
    card2DetailBtn: {
      desktop: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      tablet: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      mobile: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      }
    },
    card2ApplyBtn: {
      desktop: {
        text: 'Áp dụng',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#ef9304',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      tablet: {
        text: 'Áp dụng',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#ef9304',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      mobile: {
        text: 'Áp dụng',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#ef9304',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle'
      }
    },
    promotionCard3: {
      desktop: {
        backgroundColor: '#ffffff',
        backgroundType: 'COLOR',
        padding: { top: 0, bottom: 0, left: 0, right: 0 },
        gap: 0,
        height: 'auto',
        alignItems: 'stretch',
        width: '100%',
        border: {
          radius: 8,
          width: 1,
          color: '#e5e7eb',
          style: 'solid'
        },
        shadow: 8
      },
      tablet: {
        backgroundColor: '#ffffff',
        backgroundType: 'COLOR',
        padding: { top: 0, bottom: 0, left: 0, right: 0 },
        gap: 0,
        height: 'auto',
        alignItems: 'stretch',
        width: '100%',
        border: {
          radius: 8,
          width: 1,
          color: '#e5e7eb',
          style: 'solid'
        },
        shadow: 8
      },
      mobile: {
        backgroundColor: '#ffffff',
        backgroundType: 'COLOR',
        padding: { top: 0, bottom: 0, left: 0, right: 0 },
        margin: { top: 0, bottom: 16, left: 0, right: 0 },
        gap: 0,
        height: 'auto',
        alignItems: 'stretch',
        width: '100%',
        border: {
          radius: 8,
          width: 1,
          color: '#e5e7eb',
          style: 'solid'
        },
        shadow: 8
      }
    },
    card3Image: {
      desktop: {
        imgSrc: 'https://placehold.co/600x300/E8F5E9/1B5E20?text=Tặng+Data',
        width: '100%',
        height: 128,
        border: {
          radius: 0,
          width: 0,
          color: 'transparent',
          style: 'none'
        },
        imageFit: 'fill',
        align: 'center'
      },
      tablet: {
        imgSrc: 'https://placehold.co/600x300/E8F5E9/1B5E20?text=Tặng+Data',
        width: '100%',
        height: 128,
        border: {
          radius: 0,
          width: 0,
          color: 'transparent',
          style: 'none'
        },
        imageFit: 'fill',
        align: 'center'
      },
      mobile: {
        imgSrcMobile: 'https://placehold.co/600x300/E8F5E9/1B5E20?text=Tặng+Data',
        width: '100%',
        height: 128,
        border: {
          radius: 0,
          width: 0,
          color: 'transparent',
          style: 'none'
        },
        imageFit: 'fill',
        align: 'center'
      }
    },
    card3Content: {
      desktop: {
        padding: { top: 16, bottom: 16, left: 16, right: 16 },
        gap: 8,
        height: 'auto',
        alignItems: 'stretch',
        flexDirection: 'column'
      },
      tablet: {
        padding: { top: 16, bottom: 16, left: 16, right: 16 },
        gap: 8,
        height: 'auto',
        alignItems: 'stretch',
        flexDirection: 'column'
      },
      mobile: {
        padding: { top: 16, bottom: 16, left: 16, right: 16 },
        gap: 8,
        height: 'auto',
        alignItems: 'stretch',
        flexDirection: 'column'
      }
    },
    card3Title: {
      desktop: {
        text: 'Combo Gia Đình Tiết Kiệm',
        fontSize: 18,
        fontWeight: 700,
        color: '#0F1319',
        textAlign: 'left'
      },
      tablet: {
        text: 'Combo Gia Đình Tiết Kiệm',
        fontSize: 18,
        fontWeight: 700,
        color: '#0F1319',
        textAlign: 'left'
      },
      mobile: {
        text: 'Combo Gia Đình Tiết Kiệm',
        fontSize: 18,
        fontWeight: 700,
        color: '#0F1319',
        textAlign: 'left'
      }
    },
    card3Description: {
      desktop: {
        text: 'Giảm 20% khi đăng ký trọn bộ Internet - Truyền hình - Di động.',
        fontSize: 14,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'left'
      },
      tablet: {
        text: 'Giảm 20% khi đăng ký trọn bộ Internet - Truyền hình - Di động.',
        fontSize: 14,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'left'
      },
      mobile: {
        text: 'Giảm 20% khi đăng ký trọn bộ Internet - Truyền hình - Di động.',
        fontSize: 14,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'left'
      }
    },
    card3Condition: {
      desktop: {
        text: 'Áp dụng: Gói Home Combo.',
        fontSize: 12,
        fontWeight: 400,
        color: '#9ca3af',
        textAlign: 'left',
        icon: 'CheckCircleFilled',
        iconColor: '#10b981',
        hasIcon: true
      },
      tablet: {
        text: 'Áp dụng: Gói Home Combo.',
        fontSize: 12,
        fontWeight: 400,
        color: '#9ca3af',
        textAlign: 'left',
        icon: 'CheckCircleFilled',
        iconColor: '#10b981',
        hasIcon: true
      },
      mobile: {
        text: 'Áp dụng: Gói Home Combo.',
        fontSize: 12,
        fontWeight: 400,
        color: '#9ca3af',
        textAlign: 'left',
        icon: 'CheckCircleFilled',
        iconColor: '#10b981',
        hasIcon: true
      }
    },
    card3Time: {
      desktop: {
        text: 'Thời gian: 01/07 - 31/07/2025.',
        fontSize: 12,
        fontWeight: 400,
        color: '#9ca3af',
        textAlign: 'left',
        icon: 'CalendarFilled',
        iconColor: '#10b981',
        hasIcon: true
      },
      tablet: {
        text: 'Thời gian: 01/07 - 31/07/2025.',
        fontSize: 12,
        fontWeight: 400,
        color: '#9ca3af',
        textAlign: 'left',
        icon: 'CalendarFilled',
        iconColor: '#10b981',
        hasIcon: true
      },
      mobile: {
        text: 'Thời gian: 01/07 - 31/07/2025.',
        fontSize: 12,
        fontWeight: 400,
        color: '#9ca3af',
        textAlign: 'left',
        icon: 'CalendarFilled',
        iconColor: '#10b981',
        hasIcon: true
      }
    },
    card3Buttons: {
      desktop: {
        colWidths: [1, 1],
        gap: 8,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 16, bottom: 0, left: 0, right: 0 }
      },
      tablet: {
        colWidths: [1, 1],
        gap: 8,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 16, bottom: 0, left: 0, right: 0 }
      },
      mobile: {
        colWidths: [1, 1],
        gap: 8,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 16, bottom: 0, left: 0, right: 0 }
      }
    },
    card3DetailBtn: {
      desktop: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      tablet: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      mobile: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#ef9304',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      }
    },
    card3ApplyBtn: {
      desktop: {
        text: 'Áp dụng',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#ef9304',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      tablet: {
        text: 'Áp dụng',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#ef9304',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      mobile: {
        text: 'Áp dụng',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#ea580c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      }
    }
  },
  rules: {
    canDrag: () => true,
    canMoveIn: () => false
  }
}
