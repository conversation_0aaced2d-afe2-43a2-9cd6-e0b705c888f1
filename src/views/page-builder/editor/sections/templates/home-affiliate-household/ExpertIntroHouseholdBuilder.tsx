'use client'

import { Element, useNode, type UserComponent } from '@craftjs/core'

import { Column, Image, Row, Spacer, Text, Container } from '@/components/page-builder/selectors'

export const ExpertIntroHouseholdBuilder: UserComponent = props => {
  const {
    connectors: { connect, drag }
  } = useNode()

  return (
    <div
      className='w-full'
      ref={(ref: HTMLDivElement | null) => {
        if (ref) {
          connect(drag(ref))
        }
      }}
    >
      <Element id='container' canvas is={Container} {...props.container}>
        <Element is={Row} id='expert-intro-row' canvas {...props.expertIntroRow}>
          <Element is={Column} id='expert-image-column' canvas {...props.expertImageColumn}>
            <Element is={Image} id='expert-image' canvas {...props.expertImage} />
          </Element>
          <Element is={Column} id='expert-content-column' canvas {...props.expertContentColumn}>
            <Element is={Text} id='expert-title' canvas {...props.expertTitle} />
            <Element is={Spacer} id='expert-spacer' canvas {...props.expertSpacer} />
            <Element is={Text} id='expert-description' canvas {...props.expertDescription} />
          </Element>
        </Element>
      </Element>
    </div>
  )
}

ExpertIntroHouseholdBuilder.craft = {
  props: {
    container: {
      height: 'auto',
      background: '#FFFFFF',
      padding: ['0', '0', '0', '0']
    },
    expertIntroRow: {
      desktop: {
        backgroundColor: '#ffffff',
        backgroundType: 'COLOR',
        padding: { top: 64, bottom: 64, left: 16, right: 16 },
        colWidths: [4, 8],
        gap: 20,
        height: 'auto',
        alignItems: 'center'
      },
      tablet: {
        backgroundColor: '#ffffff',
        backgroundType: 'COLOR',
        padding: { top: 64, bottom: 64, left: 16, right: 16 },
        colWidths: [4, 8],
        gap: 20,
        height: 'auto',
        alignItems: 'center'
      },
      mobile: {
        backgroundColor: '#ffffff',
        backgroundType: 'COLOR',
        padding: { top: 48, bottom: 48, left: 16, right: 16 },
        isBreakLine: true,
        gap: 20,
        height: 'auto',
        alignItems: 'center'
      }
    },
    expertImageColumn: {
      desktop: { display: 'flex', justifyContent: 'center', alignItems: 'center', contentAlign: 'start' },
      tablet: { display: 'flex', justifyContent: 'center', alignItems: 'center', contentAlign: 'start' },
      mobile: { display: 'flex', justifyContent: 'center', alignItems: 'center', contentAlign: 'start' }
    },
    expertContentColumn: {
      desktop: { display: 'flex', justifyContent: 'center', alignItems: 'center', contentAlign: 'start' },
      tablet: { display: 'flex', justifyContent: 'center', alignItems: 'center', contentAlign: 'start' },
      mobile: { display: 'flex', justifyContent: 'center', alignItems: 'center', contentAlign: 'start' }
    },
    expertImage: {
      desktop: {
        imgSrc:
          'https://images.unsplash.com/photo-1557862921-37829c790f19?q=80&w=2071&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
        alt: 'Chuyên gia Nguyễn Văn An',
        width: 480,
        height: 320,
        border: {
          radius: 8
        },
        align: 'center',
        imageFit: 'fill'
      },
      tablet: {
        imgSrc:
          'https://images.unsplash.com/photo-1557862921-37829c790f19?q=80&w=2071&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
        alt: 'Chuyên gia Nguyễn Văn An',
        width: 250,
        height: 330,
        border: {
          radius: 8
        },
        align: 'center',
        imageFit: 'fill'
      },
      mobile: {
        imgSrc:
          'https://images.unsplash.com/photo-1557862921-37829c790f19?q=80&w=2071&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
        alt: 'Chuyên gia Nguyễn Văn An',
        width: 200,
        height: 260,
        border: {
          radius: 8
        },
        align: 'center',
        imageFit: 'fill'
      }
    },
    expertTitle: {
      desktop: {
        text: 'Chuyên gia tư vấn Nguyễn Văn An',
        fontSize: 30,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'left'
      },
      tablet: {
        text: 'Chuyên gia tư vấn Nguyễn Văn An',
        fontSize: 28,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'left'
      },
      mobile: {
        text: 'Chuyên gia tư vấn Nguyễn Văn An',
        fontSize: 24,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'center'
      }
    },
    expertSpacer: {
      desktop: { height: 16 },
      tablet: { height: 16 },
      mobile: { height: 16 }
    },
    expertDescription: {
      desktop: {
        text: 'Với mong muốn mang đến những giải pháp công nghệ tốt nhất cho mỗi gia đình Việt, tôi luôn lắng nghe và tư vấn tận tình để giúp bạn xây dựng một "tổ ấm số" hiện đại, an toàn và tiện nghi. Hãy để tôi đồng hành cùng gia đình bạn!',
        fontSize: 16,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'left'
      },
      tablet: {
        text: 'Với mong muốn mang đến những giải pháp công nghệ tốt nhất cho mỗi gia đình Việt, tôi luôn lắng nghe và tư vấn tận tình để giúp bạn xây dựng một "tổ ấm số" hiện đại, an toàn và tiện nghi. Hãy để tôi đồng hành cùng gia đình bạn!',
        fontSize: 16,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'left'
      },
      mobile: {
        text: 'Với mong muốn mang đến những giải pháp công nghệ tốt nhất cho mỗi gia đình Việt, tôi luôn lắng nghe và tư vấn tận tình để giúp bạn xây dựng một "tổ ấm số" hiện đại, an toàn và tiện nghi. Hãy để tôi đồng hành cùng gia đình bạn!',
        fontSize: 14,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'center'
      }
    }
  },
  rules: {
    canDrag: () => true,
    canMoveIn: () => false
  }
}
