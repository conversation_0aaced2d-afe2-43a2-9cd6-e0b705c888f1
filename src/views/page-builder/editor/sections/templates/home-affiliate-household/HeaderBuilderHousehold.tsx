'use client'

import { Element, useNode, type UserComponent } from '@craftjs/core'

import { useSelector } from 'react-redux'
import { styled } from 'styled-components'

import { Image, SocialMedia, Text } from '@/components/page-builder/selectors'
import { useResponsive } from '@/hooks'
import { selectScreenType } from '@/redux-store/slices/builderSlice'
import { defaultResponseSocialMediaProps } from '@/constants/page-builder/socialMedia'

export const HeaderBuilderHousehold: UserComponent = props => {
  const {
    connectors: { connect, drag }
  } = useNode()

  const screenType = useSelector(selectScreenType)
  const { isMobile } = useResponsive()

  return (
    <div
      className='w-full'
      ref={(ref: HTMLDivElement | null) => {
        if (ref) {
          connect(drag(ref))
        }
      }}
    >
      <header className='sticky top-0 z-50 bg-white shadow-sm'>
        <div className='container mx-auto px-4 py-3'>
          <div
            className={
              isMobile || screenType === 'mobile'
                ? 'flex flex-col items-center justify-between'
                : 'flex items-center justify-between sm:flex-col'
            }
          >
            <div className='mb-0 flex items-center gap-4 sm:mb-3'>
              <div className='shrink-0'>
                <Element is={Image} id='logo' canvas {...props.logo} />
              </div>
              <div className='block h-8 border-l border-solid border-gray-300 sm:hidden'></div>
              <div className=' flex items-center gap-3'>
                <StyledUserProfile className='shrink-0'>
                  <Element
                    is={Image}
                    id='expert-avatar'
                    canvas
                    {...props.expertAvatar}
                    className='focus:outline-none'
                  />
                </StyledUserProfile>
                <StyledUserProfile className='shrink-0'>
                  <Element is={Text} id='expert-name' canvas {...props.expertName} className='focus:outline-none' />
                  <Element is={Text} id='expert-title' canvas {...props.expertTitle} className='focus:outline-none' />
                </StyledUserProfile>
              </div>
            </div>
            <div className='flex items-center gap-2 text-sm text-gray-600'>
              <Element is={SocialMedia} id='contact-social-media' canvas {...props.contactSocialMedia} />
              <div className='h-5 border-l border-gray-300'></div>
              <div className='flex items-center gap-1'>
                <Element is={SocialMedia} id='social-media-links' canvas {...props.socialMediaLinks} />
              </div>
            </div>
          </div>
        </div>
      </header>
    </div>
  )
}

HeaderBuilderHousehold.craft = {
  props: {
    logo: {
      desktop: {
        imgSrc: '/assets/images/logo.svg',
        width: 'auto',
        height: 40,
        className: 'h-8 md:h-10'
      },
      tablet: {
        imgSrc: '/assets/images/logo.svg',
        width: 'auto',
        height: 40,
        className: 'h-8 md:h-10'
      },
      mobile: {
        imgSrcMobile: '/assets/images/logo.svg',
        width: 'auto',
        height: 32,
        className: 'h-8 md:h-10'
      }
    },
    expertAvatar: {
      desktop: {
        imgSrc:
          'https://images.unsplash.com/photo-1599566150163-29194dcaad36?q=80&w=1887&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
        width: 48,
        height: 48,
        border: {
          radius: 24
        },
        imageFit: 'fill',
        align: 'center',
        className: 'w-12 h-12 rounded-full object-cover'
      },
      tablet: {
        imgSrc:
          'https://images.unsplash.com/photo-1599566150163-29194dcaad36?q=80&w=1887&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
        width: 48,
        height: 48,
        border: {
          radius: 24
        },
        imageFit: 'fill',
        align: 'center',
        className: 'w-12 h-12 rounded-full object-cover'
      },
      mobile: {
        imgSrc:
          'https://images.unsplash.com/photo-1599566150163-29194dcaad36?q=80&w=1887&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
        width: 40,
        height: 40,
        border: {
          radius: 20
        },
        imageFit: 'fill',
        align: 'center',
        className: 'w-10 h-10 rounded-full object-cover'
      }
    },
    expertName: {
      desktop: {
        text: 'Nguyễn Văn An',
        fontsize: 14,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'left',
        className: 'font-bold text-lg text-primary-blue'
      },
      tablet: {
        text: 'Nguyễn Văn An',
        fontsize: 14,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'left',
        className: 'font-bold text-lg text-primary-blue'
      },
      mobile: {
        text: 'Nguyễn Văn An',
        fontSize: 16,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'left',
        className: 'font-bold text-base text-primary-blue'
      }
    },
    expertTitle: {
      desktop: {
        text: 'Chuyên gia Tổ Ấm Số',
        fontSize: 14,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'left',
        className: 'text-sm text-gray-500'
      },
      tablet: {
        text: 'Chuyên gia Tổ Ấm Số',
        fontSize: 14,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'left',
        className: 'text-sm text-gray-500'
      },
      mobile: {
        text: 'Chuyên gia Tổ Ấm Số',
        fontSize: 12,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'left',
        className: 'text-xs text-gray-500'
      }
    },
    contactSocialMedia: {
      desktop: {
        ...defaultResponseSocialMediaProps.desktop,
        size: 14,
        iconAlign: 'start',
        fontFamily: 'Inter, sans-serif',
        fontSize: 14,
        socialList: [
          {
            name: 'Số điện thoại',
            icon: 'PhoneFilled',
            iconColor: '#6b7280',
            url: '',
            displayType: 'NAME'
          },
          {
            name: 'Email',
            icon: 'MailFilled',
            iconColor: '#6b7280',
            url: '',
            displayType: 'NAME'
          }
        ]
      },
      tablet: {
        ...defaultResponseSocialMediaProps.tablet,
        size: 14,
        iconAlign: 'start',
        fontFamily: 'Inter, sans-serif',
        fontSize: 14,
        socialList: [
          {
            name: 'Số điện thoại',
            icon: 'PhoneFilled',
            iconColor: '#6b7280',
            url: '',
            displayType: 'NAME'
          },
          {
            name: 'Email',
            icon: 'MailFilled',
            iconColor: '#6b7280',
            url: '',
            displayType: 'NAME'
          }
        ]
      },
      mobile: {
        ...defaultResponseSocialMediaProps.mobile,
        size: 14,
        iconAlign: 'start',
        fontFamily: 'Inter, sans-serif',
        fontSize: 14,
        socialList: [
          {
            name: 'Số điện thoại',
            icon: 'PhoneFilled',
            iconColor: '#6b7280',
            url: '',
            displayType: 'ICON'
          },
          {
            name: 'Email',
            icon: 'MailFilled',
            iconColor: '#6b7280',
            url: '',
            displayType: 'ICON'
          }
        ]
      }
    },
    socialMediaLinks: {
      desktop: {
        ...defaultResponseSocialMediaProps.desktop,
        size: 14,
        iconAlign: 'start',
        socialList: [
          {
            name: 'Facebook',
            icon: 'FacebookIcon2',
            iconColor: '#6b7280',
            url: 'https://facebook.com',
            displayType: 'ICON'
          }
        ]
      },
      tablet: {
        ...defaultResponseSocialMediaProps.tablet,
        size: 14,
        iconAlign: 'start',
        socialList: [
          {
            name: 'Facebook',
            icon: 'FacebookIcon2',
            iconColor: '#6b7280',
            url: 'https://facebook.com',
            displayType: 'ICON'
          }
        ]
      },
      mobile: {
        ...defaultResponseSocialMediaProps.mobile,
        size: 16,
        iconAlign: 'start',
        socialList: [
          {
            name: 'Facebook',
            icon: 'FacebookIcon2',
            iconColor: '#6b7280',
            url: 'https://facebook.com',
            displayType: 'ICON'
          }
        ]
      }
    },
    zaloLink: {
      desktop: {
        icon: 'MessageFilled',
        iconColor: '#6b7280',
        title: '',
        fontsize: 14,
        fontWeight: 400,
        color: '#6b7280',
        href: '#',
        gap: 0,
        className: 'hover:text-green-500 text-lg',
        showIcon: { desktop: true, tablet: true, mobile: true },
        showTitle: { desktop: false, tablet: false, mobile: false }
      },
      tablet: {
        icon: 'MessageFilled',
        iconColor: '#6b7280',
        title: '',
        fontsize: 14,
        fontWeight: 400,
        color: '#6b7280',
        href: '#',
        gap: 0,
        className: 'hover:text-green-500 text-lg',
        showIcon: { desktop: true, tablet: true, mobile: true },
        showTitle: { desktop: false, tablet: false, mobile: false }
      },
      mobile: {
        icon: 'MessageFilled',
        iconColor: '#6b7280',
        title: '',
        fontSize: 16,
        fontWeight: 400,
        color: '#6b7280',
        href: '#',
        gap: 0,
        className: 'hover:text-green-500 text-lg',
        showIcon: { desktop: true, tablet: true, mobile: true },
        showTitle: { desktop: false, tablet: false, mobile: false }
      }
    }
  },
  rules: {
    canDrag: () => true,
    canMoveIn: () => false
  }
}

export const StyledUserProfile = styled('div')({
  p: {
    outline: 'none'
  }
})
