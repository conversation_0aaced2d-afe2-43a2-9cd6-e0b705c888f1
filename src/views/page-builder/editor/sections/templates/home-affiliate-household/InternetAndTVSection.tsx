'use client'

import React from 'react'

import type { UserComponent } from '@craftjs/core'
import { Element, useNode } from '@craftjs/core'

import { Container, Row, Column, Text, Button, Image } from '@/components/page-builder/selectors'
import { ProductCard } from '@/components/page-builder/editor/CustomComponents/IoTTemplate/ProductCard'

export const InternetAndTVSection: UserComponent = props => {
  const {
    connectors: { connect, drag }
  } = useNode()

  return (
    <div
      className='w-full'
      ref={(ref: HTMLDivElement | null) => {
        if (ref) {
          connect(drag(ref))
        }
      }}
    >
      {/* Internet & TV Section */}
      <Element id='container' canvas is={Container} {...props.container}>
        <Element is={Row} id='internet-tv-section' canvas {...props.internetTvSection}>
          <Element is={Column} id='internet-tv-heading' canvas {...props.sectionTitle}>
            <Element is={Text} id='internet-tv-heading' canvas {...props.internetTvHeading} />
          </Element>
          <Element id='container-internet-tv' canvas is={Container} {...props.container}>
            <Element is={Row} id='internet-tv-cards' canvas {...props.internetTvCards}>
              {/* Card 1: Home Mesh 2+*/}
              <Element is={Column} id='internet-tv-card-1' canvas {...props.internetCard}>
                <Element is={ProductCard} id='product1' canvas {...props.productCard}>
                  <div className='rounded-t-lg bg-[#f1f8e9]'>
                    <Element is={Image} id='internet-tv-card-1-image' canvas {...props.internetCard1Image} />
                  </div>
                  <div className='flex h-full flex-col justify-between p-6'>
                    <div className='flex flex-col'>
                      <Element is={Text} id='internet-tv-card-1-title' canvas {...props.internetCard1Title} />
                      <Element is={Text} id='internet-tv-card-1-features' canvas {...props.internetCard1Features} />
                    </div>
                    <div className='flex flex-col'>
                      <Element is={Text} id='internet-tv-card-1-price' canvas {...props.internetCard1Price} />
                      <Element is={Row} id='internet-tv-card-1-buttons' canvas {...props.internetCard1Buttons}>
                        <Element
                          is={Button}
                          id='internet-tv-card-1-detail-btn'
                          canvas
                          {...props.internetCard1DetailBtn}
                        />
                        <Element is={Button} id='internet-tv-card-1-buy-btn' canvas {...props.internetCard1BuyBtn} />
                      </Element>
                    </div>
                  </div>
                </Element>
              </Element>

              {/* Card 2: Home Net 1 */}
              <Element is={Column} id='internet-tv-card-2' canvas {...props.internetCard}>
                <Element is={ProductCard} id='product2' canvas {...props.productCard}>
                  <div className='rounded-t-lg bg-[#f1f8e9]'>
                    <Element is={Image} id='internet-tv-card-2-image' canvas {...props.internetCard2Image} />
                  </div>
                  <div className='flex h-full flex-col justify-between p-6'>
                    <div className='flex flex-col'>
                      <Element is={Text} id='internet-tv-card-2-title' canvas {...props.internetCard2Title} />
                      <Element is={Text} id='internet-tv-card-2-features' canvas {...props.internetCard2Features} />
                    </div>
                    <div className='flex flex-col'>
                      <Element is={Text} id='internet-tv-card-2-price' canvas {...props.internetCard2Price} />
                      <Element is={Row} id='internet-tv-card-2-buttons' canvas {...props.internetCard2Buttons}>
                        <Element
                          is={Button}
                          id='internet-tv-card-2-detail-btn'
                          canvas
                          {...props.internetCard2DetailBtn}
                        />
                        <Element is={Button} id='internet-tv-card-2-buy-btn' canvas {...props.internetCard2BuyBtn} />
                      </Element>
                    </div>
                  </div>
                </Element>
              </Element>

              {/* Card 3: Home TV 1 */}
              <Element is={Column} id='internet-tv-card-3' canvas {...props.internetCard}>
                <Element is={ProductCard} id='product3' canvas {...props.productCard}>
                  <div className='rounded-t-lg bg-[#f1f8e9]'>
                    <Element is={Image} id='internet-tv-card-3-image' canvas {...props.internetCard3Image} />
                  </div>
                  <div className='flex h-full flex-col justify-between p-6'>
                    <div className='flex flex-col'>
                      <Element is={Text} id='internet-tv-card-3-title' canvas {...props.internetCard3Title} />
                      <Element is={Text} id='internet-tv-card-3-features' canvas {...props.internetCard3Features} />
                    </div>
                    <div className='flex flex-col'>
                      <Element is={Text} id='internet-tv-card-3-price' canvas {...props.internetCard3Price} />
                      <Element is={Row} id='internet-tv-card-3-buttons' canvas {...props.internetCard3Buttons}>
                        <Element
                          is={Button}
                          id='internet-tv-card-3-detail-btn'
                          canvas
                          {...props.internetCard3DetailBtn}
                        />
                        <Element is={Button} id='internet-tv-card-3-buy-btn' canvas {...props.internetCard3BuyBtn} />
                      </Element>
                    </div>
                  </div>
                </Element>
              </Element>

              {/* Card 4: Home Combo */}
              <Element is={Column} id='internet-tv-card-4' canvas {...props.internetCard}>
                <Element is={ProductCard} id='product4' canvas {...props.productCard}>
                  <div className='rounded-t-lg bg-[#f1f8e9]'>
                    <Element is={Image} id='internet-tv-card-4-image' canvas {...props.internetCard4Image} />
                  </div>
                  <div className='flex h-full flex-col justify-between p-6'>
                    <div className='flex flex-col'>
                      <Element is={Text} id='internet-tv-card-4-title' canvas {...props.internetCard4Title} />
                      <Element is={Text} id='internet-tv-card-4-features' canvas {...props.internetCard4Features} />
                    </div>
                    <div className='flex flex-col'>
                      <Element is={Text} id='internet-tv-card-4-price' canvas {...props.internetCard4Price} />
                      <Element is={Row} id='internet-tv-card-4-buttons' canvas {...props.internetCard4Buttons}>
                        <Element
                          is={Button}
                          id='internet-tv-card-4-detail-btn'
                          canvas
                          {...props.internetCard4DetailBtn}
                        />
                        <Element
                          is={Button}
                          id='internet-tv-card-4-consult-btn'
                          canvas
                          {...props.internetCard4ConsultBtn}
                        />
                      </Element>
                    </div>
                  </div>
                </Element>
              </Element>
            </Element>
          </Element>
        </Element>
      </Element>
    </div>
  )
}

InternetAndTVSection.craft = {
  displayName: 'Internet & TV Section',
  props: {
    container: {
      height: 'auto',
      background: '#FFFFFF',
      padding: ['0', '0', '0', '0']
    },
    // Internet & TV Section
    internetTvSection: {
      desktop: {
        padding: { top: 0, bottom: 24, left: 0, right: 0 },
        colWidths: [12],
        gap: 6,
        height: 'auto',
        alignItems: 'center',
        contentAlign: 'start',
        justifyContent: 'center',
        width: '100%'
      },
      tablet: {
        padding: { top: 0, bottom: 24, left: 0, right: 0 },
        colWidths: [12],
        gap: 6,
        height: 'auto',
        alignItems: 'center',
        contentAlign: 'start',
        justifyContent: 'center',
        width: '100%'
      },
      mobile: {
        padding: { top: 0, bottom: 24, left: 0, right: 0 },
        colWidths: [12],
        gap: 6,
        height: 'auto',
        alignItems: 'center',
        contentAlign: 'start',
        justifyContent: 'center',
        width: '100%'
      }
    },
    internetTvHeading: {
      desktop: {
        text: '#Internet & Truyền hình',
        fontSize: 24,
        fontWeight: 700,
        color: '#374151',
        textAlign: 'left'
      },
      tablet: {
        text: '#Internet & Truyền hình',
        fontSize: 22,
        fontWeight: 700,
        color: '#374151',
        textAlign: 'left'
      },
      mobile: {
        text: '#Internet & Truyền hình',
        fontSize: 20,
        fontWeight: 700,
        color: '#374151',
        textAlign: 'left'
      }
    },
    sectionTitle: {
      desktop: {
        margin: { top: 0, bottom: 16, left: 0, right: 0 }
      },
      tablet: {
        margin: { top: 0, bottom: 16, left: 0, right: 0 }
      },
      mobile: {
        margin: { top: 0, bottom: 16, left: 0, right: 0 }
      }
    },
    internetTvCards: {
      desktop: {
        colWidths: [3, 3, 3, 3],
        padding: { top: 0, bottom: 24, left: 0, right: 0 },
        gap: 6,
        height: 'auto',
        alignItems: 'stretch',
        contentAlign: 'start',
        justifyContent: 'center',
        width: '100%'
      },
      tablet: {
        colWidths: [6, 6],
        gap: 6,
        height: 'auto',
        alignItems: 'stretch',
        contentAlign: 'start',
        justifyContent: 'center',
        width: '100%',
        isBreakLine: true
      },
      mobile: {
        colWidths: [12],
        gap: 6,
        height: 'auto',
        alignItems: 'stretch',
        contentAlign: 'start',
        justifyContent: 'center',
        width: '100%',
        isBreakLine: true
      }
    },
    // Internet Card 1: Home Mesh 2+
    internetCard: {
      desktop: {
        backgroundColor: '#ffffff',
        backgroundType: 'COLOR',
        padding: { top: 0, bottom: 0, left: 0, right: 0 },
        gap: 0,
        height: 'auto',
        alignItems: 'stretch',
        contentAlign: 'start',
        justifyContent: 'center',
        width: '100%',
        border: {
          radius: 8
        }
      },
      tablet: {
        backgroundColor: '#ffffff',
        backgroundType: 'COLOR',
        padding: { top: 0, bottom: 0, left: 0, right: 0 },
        gap: 0,
        height: 'auto',
        alignItems: 'stretch',
        contentAlign: 'start',
        justifyContent: 'center',
        width: '100%',
        border: {
          radius: 8
        }
      },
      mobile: {
        backgroundColor: '#ffffff',
        backgroundType: 'COLOR',
        padding: { top: 0, bottom: 0, left: 0, right: 0 },
        margin: { top: 0, bottom: 16, left: 0, right: 0 },
        gap: 0,
        height: 'auto',
        alignItems: 'stretch',
        contentAlign: 'start',
        justifyContent: 'center',
        width: '100%',
        border: {
          radius: 8
        }
      }
    },
    productCard: {
      desktop: {
        backgroundColor: '#ffffff',
        borderRadius: 8,
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
        hoverTransform: 'translateY(-5px)',
        hoverBoxShadow: '0 10px 25px rgba(0,0,0,0.1)',
        transition: 'transform 0.3s ease, box-shadow 0.3s ease',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        minHeight: 400
      },
      tablet: {
        backgroundColor: '#ffffff',
        borderRadius: 8,
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
        hoverTransform: 'translateY(-5px)',
        hoverBoxShadow: '0 10px 25px rgba(0,0,0,0.1)',
        transition: 'transform 0.3s ease, box-shadow 0.3s ease',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        minHeight: 400
      },
      mobile: {
        backgroundColor: '#ffffff',
        borderRadius: 8,
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
        hoverTransform: 'translateY(-5px)',
        hoverBoxShadow: '0 10px 25px rgba(0,0,0,0.1)',
        transition: 'transform 0.3s ease, box-shadow 0.3s ease',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        minHeight: 400
      }
    },
    internetCard1Image: {
      desktop: {
        imgSrc: 'https://placehold.co/600x400/FFF3E0/E65100?text=Home+Mesh+2',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      },
      tablet: {
        imgSrc: 'https://placehold.co/600x400/FFF3E0/E65100?text=Home+Mesh+2',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      },
      mobile: {
        imgSrcMobile: 'https://placehold.co/600x400/FFF3E0/E65100?text=Home+Mesh+2',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      }
    },
    internetCard1Title: {
      desktop: {
        text: 'Home Mesh 2+',
        fontSize: 18,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'left'
      },
      tablet: {
        text: 'Home Mesh 2+',
        fontSize: 18,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'left'
      },
      mobile: {
        text: 'Home Mesh 2+',
        fontSize: 18,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'left'
      }
    },
    internetCard1Features: {
      desktop: {
        text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Tốc độ Internet 150Mbps<br>✓ Truyền hình MyTV Nâng cao<br>✓ Trang bị 01 Wifi Mesh</div>',
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'left'
      },
      tablet: {
        text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Tốc độ Internet 150Mbps<br>✓ Truyền hình MyTV Nâng cao<br>✓ Trang bị 01 Wifi Mesh</div>',
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'left'
      },
      mobile: {
        text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Tốc độ Internet 150Mbps<br>✓ Truyền hình MyTV Nâng cao<br>✓ Trang bị 01 Wifi Mesh</div>',
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'left'
      }
    },
    internetCard1Price: {
      desktop: {
        text: '245.000đ<span style="font-size: 16px; font-weight: 400;">/tháng</span>',
        fontSize: 24,
        fontWeight: 700,
        color: '#f7941e',
        textAlign: 'left',
        margin: { top: 'auto', bottom: 0, left: 0, right: 0 }
      },
      tablet: {
        text: '245.000đ<span style="font-size: 16px; font-weight: 400;">/tháng</span>',
        fontSize: 24,
        fontWeight: 700,
        color: '#f7941e',
        textAlign: 'left',
        margin: { top: 'auto', bottom: 0, left: 0, right: 0 }
      },
      mobile: {
        text: '245.000đ<span style="font-size: 16px; font-weight: 400;">/tháng</span>',
        fontSize: 24,
        fontWeight: 700,
        color: '#f7941e',
        textAlign: 'left',
        margin: { top: 'auto', bottom: 0, left: 0, right: 0 }
      }
    },
    internetCard1Buttons: {
      desktop: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      },
      tablet: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      },
      mobile: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      }
    },
    internetCard1DetailBtn: {
      desktop: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      tablet: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      mobile: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      }
    },
    internetCard1BuyBtn: {
      desktop: {
        text: 'Mua ngay',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      tablet: {
        text: 'Mua ngay',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      mobile: {
        text: 'Mua ngay',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      }
    },
    // Internet Card 2: Home Net 1
    internetCard2Image: {
      desktop: {
        imgSrc: 'https://placehold.co/600x400/FFF3E0/E65100?text=Home+Net',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      },
      tablet: {
        imgSrc: 'https://placehold.co/600x400/FFF3E0/E65100?text=Home+Net',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      },
      mobile: {
        imgSrcMobile: 'https://placehold.co/600x400/FFF3E0/E65100?text=Home+Net',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      }
    },
    internetCard2Title: {
      desktop: { text: 'Home Net 1', fontSize: 18, fontWeight: 700, color: '#00529c', textAlign: 'left' },
      tablet: { text: 'Home Net 1', fontSize: 18, fontWeight: 700, color: '#00529c', textAlign: 'left' },
      mobile: { text: 'Home Net 1', fontSize: 18, fontWeight: 700, color: '#00529c', textAlign: 'left' }
    },
    internetCard2Features: {
      desktop: {
        text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Tốc độ Internet 100Mbps<br>✓ Phù hợp cho cá nhân, gia đình nhỏ</div>',
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'left'
      },
      tablet: {
        text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Tốc độ Internet 100Mbps<br>✓ Phù hợp cho cá nhân, gia đình nhỏ</div>',
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'left'
      },
      mobile: {
        text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Tốc độ Internet 100Mbps<br>✓ Phù hợp cho cá nhân, gia đình nhỏ</div>',
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'left'
      }
    },
    internetCard2Price: {
      desktop: {
        text: '165.000đ<span style="font-size: 16px; font-weight: 400;">/tháng</span>',
        fontSize: 24,
        fontWeight: 700,
        color: '#f7941e',
        textAlign: 'left',
        margin: { top: 'auto', bottom: 0, left: 0, right: 0 }
      },
      tablet: {
        text: '165.000đ<span style="font-size: 16px; font-weight: 400;">/tháng</span>',
        fontSize: 24,
        fontWeight: 700,
        color: '#f7941e',
        textAlign: 'left',
        margin: { top: 'auto', bottom: 0, left: 0, right: 0 }
      },
      mobile: {
        text: '165.000đ<span style="font-size: 16px; font-weight: 400;">/tháng</span>',
        fontSize: 24,
        fontWeight: 700,
        color: '#f7941e',
        textAlign: 'left',
        margin: { top: 'auto', bottom: 0, left: 0, right: 0 }
      }
    },
    internetCard2Buttons: {
      desktop: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      },
      tablet: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      },
      mobile: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      }
    },
    internetCard2DetailBtn: {
      desktop: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      tablet: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      mobile: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      }
    },
    internetCard2BuyBtn: {
      desktop: {
        text: 'Mua ngay',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      tablet: {
        text: 'Mua ngay',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      mobile: {
        text: 'Mua ngay',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      }
    },
    // Internet Card 3: Home TV 1
    internetCard3Image: {
      desktop: {
        imgSrc: 'https://placehold.co/600x400/FFF3E0/E65100?text=Home+TV',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      },
      tablet: {
        imgSrc: 'https://placehold.co/600x400/FFF3E0/E65100?text=Home+TV',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      },
      mobile: {
        imgSrcMobile: 'https://placehold.co/600x400/FFF3E0/E65100?text=Home+TV',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      }
    },
    internetCard3Title: {
      desktop: { text: 'Home TV 1', fontSize: 18, fontWeight: 700, color: '#00529c', textAlign: 'left' },
      tablet: { text: 'Home TV 1', fontSize: 18, fontWeight: 700, color: '#00529c', textAlign: 'left' },
      mobile: { text: 'Home TV 1', fontSize: 18, fontWeight: 700, color: '#00529c', textAlign: 'left' }
    },
    internetCard3Features: {
      desktop: {
        text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Tốc độ Internet 80Mbps<br>✓ Truyền hình MyTV Chuẩn<br>✓ Gói cước tiết kiệm</div>',
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'left'
      },
      tablet: {
        text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Tốc độ Internet 80Mbps<br>✓ Truyền hình MyTV Chuẩn<br>✓ Gói cước tiết kiệm</div>',
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'left'
      },
      mobile: {
        text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Tốc độ Internet 80Mbps<br>✓ Truyền hình MyTV Chuẩn<br>✓ Gói cước tiết kiệm</div>',
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'left'
      }
    },
    internetCard3Price: {
      desktop: {
        text: '175.000đ<span style="font-size: 16px; font-weight: 400;">/tháng</span>',
        fontSize: 24,
        fontWeight: 700,
        color: '#f7941e',
        textAlign: 'left',
        margin: { top: 'auto', bottom: 0, left: 0, right: 0 }
      },
      tablet: {
        text: '175.000đ<span style="font-size: 16px; font-weight: 400;">/tháng</span>',
        fontSize: 24,
        fontWeight: 700,
        color: '#f7941e',
        textAlign: 'left',
        margin: { top: 'auto', bottom: 0, left: 0, right: 0 }
      },
      mobile: {
        text: '175.000đ<span style="font-size: 16px; font-weight: 400;">/tháng</span>',
        fontSize: 24,
        fontWeight: 700,
        color: '#f7941e',
        textAlign: 'left',
        margin: { top: 'auto', bottom: 0, left: 0, right: 0 }
      }
    },
    internetCard3Buttons: {
      desktop: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      },
      tablet: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      },
      mobile: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      }
    },
    internetCard3DetailBtn: {
      desktop: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      tablet: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      mobile: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      }
    },
    internetCard3BuyBtn: {
      desktop: {
        text: 'Mua ngay',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      tablet: {
        text: 'Mua ngay',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      mobile: {
        text: 'Mua ngay',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      }
    },
    // Internet Card 4: Home Combo
    internetCard4Image: {
      desktop: {
        imgSrc: 'https://placehold.co/600x400/FFF3E0/E65100?text=Home+Combo',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      },
      tablet: {
        imgSrc: 'https://placehold.co/600x400/FFF3E0/E65100?text=Home+Combo',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      },
      mobile: {
        imgSrcMobile: 'https://placehold.co/600x400/FFF3E0/E65100?text=Home+Combo',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      }
    },
    internetCard4Title: {
      desktop: { text: 'Home Combo', fontSize: 18, fontWeight: 700, color: '#00529c', textAlign: 'left' },
      tablet: { text: 'Home Combo', fontSize: 18, fontWeight: 700, color: '#00529c', textAlign: 'left' },
      mobile: { text: 'Home Combo', fontSize: 18, fontWeight: 700, color: '#00529c', textAlign: 'left' }
    },
    internetCard4Features: {
      desktop: {
        text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Internet + TV + Di động<br>✓ Tiết kiệm đến 50%<br>✓ Nhiều lựa chọn gói</div>',
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'left'
      },
      tablet: {
        text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Internet + TV + Di động<br>✓ Tiết kiệm đến 50%<br>✓ Nhiều lựa chọn gói</div>',
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'left'
      },
      mobile: {
        text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Internet + TV + Di động<br>✓ Tiết kiệm đến 50%<br>✓ Nhiều lựa chọn gói</div>',
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'left'
      }
    },
    internetCard4Price: {
      desktop: {
        text: 'Từ 239.000đ',
        fontSize: 20,
        fontWeight: 700,
        color: '#f7941e',
        textAlign: 'left',
        margin: { top: 'auto', bottom: 0, left: 0, right: 0 }
      },
      tablet: {
        text: 'Từ 239.000đ',
        fontSize: 20,
        fontWeight: 700,
        color: '#f7941e',
        textAlign: 'left',
        margin: { top: 'auto', bottom: 0, left: 0, right: 0 }
      },
      mobile: {
        text: 'Từ 239.000đ',
        fontSize: 20,
        fontWeight: 700,
        color: '#f7941e',
        textAlign: 'left',
        margin: { top: 'auto', bottom: 0, left: 0, right: 0 }
      }
    },
    internetCard4Buttons: {
      desktop: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      },
      tablet: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      },
      mobile: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      }
    },
    internetCard4DetailBtn: {
      desktop: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      tablet: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      mobile: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      }
    },
    internetCard4ConsultBtn: {
      desktop: {
        text: 'Tư vấn',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      tablet: {
        text: 'Tư vấn',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      mobile: {
        text: 'Tư vấn',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      }
    }
  }
}
