'use client'

import { Element, useNode, type UserComponent } from '@craftjs/core'

import { Text, Button } from '@/components/page-builder/selectors'

export const FlashSaleHouseholdBuilder: UserComponent = props => {
  const {
    connectors: { connect, drag }
  } = useNode()

  return (
    <div
      className='w-full'
      ref={(ref: HTMLDivElement | null) => {
        if (ref) {
          connect(drag(ref))
        }
      }}
    >
      <section className='bg-white py-12'>
        <div className='container mx-auto px-4'>
          <div className='flex flex-col items-center justify-between gap-4 rounded-lg border-l-4 border-red-500 bg-red-50 p-4 shadow-lg sm:flex-row'>
            <div className='flex items-center gap-4'>
              <div className='text-4xl animate-pulse text-red-500'>
                <i className='fas fa-bolt' style={{ fontSize: '2.5rem' }}></i>
              </div>
              <div>
                <Element is={Text} id='flash-sale-title' canvas {...props.flashSaleTitle} />
                <div className='flex items-center justify-center gap-1 text-gray-700'>
                  <div className='shrink-0'>
                    <Element
                      is={Text}
                      id='flash-sale-description-prefix'
                      canvas
                      {...props.flashSaleDescriptionPrefix}
                    />
                  </div>
                  <div className='shrink-0'>
                    <Element is={Text} id='flash-sale-description-price' canvas {...props.flashSaleDescriptionPrice} />
                  </div>
                </div>
              </div>
            </div>

            <div id='countdown' className='flex flex-wrap justify-center gap-2 text-center'>
              <div className='min-w-[50px] rounded-md bg-gray-800 px-2 py-1 text-white'>
                <span id='days' className='text-lg font-bold sm:text-xl'>
                  00
                </span>
                <span className='block text-xs'>Ngày</span>
              </div>
              <div className='min-w-[50px] rounded-md bg-gray-800 px-2 py-1 text-white'>
                <span id='hours' className='text-lg font-bold sm:text-xl'>
                  00
                </span>
                <span className='block text-xs'>Giờ</span>
              </div>
              <div className='min-w-[50px] rounded-md bg-gray-800 px-2 py-1 text-white'>
                <span id='minutes' className='text-lg font-bold sm:text-xl'>
                  00
                </span>
                <span className='block text-xs'>Phút</span>
              </div>
              <div className='min-w-[50px] rounded-md bg-gray-800 px-2 py-1 text-white'>
                <span id='seconds' className='text-lg font-bold sm:text-xl'>
                  00
                </span>
                <span className='block text-xs'>Giây</span>
              </div>
            </div>

            <div>
              <Element is={Button} id='flash-sale-button' canvas {...props.flashSaleButton} />
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

FlashSaleHouseholdBuilder.craft = {
  displayName: 'FlashSaleHouseholdBuilder',
  props: {
    flashSaleTitle: {
      desktop: {
        text: 'FLASH SALE',
        fontSize: 18,
        fontWeight: 700,
        color: '#dc2626',
        textAlign: 'left',
        className: 'font-bold text-red-600 text-lg'
      },
      tablet: {
        text: 'FLASH SALE',
        fontSize: 18,
        fontWeight: 700,
        color: '#dc2626',
        textAlign: 'left',
        className: 'font-bold text-red-600 text-lg'
      },
      mobile: {
        text: 'FLASH SALE',
        fontSize: 16,
        fontWeight: 700,
        color: '#dc2626',
        textAlign: 'left',
        className: 'font-bold text-red-600 text-lg'
      }
    },
    flashSaleDescriptionPrefix: {
      desktop: {
        text: 'Camera an ninh chỉ từ ',
        fontSize: 14,
        fontWeight: 400,
        color: '#374151',
        textAlign: 'left',
        className: 'inline'
      },
      tablet: {
        text: 'Camera an ninh chỉ từ ',
        fontSize: 14,
        fontWeight: 400,
        color: '#374151',
        textAlign: 'left',
        className: 'inline'
      },
      mobile: {
        text: 'Camera an ninh chỉ từ ',
        fontSize: 12,
        fontWeight: 400,
        color: '#374151',
        textAlign: 'left',
        className: 'inline'
      }
    },
    flashSaleDescriptionPrice: {
      desktop: {
        text: '499.000đ',
        fontSize: 14,
        fontWeight: 700,
        color: '#dc2626',
        textAlign: 'left',
        className: 'inline text-red-600 font-bold'
      },
      tablet: {
        text: '499.000đ',
        fontSize: 14,
        fontWeight: 700,
        color: '#dc2626',
        textAlign: 'left',
        className: 'inline text-red-600 font-bold'
      },
      mobile: {
        text: '499.000đ',
        fontSize: 12,
        fontWeight: 700,
        color: '#dc2626',
        textAlign: 'left',
        className: 'inline text-red-600 font-bold'
      }
    },
    flashSaleButton: {
      desktop: {
        text: 'Săn Sale Ngay',
        fontSize: 14,
        fontWeight: 700,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#dc2626',
        radius: 8,
        size: 'large',
        width: 'auto',
        padding: { top: 8, right: 24, bottom: 8, left: 24 },
        margin: { top: 0, right: 0, bottom: 0, left: 0 }
      },
      tablet: {
        text: 'Săn Sale Ngay',
        fontSize: 14,
        fontWeight: 700,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#dc2626',
        radius: 8,
        size: 'large',
        width: 'auto',
        padding: { top: 8, right: 24, bottom: 8, left: 24 },
        margin: { top: 0, right: 0, bottom: 0, left: 0 }
      },
      mobile: {
        text: 'Săn Sale Ngay',
        fontSize: 12,
        fontWeight: 700,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#dc2626',
        radius: 8,
        size: 'large',
        width: '100%',
        padding: { top: 8, right: 24, bottom: 8, left: 24 },
        margin: { top: 0, right: 0, bottom: 0, left: 0 }
      }
    }
  },
  rules: {
    canDrag: () => true,
    canMoveIn: () => false
  }
}
