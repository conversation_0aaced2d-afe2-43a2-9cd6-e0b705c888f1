'use client'

import React from 'react'

import type { UserComponent } from '@craftjs/core'
import { Element, useNode } from '@craftjs/core'

import { Container, Row, Column, Text, Button, Image } from '@/components/page-builder/selectors'
import { ProductCard } from '@/components/page-builder/editor/CustomComponents/IoTTemplate/ProductCard'

export const FamilyServiceSection: UserComponent = props => {
  const {
    connectors: { connect, drag }
  } = useNode()

  return (
    <div
      className='w-full'
      ref={(ref: HTMLDivElement | null) => {
        if (ref) {
          connect(drag(ref))
        }
      }}
    >
      {/* Family Service Section */}
      <Element id='container' canvas is={Container} {...props.container}>
        <Element is={Row} id='family-service-section' canvas {...props.familyServiceSection}>
          <Element is={Column} id='family-service-heading' canvas {...props.sectionTitle}>
            <Element is={Text} id='family-service-heading' canvas {...props.familyServiceHeading} />
          </Element>
          <Element id='container-family-service' canvas is={Container} {...props.container}>
            <Element is={Row} id='family-service-cards' canvas {...props.familyServiceCards}>
              {/* Card 1: Dịch vụ Sửa chữa */}
              <Element is={Column} id='family-service-card-1' canvas {...props.familyServiceCard}>
                <Element is={ProductCard} id='product1' canvas {...props.productCard}>
                  <div className='rounded-t-lg bg-[#f3e5f5]'>
                    <Element is={Image} id='family-service-card-1-image' canvas {...props.familyServiceCard1Image} />
                  </div>
                  <div className='flex h-full flex-col justify-between p-6'>
                    <div className='flex flex-col'>
                      <Element is={Text} id='family-service-card-1-title' canvas {...props.familyServiceCard1Title} />
                      <Element
                        is={Text}
                        id='family-service-card-1-features'
                        canvas
                        {...props.familyServiceCard1Features}
                      />
                    </div>
                    <div className='flex flex-col'>
                      <Element is={Text} id='family-service-card-1-price' canvas {...props.familyServiceCard1Price} />
                      <Element is={Row} id='family-service-card-1-buttons' canvas {...props.familyServiceCard1Buttons}>
                        <Element
                          is={Button}
                          id='family-service-card-1-detail-btn'
                          canvas
                          {...props.familyServiceCard1DetailBtn}
                        />
                        <Element
                          is={Button}
                          id='family-service-card-1-request-btn'
                          canvas
                          {...props.familyServiceCard1RequestBtn}
                        />
                      </Element>
                    </div>
                  </div>
                </Element>
              </Element>

              {/* Card 2: Bảo hành Mở rộng */}
              <Element is={Column} id='family-service-card-2' canvas {...props.familyServiceCard}>
                <Element is={ProductCard} id='product2' canvas {...props.productCard}>
                  <div className='rounded-t-lg bg-[#f3e5f5]'>
                    <Element is={Image} id='family-service-card-2-image' canvas {...props.familyServiceCard2Image} />
                  </div>
                  <div className='flex h-full flex-col justify-between p-6'>
                    <div className='flex flex-col'>
                      <Element is={Text} id='family-service-card-2-title' canvas {...props.familyServiceCard2Title} />
                      <Element
                        is={Text}
                        id='family-service-card-2-features'
                        canvas
                        {...props.familyServiceCard2Features}
                      />
                    </div>
                    <div className='flex flex-col'>
                      <Element is={Text} id='family-service-card-2-price' canvas {...props.familyServiceCard2Price} />
                      <Element is={Row} id='family-service-card-2-buttons' canvas {...props.familyServiceCard2Buttons}>
                        <Element
                          is={Button}
                          id='family-service-card-2-detail-btn'
                          canvas
                          {...props.familyServiceCard2DetailBtn}
                        />
                        <Element
                          is={Button}
                          id='family-service-card-2-consult-btn'
                          canvas
                          {...props.familyServiceCard2ConsultBtn}
                        />
                      </Element>
                    </div>
                  </div>
                </Element>
              </Element>

              {/* Card 3: Bảo hiểm Thiết bị */}
              <Element is={Column} id='family-service-card-3' canvas {...props.familyServiceCard}>
                <Element is={ProductCard} id='product3' canvas {...props.productCard}>
                  <div className='rounded-t-lg bg-[#f3e5f5]'>
                    <Element is={Image} id='family-service-card-3-image' canvas {...props.familyServiceCard3Image} />
                  </div>
                  <div className='flex h-full flex-col justify-between p-6'>
                    <div className='flex flex-col'>
                      <Element is={Text} id='family-service-card-3-title' canvas {...props.familyServiceCard3Title} />
                      <Element
                        is={Text}
                        id='family-service-card-3-features'
                        canvas
                        {...props.familyServiceCard3Features}
                      />
                    </div>
                    <div className='flex flex-col'>
                      <Element is={Text} id='family-service-card-3-price' canvas {...props.familyServiceCard3Price} />
                      <Element is={Row} id='family-service-card-3-buttons' canvas {...props.familyServiceCard3Buttons}>
                        <Element
                          is={Button}
                          id='family-service-card-3-detail-btn'
                          canvas
                          {...props.familyServiceCard3DetailBtn}
                        />
                        <Element
                          is={Button}
                          id='family-service-card-3-consult-btn'
                          canvas
                          {...props.familyServiceCard3ConsultBtn}
                        />
                      </Element>
                    </div>
                  </div>
                </Element>
              </Element>
            </Element>
          </Element>
        </Element>
      </Element>
    </div>
  )
}

FamilyServiceSection.craft = {
  displayName: 'Family Service Section',
  props: {
    container: {
      height: 'auto',
      background: '#FFFFFF',
      padding: ['0', '0', '0', '0']
    },
    // Family Service Section
    familyServiceSection: {
      desktop: {
        padding: { top: 0, bottom: 24, left: 0, right: 0 },
        colWidths: [12],
        gap: 6,
        height: 'auto',
        alignItems: 'center',
        justifyContent: 'center',
        width: '100%'
      },
      tablet: {
        padding: { top: 0, bottom: 24, left: 0, right: 0 },
        colWidths: [12],
        gap: 6,
        height: 'auto',
        alignItems: 'center',
        justifyContent: 'center',
        width: '100%'
      },
      mobile: {
        padding: { top: 0, bottom: 24, left: 0, right: 0 },
        colWidths: [12],
        gap: 6,
        height: 'auto',
        alignItems: 'center',
        justifyContent: 'center',
        width: '100%'
      }
    },
    sectionTitle: {
      desktop: {
        margin: { top: 0, bottom: 16, left: 0, right: 0 }
      },
      tablet: {
        margin: { top: 0, bottom: 16, left: 0, right: 0 }
      },
      mobile: {
        margin: { top: 0, bottom: 16, left: 0, right: 0 }
      }
    },
    familyServiceHeading: {
      desktop: {
        text: '#Dịch vụ Gia đình',
        fontSize: 24,
        fontWeight: 700,
        color: '#374151',
        textAlign: 'left'
      },
      tablet: {
        text: '#Dịch vụ Gia đình',
        fontSize: 22,
        fontWeight: 700,
        color: '#374151',
        textAlign: 'left'
      },
      mobile: {
        text: '#Dịch vụ Gia đình',
        fontSize: 20,
        fontWeight: 700,
        color: '#374151',
        textAlign: 'left'
      }
    },
    familyServiceCards: {
      desktop: {
        colWidths: [4, 4, 4],
        padding: { top: 0, bottom: 24, left: 0, right: 0 },
        gap: 6,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'center',
        width: '100%'
      },
      tablet: {
        colWidths: [6, 6],
        gap: 6,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'center',
        width: '100%',
        isBreakLine: true
      },
      mobile: {
        colWidths: [12],
        gap: 6,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'center',
        width: '100%',
        isBreakLine: true
      }
    },
    // Family Service Card 1: Dịch vụ Sửa chữa
    familyServiceCard: {
      desktop: {
        backgroundColor: '#ffffff',
        backgroundType: 'COLOR',
        padding: { top: 0, bottom: 0, left: 0, right: 0 },
        gap: 0,
        height: 'auto',
        alignItems: 'stretch',
        contentAlign: 'start',
        justifyContent: 'center',
        width: '100%',
        border: {
          radius: 8
        }
      },
      tablet: {
        backgroundColor: '#ffffff',
        backgroundType: 'COLOR',
        padding: { top: 0, bottom: 0, left: 0, right: 0 },
        gap: 0,
        height: 'auto',
        alignItems: 'stretch',
        contentAlign: 'start',
        justifyContent: 'center',
        width: '100%',
        border: {
          radius: 8
        }
      },
      mobile: {
        backgroundColor: '#ffffff',
        backgroundType: 'COLOR',
        padding: { top: 0, bottom: 0, left: 0, right: 0 },
        margin: { top: 0, bottom: 16, left: 0, right: 0 },
        gap: 0,
        height: 'auto',
        alignItems: 'stretch',
        contentAlign: 'start',
        justifyContent: 'center',
        width: '100%',
        border: {
          radius: 8
        }
      }
    },
    productCard: {
      desktop: {
        backgroundColor: '#ffffff',
        borderRadius: 8,
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
        hoverTransform: 'translateY(-5px)',
        hoverBoxShadow: '0 10px 25px rgba(0,0,0,0.1)',
        transition: 'transform 0.3s ease, box-shadow 0.3s ease',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        minHeight: 400
      },
      tablet: {
        backgroundColor: '#ffffff',
        borderRadius: 8,
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
        hoverTransform: 'translateY(-5px)',
        hoverBoxShadow: '0 10px 25px rgba(0,0,0,0.1)',
        transition: 'transform 0.3s ease, box-shadow 0.3s ease',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        minHeight: 400
      },
      mobile: {
        backgroundColor: '#ffffff',
        borderRadius: 8,
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
        hoverTransform: 'translateY(-5px)',
        hoverBoxShadow: '0 10px 25px rgba(0,0,0,0.1)',
        transition: 'transform 0.3s ease, box-shadow 0.3s ease',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        minHeight: 400
      }
    },
    familyServiceCard1Image: {
      desktop: {
        imgSrc: 'https://placehold.co/600x400/F3E5F5/4A148C?text=Sửa+chữa',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      },
      tablet: {
        imgSrc: 'https://placehold.co/600x400/F3E5F5/4A148C?text=Sửa+chữa',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      },
      mobile: {
        imgSrcMobile: 'https://placehold.co/600x400/F3E5F5/4A148C?text=Sửa+chữa',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      }
    },
    familyServiceCard1Title: {
      desktop: {
        text: 'Dịch vụ Sửa chữa',
        fontSize: 18,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'left'
      },
      tablet: {
        text: 'Dịch vụ Sửa chữa',
        fontSize: 18,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'left'
      },
      mobile: {
        text: 'Dịch vụ Sửa chữa',
        fontSize: 18,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'left'
      }
    },
    familyServiceCard1Features: {
      desktop: {
        text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Sửa chữa, bảo trì tại nhà<br>✓ Áp dụng cho TV, máy lạnh, tủ lạnh...<br>✓ Đội ngũ chuyên nghiệp, tin cậy</div>',
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'left'
      },
      tablet: {
        text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Sửa chữa, bảo trì tại nhà<br>✓ Áp dụng cho TV, máy lạnh, tủ lạnh...<br>✓ Đội ngũ chuyên nghiệp, tin cậy</div>',
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'left'
      },
      mobile: {
        text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Sửa chữa, bảo trì tại nhà<br>✓ Áp dụng cho TV, máy lạnh, tủ lạnh...<br>✓ Đội ngũ chuyên nghiệp, tin cậy</div>',
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'left'
      }
    },
    familyServiceCard1Price: {
      desktop: {
        text: 'Liên hệ',
        fontSize: 20,
        fontWeight: 700,
        color: '#f7941e',
        textAlign: 'left',
        margin: { top: 'auto', bottom: 0, left: 0, right: 0 }
      },
      tablet: {
        text: 'Liên hệ',
        fontSize: 20,
        fontWeight: 700,
        color: '#f7941e',
        textAlign: 'left',
        margin: { top: 'auto', bottom: 0, left: 0, right: 0 }
      },
      mobile: {
        text: 'Liên hệ',
        fontSize: 20,
        fontWeight: 700,
        color: '#f7941e',
        textAlign: 'left',
        margin: { top: 'auto', bottom: 0, left: 0, right: 0 }
      }
    },
    familyServiceCard1Buttons: {
      desktop: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      },
      tablet: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      },
      mobile: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      }
    },
    familyServiceCard1DetailBtn: {
      desktop: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      tablet: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      mobile: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      }
    },
    familyServiceCard1RequestBtn: {
      desktop: {
        text: 'Yêu cầu',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      tablet: {
        text: 'Yêu cầu',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      mobile: {
        text: 'Yêu cầu',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      }
    },
    // Family Service Card 2: Bảo hành Mở rộng
    familyServiceCard2Image: {
      desktop: {
        imgSrc: 'https://placehold.co/600x400/F3E5F5/4A148C?text=Bảo+hành',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      },
      tablet: {
        imgSrc: 'https://placehold.co/600x400/F3E5F5/4A148C?text=Bảo+hành',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      },
      mobile: {
        imgSrcMobile: 'https://placehold.co/600x400/F3E5F5/4A148C?text=Bảo+hành',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      }
    },
    familyServiceCard2Title: {
      desktop: { text: 'Bảo hành Mở rộng', fontSize: 18, fontWeight: 700, color: '#00529c', textAlign: 'left' },
      tablet: { text: 'Bảo hành Mở rộng', fontSize: 18, fontWeight: 700, color: '#00529c', textAlign: 'left' },
      mobile: { text: 'Bảo hành Mở rộng', fontSize: 18, fontWeight: 700, color: '#00529c', textAlign: 'left' }
    },
    familyServiceCard2Features: {
      desktop: {
        text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Kéo dài thời gian bảo hành<br>✓ An tâm sử dụng thiết bị<br>✓ Áp dụng cho nhiều sản phẩm</div>',
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'left'
      },
      tablet: {
        text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Kéo dài thời gian bảo hành<br>✓ An tâm sử dụng thiết bị<br>✓ Áp dụng cho nhiều sản phẩm</div>',
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'left'
      },
      mobile: {
        text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Kéo dài thời gian bảo hành<br>✓ An tâm sử dụng thiết bị<br>✓ Áp dụng cho nhiều sản phẩm</div>',
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'left'
      }
    },
    familyServiceCard2Price: {
      desktop: {
        text: 'Liên hệ',
        fontSize: 20,
        fontWeight: 700,
        color: '#f7941e',
        textAlign: 'left',
        margin: { top: 'auto', bottom: 0, left: 0, right: 0 }
      },
      tablet: {
        text: 'Liên hệ',
        fontSize: 20,
        fontWeight: 700,
        color: '#f7941e',
        textAlign: 'left',
        margin: { top: 'auto', bottom: 0, left: 0, right: 0 }
      },
      mobile: {
        text: 'Liên hệ',
        fontSize: 20,
        fontWeight: 700,
        color: '#f7941e',
        textAlign: 'left',
        margin: { top: 'auto', bottom: 0, left: 0, right: 0 }
      }
    },
    familyServiceCard2Buttons: {
      desktop: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      },
      tablet: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      },
      mobile: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      }
    },
    familyServiceCard2DetailBtn: {
      desktop: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      tablet: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      mobile: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      }
    },
    familyServiceCard2ConsultBtn: {
      desktop: {
        text: 'Tư vấn',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      tablet: {
        text: 'Tư vấn',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      mobile: {
        text: 'Tư vấn',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      }
    },
    // Family Service Card 3: Bảo hiểm Thiết bị
    familyServiceCard3Image: {
      desktop: {
        imgSrc: 'https://placehold.co/600x400/F3E5F5/4A148C?text=Bảo+hiểm',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      },
      tablet: {
        imgSrc: 'https://placehold.co/600x400/F3E5F5/4A148C?text=Bảo+hiểm',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      },
      mobile: {
        imgSrcMobile: 'https://placehold.co/600x400/F3E5F5/4A148C?text=Bảo+hiểm',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      }
    },
    familyServiceCard3Title: {
      desktop: { text: 'Bảo hiểm Thiết bị', fontSize: 18, fontWeight: 700, color: '#00529c', textAlign: 'left' },
      tablet: { text: 'Bảo hiểm Thiết bị', fontSize: 18, fontWeight: 700, color: '#00529c', textAlign: 'left' },
      mobile: { text: 'Bảo hiểm Thiết bị', fontSize: 18, fontWeight: 700, color: '#00529c', textAlign: 'left' }
    },
    familyServiceCard3Features: {
      desktop: {
        text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Bảo vệ thiết bị khỏi rơi vỡ, vào nước<br>✓ Thủ tục đơn giản, nhanh chóng</div>',
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'left'
      },
      tablet: {
        text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Bảo vệ thiết bị khỏi rơi vỡ, vào nước<br>✓ Thủ tục đơn giản, nhanh chóng</div>',
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'left'
      },
      mobile: {
        text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Bảo vệ thiết bị khỏi rơi vỡ, vào nước<br>✓ Thủ tục đơn giản, nhanh chóng</div>',
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'left'
      }
    },
    familyServiceCard3Price: {
      desktop: {
        text: 'Liên hệ',
        fontSize: 20,
        fontWeight: 700,
        color: '#f7941e',
        textAlign: 'left',
        margin: { top: 'auto', bottom: 0, left: 0, right: 0 }
      },
      tablet: {
        text: 'Liên hệ',
        fontSize: 20,
        fontWeight: 700,
        color: '#f7941e',
        textAlign: 'left',
        margin: { top: 'auto', bottom: 0, left: 0, right: 0 }
      },
      mobile: {
        text: 'Liên hệ',
        fontSize: 20,
        fontWeight: 700,
        color: '#f7941e',
        textAlign: 'left',
        margin: { top: 'auto', bottom: 0, left: 0, right: 0 }
      }
    },
    familyServiceCard3Buttons: {
      desktop: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      },
      tablet: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      },
      mobile: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      }
    },
    familyServiceCard3DetailBtn: {
      desktop: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      tablet: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      mobile: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      }
    },
    familyServiceCard3ConsultBtn: {
      desktop: {
        text: 'Tư vấn',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      tablet: {
        text: 'Tư vấn',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      mobile: {
        text: 'Tư vấn',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      }
    }
  }
}
