'use client'

import React from 'react'

import { Element, useNode, type UserComponent } from '@craftjs/core'

import { Container, Row, Text } from '@/components/page-builder/selectors'
import { InternetAndTVSection } from './InternetAndTVSection'
import { FamilySecuritySection } from './FamilySecuritySection'
import { FamilyServiceSection } from './FamilyServiceSection'
import { SmartDevicesSection } from './SmartDevicesSection'

export const AllSolutionsHouseholdBuilder: UserComponent = props => {
  const {
    connectors: { connect, drag }
  } = useNode()

  return (
    <div
      className='w-full'
      ref={(ref: HTMLDivElement | null) => {
        if (ref) {
          connect(drag(ref))
        }
      }}
    >
      <Element id='container' canvas is={Container} {...props.container}>
        <Element is={Row} id='all-solutions-container' disableSelection={true} canvas {...props.allSolutionsContainer}>
          <Element is={Text} id='all-solutions-title' canvas {...props.allSolutionsTitle} />
          {/* Internet & TV Section */}
          <InternetAndTVSection />

          {/* Family Security Section */}
          <FamilySecuritySection />

          {/* Family Service Section */}
          <FamilyServiceSection />

          {/* Smart Devices Section */}
          <SmartDevicesSection />
        </Element>
      </Element>
    </div>
  )
}

AllSolutionsHouseholdBuilder.craft = {
  displayName: 'Tất cả Giải pháp cho Gia đình',
  props: {
    container: {
      height: 'auto',
      background: '#FFFFFF',
      padding: ['0', '0', '0', '0']
    },
    allSolutionsContainer: {
      desktop: {
        padding: { top: 64, bottom: 64, left: 16, right: 16 },
        colWidths: [12],
        gap: 6,
        height: 'auto',
        alignItems: 'center',
        contentAlign: 'start',
        justifyContent: 'center',
        width: '100%'
      },
      tablet: {
        padding: { top: 48, bottom: 48, left: 16, right: 16 },
        colWidths: [12],
        gap: 6,
        height: 'auto',
        alignItems: 'center',
        contentAlign: 'start',
        justifyContent: 'center',
        width: '100%'
      },
      mobile: {
        padding: { top: 32, bottom: 32, left: 16, right: 16 },
        colWidths: [12],
        gap: 6,
        height: 'auto',
        alignItems: 'center',
        contentAlign: 'start',
        justifyContent: 'center',
        width: '100%'
      }
    },
    allSolutionsTitle: {
      desktop: {
        text: 'Tất cả Giải pháp cho Gia đình',
        fontSize: 32,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'center'
      },
      tablet: {
        text: 'Tất cả Giải pháp cho Gia đình',
        fontSize: 28,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'center'
      },
      mobile: {
        text: 'Tất cả Giải pháp cho Gia đình',
        fontSize: 24,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'center'
      }
    },
    sectionTitle: {
      desktop: {
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        flexDirection: 'column',
        gap: 0,
        margin: { top: 0, bottom: 40, left: 0, right: 0 }
      },
      tablet: {
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        flexDirection: 'column',
        gap: 0,
        margin: { top: 0, bottom: 40, left: 0, right: 0 }
      },
      mobile: {
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        flexDirection: 'column',
        gap: 0,
        margin: { top: 0, bottom: 40, left: 0, right: 0 }
      }
    }
  }
}
