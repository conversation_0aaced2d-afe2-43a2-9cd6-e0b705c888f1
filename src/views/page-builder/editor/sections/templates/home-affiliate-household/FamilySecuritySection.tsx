'use client'

import React from 'react'

import type { UserComponent } from '@craftjs/core'
import { Element, useNode } from '@craftjs/core'

import { Container, Row, Column, Text, Button, Image } from '@/components/page-builder/selectors'
import { ProductCard } from '@/components/page-builder/editor/CustomComponents/IoTTemplate/ProductCard'

export const FamilySecuritySection: UserComponent = props => {
  const {
    connectors: { connect, drag }
  } = useNode()

  return (
    <div
      className='w-full'
      ref={(ref: HTMLDivElement | null) => {
        if (ref) {
          connect(drag(ref))
        }
      }}
    >
      {/* Security Section */}
      <Element id='container' canvas is={Container} {...props.container}>
        <Element is={Row} id='security-section' canvas {...props.securitySection}>
          <Element is={Column} id='security-heading' canvas {...props.sectionTitle}>
            <Element is={Text} id='security-heading' canvas {...props.securityHeading} />
          </Element>
          <Element id='container-security' canvas is={Container} {...props.container}>
            <Element is={Row} id='security-cards' canvas {...props.securityCards}>
              {/* Card 1: Home Camera */}
              <Element is={Column} id='security-card-1' canvas {...props.securityCard}>
                <Element is={ProductCard} id='product1' canvas {...props.productCard}>
                  <div className='rounded-t-lg bg-[#e8f5e9]'>
                    <Element is={Image} id='security-card-1-image' canvas {...props.securityCard1Image} />
                  </div>
                  <div className='flex h-full flex-col justify-between p-6'>
                    <div className='flex flex-col'>
                      <Element is={Text} id='security-card-1-title' canvas {...props.securityCard1Title} />
                      <Element is={Text} id='security-card-1-features' canvas {...props.securityCard1Features} />
                    </div>
                    <div className='flex flex-col'>
                      <Element is={Text} id='security-card-1-price' canvas {...props.securityCard1Price} />
                      <Element is={Row} id='security-card-1-buttons' canvas {...props.securityCard1Buttons}>
                        <Element is={Button} id='security-card-1-detail-btn' canvas {...props.securityCard1DetailBtn} />
                        <Element is={Button} id='security-card-1-buy-btn' canvas {...props.securityCard1BuyBtn} />
                      </Element>
                    </div>
                  </div>
                </Element>
              </Element>

              {/* Card 2: SmartHome - Căn hộ */}
              <Element is={Column} id='security-card-2' canvas {...props.securityCard}>
                <Element is={ProductCard} id='product2' canvas {...props.productCard}>
                  <div className='rounded-t-lg bg-[#e8f5e9]'>
                    <Element is={Image} id='security-card-2-image' canvas {...props.securityCard2Image} />
                  </div>
                  <div className='flex h-full flex-col justify-between p-6'>
                    <div className='flex flex-col'>
                      <Element is={Text} id='security-card-2-title' canvas {...props.securityCard2Title} />
                      <Element is={Text} id='security-card-2-features' canvas {...props.securityCard2Features} />
                    </div>
                    <div className='flex flex-col'>
                      <Element is={Text} id='security-card-2-price' canvas {...props.securityCard2Price} />
                      <Element is={Row} id='security-card-2-buttons' canvas {...props.securityCard2Buttons}>
                        <Element is={Button} id='security-card-2-detail-btn' canvas {...props.securityCard2DetailBtn} />
                        <Element
                          is={Button}
                          id='security-card-2-consult-btn'
                          canvas
                          {...props.securityCard2ConsultBtn}
                        />
                      </Element>
                    </div>
                  </div>
                </Element>
              </Element>

              {/* Card 3: SmartHome - Nhà phố */}
              <Element is={Column} id='security-card-3' canvas {...props.securityCard}>
                <Element is={ProductCard} id='product3' canvas {...props.productCard}>
                  <div className='rounded-t-lg bg-[#e8f5e9]'>
                    <Element is={Image} id='security-card-3-image' canvas {...props.securityCard3Image} />
                  </div>
                  <div className='flex h-full flex-col justify-between p-6'>
                    <div className='flex flex-col'>
                      <Element is={Text} id='security-card-3-title' canvas {...props.securityCard3Title} />
                      <Element is={Text} id='security-card-3-features' canvas {...props.securityCard3Features} />
                    </div>
                    <div className='flex flex-col'>
                      <Element is={Text} id='security-card-3-price' canvas {...props.securityCard3Price} />
                      <Element is={Row} id='security-card-3-buttons' canvas {...props.securityCard3Buttons}>
                        <Element is={Button} id='security-card-3-detail-btn' canvas {...props.securityCard3DetailBtn} />
                        <Element
                          is={Button}
                          id='security-card-3-consult-btn'
                          canvas
                          {...props.securityCard3ConsultBtn}
                        />
                      </Element>
                    </div>
                  </div>
                </Element>
              </Element>

              {/* Card 4: SmartHome - Biệt thự */}
              <Element is={Column} id='security-card-4' canvas {...props.securityCard}>
                <Element is={ProductCard} id='product4' canvas {...props.productCard}>
                  <div className='rounded-t-lg bg-[#e8f5e9]'>
                    <Element is={Image} id='security-card-4-image' canvas {...props.securityCard4Image} />
                  </div>
                  <div className='flex h-full flex-col justify-between p-6'>
                    <div className='flex flex-col'>
                      <Element is={Text} id='security-card-4-title' canvas {...props.securityCard4Title} />
                      <Element is={Text} id='security-card-4-features' canvas {...props.securityCard4Features} />
                    </div>
                    <div className='flex flex-col'>
                      <Element is={Text} id='security-card-4-price' canvas {...props.securityCard4Price} />
                      <Element is={Row} id='security-card-4-buttons' canvas {...props.securityCard4Buttons}>
                        <Element is={Button} id='security-card-4-detail-btn' canvas {...props.securityCard4DetailBtn} />
                        <Element
                          is={Button}
                          id='security-card-4-consult-btn'
                          canvas
                          {...props.securityCard4ConsultBtn}
                        />
                      </Element>
                    </div>
                  </div>
                </Element>
              </Element>
            </Element>
          </Element>
        </Element>
      </Element>
    </div>
  )
}

FamilySecuritySection.craft = {
  displayName: 'Security Section',
  props: {
    container: {
      height: 'auto',
      background: '#FFFFFF',
      padding: ['0', '0', '0', '0']
    },
    // Security Section
    securitySection: {
      desktop: {
        padding: { top: 0, bottom: 24, left: 0, right: 0 },
        colWidths: [12],
        gap: 6,
        height: 'auto',
        alignItems: 'center',
        justifyContent: 'center',
        width: '100%'
      },
      tablet: {
        padding: { top: 0, bottom: 24, left: 0, right: 0 },
        colWidths: [12],
        gap: 6,
        height: 'auto',
        alignItems: 'center',
        justifyContent: 'center',
        width: '100%'
      },
      mobile: {
        padding: { top: 0, bottom: 24, left: 0, right: 0 },
        colWidths: [12],
        gap: 6,
        height: 'auto',
        alignItems: 'center',
        justifyContent: 'center',
        width: '100%'
      }
    },
    securityHeading: {
      desktop: {
        text: '#An ninh Gia đình',
        fontSize: 24,
        fontWeight: 700,
        color: '#374151',
        textAlign: 'left'
      },
      tablet: {
        text: '#An ninh Gia đình',
        fontSize: 22,
        fontWeight: 700,
        color: '#374151',
        textAlign: 'left'
      },
      mobile: {
        text: '#An ninh Gia đình',
        fontSize: 20,
        fontWeight: 700,
        color: '#374151',
        textAlign: 'left'
      }
    },
    securityCards: {
      desktop: {
        colWidths: [3, 3, 3, 3],
        padding: { top: 0, bottom: 24, left: 0, right: 0 },
        gap: 6,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'center',
        width: '100%'
      },
      tablet: {
        colWidths: [6, 6],
        gap: 6,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'center',
        width: '100%',
        isBreakLine: true
      },
      mobile: {
        colWidths: [12],
        gap: 6,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'center',
        width: '100%',
        isBreakLine: true
      }
    },
    sectionTitle: {
      desktop: {
        margin: { top: 0, bottom: 16, left: 0, right: 0 }
      },
      tablet: {
        margin: { top: 0, bottom: 16, left: 0, right: 0 }
      },
      mobile: {
        margin: { top: 0, bottom: 16, left: 0, right: 0 }
      }
    },
    // Security Card 1: Home Camera
    securityCard: {
      desktop: {
        backgroundColor: '#ffffff',
        backgroundType: 'COLOR',
        padding: { top: 0, bottom: 0, left: 0, right: 0 },
        gap: 0,
        height: 'auto',
        alignItems: 'stretch',
        contentAlign: 'start',
        justifyContent: 'center',
        width: '100%',
        border: {
          radius: 8
        }
      },
      tablet: {
        backgroundColor: '#ffffff',
        backgroundType: 'COLOR',
        padding: { top: 0, bottom: 0, left: 0, right: 0 },
        gap: 0,
        height: 'auto',
        alignItems: 'stretch',
        contentAlign: 'start',
        justifyContent: 'center',
        width: '100%',
        border: {
          radius: 8
        }
      },
      mobile: {
        backgroundColor: '#ffffff',
        backgroundType: 'COLOR',
        padding: { top: 0, bottom: 0, left: 0, right: 0 },
        margin: { top: 0, bottom: 16, left: 0, right: 0 },
        gap: 0,
        height: 'auto',
        alignItems: 'stretch',
        contentAlign: 'start',
        justifyContent: 'center',
        width: '100%',
        border: {
          radius: 8
        }
      }
    },
    productCard: {
      desktop: {
        backgroundColor: '#ffffff',
        borderRadius: 8,
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
        hoverTransform: 'translateY(-5px)',
        hoverBoxShadow: '0 10px 25px rgba(0,0,0,0.1)',
        transition: 'transform 0.3s ease, box-shadow 0.3s ease',
        height: '100%',
        display: 'flex',
        flexDirection: 'column'
      },
      tablet: {
        backgroundColor: '#ffffff',
        borderRadius: 8,
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
        hoverTransform: 'translateY(-5px)',
        hoverBoxShadow: '0 10px 25px rgba(0,0,0,0.1)',
        transition: 'transform 0.3s ease, box-shadow 0.3s ease',
        height: '100%',
        display: 'flex',
        flexDirection: 'column'
      },
      mobile: {
        backgroundColor: '#ffffff',
        borderRadius: 8,
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
        hoverTransform: 'translateY(-5px)',
        hoverBoxShadow: '0 10px 25px rgba(0,0,0,0.1)',
        transition: 'transform 0.3s ease, box-shadow 0.3s ease',
        height: '100%',
        display: 'flex',
        flexDirection: 'column'
      }
    },
    securityCard1Image: {
      desktop: {
        imgSrc: 'https://placehold.co/600x400/E8F5E9/1B5E20?text=Home+Camera',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      },
      tablet: {
        imgSrc: 'https://placehold.co/600x400/E8F5E9/1B5E20?text=Home+Camera',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      },
      mobile: {
        imgSrcMobile: 'https://placehold.co/600x400/E8F5E9/1B5E20?text=Home+Camera',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      }
    },
    securityCard1Title: {
      desktop: {
        text: 'Home Camera',
        fontSize: 18,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'left'
      },
      tablet: {
        text: 'Home Camera',
        fontSize: 18,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'left'
      },
      mobile: {
        text: 'Home Camera',
        fontSize: 18,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'left'
      }
    },
    securityCard1Features: {
      desktop: {
        text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Internet tốc độ cao<br>✓ Tặng 01 Camera trong nhà<br>✓ Lưu trữ cloud an toàn</div>',
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'left'
      },
      tablet: {
        text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Internet tốc độ cao<br>✓ Tặng 01 Camera trong nhà<br>✓ Lưu trữ cloud an toàn</div>',
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'left'
      },
      mobile: {
        text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Internet tốc độ cao<br>✓ Tặng 01 Camera trong nhà<br>✓ Lưu trữ cloud an toàn</div>',
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'left'
      }
    },
    securityCard1Price: {
      desktop: {
        text: '210.000đ<span style="font-size: 16px; font-weight: 400;">/tháng</span>',
        fontSize: 24,
        fontWeight: 700,
        color: '#f7941e',
        textAlign: 'left',
        margin: { top: 'auto', bottom: 0, left: 0, right: 0 }
      },
      tablet: {
        text: '210.000đ<span style="font-size: 16px; font-weight: 400;">/tháng</span>',
        fontSize: 24,
        fontWeight: 700,
        color: '#f7941e',
        textAlign: 'left',
        margin: { top: 'auto', bottom: 0, left: 0, right: 0 }
      },
      mobile: {
        text: '210.000đ<span style="font-size: 16px; font-weight: 400;">/tháng</span>',
        fontSize: 24,
        fontWeight: 700,
        color: '#f7941e',
        textAlign: 'left',
        margin: { top: 'auto', bottom: 0, left: 0, right: 0 }
      }
    },
    securityCard1Buttons: {
      desktop: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      },
      tablet: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      },
      mobile: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      }
    },
    securityCard1DetailBtn: {
      desktop: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      tablet: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      mobile: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      }
    },
    securityCard1BuyBtn: {
      desktop: {
        text: 'Mua ngay',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      tablet: {
        text: 'Mua ngay',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      mobile: {
        text: 'Mua ngay',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      }
    },
    // Security Card 2: SmartHome - Căn hộ
    securityCard2Image: {
      desktop: {
        imgSrc: 'https://placehold.co/600x400/E8F5E9/1B5E20?text=SmartHome+Chung+Cư',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      },
      tablet: {
        imgSrc: 'https://placehold.co/600x400/E8F5E9/1B5E20?text=SmartHome+Chung+Cư',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      },
      mobile: {
        imgSrcMobile: 'https://placehold.co/600x400/E8F5E9/1B5E20?text=SmartHome+Chung+Cư',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      }
    },
    securityCard2Title: {
      desktop: { text: 'SmartHome - Căn hộ', fontSize: 18, fontWeight: 700, color: '#00529c', textAlign: 'left' },
      tablet: { text: 'SmartHome - Căn hộ', fontSize: 18, fontWeight: 700, color: '#00529c', textAlign: 'left' },
      mobile: { text: 'SmartHome - Căn hộ', fontSize: 18, fontWeight: 700, color: '#00529c', textAlign: 'left' }
    },
    securityCard2Features: {
      desktop: {
        text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Điều khiển chiếu sáng, rèm<br>✓ Cảm biến cửa, an ninh<br>✓ Tích hợp trợ lý ảo</div>',
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'left'
      },
      tablet: {
        text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Điều khiển chiếu sáng, rèm<br>✓ Cảm biến cửa, an ninh<br>✓ Tích hợp trợ lý ảo</div>',
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'left'
      },
      mobile: {
        text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Điều khiển chiếu sáng, rèm<br>✓ Cảm biến cửa, an ninh<br>✓ Tích hợp trợ lý ảo</div>',
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'left'
      }
    },
    securityCard2Price: {
      desktop: {
        text: 'Liên hệ',
        fontSize: 20,
        fontWeight: 700,
        color: '#f7941e',
        textAlign: 'left',
        margin: { top: 'auto', bottom: 0, left: 0, right: 0 }
      },
      tablet: {
        text: 'Liên hệ',
        fontSize: 20,
        fontWeight: 700,
        color: '#f7941e',
        textAlign: 'left',
        margin: { top: 'auto', bottom: 0, left: 0, right: 0 }
      },
      mobile: {
        text: 'Liên hệ',
        fontSize: 20,
        fontWeight: 700,
        color: '#f7941e',
        textAlign: 'left',
        margin: { top: 'auto', bottom: 0, left: 0, right: 0 }
      }
    },
    securityCard2Buttons: {
      desktop: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      },
      tablet: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      },
      mobile: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      }
    },
    securityCard2DetailBtn: {
      desktop: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      tablet: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      mobile: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      }
    },
    securityCard2ConsultBtn: {
      desktop: {
        text: 'Tư vấn',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      tablet: {
        text: 'Tư vấn',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      mobile: {
        text: 'Tư vấn',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      }
    },
    // Security Card 3: SmartHome - Nhà phố
    securityCard3Image: {
      desktop: {
        imgSrc: 'https://placehold.co/600x400/E8F5E9/1B5E20?text=SmartHome+Nhà+Phố',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      },
      tablet: {
        imgSrc: 'https://placehold.co/600x400/E8F5E9/1B5E20?text=SmartHome+Nhà+Phố',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      },
      mobile: {
        imgSrcMobile: 'https://placehold.co/600x400/E8F5E9/1B5E20?text=SmartHome+Nhà+Phố',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      }
    },
    securityCard3Title: {
      desktop: { text: 'SmartHome - Nhà phố', fontSize: 18, fontWeight: 700, color: '#00529c', textAlign: 'left' },
      tablet: { text: 'SmartHome - Nhà phố', fontSize: 18, fontWeight: 700, color: '#00529c', textAlign: 'left' },
      mobile: { text: 'SmartHome - Nhà phố', fontSize: 18, fontWeight: 700, color: '#00529c', textAlign: 'left' }
    },
    securityCard3Features: {
      desktop: {
        text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Giải pháp toàn diện cho nhà phố<br>✓ Điều khiển đa vùng<br>✓ Tích hợp hệ thống an ninh</div>',
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'left'
      },
      tablet: {
        text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Giải pháp toàn diện cho nhà phố<br>✓ Điều khiển đa vùng<br>✓ Tích hợp hệ thống an ninh</div>',
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'left'
      },
      mobile: {
        text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Giải pháp toàn diện cho nhà phố<br>✓ Điều khiển đa vùng<br>✓ Tích hợp hệ thống an ninh</div>',
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'left'
      }
    },
    securityCard3Price: {
      desktop: {
        text: 'Liên hệ',
        fontSize: 20,
        fontWeight: 700,
        color: '#f7941e',
        textAlign: 'left',
        margin: { top: 'auto', bottom: 0, left: 0, right: 0 }
      },
      tablet: {
        text: 'Liên hệ',
        fontSize: 20,
        fontWeight: 700,
        color: '#f7941e',
        textAlign: 'left',
        margin: { top: 'auto', bottom: 0, left: 0, right: 0 }
      },
      mobile: {
        text: 'Liên hệ',
        fontSize: 20,
        fontWeight: 700,
        color: '#f7941e',
        textAlign: 'left',
        margin: { top: 'auto', bottom: 0, left: 0, right: 0 }
      }
    },
    securityCard3Buttons: {
      desktop: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      },
      tablet: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      },
      mobile: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      }
    },
    securityCard3DetailBtn: {
      desktop: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      tablet: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      mobile: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      }
    },
    securityCard3ConsultBtn: {
      desktop: {
        text: 'Tư vấn',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      tablet: {
        text: 'Tư vấn',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      mobile: {
        text: 'Tư vấn',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      }
    },
    // Security Card 4: SmartHome - Biệt thự
    securityCard4Image: {
      desktop: {
        imgSrc: 'https://placehold.co/600x400/E8F5E9/1B5E20?text=SmartHome+Biệt+Thự',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      },
      tablet: {
        imgSrc: 'https://placehold.co/600x400/E8F5E9/1B5E20?text=SmartHome+Biệt+Thự',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      },
      mobile: {
        imgSrcMobile: 'https://placehold.co/600x400/E8F5E9/1B5E20?text=SmartHome+Biệt+Thự',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      }
    },
    securityCard4Title: {
      desktop: { text: 'SmartHome - Biệt thự', fontSize: 18, fontWeight: 700, color: '#00529c', textAlign: 'left' },
      tablet: { text: 'SmartHome - Biệt thự', fontSize: 18, fontWeight: 700, color: '#00529c', textAlign: 'left' },
      mobile: { text: 'SmartHome - Biệt thự', fontSize: 18, fontWeight: 700, color: '#00529c', textAlign: 'left' }
    },
    securityCard4Features: {
      desktop: {
        text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Giải pháp cao cấp, sang trọng<br>✓ Tự động hóa toàn diện<br>✓ Điều khiển bằng giọng nói</div>',
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'left'
      },
      tablet: {
        text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Giải pháp cao cấp, sang trọng<br>✓ Tự động hóa toàn diện<br>✓ Điều khiển bằng giọng nói</div>',
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'left'
      },
      mobile: {
        text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Giải pháp cao cấp, sang trọng<br>✓ Tự động hóa toàn diện<br>✓ Điều khiển bằng giọng nói</div>',
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'left'
      }
    },
    securityCard4Price: {
      desktop: {
        text: 'Liên hệ',
        fontSize: 20,
        fontWeight: 700,
        color: '#f7941e',
        textAlign: 'left',
        margin: { top: 'auto', bottom: 0, left: 0, right: 0 }
      },
      tablet: {
        text: 'Liên hệ',
        fontSize: 20,
        fontWeight: 700,
        color: '#f7941e',
        textAlign: 'left',
        margin: { top: 'auto', bottom: 0, left: 0, right: 0 }
      },
      mobile: {
        text: 'Liên hệ',
        fontSize: 20,
        fontWeight: 700,
        color: '#f7941e',
        textAlign: 'left',
        margin: { top: 'auto', bottom: 0, left: 0, right: 0 }
      }
    },
    securityCard4Buttons: {
      desktop: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      },
      tablet: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      },
      mobile: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      }
    },
    securityCard4DetailBtn: {
      desktop: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      tablet: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      mobile: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      }
    },
    securityCard4ConsultBtn: {
      desktop: {
        text: 'Tư vấn',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      tablet: {
        text: 'Tư vấn',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      mobile: {
        text: 'Tư vấn',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      }
    }
  }
}
