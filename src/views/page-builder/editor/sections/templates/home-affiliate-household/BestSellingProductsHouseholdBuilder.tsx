'use client'

import { Element, useNode, type UserComponent } from '@craftjs/core'

import { Container, Row, Column, Text, Button, Image } from '@/components/page-builder/selectors'
import { ProductCard } from '@/components/page-builder/editor/CustomComponents/IoTTemplate/ProductCard'

export const BestSellingProductsHouseholdBuilder: UserComponent = props => {
  const {
    connectors: { connect, drag }
  } = useNode()

  return (
    <div
      className='w-full'
      ref={(ref: HTMLDivElement | null) => {
        if (ref) {
          connect(drag(ref))
        }
      }}
    >
      <Element id='container' canvas is={Container} {...props.container}>
        <Element is={Row} id='products-container' disableSelection={true} canvas {...props.productsContainer}>
          <Element is={Column} id='products-title-section' canvas {...props.productsTitleSection}>
            <Element is={Text} id='products-title' canvas {...props.productsTitle} />
          </Element>
          <Element is={Row} id='products-cards-row' canvas {...props.productsCardsRow}>
            <Element is={Column} id='product-card-1' canvas {...props.productColumnWrapper}>
              <Element is={ProductCard} id='product1' canvas {...props.productCard}>
                <div className='rounded-t-lg bg-[#e2e8f0]'>
                  <Element is={Image} id='card-1-image' canvas {...props.card1Image} />
                </div>
                <div className='flex h-full flex-col justify-between p-4'>
                  <div className='flex flex-col'>
                    <Element is={Text} id='card-1-title' canvas {...props.card1Title} />
                    <Element is={Text} id='card-1-description' canvas {...props.card1Description} />
                    <Element is={Text} id='card-1-promotion' canvas {...props.card1Promotion} />
                  </div>
                  <div className='flex flex-col'>
                    <Element is={Text} id='card-1-price' canvas {...props.card1Price} />
                    <Element is={Row} id='card-1-buttons' canvas {...props.card1Buttons}>
                      <Element is={Button} id='card-1-detail-btn' canvas {...props.card1DetailBtn} />
                      <Element is={Button} id='card-1-buy-btn' canvas {...props.card1BuyBtn} />
                    </Element>
                  </div>
                </div>
              </Element>
            </Element>
            <Element is={Column} id='product-card-2' canvas {...props.productColumnWrapper}>
              <Element is={ProductCard} id='product2' canvas {...props.productCard}>
                <div className='rounded-t-lg bg-[#e2e8f0]'>
                  <Element is={Image} id='card-2-image' canvas {...props.card2Image} />
                </div>
                <div className='flex h-full flex-col justify-between p-4'>
                  <div className='flex flex-col'>
                    <Element is={Text} id='card-2-title' canvas {...props.card2Title} />
                    <Element is={Text} id='card-2-description' canvas {...props.card2Description} />
                    <Element is={Text} id='card-2-promotion' canvas {...props.card2Promotion} />
                  </div>
                  <div className='flex flex-col'>
                    <Element is={Text} id='card-2-price' canvas {...props.card2Price} />
                    <Element is={Row} id='card-2-buttons' canvas {...props.card2Buttons}>
                      <Element is={Button} id='card-2-detail-btn' canvas {...props.card2DetailBtn} />
                      <Element is={Button} id='card-2-buy-btn' canvas {...props.card2BuyBtn} />
                    </Element>
                  </div>
                </div>
              </Element>
            </Element>
            <Element is={Column} id='product-card-3' canvas {...props.productColumnWrapper}>
              <Element is={ProductCard} id='product3' canvas {...props.productCard}>
                <div className='rounded-t-lg bg-[#e2e8f0]'>
                  <Element is={Image} id='card-3-image' canvas {...props.card3Image} />
                </div>
                <div className='flex h-full flex-col justify-between p-4'>
                  <div className='flex flex-col'>
                    <Element is={Text} id='card-3-title' canvas {...props.card3Title} />
                    <Element is={Text} id='card-3-description' canvas {...props.card3Description} />
                    <Element is={Text} id='card-3-promotion' canvas {...props.card3Promotion} />
                  </div>
                  <div className='flex flex-col'>
                    <Element is={Text} id='card-3-price' canvas {...props.card3Price} />
                    <Element is={Row} id='card-3-buttons' canvas {...props.card3Buttons}>
                      <Element is={Button} id='card-3-detail-btn' canvas {...props.card3DetailBtn} />
                      <Element is={Button} id='card-3-consult-btn' canvas {...props.card3ConsultBtn} />
                    </Element>
                  </div>
                </div>
              </Element>
            </Element>
            <Element is={Column} id='product-card-4' canvas {...props.productColumnWrapper}>
              <Element is={ProductCard} id='product4' canvas {...props.productCard}>
                <div className='rounded-t-lg bg-[#e2e8f0]'>
                  <Element is={Image} id='card-4-image' canvas {...props.card4Image} />
                </div>
                <div className='flex h-full flex-col justify-between p-4'>
                  <div className='flex flex-col'>
                    <Element is={Text} id='card-4-title' canvas {...props.card4Title} />
                    <Element is={Text} id='card-4-description' canvas {...props.card4Description} />
                    <Element is={Text} id='card-4-empty' canvas {...props.card4Empty} />
                  </div>
                  <div className='flex flex-col'>
                    <Element is={Text} id='card-4-contact' canvas {...props.card4Contact} />
                    <Element is={Row} id='card-4-buttons' canvas {...props.card4Buttons}>
                      <Element is={Button} id='card-4-detail-btn' canvas {...props.card4DetailBtn} />
                      <Element is={Button} id='card-4-consult-btn' canvas {...props.card4ConsultBtn} />
                    </Element>
                  </div>
                </div>
              </Element>
            </Element>
          </Element>
        </Element>
      </Element>
    </div>
  )
}

BestSellingProductsHouseholdBuilder.craft = {
  props: {
    container: {
      height: 'auto',
      background: '#FFFFFF',
      padding: ['0', '0', '0', '0']
    },
    productsContainer: {
      desktop: {
        padding: { top: 64, bottom: 64, left: 16, right: 16 },
        colWidths: [12],
        gap: 6,
        height: 'auto',
        alignItems: 'center',
        contentAlign: 'start',
        justifyContent: 'center',
        width: '100%'
      },
      tablet: {
        padding: { top: 48, bottom: 48, left: 16, right: 16 },
        colWidths: [12],
        gap: 6,
        height: 'auto',
        alignItems: 'center',
        contentAlign: 'start',
        justifyContent: 'center',
        width: '100%'
      },
      mobile: {
        padding: { top: 32, bottom: 32, left: 16, right: 16 },
        colWidths: [12],
        gap: 6,
        height: 'auto',
        alignItems: 'center',
        contentAlign: 'start',
        justifyContent: 'center',
        width: '100%'
      }
    },
    productsTitleSection: {
      desktop: {
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        flexDirection: 'column',
        gap: 0,
        margin: { top: 0, bottom: 40, left: 0, right: 0 }
      },
      tablet: {
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        flexDirection: 'column',
        gap: 0,
        margin: { top: 0, bottom: 40, left: 0, right: 0 }
      },
      mobile: {
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        flexDirection: 'column',
        gap: 0,
        margin: { top: 0, bottom: 40, left: 0, right: 0 }
      }
    },
    productsTitle: {
      desktop: {
        text: 'Giải pháp được nhiều gia đình lựa chọn',
        fontSize: 32,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'center'
      },
      tablet: {
        text: 'Giải pháp được nhiều gia đình lựa chọn',
        fontSize: 28,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'center'
      },
      mobile: {
        text: 'Giải pháp được nhiều gia đình lựa chọn',
        fontSize: 24,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'center'
      }
    },
    productsCardsRow: {
      desktop: {
        colWidths: [3, 3, 3, 3],
        gap: 6,
        height: 'auto',
        alignItems: 'stretch',
        contentAlign: 'start',
        justifyContent: 'center',
        width: '100%'
      },
      tablet: {
        colWidths: [6, 6],
        gap: 6,
        height: 'auto',
        alignItems: 'stretch',
        contentAlign: 'start',
        justifyContent: 'center',
        width: '100%',
        isBreakLine: true
      },
      mobile: {
        colWidths: [12],
        gap: 6,
        height: 'auto',
        alignItems: 'stretch',
        contentAlign: 'start',
        justifyContent: 'center',
        width: '100%',
        isBreakLine: true
      }
    },
    productColumnWrapper: {
      desktop: {
        backgroundColor: '#ffffff',
        backgroundType: 'COLOR',
        padding: { top: 0, bottom: 0, left: 0, right: 0 },
        gap: 0,
        height: '100%',
        alignItems: 'stretch',
        contentAlign: 'start',
        justifyContent: 'center',
        width: '100%',
        border: {
          radius: 8
        }
      },
      tablet: {
        backgroundColor: '#ffffff',
        backgroundType: 'COLOR',
        padding: { top: 0, bottom: 0, left: 0, right: 0 },
        gap: 0,
        height: '100%',
        alignItems: 'stretch',
        contentAlign: 'start',
        justifyContent: 'center',
        width: '100%',
        border: {
          radius: 8
        }
      },
      mobile: {
        backgroundColor: '#ffffff',
        backgroundType: 'COLOR',
        padding: { top: 0, bottom: 0, left: 0, right: 0 },
        margin: { top: 0, bottom: 16, left: 0, right: 0 },
        gap: 0,
        height: '100%',
        alignItems: 'stretch',
        contentAlign: 'start',
        justifyContent: 'center',
        width: '100%',
        border: {
          radius: 8
        }
      }
    },
    productCard: {
      desktop: {
        backgroundColor: '#ffffff',
        borderRadius: 8,
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
        hoverTransform: 'translateY(-5px)',
        hoverBoxShadow: '0 10px 25px rgba(0,0,0,0.1)',
        transition: 'transform 0.3s ease, box-shadow 0.3s ease',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        minHeight: 400
      },
      tablet: {
        backgroundColor: '#ffffff',
        borderRadius: 8,
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
        hoverTransform: 'translateY(-5px)',
        hoverBoxShadow: '0 10px 25px rgba(0,0,0,0.1)',
        transition: 'transform 0.3s ease, box-shadow 0.3s ease',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        minHeight: 400
      },
      mobile: {
        backgroundColor: '#ffffff',
        borderRadius: 8,
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
        hoverTransform: 'translateY(-5px)',
        hoverBoxShadow: '0 10px 25px rgba(0,0,0,0.1)',
        transition: 'transform 0.3s ease, box-shadow 0.3s ease',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        minHeight: 400
      }
    },
    card1Image: {
      desktop: {
        imgSrc: 'https://placehold.co/600x400/e2e8f0/334155?text=Home+Mesh+2',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      },
      tablet: {
        imgSrc: 'https://placehold.co/600x400/e2e8f0/334155?text=Home+Mesh+2',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      },
      mobile: {
        imgSrcMobile: 'https://placehold.co/600x400/e2e8f0/334155?text=Home+Mesh+2',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      }
    },
    card1Title: {
      desktop: {
        text: 'Home Mesh 2+',
        fontSize: 18,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'left'
      },
      tablet: {
        text: 'Home Mesh 2+',
        fontSize: 18,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'left'
      },
      mobile: {
        text: 'Home Mesh 2+',
        fontSize: 18,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'left'
      }
    },
    card1Description: {
      desktop: {
        text: 'Internet & TV cho gia đình',
        fontSize: 14,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'left'
      },
      tablet: {
        text: 'Internet & TV cho gia đình',
        fontSize: 14,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'left'
      },
      mobile: {
        text: 'Internet & TV cho gia đình',
        fontSize: 14,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'left'
      }
    },
    card1Promotion: {
      desktop: {
        text: '<div style="background-color: #f0fdf4; padding: 8px; border-radius: 4px; margin: 8px 0;"><p style="font-size: 12px; font-weight: 700; color: #16a34a; margin: 0;">KM: Tặng thêm 2 tháng khi đóng trước 6 tháng.</p></div>',
        fontSize: 12,
        fontWeight: 700,
        color: '#16a34a',
        textAlign: 'left',
        padding: { top: 8, bottom: 8, left: 0, right: 0 },
        backgroundColor: '#f0fdf4',
        border: {
          radius: 4
        }
      },
      tablet: {
        text: '<div style="background-color: #f0fdf4; padding: 8px; border-radius: 4px; margin: 8px 0;"><p style="font-size: 12px; font-weight: 700; color: #16a34a; margin: 0;">KM: Tặng thêm 2 tháng khi đóng trước 6 tháng.</p></div>',
        fontSize: 12,
        fontWeight: 700,
        color: '#16a34a',
        textAlign: 'left',
        padding: { top: 8, bottom: 8, left: 0, right: 0 },
        backgroundColor: '#f0fdf4',
        border: {
          radius: 4
        }
      },
      mobile: {
        text: '<div style="background-color: #f0fdf4; padding: 8px; border-radius: 4px; margin: 8px 0;"><p style="font-size: 12px; font-weight: 700; color: #16a34a; margin: 0;">KM: Tặng thêm 2 tháng khi đóng trước 6 tháng.</p></div>',
        fontSize: 12,
        fontWeight: 700,
        color: '#16a34a',
        textAlign: 'left',
        padding: { top: 8, bottom: 8, left: 0, right: 0 },
        backgroundColor: '#f0fdf4',
        border: {
          radius: 4
        }
      }
    },
    card1Price: {
      desktop: {
        text: '245.000đ<span style="font-size: 16px; font-weight: 400;">/tháng</span>',
        fontSize: 24,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'left'
      },
      tablet: {
        text: '245.000đ<span style="font-size: 16px; font-weight: 400;">/tháng</span>',
        fontSize: 24,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'left'
      },
      mobile: {
        text: '245.000đ<span style="font-size: 16px; font-weight: 400;">/tháng</span>',
        fontSize: 24,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'left'
      }
    },
    card1Buttons: {
      desktop: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      },
      tablet: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      },
      mobile: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      }
    },
    card1DetailBtn: {
      desktop: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      tablet: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      mobile: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      }
    },
    card1BuyBtn: {
      desktop: {
        text: 'Mua ngay',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      tablet: {
        text: 'Mua ngay',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      mobile: {
        text: 'Mua ngay',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      }
    },
    card2Image: {
      desktop: {
        imgSrc: 'https://placehold.co/600x400/e2e8f0/334155?text=Home+Camera',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      },
      tablet: {
        imgSrc: 'https://placehold.co/600x400/e2e8f0/334155?text=Home+Camera',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      },
      mobile: {
        imgSrcMobile: 'https://placehold.co/600x400/e2e8f0/334155?text=Home+Camera',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      }
    },
    card2Content: {
      desktop: {
        padding: { top: 16, bottom: 16, left: 16, right: 16 },
        gap: 8,
        height: '100%',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        flexDirection: 'column',
        display: 'flex',
        flex: 'grow'
      },
      tablet: {
        padding: { top: 16, bottom: 16, left: 16, right: 16 },
        gap: 8,
        height: '100%',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        flexDirection: 'column',
        display: 'flex',
        flex: 'grow'
      },
      mobile: {
        padding: { top: 16, bottom: 16, left: 16, right: 16 },
        gap: 8,
        height: '100%',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        flexDirection: 'column',
        display: 'flex',
        flex: 'grow'
      }
    },
    card2Title: {
      desktop: {
        text: 'Home Camera',
        fontSize: 18,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'left'
      },
      tablet: {
        text: 'Home Camera',
        fontSize: 18,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'left'
      },
      mobile: {
        text: 'Home Camera',
        fontSize: 18,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'left'
      }
    },
    card2Description: {
      desktop: {
        text: 'Internet & Camera an ninh',
        fontSize: 14,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'left'
      },
      tablet: {
        text: 'Internet & Camera an ninh',
        fontSize: 14,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'left'
      },
      mobile: {
        text: 'Internet & Camera an ninh',
        fontSize: 14,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'left'
      }
    },
    card2Promotion: {
      desktop: {
        text: '<div style="background-color: #fff7ed; padding: 8px; border-radius: 4px; margin: 8px 0;"><p style="font-size: 12px; font-weight: 700; color: #ea580c; margin: 0;">KM: Tặng 01 Camera khi lắp mới.</p></div>',
        fontSize: 12,
        fontWeight: 700,
        color: '#ea580c',
        textAlign: 'left',
        padding: { top: 8, bottom: 8, left: 0, right: 0 },
        backgroundColor: '#fff7ed',
        border: {
          radius: 4
        }
      },
      tablet: {
        text: '<div style="background-color: #fff7ed; padding: 8px; border-radius: 4px; margin: 8px 0;"><p style="font-size: 12px; font-weight: 700; color: #ea580c; margin: 0;">KM: Tặng 01 Camera khi lắp mới.</p></div>',
        fontSize: 12,
        fontWeight: 700,
        color: '#ea580c',
        textAlign: 'left',
        padding: { top: 8, bottom: 8, left: 0, right: 0 },
        backgroundColor: '#fff7ed',
        border: {
          radius: 4
        }
      },
      mobile: {
        text: '<div style="background-color: #fff7ed; padding: 8px; border-radius: 4px; margin: 8px 0;"><p style="font-size: 12px; font-weight: 700; color: #ea580c; margin: 0;">KM: Tặng 01 Camera khi lắp mới.</p></div>',
        fontSize: 12,
        fontWeight: 700,
        color: '#ea580c',
        textAlign: 'left',
        padding: { top: 8, bottom: 8, left: 0, right: 0 },
        backgroundColor: '#fff7ed',
        border: {
          radius: 4
        }
      }
    },
    card2Price: {
      desktop: {
        text: '210.000đ<span style="font-size: 16px; font-weight: 400;">/tháng</span>',
        fontSize: 24,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'left'
      },
      tablet: {
        text: '210.000đ<span style="font-size: 16px; font-weight: 400;">/tháng</span>',
        fontSize: 24,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'left'
      },
      mobile: {
        text: '210.000đ<span style="font-size: 16px; font-weight: 400;">/tháng</span>',
        fontSize: 24,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'left'
      }
    },
    card2Buttons: {
      desktop: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      },
      tablet: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      },
      mobile: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      }
    },
    card2DetailBtn: {
      desktop: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      tablet: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      mobile: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      }
    },
    card2BuyBtn: {
      desktop: {
        text: 'Mua ngay',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      tablet: {
        text: 'Mua ngay',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      mobile: {
        text: 'Mua ngay',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      }
    },
    card3Image: {
      desktop: {
        imgSrc: 'https://placehold.co/600x400/e2e8f0/334155?text=Home+Combo',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      },
      tablet: {
        imgSrc: 'https://placehold.co/600x400/e2e8f0/334155?text=Home+Combo',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      },
      mobile: {
        imgSrcMobile: 'https://placehold.co/600x400/e2e8f0/334155?text=Home+Combo',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      }
    },
    card3Content: {
      desktop: {
        padding: { top: 16, bottom: 16, left: 16, right: 16 },
        gap: 8,
        height: '100%',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        flexDirection: 'column',
        display: 'flex'
      },
      tablet: {
        padding: { top: 16, bottom: 16, left: 16, right: 16 },
        gap: 8,
        height: '100%',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        flexDirection: 'column',
        display: 'flex'
      },
      mobile: {
        padding: { top: 16, bottom: 16, left: 16, right: 16 },
        gap: 8,
        height: '100%',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        flexDirection: 'column',
        display: 'flex'
      }
    },
    card3Title: {
      desktop: {
        text: 'Home Combo',
        fontSize: 18,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'left'
      },
      tablet: {
        text: 'Home Combo',
        fontSize: 18,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'left'
      },
      mobile: {
        text: 'Home Combo',
        fontSize: 18,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'left'
      }
    },
    card3Description: {
      desktop: {
        text: 'Trọn gói Internet - TV - Di động',
        fontSize: 14,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'left'
      },
      tablet: {
        text: 'Trọn gói Internet - TV - Di động',
        fontSize: 14,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'left'
      },
      mobile: {
        text: 'Trọn gói Internet - TV - Di động',
        fontSize: 14,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'left'
      }
    },
    card3Promotion: {
      desktop: {
        text: '<div style="background-color: #eff6ff; padding: 8px; border-radius: 4px; margin: 8px 0;"><p style="font-size: 12px; font-weight: 700; color: #2563eb; margin: 0;">KM: Tiết kiệm đến 50% so với dùng lẻ.</p></div>',
        fontSize: 12,
        fontWeight: 700,
        color: '#2563eb',
        textAlign: 'left',
        padding: { top: 8, bottom: 8, left: 0, right: 0 },
        backgroundColor: '#eff6ff',
        border: {
          radius: 4
        }
      },
      tablet: {
        text: '<div style="background-color: #eff6ff; padding: 8px; border-radius: 4px; margin: 8px 0;"><p style="font-size: 12px; font-weight: 700; color: #2563eb; margin: 0;">KM: Tiết kiệm đến 50% so với dùng lẻ.</p></div>',
        fontSize: 12,
        fontWeight: 700,
        color: '#2563eb',
        textAlign: 'left',
        padding: { top: 8, bottom: 8, left: 0, right: 0 },
        backgroundColor: '#eff6ff',
        border: {
          radius: 4
        }
      },
      mobile: {
        text: '<div style="background-color: #eff6ff; padding: 8px; border-radius: 4px; margin: 8px 0;"><p style="font-size: 12px; font-weight: 700; color: #2563eb; margin: 0;">KM: Tiết kiệm đến 50% so với dùng lẻ.</p></div>',
        fontSize: 12,
        fontWeight: 700,
        color: '#2563eb',
        textAlign: 'left',
        padding: { top: 8, bottom: 8, left: 0, right: 0 },
        backgroundColor: '#eff6ff',
        border: {
          radius: 4
        }
      }
    },
    card3Price: {
      desktop: {
        text: 'Từ 239.000đ',
        fontSize: 20,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'left'
      },
      tablet: {
        text: 'Từ 239.000đ',
        fontSize: 20,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'left'
      },
      mobile: {
        text: 'Từ 239.000đ',
        fontSize: 20,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'left'
      }
    },
    card3Buttons: {
      desktop: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      },
      tablet: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      },
      mobile: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      }
    },
    card3DetailBtn: {
      desktop: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      tablet: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      mobile: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      }
    },
    card3ConsultBtn: {
      desktop: {
        text: 'Tư vấn',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      tablet: {
        text: 'Tư vấn',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      mobile: {
        text: 'Tư vấn',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      }
    },
    card4Image: {
      desktop: {
        imgSrc: 'https://placehold.co/600x400/e2e8f0/334155?text=Smart+Home',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      },
      tablet: {
        imgSrc: 'https://placehold.co/600x400/e2e8f0/334155?text=Smart+Home',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      },
      mobile: {
        imgSrcMobile: 'https://placehold.co/600x400/e2e8f0/334155?text=Smart+Home',
        width: '100%',
        height: 160,
        border: {
          radius: 8
        },
        imageFit: 'fill',
        align: 'center'
      }
    },
    card4Content: {
      desktop: {
        padding: { top: 16, bottom: 16, left: 16, right: 16 },
        gap: 8,
        height: '100%',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        flexDirection: 'column',
        display: 'flex'
      },
      tablet: {
        padding: { top: 16, bottom: 16, left: 16, right: 16 },
        gap: 8,
        height: '100%',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        flexDirection: 'column',
        display: 'flex'
      },
      mobile: {
        padding: { top: 16, bottom: 16, left: 16, right: 16 },
        gap: 8,
        height: '100%',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        flexDirection: 'column',
        display: 'flex'
      }
    },
    card4Title: {
      desktop: {
        text: 'VNPT SmartHome',
        fontSize: 18,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'left'
      },
      tablet: {
        text: 'VNPT SmartHome',
        fontSize: 18,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'left'
      },
      mobile: {
        text: 'VNPT SmartHome',
        fontSize: 18,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'left'
      }
    },
    card4Description: {
      desktop: {
        text: 'Nhà thông minh trong tầm tay',
        fontSize: 14,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'left'
      },
      tablet: {
        text: 'Nhà thông minh trong tầm tay',
        fontSize: 14,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'left'
      },
      mobile: {
        text: 'Nhà thông minh trong tầm tay',
        fontSize: 14,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'left'
      }
    },
    card4Empty: {
      desktop: {
        text: '',
        fontSize: 12,
        fontWeight: 400,
        color: 'transparent',
        textAlign: 'left',
        padding: { top: 52, bottom: 0, left: 0, right: 0 }
      },
      tablet: {
        text: '',
        fontSize: 12,
        fontWeight: 400,
        color: 'transparent',
        textAlign: 'left',
        padding: { top: 52, bottom: 0, left: 0, right: 0 }
      },
      mobile: {
        text: '',
        fontSize: 12,
        fontWeight: 400,
        color: 'transparent',
        textAlign: 'left',
        padding: { top: 52, bottom: 0, left: 0, right: 0 }
      }
    },
    card4Contact: {
      desktop: {
        text: 'Liên hệ',
        fontSize: 20,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'left'
      },
      tablet: {
        text: 'Liên hệ',
        fontSize: 20,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'left'
      },
      mobile: {
        text: 'Liên hệ',
        fontSize: 20,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'left'
      }
    },
    card4Buttons: {
      desktop: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      },
      tablet: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      },
      mobile: {
        colWidths: [1, 1],
        gap: 2,
        height: 'auto',
        alignItems: 'stretch',
        justifyContent: 'space-between',
        width: '100%',
        margin: { top: 8, bottom: 0, left: 0, right: 0 }
      }
    },
    card4DetailBtn: {
      desktop: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      tablet: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      mobile: {
        text: 'Xem chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      }
    },
    card4ConsultBtn: {
      desktop: {
        text: 'Tư vấn',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      tablet: {
        text: 'Tư vấn',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      mobile: {
        text: 'Tư vấn',
        fontSize: 14,
        fontWeight: 600,
        color: '#ffffff',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      }
    }
  },
  rules: {
    canDrag: () => true,
    canMoveIn: () => false
  }
}
