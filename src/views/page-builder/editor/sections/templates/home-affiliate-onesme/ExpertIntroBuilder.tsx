'use client'

import { Element, useNode, type UserComponent } from '@craftjs/core'

import { Container, Column, Image, Row, Spacer, Text } from '@/components/page-builder/selectors'
import { defaultResponseColumnProps, defaultRowProps } from '@/constants/page-builder/row'

export const ExpertIntroBuilder: UserComponent = props => {
  const {
    connectors: { connect, drag }
  } = useNode()

  return (
    <div
      className='w-full'
      ref={(ref: HTMLDivElement | null) => {
        if (ref) {
          connect(drag(ref))
        }
      }}
    >
      <Element id='container' canvas is={Container} {...props.container}>
        <Element is={Row} id='intro-row' {...props.introRow}>
          <Element is={Column} id='image-column' canvas {...props.imageColumn}>
            <Element is={Image} id='expert-image' canvas {...props.expertImage} />
          </Element>
          <Element is={Column} id='content-column' canvas {...props.contentColumn}>
            <Element is={Text} id='intro-title' canvas {...props.introTitle} />
            <Element is={Spacer} id='spacer1' canvas {...props.spacer1} />
            <Element is={Text} id='intro-text1' canvas {...props.introText1} />
          </Element>
        </Element>
      </Element>
    </div>
  )
}

ExpertIntroBuilder.craft = {
  props: {
    container: {
      height: 'auto',
      background: '#FFFFFF',
      padding: ['0', '0', '0', '0']
    },
    introRow: {
      desktop: {
        ...defaultRowProps('desktop'),
        backgroundColor: '#ffffff',
        backgroundType: 'COLOR',
        padding: { top: 48, bottom: 48, left: 16, right: 16 },
        colWidths: [4, 8],
        gap: 8,
        height: 'auto',
        alignItems: 'center',
        align: 'center'
      },
      tablet: {
        ...defaultRowProps('tablet'),
        backgroundColor: '#ffffff',
        backgroundType: 'COLOR',
        padding: { top: 48, bottom: 48, left: 16, right: 16 },
        colWidths: [4, 8],
        gap: 8,
        height: 'auto',
        alignItems: 'center'
      },
      mobile: {
        ...defaultRowProps('mobile'),
        backgroundColor: '#ffffff',
        backgroundType: 'COLOR',
        padding: { top: 32, bottom: 32, left: 16, right: 16 },
        // isBreakLine: true,
        gap: 8,
        height: 'auto',
        alignItems: 'center'
      }
    },
    imageColumn: {
      desktop: { ...defaultResponseColumnProps.desktop, contentAlign: 'center' },
      tablet: { ...defaultResponseColumnProps.tablet, contentAlign: 'center' },
      mobile: { ...defaultResponseColumnProps.mobile, contentAlign: 'center' }
    },
    contentColumn: {
      desktop: { ...defaultResponseColumnProps.desktop, contentAlign: 'start' },
      tablet: { ...defaultResponseColumnProps.tablet, contentAlign: 'start' },
      mobile: { ...defaultResponseColumnProps.mobile, contentAlign: 'start' }
    },
    expertImage: {
      desktop: {
        imgSrc: '	https://onesme.vn/_next/static/media/personal_card.51bf21f6.webp',
        width: 300,
        height: 400,
        imageFit: 'fill',
        border: {
          style: 'solid',
          color: '#212D6E',
          thickness: 0,
          radius: 8
        },
        align: 'center',
        typeUpload: 'UPLOAD'
      },
      tablet: {
        imgSrc: '	https://onesme.vn/_next/static/media/personal_card.51bf21f6.webp',
        width: 250,
        height: 330,
        imageFit: 'fill',
        border: {
          style: 'solid',
          color: '#212D6E',
          thickness: 0,
          radius: 8
        },
        align: 'center',
        typeUpload: 'UPLOAD'
      },
      mobile: {
        imgSrcMobile: '	https://onesme.vn/_next/static/media/personal_card.51bf21f6.webp',
        width: 200,
        height: 260,
        imageFit: 'fill',
        border: {
          style: 'solid',
          color: '#212D6E',
          thickness: 0,
          radius: 8
        },
        align: 'center',
        typeUpload: 'UPLOAD'
      }
    },
    introTitle: {
      desktop: {
        text: 'Về chuyên gia Nguyễn Văn An',
        fontSize: 32,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'left'
      },
      tablet: {
        text: 'Về chuyên gia Nguyễn Văn An',
        fontSize: 28,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'left'
      },
      mobile: {
        text: 'Về chuyên gia Nguyễn Văn An',
        fontSize: 24,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'center'
      }
    },
    introText1: {
      desktop: {
        text: 'Với hơn 5 năm kinh nghiệm trong ngành, tôi tự tin sẽ mang đến cho bạn những giải pháp số phù hợp nhất, giúp tối ưu hóa chi phí và nâng cao hiệu quả hoạt động cho doanh nghiệp. Sự hài lòng của bạn là ưu tiên hàng đầu của tôi.',
        fontSize: 16,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'left'
      },
      tablet: {
        text: 'Với hơn 5 năm kinh nghiệm trong ngành, tôi tự tin sẽ mang đến cho bạn những giải pháp số phù hợp nhất, giúp tối ưu hóa chi phí và nâng cao hiệu quả hoạt động cho doanh nghiệp. Sự hài lòng của bạn là ưu tiên hàng đầu của tôi.',
        fontSize: 16,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'left'
      },
      mobile: {
        text: 'Với hơn 5 năm kinh nghiệm trong ngành, tôi tự tin sẽ mang đến cho bạn những giải pháp số phù hợp nhất, giúp tối ưu hóa chi phí và nâng cao hiệu quả hoạt động cho doanh nghiệp. Sự hài lòng của bạn là ưu tiên hàng đầu của tôi.',
        fontSize: 14,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'center'
      }
    },
    spacer1: {
      desktop: { height: 16 },
      tablet: { height: 16 },
      mobile: { height: 16 }
    },
    spacer2: {
      desktop: { height: 16 },
      tablet: { height: 16 },
      mobile: { height: 16 }
    }
  },
  rules: {
    canDrag: () => true,
    canMoveIn: () => false
  }
}
