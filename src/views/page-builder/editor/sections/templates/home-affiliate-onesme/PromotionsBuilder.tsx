'use client'

import { Element, useNode, type UserComponent } from '@craftjs/core'

import { Image, Text, Row, Column, Button } from '@/components/page-builder/selectors'
import { defaultResponseColumnProps, defaultRowProps } from '@/constants/page-builder/row'
import { defaultImageProps } from '@/constants/page-builder'

// region Dữ liệu các khuyến mãi có thể tái sử dụng
const PROMOTIONS_DATA = [
  {
    id: 'promo1',
    title: 'Mua 6 Tặng 2',
    description: 'Đăng ký <PERSON> 6 tháng, nhận ngay 2 tháng cước miễn phí.',
    image: 'https://placehold.co/600x300/E3F2FD/0277BD?text=Mua+6+Tặng+2',
    appliesText: 'Áp dụng: Khách hàng mới.',
    timeText: 'Thời gian: 01/07 - 31/07/2025.'
  },
  {
    id: 'promo2',
    title: '<PERSON><PERSON>p Internet Tặng Camera',
    description: 'Trang bị miễn phí camera giám sát an toàn cho gia đình bạn.',
    image: 'https://placehold.co/600x300/FFF3E0/E65100?text=Tặng+Camera',
    appliesText: 'Áp dụng: Gói Home Mesh 3+.',
    timeText: 'Thời gian: 01/07 - 31/07/2025.'
  },
  {
    id: 'promo3',
    title: 'Mua 10G Tặng 4G',
    description: 'Ưu đãi data cực khủng dành cho thuê bao di động trả trước.',
    image: 'https://placehold.co/600x300/E8F5E9/1B5E20?text=Tặng+Data',
    appliesText: 'Áp dụng: Nạp thẻ từ 100K.',
    timeText: 'Thời gian: Mỗi cuối tuần.'
  }
]
//endregion

// Hàm hỗ trợ tạo props responsive
const createResponsiveProps = (desktopProps: any, tabletProps?: any, mobileProps?: any) => ({
  desktop: desktopProps,
  tablet: tabletProps || desktopProps,
  mobile: mobileProps || tabletProps || desktopProps
})

// region Hàm hỗ trợ tạo props cho button
const createButtonProps = (type: 'detail' | 'apply') => {
  if (type === 'detail') {
    return createResponsiveProps(
      {
        text: 'Xem chi tiết',
        size: 'middle',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        color: '#374151',
        fontSize: 14,
        fontWeight: 600,
        padding: { top: 8, right: 16, bottom: 8, left: 16 },
        margin: { top: 0, right: 4, bottom: 0, left: 0 },
        radius: 8,
        width: '100%'
      },
      {
        text: 'Xem chi tiết',
        size: 'middle',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        color: '#374151',
        fontSize: 14,
        fontWeight: 600,
        padding: { top: 8, right: 16, bottom: 8, left: 16 },
        margin: { top: 0, right: 4, bottom: 0, left: 0 },
        radius: 8,
        width: '100%'
      },
      {
        text: 'Xem chi tiết',
        size: 'small',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        color: '#374151',
        fontSize: 12,
        fontWeight: 600,
        padding: { top: 6, right: 12, bottom: 6, left: 12 },
        margin: { top: 0, right: 4, bottom: 0, left: 0 },
        radius: 8,
        width: '100%'
      }
    )
  } else {
    return createResponsiveProps(
      {
        text: 'Áp dụng',
        size: 'middle',
        buttonType: 'primary',
        buttonBackgroundColor: '#f97316',
        color: '#ffffff',
        fontSize: 14,
        fontWeight: 600,
        padding: { top: 8, right: 16, bottom: 8, left: 16 },
        margin: { top: 0, right: 0, bottom: 0, left: 0 },
        radius: 8,
        width: '100%'
      },
      {
        text: 'Áp dụng',
        size: 'middle',
        buttonType: 'primary',
        buttonBackgroundColor: '#f97316',
        color: '#ffffff',
        fontSize: 14,
        fontWeight: 600,
        padding: { top: 8, right: 16, bottom: 8, left: 16 },
        margin: { top: 0, right: 0, bottom: 0, left: 0 },
        radius: 8,
        width: '100%'
      },
      {
        text: 'Áp dụng',
        size: 'small',
        buttonType: 'primary',
        buttonBackgroundColor: '#f97316',
        color: '#ffffff',
        fontSize: 12,
        fontWeight: 600,
        padding: { top: 6, right: 12, bottom: 6, left: 12 },
        margin: { top: 0, right: 0, bottom: 0, left: 0 },
        radius: 8,
        width: '100%'
      }
    )
  }
}
//endregion

// Tạo craft props một cách động
const generateCraftProps = () => {
  const props: any = {
    // region Props cho row tiêu đề
    titleRow: {
      desktop: {
        ...defaultRowProps('desktop'),
        backgroundColor: '#eff6ff',
        backgroundType: 'COLOR',
        padding: { top: 64, bottom: 40, left: 16, right: 16 },
        height: 'auto',
        justifyContent: 'center'
      },
      tablet: {
        ...defaultRowProps('tablet'),
        backgroundColor: '#eff6ff',
        backgroundType: 'COLOR',
        padding: { top: 64, bottom: 40, left: 16, right: 16 },
        height: 'auto',
        justifyContent: 'center'
      },
      mobile: {
        ...defaultRowProps('mobile'),
        backgroundColor: '#eff6ff',
        backgroundType: 'COLOR',
        padding: { top: 32, bottom: 24, left: 0, right: 0 },
        height: 'auto',
        justifyContent: 'center'
      }
    },
    titleColumn: {
      desktop: { ...defaultResponseColumnProps.desktop, contentAlign: 'center' },
      tablet: { ...defaultResponseColumnProps.tablet, contentAlign: 'center' },
      mobile: { ...defaultResponseColumnProps.mobile, contentAlign: 'center' }
    },
    // Props cho section title
    sectionTitle: createResponsiveProps(
      {
        text: 'Ưu đãi tháng 7',
        fontSize: 32,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'center',
        margin: { top: 0, bottom: 0 }
      },
      {
        text: 'Ưu đãi tháng 7',
        fontSize: 28,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'center',
        margin: { top: 0, bottom: 0 }
      },
      {
        text: 'Ưu đãi tháng 7',
        fontSize: 24,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'center',
        margin: { top: 0, bottom: 0 }
      }
    ),
    //endregion

    // region Props cho row các promotion
    promotionsRow: createResponsiveProps(
      {
        ...defaultRowProps('desktop'),
        colWidths: [4, 4, 4],
        gap: 6,
        backgroundColor: '#eff6ff',
        backgroundType: 'COLOR',
        padding: { top: 0, right: 0, bottom: 64, left: 0 },
        margin: { top: 0, bottom: 0 },
        contentAlign: 'start',
        justifyContent: 'start'
      },
      {
        ...defaultRowProps('tablet'),
        colWidths: [6, 6],
        gap: 6,
        backgroundColor: '#eff6ff',
        backgroundType: 'COLOR',
        padding: { top: 0, right: 0, bottom: 64, left: 0 },
        margin: { top: 0, bottom: 0 },
        contentAlign: 'start',
        justifyContent: 'start'
      },
      {
        ...defaultRowProps('mobile'),
        colWidths: [12],
        gap: 6,
        backgroundColor: '#eff6ff',
        backgroundType: 'COLOR',
        padding: { top: 0, right: 16, bottom: 32, left: 16 },
        margin: { top: 0, bottom: 0 },
        contentAlign: 'start',
        justifyContent: 'start'
      }
    ),
    //Props cho content promotion
    contentRow: {
      desktop: {
        ...defaultRowProps('desktop'),
        padding: { top: 16, right: 16, bottom: 16, left: 16 },
        backgroundColor: '#FFFFFF',
        backgroundType: 'COLOR',
        height: 'auto',
        justifyContent: 'center'
      },
      tablet: {
        ...defaultRowProps('tablet'),
        padding: { top: 16, right: 16, bottom: 16, left: 16 },
        backgroundColor: '#FFFFFF',
        backgroundType: 'COLOR',
        height: 'auto',
        justifyContent: 'center'
      },
      mobile: {
        ...defaultRowProps('mobile'),
        padding: { top: 16, right: 16, bottom: 16, left: 16 },
        backgroundColor: '#FFFFFF',
        backgroundType: 'COLOR',
        height: 'auto',
        justifyContent: 'center'
      }
    },
    contentColumn: {
      desktop: { ...defaultResponseColumnProps.desktop, contentAlign: 'start' },
      tablet: { ...defaultResponseColumnProps.tablet, contentAlign: 'start' },
      mobile: { ...defaultResponseColumnProps.mobile, contentAlign: 'start' }
    },
    //Props cho content promotion
    buttonRow: {
      desktop: {
        ...defaultRowProps('desktop'),
        colWidths: [6, 6],
        gap: 2,
        margin: { top: 16, bottom: 0, left: 0, right: 0 }
      },
      tablet: {
        ...defaultRowProps('tablet'),
        colWidths: [6, 6],
        gap: 2,
        margin: { top: 16, bottom: 0, left: 0, right: 0 }
      },
      mobile: {
        ...defaultRowProps('mobile'),
        colWidths: [6, 6],
        gap: 2,
        margin: { top: 16, bottom: 0, left: 0, right: 0 }
      }
    },
    buttonColumn: {
      desktop: { ...defaultResponseColumnProps.desktop, contentAlign: 'center' },
      tablet: { ...defaultResponseColumnProps.tablet, contentAlign: 'center' },
      mobile: { ...defaultResponseColumnProps.mobile, contentAlign: 'center' }
    },

    // Props cho buttons Xem chi tiết
    promoDetailBtn: createResponsiveProps(
      {
        text: 'Xem chi tiết',
        size: 'middle',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        color: '#374151',
        fontSize: 14,
        fontWeight: 600,
        padding: { top: 8, right: 16, bottom: 8, left: 16 },
        margin: { top: 0, right: 4, bottom: 0, left: 0 },
        radius: 8,
        width: '100%'
      },
      {
        text: 'Xem chi tiết',
        size: 'middle',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        color: '#374151',
        fontSize: 14,
        fontWeight: 600,
        padding: { top: 8, right: 16, bottom: 8, left: 16 },
        margin: { top: 0, right: 4, bottom: 0, left: 0 },
        radius: 8,
        width: '100%'
      },
      {
        text: 'Xem chi tiết',
        size: 'small',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        color: '#374151',
        fontSize: 12,
        fontWeight: 600,
        padding: { top: 6, right: 12, bottom: 6, left: 12 },
        margin: { top: 0, right: 4, bottom: 0, left: 0 },
        radius: 8,
        width: '100%'
      }
    ),
    // Props cho buttons Áp dụng
    promoApplyBtn: createResponsiveProps(
      {
        text: 'Áp dụng',
        size: 'middle',
        buttonType: 'primary',
        buttonBackgroundColor: '#f97316',
        color: '#ffffff',
        fontSize: 14,
        fontWeight: 600,
        padding: { top: 8, right: 16, bottom: 8, left: 16 },
        margin: { top: 0, right: 0, bottom: 0, left: 0 },
        radius: 8,
        width: '100%'
      },
      {
        text: 'Áp dụng',
        size: 'middle',
        buttonType: 'primary',
        buttonBackgroundColor: '#f97316',
        color: '#ffffff',
        fontSize: 14,
        fontWeight: 600,
        padding: { top: 8, right: 16, bottom: 8, left: 16 },
        margin: { top: 0, right: 0, bottom: 0, left: 0 },
        radius: 8,
        width: '100%'
      },
      {
        text: 'Áp dụng',
        size: 'small',
        buttonType: 'primary',
        buttonBackgroundColor: '#f97316',
        color: '#ffffff',
        fontSize: 12,
        fontWeight: 600,
        padding: { top: 6, right: 12, bottom: 6, left: 12 },
        margin: { top: 0, right: 0, bottom: 0, left: 0 },
        radius: 8,
        width: '100%'
      }
    )
    //endregion
  }

  // region Tạo props động cho từng promotion
  PROMOTIONS_DATA.forEach((promo, index) => {
    // Column props
    props[`${promo.id}Column`] = createResponsiveProps(defaultRowProps('desktop'), defaultRowProps('tablet'), {
      ...defaultRowProps('mobile'),
      margin: { top: 0, bottom: index === 2 ? 0 : 12, left: 0, right: 0 }
    })

    // Image props
    props[`${promo.id}Image`] = createResponsiveProps({
      ...defaultImageProps('desktop'),
      imgSrc: promo.image,
      width: '',
      height: 128,
      imageFit: 'fill'
    })

    // Title props
    props[`${promo.id}Title`] = createResponsiveProps(
      {
        text: promo.title,
        fontSize: 18,
        fontWeight: 700,
        color: '#000000',
        textAlign: 'left'
      },
      {
        text: promo.title,
        fontSize: 18,
        fontWeight: 700,
        color: '#000000',
        textAlign: 'left'
      },
      {
        text: promo.title,
        fontSize: 16,
        fontWeight: 700,
        color: '#000000',
        textAlign: 'left'
      }
    )

    // Description props
    props[`${promo.id}Description`] = createResponsiveProps(
      {
        text: promo.description,
        fontSize: 14,
        fontWeight: 400,
        color: '#4B5563',
        textAlign: 'left',
        className: 'text-sm text-gray-600 my-2 flex-grow',
        margin: { top: 8, bottom: 8 }
      },
      {
        text: promo.description,
        fontSize: 14,
        fontWeight: 400,
        color: '#4B5563',
        textAlign: 'left',
        className: 'text-sm text-gray-600 my-2 flex-grow',
        margin: { top: 8, bottom: 8 }
      },
      {
        text: promo.description,
        fontSize: 12,
        fontWeight: 400,
        color: '#4B5563',
        textAlign: 'left',
        className: 'text-sm text-gray-600 my-2 flex-grow',
        margin: { top: 8, bottom: 8 }
      }
    )

    // Applies text props
    props[`${promo.id}AppliesText`] = createResponsiveProps(
      {
        text: promo.appliesText,
        fontSize: 12,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'left',
        hasIcon: true,
        icon: 'CheckCircleFilled',
        iconAlign: 'start',
        iconColor: '#10b981',
        iconSize: 12
      },
      {
        text: promo.appliesText,
        fontSize: 12,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'left',
        hasIcon: true,
        icon: 'CheckCircleFilled',
        iconAlign: 'start',
        iconColor: '#10b981',
        iconSize: 12
      },
      {
        text: promo.appliesText,
        fontSize: 10,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'left',
        hasIcon: true,
        icon: 'CheckCircleFilled',
        iconAlign: 'start',
        iconColor: '#10b981',
        iconSize: 12
      }
    )

    // Time text props
    props[`${promo.id}TimeText`] = createResponsiveProps(
      {
        text: promo.timeText,
        fontSize: 12,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'left',
        hasIcon: true,
        icon: 'CalendarFilled',
        iconAlign: 'start',
        iconColor: '#10b981',
        iconSize: 12
      },
      {
        text: promo.timeText,
        fontSize: 12,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'left',
        hasIcon: true,
        icon: 'CalendarFilled',
        iconAlign: 'start',
        iconColor: '#10b981',
        iconSize: 12
      },
      {
        text: index === 1 ? `✓ ${promo.timeText}` : promo.timeText,
        fontSize: 10,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'left',
        hasIcon: true,
        icon: 'CalendarFilled',
        iconAlign: 'start',
        iconColor: '#10b981',
        iconSize: 12
      }
    )

    // Button props riêng cho promo2 và promo3
    if (index >= 1) {
      props[`${promo.id}DetailBtn`] = createButtonProps('detail')
      props[`${promo.id}ApplyBtn`] = createButtonProps('apply')
    }
  })
  //endregion

  return props
}

// region return PromotionsBuilder
export const PromotionsBuilder: UserComponent = props => {
  const {
    connectors: { connect, drag }
  } = useNode()

  return (
    <div
      className='w-full'
      ref={(ref: HTMLDivElement | null) => {
        if (ref) {
          connect(drag(ref))
        }
      }}
    >
      <div>
        <Element is={Row} id='title-row' canvas {...props.titleRow}>
          <Element is={Column} id='title-column' canvas {...props.titleColumn}>
            <Element is={Text} id='section-title' canvas {...props.sectionTitle} />
          </Element>
        </Element>
        <Element is={Row} id='promotions-row' canvas {...props.promotionsRow}>
          {PROMOTIONS_DATA.map((promo, index) => (
            <Element key={promo.id} is={Column} id={`${promo.id}-column`} canvas {...props[`${promo.id}Column`]}>
              <div className='flex flex-col overflow-hidden rounded-lg bg-white shadow-lg'>
                <Element is={Image} id={`${promo.id}-image`} canvas {...props[`${promo.id}Image`]} />
                <Element is={Row} id='title-row' canvas {...props.contentRow}>
                  <Element is={Column} id='title-column' canvas {...props.contentColumn}>
                    <Element is={Text} id={`${promo.id}-title`} canvas {...props[`${promo.id}Title`]} />
                    <Element is={Text} id={`${promo.id}-description`} canvas {...props[`${promo.id}Description`]} />
                    <Element is={Text} id={`${promo.id}-applies-text`} canvas {...props[`${promo.id}AppliesText`]} />
                    <Element is={Text} id={`${promo.id}-time-text`} canvas {...props[`${promo.id}TimeText`]} />
                    <Element is={Row} id='title-row' canvas {...props.buttonRow}>
                      <Element is={Column} id='title-column' canvas {...props.buttonColumn}>
                        <Element
                          is={Button}
                          id={`${promo.id}-detail-btn`}
                          canvas
                          {...(index === 0 ? props.promoDetailBtn : props[`${promo.id}DetailBtn`])}
                        />
                      </Element>
                      <Element is={Column} id='title-column' canvas {...props.titleColumn}>
                        <Element
                          is={Button}
                          id={`${promo.id}-apply-btn`}
                          canvas
                          {...(index === 0 ? props.promoApplyBtn : props[`${promo.id}ApplyBtn`])}
                        />
                      </Element>
                    </Element>
                  </Element>
                </Element>
              </div>
            </Element>
          ))}
        </Element>
      </div>
    </div>
  )
}
//endregion

PromotionsBuilder.craft = {
  props: generateCraftProps(),
  rules: {
    canDrag: () => true,
    canMoveIn: () => false
  }
}
