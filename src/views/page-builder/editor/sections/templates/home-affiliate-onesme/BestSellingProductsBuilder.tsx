'use client'

import { Element, useNode, type UserComponent } from '@craftjs/core'

import { Row, Column, Text, Image, Button, IconLibrary } from '@/components/page-builder/selectors'
import { ProductCard } from '@/components/page-builder/editor/CustomComponents/IoTTemplate/ProductCard'
import { defaultResponseColumnProps, defaultRowProps } from '@/constants/page-builder/row'
import { defaultIconLibsProps } from '@/constants/page-builder/iconLibs'

// Dữ liệu các sản phẩm có thể tái sử dụng
const PRODUCTS_DATA = [
  {
    id: 1,
    title: 'D169G',
    description: 'Data khủng, gọi thả ga',
    promotionTitle: 'KM: Giảm 30% (còn 118K) khi đăng ký vào cuối tuần.',
    promotionTitleColor: '#f7941e',
    promotionDescription: 'Áp dụng: 01/07 - 31/07/2025',
    promotionDescriptionColor: '#6B7280',
    promotionBackground: '#fff7ed',
    price: '169.000đ<span style="font-size: 16px; font-weight: 400;">/tháng</span>',
    image: 'https://placehold.co/600x400/e2e8f0/334155?text=D169G',
    buttons: ['Xem chi tiết', 'Mua ngay'],
    buttonTypes: ['detail', 'buy']
  },
  {
    id: 2,
    title: 'Home Mesh 2+',
    description: 'Internet & TV cho gia đình',
    promotionTitle: 'KM: Tặng thêm 2 tháng khi đóng trước 6 tháng.',
    promotionTitleColor: '#16A34A',
    promotionDescription: 'Áp dụng: 01/07 - 31/07/2025',
    promotionDescriptionColor: '#6B7280',
    promotionBackground: '#f0fdf4',
    price: '120.000đ<span style="font-size: 16px; font-weight: 400;">/tháng</span>',
    image: 'https://placehold.co/600x400/e2e8f0/334155?text=Home+Mesh+2',
    buttons: ['Xem chi tiết', 'Mua ngay'],
    buttonTypes: ['detail', 'consult']
  },
  {
    id: 3,
    title: 'Hóa đơn điện tử',
    description: 'Giải pháp hóa đơn cho DN',
    promotionTitle: 'KM: Giảm 20% phí khởi tạo.',
    promotionTitleColor: '#2563EB',
    promotionDescription: 'Áp dụng: 01/07 - 31/07/2025',
    promotionDescriptionColor: '#6B7280',
    promotionBackground: '#eff6ff',
    price: 'Liên hệ',
    image: 'https://placehold.co/600x400/e2e8f0/334155?text=H%C3%B3a+%C4%91%C6%A1n+%C4%91i%E1%BB%87n+t%E1%BB%AD',
    buttons: ['Xem chi tiết', 'Tư vấn'],
    buttonTypes: ['detail', 'consult']
  },
  {
    id: 4,
    title: 'VNPT SmartHome',
    description: 'Nhà thông minh trong tầm tay',
    promotionTitle: '\u00A0', // Non-breaking space
    promotionTitleColor: '',
    promotionDescription: '\u00A0', // Non-breaking space
    promotionDescriptionColor: '',
    promotionBackground: '',
    price: '120.000đ<span style="font-size: 16px; font-weight: 400;">/tháng</span>',
    image: 'https://placehold.co/600x400/e2e8f0/334155?text=Smart+Home',
    buttons: ['Xem chi tiết', 'Tư vấn'],
    buttonTypes: ['detail', 'consult']
  }
]

// Hàm hỗ trợ tạo props responsive
const createResponsiveProps = (desktopProps: any, tabletOverrides = {}, mobileOverrides = {}) => ({
  desktop: desktopProps,
  tablet: { ...desktopProps, ...tabletOverrides },
  mobile: { ...desktopProps, ...tabletOverrides, ...mobileOverrides }
})

// Hàm hỗ trợ tạo props cho button
const createButtonProps = (text: string, type: 'detail' | 'buy' | 'consult') => {
  const baseProps = {
    text,
    fontSize: 14,
    fontWeight: 600,
    radius: 8,
    padding: { top: 8, bottom: 8, left: 16, right: 16 },
    size: 'middle',
    width: '100%',
    buttonType: 'primary'
  }

  if (type === 'detail') {
    return {
      ...baseProps,
      color: '#374151',
      buttonBackgroundColor: '#e5e7eb'
    }
  }

  return {
    ...baseProps,
    color: '#ffffff',
    buttonBackgroundColor: '#00529c'
  }
}

// Tạo craft props một cách động
const generateCraftProps = () => {
  const baseProps: any = {
    // region Props cho row tiêu đề
    titleRow: {
      desktop: {
        ...defaultRowProps('desktop'),
        backgroundColor: '#f8f9fa',
        backgroundType: 'COLOR',
        padding: { top: 64, bottom: 40, left: 16, right: 16 },
        height: 'auto',
        justifyContent: 'center'
      },
      tablet: {
        ...defaultRowProps('tablet'),
        backgroundColor: '#f8f9fa',
        backgroundType: 'COLOR',
        padding: { top: 64, bottom: 40, left: 16, right: 16 },
        height: 'auto',
        justifyContent: 'center'
      },
      mobile: {
        ...defaultRowProps('mobile'),
        backgroundColor: '#f8f9fa',
        backgroundType: 'COLOR',
        padding: { top: 48, bottom: 32, left: 16, right: 16 },
        height: 'auto',
        justifyContent: 'center'
      }
    },
    titleColumn: {
      desktop: { ...defaultResponseColumnProps.desktop, contentAlign: 'center' },
      tablet: { ...defaultResponseColumnProps.tablet, contentAlign: 'center' },
      mobile: { ...defaultResponseColumnProps.mobile, contentAlign: 'center' }
    },
    //Props cho content promotion
    contentRow: {
      desktop: {
        ...defaultRowProps('desktop'),
        padding: { top: 16, right: 16, bottom: 16, left: 16 },
        backgroundColor: '#FFFFFF',
        backgroundType: 'COLOR',
        height: 'auto',
        justifyContent: 'center'
      },
      tablet: {
        ...defaultRowProps('tablet'),
        padding: { top: 16, right: 16, bottom: 16, left: 16 },
        backgroundColor: '#FFFFFF',
        backgroundType: 'COLOR',
        height: 'auto',
        justifyContent: 'center'
      },
      mobile: {
        ...defaultRowProps('mobile'),
        padding: { top: 16, right: 16, bottom: 16, left: 16 },
        backgroundColor: '#FFFFFF',
        backgroundType: 'COLOR',
        height: 'auto',
        justifyContent: 'center'
      }
    },
    contentColumn: {
      desktop: { ...defaultResponseColumnProps.desktop, contentAlign: 'start' },
      tablet: { ...defaultResponseColumnProps.tablet, contentAlign: 'start' },
      mobile: { ...defaultResponseColumnProps.mobile, contentAlign: 'start' }
    },
    // Title props
    productsTitle: createResponsiveProps(
      {
        text: 'Sản phẩm bán chạy',
        fontSize: 32,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'center'
      },
      { fontSize: 28 },
      { fontSize: 24 }
    ),

    // Cards row props
    productsCardsRow: createResponsiveProps(
      {
        ...defaultRowProps('desktop'),
        colWidths: [3, 3, 3, 3],
        gap: 6,
        backgroundColor: '#f8f9fa',
        backgroundType: 'COLOR',
        padding: { top: 0, right: 0, bottom: 64, left: 0 },
        contentAlign: 'start',
        justifyContent: 'start'
      },
      {
        colWidths: [6, 6],
        isBreakLine: true
      },
      {
        colWidths: [12],
        isBreakLine: true
      }
    ),

    // Product column wrapper props
    productColumnWrapper: createResponsiveProps(defaultRowProps('desktop'), defaultRowProps('tablet'), {
      ...defaultRowProps('mobile'),
      margin: { top: 0, bottom: 16, left: 0, right: 0 }
    }),

    // Product card props
    productCard: createResponsiveProps({
      backgroundColor: '#ffffff',
      borderRadius: 8,
      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
      hoverTransform: 'translateY(-5px)',
      hoverBoxShadow: '0 10px 25px rgba(0,0,0,0.1)',
      transition: 'transform 0.3s ease, box-shadow 0.3s ease',
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      minHeight: 400
    })
  }

  // Tạo props cho từng sản phẩm
  PRODUCTS_DATA.forEach(product => {
    // Image props
    baseProps[`card${product.id}Image`] = createResponsiveProps({
      imgSrc: product.image,
      width: '100%',
      height: 160,
      border: { style: 'solid', thickness: 0, color: '#000000', radius: 8 },
      imageFit: 'fill'
    })

    // Title props
    baseProps[`card${product.id}Title`] = createResponsiveProps({
      text: product.title,
      fontSize: 18,
      fontWeight: 700,
      color: '#00529c',
      textAlign: 'left'
    })

    // Description props
    baseProps[`card${product.id}Description`] = createResponsiveProps({
      text: product.description,
      fontSize: 14,
      fontWeight: 400,
      color: '#6b7280',
      textAlign: 'left',
      margin: { top: 4, bottom: 0, left: 0, right: 0 }
    })

    baseProps[`card${product.id}Promotion`] = createResponsiveProps({
      ...defaultIconLibsProps('desktop'),
      width: '100%',
      showIcon: {
        desktop: false,
        tablet: false,
        mobile: false
      },
      title: product.promotionTitle,
      titleFontSize: 12,
      titleFontWeight: 700,
      titleLineHeight: '',
      titleColor: product.promotionTitleColor,
      desc: product.promotionDescription,
      descFontSize: 12,
      descFontWeight: 400,
      descColor: product.promotionDescriptionColor,
      backgroundColor: product.promotionBackground,
      backgroundType: 'COLOR',
      border: {
        style: 'solid',
        color: '#E6F5FA',
        thickness: 0,
        radius: 4
      },
      contentPadding: { top: 8, bottom: 8, left: 8, right: 8 },
      margin: { top: 8, bottom: 8, left: 0, right: 0 }
    })

    // Price hoặc Contact props
    baseProps[`card${product.id}Price`] = createResponsiveProps({
      text: product.price,
      fontSize: 24,
      fontWeight: 700,
      color: '#00529c',
      textAlign: 'left',
      margin: { top: 16, bottom: 0, left: 0, right: 0 }
    })

    // Buttons row props
    baseProps[`card${product.id}Buttons`] = createResponsiveProps({
      ...defaultRowProps('desktop'),
      colWidths: [6, 6],
      gap: 2,
      height: 'auto',
      alignItems: 'stretch',
      justifyContent: 'space-between',
      width: '100%',
      margin: { top: 8, bottom: 0, left: 0, right: 0 }
    })

    // Detail button props
    baseProps[`card${product.id}DetailBtn`] = createResponsiveProps(createButtonProps(product.buttons[0], 'detail'))

    // Second button props (Buy hoặc Consult)
    const secondButtonType = product.buttonTypes[1] as 'buy' | 'consult'
    const secondButtonKey = secondButtonType === 'buy' ? 'Buy' : 'Consult'

    baseProps[`card${product.id}${secondButtonKey}Btn`] = createResponsiveProps(
      createButtonProps(product.buttons[1], secondButtonType)
    )
  })

  return baseProps
}

export const BestSellingProductsBuilder: UserComponent = props => {
  const {
    connectors: { connect, drag }
  } = useNode()

  return (
    <div
      className='w-full'
      ref={(ref: HTMLDivElement | null) => {
        if (ref) {
          connect(drag(ref))
        }
      }}
    >
      <div>
        <Element is={Row} id='title-row' canvas {...props.titleRow}>
          <Element is={Column} id='title-column' canvas {...props.titleColumn}>
            <Element is={Text} id='section-title' canvas {...props.productsTitle} />
          </Element>
        </Element>
        <Element is={Row} id='products-cards-row' canvas {...props.productsCardsRow}>
          {PRODUCTS_DATA.map(product => (
            <Element
              key={product.id}
              is={Column}
              id={`product-card-${product.id}`}
              canvas
              {...props.productColumnWrapper}
            >
              <Element is={ProductCard} id={`product${product.id}`} canvas {...props.productCard}>
                <div className='rounded-t-lg bg-[#e2e8f0]'>
                  <Element is={Image} id={`card-${product.id}-image`} canvas {...props[`card${product.id}Image`]} />
                </div>
                <Element is={Row} id='title-row' canvas {...props.contentRow}>
                  <Element is={Column} id='title-column' canvas {...props.contentColumn}>
                    {/**
                     * Nhóm phần nội dung phía trên (tiêu đề/mô tả/khuyến mãi)
                     * đặt min-height cố định để bảo đảm baseline của Giá + CTA luôn thẳng hàng
                     */}
                    <div className='flex min-h-[140px] w-full flex-col'>
                      <Element is={Text} id={`card-${product.id}-title`} canvas {...props[`card${product.id}Title`]} />
                      <Element
                        is={Text}
                        id={`card-${product.id}-description`}
                        canvas
                        {...props[`card${product.id}Description`]}
                      />
                      <Element
                        is={IconLibrary}
                        id={`card-${product.id}-promotion`}
                        canvas
                        {...props[`card${product.id}Promotion`]}
                      />
                    </div>

                    {/** Cụm Giá + CTA nằm cuối card và cùng baseline giữa các card */}
                    <div className='mt-auto w-full'>
                      <Element is={Text} id={`card-${product.id}-price`} canvas {...props[`card${product.id}Price`]} />
                      <Element is={Row} id={`card-${product.id}-buttons`} canvas {...props[`card${product.id}Buttons`]}>
                        <Element
                          is={Button}
                          id={`card-${product.id}-detail-btn`}
                          canvas
                          {...props[`card${product.id}DetailBtn`]}
                        />
                        <Element
                          is={Button}
                          id={`card-${product.id}-${product.buttonTypes[1]}-btn`}
                          canvas
                          {...props[`card${product.id}${product.buttonTypes[1] === 'buy' ? 'Buy' : 'Consult'}Btn`]}
                        />
                      </Element>
                    </div>
                  </Element>
                </Element>
              </Element>
            </Element>
          ))}
        </Element>
      </div>
    </div>
  )
}

BestSellingProductsBuilder.craft = {
  props: generateCraftProps(),
  rules: {
    canDrag: () => true,
    canMoveIn: () => false
  }
}
