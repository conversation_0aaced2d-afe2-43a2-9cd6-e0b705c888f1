'use client'

import { Element, useNode, type UserComponent } from '@craftjs/core'

import { Button, Column, Image, Row, Text } from '@/components/page-builder/selectors'
import { ProductCard } from '@/components/page-builder/editor/CustomComponents/IoTTemplate'
import { defaultResponseColumnProps, defaultRowProps } from '@/constants/page-builder/row'

// Định nghĩa kiểu dữ liệu cho sản phẩm
interface ProductData {
  id: string
  title: string
  image: string
  features: string[]
  price: string
}

// Kiểu loại sản phẩm
type ProductType = 'cellphone' | 'internet'

// Dữ liệu sản phẩm di động
const cellphoneData: ProductData[] = [
  {
    id: 'product1',
    title: 'D169G',
    image: 'https://placehold.co/600x400/E3F2FD/0277BD?text=D169G',
    features: ['7GB/ngày (210GB/tháng)', '<PERSON>ễ<PERSON> phí 2000 phút nội mạng', 'Miễn phí 150 phút ngoại mạng'],
    price: '169.000đ/tháng'
  },
  {
    id: 'product2',
    title: 'BIG120',
    image: 'https://placehold.co/600x400/E3F2FD/0277BD?text=BIG120',
    features: ['2GB/ngày (60GB/tháng)', 'Miễn phí data xem MyTV'],
    price: '120.000đ/tháng'
  },
  {
    id: 'product3',
    title: 'D15G',
    image: 'https://placehold.co/600x400/E3F2FD/0277BD?text=D15G',
    features: ['500MB/ngày (15GB/tháng)', 'Gói cước siêu tiết kiệm'],
    price: '70.000đ/tháng'
  },
  {
    id: 'product4',
    title: 'SIM Số Đẹp',
    image: 'https://placehold.co/600x400/E3F2FD/0277BD?text=SIM+S%E1%BB%90',
    features: ['Kho sim hàng triệu số', 'Chọn số theo yêu cầu', 'Hỗ trợ đăng ký chính chủ'],
    price: 'Liên hệ'
  }
]

// Dữ liệu sản phẩm Internet & TV
const internetTvData: ProductData[] = [
  {
    id: 'internet1',
    title: 'Home Mesh 2+',
    image: 'https://placehold.co/600x400/FFF3E0/E65100?text=Home+Mesh+2',
    features: ['Tốc độ Internet 150Mbps', 'Truyền hình MyTV Nâng cao', 'Trang bị 01 Wifi Mesh'],
    price: '169.000đ/tháng'
  },
  {
    id: 'internet2',
    title: 'Home Net 1',
    image: 'https://placehold.co/600x400/FFF3E0/E65100?text=Home+Net',
    features: ['Tốc độ Internet 100Mbps', 'Phù hợp cho cá nhân, gia đình nhỏ'],
    price: '120.000đ/tháng'
  },
  {
    id: 'internet3',
    title: 'Home Camera',
    image: 'https://placehold.co/600x400/FFF3E0/E65100?text=Home+Camera',
    features: ['Internet tốc độ cao', 'Tặng 01 Camera trong nhà', 'Lưu trữ cloud an toàn'],
    price: '70.000đ/tháng'
  },
  {
    id: 'internet4',
    title: 'Home Combo',
    image: 'https://placehold.co/600x400/FFF3E0/E65100?text=Home+Combo',
    features: ['Internet + TV + Di động', 'Tiết kiệm đến 50%', 'Nhiều lựa chọn gói'],
    price: 'Liên hệ'
  }
]

/**
 * Lấy dữ liệu sản phẩm theo ID và loại
 * @param productId - ID của sản phẩm
 * @param type - Loại sản phẩm (cellphone hoặc internet)
 * @returns Dữ liệu sản phẩm hoặc undefined nếu không tìm thấy
 */
const getProductData = (productId: string, type?: ProductType): ProductData | undefined => {
  const dataSource = type === 'internet' ? internetTvData : cellphoneData

  return dataSource.find(product => product.id === productId)
}

/**
 * Tạo component card sản phẩm
 * @param productId - ID của sản phẩm
 * @param props - Props được truyền từ component cha
 * @param type - Loại sản phẩm (cellphone hoặc internet)
 * @returns JSX Element của product card
 */
const createProductCard = (productId: string, props: any, type?: ProductType) => {
  // Lấy dữ liệu sản phẩm từ data source tương ứng
  const productData = getProductData(productId, type)

  if (!productData) {
    console.warn(`Không tìm thấy dữ liệu cho sản phẩm: ${productId}`)

    return null
  }

  return (
    <Element is={Column} id={productId} canvas {...props.productColumn}>
      <Element is={ProductCard} id={productId} canvas {...props.productCard}>
        {/* Phần header với hình ảnh sản phẩm */}
        <div className='rounded-t-lg bg-[#e3f2fd]'>
          <Element is={Image} id={`${productId}-image`} canvas {...props[`${productId}Image`]} />
        </div>

        {/* Phần nội dung chính của card */}
        <div className='flex flex-1 flex-col p-4'>
          {/* Tiêu đề sản phẩm */}
          <Element is={Text} id={`${productId}-title`} canvas {...props[`${productId}Title`]} />

          {/* Danh sách tính năng */}
          <div className='my-4 flex-1'>
            {productData.features.map((_, index) => (
              <Element
                key={`${productId}-feature${index + 1}`}
                is={Text}
                id={`${productId}-feature${index + 1}`}
                canvas
                {...props[`${productId}Features`][`${productId}Feature${index + 1}`]}
              />
            ))}
          </div>

          {/* Giá sản phẩm */}
          <Element is={Text} id={`${productId}-price`} canvas {...props[`${productId}Price`]} />

          {/* Nhóm nút hành động */}
          <div className='mt-4 flex gap-2'>
            <Element is={Button} id='promo1-detail-btn' canvas {...props.promoDetailBtn} />
            <Element is={Button} id='promo1-apply-btn' canvas {...props.promoApplyBtn} />
          </div>
        </div>
      </Element>
    </Element>
  )
}

/**
 * Component chính hiển thị danh mục sản phẩm
 */
export const ProductCategoriesBuilder: UserComponent = props => {
  const {
    connectors: { connect, drag }
  } = useNode()

  return (
    <div
      className='w-full py-16'
      ref={(ref: HTMLDivElement | null) => {
        if (ref) {
          connect(drag(ref))
        }
      }}
    >
      <div className='container mx-auto sm:px-4'>
        {/* Tiêu đề chính của section */}
        <Element is={Text} id='banner-title' canvas {...props.productCategoryTitle} />

        {/* Section sản phẩm di động */}
        <Element is={Text} id='cellphone-title' canvas {...props.cellphoneTitle} />
        <Element is={Row} id='products-row' canvas {...props.productsRow}>
          {createProductCard('product1', props)}
          {createProductCard('product2', props)}
          {createProductCard('product3', props)}
          {createProductCard('product4', props)}
        </Element>

        {/* Section sản phẩm Internet & TV */}
        <Element is={Text} id='internet-tv-title' canvas {...props.internetTvTitle} />
        <Element is={Row} id='internet-tv-row' canvas {...props.productsRow}>
          {createProductCard('internet1', props, 'internet')}
          {createProductCard('internet2', props, 'internet')}
          {createProductCard('internet3', props, 'internet')}
          {createProductCard('internet4', props, 'internet')}
        </Element>
      </div>
    </div>
  )
}

// Cấu hình styles chung cho các component
const commonStyles = {
  // Styles cho tiêu đề danh mục sản phẩm
  productCategoryTitle: {
    desktop: {
      text: 'Danh mục sản phẩm',
      fontSize: 32,
      fontWeight: 700,
      color: '#00529c',
      textAlign: 'center',
      className: 'section-title',
      margin: { top: 0, bottom: 40 }
    },
    tablet: {
      text: 'Danh mục sản phẩm',
      fontSize: 28,
      fontWeight: 700,
      color: '#00529c',
      textAlign: 'center',
      className: 'section-title',
      margin: { top: 0, bottom: 40 }
    },
    mobile: {
      text: 'Danh mục sản phẩm',
      fontSize: 24,
      fontWeight: 700,
      color: '#00529c',
      textAlign: 'center',
      className: 'section-title',
      margin: { top: 0, bottom: 24 }
    }
  },
  // Styles cho tiêu đề section di động
  cellphoneTitle: {
    desktop: {
      text: '#Di động',
      fontSize: 24,
      fontWeight: 700,
      color: '#1f2937',
      textAlign: 'left',
      margin: { top: 0, bottom: 24 }
    },
    tablet: {
      text: '#Di động',
      fontSize: 22,
      fontWeight: 700,
      color: '#1f2937',
      textAlign: 'left',
      margin: { top: 0, bottom: 20 }
    },
    mobile: {
      text: '#Di động',
      fontSize: 20,
      fontWeight: 700,
      color: '#1f2937',
      textAlign: 'left',
      margin: { top: 0, bottom: 16 }
    }
  },
  // Styles cho tiêu đề section Internet & TV
  internetTvTitle: {
    desktop: {
      text: '#Internet & TV',
      fontSize: 24,
      fontWeight: 700,
      color: '#1f2937',
      textAlign: 'left',
      margin: { top: 0, bottom: 24 }
    },
    tablet: {
      text: '#Internet & TV',
      fontSize: 22,
      fontWeight: 700,
      color: '#1f2937',
      textAlign: 'left',
      margin: { top: 0, bottom: 20 }
    },
    mobile: {
      text: '#Internet & TV',
      fontSize: 20,
      fontWeight: 700,
      color: '#1f2937',
      textAlign: 'left',
      margin: { top: 0, bottom: 16 }
    }
  },
  // Styles cho column chứa sản phẩm
  productColumn: defaultResponseColumnProps,
  // Styles cho card sản phẩm
  productCard: {
    desktop: {
      backgroundColor: '#ffffff',
      borderRadius: 8,
      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
      hoverTransform: 'translateY(-5px)',
      hoverBoxShadow: '0 10px 25px rgba(0,0,0,0.1)',
      transition: 'transform 0.3s ease, box-shadow 0.3s ease',
      display: 'flex',
      flexDirection: 'column',
      height: '100%'
    },
    tablet: {
      backgroundColor: '#ffffff',
      borderRadius: 8,
      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
      hoverTransform: 'translateY(-5px)',
      hoverBoxShadow: '0 10px 25px rgba(0,0,0,0.1)',
      transition: 'transform 0.3s ease, box-shadow 0.3s ease',
      display: 'flex',
      flexDirection: 'column',
      height: '100%'
    },
    mobile: {
      backgroundColor: '#ffffff',
      borderRadius: 8,
      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
      hoverTransform: 'translateY(-5px)',
      hoverBoxShadow: '0 10px 25px rgba(0,0,0,0.1)',
      transition: 'transform 0.3s ease, box-shadow 0.3s ease',
      display: 'flex',
      flexDirection: 'column',
      height: '100%'
    }
  }
}

/**
 * Tạo props cho một sản phẩm cụ thể
 * @param product - Dữ liệu sản phẩm
 * @returns Object chứa props cho các element của sản phẩm
 */
const generateSingleProductProps = (product: ProductData) => {
  const props: any = {}

  // Props cho hình ảnh sản phẩm
  props[`${product.id}Image`] = {
    desktop: {
      imgSrc: product.image,
      width: '100%',
      height: 160,
      border: { radius: 8 },
      imageFit: 'fill'
    },
    tablet: {
      imgSrc: product.image,
      width: '100%',
      height: 160,
      border: { radius: 8 },
      imageFit: 'fill'
    },
    mobile: {
      imgSrc: product.image,
      width: '100%',
      height: 160,
      border: { radius: 8 },
      imageFit: 'fill'
    }
  }

  // Props cho tiêu đề sản phẩm
  props[`${product.id}Title`] = {
    desktop: { text: product.title, fontSize: 18, fontWeight: 700, color: '#00529c' },
    tablet: { text: product.title, fontSize: 18, fontWeight: 700, color: '#00529c' },
    mobile: { text: product.title, fontSize: 18, fontWeight: 700, color: '#00529c' }
  }

  // Props cho danh sách tính năng
  props[`${product.id}Features`] = {}
  product.features.forEach((feature: string, featureIndex: number) => {
    const featureKey = `${product.id}Feature${featureIndex + 1}`
    const marginTop = featureIndex > 0 ? { margin: { top: 4, bottom: 0 } } : {}

    props[`${product.id}Features`][featureKey] = {
      desktop: {
        text: feature,
        fontSize: 14,
        fontWeight: 400,
        color: '#4B5563',
        hasIcon: true,
        icon: 'CheckOutlined',
        iconAlign: 'start',
        iconColor: '#4B5563',
        iconSize: 16,
        ...marginTop
      },
      tablet: {
        text: feature,
        fontSize: 14,
        fontWeight: 400,
        color: '#4B5563',
        hasIcon: true,
        icon: 'CheckOutlined',
        iconAlign: 'start',
        iconColor: '#4B5563',
        iconSize: 16,
        ...marginTop
      },
      mobile: {
        text: feature,
        fontSize: 14,
        fontWeight: 400,
        color: '#4B5563',
        hasIcon: true,
        icon: 'CheckOutlined',
        iconAlign: 'start',
        iconColor: '#4B5563',
        iconSize: 16,
        ...marginTop
      }
    }
  })

  // Props cho giá sản phẩm
  props[`${product.id}Price`] = {
    desktop: { text: product.price, fontSize: 20, fontWeight: 700, color: '#f7941e' },
    tablet: { text: product.price, fontSize: 20, fontWeight: 700, color: '#f7941e' },
    mobile: { text: product.price, fontSize: 20, fontWeight: 700, color: '#f7941e' }
  }

  return props
}

/**
 * Tạo tất cả props cho các sản phẩm
 * @returns Object chứa props cho tất cả sản phẩm
 */
const generateProductProps = () => {
  const props: any = {}

  // Tạo props cho tất cả sản phẩm di động
  cellphoneData.forEach(product => {
    Object.assign(props, generateSingleProductProps(product))
  })

  // Tạo props cho tất cả sản phẩm Internet & TV
  internetTvData.forEach(product => {
    Object.assign(props, generateSingleProductProps(product))
  })

  return props
}

// Cấu hình craft cho component
ProductCategoriesBuilder.craft = {
  props: {
    // Cấu hình cho row chứa sản phẩm
    productsRow: {
      desktop: {
        ...defaultRowProps('desktop'),
        backgroundColor: '#ffffff',
        backgroundType: 'COLOR',
        padding: { top: 0, bottom: 48, left: 0, right: 0 },
        colWidths: [3, 3, 3, 3],
        gap: 6,
        height: 'auto'
      },
      tablet: {
        ...defaultRowProps('tablet'),
        backgroundColor: '#ffffff',
        backgroundType: 'COLOR',
        padding: { top: 0, bottom: 48, left: 0, right: 0 },
        colWidths: [6, 6],
        gap: 6,
        height: 'auto'
      },
      mobile: {
        ...defaultRowProps('mobile'),
        backgroundColor: '#ffffff',
        backgroundType: 'COLOR',
        padding: { top: 0, bottom: 48, left: 0, right: 0 },
        gap: 6,
        height: 'auto'
      }
    },
    // Props cho nút "Xem chi tiết"
    promoDetailBtn: {
      desktop: {
        text: 'Xem chi tiết',
        size: 'middle',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        color: '#374151',
        fontSize: 14,
        fontWeight: 600,
        padding: { top: 8, right: 16, bottom: 8, left: 16 },
        margin: { top: 0, right: 4, bottom: 0, left: 0 },
        radius: 8,
        width: '100%'
      },
      tablet: {
        text: 'Xem chi tiết',
        size: 'middle',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        color: '#374151',
        fontSize: 14,
        fontWeight: 600,
        padding: { top: 8, right: 16, bottom: 8, left: 16 },
        margin: { top: 0, right: 4, bottom: 0, left: 0 },
        radius: 8,
        width: '100%'
      },
      mobile: {
        text: 'Xem chi tiết',
        size: 'small',
        buttonType: 'primary',
        buttonBackgroundColor: '#e5e7eb',
        color: '#374151',
        fontSize: 12,
        fontWeight: 600,
        padding: { top: 6, right: 12, bottom: 6, left: 12 },
        margin: { top: 0, right: 4, bottom: 0, left: 0 },
        radius: 8,
        width: '100%'
      }
    },
    // Props cho nút "Mua ngay"
    promoApplyBtn: {
      desktop: {
        text: 'Mua ngay',
        size: 'middle',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        color: '#ffffff',
        fontSize: 14,
        fontWeight: 600,
        padding: { top: 8, right: 16, bottom: 8, left: 16 },
        margin: { top: 0, right: 0, bottom: 0, left: 0 },
        radius: 8,
        width: '100%'
      },
      tablet: {
        text: 'Mua ngay',
        size: 'middle',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        color: '#ffffff',
        fontSize: 14,
        fontWeight: 600,
        padding: { top: 8, right: 16, bottom: 8, left: 16 },
        margin: { top: 0, right: 0, bottom: 0, left: 0 },
        radius: 8,
        width: '100%'
      },
      mobile: {
        text: 'Mua ngay',
        size: 'middle',
        buttonType: 'primary',
        buttonBackgroundColor: '#00529c',
        color: '#ffffff',
        fontSize: 12,
        fontWeight: 600,
        padding: { top: 6, right: 12, bottom: 6, left: 12 },
        margin: { top: 0, right: 0, bottom: 0, left: 0 },
        radius: 8,
        width: '100%'
      }
    },
    // Merge các styles chung và props sản phẩm
    ...commonStyles,
    ...generateProductProps()
  },
  // Quy tắc cho craft editor
  rules: {
    canDrag: () => true,
    canMoveIn: () => false
  }
}
