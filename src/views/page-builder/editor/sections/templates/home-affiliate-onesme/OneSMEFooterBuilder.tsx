'use client'

import { Element, useNode, type UserComponent } from '@craftjs/core'

import { Image, Text } from '@/components/page-builder/selectors'

export const OneSMEFooterBuilder: UserComponent = props => {
  const {
    connectors: { connect, drag }
  } = useNode()

  return (
    <div
      className='w-full'
      ref={(ref: HTMLDivElement | null) => {
        if (ref) {
          connect(drag(ref))
        }
      }}
    >
      <footer className='bg-gray-800 text-white'>
        <div className='container mx-auto px-4 py-8'>
          <div className='grid grid-cols-3 gap-8 text-left sm:grid-cols-1 sm:text-center'>
            <div>
              <Element is={Text} id='footer-name' canvas {...props.footerName} />
              <Element is={Text} id='footer-description' canvas {...props.footerDescription} />
            </div>
            <div>
              <Element is={Text} id='quick-links-title' canvas {...props.quickLinksTitle} />
              <ul className='space-y-2'>
                <li>
                  <Element is={Text} id='quick-link1' canvas {...props.quickLink1} />
                </li>
                <li>
                  <Element is={Text} id='quick-link2' canvas {...props.quickLink2} />
                </li>
                <li>
                  <Element is={Text} id='quick-link3' canvas {...props.quickLink3} />
                </li>
              </ul>
            </div>
            <div>
              <Element is={Text} id='connect-title' canvas {...props.connectTitle} />
              <div className='flex justify-start space-x-4 sm:justify-center'>
                <Element is={Text} id='social-link1' canvas {...props.socialLink1} />
                <Element is={Text} id='social-link2' canvas {...props.socialLink2} />
                <Element is={Text} id='social-link3' canvas {...props.socialLink3} />
              </div>
            </div>
          </div>
          <div className='mt-8 flex flex-row items-center justify-between border-t border-gray-700 pt-6 text-center text-sm text-gray-500 sm:flex-col'>
            <Element is={Text} id='copyright' canvas {...props.copyright} />
            <div className='mt-0 flex items-center gap-4 sm:mt-4'>
              <Element is={Image} id='onesme-logo' canvas {...props.onesmeLogo} />
              <Element is={Image} id='vnpt-logo' canvas {...props.vnptLogo} />
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}

OneSMEFooterBuilder.craft = {
  props: {
    footerName: {
      desktop: {
        text: 'Nguyễn Văn An',
        fontSize: 18,
        fontWeight: 700,
        color: '#ffffff',
        textAlign: 'left',
        className: 'font-bold text-lg mb-4'
      },
      tablet: {
        text: 'Nguyễn Văn An',
        fontSize: 18,
        fontWeight: 700,
        color: '#ffffff',
        textAlign: 'left',
        className: 'font-bold text-lg mb-4'
      },
      mobile: {
        text: 'Nguyễn Văn An',
        fontSize: 16,
        fontWeight: 700,
        color: '#ffffff',
        textAlign: 'left',
        className: 'font-bold text-lg mb-4'
      }
    },
    footerDescription: {
      desktop: {
        text: 'Đối tác bán hàng chính thức của oneSME by VNPT. Cam kết mang đến dịch vụ và hỗ trợ tốt nhất.',
        fontSize: 14,
        fontWeight: 400,
        color: '#9ca3af',
        textAlign: 'left',
        className: 'text-gray-400'
      },
      tablet: {
        text: 'Đối tác bán hàng chính thức của oneSME by VNPT. Cam kết mang đến dịch vụ và hỗ trợ tốt nhất.',
        fontSize: 14,
        fontWeight: 400,
        color: '#9ca3af',
        textAlign: 'left',
        className: 'text-gray-400'
      },
      mobile: {
        text: 'Đối tác bán hàng chính thức của oneSME by VNPT. Cam kết mang đến dịch vụ và hỗ trợ tốt nhất.',
        fontSize: 12,
        fontWeight: 400,
        color: '#9ca3af',
        textAlign: 'left',
        className: 'text-gray-400'
      }
    },
    quickLinksTitle: {
      desktop: {
        text: 'Liên kết nhanh',
        fontSize: 18,
        fontWeight: 700,
        color: '#ffffff',
        textAlign: 'left',
        className: 'font-bold text-lg mb-4'
      },
      tablet: {
        text: 'Liên kết nhanh',
        fontSize: 18,
        fontWeight: 700,
        color: '#ffffff',
        textAlign: 'left',
        className: 'font-bold text-lg mb-4'
      },
      mobile: {
        text: 'Liên kết nhanh',
        fontSize: 16,
        fontWeight: 700,
        color: '#ffffff',
        textAlign: 'left',
        className: 'font-bold text-lg mb-4'
      }
    },
    quickLink1: {
      desktop: {
        text: 'Về chúng tôi',
        fontSize: 14,
        fontWeight: 400,
        color: '#9ca3af',
        textAlign: 'left',
        className: 'text-gray-400 hover:text-white'
      },
      tablet: {
        text: 'Về chúng tôi',
        fontSize: 14,
        fontWeight: 400,
        color: '#9ca3af',
        textAlign: 'left',
        className: 'text-gray-400 hover:text-white'
      },
      mobile: {
        text: 'Về chúng tôi',
        fontSize: 12,
        fontWeight: 400,
        color: '#9ca3af',
        textAlign: 'left',
        className: 'text-gray-400 hover:text-white'
      }
    },
    quickLink2: {
      desktop: {
        text: 'Dịch vụ',
        fontSize: 14,
        fontWeight: 400,
        color: '#9ca3af',
        textAlign: 'left',
        className: 'text-gray-400 hover:text-white'
      },
      tablet: {
        text: 'Dịch vụ',
        fontSize: 14,
        fontWeight: 400,
        color: '#9ca3af',
        textAlign: 'left',
        className: 'text-gray-400 hover:text-white'
      },
      mobile: {
        text: 'Dịch vụ',
        fontSize: 12,
        fontWeight: 400,
        color: '#9ca3af',
        textAlign: 'left',
        className: 'text-gray-400 hover:text-white'
      }
    },
    quickLink3: {
      desktop: {
        text: 'Chính sách',
        fontSize: 14,
        fontWeight: 400,
        color: '#9ca3af',
        textAlign: 'left',
        className: 'text-gray-400 hover:text-white'
      },
      tablet: {
        text: 'Chính sách',
        fontSize: 14,
        fontWeight: 400,
        color: '#9ca3af',
        textAlign: 'left',
        className: 'text-gray-400 hover:text-white'
      },
      mobile: {
        text: 'Chính sách',
        fontSize: 12,
        fontWeight: 400,
        color: '#9ca3af',
        textAlign: 'left',
        className: 'text-gray-400 hover:text-white'
      }
    },
    connectTitle: {
      desktop: {
        text: 'Kết nối với tôi',
        fontSize: 18,
        fontWeight: 700,
        color: '#ffffff',
        textAlign: 'left',
        className: 'font-bold text-lg mb-4'
      },
      tablet: {
        text: 'Kết nối với tôi',
        fontSize: 18,
        fontWeight: 700,
        color: '#ffffff',
        textAlign: 'left',
        className: 'font-bold text-lg mb-4'
      },
      mobile: {
        text: 'Kết nối với tôi',
        fontSize: 16,
        fontWeight: 700,
        color: '#ffffff',
        textAlign: 'left',
        className: 'font-bold text-lg mb-4'
      }
    },
    socialLink1: {
      desktop: {
        text: 'Facebook',
        fontSize: 24,
        fontWeight: 400,
        color: '#9ca3af',
        textAlign: 'center',
        className: 'text-gray-400 hover:text-white text-2xl'
      },
      tablet: {
        text: 'Facebook',
        fontSize: 24,
        fontWeight: 400,
        color: '#9ca3af',
        textAlign: 'center',
        className: 'text-gray-400 hover:text-white text-2xl'
      },
      mobile: {
        text: 'Facebook',
        fontSize: 20,
        fontWeight: 400,
        color: '#9ca3af',
        textAlign: 'center',
        className: 'text-gray-400 hover:text-white text-2xl'
      }
    },
    socialLink2: {
      desktop: {
        text: 'Zalo',
        fontSize: 24,
        fontWeight: 400,
        color: '#9ca3af',
        textAlign: 'center',
        className: 'text-gray-400 hover:text-white text-2xl'
      },
      tablet: {
        text: 'Zalo',
        fontSize: 24,
        fontWeight: 400,
        color: '#9ca3af',
        textAlign: 'center',
        className: 'text-gray-400 hover:text-white text-2xl'
      },
      mobile: {
        text: 'Zalo',
        fontSize: 20,
        fontWeight: 400,
        color: '#9ca3af',
        textAlign: 'center',
        className: 'text-gray-400 hover:text-white text-2xl'
      }
    },
    socialLink3: {
      desktop: {
        text: 'Email',
        fontSize: 24,
        fontWeight: 400,
        color: '#9ca3af',
        textAlign: 'center',
        className: 'text-gray-400 hover:text-white text-2xl'
      },
      tablet: {
        text: 'Email',
        fontSize: 24,
        fontWeight: 400,
        color: '#9ca3af',
        textAlign: 'center',
        className: 'text-gray-400 hover:text-white text-2xl'
      },
      mobile: {
        text: 'Email',
        fontSize: 20,
        fontWeight: 400,
        color: '#9ca3af',
        textAlign: 'center',
        className: 'text-gray-400 hover:text-white text-2xl'
      }
    },
    copyright: {
      desktop: {
        text: '© 2025. Trang tư vấn của Nguyễn Văn An.',
        fontSize: 14,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'center',
        className: 'text-gray-500 text-sm'
      },
      tablet: {
        text: '© 2025. Trang tư vấn của Nguyễn Văn An.',
        fontSize: 14,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'center',
        className: 'text-gray-500 text-sm'
      },
      mobile: {
        text: '© 2025. Trang tư vấn của Nguyễn Văn An.',
        fontSize: 12,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'center',
        className: 'text-gray-500 text-sm'
      }
    },
    onesmeLogo: {
      desktop: {
        imgSrc: '/assets/images/logo.svg',
        width: 24,
        height: 24,
        className: 'h-6 filter brightness-0 invert'
      },
      tablet: {
        imgSrc: '/assets/images/logo.svg',
        width: 24,
        height: 24,
        className: 'h-6 filter brightness-0 invert'
      },
      mobile: {
        imgSrcMobile: '/assets/images/logo.svg',
        width: 20,
        height: 20,
        className: 'h-6 filter brightness-0 invert'
      }
    },
    vnptLogo: {
      desktop: {
        imgSrc: 'https://www.vnpt.com.vn/img/logo-vnpt.svg',
        width: 24,
        height: 24,
        className: 'h-6 filter brightness-0 invert'
      },
      tablet: {
        imgSrc: 'https://www.vnpt.com.vn/img/logo-vnpt.svg',
        width: 24,
        height: 24,
        className: 'h-6 filter brightness-0 invert'
      },
      mobile: {
        imgSrcMobile: 'https://www.vnpt.com.vn/img/logo-vnpt.svg',
        width: 20,
        height: 20,
        className: 'h-6 filter brightness-0 invert'
      }
    }
  },
  rules: {
    canDrag: () => true,
    canMoveIn: () => false
  }
}
