'use client'

import { Element, useNode, type UserComponent } from '@craftjs/core'

import { Image, Text, Row, Column, SocialMedia } from '@/components/page-builder/selectors'
import { defaultResponseRowProps, defaultResponseColumnProps } from '@/constants/page-builder/row'
import { defaultImageProps } from '@/constants/page-builder'
import { defaultResponseSocialMediaProps } from '@/constants/page-builder/socialMedia'

export const OneSMEHeaderBuilder: UserComponent = props => {
  const {
    connectors: { connect, drag }
  } = useNode()

  return (
    <div
      className='w-full'
      ref={(ref: HTMLDivElement | null) => {
        if (ref) {
          connect(drag(ref))
        }
      }}
    >
      <Element is={Row} canvas id='header-row' {...props.headerRow}>
        <Element is={Column} canvas id='logo-column' {...props.logoColumn}>
          <div className='flex items-center gap-4'>
            <div className='w-[94px] shrink-0'>
              <Element is={Image} id='image1' {...props.logo} />
            </div>
            <div className='block h-8 border-l border-solid border-gray-300 sm:hidden'></div>
            <div className='flex items-center gap-3'>
              <div className='w-[48px] shrink-0'>
                <Element is={Image} id='expert-avatar' canvas {...props.expertAvatar} />
              </div>
              <div className='shrink-0'>
                <Element is={Text} id='expert-name' canvas {...props.expertName} />
                <Element is={Text} id='expert-title' canvas {...props.expertTitle} />
              </div>
            </div>
          </div>
        </Element>
        <Element is={Column} canvas id='contact-column' {...props.contactColumn}>
          <div className='flex items-center gap-3 text-sm text-gray-600'>
            <Element is={SocialMedia} id='contact-social-media' canvas {...props.contactSocialMedia} />
            <div className='h-5 border-l border-solid border-gray-300'></div>
            <Element is={SocialMedia} id='social-media-links' canvas {...props.socialMediaLinks} />
          </div>
        </Element>
      </Element>
    </div>
  )
}

OneSMEHeaderBuilder.craft = {
  displayName: 'Header OneSME',
  props: {
    logo: {
      desktop: {
        ...defaultImageProps('desktop'),
        imgSrc: '/assets/images/logo.svg',
        width: '94px',
        height: '36px',
        border: {
          style: 'none',
          color: '',
          thickness: 0,
          radius: 0
        }
      },
      tablet: {
        ...defaultImageProps('tablet'),
        imgSrc: '/assets/images/logo.svg',
        width: '94px',
        height: '36px',
        border: {
          style: 'none',
          color: '',
          thickness: 0,
          radius: 0
        }
      },
      mobile: {
        imgSrcMobile: '/assets/images/logo.svg',
        width: '94px',
        height: '36px',
        border: {
          style: 'none',
          color: '',
          thickness: 0,
          radius: 0
        }
      }
    },
    headerRow: {
      desktop: {
        ...defaultResponseRowProps.desktop,
        padding: { top: 12, right: 0, bottom: 12, left: 0 },
        colWidths: [8, 4], // Logo column rộng hơn, contact column nhỏ hơn
        gap: 4,
        contentAlign: 'center',
        justifyContent: 'space-between'
      },
      tablet: {
        ...defaultResponseRowProps.tablet,
        padding: { top: 12, right: 0, bottom: 12, left: 0 },
        colWidths: [8, 4],
        gap: 4,
        contentAlign: 'center',
        justifyContent: 'space-between'
      },
      mobile: {
        ...defaultResponseRowProps.mobile,
        padding: { top: 12, right: 0, bottom: 12, left: 0 },
        colWidths: [12], // Mobile: full width, stack vertically
        gap: 3,
        contentAlign: 'center',
        justifyContent: 'center'
      }
    },
    logoColumn: {
      desktop: {
        ...defaultResponseColumnProps.desktop,
        contentAlign: 'start',
        justifyContent: 'center'
      },
      tablet: {
        ...defaultResponseColumnProps.tablet,
        contentAlign: 'start',
        justifyContent: 'center'
      },
      mobile: {
        ...defaultResponseColumnProps.mobile,
        contentAlign: 'center',
        justifyContent: 'center',
        margin: { top: 0, right: 0, bottom: 12, left: 0 } // Thêm margin bottom cho mobile
      }
    },
    contactColumn: {
      desktop: {
        ...defaultResponseColumnProps.desktop,
        contentAlign: 'end',
        justifyContent: 'center'
      },
      tablet: {
        ...defaultResponseColumnProps.tablet,
        contentAlign: 'end',
        justifyContent: 'center'
      },
      mobile: {
        ...defaultResponseColumnProps.mobile,
        contentAlign: 'center',
        justifyContent: 'center'
      }
    },
    expertAvatar: {
      desktop: {
        ...defaultImageProps('desktop'),
        imgSrc: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=1887&auto=format&fit=crop',
        height: '48px',
        imageFit: 'fill',
        border: {
          style: 'none',
          color: '',
          thickness: 0,
          radius: 999
        }
      },
      tablet: {
        ...defaultImageProps('tablet'),
        imgSrc: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=1887&auto=format&fit=crop',
        height: '48px',
        imageFit: 'fill',
        border: {
          style: 'none',
          color: '',
          thickness: 0,
          radius: 999
        }
      },
      mobile: {
        imgSrcMobile: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=1887&auto=format&fit=crop',
        height: '48px',
        imageFit: 'fill',
        border: {
          style: 'none',
          color: '',
          thickness: 0,
          radius: 999
        }
      }
    },
    expertName: {
      desktop: {
        text: 'Nguyễn Văn An',
        fontSize: 18,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'left'
      },
      tablet: {
        text: 'Nguyễn Văn An',
        fontSize: 18,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'left'
      },
      mobile: {
        text: 'Nguyễn Văn An',
        fontSize: 16,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'center'
      }
    },
    expertTitle: {
      desktop: {
        text: 'Chuyên gia tư vấn',
        fontSize: 14,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'left',
        className: 'text-sm text-gray-500'
      },
      tablet: {
        text: 'Chuyên gia tư vấn',
        fontSize: 14,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'left',
        className: 'text-sm text-gray-500'
      },
      mobile: {
        text: 'Chuyên gia tư vấn',
        fontSize: 12,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'center',
        className: 'text-sm text-gray-500'
      }
    },
    contactSocialMedia: {
      desktop: {
        ...defaultResponseSocialMediaProps.desktop,
        size: 14,
        iconAlign: 'start',
        fontFamily: 'Inter, sans-serif',
        fontSize: 14,
        socialList: [
          {
            name: '0912345678',
            icon: 'PhoneFilled',
            iconColor: '#4b5564',
            url: '',
            displayType: 'NAME'
          },
          {
            name: '<EMAIL>',
            icon: 'MailFilled',
            iconColor: '#4b5564',
            url: '',
            displayType: 'NAME'
          }
        ]
      },
      tablet: {
        ...defaultResponseSocialMediaProps.tablet,
        size: 14,
        iconAlign: 'start',
        fontFamily: 'Inter, sans-serif',
        fontSize: 14,
        socialList: [
          {
            name: '0912345678',
            icon: 'PhoneFilled',
            iconColor: '#4b5564',
            url: '',
            displayType: 'NAME'
          },
          {
            name: '<EMAIL>',
            icon: 'MailFilled',
            iconColor: '#4b5564',
            url: '',
            displayType: 'NAME'
          }
        ]
      },
      mobile: {
        ...defaultResponseSocialMediaProps.mobile,
        size: 14,
        iconAlign: 'start',
        fontFamily: 'Inter, sans-serif',
        fontSize: 14,
        socialList: [
          {
            name: '0912345678',
            icon: 'PhoneFilled',
            iconColor: '#4b5564',
            url: '',
            displayType: 'ICON'
          },
          {
            name: '<EMAIL>',
            icon: 'MailFilled',
            iconColor: '#4b5564',
            url: '',
            displayType: 'ICON'
          }
        ]
      }
    },
    socialMediaLinks: {
      desktop: {
        ...defaultResponseSocialMediaProps.desktop,
        size: 20,
        iconAlign: 'start',
        socialList: [
          {
            name: 'Facebook',
            icon: 'FacebookIcon2',
            iconColor: '#4b5564',
            url: 'https://facebook.com',
            displayType: 'ICON'
          },
          {
            name: 'Instagram',
            icon: 'InstagramFilled',
            iconColor: '#4b5564',
            url: 'https://instagram.com',
            displayType: 'ICON'
          }
        ]
      },
      tablet: {
        ...defaultResponseSocialMediaProps.tablet,
        size: 20,
        iconAlign: 'start',
        socialList: [
          {
            name: 'Facebook',
            icon: 'FacebookIcon2',
            iconColor: '#4b5564',
            url: 'https://facebook.com',
            displayType: 'ICON'
          },
          {
            name: 'Instagram',
            icon: 'InstagramFilled',
            iconColor: '#4b5564',
            url: 'https://instagram.com',
            displayType: 'ICON'
          }
        ]
      },
      mobile: {
        ...defaultResponseSocialMediaProps.mobile,
        size: 20,
        iconAlign: 'start',
        socialList: [
          {
            name: 'Facebook',
            icon: 'FacebookIcon2',
            iconColor: '#4b5564',
            url: 'https://facebook.com',
            displayType: 'ICON'
          },
          {
            name: 'Instagram',
            icon: 'InstagramFilled',
            iconColor: '#4b5564',
            url: 'https://instagram.com',
            displayType: 'ICON'
          }
        ]
      }
    }
  },
  rules: {
    canDrag: () => true,
    canMoveIn: () => false
  }
}
