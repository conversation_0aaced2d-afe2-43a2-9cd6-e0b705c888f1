'use client'

import { Element, useNode, type UserComponent } from '@craftjs/core'

import DOMPurify from 'dompurify'

import { Button, Text, Row, Column } from '@/components/page-builder/selectors'

// D<PERSON> liệu sản phẩm mobile cho Gen Z
const MOBILE_DEALS_DATA = {
  sectionTitle: 'Gói Data Cho Gen Z', // Tiêu đề section
  products: [
    {
      id: 'product1',
      name: 'D169G', // Tên gói cước
      price: '169K', // Giá gói
      ctaText: 'MÚC NGAY!', // Text nút CTA
      badge: {
        text: 'MAX DATA',
        color: 'bg-purple-500' // Màu badge
      },
      features: [
        '<strong>7GB</strong> data tốc độ cao/ngày',
        '<strong>2000 phút</strong> gọi nội mạng',
        '<strong>150 phút</strong> gọi ngoại mạng'
      ]
    },
    {
      id: 'product2',
      name: 'BIG120',
      price: '120K',
      ctaText: 'HỐT LIỀN!',
      badge: {
        text: 'TOP TRENDING',
        color: 'bg-pink-500'
      },
      features: ['<strong>2GB</strong> data tốc độ cao/ngày', 'Lướt TikTok, Zalo thả ga', 'Gói cước quốc dân']
    },
    {
      id: 'product3',
      name: 'D15G',
      price: '70K',
      ctaText: 'VÀO VIỆC!',
      badge: {
        text: 'BEST VALUE',
        color: 'bg-orange-500'
      },
      features: ['<strong>500MB</strong> data tốc độ cao/ngày', 'Tiết kiệm cho học sinh', 'Online mọi lúc mọi nơi']
    },
    {
      id: 'product4',
      name: 'COMBO Z',
      price: '99K',
      ctaText: 'CHỐT ĐƠN!',
      badge: {
        text: 'TIKTOK FREE',
        color: 'bg-cyan-500'
      },
      features: ['<strong>4GB</strong> data tốc độ cao/ngày', 'Miễn phí Zalo, TikTok', 'Thêm 1000 phút gọi']
    }
  ]
}

// Helper function tạo responsive props cơ bản
const createResponsiveProps = (desktopConfig: any, tabletConfig?: any, mobileConfig?: any) => ({
  desktop: desktopConfig,
  tablet: tabletConfig || desktopConfig,
  mobile: mobileConfig || tabletConfig || desktopConfig
})

// Helper function tạo props cho Text component
const createTextProps = (
  text: string,
  fontSize: { desktop: number; tablet?: number; mobile?: number },
  options: any = {}
) => {
  const baseProps = {
    text,
    fontWeight: options.fontWeight || 800,
    color: options.color || '#1f2937',
    textAlign: options.textAlign || 'center',
    className: options.className || ''
  }

  return createResponsiveProps(
    { ...baseProps, fontSize: fontSize.desktop, margin: options.margin?.desktop },
    { ...baseProps, fontSize: fontSize.tablet || fontSize.desktop, margin: options.margin?.tablet },
    { ...baseProps, fontSize: fontSize.mobile || fontSize.tablet || fontSize.desktop, margin: options.margin?.mobile }
  )
}

// Helper function tạo props cho Button component
const createButtonProps = (text: string, fontSize: { desktop: number; tablet?: number; mobile?: number }) => {
  const baseProps = {
    text,
    fontWeight: 700,
    color: '#ffffff',
    padding: { top: 12, bottom: 12, left: 24, right: 24 },
    borderRadius: 8,
    width: '100%',
    size: 'large',
    buttonBackgroundColor: '#A855F7',
    containerClassName: 'mt-auto transition-all duration-300 hover:scale-105',
    className:
      'bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 w-full text-white font-bold py-3 rounded-lg'
  }

  return createResponsiveProps(
    { ...baseProps, fontSize: fontSize.desktop },
    { ...baseProps, fontSize: fontSize.tablet || fontSize.desktop },
    { ...baseProps, fontSize: fontSize.mobile || fontSize.tablet || fontSize.desktop }
  )
}

// Helper function tạo props cho Column component
const createColumnProps = (marginConfig: { desktop: number; tablet: number; mobile: number }) => {
  const baseProps = {
    backgroundColor: 'transparent',
    backgroundType: '',
    padding: { top: 0, right: 0, bottom: 0, left: 0 },
    contentAlign: 'start',
    justifyContent: 'start'
  }

  return createResponsiveProps(
    { ...baseProps, margin: { top: 0, bottom: marginConfig.desktop, left: 0, right: 0 } },
    { ...baseProps, margin: { top: 0, bottom: marginConfig.tablet, left: 0, right: 0 } },
    { ...baseProps, margin: { top: 0, bottom: marginConfig.mobile, left: 0, right: 0 } }
  )
}

// Helper function tạo props cho Text component với icon
const createTextWithIconProps = (
  text: string,
  fontSize: { desktop: number; tablet?: number; mobile?: number },
  options: {
    className?: string
    margin?: {
      desktop?: { top: number; bottom: number; left: number; right: number }
      tablet?: { top: number; bottom: number; left: number; right: number }
      mobile?: { top: number; bottom: number; left: number; right: number }
    }
    hasIcon?: boolean
    icon?: string
    iconColor?: string
    iconAlign?: 'start' | 'end'
  } = {}
) => {
  const baseProps = {
    text,
    fontWeight: 400,
    color: '#6b7280',
    textAlign: 'left',
    hasIcon: options.hasIcon || false,
    icon: options.icon || '',
    iconColor: options.iconColor || '#10b981',
    iconAlign: options.iconAlign || 'start',
    className: options.className || ''
  }

  return createResponsiveProps(
    { ...baseProps, fontSize: fontSize.desktop, margin: options.margin?.desktop },
    { ...baseProps, fontSize: fontSize.tablet || fontSize.desktop, margin: options.margin?.tablet },
    { ...baseProps, fontSize: fontSize.mobile || fontSize.tablet || fontSize.desktop, margin: options.margin?.mobile }
  )
}

// Function tạo craft props từ data
const generateCraftProps = () => {
  const props: any = {
    // Props cho products row
    productsRow: createResponsiveProps(
      {
        colWidths: [3, 3, 3, 3],
        gap: 8,
        backgroundColor: 'transparent',
        backgroundType: '',
        padding: { top: 0, right: 0, bottom: 0, left: 0 },
        margin: { top: 0, bottom: 0 },
        contentAlign: 'start',
        justifyContent: 'start'
      },
      {
        colWidths: [6, 6],
        gap: 8,
        backgroundColor: 'transparent',
        backgroundType: '',
        padding: { top: 0, right: 0, bottom: 0, left: 0 },
        margin: { top: 0, bottom: 0 },
        contentAlign: 'start',
        justifyContent: 'start',
        isBreakLine: true
      },
      {
        colWidths: [12],
        gap: 8,
        backgroundColor: 'transparent',
        backgroundType: '',
        padding: { top: 0, right: 0, bottom: 0, left: 0 },
        margin: { top: 0, bottom: 0 },
        contentAlign: 'start',
        justifyContent: 'start',
        isBreakLine: true
      }
    ),

    // Props cho section title
    sectionTitle: createTextProps(
      MOBILE_DEALS_DATA.sectionTitle,
      { desktop: 40, tablet: 32, mobile: 28 },
      {
        color: '#A855F7', // Màu đầu tiên của gradient để hiển thị trong panel
        className: 'bg-gradient-to-r from-[#7f5af0] to-[#ff70a6] bg-clip-text text-transparent',
        margin: {
          desktop: { top: 0, bottom: 32, left: 0, right: 0 },
          tablet: { top: 0, bottom: 32, left: 0, right: 0 },
          mobile: { top: 0, bottom: 32, left: 0, right: 0 }
        }
      }
    )
  }

  // Tạo props cho từng product
  MOBILE_DEALS_DATA.products.forEach((product, index) => {
    const marginConfig = {
      desktop: 0,
      tablet: index < 2 ? 16 : index === 2 ? 16 : 0,
      mobile: index < 3 ? 16 : 0
    }

    // Props cho column
    props[`${product.id}Column`] = createColumnProps(marginConfig)

    // Props cho product name
    props[`${product.id}Name`] = createTextProps(
      product.name,
      { desktop: 24, tablet: 24, mobile: 20 },
      { className: 'text-2xl font-extrabold text-gray-800' }
    )

    // Props cho product price
    props[`${product.id}Price`] = createTextProps(
      product.price,
      { desktop: 48, tablet: 40, mobile: 32 },
      {
        fontWeight: 900,
        color: '#00529c',
        className: 'text-5xl font-black my-4 text-blue-800'
      }
    )

    // Props cho CTA button
    props[`${product.id}Cta`] = createButtonProps(product.ctaText, { desktop: 16, tablet: 16, mobile: 14 })

    // Tạo props cho từng feature với icon check
    product.features.forEach((feature, featureIndex) => {
      props[`${product.id}Feature${featureIndex}`] = createTextWithIconProps(
        feature,
        { desktop: 16, tablet: 16, mobile: 16 },
        {
          hasIcon: true,
          icon: 'CheckCircleFilled',
          iconColor: '#10b981',
          iconAlign: 'start',
          margin: {
            desktop: { top: 0, bottom: 8, left: 0, right: 0 },
            tablet: { top: 0, bottom: 8, left: 0, right: 0 },
            mobile: { top: 0, bottom: 8, left: 0, right: 0 }
          }
        }
      )
    })
  })

  return props
}

export const MobileDealsGenzBuilder: UserComponent = props => {
  const {
    connectors: { connect, drag }
  } = useNode()

  return (
    <div
      className='w-full'
      ref={(ref: HTMLDivElement | null) => {
        if (ref) {
          connect(drag(ref))
        }
      }}
    >
      <section id='mobile-deals' className='py-16'>
        <div className='container mx-auto px-4'>
          <Element is={Text} id='section-title' canvas {...props.sectionTitle} />

          <Element is={Row} id='products-row' canvas {...props.productsRow}>
            {MOBILE_DEALS_DATA.products.map(product => (
              <Element
                key={product.id}
                is={Column}
                id={`${product.id}-column`}
                canvas
                {...props[`${product.id}Column`]}
              >
                <div className='relative flex size-full flex-col overflow-hidden rounded-2xl border-2 border-transparent bg-white shadow-sm transition-all duration-300 hover:-translate-y-2 hover:border-purple-500 hover:shadow-xl'>
                  <div
                    className={`absolute -left-2 top-4 ${product.badge.color} px-3 py-1 text-xs font-bold text-white`}
                    style={{ clipPath: 'polygon(0% 0%, 100% 0, 90% 50%, 100% 100%, 0% 100%)' }}
                    dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(product.badge.text) }}
                  />
                  <div className='flex grow flex-col p-6 pt-12 text-center'>
                    <Element is={Text} id={`${product.id}-name`} canvas {...props[`${product.id}Name`]} />
                    <Element is={Text} id={`${product.id}-price`} canvas {...props[`${product.id}Price`]} />
                    {/* Feature list */}
                    {product.features.map((feature, featureIndex) => (
                      <Element
                        key={featureIndex}
                        is={Text}
                        id={`${product.id}-feature-${featureIndex}`}
                        canvas
                        {...props[`${product.id}Feature${featureIndex}`]}
                      />
                    ))}
                    <Element is={Button} id={`${product.id}-cta`} canvas {...props[`${product.id}Cta`]} />
                  </div>
                </div>
              </Element>
            ))}
          </Element>
        </div>
      </section>
    </div>
  )
}

MobileDealsGenzBuilder.craft = {
  props: generateCraftProps(),
  rules: {
    canDrag: () => true,
    canMoveIn: () => false
  }
}
