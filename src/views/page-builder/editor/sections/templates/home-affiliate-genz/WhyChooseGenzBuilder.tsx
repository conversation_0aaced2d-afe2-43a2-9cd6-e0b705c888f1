'use client'

import { Element, useNode, type UserComponent } from '@craftjs/core'

import { Column, Row, Text } from '@/components/page-builder/selectors'
import { IconLibrary } from '@/components/page-builder/selectors/IconLibrary'
import { defaultResponseColumnProps, defaultRowProps } from '@/constants/page-builder/row'

// Dữ liệu tính năng - Features data
const WHY_CHOOSE_DATA = {
  sectionTitle: {
    text: 'My Commitment',
    className: 'bg-gradient-to-r from-purple-500 to-pink-500 bg-clip-text text-transparent'
  },
  features: [
    {
      id: 'feature1',
      icon: 'ThunderboltOutlined',
      iconColor: '#a855f7',
      title: 'Tư vấn tốc độ',
      description: 'Trả lời tin nhắn trong 1 nốt nhạc. Hỗ trợ bạn mọi lúc.'
    },
    {
      id: 'feature2',
      icon: 'GiftOutlined',
      iconColor: '#ec4899',
      title: '<PERSON> độc quyền',
      description: '<PERSON><PERSON><PERSON> có những ưu đãi và voucher xịn xò nhất dành riêng cho bạn.'
    },
    {
      id: 'feature3',
      icon: 'TeamOutlined',
      iconColor: '#f97316',
      title: 'Hỗ trợ như người nhà',
      description: 'Mình sẽ giúp bạn chọn gói cước phù hợp nhất, không lòng vòng.'
    }
  ]
}

// Helper functions - Hàm hỗ trợ
const createResponsiveProps = (desktop: any, tablet: any, mobile: any) => ({
  desktop,
  tablet,
  mobile
})

const createTextProps = (
  text: string,
  fontSize: { desktop: number; tablet: number; mobile: number },
  fontWeight: number,
  color: string,
  textAlign: string,
  className?: string,
  margin?: any
) =>
  createResponsiveProps(
    { text, fontSize: fontSize.desktop, fontWeight, color, textAlign, className, ...(margin && { margin }) },
    { text, fontSize: fontSize.tablet, fontWeight, color, textAlign, className, ...(margin && { margin }) },
    { text, fontSize: fontSize.mobile, fontWeight, color, textAlign, className, ...(margin && { margin }) }
  )

const createIconLibraryProps = (icon: string, iconColor: string, size: number) =>
  createResponsiveProps(
    {
      icon,
      iconColor,
      size,
      showIcon: { desktop: true, tablet: true, mobile: true },
      showTitle: { desktop: false, tablet: false, mobile: false },
      showDesc: { desktop: false, tablet: false, mobile: false },
      backgroundColor: 'transparent',
      bgIconColor: '#f0f2f5',
      backgroundType: 'COLOR',
      padding: { top: 0, bottom: 0, left: 16, right: 0 }
    },
    {
      icon,
      iconColor,
      size,
      showIcon: { desktop: true, tablet: true, mobile: true },
      showTitle: { desktop: false, tablet: false, mobile: false },
      showDesc: { desktop: false, tablet: false, mobile: false },
      backgroundColor: 'transparent',
      bgIconColor: '#f0f2f5',
      backgroundType: 'COLOR',
      padding: { top: 0, bottom: 0, left: 16, right: 0 }
    },
    {
      icon,
      iconColor,
      size,
      showIcon: { desktop: true, tablet: true, mobile: true },
      showTitle: { desktop: false, tablet: false, mobile: false },
      showDesc: { desktop: false, tablet: false, mobile: false },
      backgroundColor: 'transparent',
      bgIconColor: '#f0f2f5',
      backgroundType: 'COLOR',
      padding: { top: 0, bottom: 0, left: 16, right: 0 }
    }
  )

// Generate craft props - Tạo props tự động
const generateCraftProps = () => {
  const props: any = {}

  // Title Row props
  props.titleRow = {
    desktop: {
      ...defaultRowProps('desktop'),
      backgroundColor: '#f0f2f5',
      backgroundType: 'COLOR',
      padding: { top: 64, bottom: 0, left: 16, right: 16 },
      height: 'auto',
      justifyContent: 'center'
    },
    tablet: {
      ...defaultRowProps('tablet'),
      backgroundColor: '#f0f2f5',
      backgroundType: 'COLOR',
      padding: { top: 64, bottom: 0, left: 16, right: 16 },
      height: 'auto',
      justifyContent: 'center'
    },
    mobile: {
      ...defaultRowProps('mobile'),
      backgroundColor: '#f0f2f5',
      backgroundType: 'COLOR',
      padding: { top: 48, bottom: 0, left: 16, right: 16 },
      height: 'auto',
      justifyContent: 'center'
    }
  }

  // Title Column props
  props.titleColumn = defaultResponseColumnProps

  // Features Row props (thay thế grid)
  props.featuresRow = {
    desktop: {
      ...defaultRowProps('desktop'),
      colWidths: [4, 4, 4],
      gap: 8,
      justifyContent: 'center',
      height: 'auto',
      padding: { top: 0, bottom: 64, left: 0, right: 0 }
    },
    tablet: {
      ...defaultRowProps('tablet'),
      colWidths: [4, 4, 4],
      gap: 8,
      justifyContent: 'center',
      alignItems: 'stretch',
      height: 'auto',
      padding: { top: 0, bottom: 64, left: 0, right: 0 }
    },
    mobile: {
      ...defaultRowProps('mobile'),
      colWidths: [12],
      gap: 6,
      justifyContent: 'center',
      alignItems: 'stretch',
      height: 'auto',
      isBreakLine: true,
      padding: { top: 0, bottom: 48, left: 0, right: 0 }
    }
  }

  // Section title props
  props.sectionTitle = createTextProps(
    WHY_CHOOSE_DATA.sectionTitle.text,
    { desktop: 40, tablet: 32, mobile: 28 },
    800,
    '#A855F7', // Màu đầu tiên của gradient để hiển thị trong panel
    'center',
    WHY_CHOOSE_DATA.sectionTitle.className,
    { top: 0, bottom: 0 }
  )

  // Feature props
  WHY_CHOOSE_DATA.features.forEach((feature, index) => {
    const featureNum = index + 1

    // Feature Column props
    props[`feature${featureNum}Column`] = {
      desktop: {
        ...defaultResponseColumnProps.desktop,
        padding: { top: 24, bottom: 24, left: 24, right: 24 }, // p-6
        contentAlign: 'center', // text-center
        justifyContent: 'center',
        height: 'auto'
      },
      tablet: {
        ...defaultResponseColumnProps.tablet,
        padding: { top: 24, bottom: 24, left: 24, right: 24 },
        contentAlign: 'center',
        justifyContent: 'center',
        height: 'auto'
      },
      mobile: {
        ...defaultResponseColumnProps.mobile,
        padding: { top: 24, bottom: 24, left: 24, right: 24 },
        contentAlign: 'center',
        justifyContent: 'center',
        height: 'auto'
      }
    }

    // Title props
    props[`feature${featureNum}Title`] = createTextProps(
      feature.title,
      { desktop: 20, tablet: 18, mobile: 16 },
      700,
      '#1f2937',
      'center',
      `text-${index === 0 ? 'xl' : index === 1 ? 'lg' : 'base'} font-bold mb-2 text-gray-800`,
      { top: 16, bottom: 8 }
    )

    // Description props
    props[`feature${featureNum}Description`] = createTextProps(
      feature.description,
      { desktop: 16, tablet: 14, mobile: 14 },
      400,
      '#6b7280',
      'center',
      'text-gray-600'
    )

    // Icon props
    props[`feature${featureNum}Icon`] = createIconLibraryProps(feature.icon, feature.iconColor, 48)
  })

  return props
}

export const WhyChooseGenzBuilder: UserComponent = props => {
  const {
    connectors: { connect, drag }
  } = useNode()

  return (
    <div
      className='w-full'
      ref={(ref: HTMLDivElement | null) => {
        if (ref) {
          connect(drag(ref))
        }
      }}
    >
      <Element is={Row} id='title-row' canvas {...props.titleRow}>
        <Element is={Column} id='title-column' canvas {...props.titleColumn}>
          <Element is={Text} id='section-title' canvas {...props.sectionTitle} />
        </Element>
      </Element>
      <Element is={Row} id='features-row' canvas {...props.featuresRow}>
        {WHY_CHOOSE_DATA.features.map((feature, index) => {
          const featureNum = index + 1

          return (
            <Element
              key={feature.id}
              is={Column}
              id={`feature-${featureNum}-column`}
              canvas
              {...props[`feature${featureNum}Column`]}
            >
              <Element is={IconLibrary} id={`${feature.id}-icon`} canvas {...props[`feature${featureNum}Icon`]} />
              <Element is={Text} id={`${feature.id}-title`} canvas {...props[`feature${featureNum}Title`]} />
              <Element
                is={Text}
                id={`${feature.id}-description`}
                canvas
                {...props[`feature${featureNum}Description`]}
              />
            </Element>
          )
        })}
      </Element>
    </div>
  )
}

WhyChooseGenzBuilder.craft = {
  props: generateCraftProps(),
  rules: {
    canDrag: () => true,
    canMoveIn: () => false
  }
}
