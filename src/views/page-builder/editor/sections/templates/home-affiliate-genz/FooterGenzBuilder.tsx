'use client'

import { Element, useNode, type UserComponent } from '@craftjs/core'

import { Column, Container, Row, Text, Image, SocialMedia } from '@/components/page-builder/selectors'
import { defaultImageProps } from '@/constants/page-builder'
import { defaultResponseColumnProps, defaultRowProps } from '@/constants/page-builder/row'
import { defaultResponseSocialMediaProps } from '@/constants/page-builder/socialMedia'

export const FooterGenzBuilder: UserComponent = props => {
  const {
    connectors: { connect, drag }
  } = useNode()

  return (
    <div
      className='w-full'
      ref={(ref: HTMLDivElement | null) => {
        if (ref) {
          connect(drag(ref))
        }
      }}
    >
      <Element is={Container} id='container' canvas>
        <Element is={Row} id='footer-main-row' canvas {...props.footerMainRow}>
          <Element is={Column} id='info-column' canvas {...props.infoColumn}>
            <Element is={Text} id='expert-name' canvas {...props.expertName} />
            <Element is={Text} id='description' canvas {...props.description} />
            <Element is={Image} id='onesme-logo' canvas {...props.onesmeLogo} />
          </Element>
          <Element is={Column} id='links-column' canvas {...props.linksColumn}>
            <Element is={Text} id='links-title' canvas {...props.linksTitle} />
            <Element is={Text} id='about-link' canvas {...props.aboutLink} />
            <Element is={Text} id='services-link' canvas {...props.servicesLink} />
            <Element is={Text} id='policy-link' canvas {...props.policyLink} />
          </Element>
          <Element is={Column} id='social-column' canvas {...props.socialColumn}>
            <Element is={Text} id='social-title' canvas {...props.socialTitle} />
            <div className='flex flex-col gap-2'>
              <Element is={SocialMedia} id='phone-contact' canvas {...props.phoneContact} />
              <Element is={SocialMedia} id='email-contact' canvas {...props.emailContact} />
            </div>
            <div className='mt-4'>
              <Element is={SocialMedia} id='social-media-links' canvas {...props.socialMediaLinks} />
            </div>
          </Element>
        </Element>
        <Element is={Row} id='footer-bottom-row' canvas {...props.footerBottomRow}>
          <Element is={Column} id='copyright-column' canvas {...props.copyrightColumn}>
            <Element is={Text} id='copyright-text' canvas {...props.copyrightText} />
          </Element>
        </Element>
      </Element>
    </div>
  )
}

FooterGenzBuilder.craft = {
  props: {
    footerMainRow: {
      desktop: {
        ...defaultRowProps('desktop'),
        backgroundColor: '#1e293b',
        backgroundType: 'COLOR',
        padding: { top: 32, bottom: 32, left: 16, right: 16 },
        colWidths: [4, 4, 4],
        gap: 20,
        height: 'auto'
      },
      tablet: {
        ...defaultRowProps('tablet'),
        backgroundColor: '#1e293b',
        backgroundType: 'COLOR',
        padding: { top: 32, bottom: 32, left: 16, right: 16 },
        colWidths: [4, 4, 4],
        gap: 20,
        height: 'auto'
      },
      mobile: {
        ...defaultRowProps('mobile'),
        backgroundColor: '#1e293b',
        backgroundType: 'COLOR',
        padding: { top: 32, bottom: 32, left: 16, right: 16 },
        isBreakLine: true,
        gap: 20,
        height: 'auto'
      }
    },
    footerBottomRow: {
      desktop: {
        ...defaultRowProps('desktop'),
        backgroundColor: '#1e293b',
        backgroundType: 'COLOR',
        padding: { top: 24, bottom: 48, left: 16, right: 16 },
        colWidths: [12],
        gap: 0,
        height: 'auto'
      },
      tablet: {
        ...defaultRowProps('tablet'),
        backgroundColor: '#1e293b',
        backgroundType: 'COLOR',
        padding: { top: 24, bottom: 48, left: 16, right: 16 },
        colWidths: [12],
        gap: 0,
        height: 'auto'
      },
      mobile: {
        ...defaultRowProps('mobile'),
        backgroundColor: '#1e293b',
        backgroundType: 'COLOR',
        padding: { top: 24, bottom: 48, left: 16, right: 16 },
        isBreakLine: true,
        gap: 0,
        height: 'auto'
      }
    },
    infoColumn: defaultResponseColumnProps,
    linksColumn: defaultResponseColumnProps,
    socialColumn: {
      desktop: { ...defaultRowProps('desktop'), imageFit: 'fill', contentAlign: 'start' },
      tablet: { ...defaultRowProps('tablet'), imageFit: 'fill', contentAlign: 'start' },
      mobile: { ...defaultRowProps('mobile'), imageFit: 'fill', contentAlign: 'start' }
    },
    copyrightColumn: {
      desktop: { ...defaultRowProps('desktop'), contentAlign: 'center' },
      tablet: { ...defaultRowProps('tablet'), contentAlign: 'center' },
      mobile: { ...defaultRowProps('mobile'), contentAlign: 'center' }
    },
    logosColumn: defaultResponseColumnProps,
    expertName: {
      desktop: {
        text: "An An's Deals",
        fontSize: 20,
        fontWeight: 700,
        color: '#ffffff',
        margin: { top: 0, bottom: 16 }
      },
      tablet: {
        text: "An An's Deals",
        fontSize: 20,
        fontWeight: 700,
        color: '#ffffff',
        margin: { top: 0, bottom: 16 }
      },
      mobile: {
        text: "An An's Deals",
        fontSize: 20,
        fontWeight: 700,
        color: '#ffffff',
        textAlign: 'center',
        margin: { top: 0, bottom: 16 }
      }
    },
    description: {
      desktop: {
        text: 'Trang affiliate chính thức của Nguyễn Văn An, đối tác của oneSME by VNPT.',
        fontSize: 16,
        fontWeight: 400,
        color: '#9ca3af'
      },
      tablet: {
        text: 'Trang affiliate chính thức của Nguyễn Văn An, đối tác của oneSME by VNPT.',
        fontSize: 16,
        fontWeight: 400,
        color: '#9ca3af'
      },
      mobile: {
        text: 'Trang affiliate chính thức của Nguyễn Văn An, đối tác của oneSME by VNPT.',
        fontSize: 14,
        fontWeight: 400,
        color: '#9ca3af',
        textAlign: 'center'
      }
    },
    linksTitle: {
      desktop: {
        text: 'Khám phá',
        fontSize: 20,
        fontWeight: 700,
        color: '#ffffff',
        margin: { top: 0, bottom: 16 }
      },
      tablet: {
        text: 'Khám phá',
        fontSize: 20,
        fontWeight: 700,
        color: '#ffffff',
        margin: { top: 0, bottom: 16 }
      },
      mobile: {
        text: 'Khám phá',
        fontSize: 20,
        fontWeight: 700,
        color: '#ffffff',
        textAlign: 'center',
        margin: { top: 0, bottom: 16 }
      }
    },
    aboutLink: {
      desktop: { text: 'Gói cước Di động', fontSize: 16, fontWeight: 400, color: '#9ca3af' },
      tablet: { text: 'Gói cước Di động', fontSize: 16, fontWeight: 400, color: '#9ca3af' },
      mobile: { text: 'Gói cước Di động', fontSize: 14, fontWeight: 400, color: '#9ca3af', textAlign: 'center' }
    },
    servicesLink: {
      desktop: {
        text: 'Internet Gaming',
        fontSize: 16,
        fontWeight: 400,
        color: '#9ca3af',
        margin: { top: 8, bottom: 0 }
      },
      tablet: {
        text: 'Internet Gaming',
        fontSize: 16,
        fontWeight: 400,
        color: '#9ca3af',
        margin: { top: 8, bottom: 0 }
      },
      mobile: {
        text: 'Internet Gaming',
        fontSize: 14,
        fontWeight: 400,
        color: '#9ca3af',
        textAlign: 'center',
        margin: { top: 8, bottom: 0 }
      }
    },
    policyLink: {
      desktop: { text: 'Dịch vụ khác', fontSize: 16, fontWeight: 400, color: '#9ca3af', margin: { top: 8, bottom: 0 } },
      tablet: { text: 'Dịch vụ khác', fontSize: 16, fontWeight: 400, color: '#9ca3af', margin: { top: 8, bottom: 0 } },
      mobile: {
        text: 'Dịch vụ khác',
        fontSize: 14,
        fontWeight: 400,
        color: '#9ca3af',
        textAlign: 'center',
        margin: { top: 8, bottom: 0 }
      }
    },
    socialTitle: {
      desktop: {
        text: 'Kết nối với tôi',
        fontSize: 18,
        fontWeight: 700,
        color: '#ffffff',
        margin: { top: 0, bottom: 16 }
      },
      tablet: {
        text: 'Kết nối với tôi',
        fontSize: 18,
        fontWeight: 700,
        color: '#ffffff',
        margin: { top: 0, bottom: 16 }
      },
      mobile: {
        text: 'Kết nối với tôi',
        fontSize: 18,
        fontWeight: 700,
        color: '#ffffff',
        textAlign: 'center',
        margin: { top: 0, bottom: 16 }
      }
    },
    phoneContact: {
      desktop: {
        ...defaultResponseSocialMediaProps.desktop,
        size: 16,
        iconAlign: 'start',
        fontFamily: 'Inter, sans-serif',
        fontSize: 16,
        color: '#9ca3af',
        socialList: [
          {
            name: '0912345678',
            icon: 'PhoneFilled',
            iconColor: '#9ca3af',
            textColor: '#9ca3af',
            url: '',
            displayType: 'NAME'
          }
        ]
      },
      tablet: {
        ...defaultResponseSocialMediaProps.tablet,
        size: 16,
        iconAlign: 'start',
        fontFamily: 'Inter, sans-serif',
        fontSize: 16,
        color: '#9ca3af',
        socialList: [
          {
            name: '0912345678',
            icon: 'PhoneFilled',
            iconColor: '#9ca3af',
            textColor: '#9ca3af',
            url: '',
            displayType: 'NAME'
          }
        ]
      },
      mobile: {
        ...defaultResponseSocialMediaProps.mobile,
        size: 16,
        iconAlign: 'start',
        fontFamily: 'Inter, sans-serif',
        fontSize: 16,
        color: '#9ca3af',
        socialList: [
          {
            name: '0912345678',
            icon: 'PhoneFilled',
            iconColor: '#9ca3af',
            url: '',
            displayType: 'ICON'
          }
        ]
      }
    },
    emailContact: {
      desktop: {
        ...defaultResponseSocialMediaProps.desktop,
        size: 16,
        iconAlign: 'start',
        fontFamily: 'Inter, sans-serif',
        fontSize: 16,
        color: '#9ca3af',
        socialList: [
          {
            name: '<EMAIL>',
            icon: 'MailFilled',
            iconColor: '#9ca3af',
            textColor: '#9ca3af',
            url: '',
            displayType: 'NAME'
          }
        ]
      },
      tablet: {
        ...defaultResponseSocialMediaProps.tablet,
        size: 16,
        iconAlign: 'start',
        fontFamily: 'Inter, sans-serif',
        fontSize: 16,
        color: '#9ca3af',
        socialList: [
          {
            name: '<EMAIL>',
            icon: 'MailFilled',
            iconColor: '#9ca3af',
            textColor: '#9ca3af',
            url: '',
            displayType: 'NAME'
          }
        ]
      },
      mobile: {
        ...defaultResponseSocialMediaProps.mobile,
        size: 16,
        iconAlign: 'start',
        fontFamily: 'Inter, sans-serif',
        fontSize: 16,
        color: '#9ca3af',
        socialList: [
          {
            name: '<EMAIL>',
            icon: 'MailFilled',
            iconColor: '#9ca3af',
            url: '',
            displayType: 'ICON'
          }
        ]
      }
    },
    socialMediaLinks: {
      desktop: {
        ...defaultResponseSocialMediaProps.desktop,
        size: 24,
        iconAlign: 'start',
        socialList: [
          {
            name: 'Facebook',
            icon: 'FacebookIcon2',
            iconColor: '#9ca3af',
            url: 'https://facebook.com',
            displayType: 'ICON'
          },
          {
            name: 'Instagram',
            icon: 'InstagramOutlined',
            iconColor: '#9ca3af',
            url: 'https://instagram.com',
            displayType: 'ICON'
          },
          {
            name: 'TikTok',
            icon: 'TikTokOutlined',
            iconColor: '#9ca3af',
            url: 'https://tiktok.com',
            displayType: 'ICON'
          }
        ]
      },
      tablet: {
        ...defaultResponseSocialMediaProps.tablet,
        size: 24,
        iconAlign: 'start',
        socialList: [
          {
            name: 'Facebook',
            icon: 'FacebookIcon2',
            iconColor: '#9ca3af',
            url: 'https://facebook.com',
            displayType: 'ICON'
          },
          {
            name: 'Instagram',
            icon: 'InstagramOutlined',
            iconColor: '#9ca3af',
            url: 'https://instagram.com',
            displayType: 'ICON'
          },
          {
            name: 'TikTok',
            icon: 'TikTokOutlined',
            iconColor: '#9ca3af',
            url: 'https://tiktok.com',
            displayType: 'ICON'
          }
        ]
      },
      mobile: {
        ...defaultResponseSocialMediaProps.mobile,
        size: 24,
        iconAlign: 'start',
        socialList: [
          {
            name: 'Facebook',
            icon: 'FacebookIcon2',
            iconColor: '#9ca3af',
            url: 'https://facebook.com',
            displayType: 'ICON'
          },
          {
            name: 'Instagram',
            icon: 'InstagramOutlined',
            iconColor: '#9ca3af',
            url: 'https://instagram.com',
            displayType: 'ICON'
          },
          {
            name: 'TikTok',
            icon: 'TikTokOutlined',
            iconColor: '#9ca3af',
            url: 'https://tiktok.com',
            displayType: 'ICON'
          }
        ]
      }
    },
    copyrightText: {
      desktop: { text: '© 2025. Thiết kế bởi An An.', fontSize: 14, fontWeight: 400, color: '#6b7280' },
      tablet: { text: '© 2025. Thiết kế bởi An An.', fontSize: 14, fontWeight: 400, color: '#6b7280' },
      mobile: {
        text: '© 2025. Thiết kế bởi An An.',
        fontSize: 14,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'center'
      }
    },
    onesmeLogo: {
      desktop: {
        ...defaultImageProps('desktop'),
        imageFit: 'fit',
        align: 'start',
        width: '',
        height: '32px',
        imgSrc: 'https://onesme.vn/assets/images/logo-footer.svg',
        padding: { top: 24, right: 0, bottom: 0, left: 0 }
      },
      tablet: {
        ...defaultImageProps('tablet'),
        imageFit: 'fit',
        align: 'start',
        width: '',
        height: '32px',
        imgSrc: 'https://onesme.vn/assets/images/logo-footer.svg',
        padding: { top: 24, right: 0, bottom: 0, left: 0 }
      },
      mobile: {
        ...defaultImageProps('mobile'),
        imageFit: 'fit',
        align: 'start',
        width: '',
        height: '32px',
        imgSrc: 'https://onesme.vn/assets/images/logo-footer.svg',
        padding: { top: 24, right: 0, bottom: 0, left: 0 }
      }
    },
    vnptLogo: {
      desktop: {
        imgSrc: 'https://www.vnpt.com.vn/img/logo-vnpt.svg'
        // width: 24,
        // height: 24,
        // filter: 'brightness(0) invert(1)'
      },
      tablet: {
        imgSrc: 'https://www.vnpt.com.vn/img/logo-vnpt.svg'
      },
      mobile: {
        imgSrcMobile: 'https://www.vnpt.com.vn/img/logo-vnpt.svg'
      }
    },
    spacer1: {
      desktop: { height: 16 },
      tablet: { height: 16 },
      mobile: { height: 16 }
    },
    spacer2: {
      desktop: { height: 16 },
      tablet: { height: 16 },
      mobile: { height: 16 }
    },
    spacer3: {
      desktop: { height: 16 },
      tablet: { height: 16 },
      mobile: { height: 16 }
    }
  },
  rules: {
    canDrag: () => true,
    canMoveIn: () => false
  }
}
