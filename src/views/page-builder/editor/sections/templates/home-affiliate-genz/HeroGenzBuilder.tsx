'use client'

import { Element, useNode, type UserComponent } from '@craftjs/core'

import { BannerList } from '@/components/page-builder/selectors'
import { defaultResponsiveGenZBannerListProps } from '@/constants/page-builder/bannerListGenZ'

export const HeroGenzBuilder: UserComponent = props => {
  const {
    connectors: { connect, drag }
  } = useNode()

  return (
    <div
      className='w-full'
      ref={(ref: HTMLDivElement | null) => {
        if (ref) {
          connect(drag(ref))
        }
      }}
    >
      <Element is={BannerList} id='hero-banner' canvas {...props.heroBanner} />
    </div>
  )
}

HeroGenzBuilder.craft = {
  props: {
    heroBanner: defaultResponsiveGenZBannerListProps
  },
  rules: {
    canDrag: () => true,
    canMoveIn: () => false
  }
}
