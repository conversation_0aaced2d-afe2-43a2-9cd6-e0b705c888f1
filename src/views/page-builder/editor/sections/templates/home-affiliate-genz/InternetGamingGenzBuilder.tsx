'use client'

//#region Imports
import { Element, useNode, type UserComponent } from '@craftjs/core'

import { Button, Column, IconLibrary, Image, Row, Text } from '@/components/page-builder/selectors'
import { defaultResponseColumnProps, defaultRowProps } from '@/constants/page-builder/row'
import { defaultButtonProps, defaultImageProps } from '@/constants/page-builder'
//#endregion

//#region Data Constants
// Dữ liệu Internet Gaming được cấu trúc hóa với nội dung tiếng Việt
const INTERNET_GAMING_DATA = {
  section: {
    title: 'Internet Gaming - Cân Mọi Tựa Game',
    image: 'https://lapwifivnpt.vn/wp-content/uploads/2023/07/home-mesh-2-vnpt.png'
  },
  product: {
    title: 'Home Mesh 2+',
    description: '<PERSON> thấp, tốc độ cao, leo rank thần tốc. Combo Internet và truyền hình MyTV cho cả nhà cùng vui.',
    features: [
      { id: 'speed', icon: 'RocketFilled', text: 'Tốc độ 150Mbps' },
      { id: 'gaming', icon: 'FundViewOutlined', text: 'Tối ưu cho game, giảm giật lag' },
      { id: 'wifi', icon: 'WifiOutlined', text: 'Tối ưu cho game, giảm giật lag' }
    ],
    cta: 'Nâng cấp đường truyền'
  }
}
//#endregion

//#region Helper Functions
// Hàm hỗ trợ tạo responsive props
const createResponsiveProps = (desktopProps: any, tabletProps?: any, mobileProps?: any) => ({
  desktop: desktopProps,
  tablet: tabletProps || desktopProps,
  mobile: mobileProps || tabletProps || desktopProps
})

// Hàm tạo props cho text elements
const createTextProps = (
  text: string,
  fontSize: { desktop: number; tablet?: number; mobile: number },
  options: any = {}
) => {
  const baseProps = {
    text,
    fontWeight: options.fontWeight || 400,
    color: options.color || '#000000',
    textAlign: options.textAlign || 'left',
    margin: options.margin || { top: 0, bottom: 16 },
    ...options.extra
  }

  return createResponsiveProps(
    { ...baseProps, fontSize: fontSize.desktop },
    {
      ...baseProps,
      fontSize: fontSize.tablet || fontSize.desktop,
      textAlign: options.tabletAlign || baseProps.textAlign
    },
    {
      ...baseProps,
      fontSize: fontSize.mobile,
      textAlign: options.mobileAlign || options.tabletAlign || baseProps.textAlign
    }
  )
}

// Hàm tạo props cho image elements
const createImageProps = (imgSrc: string, height: { desktop: number; tablet?: number; mobile: number }) => {
  const baseProps = {
    ...defaultImageProps('desktop'),
    imgSrc,
    width: 'auto',
    imageFit: 'fill',
    border: { style: 'solid', color: '#212D6E', thickness: 0, radius: 16 }
  }

  return createResponsiveProps(
    { ...baseProps, height: height.desktop, align: 'center' },
    { ...baseProps, height: height.tablet || height.desktop },
    { ...baseProps, height: height.mobile }
  )
}

// Hàm tạo props cho icon library elements
const createIconLibraryProps = (icon: string, title: string, options: any = {}) => {
  const baseProps = {
    icon,
    iconColor: '#4B5563',
    iconAlign: 'start',
    size: 24,
    title,
    titleFontSize: 16,
    titleFontWeight: 500,
    titleColor: options.titleColor || '#4B5563',
    titleAlign: 'left',
    showIcon: { desktop: true, tablet: true, mobile: true },
    showTitle: { desktop: true, tablet: true, mobile: true },
    showDesc: { desktop: false, tablet: false, mobile: false },
    backgroundColor: '#ffffff',
    backgroundType: 'COLOR',
    border: { style: 'none' },
    padding: { top: 0, right: 0, bottom: 0, left: 0 },
    margin: { top: 0, right: 0, bottom: 12, left: 0 }
  }

  return createResponsiveProps(
    baseProps,
    {
      ...baseProps,
      iconColor: '#9ca3af',
      titleColor: '#9ca3af',
      titleAlign: 'center',
      border: { style: 'none', color: '#212d6e', thickness: 0, radius: 0 }
    },
    {
      ...baseProps,
      iconColor: '#9ca3af',
      titleColor: '#9ca3af',
      titleAlign: 'center',
      border: { style: 'none', color: '#212d6e', thickness: 0, radius: 0 }
    }
  )
}

// Hàm tạo props cho button elements
const createButtonProps = (text: string) => {
  const baseProps = {
    ...defaultButtonProps('desktop'),
    text,
    fontWeight: 700,
    color: '#ffffff',
    size: 'large',
    buttonBackgroundColor: '#A855F7',
    radius: 8,
    containerClassName: 'mt-3 transition-all duration-300 hover:scale-105',
    className:
      'bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-bold py-4'
  }

  return createResponsiveProps(
    {
      ...baseProps,
      fontSize: 20,
      padding: { top: 28, right: 32, bottom: 28, left: 32 },
      className: baseProps.className + ' text-xl'
    },
    {
      ...baseProps,
      fontSize: 18,
      padding: { top: 14, right: 28, bottom: 14, left: 28 },
      className: baseProps.className + ' text-lg'
    },
    {
      ...baseProps,
      fontSize: 16,
      padding: { top: 12, right: 24, bottom: 12, left: 24 },
      className: baseProps.className + ' text-base'
    }
  )
}
//#endregion

//#region Craft Props Generator
// Hàm tạo craft props một cách động
const generateCraftProps = () => {
  const props: any = {}

  // Title row props
  props.titleRow = {
    desktop: {
      ...defaultRowProps('desktop'),
      backgroundColor: '#ffffff',
      backgroundType: 'COLOR',
      padding: { top: 64, bottom: 40, left: 16, right: 16 },
      height: 'auto',
      justifyContent: 'center'
    },
    tablet: {
      ...defaultRowProps('tablet'),
      backgroundColor: '#ffffff',
      backgroundType: 'COLOR',
      padding: { top: 64, bottom: 40, left: 16, right: 16 },
      height: 'auto',
      justifyContent: 'center'
    },
    mobile: {
      ...defaultRowProps('mobile'),
      backgroundColor: '#ffffff',
      backgroundType: 'COLOR',
      padding: { top: 48, bottom: 32, left: 16, right: 16 },
      height: 'auto',
      justifyContent: 'center'
    }
  }

  // Title column props
  props.titleColumn = defaultResponseColumnProps

  // Content row props
  props.contentRow = {
    desktop: {
      ...defaultRowProps('desktop'),
      backgroundColor: '#FFFFFF',
      backgroundType: 'COLOR',
      padding: { top: 0, bottom: 64, left: 16, right: 16 },
      colWidths: [6, 6],
      gap: 8,
      height: 'auto'
    },
    tablet: {
      ...defaultRowProps('tablet'),
      backgroundColor: '#FFFFFF',
      backgroundType: 'COLOR',
      padding: { top: 0, bottom: 64, left: 16, right: 16 },
      colWidths: [6, 6],
      gap: 8,
      height: 'auto'
    },
    mobile: {
      ...defaultRowProps('mobile'),
      backgroundColor: '#FFFFFF',
      backgroundType: 'COLOR',
      padding: { top: 0, bottom: 48, left: 16, right: 16 },
      isBreakLine: true,
      gap: 8,
      height: 'auto'
    }
  }

  // Content column props
  props.imageColumn = {
    desktop: { ...defaultResponseColumnProps.desktop, justifyContent: 'center' },
    tablet: { ...defaultResponseColumnProps.tablet, justifyContent: 'center' },
    mobile: { ...defaultResponseColumnProps.mobile, justifyContent: 'center' }
  }

  props.productColumn = {
    desktop: {
      ...defaultResponseColumnProps.desktop,
      justifyContent: 'center',
      backgroundColor: '#ffffff',
      backgroundType: 'COLOR',
      padding: { top: 32, bottom: 32, left: 32, right: 32 },
      shadow: 3,
      border: { style: 'solid', thickness: 0, color: '#f97316', radius: 16 }
    },
    tablet: {
      ...defaultResponseColumnProps.tablet,
      justifyContent: 'center',
      padding: { top: 32, bottom: 32, left: 32, right: 32 },
      shadow: 3,
      border: { style: 'solid', thickness: 0, color: '#f97316', radius: 16 }
    },
    mobile: {
      ...defaultResponseColumnProps.mobile,
      justifyContent: 'center',
      padding: { top: 32, bottom: 32, left: 32, right: 32 },
      shadow: 3,
      border: { style: 'solid', thickness: 0, color: '#f97316', radius: 16 }
    }
  }

  // Section title props
  props.sectionTitle = createTextProps(
    INTERNET_GAMING_DATA.section.title,
    { desktop: 40, tablet: 32, mobile: 28 },
    {
      fontWeight: 800,
      color: '#A855F7', // Màu đầu tiên của gradient để hiển thị trong panel
      textAlign: 'center',
      tabletAlign: 'center',
      mobileAlign: 'center',
      extra: {
        className:
          'text-center mb-8 font-extrabold bg-gradient-to-r from-purple-500 to-pink-500 bg-clip-text text-transparent'
      }
    }
  )

  // Gaming image props
  props.gamingImage = createImageProps(INTERNET_GAMING_DATA.section.image, { desktop: 400, tablet: 300, mobile: 250 })

  // Product title props
  props.productTitle = createTextProps(
    INTERNET_GAMING_DATA.product.title,
    { desktop: 32, tablet: 28, mobile: 24 },
    {
      fontWeight: 800,
      color: '#1f2937',
      textAlign: 'left'
    }
  )

  // Product description props
  props.productDescription = createTextProps(
    INTERNET_GAMING_DATA.product.description,
    { desktop: 16, mobile: 14 },
    {
      color: '#4b5563',
      textAlign: 'left',
      margin: { top: 0, bottom: 24 }
    }
  )

  // Dynamic feature props
  INTERNET_GAMING_DATA.product.features.forEach(feature => {
    props[`${feature.id}Feature`] = createIconLibraryProps(feature.icon, feature.text)
  })

  // Product CTA props
  props.productCta = createButtonProps(INTERNET_GAMING_DATA.product.cta)

  return props
}
//#endregion

//#region Main Component
export const InternetGamingGenzBuilder: UserComponent = props => {
  const {
    connectors: { connect, drag }
  } = useNode()

  return (
    <div
      className='w-full'
      ref={(ref: HTMLDivElement | null) => {
        if (ref) {
          connect(drag(ref))
        }
      }}
    >
      <Element is={Row} id='title-row' canvas {...props.titleRow}>
        <Element is={Column} id='title-column' canvas {...props.titleColumn}>
          <Element is={Text} id='section-title' canvas {...props.sectionTitle} />
        </Element>
      </Element>
      <Element is={Row} id='content-row' canvas {...props.contentRow}>
        <Element is={Column} id='image-column' canvas {...props.imageColumn}>
          <Element is={Image} id='gaming-image' canvas {...props.gamingImage} />
        </Element>
        <Element is={Column} id='product-column' canvas {...props.productColumn}>
          <Element is={Text} id='product-title' canvas {...props.productTitle} />
          <Element is={Text} id='product-description' canvas {...props.productDescription} />
          {INTERNET_GAMING_DATA.product.features.map(feature => (
            <Element
              key={feature.id}
              is={IconLibrary}
              id={`${feature.id}-feature`}
              canvas
              {...props[`${feature.id}Feature`]}
            />
          ))}
          <Element is={Button} id='product-cta' canvas {...props.productCta} />
        </Element>
      </Element>
    </div>
  )
}
//#endregion

//#region Craft Configuration
InternetGamingGenzBuilder.craft = {
  props: generateCraftProps(),
  rules: {
    canDrag: () => true,
    canMoveIn: () => false
  }
}
//#endregion
