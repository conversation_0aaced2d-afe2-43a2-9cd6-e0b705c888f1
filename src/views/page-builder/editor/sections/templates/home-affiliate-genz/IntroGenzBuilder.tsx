'use client'

import { Element, useNode, type UserComponent } from '@craftjs/core'

import { Container, Column, Image, Row, Spacer, Text } from '@/components/page-builder/selectors'
import { defaultResponseColumnProps, defaultResponseRowProps } from '@/constants/page-builder/row'
import { defaultResponseSpacerProps } from '@/constants/page-builder/spacer'

export const IntroGenzBuilder: UserComponent = props => {
  const {
    connectors: { connect, drag }
  } = useNode()

  return (
    <div
      className='z-10 w-full'
      ref={(ref: HTMLDivElement | null) => {
        if (ref) {
          connect(drag(ref))
        }
      }}
    >
      <Element is={Container} id='container' canvas>
        <Element is={Row} id='intro-row' canvas {...props.introRow}>
          <Element is={Column} id='intro-column' canvas {...props.introColumn}>
            <div className='w-[128px] shrink-0'>
              <Element is={Image} id='avatar' canvas {...props.avatar} />
            </div>

            <Element is={Spacer} id='intro-spacer' canvas {...props.introSpacer} />
            <Element is={Text} id='intro-title' canvas {...props.introTitle} />
            <Element is={Text} id='intro-subtitle' canvas {...props.introSubtitle} />
          </Element>
        </Element>
      </Element>
    </div>
  )
}

IntroGenzBuilder.craft = {
  props: {
    introRow: {
      desktop: {
        ...defaultResponseRowProps.desktop,
        padding: { top: 80, right: 16, bottom: 80, left: 16 },
        colWidths: [12]
      },
      tablet: {
        ...defaultResponseRowProps.tablet,
        padding: { top: 60, right: 16, bottom: 60, left: 16 },
        colWidths: [12]
      },
      mobile: {
        ...defaultResponseRowProps.mobile,
        padding: { top: 40, right: 16, bottom: 40, left: 16 },
        colWidths: [12]
      }
    },
    introColumn: {
      desktop: {
        ...defaultResponseColumnProps.desktop,
        contentAlign: 'center',
        justifyContent: 'center'
      },
      tablet: {
        ...defaultResponseColumnProps.tablet,
        contentAlign: 'center',
        justifyContent: 'center'
      },
      mobile: {
        ...defaultResponseColumnProps.mobile,
        contentAlign: 'center',
        justifyContent: 'center'
      }
    },
    avatar: {
      desktop: {
        imgSrc: 'https://placehold.co/128x128/7f5af0/ffffff?text=An',
        width: 128,
        height: 128,
        border: { radius: 999, thickness: 4, color: 'white', style: 'solid' },
        margin: { top: -144, right: 0, bottom: 24, left: 0 },
        imageFit: 'fill',
        align: 'center',
        typeUpload: 'EXIST'
      },
      tablet: {
        imgSrc: 'https://placehold.co/128x128/7f5af0/ffffff?text=An',
        width: 128,
        height: 128,
        border: { radius: 999, thickness: 4, color: 'white', style: 'solid' },
        margin: { top: -144, right: 0, bottom: 24, left: 0 },
        imageFit: 'fill',
        align: 'center',
        typeUpload: 'EXIST'
      },
      mobile: {
        imgSrcMobile: 'https://placehold.co/128x128/7f5af0/ffffff?text=An',
        width: 96,
        height: 96,
        border: { radius: 48, thickness: 4, color: 'white', style: 'solid' },
        margin: { top: -144, right: 0, bottom: 24, left: 0 },
        imageFit: 'fill',
        align: 'center',
        typeUpload: 'EXIST'
      }
    },
    introSpacer: {
      desktop: {
        ...defaultResponseSpacerProps.desktop,
        height: 0
      },
      tablet: {
        ...defaultResponseSpacerProps.tablet,
        height: 0
      },
      mobile: {
        ...defaultResponseSpacerProps.mobile,
        height: 0
      }
    },
    introTitle: {
      desktop: {
        text: 'Hey bạn, mình là An An đây!',
        fontSize: 32,
        fontWeight: 700,
        color: '#1f2937',
        textAlign: 'center',
        className: 'text-3xl font-bold text-gray-800'
      },
      tablet: {
        text: 'Hey bạn, mình là An An đây!',
        fontSize: 28,
        fontWeight: 700,
        color: '#1f2937',
        textAlign: 'center',
        className: 'text-3xl font-bold text-gray-800'
      },
      mobile: {
        text: 'Hey bạn, mình là An An đây!',
        fontSize: 24,
        fontWeight: 700,
        color: '#1f2937',
        textAlign: 'center',
        className: 'text-3xl font-bold text-gray-800'
      }
    },
    introSubtitle: {
      desktop: {
        text: 'Mình ở đây để giúp bạn tìm được những gói cước xịn xò nhất!',
        fontSize: 18,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'center',
        className: 'text-lg text-gray-600 mt-2'
      },
      tablet: {
        text: 'Mình ở đây để giúp bạn tìm được những gói cước xịn xò nhất!',
        fontSize: 16,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'center',
        className: 'text-lg text-gray-600 mt-2'
      },
      mobile: {
        text: 'Mình ở đây để giúp bạn tìm được những gói cước xịn xò nhất!',
        fontSize: 16,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'center',
        className: 'text-lg text-gray-600 mt-2'
      }
    }
  },
  rules: {
    canDrag: () => true,
    canMoveIn: () => true
  }
}
