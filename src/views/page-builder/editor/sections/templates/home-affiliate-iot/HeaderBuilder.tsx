'use client'

import { Element, useNode, type UserComponent } from '@craftjs/core'

import { Image, Text, Row, Column, SocialMedia } from '@/components/page-builder/selectors'
import { defaultResponseRowProps, defaultResponseColumnProps } from '@/constants/page-builder/row'
import { defaultImageProps } from '@/constants/page-builder'
import { defaultResponseSocialMediaProps } from '@/constants/page-builder/socialMedia'

export const HeaderBuilder: UserComponent = props => {
  const {
    connectors: { connect, drag }
  } = useNode()

  return (
    <div
      className='w-full'
      ref={(ref: HTMLDivElement | null) => {
        if (ref) {
          connect(drag(ref))
        }
      }}
    >
      <Element is={Row} canvas id='header-row' {...props.headerRow}>
        <Element is={Column} canvas id='logo-column' {...props.logoColumn}>
          <div className='flex items-center gap-4'>
            <div className='w-[94px] shrink-0'>
              <Element is={Image} id='image1' {...props.logo} />
            </div>
            <div className='block h-8 border-l border-solid border-gray-300 sm:hidden'></div>
            <Element is={Text} id='expert-name' canvas {...props.expertName} />
          </div>
        </Element>
        <Element is={Column} canvas id='contact-column' {...props.contactColumn}>
          <div className='flex items-center gap-6 text-sm text-gray-600'>
            <Element is={SocialMedia} id='contact-social-media' canvas {...props.contactSocialMedia} />
          </div>
        </Element>
      </Element>
    </div>
  )
}

HeaderBuilder.craft = {
  displayName: 'Header IoT',
  props: {
    logo: {
      desktop: {
        ...defaultImageProps('desktop'),
        imgSrc: '/assets/images/logo.svg',
        width: '',
        height: '36px'
      },
      tablet: {
        ...defaultImageProps('tablet'),
        imgSrc: '/assets/images/logo.svg',
        width: '',
        height: '36px'
      },
      mobile: {
        ...defaultImageProps('mobile'),
        imgSrcMobile: '/assets/images/logo.svg',
        width: '',
        height: '36px'
      }
    },
    headerRow: {
      desktop: {
        ...defaultResponseRowProps.desktop,
        padding: { top: 12, right: 0, bottom: 12, left: 0 },
        colWidths: [8, 4], // Logo column rộng hơn, contact column nhỏ hơn
        gap: 4,
        contentAlign: 'center',
        justifyContent: 'space-between'
      },
      tablet: {
        ...defaultResponseRowProps.tablet,
        padding: { top: 12, right: 0, bottom: 12, left: 0 },
        colWidths: [8, 4],
        gap: 4,
        contentAlign: 'center',
        justifyContent: 'space-between'
      },
      mobile: {
        ...defaultResponseRowProps.mobile,
        padding: { top: 12, right: 0, bottom: 12, left: 0 },
        colWidths: [12], // Mobile: full width, stack vertically
        gap: 3,
        contentAlign: 'center',
        justifyContent: 'center'
        // isBreakLine: true // Cho phép xuống dòng trên mobile
      }
    },
    logoColumn: {
      desktop: {
        ...defaultResponseColumnProps.desktop,
        contentAlign: 'start',
        justifyContent: 'center'
      },
      tablet: {
        ...defaultResponseColumnProps.tablet,
        contentAlign: 'start',
        justifyContent: 'center'
      },
      mobile: {
        ...defaultResponseColumnProps.mobile,
        contentAlign: 'center',
        justifyContent: 'center',
        margin: { top: 0, right: 0, bottom: 12, left: 0 } // Thêm margin bottom cho mobile
      }
    },
    contactColumn: {
      desktop: {
        ...defaultResponseColumnProps.desktop,
        contentAlign: 'end',
        justifyContent: 'center'
      },
      tablet: {
        ...defaultResponseColumnProps.tablet,
        contentAlign: 'end',
        justifyContent: 'center'
      },
      mobile: {
        ...defaultResponseColumnProps.mobile,
        contentAlign: 'center',
        justifyContent: 'center'
      }
    },
    expertName: {
      desktop: {
        text: 'Nguyễn Văn An - Chuyên gia IoT',
        fontSize: 18,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'left',
        className: 'font-bold text-lg text-primary-blue'
      },
      tablet: {
        text: 'Nguyễn Văn An - Chuyên gia IoT',
        fontSize: 18,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'left',
        className: 'font-bold text-lg text-primary-blue'
      },
      mobile: {
        text: 'Nguyễn Văn An - Chuyên gia IoT',
        fontSize: 16,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'center',
        className: 'font-bold text-lg text-primary-blue'
      }
    },
    contactSocialMedia: {
      desktop: {
        ...defaultResponseSocialMediaProps.desktop,
        size: 14,
        iconAlign: 'start',
        fontFamily: 'Inter, sans-serif',
        fontSize: 14,
        socialList: [
          {
            name: '0912345678',
            icon: 'PhoneFilled',
            iconColor: '#4b5564',
            url: '',
            displayType: 'NAME'
          },
          {
            name: '<EMAIL>',
            icon: 'MailFilled',
            iconColor: '#4b5564',
            url: '',
            displayType: 'NAME'
          }
        ]
      },
      tablet: {
        ...defaultResponseSocialMediaProps.tablet,
        size: 14,
        iconAlign: 'start',
        fontFamily: 'Inter, sans-serif',
        fontSize: 14,
        socialList: [
          {
            name: '0912345678',
            icon: 'PhoneFilled',
            iconColor: '#4b5564',
            url: '',
            displayType: 'NAME'
          },
          {
            name: '<EMAIL>',
            icon: 'MailFilled',
            iconColor: '#4b5564',
            url: '',
            displayType: 'NAME'
          }
        ]
      },
      mobile: {
        ...defaultResponseSocialMediaProps.mobile,
        size: 14,
        iconAlign: 'start',
        fontFamily: 'Inter, sans-serif',
        fontSize: 14,
        socialList: [
          {
            name: '0912345678',
            icon: 'PhoneFilled',
            iconColor: '#4b5564',
            url: '',
            displayType: 'ICON'
          },
          {
            name: '<EMAIL>',
            icon: 'MailFilled',
            iconColor: '#4b5564',
            url: '',
            displayType: 'ICON'
          }
        ]
      }
    },
  },
  rules: {
    canDrag: () => true,
    canMoveIn: () => false
  }
}
