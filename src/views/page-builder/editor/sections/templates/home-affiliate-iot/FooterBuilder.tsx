'use client'

import { Element, useNode, type UserComponent } from '@craftjs/core'

import { Column, Container, Image, Row, Spacer, Text, SocialMedia } from '@/components/page-builder/selectors'
import { defaultImageProps } from '@/constants/page-builder'
import { defaultResponseColumnProps, defaultRowProps } from '@/constants/page-builder/row'
import { defaultResponseSocialMediaProps } from '@/constants/page-builder/socialMedia'

export const FooterBuilder: UserComponent = props => {
  const {
    connectors: { connect, drag }
  } = useNode()

  return (
    <div
      className='w-full'
      ref={(ref: HTMLDivElement | null) => {
        if (ref) {
          connect(drag(ref))
        }
      }}
    >
      <Element is={Container} id='container' canvas>
        <Element is={Row} id='footer-main-row' canvas {...props.footerMainRow}>
          <Element is={Column} id='info-column' canvas {...props.infoColumn}>
            <Element is={Text} id='expert-name' canvas {...props.expertName} />
            <Element is={Spacer} id='spacer1' canvas {...props.spacer1} />
            <Element is={Text} id='description' canvas {...props.description} />
          </Element>
          <Element is={Column} id='links-column' canvas {...props.linksColumn}>
            <Element is={Text} id='links-title' canvas {...props.linksTitle} />
            <Element is={Spacer} id='spacer2' canvas {...props.spacer2} />
            <Element is={Text} id='about-link' canvas {...props.aboutLink} />
            <Element is={Text} id='services-link' canvas {...props.servicesLink} />
            <Element is={Text} id='policy-link' canvas {...props.policyLink} />
          </Element>
          <Element is={Column} id='social-column' canvas {...props.socialColumn}>
            <Element is={Text} id='social-title' canvas {...props.socialTitle} />
            <Element is={Spacer} id='spacer3' canvas {...props.spacer3} />
            <div className='flex gap-2'>
              <Element is={SocialMedia} id='social-media-links' canvas {...props.socialMediaLinks} />
            </div>
          </Element>
        </Element>
        <Element is={Row} id='footer-bottom-row' canvas {...props.footerBottomRow}>
          <Element is={Column} id='copyright-column' canvas {...props.copyrightColumn}>
            <Element is={Text} id='copyright-text' canvas {...props.copyrightText} />
          </Element>
          <Element is={Column} id='logos-column' canvas {...props.logosColumn}>
            <Element is={Image} id='onesme-logo' canvas {...props.onesmeLogo} />
          </Element>
        </Element>
      </Element>
    </div>
  )
}

FooterBuilder.craft = {
  props: {
    footerMainRow: {
      desktop: {
        ...defaultRowProps('desktop'),
        backgroundColor: '#374151',
        backgroundType: 'COLOR',
        padding: { top: 32, bottom: 32, left: 16, right: 16 },
        colWidths: [4, 4, 4],
        gap: 20,
        height: 'auto'
      },
      tablet: {
        ...defaultRowProps('tablet'),
        backgroundColor: '#374151',
        backgroundType: 'COLOR',
        padding: { top: 32, bottom: 32, left: 16, right: 16 },
        colWidths: [4, 4, 4],
        gap: 20,
        height: 'auto'
      },
      mobile: {
        ...defaultRowProps('mobile'),
        backgroundColor: '#374151',
        backgroundType: 'COLOR',
        padding: { top: 32, bottom: 32, left: 16, right: 16 },
        isBreakLine: true,
        gap: 20,
        height: 'auto'
      }
    },
    footerBottomRow: {
      desktop: {
        ...defaultRowProps('desktop'),
        backgroundColor: '#374151',
        backgroundType: 'COLOR',
        padding: { top: 24, bottom: 24, left: 16, right: 16 },
        colWidths: [8, 4],
        gap: 0,
        height: 'auto'
      },
      tablet: {
        ...defaultRowProps('tablet'),
        backgroundColor: '#374151',
        backgroundType: 'COLOR',
        padding: { top: 24, bottom: 24, left: 16, right: 16 },
        colWidths: [8, 4],
        gap: 0,
        height: 'auto'
      },
      mobile: {
        ...defaultRowProps('mobile'),
        backgroundColor: '#374151',
        backgroundType: 'COLOR',
        padding: { top: 24, bottom: 24, left: 16, right: 16 },
        isBreakLine: true,
        gap: 0,
        height: 'auto'
      }
    },
    infoColumn: defaultResponseColumnProps,
    linksColumn: defaultResponseColumnProps,
    socialColumn: {
      desktop: { ...defaultRowProps('desktop'), imageFit: 'fill', contentAlign: 'start' },
      tablet: { ...defaultRowProps('tablet'), imageFit: 'fill', contentAlign: 'start' },
      mobile: { ...defaultRowProps('mobile'), imageFit: 'fill', contentAlign: 'start' }
    },
    copyrightColumn: defaultResponseColumnProps,
    logosColumn: defaultResponseColumnProps,
    expertName: {
      desktop: { text: 'Nguyễn Văn An', fontSize: 18, fontWeight: 700, color: '#ffffff' },
      tablet: { text: 'Nguyễn Văn An', fontSize: 18, fontWeight: 700, color: '#ffffff' },
      mobile: { text: 'Nguyễn Văn An', fontSize: 18, fontWeight: 700, color: '#ffffff', textAlign: 'center' }
    },
    description: {
      desktop: {
        text: 'Đối tác bán hàng chính thức của oneSME by VNPT. Cam kết mang đến dịch vụ và hỗ trợ tốt nhất.',
        fontSize: 16,
        fontWeight: 400,
        color: '#9ca3af'
      },
      tablet: {
        text: 'Đối tác bán hàng chính thức của oneSME by VNPT. Cam kết mang đến dịch vụ và hỗ trợ tốt nhất.',
        fontSize: 16,
        fontWeight: 400,
        color: '#9ca3af'
      },
      mobile: {
        text: 'Đối tác bán hàng chính thức của oneSME by VNPT. Cam kết mang đến dịch vụ và hỗ trợ tốt nhất.',
        fontSize: 14,
        fontWeight: 400,
        color: '#9ca3af',
        textAlign: 'center'
      }
    },
    linksTitle: {
      desktop: { text: 'Liên kết nhanh', fontSize: 18, fontWeight: 700, color: '#ffffff' },
      tablet: { text: 'Liên kết nhanh', fontSize: 18, fontWeight: 700, color: '#ffffff' },
      mobile: { text: 'Liên kết nhanh', fontSize: 18, fontWeight: 700, color: '#ffffff', textAlign: 'center' }
    },
    aboutLink: {
      desktop: { text: 'Về chúng tôi', fontSize: 16, fontWeight: 400, color: '#9ca3af' },
      tablet: { text: 'Về chúng tôi', fontSize: 16, fontWeight: 400, color: '#9ca3af' },
      mobile: { text: 'Về chúng tôi', fontSize: 14, fontWeight: 400, color: '#9ca3af', textAlign: 'center' }
    },
    servicesLink: {
      desktop: {
        text: 'Dịch vụ',
        fontSize: 16,
        fontWeight: 400,
        color: '#9ca3af',
        margin: { top: 4, bottom: 0 }
      },
      tablet: { text: 'Dịch vụ', fontSize: 16, fontWeight: 400, color: '#9ca3af', margin: { top: 4, bottom: 0 } },
      mobile: {
        text: 'Dịch vụ',
        fontSize: 14,
        fontWeight: 400,
        color: '#9ca3af',
        textAlign: 'center',
        margin: { top: 4, bottom: 0 }
      }
    },
    policyLink: {
      desktop: { text: 'Chính sách', fontSize: 16, fontWeight: 400, color: '#9ca3af', margin: { top: 4, bottom: 0 } },
      tablet: { text: 'Chính sách', fontSize: 16, fontWeight: 400, color: '#9ca3af', margin: { top: 4, bottom: 0 } },
      mobile: {
        text: 'Chính sách',
        fontSize: 14,
        fontWeight: 400,
        color: '#9ca3af',
        textAlign: 'center',
        margin: { top: 4, bottom: 0 }
      }
    },
    socialTitle: {
      desktop: { text: 'Kết nối với tôi', fontSize: 18, fontWeight: 700, color: '#ffffff' },
      tablet: { text: 'Kết nối với tôi', fontSize: 18, fontWeight: 700, color: '#ffffff' },
      mobile: { text: 'Kết nối với tôi', fontSize: 18, fontWeight: 700, color: '#ffffff', textAlign: 'center' }
    },
    socialMediaLinks: {
      desktop: {
        ...defaultResponseSocialMediaProps.desktop,
        size: 24,
        iconAlign: 'start',
        socialList: [
          {
            name: 'Facebook',
            icon: 'FacebookIcon2',
            iconColor: '#9CA3AF',
            url: '',
            displayType: 'ICON'
          },
          {
            name: 'Email',
            icon: 'MailFilled',
            iconColor: '#9CA3AF',
            url: '',
            displayType: 'ICON'
          }
        ]
      },
      tablet: {
        ...defaultResponseSocialMediaProps.tablet,
        size: 24,
        iconAlign: 'start',
        socialList: [
          {
            name: 'Facebook',
            icon: 'FacebookIcon2',
            iconColor: '#9CA3AF',
            url: '',
            displayType: 'ICON'
          },
          {
            name: 'Email',
            icon: 'MailFilled',
            iconColor: '#9CA3AF',
            url: '',
            displayType: 'ICON'
          }
        ]
      },
      mobile: {
        ...defaultResponseSocialMediaProps.mobile,
        size: 24,
        iconAlign: 'start',
        socialList: [
          {
            name: 'Facebook',
            icon: 'FacebookIcon2',
            iconColor: '#9CA3AF',
            url: '',
            displayType: 'ICON'
          },
          {
            name: 'Email',
            icon: 'MailFilled',
            iconColor: '#9CA3AF',
            url: '',
            displayType: 'ICON'
          }
        ]
      }
    },
    copyrightText: {
      desktop: { text: '© 2025. Trang tư vấn của Nguyễn Văn An.', fontSize: 14, fontWeight: 400, color: '#6b7280' },
      tablet: { text: '© 2025. Trang tư vấn của Nguyễn Văn An.', fontSize: 14, fontWeight: 400, color: '#6b7280' },
      mobile: {
        text: '© 2025. Trang tư vấn của Nguyễn Văn An.',
        fontSize: 14,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'center'
      }
    },
    onesmeLogo: {
      desktop: {
        ...defaultImageProps('desktop'),
        imageFit: 'fit',
        align: 'start',
        height: '36px',
        imgSrc: 'https://onesme.vn/assets/images/logo-footer.svg',
        padding: { top: 0, right: 24, bottom: 0, left: 24 }
      },
      tablet: {
        ...defaultImageProps('tablet'),
        imageFit: 'fit',
        align: 'start',
        height: '36px',
        imgSrc: 'https://onesme.vn/assets/images/logo-footer.svg',
        padding: { top: 0, right: 24, bottom: 0, left: 24 }
      },
      mobile: {
        ...defaultImageProps('mobile'),
        imageFit: 'fit',
        align: 'start',
        height: '36px',
        imgSrc: 'https://onesme.vn/assets/images/logo-footer.svg',
        padding: { top: 0, right: 24, bottom: 0, left: 24 }
      }
    },
    vnptLogo: {
      desktop: {
        imgSrc: 'https://www.vnpt.com.vn/img/logo-vnpt.svg'
        // width: 24,
        // height: 24,
        // filter: 'brightness(0) invert(1)'
      },
      tablet: {
        imgSrc: 'https://www.vnpt.com.vn/img/logo-vnpt.svg'
      },
      mobile: {
        imgSrcMobile: 'https://www.vnpt.com.vn/img/logo-vnpt.svg'
      }
    },
    spacer1: {
      desktop: { height: 16 },
      tablet: { height: 16 },
      mobile: { height: 16 }
    },
    spacer2: {
      desktop: { height: 16 },
      tablet: { height: 16 },
      mobile: { height: 16 }
    },
    spacer3: {
      desktop: { height: 16 },
      tablet: { height: 16 },
      mobile: { height: 16 }
    }
  },
  rules: {
    canDrag: () => true,
    canMoveIn: () => false
  }
}
