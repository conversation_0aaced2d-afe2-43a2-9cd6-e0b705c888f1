'use client'

import { Element, useNode, type UserComponent } from '@craftjs/core'

import { FlexContainer } from '@/components/page-builder/editor/CustomComponents/IoTTemplate'
import { Column, Image, Row, Text } from '@/components/page-builder/selectors'
import { defaultResponseColumnProps, defaultRowProps } from '@/constants/page-builder/row'

export const ClientsPartnersBuilder: UserComponent = props => {
  const {
    connectors: { connect, drag }
  } = useNode()

  return (
    <div
      className='w-full'
      ref={(ref: HTMLDivElement | null) => {
        if (ref) {
          connect(drag(ref))
        }
      }}
    >
      <div>
        <Element is={Row} id='title-row' canvas {...props.titleRow}>
          <Element is={Column} id='title-column' canvas {...props.titleColumn}>
            <Element is={Text} id='section-title' canvas {...props.sectionTitle} />
          </Element>
        </Element>
        <Element is={FlexContainer} id='clients-row' canvas {...props.clientsRow}>
          <div className='container mx-auto flex w-full items-center justify-center gap-12'>
            <Element is={Image} id='vingroup-logo' canvas {...props.spotifyLogo} />
            <Element is={Image} id='sungroup-logo' canvas {...props.googleLogo} />
            <Element is={Image} id='novaland-logo' canvas {...props.microsoftLogo} />
            <Element is={Image} id='ecopark-logo' canvas {...props.amazonLogo} />
            <Element is={Image} id='brg-logo' canvas {...props.adobeLogo} />
          </div>
        </Element>
      </div>
    </div>
  )
}

ClientsPartnersBuilder.craft = {
  props: {
    titleRow: {
      desktop: {
        ...defaultRowProps('desktop'),
        backgroundColor: '#ffffff',
        backgroundType: 'COLOR',
        padding: { top: 64, bottom: 40, left: 16, right: 16 },
        height: 'auto',
        justifyContent: 'center'
      },
      tablet: {
        ...defaultRowProps('tablet'),
        backgroundColor: '#ffffff',
        backgroundType: 'COLOR',
        padding: { top: 64, bottom: 40, left: 16, right: 16 },
        height: 'auto',
        justifyContent: 'center'
      },
      mobile: {
        ...defaultRowProps('mobile'),
        backgroundColor: '#ffffff',
        backgroundType: 'COLOR',
        padding: { top: 48, bottom: 32, left: 16, right: 16 },
        height: 'auto',
        justifyContent: 'center'
      }
    },
    titleColumn: {
      desktop: { ...defaultResponseColumnProps.desktop, contentAlign: 'center' },
      tablet: { ...defaultResponseColumnProps.tablet, contentAlign: 'center' },
      mobile: { ...defaultResponseColumnProps.mobile, contentAlign: 'center' }
    },
    clientsRow: {
      desktop: {
        backgroundColor: '#ffffff',
        backgroundType: 'COLOR',
        padding: { top: 0, bottom: 64, left: 16, right: 16 },
        height: 'auto',
        justifyContent: 'center',
        alignItems: 'center',
        flexWrap: 'nowrap',
        gap: 48,
        display: 'flex'
      },
      tablet: {
        backgroundColor: '#ffffff',
        backgroundType: 'COLOR',
        padding: { top: 0, bottom: 64, left: 16, right: 16 },
        height: 'auto',
        justifyContent: 'center',
        alignItems: 'center',
        flexWrap: 'wrap',
        gap: 20,
        display: 'flex'
      },
      mobile: {
        backgroundColor: '#ffffff',
        backgroundType: 'COLOR',
        padding: { top: 0, bottom: 48, left: 16, right: 16 },
        height: 'auto',
        justifyContent: 'center',
        alignItems: 'center',
        flexWrap: 'wrap',
        gap: 20,
        display: 'flex'
      }
    },
    clientColumn: {
      desktop: { justifyContent: 'center', alignItems: 'center' },
      tablet: { justifyContent: 'center', alignItems: 'center' },
      mobile: { justifyContent: 'center', alignItems: 'center' }
    },
    sectionTitle: {
      desktop: {
        text: 'Khách hàng & Đối tác tiêu biểu',
        fontSize: 32,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'center'
      },
      tablet: {
        text: 'Khách hàng & Đối tác tiêu biểu',
        fontSize: 28,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'center'
      },
      mobile: {
        text: 'Khách hàng & Đối tác tiêu biểu',
        fontSize: 24,
        fontWeight: 700,
        color: '#00529c',
        textAlign: 'center'
      }
    },
    spotifyLogo: {
      desktop: {
        imgSrc: '/assets/images/pages/home/<USER>',
        width: 'auto',
        height: 40,
        typeUpload: 'EXIST',
        align: 'center',
        border: {
          style: 'none',
          color: '#212d6e',
          thickness: 0,
          radius: 0
        }
      },
      tablet: {
        imgSrc: '/assets/images/pages/home/<USER>',
        width: 'auto',
        height: 40,
        typeUpload: 'EXIST',
        align: 'center',
        border: {
          style: 'none',
          color: '#212d6e',
          thickness: 0,
          radius: 0
        }
      },
      mobile: {
        imgSrcMobile: 'https://onesme.vn/assets/images/pages/home/<USER>',
        width: 'auto',
        height: 32,
        typeUpload: 'EXIST',
        align: 'center',
        border: {
          style: 'none',
          color: '#212d6e',
          thickness: 0,
          radius: 0
        }
      }
    },
    googleLogo: {
      desktop: {
        imgSrc: '/assets/images/pages/home/<USER>',
        width: 'auto',
        height: 48,
        typeUpload: 'EXIST',
        align: 'center',
        border: {
          style: 'none',
          color: '#212d6e',
          thickness: 0,
          radius: 0
        }
      },
      tablet: {
        imgSrc: '/assets/images/pages/home/<USER>',
        width: 'auto',
        height: 48,
        typeUpload: 'EXIST',
        align: 'center',
        border: {
          style: 'none',
          color: '#212d6e',
          thickness: 0,
          radius: 0
        }
      },
      mobile: {
        imgSrcMobile: '/assets/images/pages/home/<USER>',
        width: 'auto',
        height: 40,
        typeUpload: 'EXIST',
        align: 'center',
        border: {
          style: 'none',
          color: '#212d6e',
          thickness: 0,
          radius: 0
        }
      }
    },
    microsoftLogo: {
      desktop: {
        imgSrc: '/assets/images/pages/home/<USER>',
        width: 'auto',
        height: 32,
        typeUpload: 'EXIST',
        align: 'center',
        border: {
          style: 'none',
          color: '#212d6e',
          thickness: 0,
          radius: 0
        }
      },
      tablet: {
        imgSrc: '/assets/images/pages/home/<USER>',
        width: 'auto',
        height: 32,
        typeUpload: 'EXIST',
        align: 'center',
        border: {
          style: 'none',
          color: '#212d6e',
          thickness: 0,
          radius: 0
        }
      },
      mobile: {
        imgSrcMobile: '/assets/images/pages/home/<USER>',
        width: 'auto',
        height: 28,
        typeUpload: 'EXIST',
        align: 'center',
        border: {
          style: 'none',
          color: '#212d6e',
          thickness: 0,
          radius: 0
        }
      }
    },
    amazonLogo: {
      desktop: {
        imgSrc: '/assets/images/pages/home/<USER>',
        width: 'auto',
        height: 48,
        typeUpload: 'EXIST',
        align: 'center',
        border: {
          style: 'none',
          color: '#212d6e',
          thickness: 0,
          radius: 0
        }
      },
      tablet: {
        imgSrc: '/assets/images/pages/home/<USER>',
        width: 'auto',
        height: 48,
        typeUpload: 'EXIST',
        align: 'center',
        border: {
          style: 'none',
          color: '#212d6e',
          thickness: 0,
          radius: 0
        }
      },
      mobile: {
        imgSrcMobile: '/assets/images/pages/home/<USER>',
        width: 'auto',
        height: 40,
        typeUpload: 'EXIST',
        align: 'center',
        border: {
          style: 'none',
          color: '#212d6e',
          thickness: 0,
          radius: 0
        }
      }
    },
    adobeLogo: {
      desktop: {
        imgSrc: '/assets/images/pages/home/<USER>',
        width: 'auto',
        height: 48,
        typeUpload: 'EXIST',
        align: 'center',
        border: {
          style: 'none',
          color: '#212d6e',
          thickness: 0,
          radius: 0
        }
      },
      tablet: {
        imgSrc: '/assets/images/pages/home/<USER>',
        width: 'auto',
        height: 48,
        typeUpload: 'EXIST',
        align: 'center',
        border: {
          style: 'none',
          color: '#212d6e',
          thickness: 0,
          radius: 0
        }
      },
      mobile: {
        imgSrcMobile: '/assets/images/pages/home/<USER>',
        width: 'auto',
        height: 40,
        typeUpload: 'EXIST',
        align: 'center',
        border: {
          style: 'none',
          color: '#212d6e',
          thickness: 0,
          radius: 0
        }
      }
    }
  },
  rules: {
    canDrag: () => true,
    canMoveIn: () => false
  }
}
