'use client'

import { Element, useNode, type UserComponent } from '@craftjs/core'

import { Text, Image, Button } from '@/components/page-builder/selectors'
import { CartoonCard } from '@/components/page-builder/editor/CustomComponents/IoTTemplate'

export const ServicesGridTeenBuilder: UserComponent = props => {
  const {
    connectors: { connect, drag }
  } = useNode()

  return (
    <div
      className='w-full'
      ref={(ref: HTMLDivElement | null) => {
        if (ref) {
          connect(drag(ref))
        }
      }}
    >
      <section className='bg-blue-50 py-12'>
        <div className='container mx-auto px-4'>
          <div className='mb-10'>
            <Element is={Text} id='section-title' canvas {...props.sectionTitle} />
          </div>
          <div className='grid grid-cols-3 gap-8 md:grid-cols-2'>
            {/* Card 1 */}
            <CartoonCard>
              <div className='overflow-hidden'>
                <Element is={Image} id='card1-image' canvas {...props.card1Image} />
              </div>
              <div className='flex grow flex-col justify-between p-4'>
                <div>
                  <Element is={Text} id='card1-title' canvas {...props.card1Title} />
                  <div className='my-2'>
                    <Element is={Text} id='card1-desc' canvas {...props.card1Desc} />
                  </div>
                  <Element is={Text} id='card1-apply' canvas {...props.card1Apply} />
                  <Element is={Text} id='card1-time' canvas {...props.card1Time} />
                </div>
                <div className='mt-4 flex gap-2'>
                  <Element is={Button} id='card1-btn1' canvas {...props.card1Btn1} />
                  <Element is={Button} id='card1-btn2' canvas {...props.card1Btn2} />
                </div>
              </div>
            </CartoonCard>
            {/* Card 2 */}
            <CartoonCard>
              <div className='overflow-hidden'>
                <Element is={Image} id='card2-image' canvas {...props.card2Image} />
              </div>
              <div className='flex grow flex-col justify-between p-4'>
                <div>
                  <Element is={Text} id='card2-title' canvas {...props.card2Title} />
                  <div className='my-2'>
                    <Element is={Text} id='card2-desc' canvas {...props.card2Desc} />
                  </div>
                  <Element is={Text} id='card2-apply' canvas {...props.card2Apply} />
                  <Element is={Text} id='card2-time' canvas {...props.card2Time} />
                </div>
                <div className='mt-4 flex gap-2'>
                  <Element is={Button} id='card2-btn1' canvas {...props.card2Btn1} />
                  <Element is={Button} id='card2-btn2' canvas {...props.card2Btn2} />
                </div>
              </div>
            </CartoonCard>
            {/* Card 3 */}
            <CartoonCard>
              <div className='overflow-hidden'>
                <Element is={Image} id='card3-image' canvas {...props.card3Image} />
              </div>
              <div className='flex grow flex-col justify-between p-4'>
                <div>
                  <Element is={Text} id='card3-title' canvas {...props.card3Title} />
                  <div className='my-2'>
                    <Element is={Text} id='card3-desc' canvas {...props.card3Desc} />
                  </div>
                  <Element is={Text} id='card3-apply' canvas {...props.card3Apply} />
                  <Element is={Text} id='card3-time' canvas {...props.card3Time} />
                </div>
                <div className='mt-4 flex gap-2'>
                  <Element is={Button} id='card3-btn1' canvas {...props.card3Btn1} />
                  <Element is={Button} id='card3-btn2' canvas {...props.card3Btn2} />
                </div>
              </div>
            </CartoonCard>
          </div>
        </div>
      </section>
    </div>
  )
}

ServicesGridTeenBuilder.craft = {
  props: {
    sectionTitle: {
      desktop: {
        text: 'Ưu đãi tháng 7',
        fontSize: 40,
        fontWeight: 700,
        color: '#2a2a2a',
        textAlign: 'center',
        className: '[text-shadow:3px_3px_0_#ffffff,_6px_6px_0_rgba(0,0,0,0.1)]'
      },
      tablet: {
        text: 'Ưu đãi tháng 7',
        fontSize: 32,
        fontWeight: 700,
        color: '#2a2a2a',
        textAlign: 'center',
        className: '[text-shadow:3px_3px_0_#ffffff,_6px_6px_0_rgba(0,0,0,0.1)]'
      },
      mobile: {
        text: 'Ưu đãi tháng 7',
        fontSize: 28,
        fontWeight: 700,
        color: '#2a2a2a',
        textAlign: 'center',
        className: '[text-shadow:3px_3px_0_#ffffff,_6px_6px_0_rgba(0,0,0,0.1)]'
      }
    },
    card1Image: {
      desktop: {
        imgSrc: 'https://placehold.co/600x300/E3F2FD/0277BD?text=Nạp+thẻ+tặng+Data',
        width: '100%',
        height: 128,
        imageFit: 'fill',
        align: 'center'
      },
      tablet: {
        imgSrc: 'https://placehold.co/600x300/E3F2FD/0277BD?text=Nạp+thẻ+tặng+Data',
        width: '100%',
        height: 96,
        imageFit: 'fill',
        align: 'center'
      },
      mobile: {
        imgSrcMobile: 'https://placehold.co/600x300/E3F2FD/0277BD?text=Nạp+thẻ+tặng+Data',
        width: '100%',
        height: 72,
        imageFit: 'fill',
        align: 'center'
      }
    },
    card1Title: {
      desktop: {
        text: 'Nạp Thẻ Tặng Data',
        fontSize: 20,
        fontWeight: 700,
        className: 'font-cartoon text-lg'
      },
      tablet: {
        text: 'Nạp Thẻ Tặng Data',
        fontSize: 18,
        fontWeight: 700,
        className: 'font-cartoon text-base'
      },
      mobile: {
        text: 'Nạp Thẻ Tặng Data',
        fontSize: 16,
        fontWeight: 700,
        className: 'font-cartoon text-base'
      }
    },
    card1Desc: {
      desktop: {
        text: 'Nạp từ 50K, nhận ngay data miễn phí để lướt web.',
        fontSize: 14,
        className: 'text-sm text-gray-600 flex-grow'
      },
      tablet: {
        text: 'Nạp từ 50K, nhận ngay data miễn phí để lướt web.',
        fontSize: 13,
        className: 'text-xs text-gray-600 my-2 flex-grow'
      },
      mobile: {
        text: 'Nạp từ 50K, nhận ngay data miễn phí để lướt web.',
        fontSize: 12,
        className: 'text-xs text-gray-600 my-2 flex-grow'
      }
    },
    card1Apply: {
      desktop: {
        text: '<i class="fas fa-check-circle text-fun-green"></i> Áp dụng: Thuê bao trả trước.',
        fontSize: 12,
        className: 'text-xs text-gray-500',
        isHtml: true
      },
      tablet: {
        text: '<i class="fas fa-check-circle text-fun-green"></i> Áp dụng: Thuê bao trả trước.',
        fontSize: 12,
        className: 'text-xs text-gray-500',
        isHtml: true
      },
      mobile: {
        text: '<i class="fas fa-check-circle text-fun-green"></i> Áp dụng: Thuê bao trả trước.',
        fontSize: 12,
        className: 'text-xs text-gray-500',
        isHtml: true
      }
    },
    card1Time: {
      desktop: {
        text: '<i class="fas fa-calendar-alt text-fun-green"></i> Thời gian: Mỗi thứ Sáu.',
        fontSize: 12,
        className: 'text-xs text-gray-500',
        isHtml: true
      },
      tablet: {
        text: '<i class="fas fa-calendar-alt text-fun-green"></i> Thời gian: Mỗi thứ Sáu.',
        fontSize: 12,
        className: 'text-xs text-gray-500',
        isHtml: true
      },
      mobile: {
        text: '<i class="fas fa-calendar-alt text-fun-green"></i> Thời gian: Mỗi thứ Sáu.',
        fontSize: 12,
        className: 'text-xs text-gray-500',
        isHtml: true
      }
    },
    card1Btn1: {
      desktop: {
        text: 'Chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#2a2a2a',
        buttonType: 'primary',
        buttonBackgroundColor: '#e0e0e0',
        border: '2px solid #2a2a2a',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      tablet: {
        text: 'Chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#2a2a2a',
        buttonType: 'primary',
        buttonBackgroundColor: '#e0e0e0',
        border: '2px solid #2a2a2a',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      mobile: {
        text: 'Chi tiết',
        fontSize: 12,
        fontWeight: 600,
        color: '#2a2a2a',
        buttonType: 'primary',
        buttonBackgroundColor: '#e0e0e0',
        border: '2px solid #2a2a2a',
        radius: 8,
        padding: { top: 6, bottom: 6, left: 12, right: 12 },
        size: 'small',
        width: '100%'
      }
    },
    card1Btn2: {
      desktop: {
        text: 'Nạp ngay',
        fontSize: 14,
        fontWeight: 600,
        color: '#2a2a2a',
        buttonType: 'primary',
        buttonBackgroundColor: '#ffd600',
        border: '2px solid #2a2a2a',
        boxShadow: '2px 2px 0 #2a2a2a',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%',
        className: 'btn-cartoon-primary'
      },
      tablet: {
        text: 'Nạp ngay',
        fontSize: 14,
        fontWeight: 600,
        color: '#2a2a2a',
        buttonType: 'primary',
        buttonBackgroundColor: '#ffd600',
        border: '2px solid #2a2a2a',
        boxShadow: '2px 2px 0 #2a2a2a',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%',
        className: 'btn-cartoon-primary'
      },
      mobile: {
        text: 'Nạp ngay',
        fontSize: 14,
        fontWeight: 600,
        color: '#2a2a2a',
        buttonType: 'primary',
        buttonBackgroundColor: '#ffd600',
        border: '2px solid #2a2a2a',
        boxShadow: '2px 2px 0 #2a2a2a',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%',
        className: 'btn-cartoon-primary'
      }
    },
    // Card 2
    card2Image: {
      desktop: {
        imgSrc: 'https://placehold.co/600x300/FFF3E0/E65100?text=Săn+SIM+0Đ',
        width: '100%',
        height: 128,
        imageFit: 'fill',
        align: 'center'
      },
      tablet: {
        imgSrc: 'https://placehold.co/600x300/FFF3E0/E65100?text=Săn+SIM+0Đ',
        width: '100%',
        height: 96,
        imageFit: 'fill',
        align: 'center'
      },
      mobile: {
        imgSrcMobile: 'https://placehold.co/600x300/FFF3E0/E65100?text=Săn+SIM+0Đ',
        width: '100%',
        height: 72,
        imageFit: 'fill',
        align: 'center'
      }
    },
    card2Title: {
      desktop: {
        text: 'Săn SIM 0 Đồng',
        fontSize: 20,
        fontWeight: 700,
        className: 'font-cartoon text-lg'
      },
      tablet: {
        text: 'Săn SIM 0 Đồng',
        fontSize: 18,
        fontWeight: 700,
        className: 'font-cartoon text-base'
      },
      mobile: {
        text: 'Săn SIM 0 Đồng',
        fontSize: 16,
        fontWeight: 700,
        className: 'font-cartoon text-base'
      }
    },
    card2Desc: {
      desktop: {
        text: 'Nhận SIM miễn phí khi đăng ký kèm các gói cước hot.',
        fontSize: 14,
        className: 'text-sm text-gray-600 my-2 flex-grow'
      },
      tablet: {
        text: 'Nhận SIM miễn phí khi đăng ký kèm các gói cước hot.',
        fontSize: 13,
        className: 'text-xs text-gray-600 my-2 flex-grow'
      },
      mobile: {
        text: 'Nhận SIM miễn phí khi đăng ký kèm các gói cước hot.',
        fontSize: 12,
        className: 'text-xs text-gray-600 my-2 flex-grow'
      }
    },
    card2Apply: {
      desktop: {
        text: '<i class="fas fa-check-circle text-fun-green"></i> Áp dụng: Gói BIG120 trở lên.',
        fontSize: 12,
        className: 'text-xs text-gray-500',
        isHtml: true
      },
      tablet: {
        text: '<i class="fas fa-check-circle text-fun-green"></i> Áp dụng: Gói BIG120 trở lên.',
        fontSize: 12,
        className: 'text-xs text-gray-500',
        isHtml: true
      },
      mobile: {
        text: '<i class="fas fa-check-circle text-fun-green"></i> Áp dụng: Gói BIG120 trở lên.',
        fontSize: 12,
        className: 'text-xs text-gray-500',
        isHtml: true
      }
    },
    card2Time: {
      desktop: {
        text: '<i class="fas fa-calendar-alt text-fun-green"></i> Thời gian: 01/07 - 31/07/2025.',
        fontSize: 12,
        className: 'text-xs text-gray-500',
        isHtml: true
      },
      tablet: {
        text: '<i class="fas fa-calendar-alt text-fun-green"></i> Thời gian: 01/07 - 31/07/2025.',
        fontSize: 12,
        className: 'text-xs text-gray-500',
        isHtml: true
      },
      mobile: {
        text: '<i class="fas fa-calendar-alt text-fun-green"></i> Thời gian: 01/07 - 31/07/2025.',
        fontSize: 12,
        className: 'text-xs text-gray-500',
        isHtml: true
      }
    },
    card2Btn1: {
      desktop: {
        text: 'Chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#2a2a2a',
        buttonType: 'primary',
        buttonBackgroundColor: '#e0e0e0',
        border: '2px solid #2a2a2a',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      tablet: {
        text: 'Chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#2a2a2a',
        buttonType: 'primary',
        buttonBackgroundColor: '#e0e0e0',
        border: '2px solid #2a2a2a',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      mobile: {
        text: 'Chi tiết',
        fontSize: 12,
        fontWeight: 600,
        color: '#2a2a2a',
        buttonType: 'primary',
        buttonBackgroundColor: '#e0e0e0',
        border: '2px solid #2a2a2a',
        radius: 8,
        padding: { top: 6, bottom: 6, left: 12, right: 12 },
        size: 'small',
        width: '100%'
      }
    },
    card2Btn2: {
      desktop: {
        text: 'Săn SIM',
        fontSize: 14,
        fontWeight: 600,
        color: '#2a2a2a',
        buttonType: 'primary',
        buttonBackgroundColor: '#ffd600',
        border: '2px solid #2a2a2a',
        boxShadow: '2px 2px 0 #2a2a2a',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%',
        className: 'btn-cartoon-primary'
      },
      tablet: {
        text: 'Săn SIM',
        fontSize: 14,
        fontWeight: 600,
        color: '#2a2a2a',
        buttonType: 'primary',
        buttonBackgroundColor: '#ffd600',
        border: '2px solid #2a2a2a',
        boxShadow: '2px 2px 0 #2a2a2a',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%',
        className: 'btn-cartoon-primary'
      },
      mobile: {
        text: 'Săn SIM',
        fontSize: 14,
        fontWeight: 600,
        color: '#2a2a2a',
        buttonType: 'primary',
        buttonBackgroundColor: '#ffd600',
        border: '2px solid #2a2a2a',
        boxShadow: '2px 2px 0 #2a2a2a',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%',
        className: 'btn-cartoon-primary'
      }
    },
    // Card 3
    card3Image: {
      desktop: {
        imgSrc: 'https://placehold.co/600x300/E8F5E9/1B5E20?text=Giảm+giá+phụ+kiện',
        width: '100%',
        height: 128,
        imageFit: 'fill',
        align: 'center'
      },
      tablet: {
        imgSrc: 'https://placehold.co/600x300/E8F5E9/1B5E20?text=Giảm+giá+phụ+kiện',
        width: '100%',
        height: 96,
        imageFit: 'fill',
        align: 'center'
      },
      mobile: {
        imgSrcMobile: 'https://placehold.co/600x300/E8F5E9/1B5E20?text=Giảm+giá+phụ+kiện',
        width: '100%',
        height: 72,
        imageFit: 'fill',
        align: 'center'
      }
    },
    card3Title: {
      desktop: {
        text: 'Sale Sốc Phụ Kiện',
        fontSize: 20,
        fontWeight: 700,
        className: 'font-cartoon text-lg'
      },
      tablet: {
        text: 'Sale Sốc Phụ Kiện',
        fontSize: 18,
        fontWeight: 700,
        className: 'font-cartoon text-base'
      },
      mobile: {
        text: 'Sale Sốc Phụ Kiện',
        fontSize: 16,
        fontWeight: 700,
        className: 'font-cartoon text-base'
      }
    },
    card3Desc: {
      desktop: {
        text: 'Giảm đến 30% cho tai nghe, chuột gaming khi mua kèm gói cước.',
        fontSize: 14,
        className: 'text-sm text-gray-600 my-2 flex-grow'
      },
      tablet: {
        text: 'Giảm đến 30% cho tai nghe, chuột gaming khi mua kèm gói cước.',
        fontSize: 13,
        className: 'text-xs text-gray-600 my-2 flex-grow'
      },
      mobile: {
        text: 'Giảm đến 30% cho tai nghe, chuột gaming khi mua kèm gói cước.',
        fontSize: 12,
        className: 'text-xs text-gray-600 my-2 flex-grow'
      }
    },
    card3Apply: {
      desktop: {
        text: '<i class="fas fa-check-circle text-fun-green"></i> Áp dụng: Khi đăng ký gói Internet.',
        fontSize: 12,
        className: 'text-xs text-gray-500',
        isHtml: true
      },
      tablet: {
        text: '<i class="fas fa-check-circle text-fun-green"></i> Áp dụng: Khi đăng ký gói Internet.',
        fontSize: 12,
        className: 'text-xs text-gray-500',
        isHtml: true
      },
      mobile: {
        text: '<i class="fas fa-check-circle text-fun-green"></i> Áp dụng: Khi đăng ký gói Internet.',
        fontSize: 12,
        className: 'text-xs text-gray-500',
        isHtml: true
      }
    },
    card3Time: {
      desktop: {
        text: '<i class="fas fa-calendar-alt text-fun-green"></i> Thời gian: 01/07 - 31/07/2025.',
        fontSize: 12,
        className: 'text-xs text-gray-500',
        isHtml: true
      },
      tablet: {
        text: '<i class="fas fa-calendar-alt text-fun-green"></i> Thời gian: 01/07 - 31/07/2025.',
        fontSize: 12,
        className: 'text-xs text-gray-500',
        isHtml: true
      },
      mobile: {
        text: '<i class="fas fa-calendar-alt text-fun-green"></i> Thời gian: 01/07 - 31/07/2025.',
        fontSize: 12,
        className: 'text-xs text-gray-500',
        isHtml: true
      }
    },
    card3Btn1: {
      desktop: {
        text: 'Chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#2a2a2a',
        buttonType: 'primary',
        buttonBackgroundColor: '#e0e0e0',
        border: '2px solid #2a2a2a',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      tablet: {
        text: 'Chi tiết',
        fontSize: 14,
        fontWeight: 600,
        color: '#2a2a2a',
        buttonType: 'primary',
        buttonBackgroundColor: '#e0e0e0',
        border: '2px solid #2a2a2a',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%'
      },
      mobile: {
        text: 'Chi tiết',
        fontSize: 12,
        fontWeight: 600,
        color: '#2a2a2a',
        buttonType: 'primary',
        buttonBackgroundColor: '#e0e0e0',
        border: '2px solid #2a2a2a',
        radius: 8,
        padding: { top: 6, bottom: 6, left: 12, right: 12 },
        size: 'small',
        width: '100%'
      }
    },
    card3Btn2: {
      desktop: {
        text: 'Mua sắm',
        fontSize: 14,
        fontWeight: 600,
        color: '#2a2a2a',
        buttonType: 'primary',
        buttonBackgroundColor: '#ffd600',
        border: '2px solid #2a2a2a',
        boxShadow: '2px 2px 0 #2a2a2a',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%',
        className: 'btn-cartoon-primary'
      },
      tablet: {
        text: 'Mua sắm',
        fontSize: 14,
        fontWeight: 600,
        color: '#2a2a2a',
        buttonType: 'primary',
        buttonBackgroundColor: '#ffd600',
        border: '2px solid #2a2a2a',
        boxShadow: '2px 2px 0 #2a2a2a',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%',
        className: 'btn-cartoon-primary'
      },
      mobile: {
        text: 'Mua sắm',
        fontSize: 14,
        fontWeight: 600,
        color: '#2a2a2a',
        buttonType: 'primary',
        buttonBackgroundColor: '#ffd600',
        border: '2px solid #2a2a2a',
        boxShadow: '2px 2px 0 #2a2a2a',
        radius: 8,
        padding: { top: 8, bottom: 8, left: 16, right: 16 },
        size: 'middle',
        width: '100%',
        className: 'btn-cartoon-primary'
      }
    }
  },
  rules: {
    canDrag: () => true,
    canMoveIn: () => false
  }
}
