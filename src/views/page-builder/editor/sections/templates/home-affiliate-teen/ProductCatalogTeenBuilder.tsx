'use client'

import { Element, useNode, type UserComponent } from '@craftjs/core'

import { Button, Image, Text } from '@/components/page-builder/selectors'
import { CartoonCard } from '@/components/page-builder/editor/CustomComponents/IoTTemplate'

import { productCatalogInternet } from '@/constants/page-builder/productCatalogInternet'
import { productCatalogMobile } from '@/constants/page-builder/productCatalogMobile'

// Product Card Component
const ProductCard = ({ productId, props }: { productId: string; props: any }) => {
  return (
    <CartoonCard className='flex flex-col'>
      <div className='h-[160px]'>
        <Element is={Image} id={`${productId}-image`} canvas {...props[`${productId}Image`]} />
      </div>
      <div className='flex h-full grow flex-col p-4'>
        <div className='flex-1'>
          <Element is={Text} id={`${productId}-title`} canvas {...props[`${productId}Title`]} />
          <Element is={Text} id={`${productId}-features`} canvas {...props[`${productId}Features`]} />
        </div>

        <div className='mt-auto'>
          <Element is={Text} id={`${productId}-price`} canvas {...props[`${productId}Price`]} />
          <div className='mt-2 flex gap-2'>
            <Element is={Button} id={`${productId}-detail-btn`} canvas {...props[`${productId}DetailBtn`]} />
            <Element is={Button} id={`${productId}-buy-btn`} canvas {...props[`${productId}BuyBtn`]} />
          </div>
        </div>
      </div>
    </CartoonCard>
  )
}

// Helper function để tạo props cho sản phẩm
const createProductProps = (productId: string, data: any) => {
  return {
    [`${productId}Image`]: data.image,
    [`${productId}Title`]: data.title,
    [`${productId}Features`]: data.features,
    [`${productId}Price`]: data.price,
    [`${productId}DetailBtn`]: data.detailBtn,
    [`${productId}BuyBtn`]: data.buyBtn
  }
}

export const ProductCatalogTeenBuilder: UserComponent = props => {
  const {
    connectors: { connect, drag }
  } = useNode()

  // Tạo props cho tất cả sản phẩm (internet và mobile)
  const allProps = [...productCatalogInternet, ...productCatalogMobile].reduce((acc, product) => {
    const productProps = createProductProps(product.id, product.data)

    return { ...acc, ...productProps }
  }, {})

  return (
    <div
      className='w-full'
      ref={(ref: HTMLDivElement | null) => {
        if (ref) {
          connect(drag(ref))
        }
      }}
    >
      <section className='py-12'>
        <div className='container mx-auto px-4'>
          <div className='mb-10'>
            <Element is={Text} id='section-title' canvas {...props.sectionTitle} />
          </div>
          <div className='mb-12'>
            <div className='mb-6'>
              <Element is={Text} id='mobile-category-title' canvas {...props.mobileCategoryTitle} />
            </div>
            <div id='product-catalog-mobile' className='grid grid-cols-4 gap-8 sm:grid-cols-2 md:grid-cols-3'>
              {productCatalogMobile.map(product => (
                <ProductCard key={product.id} productId={product.id} props={allProps} />
              ))}
            </div>
          </div>

          <div>
            <div className='mb-6'>
              <Element is={Text} id='internet-category-title' canvas {...props.internetCategoryTitle} />
            </div>
            <div id='product-catalog-internet' className='grid grid-cols-4 gap-8 sm:grid-cols-2 md:grid-cols-3'>
              {productCatalogInternet.map(product => (
                <ProductCard key={product.id} productId={product.id} props={allProps} />
              ))}
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

ProductCatalogTeenBuilder.craft = {
  displayName: 'Product Catalog Teen',
  props: {
    sectionTitle: {
      desktop: {
        text: 'Danh mục sản phẩm',
        fontSize: 40,
        fontWeight: 700,
        color: '#2a2a2a',
        textAlign: 'center',
        className: '[text-shadow:3px_3px_0_#ffffff,_6px_6px_0_rgba(0,0,0,0.1)]'
      },
      tablet: {
        text: 'Danh mục sản phẩm',
        fontSize: 32,
        fontWeight: 700,
        color: '#2a2a2a',
        textAlign: 'center',
        className: '[text-shadow:3px_3px_0_#ffffff,_6px_6px_0_rgba(0,0,0,0.1)]'
      },
      mobile: {
        text: 'Danh mục sản phẩm',
        fontSize: 32,
        fontWeight: 700,
        color: '#2a2a2a',
        textAlign: 'center',
        className: '[text-shadow:3px_3px_0_#ffffff,_6px_6px_0_rgba(0,0,0,0.1)]'
      }
    },
    mobileCategoryTitle: {
      desktop: {
        text: '#Di động',
        fontSize: 30,
        fontWeight: 700,
        color: '#374151',
        textAlign: 'left',
        className: 'font-cartoon'
      },
      tablet: {
        text: '#Di động',
        fontSize: 28,
        fontWeight: 700,
        color: '#374151',
        textAlign: 'left',
        className: 'font-cartoon'
      },
      mobile: {
        text: '#Di động',
        fontSize: 24,
        fontWeight: 700,
        color: '#374151',
        textAlign: 'left',
        className: 'font-cartoon'
      }
    },
    internetCategoryTitle: {
      desktop: {
        text: '#Internet & TV',
        fontSize: 30,
        fontWeight: 700,
        color: '#374151',
        textAlign: 'left',
        className: 'font-cartoon'
      },
      tablet: {
        text: '#Internet & TV',
        fontSize: 28,
        fontWeight: 700,
        color: '#374151',
        textAlign: 'left',
        className: 'font-cartoon'
      },
      mobile: {
        text: '#Internet & TV',
        fontSize: 24,
        fontWeight: 700,
        color: '#374151',
        textAlign: 'left',
        className: 'font-cartoon'
      }
    }
  },
  rules: {
    canDrag: () => true,
    canMoveIn: () => false
  }
}
