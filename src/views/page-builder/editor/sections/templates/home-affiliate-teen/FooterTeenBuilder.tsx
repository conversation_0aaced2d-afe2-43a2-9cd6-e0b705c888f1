'use client'

import { Element, useNode, type UserComponent } from '@craftjs/core'

import { Image, Text, SocialMedia } from '@/components/page-builder/selectors'
import { defaultResponseSocialMediaProps } from '@/constants/page-builder/socialMedia'

export const FooterTeenBuilder: UserComponent = props => {
  const {
    connectors: { connect, drag }
  } = useNode()

  return (
    <div
      className='w-full'
      ref={(ref: HTMLDivElement | null) => {
        if (ref) {
          connect(drag(ref))
        }
      }}
    >
      <footer className='bg-fun-dark text-white'>
        <div className='container mx-auto px-4 py-8'>
          <div className='grid grid-cols-3 gap-8 text-center md:text-left'>
            {/* Company Info */}
            <div>
              <Element is={Text} id='brand-title' canvas {...props.brandTitle} />
              <Element is={Text} id='brand-description' canvas {...props.brandDescription} />
            </div>

            {/* Quick Links */}
            <div>
              <Element is={Text} id='quick-links-title' canvas {...props.quickLinksTitle} />
              <div className='space-y-2'>
                <Element is={Text} id='about-link' canvas {...props.aboutLink} />
                <Element is={Text} id='services-link' canvas {...props.servicesLink} />
                <Element is={Text} id='policy-link' canvas {...props.policyLink} />
              </div>
            </div>

            {/* Contact Info */}
            <div>
              <Element is={Text} id='contact-title' canvas {...props.contactTitle} />
              <div className='flex justify-start space-x-4'>
                <Element is={SocialMedia} id='footer-social-media' canvas {...props.footerSocialMedia} />
              </div>
            </div>
          </div>

          {/* Bottom Bar */}
          <div className='mt-8 flex items-center justify-between border-t border-gray-700 pt-6 text-center text-sm text-gray-500 sm:flex-row'>
            <Element is={Text} id='copyright' canvas {...props.copyright} />
            <div className='mt-4 flex items-center gap-4 sm:mt-0'>
              <Element is={Image} id='onesme-logo' canvas {...props.onesmeLogo} />
              <Element is={Image} id='vnpt-logo' canvas {...props.vnptLogo} />
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}

FooterTeenBuilder.craft = {
  displayName: 'Footer Teen',
  props: {
    brandTitle: {
      desktop: {
        text: "An An's Deals",
        fontSize: 20,
        fontWeight: 700,
        color: '#FFC107',
        textAlign: 'left',
        className: 'font-cartoon text-lg text-fun-yellow mb-4',
        margin: { top: 0, bottom: 16 }
      },
      tablet: {
        text: "An An's Deals",
        fontSize: 20,
        fontWeight: 700,
        color: '#FFC107',
        textAlign: 'center',
        className: 'font-cartoon text-lg text-fun-yellow mb-4',
        margin: { top: 0, bottom: 16 }
      },
      mobile: {
        text: "An An's Deals",
        fontSize: 18,
        fontWeight: 700,
        color: '#FFC107',
        textAlign: 'center',
        className: 'font-cartoon text-lg text-fun-yellow mb-4',
        margin: { top: 0, bottom: 16 }
      }
    },
    brandDescription: {
      desktop: {
        text: 'Đối tác bán hàng chính thức của oneSME by VNPT. Cam kết mang đến dịch vụ và hỗ trợ tốt nhất.',
        fontSize: 16,
        fontWeight: 400,
        color: '#9CA3AF',
        textAlign: 'left',
        className: 'text-gray-400',
        lineHeight: 1.6
      },
      tablet: {
        text: 'Đối tác bán hàng chính thức của oneSME by VNPT. Cam kết mang đến dịch vụ và hỗ trợ tốt nhất.',
        fontSize: 16,
        fontWeight: 400,
        color: '#9CA3AF',
        textAlign: 'center',
        className: 'text-gray-400',
        lineHeight: 1.6
      },
      mobile: {
        text: 'Đối tác bán hàng chính thức của oneSME by VNPT. Cam kết mang đến dịch vụ và hỗ trợ tốt nhất.',
        fontSize: 14,
        fontWeight: 400,
        color: '#9CA3AF',
        textAlign: 'center',
        className: 'text-gray-400',
        lineHeight: 1.6
      }
    },
    quickLinksTitle: {
      desktop: {
        text: 'Liên kết nhanh',
        fontSize: 20,
        fontWeight: 700,
        color: '#FFC107',
        textAlign: 'left',
        className: 'font-cartoon text-lg text-fun-yellow mb-4',
        margin: { top: 0, bottom: 16 }
      },
      tablet: {
        text: 'Liên kết nhanh',
        fontSize: 20,
        fontWeight: 700,
        color: '#FFC107',
        textAlign: 'center',
        className: 'font-cartoon text-lg text-fun-yellow mb-4',
        margin: { top: 0, bottom: 16 }
      },
      mobile: {
        text: 'Liên kết nhanh',
        fontSize: 18,
        fontWeight: 700,
        color: '#FFC107',
        textAlign: 'center',
        className: 'font-cartoon text-lg text-fun-yellow mb-4',
        margin: { top: 0, bottom: 16 }
      }
    },
    aboutLink: {
      desktop: {
        text: 'Về chúng tôi',
        fontSize: 16,
        fontWeight: 400,
        color: '#9CA3AF',
        textAlign: 'left',
        link: '#',
        className: 'text-gray-400 hover:text-white transition-colors duration-200',
        padding: { top: 0, right: 0, bottom: 4, left: 0 },
        margin: { top: 0, bottom: 0 }
      },
      tablet: {
        text: 'Về chúng tôi',
        fontSize: 16,
        fontWeight: 400,
        color: '#9CA3AF',
        textAlign: 'center',
        link: '#',
        className: 'text-gray-400 hover:text-white transition-colors duration-200',
        padding: { top: 0, right: 0, bottom: 4, left: 0 },
        margin: { top: 0, bottom: 0 }
      },
      mobile: {
        text: 'Về chúng tôi',
        fontSize: 16,
        fontWeight: 400,
        color: '#9CA3AF',
        textAlign: 'center',
        link: '#',
        className: 'text-gray-400 hover:text-white transition-colors duration-200',
        padding: { top: 0, right: 0, bottom: 4, left: 0 },
        margin: { top: 0, bottom: 0 }
      }
    },
    servicesLink: {
      desktop: {
        text: 'Dịch vụ',
        fontSize: 16,
        fontWeight: 400,
        color: '#9CA3AF',
        textAlign: 'left',
        link: '#services',
        className: 'text-gray-400 hover:text-white transition-colors duration-200',
        padding: { top: 0, right: 0, bottom: 4, left: 0 },
        margin: { top: 0, bottom: 0 }
      },
      tablet: {
        text: 'Dịch vụ',
        fontSize: 16,
        fontWeight: 400,
        color: '#9CA3AF',
        textAlign: 'center',
        link: '#services',
        className: 'text-gray-400 hover:text-white transition-colors duration-200',
        padding: { top: 0, right: 0, bottom: 4, left: 0 },
        margin: { top: 0, bottom: 0 }
      },
      mobile: {
        text: 'Dịch vụ',
        fontSize: 16,
        fontWeight: 400,
        color: '#9CA3AF',
        textAlign: 'center',
        link: '#services',
        className: 'text-gray-400 hover:text-white transition-colors duration-200',
        padding: { top: 0, right: 0, bottom: 4, left: 0 },
        margin: { top: 0, bottom: 0 }
      }
    },
    policyLink: {
      desktop: {
        text: 'Chính sách',
        fontSize: 16,
        fontWeight: 400,
        color: '#9CA3AF',
        textAlign: 'left',
        link: '#',
        className: 'text-gray-400 hover:text-white transition-colors duration-200',
        padding: { top: 0, right: 0, bottom: 4, left: 0 },
        margin: { top: 0, bottom: 0 }
      },
      tablet: {
        text: 'Chính sách',
        fontSize: 16,
        fontWeight: 400,
        color: '#9CA3AF',
        textAlign: 'center',
        link: '#',
        className: 'text-gray-400 hover:text-white transition-colors duration-200',
        padding: { top: 0, right: 0, bottom: 4, left: 0 },
        margin: { top: 0, bottom: 0 }
      },
      mobile: {
        text: 'Chính sách',
        fontSize: 16,
        fontWeight: 400,
        color: '#9CA3AF',
        textAlign: 'center',
        link: '#',
        className: 'text-gray-400 hover:text-white transition-colors duration-200',
        padding: { top: 0, right: 0, bottom: 4, left: 0 },
        margin: { top: 0, bottom: 0 }
      }
    },
    contactTitle: {
      desktop: {
        text: 'Kết nối với tôi',
        fontSize: 20,
        fontWeight: 700,
        color: '#FFC107',
        textAlign: 'left',
        className: 'font-cartoon text-lg text-fun-yellow mb-4',
        margin: { top: 0, bottom: 16 }
      },
      tablet: {
        text: 'Kết nối với tôi',
        fontSize: 20,
        fontWeight: 700,
        color: '#FFC107',
        textAlign: 'center',
        className: 'font-cartoon text-lg text-fun-yellow mb-4',
        margin: { top: 0, bottom: 16 }
      },
      mobile: {
        text: 'Kết nối với tôi',
        fontSize: 18,
        fontWeight: 700,
        color: '#FFC107',
        textAlign: 'center',
        className: 'font-cartoon text-lg text-fun-yellow mb-4',
        margin: { top: 0, bottom: 16 }
      }
    },
    footerSocialMedia: {
      desktop: {
        ...defaultResponseSocialMediaProps.desktop,
        size: 24,
        iconAlign: 'start',
        socialList: [
          {
            name: 'Facebook',
            icon: 'FacebookIcon2',
            iconColor: '#9CA3AF',
            url: '',
            displayType: 'ICON'
          },
          {
            name: 'Email',
            icon: 'MailFilled',
            iconColor: '#9CA3AF',
            url: '',
            displayType: 'ICON'
          }
        ]
      },
      tablet: {
        ...defaultResponseSocialMediaProps.tablet,
        size: 24,
        iconAlign: 'start',
        socialList: [
          {
            name: 'Facebook',
            icon: 'FacebookIcon2',
            iconColor: '#9CA3AF',
            url: '',
            displayType: 'ICON'
          },
          {
            name: 'Email',
            icon: 'MailFilled',
            iconColor: '#9CA3AF',
            url: '',
            displayType: 'ICON'
          }
        ]
      },
      mobile: {
        ...defaultResponseSocialMediaProps.mobile,
        size: 24,
        iconAlign: 'start',
        socialList: [
          {
            name: 'Facebook',
            icon: 'FacebookIcon2',
            iconColor: '#9CA3AF',
            url: '',
            displayType: 'ICON'
          },
          {
            name: 'Email',
            icon: 'MailFilled',
            iconColor: '#9CA3AF',
            url: '',
            displayType: 'ICON'
          }
        ]
      }
    },
    footerEmailOld: {
      desktop: {
        icon: 'MessageFilled',
        iconColor: '#9CA3AF',
        title: '',
        fontSize: 24,
        fontWeight: 400,
        color: '#9CA3AF',
        href: '#',
        gap: 0,
        className: 'text-2xl text-gray-400 hover:text-white transition-colors duration-200',
        showIcon: { desktop: true, tablet: true, mobile: true },
        showTitle: { desktop: false, tablet: false, mobile: false }
      },
      tablet: {
        icon: 'MessageFilled',
        iconColor: '#9CA3AF',
        title: '',
        fontSize: 24,
        fontWeight: 400,
        color: '#9CA3AF',
        href: '#',
        gap: 0,
        className: 'text-2xl text-gray-400 hover:text-white transition-colors duration-200',
        showIcon: { desktop: true, tablet: true, mobile: true },
        showTitle: { desktop: false, tablet: false, mobile: false }
      },
      mobile: {
        icon: 'MessageFilled',
        iconColor: '#9CA3AF',
        title: '',
        fontSize: 24,
        fontWeight: 400,
        color: '#9CA3AF',
        href: '#',
        gap: 0,
        className: 'text-2xl text-gray-400 hover:text-white transition-colors duration-200',
        showIcon: { desktop: true, tablet: true, mobile: true },
        showTitle: { desktop: false, tablet: false, mobile: false }
      }
    },
    footerEmail: {
      desktop: {
        icon: 'MailFilled',
        iconColor: '#9CA3AF',
        title: '',
        fontSize: 24,
        fontWeight: 400,
        color: '#9CA3AF',
        href: '#',
        gap: 0,
        className: 'text-2xl text-gray-400 hover:text-white transition-colors duration-200',
        showIcon: { desktop: true, tablet: true, mobile: true },
        showTitle: { desktop: false, tablet: false, mobile: false }
      },
      tablet: {
        icon: 'MailFilled',
        iconColor: '#9CA3AF',
        title: '',
        fontSize: 24,
        fontWeight: 400,
        color: '#9CA3AF',
        href: '#',
        gap: 0,
        className: 'text-2xl text-gray-400 hover:text-white transition-colors duration-200',
        showIcon: { desktop: true, tablet: true, mobile: true },
        showTitle: { desktop: false, tablet: false, mobile: false }
      },
      mobile: {
        icon: 'MailFilled',
        iconColor: '#9CA3AF',
        title: '',
        fontSize: 24,
        fontWeight: 400,
        color: '#9CA3AF',
        href: '#',
        gap: 0,
        className: 'text-2xl text-gray-400 hover:text-white transition-colors duration-200',
        showIcon: { desktop: true, tablet: true, mobile: true },
        showTitle: { desktop: false, tablet: false, mobile: false }
      }
    },
    copyright: {
      desktop: {
        text: '2025. Trang tư vấn của Nguyễn Văn An.',
        fontSize: 16,
        fontWeight: 400,
        color: '#6B7280',
        textAlign: 'left',
        className: 'text-center text-gray-500 text-sm w-1/2'
      },
      tablet: {
        text: '© 2025. Trang tư vấn của Nguyễn Văn An.',
        fontSize: 16,
        fontWeight: 400,
        color: '#6B7280',
        textAlign: 'left',
        className: 'text-center text-gray-500 text-sm w-1/2'
      },
      mobile: {
        text: '© 2025. Trang tư vấn của Nguyễn Văn An.',
        fontSize: 12,
        fontWeight: 400,
        color: '#6B7280',
        textAlign: 'left',
        className: 'text-center text-gray-500 text-xs w-1/2'
      }
    },
    onesmeLogo: {
      desktop: {
        imgSrc: '/assets/images/logo.svg',
        width: 'auto',
        height: 24,
        className: 'h-6 filter brightness-0 invert',
        border: {
          radius: 0,
          thickness: 0,
          color: '',
          style: 'none'
        }
      },
      tablet: {
        imgSrc: '/assets/images/logo.svg',
        width: 'auto',
        height: 24,
        className: 'h-6 filter brightness-0 invert',
        border: {
          radius: 0,
          thickness: 0,
          color: '',
          style: 'none'
        }
      },
      mobile: {
        imgSrcMobile: '/assets/images/logo.svg',
        width: 'auto',
        height: 24,
        className: 'h-6 filter brightness-0 invert',
        border: {
          radius: 0,
          thickness: 0,
          color: '',
          style: 'none'
        }
      }
    },
    vnptLogo: {
      desktop: {
        imgSrc: 'https://www.vnpt.com.vn/img/logo-vnpt.svg',
        width: 'auto',
        height: 24,
        className: 'h-6 filter brightness-0 invert',
        border: {
          radius: 0,
          thickness: 0,
          color: '',
          style: 'none'
        }
      },
      tablet: {
        imgSrc: 'https://www.vnpt.com.vn/img/logo-vnpt.svg',
        width: 'auto',
        height: 24,
        className: 'h-6 filter brightness-0 invert',
        border: {
          radius: 0,
          thickness: 0,
          color: '',
          style: 'none'
        }
      },
      mobile: {
        imgSrcMobile: 'https://www.vnpt.com.vn/img/logo-vnpt.svg',
        width: 'auto',
        height: 24,
        className: 'h-6 filter brightness-0 invert',
        border: {
          radius: 0,
          thickness: 0,
          color: '',
          style: 'none'
        }
      }
    }
  },
  rules: {
    canDrag: () => true,
    canMoveIn: () => false
  }
}
