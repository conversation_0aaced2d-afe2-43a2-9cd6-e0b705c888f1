'use client'

import { Element, useNode, type UserComponent } from '@craftjs/core'

import { Column, Image, Row, Spacer, Text } from '@/components/page-builder/selectors'

export const IntroExpertTeenBuilder: UserComponent = props => {
  const {
    connectors: { connect, drag }
  } = useNode()

  return (
    <div
      className='w-full'
      ref={(ref: HTMLDivElement | null) => {
        if (ref) {
          connect(drag(ref))
        }
      }}
    >
      <Element is={Row} id='expert-intro-row' canvas {...props.expertIntroRow}>
        <Element is={Column} id='expert-image-column' canvas {...props.expertImageColumn}>
          <Element is={Image} id='expert-image' canvas {...props.expertImage} />
        </Element>
        <Element is={Column} id='expert-content-column' canvas {...props.expertContentColumn}>
          <Element is={Text} id='expert-title' canvas {...props.expertTitle} />
          <Element is={Spacer} id='expert-spacer' canvas {...props.expertSpacer} />
          <Element is={Text} id='expert-desc' canvas {...props.expertDesc} />
        </Element>
      </Element>
    </div>
  )
}

IntroExpertTeenBuilder.craft = {
  props: {
    expertIntroRow: {
      desktop: {
        backgroundColor: '#ffffff',
        backgroundType: 'COLOR',
        padding: { top: 48, bottom: 48, left: 16, right: 16 },
        colWidths: [4, 8],
        gap: 20,
        height: 'auto',
        alignItems: 'center'
      },
      tablet: {
        backgroundColor: '#ffffff',
        backgroundType: 'COLOR',
        padding: { top: 48, bottom: 48, left: 16, right: 16 },
        colWidths: [4, 8],
        gap: 20,
        height: 'auto',
        alignItems: 'center'
      },
      mobile: {
        backgroundColor: '#ffffff',
        backgroundType: 'COLOR',
        padding: { top: 32, bottom: 32, left: 16, right: 16 },
        isBreakLine: true,
        gap: 20,
        height: 'auto',
        alignItems: 'center'
      }
    },
    expertImageColumn: {
      desktop: { display: 'flex', justifyContent: 'center', alignItems: 'center', contentAlign: 'center' },
      tablet: { display: 'flex', justifyContent: 'center', alignItems: 'center', contentAlign: 'center' },
      mobile: { display: 'flex', justifyContent: 'center', alignItems: 'center', contentAlign: 'center' }
    },
    expertContentColumn: {
      desktop: { display: 'flex', justifyContent: 'center', alignItems: 'center', contentAlign: 'start' },
      tablet: { display: 'flex', justifyContent: 'center', alignItems: 'center', contentAlign: 'start' },
      mobile: { display: 'flex', justifyContent: 'center', alignItems: 'center', contentAlign: 'start' }
    },
    expertImage: {
      desktop: {
        imgSrc: 'https://placehold.co/600x600/3D5AFE/FFFFFF?text=AnAn&font=baloo-2',
        alt: 'An An - Chuyên gia tư vấn teen',
        width: 320,
        height: 320,
        border: {
          radius: 16
        },
        imageFit: 'cover'
      },
      tablet: {
        imgSrc: 'https://placehold.co/600x600/3D5AFE/FFFFFF?text=AnAn&font=baloo-2',
        alt: 'An An - Chuyên gia tư vấn teen',
        width: 240,
        height: 240,
        border: {
          radius: 12
        },
        imageFit: 'cover'
      },
      mobile: {
        imgSrc: 'https://placehold.co/600x600/3D5AFE/FFFFFF?text=AnAn&font=baloo-2',
        alt: 'An An - Chuyên gia tư vấn teen',
        width: 180,
        height: 180,
        border: {
          radius: 12
        },
        imageFit: 'cover'
      }
    },
    expertTitle: {
      desktop: {
        text: 'Hey! Tui là An An đây!',
        fontSize: 36,
        fontWeight: 700,
        color: '#000000',
        textAlign: 'left',
        className: 'text-4xl font-cartoon mb-4'
      },
      tablet: {
        text: 'Hey! Tui là An An đây!',
        fontSize: 32,
        fontWeight: 700,
        color: '#000000',
        textAlign: 'left',
        className: 'text-3xl font-cartoon mb-4'
      },
      mobile: {
        text: 'Hey! Tui là An An đây!',
        fontSize: 24,
        fontWeight: 700,
        color: '#000000',
        textAlign: 'center',
        className: 'text-2xl font-cartoon mb-3'
      }
    },
    expertSpacer: {
      desktop: { height: 16 },
      tablet: { height: 16 },
      mobile: { height: 16 }
    },
    expertDesc: {
      desktop: {
        text: 'Là một "chiến thần săn deal" chính hiệu, tui sẽ giúp bạn tìm ra những gói cước và ưu đãi "hời" nhất từ vũ trụ VNPT. Cần gì cứ hú tui nha!',
        fontSize: 20,
        fontWeight: 400,
        color: '#374151',
        textAlign: 'left',
        className: 'text-gray-700 text-lg leading-relaxed'
      },
      tablet: {
        text: 'Là một "chiến thần săn deal" chính hiệu, tui sẽ giúp bạn tìm ra những gói cước và ưu đãi "hời" nhất từ vũ trụ VNPT. Cần gì cứ hú tui nha!',
        fontSize: 18,
        fontWeight: 400,
        color: '#374151',
        textAlign: 'left',
        className: 'text-gray-700 text-base leading-relaxed'
      },
      mobile: {
        text: 'Là một "chiến thần săn deal" chính hiệu, tui sẽ giúp bạn tìm ra những gói cước và ưu đãi "hời" nhất từ vũ trụ VNPT. Cần gì cứ hú tui nha!',
        fontSize: 16,
        fontWeight: 400,
        color: '#374151',
        textAlign: 'center',
        className: 'text-gray-700 text-sm leading-relaxed'
      }
    }
  },
  rules: {
    canDrag: () => true,
    canMoveIn: () => false
  }
}
