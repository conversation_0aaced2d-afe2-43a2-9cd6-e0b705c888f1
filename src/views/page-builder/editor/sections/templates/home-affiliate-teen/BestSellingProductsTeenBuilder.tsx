'use client'

import { Element, useNode, type UserComponent } from '@craftjs/core'

import { Button, Image, Text } from '@/components/page-builder/selectors'
import { CartoonCard } from '@/components/page-builder/editor/CustomComponents/IoTTemplate'

// Product Card Component
const ProductCard = ({ productId, props }: { productId: string; props: any }) => {
  // Xác định màu promotion dựa trên productId
  const getPromotionStyle = (productId: string) => {
    switch (productId) {
      case 'product1':
        return 'my-2 rounded border border-red-200 bg-red-50 p-2 text-xs'
      case 'product2':
        return 'my-2 rounded border border-green-200 bg-green-50 p-2 text-xs'
      case 'product4':
        return 'my-2 rounded border border-orange-200 bg-orange-50 p-2 text-xs'
      default:
        return 'my-2 rounded border border-gray-200 bg-gray-50 p-2 text-xs'
    }
  }

  return (
    <CartoonCard className='flex flex-col'>
      {/* Đặt chiều cao cố định cho card */}
      <div className='h-[160px]'>
        <Element is={Image} id={`${productId}-image`} canvas {...props[`${productId}Image`]} />
      </div>
      <div className='flex flex-1 flex-col p-4'>
        <div className='flex-1'>
          <Element is={Text} id={`${productId}-title`} canvas {...props[`${productId}Title`]} />
          {/* Hiển thị promotion nếu có, ngược lại hiển thị placeholder để giữ chiều cao đồng đều */}
          {props[`${productId}Promotion`] ? (
            <div className={getPromotionStyle(productId)}>
              <Element is={Text} id={`${productId}-promotion`} canvas {...props[`${productId}Promotion`]} />
            </div>
          ) : (
            <div className='my-2 h-[42px]'></div>
          )}
        </div>

        <div className='mt-auto'>
          <Element is={Text} id={`${productId}-price`} canvas {...props[`${productId}Price`]} />
          <div className='mt-2 flex gap-2'>
            <Element is={Button} id={`${productId}-detail-btn`} canvas {...props[`${productId}DetailBtn`]} />
            <Element is={Button} id={`${productId}-buy-btn`} canvas {...props[`${productId}BuyBtn`]} />
          </div>
        </div>
      </div>
    </CartoonCard>
  )
}

// Helper function để tạo props cho sản phẩm
const createProductProps = (productId: string, data: any) => {
  return {
    [`${productId}Image`]: data.image,
    [`${productId}Title`]: data.title,
    [`${productId}Promotion`]: data.promotion,
    [`${productId}Price`]: data.price,
    [`${productId}DetailBtn`]: data.detailBtn,
    [`${productId}BuyBtn`]: data.buyBtn
  }
}

export const BestSellingProductsTeenBuilder: UserComponent = props => {
  const {
    connectors: { connect, drag }
  } = useNode()

  // Dữ liệu sản phẩm
  const products = [
    {
      id: 'product1',
      data: {
        image: {
          desktop: {
            imgSrc: 'https://placehold.co/600x300/e2e8f0/334155?text=D169G',
            width: '100%',
            height: 160,
            imageFit: 'fill',
            align: 'center'
          },
          tablet: {
            imgSrc: 'https://placehold.co/600x300/e2e8f0/334155?text=D169G',
            width: '100%',
            height: 160,
            imageFit: 'fill',
            align: 'center'
          },
          mobile: {
            imgSrc: 'https://placehold.co/600x200/e2e8f0/334155?text=D169G',
            width: '100%',
            height: 160,
            imageFit: 'fill',
            align: 'center'
          }
        },
        title: {
          desktop: {
            text: 'D169G',
            fontSize: 18,
            fontWeight: 700,
            className: 'font-cartoon text-lg'
          },
          tablet: {
            text: 'D169G',
            fontSize: 18,
            fontWeight: 700,
            className: 'font-cartoon text-lg'
          },
          mobile: {
            text: 'D169G',
            fontSize: 18,
            fontWeight: 700,
            className: 'font-cartoon text-lg'
          }
        },
        promotion: {
          desktop: {
            text: 'FLASH SALE: Giảm 30% (còn 118K) cuối tuần.',
            fontSize: 12,
            color: '#dc2626',
            className: 'font-bold text-red-600'
          },
          tablet: {
            text: 'FLASH SALE: Giảm 30% (còn 118K) cuối tuần.',
            fontSize: 12,
            color: '#dc2626',
            className: 'font-bold text-red-600'
          },
          mobile: {
            text: 'FLASH SALE: Giảm 30% (còn 118K) cuối tuần.',
            fontSize: 12,
            color: '#dc2626',
            className: 'font-bold text-red-600'
          }
        },
        price: {
          desktop: {
            text: '169.000đ<span class="text-base font-normal">/tháng</span>',
            fontSize: 20,
            fontWeight: 700,
            className: 'text-xl font-bold text-fun-dark mt-auto pt-2'
          },
          tablet: {
            text: '169.000đ<span class="text-base font-normal">/tháng</span>',
            fontSize: 20,
            fontWeight: 700,
            className: 'text-xl font-bold text-fun-dark mt-auto pt-2'
          },
          mobile: {
            text: '169.000đ<span class="text-base font-normal">/tháng</span>',
            fontSize: 18,
            fontWeight: 700,
            className: 'text-xl font-bold text-fun-dark mt-auto pt-2'
          }
        },
        detailBtn: {
          desktop: {
            text: 'Chi tiết',
            fontSize: 14,
            fontWeight: 600,
            color: '#2a2a2a',
            buttonType: 'primary',
            buttonBackgroundColor: '#e0e0e0',
            border: '2px solid #2a2a2a',
            radius: 12,
            padding: { top: 8, bottom: 8, left: 16, right: 16 },
            size: 'middle',
            width: '100%'
          },
          tablet: {
            text: 'Chi tiết',
            fontSize: 14,
            fontWeight: 600,
            color: '#2a2a2a',
            buttonType: 'primary',
            buttonBackgroundColor: '#e0e0e0',
            border: '2px solid #2a2a2a',
            radius: 12,
            padding: { top: 8, bottom: 8, left: 16, right: 16 },
            size: 'middle',
            width: '100%'
          },
          mobile: {
            text: 'Chi tiết',
            fontSize: 14,
            fontWeight: 600,
            color: '#2a2a2a',
            buttonType: 'primary',
            buttonBackgroundColor: '#e0e0e0',
            border: '2px solid #2a2a2a',
            radius: 12,
            padding: { top: 8, bottom: 8, left: 16, right: 16 },
            size: 'middle',
            width: '100%'
          }
        },
        buyBtn: {
          desktop: {
            text: 'Mua ngay',
            fontSize: 14,
            fontWeight: 600,
            color: '#2a2a2a',
            buttonType: 'primary',
            buttonBackgroundColor: '#ffd600',
            border: '2px solid #2a2a2a',
            boxShadow: '2px 2px 0 #2a2a2a',
            radius: 12,
            padding: { top: 8, bottom: 8, left: 16, right: 16 },
            size: 'middle',
            width: '100%',
            className: 'btn-cartoon-primary'
          },
          tablet: {
            text: 'Mua ngay',
            fontSize: 14,
            fontWeight: 600,
            color: '#2a2a2a',
            buttonType: 'primary',
            buttonBackgroundColor: '#ffd600',
            border: '2px solid #2a2a2a',
            boxShadow: '2px 2px 0 #2a2a2a',
            radius: 12,
            padding: { top: 8, bottom: 8, left: 16, right: 16 },
            size: 'middle',
            width: '100%',
            className: 'btn-cartoon-primary'
          },
          mobile: {
            text: 'Mua ngay',
            fontSize: 14,
            fontWeight: 600,
            color: '#2a2a2a',
            buttonType: 'primary',
            buttonBackgroundColor: '#ffd600',
            border: '2px solid #2a2a2a',
            boxShadow: '2px 2px 0 #2a2a2a',
            radius: 12,
            padding: { top: 8, bottom: 8, left: 16, right: 16 },
            size: 'middle',
            width: '100%',
            className: 'btn-cartoon-primary'
          }
        }
      }
    },
    {
      id: 'product2',
      data: {
        image: {
          desktop: {
            imgSrc: 'https://placehold.co/600x400/e2e8f0/334155?text=Home+Mesh+2',
            width: '100%',
            height: 160,
            imageFit: 'fill',
            align: 'center'
          },
          tablet: {
            imgSrc: 'https://placehold.co/600x400/e2e8f0/334155?text=Home+Mesh+2',
            width: '100%',
            height: 160,
            imageFit: 'fill',
            align: 'center'
          },
          mobile: {
            imgSrc: 'https://placehold.co/600x400/e2e8f0/334155?text=Home+Mesh+2',
            width: '100%',
            height: 160,
            imageFit: 'fill',
            align: 'center'
          }
        },
        title: {
          desktop: {
            text: 'Home Mesh 2+',
            fontSize: 18,
            fontWeight: 700,
            className: 'font-cartoon text-lg'
          },
          tablet: {
            text: 'Home Mesh 2+',
            fontSize: 18,
            fontWeight: 700,
            className: 'font-cartoon text-lg'
          },
          mobile: {
            text: 'Home Mesh 2+',
            fontSize: 18,
            fontWeight: 700,
            className: 'font-cartoon text-lg'
          }
        },
        promotion: {
          desktop: {
            text: 'KM: Tặng thêm 2 tháng khi đóng trước 6 tháng.',
            fontSize: 12,
            color: '#16a34a',
            className: 'font-bold text-green-600'
          },
          tablet: {
            text: 'KM: Tặng thêm 2 tháng khi đóng trước 6 tháng.',
            fontSize: 12,
            color: '#16a34a',
            className: 'font-bold text-green-600'
          },
          mobile: {
            text: 'KM: Tặng thêm 2 tháng khi đóng trước 6 tháng.',
            fontSize: 12,
            color: '#16a34a',
            className: 'font-bold text-green-600'
          }
        },
        price: {
          desktop: {
            text: '245.000đ<span class="text-base font-normal">/tháng</span>',
            fontSize: 20,
            fontWeight: 700,
            className: 'text-xl font-bold text-fun-dark mt-auto pt-2'
          },
          tablet: {
            text: '245.000đ<span class="text-base font-normal">/tháng</span>',
            fontSize: 20,
            fontWeight: 700,
            className: 'text-xl font-bold text-fun-dark mt-auto pt-2'
          },
          mobile: {
            text: '245.000đ<span class="text-base font-normal">/tháng</span>',
            fontSize: 18,
            fontWeight: 700,
            className: 'text-xl font-bold text-fun-dark mt-auto pt-2'
          }
        },
        detailBtn: {
          desktop: {
            text: 'Chi tiết',
            fontSize: 14,
            fontWeight: 600,
            color: '#2a2a2a',
            buttonType: 'primary',
            buttonBackgroundColor: '#e0e0e0',
            border: '2px solid #2a2a2a',
            radius: 8,
            padding: { top: 8, bottom: 8, left: 16, right: 16 },
            size: 'middle',
            width: '100%'
          },
          tablet: {
            text: 'Chi tiết',
            fontSize: 14,
            fontWeight: 600,
            color: '#2a2a2a',
            buttonType: 'primary',
            buttonBackgroundColor: '#e0e0e0',
            border: '2px solid #2a2a2a',
            radius: 8,
            padding: { top: 8, bottom: 8, left: 16, right: 16 },
            size: 'middle',
            width: '100%'
          },
          mobile: {
            text: 'Chi tiết',
            fontSize: 14,
            fontWeight: 600,
            color: '#2a2a2a',
            buttonType: 'primary',
            buttonBackgroundColor: '#e0e0e0',
            border: '2px solid #2a2a2a',
            radius: 8,
            padding: { top: 8, bottom: 8, left: 16, right: 16 },
            size: 'middle',
            width: '100%'
          }
        },
        buyBtn: {
          desktop: {
            text: 'Mua ngay',
            fontSize: 14,
            fontWeight: 600,
            color: '#2a2a2a',
            buttonType: 'primary',
            buttonBackgroundColor: '#ffd600',
            border: '2px solid #2a2a2a',
            boxShadow: '2px 2px 0 #2a2a2a',
            radius: 12,
            padding: { top: 8, bottom: 8, left: 16, right: 16 },
            size: 'middle',
            width: '100%',
            className: 'btn-cartoon-primary'
          },
          tablet: {
            text: 'Mua ngay',
            fontSize: 14,
            fontWeight: 600,
            color: '#2a2a2a',
            buttonType: 'primary',
            buttonBackgroundColor: '#ffd600',
            border: '2px solid #2a2a2a',
            boxShadow: '2px 2px 0 #2a2a2a',
            radius: 12,
            padding: { top: 8, bottom: 8, left: 16, right: 16 },
            size: 'middle',
            width: '100%',
            className: 'btn-cartoon-primary'
          },
          mobile: {
            text: 'Mua ngay',
            fontSize: 14,
            fontWeight: 600,
            color: '#2a2a2a',
            buttonType: 'primary',
            buttonBackgroundColor: '#ffd600',
            border: '2px solid #2a2a2a',
            boxShadow: '2px 2px 0 #2a2a2a',
            radius: 12,
            padding: { top: 8, bottom: 8, left: 16, right: 16 },
            size: 'middle',
            width: '100%',
            className: 'btn-cartoon-primary'
          }
        }
      }
    },
    {
      id: 'product3',
      data: {
        image: {
          desktop: {
            imgSrc: 'https://placehold.co/600x400/e2e8f0/334155?text=BIG120',
            width: '100%',
            height: 160,
            imageFit: 'fill',
            align: 'center'
          },
          tablet: {
            imgSrc: 'https://placehold.co/600x400/e2e8f0/334155?text=Deco+X50',
            width: '100%',
            height: 160,
            imageFit: 'fill',
            align: 'center'
          },
          mobile: {
            imgSrc: 'https://placehold.co/600x400/e2e8f0/334155?text=Deco+X50',
            width: '100%',
            height: 160,
            imageFit: 'fill',
            align: 'center'
          }
        },
        title: {
          desktop: {
            text: 'BIG120',
            fontSize: 18,
            fontWeight: 700,
            className: 'font-cartoon text-lg'
          },
          tablet: {
            text: 'BIG120',
            fontSize: 18,
            fontWeight: 700,
            className: 'font-cartoon text-lg'
          },
          mobile: {
            text: 'BIG120',
            fontSize: 18,
            fontWeight: 700,
            className: 'font-cartoon text-lg'
          }
        },
        promotion: null, // Không có promotion cho product3
        price: {
          desktop: {
            text: '120.000đ<span class="text-base font-normal">/tháng</span>',
            fontSize: 20,
            fontWeight: 700,
            className: 'text-xl font-bold text-fun-dark mt-auto pt-2'
          },
          tablet: {
            text: '120.000đ<span class="text-base font-normal">/tháng</span>',
            fontSize: 20,
            fontWeight: 700,
            className: 'text-xl font-bold text-fun-dark mt-auto pt-2'
          },
          mobile: {
            text: '120.000đ<span class="text-base font-normal">/tháng</span>',
            fontSize: 18,
            fontWeight: 700,
            className: 'text-xl font-bold text-fun-dark mt-auto pt-2'
          }
        },
        detailBtn: {
          desktop: {
            text: 'Chi tiết',
            fontSize: 14,
            fontWeight: 600,
            color: '#2a2a2a',
            buttonType: 'primary',
            buttonBackgroundColor: '#e0e0e0',
            border: '2px solid #2a2a2a',
            radius: 8,
            padding: { top: 8, bottom: 8, left: 16, right: 16 },
            size: 'middle',
            width: '100%'
          },
          tablet: {
            text: 'Chi tiết',
            fontSize: 14,
            fontWeight: 600,
            color: '#2a2a2a',
            buttonType: 'primary',
            buttonBackgroundColor: '#e0e0e0',
            border: '2px solid #2a2a2a',
            radius: 8,
            padding: { top: 8, bottom: 8, left: 16, right: 16 },
            size: 'middle',
            width: '100%'
          },
          mobile: {
            text: 'Chi tiết',
            fontSize: 14,
            fontWeight: 600,
            color: '#2a2a2a',
            buttonType: 'primary',
            buttonBackgroundColor: '#e0e0e0',
            border: '2px solid #2a2a2a',
            radius: 8,
            padding: { top: 8, bottom: 8, left: 16, right: 16 },
            size: 'middle',
            width: '100%'
          }
        },
        buyBtn: {
          desktop: {
            text: 'Mua ngay',
            fontSize: 14,
            fontWeight: 600,
            color: '#2a2a2a',
            buttonType: 'primary',
            buttonBackgroundColor: '#ffd600',
            border: '2px solid #2a2a2a',
            boxShadow: '2px 2px 0 #2a2a2a',
            radius: 12,
            padding: { top: 8, bottom: 8, left: 16, right: 16 },
            size: 'middle',
            width: '100%',
            className: 'btn-cartoon-primary'
          },
          tablet: {
            text: 'Mua ngay',
            fontSize: 14,
            fontWeight: 600,
            color: '#2a2a2a',
            buttonType: 'primary',
            buttonBackgroundColor: '#ffd600',
            border: '2px solid #2a2a2a',
            boxShadow: '2px 2px 0 #2a2a2a',
            radius: 12,
            padding: { top: 8, bottom: 8, left: 16, right: 16 },
            size: 'middle',
            width: '100%',
            className: 'btn-cartoon-primary'
          },
          mobile: {
            text: 'Mua ngay',
            fontSize: 14,
            fontWeight: 600,
            color: '#2a2a2a',
            buttonType: 'primary',
            buttonBackgroundColor: '#ffd600',
            border: '2px solid #2a2a2a',
            boxShadow: '2px 2px 0 #2a2a2a',
            radius: 12,
            padding: { top: 8, bottom: 8, left: 16, right: 16 },
            size: 'middle',
            width: '100%',
            className: 'btn-cartoon-primary'
          }
        }
      }
    },
    {
      id: 'product4',
      data: {
        image: {
          desktop: {
            imgSrc: 'https://placehold.co/600x400/e2e8f0/334155?text=Home+Camera',
            width: '100%',
            height: 160,
            imageFit: 'fill',
            align: 'center'
          },
          tablet: {
            imgSrc: 'https://placehold.co/600x400/e2e8f0/334155?text=Archer+AX73',
            width: '100%',
            height: 128,
            imageFit: 'fill',
            align: 'center'
          },
          mobile: {
            imgSrc: 'https://placehold.co/600x400/e2e8f0/334155?text=Archer+AX73',
            width: '100%',
            height: 96,
            imageFit: 'fill',
            align: 'center'
          }
        },
        title: {
          desktop: {
            text: 'Home Camera',
            fontSize: 18,
            fontWeight: 700,
            className: 'font-cartoon text-lg'
          },
          tablet: {
            text: 'Home Camera',
            fontSize: 18,
            fontWeight: 700,
            className: 'font-cartoon text-lg'
          },
          mobile: {
            text: 'Home Camera',
            fontSize: 18,
            fontWeight: 700,
            className: 'font-cartoon text-lg'
          }
        },
        promotion: {
          desktop: {
            text: 'KM: Tặng 01 Camera khi lắp mới.',
            className: 'my-2 p-2 text-xs bg-orange-50 font-bold rounded border border-orange-200'
          },
          tablet: {
            text: 'KM: Tặng 01 Camera khi lắp mới.',
            className: 'my-2 p-2 text-xs bg-orange-50 font-bold rounded border border-orange-200'
          },
          mobile: {
            text: 'KM: Tặng 01 Camera khi lắp mới.',
            className: 'my-2 p-2 text-xs bg-orange-50 font-bold rounded border border-orange-200'
          }
        },
        price: {
          desktop: {
            text: '210.000đ<span class="text-base font-normal">/tháng</span>',
            fontSize: 20,
            fontWeight: 700,
            className: 'text-xl font-bold text-fun-dark mt-auto pt-2'
          },
          tablet: {
            text: '210.000đ<span class="text-base font-normal">/tháng</span>',
            fontSize: 20,
            fontWeight: 700,
            className: 'text-xl font-bold text-fun-dark mt-auto pt-2'
          },
          mobile: {
            text: '210.000đ<span class="text-base font-normal">/tháng</span>',
            fontSize: 18,
            fontWeight: 700,
            className: 'text-xl font-bold text-fun-dark mt-auto pt-2'
          }
        },
        detailBtn: {
          desktop: {
            text: 'Chi tiết',
            fontSize: 14,
            fontWeight: 600,
            color: '#2a2a2a',
            buttonType: 'primary',
            buttonBackgroundColor: '#e0e0e0',
            border: '2px solid #2a2a2a',
            radius: 8,
            padding: { top: 8, bottom: 8, left: 16, right: 16 },
            size: 'middle',
            width: '100%'
          },
          tablet: {
            text: 'Chi tiết',
            fontSize: 14,
            fontWeight: 600,
            color: '#2a2a2a',
            buttonType: 'primary',
            buttonBackgroundColor: '#e0e0e0',
            border: '2px solid #2a2a2a',
            radius: 8,
            padding: { top: 8, bottom: 8, left: 16, right: 16 },
            size: 'middle',
            width: '100%'
          },
          mobile: {
            text: 'Chi tiết',
            fontSize: 14,
            fontWeight: 600,
            color: '#2a2a2a',
            buttonType: 'primary',
            buttonBackgroundColor: '#e0e0e0',
            border: '2px solid #2a2a2a',
            radius: 8,
            padding: { top: 8, bottom: 8, left: 16, right: 16 },
            size: 'middle',
            width: '100%'
          }
        },
        buyBtn: {
          desktop: {
            text: 'Mua ngay',
            fontSize: 14,
            fontWeight: 600,
            color: '#2a2a2a',
            buttonType: 'primary',
            buttonBackgroundColor: '#ffd600',
            border: '2px solid #2a2a2a',
            boxShadow: '2px 2px 0 #2a2a2a',
            radius: 8,
            padding: { top: 8, bottom: 8, left: 16, right: 16 },
            size: 'middle',
            width: '100%',
            className: 'btn-cartoon-primary'
          },
          tablet: {
            text: 'Mua ngay',
            fontSize: 14,
            fontWeight: 600,
            color: '#2a2a2a',
            buttonType: 'primary',
            buttonBackgroundColor: '#ffd600',
            border: '2px solid #2a2a2a',
            boxShadow: '2px 2px 0 #2a2a2a',
            radius: 8,
            padding: { top: 8, bottom: 8, left: 16, right: 16 },
            size: 'middle',
            width: '100%',
            className: 'btn-cartoon-primary'
          },
          mobile: {
            text: 'Mua ngay',
            fontSize: 14,
            fontWeight: 600,
            color: '#2a2a2a',
            buttonType: 'primary',
            buttonBackgroundColor: '#ffd600',
            border: '2px solid #2a2a2a',
            boxShadow: '2px 2px 0 #2a2a2a',
            radius: 8,
            padding: { top: 8, bottom: 8, left: 16, right: 16 },
            size: 'middle',
            width: '100%',
            className: 'btn-cartoon-primary'
          }
        }
      }
    }
  ]

  // Tạo props cho tất cả sản phẩm
  const allProps = products.reduce((acc, product) => {
    const productProps = createProductProps(product.id, product.data)

    return { ...acc, ...productProps }
  }, {})

  return (
    <div
      className='w-full'
      ref={(ref: HTMLDivElement | null) => {
        if (ref) {
          connect(drag(ref))
        }
      }}
    >
      <section className='py-12'>
        <div className='container mx-auto px-4'>
          <div className='mb-10'>
            <Element is={Text} id='section-title' canvas {...props.sectionTitle} />
          </div>
          <div className='grid grid-cols-4 gap-8 sm:grid-cols-2'>
            {products.map(product => (
              <ProductCard key={product.id} productId={product.id} props={allProps} />
            ))}
          </div>
        </div>
      </section>
    </div>
  )
}

BestSellingProductsTeenBuilder.craft = {
  displayName: 'Best Selling Products Teen',
  props: {
    sectionTitle: {
      desktop: {
        text: 'Sản phẩm bán chạy',
        fontSize: 40,
        fontWeight: 700,
        color: '#2a2a2a',
        textAlign: 'center',
        className: '[text-shadow:3px_3px_0_#ffffff,_6px_6px_0_rgba(0,0,0,0.1)]'
      },
      tablet: {
        text: 'Sản phẩm bán chạy',
        fontSize: 32,
        fontWeight: 700,
        color: '#2a2a2a',
        textAlign: 'center',
        className: '[text-shadow:3px_3px_0_#ffffff,_6px_6px_0_rgba(0,0,0,0.1)]'
      },
      mobile: {
        text: 'Sản phẩm bán chạy',
        fontSize: 32,
        fontWeight: 700,
        color: '#2a2a2a',
        textAlign: 'center',
        className: '[text-shadow:3px_3px_0_#ffffff,_6px_6px_0_rgba(0,0,0,0.1)]'
      }
    }
  },
  rules: {
    canDrag: () => true,
    canMoveIn: () => false
  }
}
