'use client'

import { Element, useNode, type UserComponent } from '@craftjs/core'

import { useSelector } from 'react-redux'

import { Image, Text, SocialMedia } from '@/components/page-builder/selectors'
import { useResponsive } from '@/hooks'
import { selectScreenType } from '@/redux-store/slices/builderSlice'
import { defaultResponseSocialMediaProps } from '@/constants/page-builder/socialMedia'

export const HeaderTeenBuilder: UserComponent = props => {
  const {
    connectors: { connect, drag }
  } = useNode()

  const screenType = useSelector(selectScreenType)
  const { isMobile } = useResponsive()

  return (
    <div
      className='w-full'
      ref={(ref: HTMLDivElement | null) => {
        if (ref) {
          connect(drag(ref))
        }
      }}
    >
      <header className='border-fun-dark sticky top-0 z-50 border-b-4 bg-white shadow-md'>
        <div className='container mx-auto px-4 py-3'>
          <div
            className={
              isMobile || screenType === 'mobile'
                ? 'flex flex-col items-center justify-between'
                : 'flex items-center justify-between sm:flex-col'
            }
          >
            <div className='mb-0 flex items-center gap-4 sm:mb-3'>
              <div className='shrink-0'>
                <Element is={Image} id='logo' canvas {...props.logo} />
              </div>
              <div className='block h-8 border-l border-solid border-gray-300 sm:hidden'></div>
              <div className='flex items-center gap-3'>
                <div className='shrink-0'>
                  <Element is={Image} id='expert-avatar' canvas {...props.expertAvatar} />
                </div>
                <div className='shrink-0'>
                  <Element is={Text} id='expert-name' canvas {...props.expertName} />
                  <Element is={Text} id='expert-title' canvas {...props.expertTitle} />
                </div>
              </div>
            </div>
            <div className='text-fun-dark flex items-center gap-2 text-sm'>
              <Element is={SocialMedia} id='contact-social-media' canvas {...props.contactSocialMedia} />
              <div className='h-5 border-l border-gray-300'></div>
              <div className='flex items-center gap-1'>
                <Element is={SocialMedia} id='social-media-links' canvas {...props.socialMediaLinks} />
              </div>
            </div>
          </div>
        </div>
      </header>
    </div>
  )
}

HeaderTeenBuilder.craft = {
  props: {
    logo: {
      desktop: {
        imgSrc: '/assets/images/logo.svg',
        width: 'auto',
        height: 40,
        className: 'h-8 md:h-10',
        border: {
          radius: 0,
          thickness: 0,
          color: '',
          style: 'none'
        }
      },
      tablet: {
        imgSrc: '/assets/images/logo.svg',
        width: 'auto',
        height: 40,
        className: 'h-8 md:h-10',
        border: {
          radius: 0,
          thickness: 0,
          color: '',
          style: 'none'
        }
      },
      mobile: {
        imgSrcMobile: '/assets/images/logo.svg',
        width: 'auto',
        height: 32,
        className: 'h-8 md:h-10',
        border: {
          radius: 0,
          thickness: 0,
          color: '',
          style: 'none'
        }
      }
    },
    expertAvatar: {
      desktop: {
        imgSrc: 'https://placehold.co/80x80/3D5AFE/FFFFFF?text=An&font=baloo-2',
        width: 48,
        height: 48,
        border: {
          radius: 0,
          thickness: 0,
          color: '',
          style: 'none'
        },
        imageFit: 'fill',
        align: 'center',
        className: 'w-12 h-12 rounded-full object-cover'
      },
      tablet: {
        imgSrc: 'https://placehold.co/80x80/3D5AFE/FFFFFF?text=An&font=baloo-2',
        width: 48,
        height: 48,
        border: {
          radius: 0,
          thickness: 0,
          color: '',
          style: 'none'
        },
        imageFit: 'fill',
        align: 'center',
        className: 'w-12 h-12 rounded-full object-cover'
      },
      mobile: {
        imgSrcMobile: 'https://placehold.co/80x80/3D5AFE/FFFFFF?text=An&font=baloo-2',
        width: 40,
        height: 40,
        border: {
          radius: 0,
          thickness: 0,
          color: '',
          style: 'none'
        },
        imageFit: 'fill',
        align: 'center',
        className: 'w-10 h-10 rounded-full object-cover'
      }
    },
    expertName: {
      desktop: {
        text: 'An An',
        fontSize: 20,
        fontWeight: 700,
        color: '#000000',
        textAlign: 'left',
        className: 'font-cartoon text-xl text-fun-blue'
      },
      tablet: {
        text: 'An An',
        fontSize: 20,
        fontWeight: 700,
        color: '#000000',
        textAlign: 'left',
        className: 'font-cartoon text-xl text-fun-blue'
      },
      mobile: {
        text: 'An An',
        fontSize: 18,
        fontWeight: 700,
        color: '#000000',
        textAlign: 'left',
        className: 'font-cartoon text-lg text-fun-blue'
      }
    },
    expertTitle: {
      desktop: {
        text: 'Chuyên gia Deal Z',
        fontSize: 14,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'left',
        className: 'text-sm text-gray-500'
      },
      tablet: {
        text: 'Chuyên gia Deal Z',
        fontSize: 14,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'left',
        className: 'text-sm text-gray-500'
      },
      mobile: {
        text: 'Chuyên gia Deal Z',
        fontSize: 12,
        fontWeight: 400,
        color: '#6b7280',
        textAlign: 'left',
        className: 'text-xs text-gray-500'
      }
    },
    contactSocialMedia: {
      desktop: {
        ...defaultResponseSocialMediaProps.desktop,
        size: 14,
        iconAlign: 'start',
        fontFamily: 'Inter, sans-serif',
        fontSize: 14,
        socialList: [
          {
            name: '0912345678',
            icon: 'PhoneFilled',
            iconColor: '#2A2A2A',
            url: '',
            displayType: 'NAME'
          },
          {
            name: '<EMAIL>',
            icon: 'MailFilled',
            iconColor: '#2A2A2A',
            url: '',
            displayType: 'NAME'
          }
        ]
      },
      tablet: {
        ...defaultResponseSocialMediaProps.tablet,
        size: 14,
        iconAlign: 'start',
        fontFamily: 'Inter, sans-serif',
        fontSize: 14,
        socialList: [
          {
            name: '0912345678',
            icon: 'PhoneFilled',
            iconColor: '#2A2A2A',
            url: '',
            displayType: 'NAME'
          },
          {
            name: '<EMAIL>',
            icon: 'MailFilled',
            iconColor: '#2A2A2A',
            url: '',
            displayType: 'NAME'
          }
        ]
      },
      mobile: {
        ...defaultResponseSocialMediaProps.mobile,
        size: 14,
        iconAlign: 'start',
        fontFamily: 'Inter, sans-serif',
        fontSize: 14,
        socialList: [
          {
            name: '0912345678',
            icon: 'PhoneFilled',
            iconColor: '#2A2A2A',
            url: '',
            displayType: 'ICON'
          },
          {
            name: '<EMAIL>',
            icon: 'MailFilled',
            iconColor: '#2A2A2A',
            url: '',
            displayType: 'ICON'
          }
        ]
      }
    },
    socialMediaLinks: {
      desktop: {
        ...defaultResponseSocialMediaProps.desktop,
        size: 14,
        iconAlign: 'start',
        socialList: [
          {
            name: 'Facebook',
            icon: 'FacebookIcon2',
            iconColor: '#2A2A2A',
            url: 'https://facebook.com',
            displayType: 'ICON'
          },
          {
            name: 'Instagram',
            icon: 'InstagramFilled',
            iconColor: '#2A2A2A',
            url: 'https://instagram.com',
            displayType: 'ICON'
          }
        ]
      },
      tablet: {
        ...defaultResponseSocialMediaProps.tablet,
        size: 14,
        iconAlign: 'start',
        socialList: [
          {
            name: 'Facebook',
            icon: 'FacebookIcon2',
            iconColor: '#2A2A2A',
            url: 'https://facebook.com',
            displayType: 'ICON'
          },
          {
            name: 'Instagram',
            icon: 'InstagramFilled',
            iconColor: '#2A2A2A',
            url: 'https://instagram.com',
            displayType: 'ICON'
          }
        ]
      },
      mobile: {
        ...defaultResponseSocialMediaProps.mobile,
        size: 16,
        iconAlign: 'start',
        socialList: [
          {
            name: 'Facebook',
            icon: 'FacebookIcon2',
            iconColor: '#2A2A2A',
            url: 'https://facebook.com',
            displayType: 'ICON'
          },
          {
            name: 'Instagram',
            icon: 'InstagramFilled',
            iconColor: '#2A2A2A',
            url: 'https://instagram.com',
            displayType: 'ICON'
          }
        ]
      }
    },
    zaloLink: {
      desktop: {
        icon: 'MessageFilled',
        iconColor: '#2A2A2A',
        title: '',
        fontSize: 18,
        fontWeight: 400,
        color: '#2A2A2A',
        href: '#',
        gap: 0,
        className: 'hover:text-green-500 text-lg',
        showIcon: { desktop: true, tablet: true, mobile: true },
        showTitle: { desktop: false, tablet: false, mobile: false }
      },
      tablet: {
        icon: 'MessageFilled',
        iconColor: '#2A2A2A',
        title: '',
        fontSize: 18,
        fontWeight: 400,
        color: '#2A2A2A',
        href: '#',
        gap: 0,
        className: 'hover:text-green-500 text-lg',
        showIcon: { desktop: true, tablet: true, mobile: true },
        showTitle: { desktop: false, tablet: false, mobile: false }
      },
      mobile: {
        icon: 'MessageFilled',
        iconColor: '#2A2A2A',
        title: '',
        fontSize: 16,
        fontWeight: 400,
        color: '#2A2A2A',
        href: '#',
        gap: 0,
        className: 'hover:text-green-500 text-lg',
        showIcon: { desktop: true, tablet: true, mobile: true },
        showTitle: { desktop: false, tablet: false, mobile: false }
      }
    }
  },
  rules: {
    canDrag: () => true,
    canMoveIn: () => false
  }
}
