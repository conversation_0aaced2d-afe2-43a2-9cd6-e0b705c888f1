'use client'
import React from 'react'

import { Element, useNode, type UserComponent } from '@craftjs/core'

import { Container, Image, Row, Column, Button, Text, Spacer } from '@/components/page-builder/selectors'
import { defaultResponseAffiliateProps } from '@/constants/page-builder/affiliate'

export const AffiliateBuilder: UserComponent = props => {
  const {
    connectors: { connect, drag }
  } = useNode()

  return (
    <div
      className='w-full'
      ref={(ref: HTMLDivElement | null) => {
        if (ref) {
          connect(drag(ref))
        }
      }}
    >
      <Element id='container' canvas is={Container} {...props.container}>
        <Element is={Row} id='row' disableSelection={true} canvas {...props.rowContainer}>
          <Element is={Image} id='image-1' canvas {...props.image1} />
          <Element is={Column} id='column' canvas {...props.column}>
            <Element is={Text} id='text1' canvas {...props.textHeading} />
            <Element is={Spacer} id='spacer' canvas {...props.spacer1} />
            <Element is={Text} id='text2' canvas {...props.text} />
            <Element is={Spacer} id='spacer' canvas {...props.spacer2} />
            <Element is={Row} id='row-2' canvas {...props.buttonRow}>
              <Element is={Button} id='button-1' canvas {...props.button1} />
              <Element is={Button} id='button-2' canvas {...props.button2} />
            </Element>
          </Element>
          <Element is={Image} id='image-2' canvas {...props.image2} />
          <Element is={Spacer} id='spacer' canvas {...props.spacer3} />
          <Element is={Row} id='row-3' canvas {...props.buttonRowMobile}>
            <Element is={Button} id='button-1' canvas {...props.button1} />
            <Element is={Button} id='button-2' canvas {...props.button2} />
          </Element>
        </Element>
      </Element>
    </div>
  )
}

AffiliateBuilder.craft = {
  props: defaultResponseAffiliateProps,
  rules: {
    canDrag: () => true,
    canMoveIn: () => false
  }
}
