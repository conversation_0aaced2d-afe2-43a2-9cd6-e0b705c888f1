import type { UserComponent } from '@craftjs/core'
import { Element, useNode } from '@craftjs/core'

import { Container, Row, Image, Column, Text, Button, Spacer } from '@/components/page-builder/selectors'
import { defaultWhyChooseUsProps } from '@/constants/page-builder/whyChooseUs'
import { IconLibrary } from '@/components/page-builder/selectors/IconLibrary'
import { MobileReasonsBuilder } from './MobileReasonsBuilder'

export const WhyChooseUsBuilder: UserComponent = props => {
  const {
    connectors: { connect, drag }
  } = useNode()

  return (
    <div
      className='w-full'
      ref={(ref: HTMLDivElement | null) => {
        if (ref) {
          connect(drag(ref))
        }
      }}
    >
      <Element id='container' canvas is={Container} {...props.container}>
        <Element is={Row} id='row' canvas {...props.rowContainer}>
          <Element is={Column} id='column-1' canvas {...props.columnContainer1}>
            <Element is={Text} id='text-1' canvas {...props.textHeading1} />
            <Element is={Spacer} id='spacer' canvas {...props.spacer} />
            <Element is={Text} id='text-1' canvas {...props.textContent1} />
            <Element is={Spacer} id='spacer' canvas {...props.spacer1} />
            <Element is={Button} id='text-1' canvas {...props.buttonDesktop} />
          </Element>
          <Element is={Image} id='image-1' canvas {...props.image} />
          <Element is={Column} id='column-2' canvas {...props.columnContainer2}>
            <Element is={Column} id='column-child' canvas {...props.columnChild}>
              <Element is={IconLibrary} id='icon-text canvas' {...props.iconLib1} />
              <Element is={Spacer} id='spacer' canvas {...props.spacer2} />
              <Element is={Text} id='text-2' canvas {...props.textContent2} />
            </Element>
            <Element is={Spacer} id='spacer' canvas {...props.spacer3} />
            <Element is={Column} id='column-child' canvas {...props.columnChild}>
              <Element is={IconLibrary} id='icon-text canvas' {...props.iconLib2} />
              <Element is={Spacer} id='spacer' canvas {...props.spacer2} />
              <Element is={Text} id='text-2' canvas {...props.textContent3} />
            </Element>
            <Element is={Spacer} id='spacer' canvas {...props.spacer3} />
            <Element is={Column} id='column-child' canvas {...props.columnChild}>
              <Element is={IconLibrary} id='icon-text canvas' {...props.iconLib3} />
              <Element is={Spacer} id='spacer' canvas {...props.spacer2} />
              <Element is={Text} id='text-2' canvas {...props.textContent4} />
            </Element>
          </Element>

          {/* mobile */}
          <MobileReasonsBuilder />
          <Element is={Spacer} id='spacer' canvas {...props.spacer4} />
          <Element is={Button} id='text-1' canvas {...props.buttonMobile} />
        </Element>
      </Element>
    </div>
  )
}

WhyChooseUsBuilder.craft = {
  props: defaultWhyChooseUsProps,
  rules: {
    canDrag: () => true,
    canMoveIn: () => false
  }
  // related: {
  //   toolbar: MenuBarSetting
  // }
}
