import React from 'react'

import { Element, useNode } from '@craftjs/core'

import { Container, Button, Image, Row, Spacer, Text } from '@/components/page-builder/selectors'
import { Column } from '@/components/page-builder/selectors/Column'
import { defaultResponsiveSupplyPartnerProps } from '@/constants/page-builder/supplyPartner'

export const SupplyPartnerBuilder = (props: any) => {
  const {
    connectors: { connect, drag }
  } = useNode()

  return (
    <div
      className='w-full'
      ref={(ref: HTMLDivElement | null) => {
        if (ref) {
          connect(drag(ref))
        }
      }}
    >
      <Element id='container' canvas is={Container} {...props.container}>
        <Element is={Row} id='row' disableSelection={true} canvas {...props.rowContainer}>
          <Element is={Column} id='col2' canvas {...props.columnMobile}>
            <Element is={Image} id='image' canvas {...props.imageMobile} />
          </Element>
          <Element is={Column} id='col1' canvas {...props.column1}>
            <Element is={Text} id='title' canvas {...props.title} />
            <Spacer id='spacer' canvas {...props.spacer} />
            <Element is={Text} id='description' canvas {...props.description} />
            <Spacer id='spacer' canvas {...props.spacer2} />
            <Element is={Row} id='row-2' canvas {...props.buttonRow}>
              <Element is={Button} id='button-1' canvas {...props.button1} />
              <Element is={Button} id='button-2' canvas {...props.button2} />
            </Element>
          </Element>
          {/* <Element is={Column} id='col2' canvas {...props.column2}>
          <Element is={Image} id='image' canvas {...props.rightImage} />
        </Element> */}
        </Element>
      </Element>
    </div>
  )
}

SupplyPartnerBuilder.craft = {
  props: defaultResponsiveSupplyPartnerProps,
  rules: {
    canDrag: () => true,
    canMoveIn: () => false
  }
  // related: {
  //   toolbar: MenuBarSetting
  // }
}
