'use client'
import React from 'react'

import { Element, useNode } from '@craftjs/core'

import DigitalSolution from '@/views/sme-portal/sections/DigitalSolution'
import { Container, Row, Column, Text, Spacer } from '@/components/page-builder/selectors'
import { defaultResponseDigitalSolutionProps } from '@/constants/page-builder/digitalSolution'

export const DigitalSolutionBuilder = (props: any) => {
  const {
    connectors: { connect, drag }
  } = useNode()

  return (
    <div
      className='w-full'
      ref={(ref: HTMLDivElement | null) => {
        if (ref) {
          connect(drag(ref))
        }
      }}
    >
      <Element id='container' canvas is={Container} {...props.container}>
        <Element is={Row} id='row' disableSelection={true} canvas {...props.rowContainer}>
          <Element is={Column} id='column' canvas {...props.column}>
            <Element is={Text} id='text-1' canvas {...props.text1} />
            <Element is={Spacer} id='spacer' canvas {...props.spacer} />
            <Element is={Text} id='text-2' canvas {...props.text2} />
          </Element>
        </Element>
      </Element>
      <DigitalSolution hideTextBuilder={true} />
    </div>
  )
}

DigitalSolutionBuilder.craft = {
  props: defaultResponseDigitalSolutionProps,
  rules: {
    canDrag: () => true,
    canMoveIn: () => false
  }
}
