import React, { useEffect, useState } from 'react'

import dayjs from 'dayjs'

import moment from 'moment'

import { isEmpty, trim } from 'lodash'
import { useSelector } from 'react-redux'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { Button, Col, DatePicker, Form, Input, Modal, Row, Select } from 'antd'

import { IDENTITY_TYPE } from '@/constants/constantEnum'

import { message } from '@components/notification'

import { useNewLocation } from '@/hooks/useNewLocation'

import SmeProfile from '@/models/SmeProfile'

import useOpenMyAddress from '@views/payment/hooks/useOpenMyAddress'

import SmeSubscription from '@/models/SmeSubscription'

import { processDetailService } from '@views/enterprise/convert'

import {
  validateCode,
  validateEmail,
  validateLengthTinNoRequire,
  validateMaxLengthStr,
  validatePhoneNumber,
  validateRequireInput
} from '@/validator'

import {
  idSetupAddressUpdateSelect,
  isEditAddressSelect,
  profileInfoSelect,
  setupAddressListSelect
} from '@/redux-store/slices/PaymentSlice'

import { useUser } from '@/hooks'

import { handleAddress } from '@views/payment/utils/serviceUtils'

export const convertIdToName = (id: any) => {
  if (id === 1) return 'CMTND_CCCD'
  if (id === 2) return 'PASSPORT'
  if (id === 3) return 'CCCD'

  return 'OTHER'
}

const ModalInfoProfileCompany = ({ isOpenProfileInfo }: { isOpenProfileInfo: boolean }) => {
  const [form] = Form.useForm()
  const { handleCloseProfileInfo, handleSetIsNewAcc } = useOpenMyAddress()
  const setupAddressList = useSelector(setupAddressListSelect)
  const isEditAddress = useSelector(isEditAddressSelect)
  const idSetupAddressUpdate = useSelector(idSetupAddressUpdateSelect)
  const { provinceList, wardList, streetList, updateAddress } = useNewLocation()
  const queryClient = useQueryClient()
  const dataCompanyInfo = useSelector(profileInfoSelect)
  const regex = /^[0-9]+$/
  const { user } = useUser()

  const customerType = user.customerType

  const [locationSearch, setLocationSearch] = useState({
    city: '',
    ward: '',
    street: ''
  })

  const handleSearchLocation = (field: string, value: string) => {
    setLocationSearch({ ...locationSearch, [field]: value })
  }

  const handleFilterLocation = (input: string, option: any) =>
    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())

  const updateAccountMutation = useMutation({
    mutationKey: ['updateAccountMutation'],
    mutationFn: SmeSubscription.updateSmeProfile,
    onMutate: () => {
      message.loading('Đang cập nhật...')
    },
    onSuccess: () => {
      message.success('Cập nhật thông tin thành công')
      queryClient.invalidateQueries({ queryKey: ['getSetupAddress'] })
      queryClient.invalidateQueries({ queryKey: ['getInfoCustomer'] })
      queryClient.invalidateQueries({ queryKey: ['getCustomerAddressList'] })
      queryClient.invalidateQueries({ queryKey: ['getProfileUserInApp'] })
      handleCloseProfileInfo()
      handleSetIsNewAcc()
    },
    onError: () => message.error('Cập nhật thông tin thất bại')
  })

  const handleOk = (data: any) => {
    const dataSubmit = { ...data, repFullName: data?.repFullName }

    dataSubmit.icon = dataSubmit?.icon?.id
    dataSubmit.description = dataSubmit?.description != null ? dataSubmit?.description.trim() : ''
    dataSubmit.updateType = user?.customerType
    dataSubmit.isConfirmUpdate = false
    dataSubmit.isUpdateEnterpriseSme = true
    dataSubmit.identityCreatedDate = moment(dataSubmit?.identityCreatedDate).format('DD/MM/YYYY')
    dataSubmit.countryId = 1

    // Tìm province dựa trên provinceId
    const selectedProvince = provinceList.find(
      (province: { value: number }) => province.value === dataSubmit?.provinceId
    )

    dataSubmit.provinceCode = selectedProvince ? selectedProvince?.code : ''

    if (customerType !== 'PERSONAL') {
      updateAccountMutation.mutate({
        ...dataSubmit
      })
    } else {
      updateAccountMutation.mutate({
        ...dataSubmit,
        identityType: dataSubmit?.repIdentityType != null && data.repIdentityType,
        identityNo: dataSubmit?.repIdentityNo != null && data.repIdentityNo
      })
    }

    form.resetFields()
  }

  // Thông tin doanh nghiệp
  // const {
  //   data: dataCompanyInfo
  //   // refetch
  //   // isFetching: isFetchingDataCompanyInfo
  // } = useQuery({
  //   queryKey: ['dataCompanyInfo'],
  //   queryFn: async () => {
  //     const res = await Users.getSmeInfor({ customerTypeCode: user.customerType })
  //
  //     // if (res.taxCode) res.taxCode = DX.callFormatTaxCode(res.taxCode);
  //     // const data = {
  //     //   ...res,
  //     //   provinceId: res.provinceId ? `${res.provinceId}/${res.provinceCode}` : null,
  //     //   repFullName: res?.repFullName || null,
  //     //   identityCreatedDate: res.identityCreatedDate ? moment(res.identityCreatedDate, 'DD/MM/YYYY') : undefined
  //     // }
  //     // form.setFieldsValue(data)
  //     return res
  //   }
  // })

  const isDataCompany =
    !!dataCompanyInfo?.repFullName &&
    // !!dataCompanyInfo.countryId &&
    !!dataCompanyInfo?.provinceId &&
    !!dataCompanyInfo?.wardId &&
    !!dataCompanyInfo?.streetId &&
    !!dataCompanyInfo?.address &&
    !!dataCompanyInfo?.phoneNumber &&
    !!dataCompanyInfo?.taxCode &&
    !!dataCompanyInfo?.identityTypeHKD

  useEffect(() => {
    // if (!isEditAddress) return

    const setupAddress = dataCompanyInfo

    if (!setupAddress) return

    const { provinceId, wardId } = setupAddress

    if (provinceId) updateAddress('provinceId', provinceId)
    if (wardId) updateAddress('wardId', wardId)

    const newAddress = {
      name: setupAddress.smeName,
      taxCode: setupAddress.tin,
      identityTypeHKD: setupAddress.identityTypeHKD,
      repIdentityNo: setupAddress.repIdentityNo,
      ...setupAddress,
      identityCreatedDate: setupAddress?.identityCreatedDate
        ? dayjs(setupAddress?.identityCreatedDate, 'DD/MM/YYYY')
        : null
    }

    form.setFieldsValue(newAddress)
  }, [
    isEditAddress,
    idSetupAddressUpdate,
    setupAddressList,
    updateAddress,
    form,
    dataCompanyInfo,
    isDataCompany,
    user.customerType
  ])

  const handleCreateAddress = (field: any, option: any) => {
    handleAddress(field, option, form, 'address')
  }

  const handleClose = () => {
    form.resetFields()
    handleCloseProfileInfo()
  }

  const [checkTin, setCheckTin] = useState(false)
  const [checkTinValue, setCheckTinValue] = useState(null)

  const { refetch: refetchCheckTinData } = useQuery({
    queryKey: ['getListAddress12'],
    queryFn: async () => {
      const res: any = await SmeSubscription.getListTin1(checkTinValue).catch(error => {
        if (!isEmpty(checkTinValue)) {
          if (error?.errorCode === 'error.data.exists') {
            message.error('Mã số thuế đã tồn tại')
            form.setFields([
              {
                name: 'taxCode',
                errors: ['Mã số thuế đã tồn tại trong hệ thống']
              }
            ])
            setCheckTin(true)
          }

          setCheckTinValue(null)
        }

        setCheckTin(false)
      })

      return processDetailService(res)
    },

    enabled: !isEmpty(checkTinValue)
  })

  useEffect(() => {
    if (!isEmpty(checkTinValue)) refetchCheckTinData()
  }, [checkTinValue, refetchCheckTinData])

  const { data: dataPersonalCertType } = useQuery({
    initialData: [],
    queryKey: ['getPersonalCertType11'],
    queryFn: async () => {
      const res = await SmeProfile.getPersonalCertType()

      return res.map((item: any) => ({ label: item.name, value: convertIdToName(item.id) }))
    }
  })

  return (
    <>
      {isOpenProfileInfo && (
        <Modal
          width={632}
          title={
            <div className='text-base font-semibold leading-normal tracking-tight'>
              {customerType !== 'PERSONAL' ? 'Thông tin hồ sơ doanh nghiệp' : 'Thông tin hồ sơ cá nhân'}
              <div className='text-sm font-normal'>
                Vui lòng nhập đầy đủ thông tin hồ sơ cá nhân để tiếp tục thanh toán
              </div>
              <div className='text-sm font-normal'>Lưu ý: thông tin sẽ được dùng để xuất hoá đơn.</div>
            </div>
          }
          open={isOpenProfileInfo}
          onOk={form.submit}
          onCancel={handleClose}
          footer={[
            <>
              <Button className='ml-4 w-[120px]' type='primary' onClick={form.submit} disabled={checkTin}>
                Cập nhật
              </Button>
              <Button className='w-[120px] bg-bg-primary-light text-text-primary-default' onClick={handleClose}>
                Hủy
              </Button>
            </>
          ]}
        >
          <Form onFinish={handleOk} form={form} autoComplete='off' layout='vertical'>
            <div className='cusom-label my-6'>
              {customerType !== 'PERSONAL' ? (
                <>
                  {dataCompanyInfo?.identityTypeHKD === IDENTITY_TYPE.CERT_NUMBER && (
                    <Form.Item
                      label={<div className='text-xs'>Giấy chứng thực</div>}
                      name='repPersonalCertTypeId'
                      rules={[validateRequireInput('Giấy chứng thực không được bỏ trống')]}
                      className='mb-3 w-full sm:mb-4'
                      required
                    >
                      <Select className='w-full' options={dataPersonalCertType} placeholder='Chọn giấy chứng thực' />
                    </Form.Item>
                  )}

                  <div className='flex w-full gap-4 text-sm'>
                    {dataCompanyInfo?.identityTypeHKD !== IDENTITY_TYPE.CERT_NUMBER ? (
                      <Form.Item
                        label={<div className='text-xs '>Mã số thuế</div>}
                        name='taxCode'
                        rules={[
                          validateRequireInput('Mã số thuế không được bỏ trống'),
                          validateLengthTinNoRequire(10, 13, 'Sai định dạng mã số thuế'),
                          validateCode('Không được chứa chữ cái hoặc ký tự đặc biệt', regex)
                        ]}
                        className='w-1/2 text-sm'
                        required
                      >
                        <Input
                          allowClear
                          maxLength={13}
                          placeholder='Nhập mã số thuế'
                          onBlur={e => {
                            if (
                              !isEmpty(e.target.value) &&
                              Number(e.target.value) !== Number(dataCompanyInfo.taxCode) &&
                              (e.target.value.length === 10 || e.target.value.length === 13)
                            ) {
                              // @ts-ignore
                              setCheckTinValue(e?.target.value)
                            }
                          }}
                        />
                      </Form.Item>
                    ) : (
                      <Form.Item
                        className='w-1/2 text-sm'
                        label={<div className='text-xs'>Số chứng thực cá nhân</div>}
                        // type='text'
                        name='repIdentityNo'
                        rules={[validateRequireInput('Số chứng thực cá nhân không được bỏ trống')]}
                      >
                        <Input
                          className='text-sm'
                          maxLength={30}
                          disabled={true}
                          placeholder='Nhập số chứng thực cá nhân'
                        />
                      </Form.Item>
                    )}

                    <Form.Item
                      label={<div className='text-xs'>Tên doanh nghiệp</div>}
                      name='name'
                      rules={[
                        validateRequireInput('Tên doanh nghiệp không được bỏ trống'),
                        validateMaxLengthStr(300, 'Nhập tối đa 300 ký tự')
                      ]}
                      className='w-1/2 text-sm'
                      required
                    >
                      <Input maxLength={300} placeholder='Nhập tên doanh nghiệp' />
                    </Form.Item>
                  </div>
                  <div className='mb-4 w-full'>
                    <Form.Item
                      label={<div className='text-xs'>Tên người liên hệ</div>}
                      name='repFullName'
                      rules={[
                        validateRequireInput('Tên người liên hệ không được bỏ trống'),
                        validateMaxLengthStr(300, 'Nhập tối đa 300 ký tự')
                      ]}
                      className='mb-3 sm:mb-4'
                      required
                    >
                      <Input allowClear maxLength={300} placeholder='Nhập tên người liên hệ' />
                    </Form.Item>
                  </div>

                  <div className='mb-4 mt-3 w-full'>
                    <Form.Item
                      label={<div className='text-xs'>Số điện thoại người liên hệ</div>}
                      name='phoneNumber'
                      normalize={trim}
                      rules={[
                        validateRequireInput('Số điện thoại người liên hệ không được bỏ trống'),
                        validatePhoneNumber('Sai định dạng số điện thoại')
                      ]}
                      className='mb-3 sm:mb-4'
                      required
                    >
                      <Input maxLength={30} placeholder='Nhập số điện thoại người liên hệ' />
                    </Form.Item>
                  </div>
                </>
              ) : (
                <>
                  <div className='flex w-full gap-4 text-sm'>
                    <div className='w-1/2'>
                      <Form.Item
                        name='lastName'
                        className='text-sm'
                        rules={[
                          validateRequireInput('Họ không được bỏ trống'),
                          validateMaxLengthStr(20, 'Nhập tối đa 20 ký tự')
                        ]}
                        label={<div className='text-xs'>Họ</div>}
                      >
                        <Input type='text' placeholder='Nhập Họ' maxLength={20} className='text-sm' />
                      </Form.Item>
                    </div>
                    <div className='w-1/2 '>
                      <Form.Item
                        name='firstName'
                        label={<div className='text-xs'>Tên</div>}
                        className='text-sm'
                        rules={[
                          validateRequireInput('Tên không được bỏ trống'),
                          validateMaxLengthStr(20, 'Nhập tối đa 20 ký tự')
                        ]}
                      >
                        <Input placeholder='Nhập Tên' maxLength={20} className='text-sm' />
                      </Form.Item>
                    </div>
                  </div>

                  <Form.Item
                    name='email'
                    className='text-sm'
                    rules={[validateRequireInput('Email không được bỏ trống'), validateEmail('Sai định dạng Email')]}
                    label={<div className='text-xs'>Email</div>}
                  >
                    <Input
                      type='text'
                      className='text-sm'
                      placeholder='Nhập Email'
                      maxLength={100}
                      disabled={user?.userProviderType !== 'PHONE'}
                    />
                  </Form.Item>
                  <Form.Item
                    name='phoneNumber'
                    label={<div className='text-xs'>Số điện thoại</div>}
                    className='text-sm'
                    rules={[
                      validateRequireInput('Số điện thoại không được bỏ trống'),
                      validatePhoneNumber('Sai định dạng số điện thoại')
                    ]}
                  >
                    <Input className='text-sm' type='phoneNumber' placeholder='Nhập số điện thoại' />
                  </Form.Item>
                  <Form.Item
                    label={<div className='text-xs'>Giấy chứng thực cá nhân</div>}
                    name='repIdentityType'
                    className='text-sm'
                    rules={[validateRequireInput('Giấy chứng thực không được bỏ trống')]}
                  >
                    <Select
                      className='text-sm'
                      // disabled={!CAN_UPDATE}
                      options={dataPersonalCertType}
                      placeholder='Giấy chứng thực'
                    />
                  </Form.Item>
                  <Row gutter={24}>
                    <Col span={12}>
                      <Form.Item
                        className='text-sm'
                        label={<div className='text-xs'>Số chứng thực cá nhân</div>}
                        // type='text'
                        name='repIdentityNo'
                        rules={[validateRequireInput('Số chứng thực cá nhân không được bỏ trống')]}
                      >
                        <Input className='text-sm' maxLength={30} placeholder='Nhập số chứng thực cá nhân' />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        label={<div className='text-xs'>Ngày cấp</div>}
                        className='text-sm'
                        name='identityCreatedDate'
                        labelCol={{ span: 8 }}
                        rules={[validateRequireInput('Ngày cấp không được bỏ trống')]}
                      >
                        <DatePicker
                          placeholder='dd/mm/yyyy'
                          format='DD/MM/YYYY'
                          className='w-full text-sm'
                          disabledDate={current => current && current > moment().subtract(1, 'days')}
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                </>
              )}
              <div className='mt-3 flex gap-6'>
                <Form.Item
                  label={<div className='text-xs'>Thành phố/ tỉnh</div>}
                  name='provinceId'
                  rules={[validateRequireInput('Tên đăng nhập không được bỏ trống')]}
                  className='mb-3 w-1/2 sm:mb-4'
                  required
                >
                  <Select
                    options={provinceList}
                    placeholder='Chọn thành phố/ tỉnh'
                    onChange={(value: { value: string; label: React.ReactNode }, option) => {
                      updateAddress('provinceId', value)
                      handleCreateAddress('provinceId', option)
                    }}
                    showSearch
                    searchValue={locationSearch.city}
                    onSearch={(value: string) => handleSearchLocation('city', value)}
                    filterOption={(input, option) => handleFilterLocation(locationSearch.city, option)}
                    notFoundContent='Không có dữ liệu'
                  />
                </Form.Item>
                <Form.Item
                  label={<div className='text-xs'>Phường/ xã</div>}
                  name='wardId'
                  rules={[validateRequireInput('Phường/ xã không được bỏ trống')]}
                  className='mb-3 w-1/2 sm:mb-4'
                  required
                >
                  <Select
                    options={wardList}
                    placeholder='Chọn phường/ xã'
                    onChange={(value: { value: string; label: React.ReactNode }, option) => {
                      updateAddress('wardId', value)
                      handleCreateAddress('wardId', option)
                    }}
                    showSearch
                    searchValue={locationSearch.ward}
                    onSearch={(value: string) => handleSearchLocation('ward', value)}
                    filterOption={(input, option) => handleFilterLocation(locationSearch.ward, option)}
                    notFoundContent='Không có dữ liệu'
                  />
                </Form.Item>
              </div>
              <div className='flex gap-6'>
                <Form.Item
                  label={<div className='text-xs'>Phố/ đường</div>}
                  name='streetId'
                  rules={[validateRequireInput('Phố/ đường không được bỏ trống')]}
                  className='mb-3 w-full sm:mb-4'
                  required
                >
                  <Select
                    options={streetList}
                    placeholder='Chọn Phố/ đường'
                    onChange={(_: any, option) => {
                      handleCreateAddress('streetId', option)
                    }}
                    showSearch
                    searchValue={locationSearch.street}
                    onSearch={(value: string) => handleSearchLocation('street', value)}
                    filterOption={(input, option) => handleFilterLocation(locationSearch.street, option)}
                    notFoundContent='Không có dữ liệu'
                  />
                </Form.Item>
              </div>

              <div className='w-full'>
                <Form.Item label={<div className='text-xs'>Số nhà</div>} name='homeNumber' className='mb-3 sm:mb-4'>
                  <Input
                    onChange={e => {
                      const value = e.target.value

                      handleCreateAddress('homeNumber', value)
                    }}
                    allowClear
                    maxLength={100}
                    placeholder='Nhập số nhà'
                  />
                </Form.Item>
              </div>

              <div className='w-full'>
                <Form.Item
                  label={<div className='text-xs'>Địa chỉ</div>}
                  name='address'
                  rules={[
                    validateRequireInput('Địa chỉ không được bỏ trống'),
                    validateMaxLengthStr(500, 'Nhập tối đa 500 ký tự')
                  ]}
                  className='mb-3 sm:mb-4'
                  required
                >
                  <Input allowClear maxLength={500} placeholder='Nhập địa chỉ' />
                </Form.Item>
              </div>

              <div className='w-full'>
                <span className='font-bold'>Lưu ý: </span>
                Vui lòng nhập đúng theo địa chỉ đăng ký kinh doanh
              </div>
            </div>
          </Form>
        </Modal>
      )}
    </>
  )
}

export default ModalInfoProfileCompany
