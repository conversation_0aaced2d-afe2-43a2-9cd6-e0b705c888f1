import { message } from 'antd'

import { cloneDeep, defaultsDeep, get, isNil, set } from 'lodash'

import { convertPhoneNumberTo0xx, convertPhoneNumberTo84x } from '@/constants/commonFunction'
import type { CardSimState } from '@/redux-store/slices/cardSimSlice'

import type {
  ApiFilterResponse,
  CalculateSimPriceProps,
  CouponMc,
  SimRegistrationOptions,
  GiaSim,
  KeyedObject,
  SimPayOptions,
  CalculatePrice,
  MobilePlansPayOptions
} from '@/types/PaymentType'

import { SIM_SERVICE_ID } from '../constant/PaymentConstant'
import { PARTNER_ACCOUNT_ID } from '@/models/Base'

//#region Constansts
enum statusEnum {
  Active = 1,
  Inactive = 0
}

enum pageTypeEnum {
  SIM = 'sim',
  SERVICE = 'service',
  BUNDLING = 'bundling'
}

/**
 * Danh sách các loại sản phẩm thanh toán
 * - SIM: Sản phẩm sim số
 * - MOBILE_PLAN: Sản phẩm gói cước di động
 * - SIM_WITH_PLAN: Sản phẩm sim số kèm gói cước
 */
export enum PaymentProductType {
  NONE = '',

  /** @deprecated Use SIM_WITH_PLAN instead */
  SIM = 'sim',
  MOBILE_PLAN = 'mobile-plan',
  SIM_WITH_PLAN = 'sim-with-plan'
}

export enum simCategoryEnum {
  SIM = 'sim-number',
  SUBSCRIPTION = 'subscription'
}

//1:nhan tai nha; 2:nhan tai diem gd
enum OrderType {
  Delivery = 1,
  Pickup = 2
}

export enum paymentMethodEnum {
  UNSET = 'UNSET',
  VNPTPAY = 'VNPTPAY',
  BY_CASH = 'BY_CASH',
  QR_CODE = 'VNPTPAY_QR',
  VNPTPAY_OR_CASH = 'VNPTPAY_OR_CASH'
}

// thời điểm thanh toán
export enum paymentTimeEnum {
  PREPAID = 'PREPAY',
  POSTPAID = 'POSTPAID'
}

// 2:sim vat ly; 1:esim
enum simTypeEnum {
  physical = 2,
  esim = 1
}

enum subscriptionTypeEnum {
  prepaid = 21,
  postpaid = 20
}

enum streetTypeEnum {
  Pho = 1,
  Ap = 2,
  Khu = 3
}

// ? Render theo layout dựng sẵn
// Danh mục: flex-1
// đơn giá: fixed 116px
// số lượng: fixed 78px
// thành tiền: fixed 97px
const layout = [
  { width: -1, title: 'Danh mục', key: 'category', contentClass: '', wrapperClass: '' },
  { width: 116, title: 'Đơn giá', key: 'price', contentClass: 'text-left sm:hidden', wrapperClass: 'sm:hidden' },
  { width: 78, title: 'Số lượng', key: 'quantity', contentClass: 'text-center sm:hidden', wrapperClass: 'sm:hidden' },
  { width: 97, title: 'Thành tiền', key: 'total', contentClass: 'text-right', wrapperClass: '' }
]

// 19/6/2025: Hiện tại chỉ mua được sim vật lý, xóa sim eSim
const simType: {
  enum: typeof simTypeEnum
  options: { label: string; value: number; code: number }[]
  paymentMethod?: { label: string; value: number; lstMethod: paymentMethodEnum[] }
  lstMethod?: Record<number, paymentMethodEnum[]>
  defaultValue: number
} = {
  enum: simTypeEnum,
  options: [
    { label: 'Sim vật lý', value: simTypeEnum.physical, code: 2 },
    { label: 'Sim eSIM', value: simTypeEnum.esim, code: 1 }
  ],
  lstMethod: {
    [simTypeEnum.physical]: [
      paymentMethodEnum.BY_CASH,
      paymentMethodEnum.VNPTPAY,
      paymentMethodEnum.QR_CODE
      // paymentMethodEnum.VNPTPAY_OR_CASH
    ],
    [simTypeEnum.esim]: [paymentMethodEnum.VNPTPAY, paymentMethodEnum.QR_CODE]
  },

  defaultValue: simTypeEnum.physical
}

const subscriptionType = {
  enum: subscriptionTypeEnum,
  options: [
    { label: 'Trả trước', value: subscriptionTypeEnum.prepaid },
    { label: 'Trả sau', value: subscriptionTypeEnum.postpaid }
  ],
  defaultValue: subscriptionTypeEnum.prepaid
}
//#endregion

//#region Calculator
const calculateGiaSim = (gia_sim: GiaSim, loai_sim: number) => {
  const priceType = {
    [simType.enum.physical]: gia_sim.gia_sim_vat_ly,
    [simType.enum.esim]: gia_sim.gia_e_sim
  }

  return priceType[loai_sim as keyof typeof priceType] || 0
}

/**
 * Tổng hợp giá sim
 * @param gia_sim
 * Loại thuê bao: trả trước, trả sau
 * @param loai_thue_bao
 * Loại sim: sim vật lý, sim eSIM
 * @param loai_sim
 */
const calculateSimPrice = ({
  gia_sim,
  loai_thue_bao,
  loai_sim, // @see simTypeEnum
  orderType, // Loại giao hàng
  quantity
}: CalculateSimPriceProps): CalculatePrice => {
  let price = 0

  const deliveryFeeMapper = {
    [OrderType.Delivery]: gia_sim.phi_giao_hang_tai_nha,
    [OrderType.Pickup]: gia_sim.phi_giao_hang_tai_quay
  }

  // Nếu là eSIM thì không có phí giao hàng
  const phi_giao_hang =
    loai_sim === simType.enum.esim ? 0 : deliveryFeeMapper[orderType as keyof typeof deliveryFeeMapper] || 0

  const connectionFeeMapper = {
    [subscriptionType.enum.prepaid]: gia_sim.phi_hoa_mang_tra_truoc,
    [subscriptionType.enum.postpaid]: gia_sim.phi_hoa_mang_tra_sau
  }

  const phi_hoa_mang = connectionFeeMapper[loai_thue_bao as keyof typeof connectionFeeMapper] || 0

  const tien_sim = calculateGiaSim(gia_sim, loai_sim)

  price = phi_hoa_mang + tien_sim

  const quantityNumber = typeof quantity === 'string' ? parseInt(quantity, 10) : quantity || 1

  return {
    price,
    tien_sim,
    phi_hoa_mang,
    fee: {
      phi_giao_hang,
      total: phi_giao_hang
    },
    total: price * quantityNumber
  }
}

const calculateMobilePlanPrice = (pricing: any): CalculatePrice => {
  return {
    price: pricing?.price || 0,
    total: pricing?.price || 0,
    tien_sim: 0, // Không có giá sim trong gói di động
    phi_hoa_mang: 0, // Không có phí hòa mạng trong gói di động
    fee: {
      phi_giao_hang: 0,
      total: 0
    }
  }
}

const calculateOrderSummary = (SimProduct: any, shippingFee?: number, lstAddition?: any) => {
  const totalAmount = SimProduct?.simItem?.total + SimProduct?.pricing?.total

  // boolean to int (status)
  const totalProduct = Number(SimProduct?.simItem?.visible) + Number(SimProduct?.pricing?.visible)
  const totalFee = 0

  // Update 23/07/2025: Chỉ hiển thị thông báo tiền giao hàng, không tính vào tổng tiền
  const OrderSummaryNew = {
    showSessionTimeout: true, // Hiển thị tính năng timeout cho thanh toán sim
    initialMinutes: 10,
    initialUnit: 60,
    totalProductQuantity: totalProduct,
    totalProductPrice: totalAmount,
    shippingFee: shippingFee || 0,
    totalPrice: totalAmount + totalFee,
    interfaceProps: {
      priceClass: 'text-text-primary-strong',
      showNoteShippingFee: true // Hiển thị thông báo tiền giao hàng
    },
    ...lstAddition
  }

  return OrderSummaryNew
}
//#endregion

// #region Business Logic
/**
 * Chuyển đổi dữ liệu thanh toán
 * @method getInitialValues Lấy dữ liệu ban đầu cho từng loại sản phẩm thanh toán
 * @method convertToPayload Chuyển đổi dữ liệu từ form sang payload để gửi API
 * @param productType Loại sản phẩm thanh toán: SIM, MOBILE_PLAN, SIM_WITH_PLAN
 */
export class PaymentConverter {
  static getInitialValues(productType: PaymentProductType, input: Partial<CardSimState>, res?: ApiFilterResponse) {
    switch (productType) {
      case PaymentProductType.MOBILE_PLAN:
        return this.getMobilePlanInitialValues(input)
      case PaymentProductType.SIM:
      case PaymentProductType.SIM_WITH_PLAN:
        return this.getSimInitialValues(res, input)
      default:
        return undefined
    }
  }

  private static validateSimPayment = (values: Partial<CardSimState>) => {
    if (values.productType === PaymentProductType.MOBILE_PLAN) {
      return true // Không phải thanh toán SIM, không cần validate
    }

    const lstPathRequired = ['codeBookSim', 'simNumber', 'simItem.msisdn']

    if (lstPathRequired.some(path => isNil(get(values, path)))) {
      message.error('Vui lòng nhập đầy đủ thông tin')

      return false
    }

    return true
  }

  static validate84XPhoneNumber = (phoneNumber: string) => {
    if (!phoneNumber || !/^84\d{9,10}$/.test(phoneNumber)) {
      return false
    }

    return true
  }

  private static getSimInitialValues = (res: ApiFilterResponse | undefined, input: Partial<CardSimState>) => {
    const productType = input.productType || PaymentProductType.SIM_WITH_PLAN
    const validate = this.validateSimPayment(input)

    if (!validate || productType === PaymentProductType.MOBILE_PLAN) return

    const priceInfo: CalculatePrice = calculateSimPrice({
      gia_sim: res?.item?.gia_sim as GiaSim,
      loai_thue_bao: subscriptionType.defaultValue,
      loai_sim: simType.defaultValue,
      orderType: OrderType.Delivery,
      quantity: 1
    })

    const simInput = Object.assign({}, res?.item, input)

    const simProduct = {
      simItem: simInput?.simItem
        ? {
            visible: simInput?.productType === PaymentProductType.MOBILE_PLAN ? statusEnum.Inactive : statusEnum.Active, // Mặc định ẩn sim nếu là màn thanh toán gói di động
            category: {
              id: simInput?.simItem.id,
              type: simCategoryEnum.SIM, // optional
              value: convertPhoneNumberTo0xx(simInput?.simItem.msisdn),
              simType: simType.defaultValue,
              subscriptionType: simInput.simItem.subscriptionType || subscriptionType.defaultValue,
              tien_sim: priceInfo.tien_sim,
              phi_hoa_mang: priceInfo.phi_hoa_mang
            },
            price: priceInfo.price,
            quantity: 1,
            total: priceInfo.total
          }
        : { visible: statusEnum.Inactive, category: {}, price: 0, quantity: 0, total: 0 },
      pricing:
        simInput.pricing && Object.keys(simInput.pricing).length > 0
          ? {
              visible: statusEnum.Active,
              category: {
                id: simInput?.pricing.migrationId,
                type: simCategoryEnum.SUBSCRIPTION, // optional
                value: simInput?.pricing?.planName || simInput?.pricing?.pricingName
              },
              price: simInput?.pricing.price,
              quantity: 1,
              total: simInput?.pricing.price
            }
          : { visible: statusEnum.Inactive, category: {}, price: 0, quantity: 0, total: 0 },
      total: 0 // set later
    }

    const initialData = {
      simInput,
      orderType: 1,
      SimProduct: simProduct,
      // OrderSummary: {} // set later
      SimPersonalInfo: {
        phoneNumber: simInput.simNumber,
        referCode: simInput.referCode
      },
      OrderAddress: {
        hidden: false,
        provinceId: undefined
      },
      PaymentMethod: {
        value: paymentMethodEnum.VNPTPAY,
        description: '',
        lstMethod: simType.lstMethod?.[simType.defaultValue] || []
      }
    }

    if (initialData?.SimProduct && initialData?.SimProduct?.simItem.visible === statusEnum.Active) {
      // const OrderSummaryNew = calculateOrderSummary(initialData?.SimProduct, fee.phi_giao_hang, {
      //   phi_hoa_mang,
      //   gia_sim: tien_sim
      // })
      const OrderSummaryNew = calculateOrderSummary(initialData?.SimProduct, priceInfo?.fee.phi_giao_hang)

      set(initialData, 'SimProduct.total', OrderSummaryNew.totalProductPrice)

      set(initialData, 'OrderSummary', OrderSummaryNew)
    }

    return initialData
  }

  private static getMobilePlanInitialValues = (input: Partial<CardSimState>) => {
    if (!input.pricing) return

    const priceInfo: CalculatePrice = calculateMobilePlanPrice(input.pricing)

    // giá trị cho màn SimProduct
    const simProduct = {
      simItem: {
        visible: statusEnum.Inactive, // Mặc định ẩn sim nếu là màn thanh toán gói di động
        category: {
          id: input?.simItem?.id,
          type: simCategoryEnum.SIM, // optional
          value: undefined,
          simType: simType.defaultValue,
          subscriptionType: subscriptionType.defaultValue,
          tien_sim: priceInfo?.tien_sim,
          phi_hoa_mang: priceInfo?.phi_hoa_mang
        },
        price: 0,
        quantity: 1,
        total: 0
      },
      pricing: {
        visible: statusEnum.Active,
        category: {
          id: input.pricing.migrationId,
          type: simCategoryEnum.SUBSCRIPTION, // optional
          value: input.pricing.pricingName || input.pricing.planName
        },
        price: priceInfo.price,
        quantity: 1,
        total: priceInfo.total
      },
      total: 0 // set later
    }

    const initialData = {
      simInput: input,
      orderType: OrderType.Delivery,
      SimProduct: simProduct,
      OrderSummary: calculateOrderSummary(simProduct, 0),
      SimPersonalInfo: {
        phoneNumber: input.simNumber,
        referCode: input.referCode
      },
      PaymentMethod: {
        value: paymentMethodEnum.VNPTPAY,
        description: '',
        lstMethod: [paymentMethodEnum.VNPTPAY, paymentMethodEnum.QR_CODE]
      }
    }

    return initialData
  }

  static convertToPayload(productType: PaymentProductType, values: any): { body: any; options: any } {
    switch (productType) {
      case PaymentProductType.MOBILE_PLAN:
        return this.handleConvertMobilePlanPayload(values)
      case PaymentProductType.SIM:
        return this.handleConvertSubmitSim(values)
      case PaymentProductType.SIM_WITH_PLAN:
        return this.handleConvertSimRegistration(values)
      default:
        return {
          body: undefined,
          options: undefined
        }
    }
  }

  /**
   * Submit data to API
   * @description Mandatory/Optional (M/O)
   * @param payload
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  private static handleConvertSubmitSim = (payload: any) => {
    const sim = payload.SimProduct.simItem

    const connectionFeeMapper = {
      [subscriptionType.enum.prepaid]: payload?.simInput?.gia_sim?.phi_hoa_mang_tra_truoc,
      [subscriptionType.enum.postpaid]: payload?.simInput?.gia_sim?.phi_hoa_mang_tra_sau
    }

    const phi_hoa_mang = connectionFeeMapper[sim?.category?.subscriptionType as keyof typeof connectionFeeMapper] || 0

    const options: SimPayOptions = {
      // Base fields mapping
      // cmt: payload.SimPersonalInfo?.idNumber, // ! (string O) Số chứng minh thư
      cuoc_camket: parserInt10(payload.simInput?.simItem?.cuoc_camket), // (int O) Cước cam kết
      diachi: payload.OrderAddress?.address, // ? (string O) Địa chỉ giao hàng
      fullname: payload.SimPersonalInfo?.fullName, // (string M) Tên người nhận
      so_dt: convertPhoneNumberTo84x(payload.simInput?.simNumber), // * (string M) Số điện thoại người nhận 84xx
      ghichu: payload.OrderAddress?.note || '', // ! (string O) Ghi chú
      gia_sim: calculateGiaSim(payload.simInput?.gia_sim, sim.category?.simType), // (float M) Giá sim
      phi_hoa_mang, // (float M) Phí hoa mạng
      phi_giao_hang: payload.OrderSummary?.shippingFee, // ! (float M) Chọn giao hàng hoặc đến cửa hàng ?? 0

      // SIM type & subscription mapping
      loai_sim: sim?.category?.simType, // (int M) Loại sim - 2: sim vat ly, 1: sim eSIM
      Loaitb_id: sim?.category?.subscriptionType, // (int M) Loại thuê bao - 21: trả trước, 20: trả sau
      thoi_gian_cam_ket: payload.simInput?.simItem?.thoigian_camket, // (int O) Thời gian cam kết

      // Location IDs mapping
      tinh_id: parserInt10(payload.OrderAddress?.provinceId), // (string M) ID tỉnh - id quận thì là int, tỉnh là string ? lú
      phuong_id: parserInt10(payload.OrderAddress?.wardId), // (int O) ID phường
      // pho_it_id: parserInt10(payload.OrderAddress?.street.itId),
      pho_it_id:
        payload.OrderAddress?.street.type === streetTypeEnum.Pho
          ? parserInt10(payload.OrderAddress?.street.itId)
          : undefined, // (int M) ID phố IT
      ap_it_id:
        payload.OrderAddress?.street.type === streetTypeEnum.Ap
          ? parserInt10(payload.OrderAddress?.street.itId)
          : undefined, // (int O) ID ấp IT
      khu_it_id:
        payload.OrderAddress?.street.type === streetTypeEnum.Khu
          ? parserInt10(payload.OrderAddress?.street.itId)
          : undefined, // (int O) ID khu IT
      /* @deprecated */
      // quan_id: parserInt10(payload.OrderAddress?.districtId), // (int M) ID quận
      // quan_it_id: parserInt10(payload.OrderAddress?.district.it_id), // (int M) ID quận IT
      // phuong_it_id: parserInt10(payload.OrderAddress?.ward.it_id), // (int M) ID phường IT

      // Transaction points mapping
      diem_gd_id: payload.orderType === OrderType.Pickup ? parserInt10(payload.OrderAddress?.branchId) : undefined, // ! (int O) ID điểm giao dịch
      phonggd: payload.orderType === OrderType.Pickup ? parserInt10(payload.OrderAddress?.branchId) : undefined, // (int O) Phòng giao dịch
      empowered_point_id: undefined, // ! (int O) id điểm ủy quyền
      empowered_point_name: undefined, // ! (string O) Tên điểm ủy quyền

      // Bundle info mapping
      product_id: payload?.simInput?.pricing?.migrationId, // (int O) ID gói cước
      gia_goi: payload.simInput?.pricing?.price || 0, // (float M) Giá gói cước

      // Delivery & booking info mapping
      ht_nhansim: payload.orderType, // (int M) 1: nhận tại nhà; 2: nhận tại điểm giao dịch
      ma_giu_so: payload.simInput?.codeBookSim, // (string M) Mã giữ số
      ma_kit: '', // ! (string M) lấy từ filter chọn 1 gói gọi sang getList lấy value của ext_property name =”Mã gói cước”
      ma_tb: convertPhoneNumberTo84x(payload.simInput?.simItem?.msisdn), // * (string M) Số điện thoại của sim 84xxx
      // pay_order: paymentPaidMapper[payload.PaymentMethod?.value as paymentMethodEnum], // ! (int M) Thông tin đơn hàng đã được thanh toán chưa? 0 – chưa thanh toán 1– đã thanh toán
      pay_order: 0,
      partner_accountID: PARTNER_ACCOUNT_ID // (string M) ID tài khoản đối tác
    }

    const body = {
      ACTION: 'RELIEF',
      SERVICE_ID: SIM_SERVICE_ID,
      PAYMENT_CODE: convertPhoneNumberTo84x(payload.simInput.simNumber), //payload.simInput.simItem.msisdn
      ADDITIONAL_INFO: 'ADDITIONAL_INFO', // ? Thông tin bổ sung
      // BILL_AMOUNT: payload.OrderSummary.totalPrice.toString(),
      BILL_AMOUNT: '0',
      // BILL_FEE: payload.OrderSummary.shippingFee.toString(),
      BILL_FEE: '0',
      BILL_DETAIL: JSON.stringify([
        {
          // AMOUNT: payload.SimProduct?.total.toString(),
          AMOUNT: '0',
          INFO: payload.simInput?.codeBookSim,
          MONTH: ''
        }
      ]),
      REFER_CODE: payload.SimPersonalInfo?.referCode,
      OPTIONS: JSON.stringify(options)
    }

    return {
      body,
      options
    }
  }

  /**
   * Convert form data to API payload for SIM registration (esim or physical sim) - New Sim Payment API
   * @description Mandatory/Optional (M/O)
   * @param payload Form data from user input
   * @returns Object containing body and options for API call
   */
  private static handleConvertSimRegistration = (payload: any) => {
    const sim = payload.SimProduct.simItem

    const connectionFeeMapper = {
      [subscriptionType.enum.prepaid]: payload?.simInput?.gia_sim?.phi_hoa_mang_tra_truoc,
      [subscriptionType.enum.postpaid]: payload?.simInput?.gia_sim?.phi_hoa_mang_tra_sau
    }

    const phi_hoa_mang = connectionFeeMapper[sim?.category?.subscriptionType as keyof typeof connectionFeeMapper] || 0

    const options: SimRegistrationOptions = {
      // Base fields mapping
      // identityNumber: payload.SimPersonalInfo?.identityNumber, // ! (string O) Số chứng minh thư
      // identityNumber: payload.SimPersonalInfo?.identityNumber || Math.random().toString().slice(2, 14), // (string O) Số chứng minh thư - random 12 digits if not provided TODO: remove this line after testing
      commitmentFee: parserInt10(payload.simInput?.simItem?.cuoc_camket), // (int O) Cước cam kết
      address: payload.OrderAddress?.address, // ? (string O) Địa chỉ giao hàng
      fullName: payload.SimPersonalInfo?.fullName, // (string M) Tên người nhận
      phoneNumber: convertPhoneNumberTo84x(payload.simInput?.simNumber), // * (string M) Số điện thoại người nhận 84xx
      note: payload.OrderAddress?.note || '', // ! (string O) Ghi chú
      simPrice: calculateGiaSim(payload.simInput?.gia_sim, sim.category?.simType), // (float M) Giá sim
      activationFee: phi_hoa_mang, // (float M) Phí hoa mạng
      deliveryFee: payload.OrderSummary?.shippingFee, // ! (float M) Phí giao hàng

      // SIM type & subscription mapping
      simType: sim?.category?.simType, // (int M) Loại sim - 2: sim vat ly, 1: sim eSIM
      subscriberType: sim?.category?.subscriptionType, // (int M) Loại thuê bao - 21: trả trước, 20: trả sau
      commitmentPeriod: payload.simInput?.simItem?.thoigian_camket, // (int O) Thời gian cam kết

      // Location IDs mapping
      provinceId: parserInt10(payload.OrderAddress?.provinceId), // (string M) ID tỉnh
      // wardId: parserInt(payload.OrderAddress?.wardId), // (int O) ID phường
      wardItId: parserInt(payload.OrderAddress?.wardId), // (int O) ID phường IT
      // wardItId: parserInt(payload.OrderAddress?.ward?.itId), // (int O) ID phường IT
      streetItId: parserInt(payload.OrderAddress?.street?.itId), // (int O) ID phố IT
      /* @deprecated */
      // streetItId:
      //   payload.OrderAddress?.street?.type === streetTypeEnum.Pho
      //     ? parserInt10(payload.OrderAddress?.street?.itId)
      //     : undefined, // (int O) ID phố IT
      // villageItId:
      //   payload.OrderAddress?.street?.type === streetTypeEnum.Ap
      //     ? parserInt10(payload.OrderAddress?.street?.itId)
      //     : undefined, // (int O) ID ấp IT
      // zoneItId:
      //   payload.OrderAddress?.street?.type === streetTypeEnum.Khu
      //     ? parserInt10(payload.OrderAddress?.street?.itId)
      //     : undefined, // (int O) ID khu IT
      // districtItId: parserInt10(payload.OrderAddress?.district?.it_id), // (int O) ID quận IT
      // districtId: parserInt10(payload.OrderAddress?.districtId), // (int O) ID quận

      // Transaction points mapping
      transactionPointId:
        payload.orderType === OrderType.Pickup ? parserInt(payload.OrderAddress?.branchId) : undefined, // ! (int O) ID điểm giao dịch
      transactionOfficeId:
        payload.orderType === OrderType.Pickup ? parserInt(payload.OrderAddress?.branchId) : undefined, // (int O) Phòng giao dịch
      empoweredPointId: undefined, // ! (int O) id điểm ủy quyền
      empoweredPointName: undefined, // ! (string O) Tên điểm ủy quyền

      // Bundle info mapping
      bundlePrice: payload.simInput?.pricing?.price || 0, // (float M) Giá gói cước
      bundleId: payload?.simInput?.pricing?.migrationId, // (int O) ID gói cước

      // Delivery & booking info mapping
      deliveryMethod: payload.orderType, // (int O) 1: nhận tại nhà; 2: nhận tại điểm giao dịch
      reservationCode: payload.simInput?.codeBookSim, // (string M) Mã giữ số
      kitCode: '', // ! (string M) lấy từ filter chọn 1 gói gọi sang getList lấy value của ext_property name ="Mã gói cước"
      bookingNumber: convertPhoneNumberTo84x(payload.simInput?.simItem?.msisdn), // * (string M) Số điện thoại của sim 84xxx
      paymentStatus: 0, // ! (int M) Thông tin đơn hàng đã được thanh toán chưa? 0 – chưa thanh toán 1– đã thanh toán
      // partner_accountID: PARTNER_ACCOUNT_ID, // (string M) ID tài khoản đối tác

      // eSIM specific fields
      paymentMethod: payload.PaymentMethod?.value, // (string M) Phương thức thanh toán
      ipAddress: payload.ipAddress, // (string M) IP của khách hàng
      email: payload.SimPersonalInfo?.email // (string O) Email của khách hàng
    }

    const body = {
      ACTION: 'RELIEF',
      SERVICE_ID: SIM_SERVICE_ID,
      PAYMENT_CODE: convertPhoneNumberTo84x(payload.simInput?.simItem?.msisdn), // Số thuê bao
      ADDITIONAL_INFO: 'ADDITIONAL_INFO',
      BILL_AMOUNT: payload.OrderSummary?.totalPrice.toString(), // Tổng tiền
      BILL_FEE: payload.OrderSummary?.shippingFee.toString(), // Phí giao hàng
      BILL_DETAIL: JSON.stringify([
        {
          AMOUNT: payload.SimProduct?.total.toString(),
          INFO: payload.simInput?.codeBookSim,
          MONTH: ''
        }
      ]),
      REFER_CODE: payload.SimPersonalInfo?.referCode,
      OPTIONS: JSON.stringify(options)
    }

    return {
      body,
      options
    }
  }

  // Hàm convert này được sử dụng trong trường hợp thanh toán gói cước di động qua tài khoản ngân hàng (paymentMethod = 1, otp là không bắt buộc)
  private static handleConvertMobilePlanPayload = (
    payload: any
  ): {
    body: any
    options: MobilePlansPayOptions
  } => {
    return {
      body: {},
      options: {
        ipAddress: payload.ipAddress, // (string M) IP của khách hàng
        planId: payload?.simInput?.pricing?.migrationId, // (int M) ID gói cước
        paymentMethod: 1, // (int M) 0: Thanh toán qua tài khoản chính, 1: Thanh toán qua tài khoản bank
        phoneNumber: convertPhoneNumberTo84x(payload.SimPersonalInfo?.phoneNumber), // (string M) Số điện thoại khách hàng
        paymentType: payload.PaymentMethod?.value, // (string M) Loại thanh toán - PREPAY hoặc POSTPAY
        planPrice: payload?.simInput?.pricing?.price || 0 // (float M) Giá gói cước
        // otp: payload.otp // (string O) Mã OTP nếu có
      }
    }
  }
}
// #endregion

//#region Functions
const parserInt10 = (value: string) => {
  const parsedValue = parseInt(value, 10)

  return isNaN(parsedValue) ? 0 : parsedValue
}

const parserInt = (value: string) => {
  const parsedValue = parseInt(value, 10)

  return isNaN(parsedValue) ? undefined : parsedValue
}

const formUtils = (form: any, path?: string) => {
  if (!path) path = '' // ? Default path
  const arrayPath = path.split('.')

  return {
    getNamePath: (key: string | string[]) => {
      return arrayPath.concat(key)
    },
    setPathFieldsValue: (value: any) => {
      if (path || path !== '') {
        form.setFieldValue(path, value)
      }
    },
    getPathFieldsValue: (isGetAll: boolean) => {
      return form.getFieldsValue(isGetAll ? undefined : path)
    },
    getPathFieldValue: (key: string) => {
      return form.getFieldValue(arrayPath.concat(key))
    }
  }
}

const onFillAddress = (form: any, subPath?: string) => {
  if (!form) return
  if (!subPath) subPath = 'OrderAddress' // ? Default path
  const OrderAddress = form.getFieldValue(subPath)

  if (!OrderAddress) return

  const { province, district, ward, street } = OrderAddress
  const addressParts = [street?.label, ward?.label, district?.label, province?.label]
  const address = addressParts.filter(Boolean).join(', ')

  form.setFieldValue(['OrderAddress', 'address'], address)
  form.setFields([
    {
      name: ['OrderAddress', 'address'],
      errors: []
    }
  ])
}

const onSimTypeChange = (value: number, form: any) => {
  let paymentValue, lstMethod, hidden

  if (value === simTypeEnum.physical) {
    paymentValue = paymentMethodEnum.BY_CASH
    lstMethod = simType.lstMethod?.[simTypeEnum.physical] || []
    hidden = false
  } else if (value === simTypeEnum.esim) {
    paymentValue = paymentMethodEnum.VNPTPAY
    lstMethod = simType.lstMethod?.[simTypeEnum.esim] || []
    hidden = true
  } else {
    return
  }

  // Gộp các setFieldValue thành một setFieldsValue
  form.setFieldsValue({
    PaymentMethod: {
      value: paymentValue,
      description: '',
      lstMethod
    },
    OrderAddress: {
      hidden
    }
  })
}

const onChangeSimForm: ((changedValues: KeyedObject, values: any, form?: any) => void) | undefined = (
  changedValues,
  values,
  form
) => {
  const keysCheck = ['SimProduct', 'orderType']

  const isChanged = keysCheck.some(key => Object.keys(changedValues).includes(key))

  if (isChanged) {
    const fieldsValue = form.getFieldsValue(true)
    const simProduct = defaultsDeep(changedValues?.SimProduct, fieldsValue?.SimProduct) // Equal values when with hidden fields
    const loai_thue_bao = simProduct?.simItem?.category?.subscriptionType
    const loai_sim = simProduct?.simItem?.category?.simType as simTypeEnum

    const { price, total, fee, tien_sim, phi_hoa_mang } = calculateSimPrice({
      gia_sim: fieldsValue.simInput?.gia_sim as GiaSim,
      loai_thue_bao,
      loai_sim,
      orderType: fieldsValue.orderType,
      quantity: 1
    })

    const newSimItem = {
      visible: simProduct.simItem?.visible,
      category: {
        id: simProduct.simItem?.category?.id,
        type: simCategoryEnum.SIM,
        value: simProduct?.simItem?.category?.value,
        simType: loai_sim,
        subscriptionType: loai_thue_bao,
        tien_sim,
        phi_hoa_mang
      },
      price: price,
      quantity: simProduct?.simItem?.quantity,
      total: total
    }

    set(simProduct, 'simItem', newSimItem)

    onSimTypeChange(loai_sim, form)

    const newOrderSummary = calculateOrderSummary(simProduct, fee.phi_giao_hang)

    form.setFieldValue('SimProduct', {
      ...simProduct,
      total: newOrderSummary.totalProductPrice
    })
    form.setFieldValue('OrderSummary', newOrderSummary)
  }
}
//#endregion

export function getDataToCalculateCombo(state: any) {
  const { addons } = state
  const getCouponIds = (coupons: any) => coupons?.map((el: any) => el.id)

  const getMcCoupon = (coupons: CouponMc[]) => {
    const result: { mcId: number; activityIdx: number }[] = []

    coupons?.forEach((coupon: CouponMc) => {
      if (coupon.promotionPopupType === 'MC') result.push({ mcId: coupon.mcId, activityIdx: coupon.activityIdx })
    })

    return result
  }

  return {
    object: {
      id: state.combo.comboPlan.id,
      quantity: state.combo.quantity,
      price: state.combo.comboPlan.price,
      couponIds: getCouponIds(state.combo.comboPlan.couponList),
      lstMcPrivate: getMcCoupon(state.combo.comboPlan.couponList)
    },
    addons: addons
      .filter((el: any) => el.quantity)
      .map((addon: any) => ({
        id: addon.id,
        quantity: addon.quantity,
        couponIds: getCouponIds(addon.couponList),
        lstMcAddon: getMcCoupon(addon?.couponList),
        addonMultiPlanId: addon.addonMultiPlanId,
        price: addon.price,
        unitLimitedList: addon.unitLimitedList
      })),
    coupons: getCouponIds(state.coupons),
    lstMcInvoice: getMcCoupon(state.coupons)
  }
}

//#region Export
export const constansts = {
  layout,
  pageTypeEnum,
  simType,
  subscriptionType,
  simCategoryEnum,
  statusEnum,
  OrderType
}

export const functionsPayment = {
  formUtils,
  onFillAddress,
  onChangeSimForm,
  onSimTypeChange
}

export const calculatorPayment = {
  calculateSimPrice,
  calculateOrderSummary
}
//#endregion

//#region Utils sửa dụng cho SHOPPING CART

// Trạng thái hoạt động sản phẩm
export const ACTIVITY_STATUS = {
  DISABLE: 0,
  ENABLE: 1
}

export const DELETED_FLAG_STATUS = {
  DISABLE: 0,
  ENABLE: 1
}

/** Hàm lọc các sản phẩm được chọn từ api giỏ hàng */
export const filterSelectedProduct = (data: any) => {
  // Lọc các sản phẩm được chọn
  const productFilter = data?.filter(
    (product: any) =>
      product?.status === ACTIVITY_STATUS.ENABLE &&
      (product?.allowMultiSub || product?.numSub === 0) &&
      product?.deletedFlag === DELETED_FLAG_STATUS.ENABLE &&
      product.selected
  )

  productFilter.forEach((product: any) => {
    // Lọc các pricing được chọn
    if (product?.lstPricing?.length > 0) {
      product.lstPricing =
        product?.lstPricing?.filter(
          (pricing: any) =>
            pricing?.selected &&
            pricing?.status === ACTIVITY_STATUS.ENABLE &&
            pricing?.deletedFlag === DELETED_FLAG_STATUS.ENABLE
        ) || []

      product?.lstPricing?.forEach((pricing: any) => {
        // Lọc các addon được chọn
        if (pricing?.lstAddon?.length > 0) {
          pricing.lstAddon = pricing?.lstAddon?.filter((addon: any) => addon?.selected && addon?.status === 1) || []
        }
      })
    }
  })

  return productFilter
}

/** Hàm sử dụng để convert data (copy lại luồng cũ)
 * Input: Đầu vào từ API chi tiết giỏ hàng
 * Output: body cho API Calculate-Cart
 * */

export const bodyCartConvert = (data: any) => {
  // Biến lưu dto các sản phẩm
  let lstSubscriptionFilter: any[] = []

  // Convert thông tin tính toán
  data.forEach((product: any) => {
    // DTO tính toán trường hợp sản phẩm không có gói
    if (product?.lstPricing?.length === 0) {
      lstSubscriptionFilter = lstSubscriptionFilter?.concat({
        idx: product?.idx,
        parentIdx: null,
        subscriptionId: null,
        serviceId: product?.serviceId,
        variantDraftId: product?.variantDraftId,
        isBuyOnlyService: true,
        serviceDraftId: product?.serviceDraftId,
        planType: 'PREPAY',
        addons: [],
        otherFeeList: [],
        productType: product?.productType,
        serviceType: product?.serviceType,
        serviceOwner: product?.type,
        paymentMethod: product?.paymentMethod
      })
    } else {
      // DTO tính toán trường hợp sản phẩm có gói
      // Map dữ liệu tính toán theo BE DTO
      lstSubscriptionFilter = lstSubscriptionFilter?.concat(
        product?.lstPricing?.map((pricing: any) => ({
          idx: pricing?.idx,
          parentIdx: pricing?.parentIdx,
          subscriptionId: null,
          calculateType: product.isService ? 'PRICING' : 'COMBO',
          planType: 'PREPAY',
          variantDraftId: product?.variantDraftId,
          serviceDraftId: product.serviceDraftId,
          serviceId: product?.serviceId,
          productType: product?.productType,
          isBuyOnlyService: false,
          serviceType: product?.serviceType,
          serviceOwner: product?.type,
          paymentMethod: product?.paymentMethod,
          addons:
            pricing.lstAddon?.map((addon: any) => ({
              idx: addon?.idx,
              parentIdx: addon?.parentIdx,
              id: addon.addonId,
              quantity: addon.quantity,
              setupFee: addon.setupFee || null,
              couponIds: addon?.couponList?.map((coupon: any) => coupon.id) || [],
              addonMultiPlanId: addon.addonMultiPlanId,
              amount: 0,
              lstMcAddon:
                addon?.lstMcAddon?.map((mc: any) => ({
                  mcId: mc.mcId,
                  activityIdx: mc.activityIdx
                })) || []
            })) || [],
          otherFeeList: [],
          object: {
            idx: pricing?.idx,
            parentIdx: pricing?.parentIdx,
            id: pricing.pricingId,
            setupFee: pricing.setupFee || null,
            price: pricing.preTaxAmount || null,
            multiPlanId: pricing.pricingMultiPlanId,
            quantity: pricing.quantity,
            couponIds:
              pricing?.couponList?.map((coupon: any) => (!coupon.couponSetId ? coupon.id : coupon.couponId)) || [],
            hasTax: 'NO',
            amount: 0,
            lstMcPrivate:
              pricing?.lstMcPrivate?.map((mc: any) => ({
                mcId: mc.mcId,
                activityIdx: mc.activityIdx
              })) || []
          },
          lastPromotion: pricing.lastPromotion
        })) || []
      )
    }
  })

  // Lọc các thông tin không có giá trị (null, undefined)
  const lstSubscription = lstSubscriptionFilter?.filter(element => Object.keys(element).length !== 0)

  lstSubscription.forEach(sub => {
    sub.addons = sub?.addons?.filter((element: any) => Object.keys(element).length !== 0)
  })

  // Body cho api tính toán
  return {
    lstSubscription,

    // Hiện chưa làm khuyến mại tổng
    lstMcInvoice: [],
    couponList: [],
    lastPromotion: {}
  }
}

/** Hàm tạo key (gồm index và id) */
function getDictKey(id: any, idx: any) {
  const idxStr = 'dict_'.concat(id)

  return idxStr.concat('_', idx)
}

/** Hàm tạo từ điển chung cho promotion */
function processPromotionDict(promotionCalList: any, idx: any) {
  const promotionDict: any = {}

  promotionCalList?.forEach((coupon: any) => {
    const key = getDictKey(coupon?.id, idx)

    promotionDict[key] = coupon
  })

  return promotionDict
}

/** Hàm sử dụng merge dữ liệu detail cart và dữ liệu calculate
 * Sử dụng từ điển để lưu trữ thông tin calculate
 * Loop Cart Detail để map dữ liệu
 * */
export const mergeCartInfo = (cartDetail: any, res: any) => {
  // Clone lại res
  const calculateProduct = cloneDeep(res)

  const cartDetailCopy = cloneDeep(cartDetail)

  // Khai báo từ điển cho sản phẩm và addon
  const productDict: any = {}
  const addOnDict: any = {}

  // Dict cho khuyến mại gói chính (API calculate cart trả về riêng các loại khuyến mại)
  const pricingPercentDict: any = {}
  const pricingPricesDict: any = {}
  const pricingMcPrivatePriceDict: any = {}
  const pricingMcPrivatePercentDict: any = {}

  // Dict cho khuyến mại addon  (API calculate cart trả về riêng các loại khuyến mại)
  let addonPercentDict: any = {}
  let addonPricesDict: any = {}
  let addonLstMcPrivatePercentDict: any = {}
  let addonLstMcPrivatePriceDict: any = {}

  calculateProduct?.lstSubscriptionResponse?.forEach((product: any) => {
    // Khai báo key cho sản phẩm (key = idx, tương ứng với idx của pricing trong cartDetail)
    const productKey = product?.idx

    // Lưu từ điển KM cho pricing, addon (KM thường và theo chương trình KM)
    // KM thường và chương trình KM đều có dạng KM theo % hoặc theo số tiền, DTO trả về riêng biệt từng loại
    //

    const pricingPromotionListUsed: any = []

    // Lưu từ điển cho pricing
    if (product?.object?.couponPercent?.length > 0) {
      product?.object?.couponPercent?.forEach((coupon: any) => {
        const key = getDictKey(coupon?.id, product?.idx)

        pricingPromotionListUsed.push(coupon)
        pricingPercentDict[key] = coupon
      })
    }

    if (product?.object?.couponPrices?.length > 0) {
      product?.object?.couponPrices?.forEach((coupon: any) => {
        const key = getDictKey(coupon?.id, product?.idx)

        pricingPromotionListUsed.push(coupon)
        pricingPricesDict[key] = coupon
      })
    }

    if (product?.object?.lstMcPrivatePrice?.length) {
      product?.object?.lstMcPrivatePrice?.forEach((coupon: any) => {
        const key = getDictKey(coupon?.id, product?.idx)

        coupon.mcId = coupon?.id
        pricingPromotionListUsed.push(coupon)
        pricingMcPrivatePriceDict[key] = coupon
      })
    }

    if (product?.object?.lstMcPrivatePercent?.length) {
      product?.object?.lstMcPrivatePercent?.forEach((coupon: any) => {
        const key = getDictKey(coupon?.id, product?.idx)

        coupon.mcId = coupon?.id
        pricingPromotionListUsed.push(coupon)
        pricingMcPrivatePercentDict[key] = coupon
      })
    }

    product.promotionListUsed = pricingPromotionListUsed

    // Lưu từ điển cho Addon
    product?.addonShopCart?.forEach((addon: any) => {
      const addOnKey = addon?.idx
      let addonPromotionListUsed: any = []

      // Lưu KM Addon
      if (addon?.couponPercent?.length > 0) {
        addonPercentDict = {
          ...addonPercentDict,
          ...processPromotionDict(addon?.couponPercent, addOnKey)
        }
        addonPromotionListUsed = addonPromotionListUsed.concat(addon?.couponPercent)
      }

      if (addon?.couponPrices?.length > 0) {
        addonPricesDict = {
          ...addonPricesDict,
          ...processPromotionDict(addon?.couponPrices, addOnKey)
        }
        addonPromotionListUsed = addonPromotionListUsed.concat(addon?.couponPrices)
      }

      if (addon?.lstMcPrivatePercent?.length > 0) {
        addonLstMcPrivatePercentDict = {
          ...addonLstMcPrivatePercentDict,
          ...processPromotionDict(addon?.lstMcPrivatePercent, addOnKey)
        }
        addonPromotionListUsed = addonPromotionListUsed.concat(addon?.lstMcPrivatePercent)
      }

      if (addon?.lstMcPrivatePrice?.length > 0) {
        addonLstMcPrivatePriceDict = {
          ...addonLstMcPrivatePriceDict,
          ...processPromotionDict(addon?.lstMcPrivatePrice, addOnKey)
        }
        addonPromotionListUsed = addonPromotionListUsed.concat(addon?.lstMcPrivatePrice)
      }

      addon.promotionListUsed = addonPromotionListUsed
      // Lưu addon chung trong từ điển
      addOnDict[addOnKey] = addon
    })

    // Lưu sản phẩm trong từ điển
    productDict[productKey] = product
  })

  cartDetailCopy?.forEach((product: any) => {
    if (product?.lstPricing?.length > 0) {
      product?.lstPricing?.forEach((pricing: any) => {
        // Key của product được lấy theo key của pricing
        const productKey = pricing?.idx

        if (productKey in productDict) {
          pricing.calculationDetail = productDict[productKey]
        }

        pricing?.lstAddon?.forEach((addon: any) => {
          const addOnKey = addon?.idx

          addon.addonDetail = addOnDict[addOnKey]
        })
      })
    } else {
      const productKey = product?.idx

      product.calculationDetail = productDict[productKey]
    }
  })

  return cartDetailCopy
}

// Hàm lấy giá trị đơn giá từ danh sách chiến lược định giá (Lũy kế, Bậc Thang, Khối Lượng)
export function getPriceForUnit(unit: any, pricingData: any) {
  let price = 0

  const cloneData = cloneDeep(pricingData)

  // Sort data by unitFrom in ascending order
  const sortedData = [...cloneData].sort((a, b) => a.unitFrom - b.unitFrom)

  for (const item of sortedData) {
    if (unit >= item.unitFrom && (item.unitTo === -1 || unit <= item.unitTo)) {
      price = item?.price || item?.unitPrice
    }
  }

  return price // Return 0 if no matching range is found
}

//#endregion
