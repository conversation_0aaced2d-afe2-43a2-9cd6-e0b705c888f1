'use client'

import React, { useEffect, useState } from 'react'

import { usePathname, useRouter } from 'next/navigation'

import { LoadingButton } from '@mui/lab'
import { Button, Divider, Form, Modal } from 'antd'
import { useSelector } from 'react-redux'

import Cookies from 'js-cookie'

import classNames from 'classnames'

import { paymentMethodEnum } from '@views/payment/utils/paymentUtils'

import { formatCurrency } from '@/constants/commonFunction'
import { useResponsive } from '@/hooks'
import { infoServiceSelect } from '@/redux-store/slices/PaymentSlice'

import { trackDirectPurchase } from '@/components/third-parties/matomo/utils/payment'
import Voucher from '@views/payment/components/voucher/Voucher'
import { IN_SERVICE_GROUP, IN_TOTAL_SERVICE_GROUP } from '@views/payment/constant/PaymentConstant'
import useCountdown from '../../hooks/useCountDown'
import { message } from '@/components/notification'

type orderSummaryProps = {
  totalProductQuantity: number | string
  totalProductPrice: number | string
  shippingFee: number
  noteShippingFee?: number // Phí vận chuyển, thanh toán khi nhận hàng - Không tính vào tổng thanh toán
  isShowVoucher: boolean
  totalPrice: number | string
  couponPrice: number | string
  typeCoupon?: string
  showSessionTimeout?: boolean // Control hiển thị tính năng timeout cho thanh toán sim
  initialMinutes?: number // Số phút ban đầu cho countdown
  initialUnit?: number // Số giây tương ứng với phút ban đầu
  interfaceProps?: {
    priceClass?: string // Class CSS cho giá
    showNoteShippingFee?: boolean // Hiển thị thông báo tiền giao hàng
  }
}

const SessionTimeoutModal = ({ isOpen, onRetry }: { isOpen: boolean; onRetry: () => void }) => {
  return (
    <Modal width={400} closable={false} footer={null} open={isOpen} centered>
      <div className='flex w-full flex-col bg-white'>
        <div className='flex gap-4'>
          <div className='flex-1'>
            <h2 className='text-base font-semibold leading-6 tracking-[0.08px] text-gray-11'>
              Phiên thanh toán đã hết hạn
            </h2>
          </div>
          <div className='flex items-start'>
            <i onClick={onRetry} className='onedx-close-icon size-6 cursor-pointer' />
          </div>
        </div>

        <p className='py-4 text-sm font-normal leading-5 tracking-[0.07px] text-text-neutral-medium'>
          Để đảm bảo an toàn cho giao dịch của bạn, phiên thanh toán đã hết hạn do không có hoạt động trong một thời
          gian. Vui lòng thực hiện đăng ký lại đơn hàng.
        </p>

        <div className='flex justify-end pt-2'>
          <Button className='min-w-[120px] px-4 py-2' type='primary' onClick={onRetry}>
            Thực hiện lại
          </Button>
        </div>
      </div>
    </Modal>
  )
}

const OrderSummary = ({
  isLoading,
  handleErrorModalClose,
  isErrorModalVisible,
  typeCoupon,
  promotionFn,
  isOpenModalCoupon,
  couponUsed,
  isOrderService
}: {
  isLoading: boolean | undefined
  handleErrorModalClose?: any
  isErrorModalVisible?: any
  typeCoupon: string
  promotionFn: any
  couponUsed: any
  isOpenModalCoupon: boolean
  isOrderService?: boolean
}) => {
  const { isMobile } = useResponsive()
  const form = Form.useFormInstance()
  const pathName = usePathname()
  const router = useRouter()

  const orderSummary: orderSummaryProps = Form.useWatch('OrderSummary', {
    form,
    preserve: true
  })

  // Khởi tạo countdown chỉ khi showSessionTimeout = true
  const { minutes, seconds, timeLeft } = useCountdown(
    orderSummary?.initialMinutes ?? 10,
    orderSummary?.initialUnit ?? 60,
    orderSummary?.showSessionTimeout ?? false
  )

  // State for session timeout modal
  const [isTimeoutModalOpen, setIsTimeoutModalOpen] = useState(false)

  // Handle retry action
  const handleRetry = () => {
    setIsTimeoutModalOpen(false)
    form.resetFields()
    // Thực hiện lại: Người dùng click chọn button sẽ điều hướng đến trang chủ KHCN
    router.push('/personal')
  }

  // Xử lý khi hết thời gian - chỉ khi showSessionTimeout = true
  useEffect(() => {
    if (timeLeft <= 0 && orderSummary?.showSessionTimeout) {
      setIsTimeoutModalOpen(true)
    }
  }, [timeLeft, orderSummary?.showSessionTimeout])

  const infoBilling = useSelector(infoServiceSelect)
  const [expand, setExpand] = useState(false)

  const payments = [
    infoBilling?.pricing?.paymentMethod,
    infoBilling?.deviceInfo?.paymentMethod,
    infoBilling?.serviceGroup?.paymentMethod
  ]

  const paymentForm = Form.useWatch('PaymentMethod', {
    form,
    preserve: true
  })?.value

  const getPaymentMethod = () => {
    if (payments.includes(paymentMethodEnum.BY_CASH) || isOrderService) {
      return 'Đăng ký'
    }

    if (
      (pathName?.includes('combo') && paymentForm !== paymentMethodEnum.BY_CASH) ||
      (pathName?.includes('service-group') && paymentForm !== paymentMethodEnum.BY_CASH) ||
      payments.includes(paymentMethodEnum.VNPTPAY)
    ) {
      return 'Thanh toán'
    }

    return 'Đăng ký'
  }

  useEffect(() => {
    if (orderSummary?.isShowVoucher) {
      form.setFieldValue(
        'OrderSummary.couponPrice',
        infoBilling.coupons.reduce((total: number, coupon: any) => total + coupon.price, 0)
      )
    }
  }, [form, infoBilling.coupons, orderSummary, orderSummary?.isShowVoucher])

  const handleSubmit = () => {
    const { invoiceInfoList } = infoBilling
    const invoiceInfo = invoiceInfoList.find((invoiceInfo: any) => invoiceInfo.checked)

    if ((!invoiceInfo?.streetId || !invoiceInfo?.wardId) && !pathName?.includes('/checkout/sim')) {
      message.error('Vui lòng cập nhật địa chỉ xuất hóa đơn theo thông tin địa giới hành chính mới')
    } else {
      if (form.getFieldValue('rules') && infoBilling?.pricing) {
        // Format tracking data theo cấu trúc mới
        const matomoTrackingData = {
          items: [
            {
              productId: infoBilling.pricing.id,
              productName: infoBilling.pricing.name,
              productCategory: infoBilling.service.name,
              price: infoBilling.totalAmountAfterTaxFinal,
              quantity: orderSummary?.totalProductQuantity
            }
          ],
          total: infoBilling.totalAmountAfterTaxFinal
        }

        // Track direct purchase với items array
        trackDirectPurchase(matomoTrackingData.items)

        // Lưu data với format mới vào cookie
        Cookies.set('matomoTrackingData', JSON.stringify(matomoTrackingData), {
          expires: 1
        })

        // Log để verify
        console.log('Direct purchase tracking data:', matomoTrackingData)
      }

      form.submit()
    }
  }

  return (
    <div className='relative flex flex-col gap-2.5 justify-self-end pb-6 pt-8 tracking-tight md:sticky md:bottom-0 md:z-10 md:w-full md:gap-0 md:p-0 md:pt-2'>
      {!isMobile && (
        <div className='sticky top-20 grid w-80 grid-cols-1 items-center justify-start rounded-lg md:w-full'>
          <div className='flex flex-col items-stretch self-stretch rounded-xl bg-white p-4'>
            <div className='flex pb-[10px] text-headline-18 font-medium text-text-neutral-strong'>Thanh toán</div>

            <div className='flex justify-between gap-6 py-3'>
              <div className='shrink grow basis-0 text-sm font-normal leading-tight text-text-neutral-strong'>
                Tổng đơn hàng ({orderSummary?.totalProductQuantity || 0})
              </div>
              <div className='flex font-normal leading-tight'>
                <div className='text-xs text-gray-8'>₫</div>
                <div className='text-sm text-text-neutral-strong'>
                  {formatCurrency(orderSummary?.totalProductPrice)}
                </div>
              </div>
            </div>
            {!!orderSummary?.shippingFee && !orderSummary?.interfaceProps?.showNoteShippingFee && (
              <div className='flex justify-between gap-6 pb-2 pt-3'>
                <div className='shrink grow basis-0 text-sm font-normal leading-tight text-text-neutral-strong'>
                  Phí vận chuyển
                </div>
                <div className='flex'>
                  <div className='text-xs font-normal leading-none text-gray-8'>₫</div>
                  <div className='text-sm font-normal leading-tight text-text-neutral-strong'>
                    {formatCurrency(orderSummary?.shippingFee)}
                  </div>
                </div>
              </div>
            )}

            {(infoBilling?.pricing?.id || infoBilling?.combo?.comboPlan?.id) && orderSummary?.isShowVoucher && (
              <>
                <div className='flex justify-between gap-6 pb-3 pt-4'>
                  <div className='shrink grow basis-0 text-sm font-normal leading-tight text-dark-charcoal'>
                    Khuyến mại
                  </div>
                </div>

                {![IN_TOTAL_SERVICE_GROUP, IN_SERVICE_GROUP].includes(typeCoupon) && (
                  <Voucher
                    type={typeCoupon}
                    promotionFn={promotionFn}
                    isOpenModalVoucher={isOpenModalCoupon}
                    couponUsed={couponUsed}
                    popoverPosition='bottomRight'
                    totalPrice={orderSummary?.totalPrice}
                  />
                )}
              </>
            )}
          </div>
          <div className='mx-[18px] md:mx-0 md:bg-white'>
            <Divider
              style={{
                margin: 0,
                borderTop: 'none',
                height: '1px',
                background:
                  'repeating-linear-gradient(to right, rgba(2, 23, 60, 0.09) 0, rgba(2, 23, 60, 0.09) 8px, transparent 8px, transparent 16px)'
              }}
            />
          </div>
          <div className='flex flex-col items-stretch gap-3 self-stretch rounded-xl bg-white px-4 pb-4 pt-2 md:gap-3'>
            <div className='flex justify-between gap-6'>
              <div className='shrink grow basis-0 text-lg font-semibold leading-normal text-text-neutral-strong'>
                Tổng thanh toán
              </div>
              <div className='flex text-green-7'>
                <div className=' text-xs font-normal leading-none'>₫</div>
                <div className=' text-lg font-semibold leading-normal'>{formatCurrency(orderSummary?.totalPrice)}</div>
              </div>
            </div>
            <LoadingButton
              className='h-10 w-full rounded-xl text-body-14 font-medium group-hover:hidden'
              color='primary'
              variant='contained'
              onClick={handleSubmit}
              loading={isLoading}
              disabled={infoBilling?.isNewAcc}
            >
              {getPaymentMethod()}
            </LoadingButton>
          </div>

          {orderSummary?.interfaceProps?.showNoteShippingFee && orderSummary?.shippingFee > 0 && (
            <>
              <div className='mx-[18px] md:mx-0 md:bg-white'>
                <Divider
                  style={{
                    margin: 0,
                    borderTop: 'none',
                    height: '1px',
                    background:
                      'repeating-linear-gradient(to right, rgba(2, 23, 60, 0.09) 0, rgba(2, 23, 60, 0.09) 8px, transparent 8px, transparent 16px)'
                  }}
                />
              </div>
              <div className='flex flex-col items-stretch gap-3 self-stretch rounded-xl bg-white px-4 py-3 md:gap-3'>
                <div className='flex justify-between gap-6'>
                  <div className='flex shrink grow basis-0 flex-col'>
                    <div className='body-14-semibold text-text-neutral-strong'>Phí vận chuyển</div>
                    <div className='body-14-regular text-text-error-default'>(Thanh toán khi nhận hàng)</div>
                  </div>
                  <div
                    className={classNames(
                      'flex items-center',
                      orderSummary?.interfaceProps?.priceClass || 'text-green-7'
                    )}
                  >
                    <div className='caption-12-regular'>₫</div>
                    <div className='body-14-semibold'>{formatCurrency(orderSummary?.shippingFee)}</div>
                  </div>
                </div>
              </div>
            </>
          )}

          {orderSummary?.showSessionTimeout && (
            <div className='mt-4 flex gap-4 rounded-xl bg-sme-blue-3 p-4 text-sme-blue-8'>
              <div className='flex-1 self-center text-body-14'>Phiên thanh toán sẽ hết hạn sau:</div>
              <div className='flex min-w-20 items-center justify-center rounded bg-sme-blue-4 px-3 py-1.5 text-headline-20'>
                <span>
                  {minutes}:{seconds}
                </span>
              </div>
            </div>
          )}
        </div>
      )}
      {isMobile && (
        <>
          <div className='justify-end gap-4 border-b border-solid border-slate-200 bg-white px-4 pb-8 pt-4'>
            <div className='flex flex-col '>
              <div className='items-start pb-6'>
                <div className='pb-1.5 pt-2' onClick={() => setExpand(!expand)}>
                  <div className='flex'>
                    <div className='flex-1 text-lg'>Thanh toán</div>
                    <div className='flex'>
                      {expand ? (
                        <span>
                          <i className={`onedx-chevron-down size-5 cursor-pointer transition-transform `} />
                        </span>
                      ) : (
                        <span>
                          <i className={`onedx-chevron-down size-5 rotate-180 cursor-pointer transition-transform`} />
                        </span>
                      )}
                    </div>
                  </div>
                </div>
                {expand && (
                  <div>
                    <div className='flex justify-between gap-6 py-3'>
                      <div className='text-sm font-normal leading-5 tracking-[0.075px]'>
                        Tổng đơn hàng ({orderSummary?.totalProductQuantity || 0})
                      </div>
                      <div className='flex font-normal leading-tight'>
                        <div className='text-xs text-gray-8'>₫</div>
                        <div className='text-sm font-normal leading-5 tracking-[0.075px]'>
                          {formatCurrency(orderSummary?.totalProductPrice)}
                        </div>
                      </div>
                    </div>
                    <div className='flex justify-between gap-6 pb-2 pt-3'>
                      <div className='text-sm font-normal leading-5 tracking-[0.075px]'>Phí vận chuyển</div>
                      <div className='flex'>
                        <div className='text-xs font-normal leading-none text-gray-8'>₫</div>
                        <div className='text-sm font-normal leading-5 tracking-[0.075px]'>
                          {formatCurrency(orderSummary?.shippingFee ?? 0)}
                        </div>
                      </div>
                    </div>
                    {(infoBilling?.pricing?.id || infoBilling?.combo?.comboPlan?.id) && orderSummary?.isShowVoucher && (
                      <>
                        <div className='flex justify-between gap-6 pb-3 pt-4'>
                          <div className='text-sm font-normal leading-5 tracking-[0.075px]'>Khuyến mại</div>
                        </div>

                        {![IN_TOTAL_SERVICE_GROUP, IN_SERVICE_GROUP].includes(typeCoupon) && (
                          <Voucher
                            type={typeCoupon}
                            promotionFn={promotionFn}
                            isOpenModalVoucher={isOpenModalCoupon}
                            couponUsed={couponUsed}
                          />
                        )}
                      </>
                    )}
                  </div>
                )}
                <Divider className='m-0' dashed />
              </div>

              <div className='flex h-10 shrink grow basis-0 items-center justify-between'>
                <div className='inline-flex flex-col items-start justify-start'>
                  <div className='inline-flex items-start justify-start text-green-7'>
                    <div className='text-3xs font-medium leading-4'>₫</div>
                    <div className='text-lg font-semibold leading-6'>{formatCurrency(orderSummary?.totalPrice)}</div>
                  </div>
                  <div className='text-3xs font-medium leading-none text-text-neutral-light'>
                    (Chưa bao gồm một số phụ phí)
                  </div>
                </div>

                <div className='flex items-center justify-center gap-2 rounded-xl bg-[#0070c4] px-4 py-2.5'>
                  <LoadingButton
                    className='w-full rounded-xl text-body-14 font-medium group-hover:hidden'
                    color='primary'
                    variant='contained'
                    onClick={handleSubmit}
                    loading={isLoading}
                    disabled={infoBilling?.isNewAcc}
                  >
                    {getPaymentMethod()}
                  </LoadingButton>
                </div>
              </div>
            </div>
          </div>
        </>
      )}

      {isErrorModalVisible !== '' && (
        <Modal
          width={400}
          title={
            <div className='flex size-10 items-center justify-center rounded-full bg-bg-error-light'>
              <i className='onedx-warning size-5 text-text-error-strong' />
            </div>
          }
          open={isErrorModalVisible !== ''}
          onCancel={handleErrorModalClose}
          footer={[
            <>
              <Button
                className='ml-4 px-4 py-2'
                type='primary'
                loading={isLoading}
                onClick={() => {
                  form.submit()
                }}
              >
                Thử lại
              </Button>
              <Button className='ml-4 px-4 py-2' loading={isLoading} onClick={handleErrorModalClose}>
                Hủy
              </Button>
            </>
          ]}
        >
          <div className='mb-2 text-headline-16 font-semibold text-text-neutral-strong'>{isErrorModalVisible}</div>
          <div className='text-body-14 font-normal text-text-neutral-light'>Bạn có muốn thử lại không ?</div>
        </Modal>
      )}

      {/* Chỉ hiển thị modal khi showSessionTimeout = true */}
      {orderSummary?.showSessionTimeout && <SessionTimeoutModal isOpen={isTimeoutModalOpen} onRetry={handleRetry} />}
    </div>
  )
}

OrderSummary.propTypes = {}

export default OrderSummary
