'use client'

/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useEffect, useState } from 'react'

import { useRouter } from 'next/navigation'

import { Button, Divider, message, Modal, Spin } from 'antd'

import { isArray, isEmpty, isNil, toLower } from 'lodash'

import { useResponsive } from '@/hooks'
import useCountdown from '../../hooks/useCountDown'

import { formatCurrency } from '@/constants/commonFunction'
import { downloadQRCode } from '@/views/management/my-service/components/search/ultil'
import usePopup from '@views/payment/hooks/useOpenMyAddress'

import smeSubscriptionInstance from '@/models/SmeSubscription'
import DXPortal from '@/models/DXPortal'

export const STATUS_QR_ENUM = {
  SUCCESS: 'SUCCESS',
  FAIL: 'FAIL',
  PROCESSING: 'PROCESSING',
  NOT_FOUND: 'NOT_FOUND'
}

export type QRCodeStateProps = {
  totalFinalPrice: number
  totalQuantity: number
  totalPrice: number
  shippingFee: number
  setupFee: number
  successPopup: {
    title: string
    content: string
  }
}

export type QRCodeFunctionProps = {
  refetchQRCode: () => void
  onSuccess: (setVisiblePopup: (value: boolean) => void) => void
}

interface Props {
  isOpenModal: boolean
  handleCloseModal: () => void
  name: string
  qrCodeImage: string
  expiredTime: number
  billId: string | number | string[] | number[]
  state?: Partial<QRCodeStateProps>
  functions?: Partial<QRCodeFunctionProps>
}

export const QRCodeModal = ({
  isOpenModal,
  handleCloseModal,
  name, // Thêm tên để phân biệt các modal (good for debugging)
  qrCodeImage,
  expiredTime = 10,
  billId,
  state: { totalFinalPrice = 0, totalQuantity = 1, totalPrice = 0, shippingFee, setupFee, successPopup } = {},
  functions: { refetchQRCode, onSuccess } = {}
}: Props) => {
  const router = useRouter()
  const { isDesktop } = useResponsive()
  const { timeLeft, minutes, seconds } = useCountdown(expiredTime, 60, isOpenModal)
  const [isLoading, setLoading] = useState(false)
  const [visiblePopupError, setVisiblePopupError] = useState(false)

  const { handleOpenModalPaymentSuccess } = usePopup()

  const isExpired = timeLeft <= 0

  useEffect(() => {
    let intervalId: string | number | NodeJS.Timeout | undefined
    const billsId = isArray(billId) ? billId[0] : billId

    const handlePaymentError = async () => {
      setVisiblePopupError(true)
      clearInterval(intervalId)
    }

    const handleAdminOrDevPortal = async (data: any) => {
      let portalPath = DXPortal.admin.createPath

      if (toLower(data.PORTAL_TYPE) === 'dev') {
        portalPath = DXPortal.dev.createPath
      }

      const actionMessages: any = {
        5: 'Thuê bao đã được gia hạn thành công',
        3: 'Thuê bao đã được kích hoạt lại',
        4: 'Thuê bao đã được kích hoạt lại',
        '-1': 'Thuê bao đã được tạo thành công',
        1: 'Cập nhật thuê bao thành công.',
        2: 'Đổi gói dịch vụ thành công'
      }

      if (data.action === 5) {
        router.push(portalPath(`/subscription/service/${data.SUBSCRIPTION_ID}`))

        message.success(actionMessages[5])
      } else if (data.action === 3 || data.action === 4) {
        setTimeout(() => {
          router.push(portalPath(`/subscription/service/${data.SUBSCRIPTION_ID}`))

          message.success(actionMessages[3])
        }, 20000)
      } else {
        const timeout = data?.lstBillId?.length ? data.lstBillId.length * 1200 : 2000

        setTimeout(() => {
          message.success(actionMessages[data.action] || '')

          router.replace(portalPath('/subscription/service'))
        }, timeout)
      }
    }

    const handleSMEPortal = async (data: any, baseParams: any) => {
      baseParams.append('owner', data?.SERVICE_OWNER)
      baseParams.append('cartCodeSub', data?.CART_CODE_SUB)
      baseParams.append('cartCodeBill', data?.CART_CODE_BILL)
      baseParams.append('subCode', data?.SUBSCRIPTION_CODE)
      baseParams.append('isOn', data?.isON)
      baseParams.append('serviceName', data?.SERVICE_NAME)
      baseParams.append('metadata', JSON.stringify(data?.metadata || {}))

      handleOpenModalPaymentSuccess()
      router.push(`/sme-portal/payment-success?${baseParams.toString()}`)
    }

    const checkPaymentStatus = async () => {
      try {
        const response = await smeSubscriptionInstance.checkOrderQr(billsId)
        // if (response === STATUS_ENUM.PROCESSING) Continue checking

        const isCombo = isEmpty()

        const baseParams = new URLSearchParams({
          serviceId: isCombo ? response.COMBO_ID : response.SERVICE_ID,
          planId: isCombo ? response.COMBO_PLAN_ID : response.PLAN_ID,
          orderId: response.MERCHANT_ORDER_ID,
          paymentFrom: response.SCREEN_TYPE,
          isCombo: isCombo ? 'YES' : 'NO',
          actionType: response.ACTION_TYPE,
          subscriptionId: response.SUBSCRIPTION_ID,
          billingId: response.BILLING_ID,
          lstSubId: response?.lstSubId ? response?.lstSubId.join(',') : null
        })

        if (isExpired) {
          clearInterval(intervalId)

          return
        }

        // if (response === 'true' || response === true) {
        if (response.STATUS === STATUS_QR_ENUM.SUCCESS) {
          clearInterval(intervalId)
          setLoading(true)

          /* Xử lý đoạn điều hướng thanh toan thành công */

          if (response.PORTAL_TYPE === 'ADMIN' || response.PORTAL_TYPE === 'DEV') {
            await handleAdminOrDevPortal(response)
          } else if (response.PORTAL_TYPE === 'SME') {
            await handleSMEPortal(response, baseParams)
          }

          // setVisiblePopup(true)

          handleCloseModal()
        }

        if ([STATUS_QR_ENUM.FAIL, STATUS_QR_ENUM.NOT_FOUND].includes(response.STATUS)) {
          clearInterval(intervalId)
          handlePaymentError()
        }
      } catch (error) {
        console.error('Error checking payment status:', error)
      }
    }

    if (isOpenModal && !isNil(billsId) && billsId !== -1) {
      intervalId = setInterval(checkPaymentStatus, 3000) // Check every 3 seconds
    }

    return () => {
      clearInterval(intervalId)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpenModal, billId, isExpired])

  if (isLoading) {
    return <Spin spinning={isLoading} />
  }

  return (
    <>
      <Modal
        open={isOpenModal}
        style={{ maxHeight: '100vh' }}
        width={isDesktop ? 680 : '100vw'}
        closeIcon={false}
        centered
        title={
          <div className='flex justify-between'>
            <div className='flex items-center text-headline-16 font-semibold'>Thanh toán bằng QR Code</div>
            <Button
              type='text'
              className='flex size-fit cursor-pointer items-start bg-inherit p-0'
              onClick={handleCloseModal}
            >
              <i className='onedx-close-icon size-6' />
            </Button>
          </div>
        }
        footer={
          <div className='flex justify-end gap-4'>
            <Button
              type='primary'
              style={{ width: '120px' }}
              onClick={() => downloadQRCode(qrCodeImage)}
              disabled={!timeLeft}
              className='border-none'
            >
              Lưu mã QR
            </Button>
            <Button
              type='text'
              style={{ width: '120px' }}
              className='border border-solid border-gray-alpha-3'
              onClick={handleCloseModal}
            >
              Huỷ
            </Button>
          </div>
        }
      >
        <div
          id={`modal-${name}`}
          className='flex h-[355px] gap-6 py-4 sm:h-fit sm:flex-col sm:items-center sm:py-0 sm:pt-4'
        >
          <div id='checkout-qrcode' className='relative flex w-[235px] flex-col gap-3'>
            <Button
              type='text'
              className='relative h-[295px] border-none p-0'
              style={{
                opacity: timeLeft ? '1' : '0.2'
              }}
              disabled={!timeLeft}
              onClick={() => downloadQRCode(qrCodeImage)}
            >
              <img src={`data:image/png;base64,${qrCodeImage}`} />
            </Button>
            {!timeLeft && (
              <Button
                type='primary'
                className='absolute left-1/2 top-1/2 z-20 size-max -translate-x-1/2 -translate-y-full'
                onClick={refetchQRCode}
              >
                Lấy mã QR mới
              </Button>
            )}
            {!!timeLeft ? (
              <div className='flex items-center justify-center gap-2'>
                <div className='text-body-14 font-medium text-gray-6'>Mã QR hết hạn sau:</div>
                <div className='text-body-14 font-medium text-red-6'>
                  {minutes}:{seconds}
                </div>
              </div>
            ) : (
              <div className='text-center text-body-14 font-medium text-gray-6'>Mã QR đã hết hạn</div>
            )}
          </div>
          {isDesktop ? (
            <Divider type='vertical' className='mx-0 h-full w-px bg-gray-alpha-3' />
          ) : (
            <Divider type='horizontal' className='my-0 h-px w-full bg-gray-alpha-3' />
          )}

          <div className='flex w-80 flex-col px-4 sm:w-full sm:px-0'>
            <div className='py-2 text-headline-18 font-medium'>Thanh toán</div>
            <div className='flex justify-between py-2 text-body-14 font-normal'>
              <div>Tổng đơn hàng ({totalQuantity ?? 0})</div>
              <div className='flex'>
                <div className='text-caption-12 leading-4'>₫</div>
                <div className='leading-5'>{formatCurrency(totalPrice ?? 0)}</div>
              </div>
            </div>

            {!isNil(shippingFee) && !!shippingFee && (
              <div className='flex justify-between py-2 text-body-14 font-normal'>
                <div>Phí vận chuyển</div>
                <div className='flex'>
                  <div className='text-caption-12 leading-4'>₫</div>
                  <div className='leading-5'>{formatCurrency(shippingFee)}</div>
                </div>
              </div>
            )}

            {!isNil(setupFee) && (
              <div className='flex justify-between py-2 text-body-14 font-normal'>
                <div>Phí lắp đặt</div>
                <div className='flex'>
                  <div className='text-caption-12 leading-4'>₫</div>
                  <div className='leading-5'>{formatCurrency(setupFee)}</div>
                </div>
              </div>
            )}

            <Divider
              style={{
                margin: '10px 0',
                borderTop: 'none',
                height: '1px',
                background:
                  'repeating-linear-gradient(to right, rgba(2, 23, 60, 0.09) 0, rgba(2, 23, 60, 0.09) 8px, transparent 8px, transparent 16px)'
              }}
            />
            <div className='flex justify-between py-2 text-headline-18 font-semibold'>
              <div>Tổng thanh toán</div>
              <div className='flex text-green-7'>
                <div className='text-caption-12 leading-4'>₫</div>
                <div className='leading-6 '>{formatCurrency(totalFinalPrice ?? 0)}</div>
              </div>
            </div>
          </div>
        </div>
      </Modal>

      <Modal
        width={400}
        title={
          <div className='flex size-10 items-center justify-center rounded-full bg-bg-error-light'>
            <i className='onedx-warning size-5 text-text-error-strong' />
          </div>
        }
        open={visiblePopupError}
        onCancel={() => setVisiblePopupError(false)}
        footer={[
          <>
            <Button
              className='ml-4 px-4 py-2'
              type='primary'
              onClick={() => {
                if (refetchQRCode) {
                  refetchQRCode()
                } else {
                  message.error('Có lỗi xảy ra, vui lòng thử lại sau')
                }

                setVisiblePopupError(false)
              }}
            >
              Thử lại
            </Button>
            <Button className='ml-4 px-4 py-2' onClick={() => setVisiblePopupError(false)}>
              Hủy
            </Button>
          </>
        ]}
      >
        <div className='mb-2 text-headline-16 font-semibold text-text-neutral-strong'>Thanh toán thất bại</div>
        <div className='text-body-14 font-normal text-text-neutral-light'>Bạn có muốn thử lại không ?</div>
      </Modal>
    </>
  )
}
