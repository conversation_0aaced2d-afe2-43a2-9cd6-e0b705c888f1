import React from 'react'

import { useSelector } from 'react-redux'
import { Button } from 'antd'

import { useUser } from '@/hooks'

import { customerInfoSelect } from '@/redux-store/slices/PaymentSlice'

import usePopup from '@views/payment/hooks/useOpenMyAddress'

const CustomerInformation = () => {
  const { user } = useUser()
  const { handleOpenEInvoiceAddress } = usePopup()
  const customerInfo = useSelector(customerInfoSelect)

  const isPersonal = user?.customerType === 'PERSONAL'

  if (!customerInfo) {
    return <div>Không có thông tin</div>
  }

  return (
    <div>
      <div className='flex items-center gap-2 sm:px-4'>
        <div className='h-4 w-0.5 bg-sme-orange-7' />
        <h3 className='body-14-semibold text-gray-8'>Thông tin khách hàng</h3>
      </div>

      <div className='mt-3 flex flex-col justify-start gap-2 self-stretch rounded-lg bg-white px-6 py-4'>
        <div className='flex grow gap-4'>
          <div className='flex grow flex-col gap-1'>
            <div className='body-14-semibold flex items-center gap-1 self-stretch text-gray-11'>
              <div>{`${isPersonal ? customerInfo?.fullName : customerInfo?.smeName}`}</div>
              <div className='caption-12-medium'>|</div>
              <div>{`${isPersonal ? customerInfo?.identityNo : (customerInfo?.taxNo ?? customerInfo?.identityNo)} `}</div>
            </div>
            <p className='body-14-regular m-0 self-stretch text-gray-11'>{customerInfo?.address}</p>
          </div>

          <div className='block sm:hidden'>
            <Button type='link' onClick={handleOpenEInvoiceAddress}>
              Thay đổi
            </Button>
          </div>

          {/* danh cho hiển thị mobile */}
          <div className=' hidden sm:flex sm:h-20 sm:items-center' onClick={handleOpenEInvoiceAddress}>
            <i className='onedx-chevron-right size-4' />
          </div>
        </div>

        {/* <Divider
          style={{
            margin: '0',
            borderTop: 'none',
            height: '1px',
            background: isDesktop
              ? 'rgba(2, 23, 60, 0.09)'
              : 'repeating-linear-gradient(to right, rgba(2, 23, 60, 0.09) 0, rgba(2, 23, 60, 0.09) 8px, transparent 8px, transparent 16px)'
          }}
        />

        <div className='flex grow flex-col items-start'>
          <p className='mb-2 mt-0 w-[95%] truncate text-sm text-gray-8'>
            Địa chỉ lắp đặt: {`${customerInfo?.setupAddress}`}
          </p>
          <div className='caption-12-medium flex items-center gap-1 self-stretch text-white'>
            <div className='rounded-md bg-gray-6 px-2 py-0.5'>Mặc định</div>
          </div>
        </div> */}
      </div>
    </div>
  )
}

export default CustomerInformation
