'use client'

import React, { useEffect } from 'react'

import { usePathname, useRouter, useSearchParams } from 'next/navigation'

import { useMutation } from '@tanstack/react-query'

import { message } from 'antd'

import Payment from '@/models/Payment'

import { ACTION_MESSAGES } from '@views/payment/constant/PaymentConstant'

import usePopup from '@views/payment/hooks/useOpenMyAddress'
import warehouseInstance from '@/models/Warehouse'
import SmeSubscription from '@/models/SmeSubscription'

const PaymentCallbackPage = () => {
  const pathname = usePathname()

  const router = useRouter()
  const { handleOpenModalPaymentError, handleOpenModalPaymentSuccess } = usePopup()

  const searchParams = useSearchParams()
  const orderId = searchParams.get('data')
  const responseCode = searchParams.get('vnptpayResponseCode')
  const isIotPortal = pathname.includes('/iot-portal') || searchParams.get('portal') === 'iot'

  const updateStockByActionMutation = useMutation({
    mutationFn: (data: any) => warehouseInstance.updateStockByAction(data)
  })

  const handleAdminOrDevPortal = async (data: any) => {
    // Tên portal
    const portal = `${data?.PORTAL_TYPE?.toLowerCase()}-portal`

    if (data.action === 5) {
      message.success(ACTION_MESSAGES[5])

      setTimeout(() => {
        window.location.replace(`/${portal}/subscription/service/${data.SUBSCRIPTION_ID}`)
      }, 2000)
    } else if (data.action === 3 || data.action === 4) {
      setTimeout(() => {
        window.location.replace(`/${portal}/subscription/service/${data.SUBSCRIPTION_ID}`)

        message.success(ACTION_MESSAGES[3])
      }, 20000)
    } else {
      const timeout = data?.lstBillId?.length ? data.lstBillId.length * 1200 : 2000

      const lstSubId = data?.lstSubId ? data?.lstSubId : []

      //Cập nhật thông tin số lượng trong kho
      if (lstSubId.length > 0) {
        try {
          const res = await SmeSubscription.getSubscriptionsByIds(lstSubId)

          const result = res.map((value: any) => ({
            serviceId: value.serviceId,
            variantId: value.variantId,
            action: 'PAYMENT_SUCCESS',
            quantity: value.quantity
          }))

          if (result.length > 0) updateStockByActionMutation.mutateAsync(result)
        } catch (error) {
          console.error('Error while updating stock:', error)
        }
      }

      setTimeout(() => {
        message.success(ACTION_MESSAGES[data.action] || '')

        if (data.isOrders) {
          window.location.replace(`/${portal}/order/list`)
        } else {
          window.location.replace(`/${portal}/subscription/service`)
        }
      }, timeout)
    }
  }

  const handlePaymentError = async (data: any, baseParams: any) => {
    baseParams.append('pricingMultiPlanId', data.PRICING_MULTI_PLAN_ID || '')
    baseParams.append('portalType', data.PORTAL_TYPE)

    handleOpenModalPaymentError()
    router.push(`/sme-portal/payment-error?${baseParams.toString()}`)
  }

  const handleSMEPortal = async (data: any, baseParams: any) => {
    baseParams.append('owner', data?.SERVICE_OWNER)
    baseParams.append('cartCodeSub', data?.CART_CODE_SUB)
    baseParams.append('cartCodeBill', data?.CART_CODE_BILL)
    baseParams.append('subCode', data?.SUBSCRIPTION_CODE)
    baseParams.append('isOn', data?.isON)
    baseParams.append('serviceName', data?.SERVICE_NAME)
    baseParams.append('isOrders', data?.isOrders)
    baseParams.append('lstSubId', data?.lstSubId ? data?.lstSubId.join(',') : null)
    baseParams.append('metadata', JSON.stringify(data?.metadata || {})) // This will be encoded in the URL

    // Thêm param portal nếu từ iot-portal
    if (isIotPortal) {
      baseParams.append('portal', 'iot')
    }

    handleOpenModalPaymentSuccess()
    router.push(`/sme-portal/payment-success?${baseParams.toString()}`)
  }

  const activeMutation = useMutation({
    mutationKey: ['verifyPayment'],
    mutationFn: Payment.verifyPayment,
    onSuccess: async (data: any) => {
      if (data.TRANSACTION_STATUS === 1) {
        throw new Error('PROCESSING_PAYMENT')
      }

      const isCombo = data?.TYPE === 'COMBO'

      const baseParams = new URLSearchParams({
        serviceId: isCombo ? data.COMBO_ID : data.SERVICE_ID,
        planId: isCombo ? data.COMBO_PLAN_ID : data.PLAN_ID,
        orderId: data.MERCHANT_ORDER_ID,
        paymentFrom: data.SCREEN_TYPE,
        isCombo: isCombo ? 'YES' : 'NO',
        actionType: data.ACTION_TYPE,
        subscriptionId: data.SUBSCRIPTION_ID,
        billingId: data.BILLING_ID,
        action: data.action,
        subscriptionType: data?.TYPE,
        portal: isIotPortal ? 'iot' : ''
      })

      if (responseCode !== '00') {
        await handlePaymentError(data, baseParams)
      } else if (data.PORTAL_TYPE === 'ADMIN' || data.PORTAL_TYPE === 'DEV') {
        await handleAdminOrDevPortal(data)
      } else if (data.PORTAL_TYPE === 'SME') {
        await handleSMEPortal(data, baseParams)
      }
    }
  })

  useEffect(() => {
    document.body.style.overflow = 'hidden'

    if (orderId) {
      setTimeout(() => activeMutation.mutate({ orderId }), 3 * 1000)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [orderId])

  useEffect(
    () => () => {
      document.body.style.overflow = 'auto'
    },
    []
  )

  return (
    <div className='flex h-screen w-screen items-center justify-center'>
      <div className='flex size-fit items-center justify-center'>
        <i className='onedx-dx size-30 animate-[loadingDX_3s_linear_infinite]' />
        <i className='onedx-dx-other -ml-12 size-30 bg-gradient-to-r from-[#F47A2A] to-[#EFC65E]' />
      </div>
      <div className='text-[#2B68B2]'>
        <div className='title-56-bold'>Đang kiểm tra giao dịch</div>
        <div className='title-24-regular'>Vui lòng đợi trong giây lát...</div>
      </div>
    </div>
  )
}

export default PaymentCallbackPage
