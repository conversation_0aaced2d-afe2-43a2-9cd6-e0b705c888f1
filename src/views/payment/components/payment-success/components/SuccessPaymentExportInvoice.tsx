import React from 'react'

import { useSearchParams } from 'next/navigation'

import { useMutation, useQuery } from '@tanstack/react-query'
import { Button } from 'antd'

import warehouseInstance from '@/models/Warehouse'
import SmeSubscription from '@/models/SmeSubscription'

import { usePaymentRedirect } from '@views/payment/hooks/usePaymentRedirect'

// Màn đăng ký thuê bao thành công có xuất hóa đơn
export const SuccessPaymentExportInvoice = () => {
  const searchParams = useSearchParams()
  const lstSubId = searchParams.get('lstSubId')

  // Hàm điều hướng khi bấm nút
  const { toMyService, toSubscription } = usePaymentRedirect()

  const updateStockByActionMutation = useMutation({
    mutationFn: (data: any) => warehouseInstance.updateStockByAction(data)
  })

  //Cập nhật thông tin số lượng trong kho
  useQuery({
    queryKey: ['getSubscriptionsByIds', lstSubId],
    queryFn: async () => {
      let subIdList = []

      subIdList = lstSubId
        ? lstSubId
            .split(',')
            .map(id => Number(id.trim()))
            .filter(id => !isNaN(id))
        : []

      if (subIdList.length > 0) {
        const res = await SmeSubscription.getSubscriptionsByIds(subIdList)

        const result = res.map((value: any) => ({
          serviceId: value.serviceId,
          variantId: value.variantId,
          action: 'PAYMENT_SUCCESS',
          quantity: value.quantity
        }))

        if (result.length > 0) updateStockByActionMutation.mutateAsync(result)

        return result
      }

      return []
    },
    enabled: !!lstSubId
  })

  return (
    <>
      <div className='mb-2 text-base font-semibold leading-normal tracking-tight text-dark-charcoal'>
        Thanh toán thành công
      </div>

      <div className='mb-6'>
        <span className='text-sm font-normal leading-tight tracking-tight text-gray-8'>Truy cập </span>
        <span
          className='cursor-pointer text-sm font-semibold leading-tight tracking-tight text-[#0070c4] underline'
          onClick={toMyService}
        >
          {' '}
          Dịch vụ của tôi
        </span>
        <span className='text-sm font-normal leading-tight tracking-tight text-gray-8'>
          {' '}
          để xem thông tin chi tiết dịch vụ. Truy cập email để kiểm tra thông tin dịch vụ từ nhà cung cấp
        </span>
      </div>

      <div className='my-3 flex justify-end gap-2'>
        {/* <Button className='px-4 py-2' type='primary'>
          Xuất hóa đơn
        </Button> */}
        <Button className='px-4 py-2' type='primary' onClick={toSubscription}>
          Xem đơn hàng
        </Button>
      </div>
    </>
  )
}
