'use client'
import React, { useEffect } from 'react'

import { useSearchParams } from 'next/navigation'

import { useMutation, useQuery } from '@tanstack/react-query'

import { message, Modal } from 'antd'

import Cookies from 'js-cookie'

import { trackPaymentSuccess } from '@/components/third-parties/matomo/utils/payment'
import ShoppingCart from '@/models/ShoppingCart'
import SmeSubscription from '@/models/SmeSubscription'
import warehouseInstance from '@/models/Warehouse'

import {
  SuccessPaymentExportInvoice,
  SuccessPaymentService,
  SuccessPricingChange,
  SuccessReactivate,
  SuccessRenew
} from '@views/payment/components/payment-success/components'
import { SuccessPaymentTelco } from './components/SuccessPaymentTelco'

export type TelcoService = 'eSIM' | 'SIM' | 'mobilePlan'

// Màn thanh toán thành công
const PaymentSuccessPage = () => {
  // Lấy các tham số đ<PERSON>h kèm của URL
  const searchParams = useSearchParams()
  const serviceId = searchParams.get('serviceId')
  const actionType = searchParams.get('actionType')
  const cartCodeBill = searchParams.get('cartCodeBill')
  const cartCodeSub = searchParams.get('cartCodeSub')
  const action = searchParams.get('action')
  const metadata = searchParams.get('metadata')
  const subscriptionId = searchParams.get('subscriptionId')
  const lstSubId = searchParams.get('lstSubId')

  const getMetadata = (metadataStr: string | null) => {
    try {
      return metadataStr ? JSON.parse(metadataStr) : null
    } catch {
      return null
    }
  }

  const parsedMetadata = getMetadata(metadata)

  // region Các loại màn có thể có trong trang
  /** Màn kích hoạt lại */
  const isReactivate = ['3', '4'].includes(String(action))

  /** Màn gia hạn */
  const isRenew = String(actionType) === 'RENEW_SUBSCRIPTION'

  /** Màn đổi gói */
  const isPricingChange = action === '2'

  /** Màn thanh toán xuất hóa đơn */
  const isExportInvoice =
    (!cartCodeSub || cartCodeSub === 'null' || !cartCodeBill || cartCodeBill === 'null') &&
    !!serviceId &&
    serviceId !== 'null'

  // Các dịch vụ viễn thông
  const telcoService: TelcoService = parsedMetadata?.telcoService || ''
  const isTelcoService = ['eSIM', 'SIM', 'mobilePlan'].includes(telcoService)

  // endregion

  // Gọi API cập nhật sản phẩm trong giỏ hàng
  const updateCart = useMutation({
    mutationKey: ['updateCart'],
    mutationFn: ShoppingCart.updateCart,
    onMutate: (body: any) => {
      body.lstProduct.forEach((product: any) => {
        product.type = 1
      })
    },
    onSuccess: () => localStorage.removeItem('uncheckedProducts'),
    onError: () => message.error('Cập nhật thông tin giỏ hàng thất bại')
  })

  // Cập nhật sản phẩm chưa mua trong giỏ hàng
  useEffect(() => {
    const processPaymentTracking = () => {
      try {
        // Handle unchecked products
        const uncheckedProducts = localStorage.getItem('uncheckedProducts')

        if (uncheckedProducts) {
          updateCart.mutate(JSON.parse(uncheckedProducts))
        }

        // Handle Matomo tracking
        const matomoTrackingStr = Cookies.get('matomoTrackingData')

        if (matomoTrackingStr) {
          const trackingData = JSON.parse(matomoTrackingStr)

          // Kiểm tra cấu trúc dữ liệu mới
          if (trackingData && trackingData.items && Array.isArray(trackingData.items)) {
            // Đảm bảo mỗi item có đầy đủ thông tin cần thiết
            const validItems = trackingData.items.filter(
              (item: { productId: any; price: any; quantity: any }) =>
                item.productId && typeof item.price === 'number' && typeof item.quantity === 'number'
            )

            if (validItems.length > 0) {
              // Thêm flag để đánh dấu đã tracking
              if (!localStorage.getItem('matomoTrackingProcessed')) {
                trackPaymentSuccess(validItems)
                Cookies.remove('matomoTrackingData')

                // Đánh dấu đã tracking
                localStorage.setItem('matomoTrackingProcessed', 'true')

                // Log để debug
                console.log('Successfully tracked payment for items:', validItems)
                console.log('Total amount tracked:', trackingData.total)
              }
            }
          }
        }
      } catch (error) {
        console.error('Error processing payment tracking:', error)
      }
    }

    // Gọi hàm tracking
    processPaymentTracking()

    Cookies.remove('matomoUrl')
    Cookies.remove('_aff_network')
    Cookies.remove('_aff_sid')
    Cookies.remove('apinfo')
    Cookies.remove('APINFO')
    Cookies.remove('affiliate_link_code')

    // Cleanup function để xóa flag khi component unmount
    return () => {
      localStorage.removeItem('matomoTrackingProcessed')
    }
  })

  const updateStockByActionMutation = useMutation({
    mutationFn: (data: any) => warehouseInstance.updateStockByAction(data)
  })

  //Cập nhật thông tin số lượng trong kho
  useQuery({
    queryKey: ['getSubscriptionsByIds', lstSubId],
    queryFn: async () => {
      let subIdList = []

      subIdList = lstSubId
        ? lstSubId
            .split(',')
            .map(id => Number(id.trim()))
            .filter(id => !isNaN(id))
        : []

      if (subIdList.length > 0) {
        const res = await SmeSubscription.getSubscriptionsByIds(subIdList)

        const result = res.map((value: any) => ({
          serviceId: value.serviceId,
          variantId: value.variantId,
          action: 'PAYMENT_SUCCESS',
          quantity: value.quantity
        }))

        if (result.length > 0) updateStockByActionMutation.mutateAsync(result)

        return result
      }

      return []
    },
    enabled: !!lstSubId
  })

  return (
    <>
      <div className='flex h-screen w-screen items-center justify-center'>
        <div className='flex size-fit items-center justify-center'>
          <i className='onedx-dx size-30 bg-gradient-to-r from-[#00559D] to-[#0099D7]' />
          <i className='onedx-dx-other -ml-12 size-30 bg-gradient-to-r from-[#F47A2A] to-[#EFC65E]' />
        </div>
        <div className='text-[#2B68B2]'>
          <div className='title-56-bold'>Đang kiểm tra giao dịch</div>
          <div className='title-24-regular'>Vui lòng đợi trong giây lát...</div>
        </div>
      </div>
      <Modal
        width={400}
        title={!isTelcoService ? <i className='onedx-check-circle size-5 text-green-7' /> : undefined}
        closable={false}
        footer={null}
        open
        centered
      >
        {(() => {
          if (isTelcoService) return <SuccessPaymentTelco type={telcoService} orderId={subscriptionId} />
          if (isReactivate) return <SuccessReactivate />
          if (isRenew) return <SuccessRenew />
          if (isPricingChange) return <SuccessPricingChange />
          if (isExportInvoice) return <SuccessPaymentExportInvoice />

          return <SuccessPaymentService />
        })()}
      </Modal>
    </>
  )
}

export default PaymentSuccessPage
