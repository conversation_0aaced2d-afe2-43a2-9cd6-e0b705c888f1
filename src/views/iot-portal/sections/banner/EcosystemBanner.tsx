'use client'

import { useState } from 'react'

import Image from 'next/image'

import { But<PERSON> } from 'antd'

import EcosystemBannerImage from '@public/assets/images/pages/IoT/EcosystemBanner.svg'
import { ArrowRightIcon } from '@/assets/builder-icons'
import { SectionHeader } from '../section-item'
import ContactFormModal from '@/views/enterprise/contact-customer/ContactFormModal'

export const EcosystemBanner = () => {
  const [isModalVisible, setIsModalVisible] = useState(false)

  // region Return
  return (
    <div className='relative w-full'>
      <div className='flex h-[636px] w-full items-center justify-center'>
        <Image src={EcosystemBannerImage} alt='IoT Ecosystem' className='size-full' />
      </div>

      <div className='absolute top-8 w-full text-center'>
        <SectionHeader
          title='Hệ Sinh Thái IoT của VNPT'
          description={
            <div className='flex items-center justify-center'>
              <div className='title-32-semibold text-black'>Kết <PERSON>i <PERSON>ơng Lai Thông Qua&nbsp;</div>
              <span className='text-[#0A6FD0]'>Iot</span>
            </div>
          }
        />
      </div>

      <div className='absolute top-28 w-full px-4'>
        <p className='headline-16-regular mx-auto max-w-6xl text-center text-[#555f70]'>
          Hệ sinh thái IoT của VNPT tiên phong kết nối tương lai Việt Nam , trải dài trên nhiều lĩnh vực trọng yếu như
          nông nghiệp, y tế số, nhà thông minh, giao thông vận tải và nhiều ngành công nghiệp khác. Mục tiêu phát triển
          các giải pháp toàn diện dựa trên AI, Big Data, Cloud Computing, giúp tối ưu hóa, nâng cao hiệu quả và kiến tạo
          giá trị cho doanh nghiệp, cộng đồng trong tiến trình chuyển đổi số quốc gia.
        </p>
      </div>

      <div className='absolute top-64 flex w-full justify-center gap-4'>
        <Button type='primary' className='h-10 w-56 rounded-4xl'>
          <div className='body-14-medium'>Tìm hiểu về VNPT IoT</div>
          <ArrowRightIcon />
        </Button>
        <Button
          type='default'
          className='h-10 w-56 rounded-4xl border-[#0A6FD0]'
          onClick={() => setIsModalVisible(true)}
        >
          <div className='body-14-medium text-text-info-default'>Liên hệ với chúng tôi</div>
        </Button>
      </div>

      <ContactFormModal isModalVisible={isModalVisible} setIsModalVisible={setIsModalVisible} customerType='IOT' />
    </div>
  )
  // endregion
}
