// data/dataFake.ts
// types/solution.ts

export interface SolutionCategoryType {
  id: number
  icon: string
  title: string
  description: string
  active: boolean
}

export interface SolutionCardType {
  id: number
  title: string
  description: string
  image: any
  hasActionButton: boolean
}

export interface OutstandingSolution {
  id: number
  title: string
  description: string
  fullDescription: string
  icon: string
  image: string
}

export interface AdviseSolution {
  id: number
  icon: string
  title: string
  description: string
  titleDescription: string
  image: string
  hoverImage: string
}

export interface ComprehensiveIoTSolutionType {
  id: number
  image: string
}

export const solutionCategories: SolutionCategoryType[] = [
  {
    id: 1,
    icon: 'tree-mini',
    title: 'Nông Nghiệp',
    description: 'Gi<PERSON>i pháp IoT dành cho nông nghiệp thông minh',
    active: true
  },
  {
    id: 2,
    icon: 'smart-city',
    title: 'Smart City',
    description: 'Giải pháp IoT dành cho Thành Phố thông minh',
    active: false
  },
  {
    id: 3,
    icon: 'medical-health',
    title: 'Y tế, sức khỏe',
    description: 'Giải pháp IoT dành cho y tế chăm sóc sức khỏe',
    active: false
  },
  {
    id: 4,
    icon: 'smart-home',
    title: 'Nhà Thông Minh',
    description: 'Giải pháp IoT dành cho nhà thông minh',
    active: false
  },
  {
    id: 5,
    icon: 'transport-iot',
    title: 'Giao Thông Vận Tải',
    description: 'Giải pháp IoT dành cho giao thông vận tải',
    active: false
  },
  {
    id: 6,
    icon: 'tech-iot',
    title: 'Công nghiệp 4.0',
    description: 'Giải pháp IoT dành cho sản xuất',
    active: false
  }
]

export const solutionCards: SolutionCardType[] = [
  {
    id: 1,
    title: 'Quản lý hệ thống nước tưới',
    description: 'is a long established fact that a reader will be distracted by the readable content of a page when',
    image: '/assets/images/pages/IoT/Solution_1.png',
    hasActionButton: false
  },
  {
    id: 2,
    title: 'Quản lý xưởng sản xuất',
    description: 'is a long established fact that a reader will be distracted by the readable content of a page when',
    image: '/assets/images/pages/IoT/Solution_2.png',
    hasActionButton: false
  },
  {
    id: 3,
    title: 'Tổng hợp hệ sinh thái',
    description: 'is a long established fact that a reader will be distracted by the readable content of a page when',
    image: '/assets/images/pages/IoT/Solution_3.png',
    hasActionButton: true
  },
  {
    id: 4,
    title: 'Quản lý vận chuyển nông nghiệp',
    description: 'is a long established fact that a reader will be distracted by the readable content of a page when',
    image: '/assets/images/pages/IoT/Solution_4.png',
    hasActionButton: false
  },
  {
    id: 5,
    title: 'Quản lý vận chuyển nông nghiệp',
    description: 'is a long established fact that a reader will be distracted by the readable content of a page when',
    image: '/assets/images/pages/IoT/Solution_5.png',
    hasActionButton: false
  },
  {
    id: 6,
    title: 'Quản lý vận chuyển nông nghiệp',
    description: 'is a long established fact that a reader will be distracted by the readable content of a page when',
    image: '/assets/images/pages/IoT/Solution_6.png',
    hasActionButton: false
  },
  {
    id: 7,
    title: 'Quản lý vận chuyển nông nghiệp',
    description: 'is a long established fact that a reader will be distracted by the readable content of a page when',
    image: '/assets/images/pages/IoT/Solution_7.png',
    hasActionButton: false
  },
  {
    id: 8,
    title: 'Quản lý vận chuyển nông nghiệp',
    description: 'is a long established fact that a reader will be distracted by the readable content of a page when',
    image: '/assets/images/pages/IoT/Solution_8.png',
    hasActionButton: false
  }
]

export const outstandingSolutions: OutstandingSolution[] = [
  {
    id: 1,
    title: 'Chăm sóc sức khỏe thông minh',
    description: 'VNPT mang đến giải pháp IoT chăm sóc sức khỏe toàn diện và kết nối.',
    fullDescription:
      'VNPT ứng dụng IoT trong lĩnh vực y tế để mang đến các giải pháp đột phá, hướng tới chăm sóc sức khỏe chủ động và toàn diện. <br /><br /> Thiết bị đeo thông minh giúp theo dõi chỉ số sinh học; hệ thống bệnh viện thông minh tối ưu quy trình khám chữa bệnh; hồ sơ sức khỏe điện tử kết nối bác sĩ và người bệnh mọi lúc, mọi nơi.Các giải pháp chăm sóc từ xa hỗ trợ hiệu quả cho người cao tuổi và bệnh nhân mãn tính. VNPT IoT góp phần nâng cao chất lượng dịch vụ y tế, giảm tải cho bệnh viện và mang lại sự an tâm cho cộng đồng.',
    icon: 'medical-health',
    image: '/assets/images/pages/IoT/health-care.png'
  },
  {
    id: 2,
    title: 'Nông nghiệp thông minh',
    description: 'VNPT IoT giúp nhà nông tối ưu quy trình, tăng năng suất và chất lượng nông sản',
    fullDescription:
      'VNPT IoT mở ra một kỷ nguyên mới cho ngành nông nghiệp Việt Nam bằng các giải pháp toàn diện, từ theo dõi môi trường canh tác (nhiệt độ, độ ẩm, ánh sáng, dinh dưỡng đất) đến quản lý trang trại thông minh (tưới tiêu tự động, giám sát dịch bệnh, quản lý vật nuôi).<br /><br />Dữ liệu thu thập được phân tích chuyên sâu, hỗ trợ nhà nông đưa ra quyết định chính xác, tối ưu hóa nguồn lực, giảm thiểu chi phí sản xuất và nâng cao năng suất, chất lượng nông sản, hướng đến một nền nông nghiệp bền vững và hiệu quả.',
    icon: 'tree-mini',
    image: '/assets/images/pages/IoT/smart-agriculture.png'
  },
  {
    id: 3,
    title: 'Thành phố Thông minh',
    description: 'VNPT kiến tạo đô thị thông minh, nâng cao hiệu quả quản lý và chất lượng sống cư dân.',
    fullDescription:
      'VNPT là đối tác tin cậy trong xây dựng đô thị thông minh trên toàn quốc, mang đến các giải pháp IoT toàn diện và hiệu quả.\n' +
      'Giải pháp bao gồm: <br />' +
      '<ul> <li>Giao thông thông minh: điều khiển đèn tín hiệu, giám sát lưu lượng.</li> <li>Giám sát môi trường: chất lượng không khí, mực nước.</li> <li>Quản lý năng lượng: chiếu sáng thông minh.</li> <li>An ninh đô thị: giám sát camera, cảnh báo sớm.</li> <li>Dịch vụ công thông minh: quản lý chất thải, cấp thoát nước.</li> <li>Mục tiêu là kiến tạo môi trường sống hiện đại, an toàn và bền vững cho người dân và chính quyền đô thị.</li> </ul>',
    icon: 'smart-city',
    image: '/assets/images/pages/IoT/smart-city.png'
  },
  {
    id: 4,
    title: 'Nhà thông minh',
    description: 'VNPT biến ngôi nhà của bạn thành không gian sống tiện nghi, an toàn và hiện đại.',
    fullDescription:
      'VNPT Smart Home kiến tạo không gian sống hiện đại, tiện nghi và an toàn cho mọi gia đình. Với các thiết bị và giải pháp IoT, người dùng có thể dễ dàng điều khiển hệ thống chiếu sáng, điều hòa, rèm cửa, khóa cửa thông minh, hệ thống an ninh, và các thiết bị gia dụng khác từ xa thông qua điện thoại hoặc giọng nói. Các kịch bản tự động hóa giúp ngôi nhà phản ứng thông minh theo thói quen và nhu cầu của gia chủ, mang đến trải nghiệm sống thoải mái, tiết kiệm năng lượng và an tâm tuyệt đối.',
    icon: 'smart-home',
    image: '/assets/images/pages/IoT/smart-home.png'
  },
  {
    id: 5,
    title: 'Giao thông vận tải',
    description: 'VNPT ứng dụng IoT để giao thông thông suốt, an toàn và hiệu quả hơn.',
    fullDescription:
      'VNPT IoT đóng góp vào việc xây dựng hệ thống giao thông vận tải thông minh, hiệu quả và an toàn.<br/><br/> Các giải pháp bao gồm giám sát hành trình và quản lý đội xe, hệ thống thu phí tự động không dừng, hệ thống thông tin giao thông thời gian thực, quản lý bãi đỗ xe thông minh, và các ứng dụng hỗ trợ lái xe an toàn. Mục tiêu là giảm thiểu ùn tắc giao thông, tai nạn, ô nhiễm môi trường và tối ưu hóa luồng di chuyển hàng hóa và hành khách.',
    icon: 'transport-iot',
    image: '/assets/images/pages/IoT/transportation.png'
  },
  {
    id: 6,
    title: 'Công nghiệp 4.0',
    description: 'VNPT đồng hành cùng doanh nghiệp chuyển đổi số, bứt phá trong kỷ nguyên công nghiệp 4.0.',
    fullDescription:
      'VNPT đồng hành cùng các doanh nghiệp trong hành trình chuyển đổi số và ứng dụng các công nghệ của cuộc cách mạng công nghiệp 4.0.<br/><br/> Các giải pháp IoT cho công nghiệp bao gồm giám sát và điều khiển máy móc từ xa, thu thập và phân tích dữ liệu sản xuất, quản lý kho thông minh, bảo trì dự đoán, và các ứng dụng tự động hóa quy trình sản xuất. VNPT IoT giúp doanh nghiệp nâng cao hiệu suất, giảm chi phí, cải thiện chất lượng sản phẩm và tăng cường khả năng cạnh tranh trên thị trường.',
    icon: 'tech-iot',
    image: '/assets/images/pages/IoT/technology40.png'
  }
]

export const adviseSolutions: AdviseSolution[] = [
  {
    id: 1,
    icon: 'transport-iot',
    image: '/assets/images/pages/IoT/automative.svg',
    hoverImage: '/assets/images/pages/IoT/hover-solution-1.svg',
    title: 'Automotive',
    titleDescription: 'Safe Car',
    description:
      'Safe Car là giải pháp an toàn cho xe hơi, kết hợp công nghệ IoT, GPS, và bảo mật thông minh để giảm tai nạn, phòng chống trộm cắp, giám sát xe từ xa và tối ưu trải nghiệm lái xe.'
  },
  {
    id: 2,
    icon: 'transport-iot',
    image: '/assets/images/pages/IoT/manufacture.svg',
    hoverImage: '/assets/images/pages/IoT/hover-solution-2.svg',
    title: 'Sản Xuất',
    titleDescription: 'Giải pháp RCU',
    description:
      'RCU (Remote Control Unit) là bộ điều khiển trung tâm, giúp tự động quản lý ánh sáng, điều hòa, rèm cửa, nước nóng, và các thiết bị khác để tối ưu vận hành và tiết kiệm năng lượng.'
  },
  {
    id: 3,
    icon: 'transport-iot',
    image: '/assets/images/pages/IoT/nature_environment.svg',
    hoverImage: '/assets/images/pages/IoT/hover-solution-3.svg',
    title: 'Thiên Nhiên Môi Trường',
    titleDescription: 'Giám sát hồ đập',
    description: 'Đây là hệ thống giám sát tự động, theo thời gian thực giúp quản lý hồ đập hiệu quả'
  },
  {
    id: 4,
    icon: 'transport-iot',
    image: '/assets/images/pages/IoT/wearable_device.svg',
    hoverImage: '/assets/images/pages/IoT/hover-solution-4.svg',
    title: 'Thiết Bị Đeo',
    titleDescription: '',
    description:
      'Thiết bị đeo giám sát an toàn cho người cao tuổi & trẻ em là một giải pháp công nghệ ứng dụng IoT, GPS, AI và cảm biến thông minh giúp theo dõi định vị GPS, cảnh báo khẩn cấp và hỗ trợ chăm sóc từ xa'
  }
]

export const comprehensiveIoTSolution: ComprehensiveIoTSolutionType[] = [
  {
    id: 1,
    image: '/assets/images/pages/IoT/futuristic-landscape-dubai.png'
  },
  {
    id: 2,
    image: '/assets/images/pages/IoT/cityscape-with-digital-overlay.png'
  },
  {
    id: 3,
    image: '/assets/images/pages/IoT/futuristic-environmentally-friendly-city-with-green-spaces.png'
  },
  {
    id: 4,
    image: '/assets/images/pages/IoT/modern-city-concept-3d-illustration-form-circle.png'
  },
  {
    id: 5,
    image:
      '/assets/images/pages/IoT/futuristic-cityscape-with-modern-skyscrapers-against-traditional-indonesian-architecture.png'
  },
  {
    id: 6,
    image: '/assets/images/pages/IoT/futuristic-landscape-dubai-1.png'
  },
  {
    id: 7,
    image: '/assets/images/pages/IoT/high-tech-travel-future-time.png'
  }
]

export const CAPACITY_DATA = [
  {
    icon: 'onedx-shake-hand',
    title: '20 Đối tác',
    description: 'Đầu ngành uy tín',
    iconSize: 'size-6'
  },
  {
    icon: 'onedx-star-reward',
    title: '20 Dự án',
    description: 'Đã được triển khai',
    iconSize: 'size-6'
  },
  {
    icon: 'onedx-everyone-cover',
    title: '500 Cán bộ',
    description: 'Chuyên môn cao',
    iconSize: 'size-6'
  },
  {
    icon: 'onedx-many-star',
    title: '15 Năm Kinh Nghiệm',
    description: 'Trong lĩnh vực IoT',
    iconSize: 'size-4'
  }
] as const

export const solutionPioneerData = {
  breadcrumb: [
    { label: 'IoT', href: '/iot-portal', color: '#52A6FF' },
    { label: 'Y tế thông minh', href: '/iot-portal/healthcare' }
  ],
  title: 'Giải pháp toàn diện cho ngành',
  highlight: 'Y tế',
  highlightColor: '#52A6FF',
  description: `Ứng dụng IoT trong lĩnh vực y tế và sức khỏe của VNPT mang đến những giải pháp đột phá, hướng đến việc chăm sóc sức khỏe chủ động và toàn diện.
Các thiết bị đeo thông minh theo dõi các chỉ số sinh học, hệ thống quản lý bệnh viện thông minh tối ưu hóa quy trình khám chữa bệnh, hồ sơ sức khỏe điện tử kết nối người bệnh và bác sĩ mọi lúc mọi nơi, các giải pháp chăm sóc sức khỏe từ xa hỗ trợ người cao tuổi và bệnh mãn tính. VNPT IoT góp phần nâng cao chất lượng dịch vụ y tế, giảm tải cho bệnh viện và mang lại sự tiện lợi, an tâm cho người dân.`,
  banner: '/assets/images/pages/IoT/solution-list/medical-banner.png',
  filters: ['Ngoại khoa', 'Nội khoa', 'Tim mạch', 'Tiêu hoá', 'Thần kinh', 'Xương khớp'],
  solutions: [
    {
      image: '/assets/images/pages/IoT/solution-list/solution-medical-a.png',
      title: 'Giải pháp thông minh A',
      description: 'is a long established fact that a reader will be distracted by the readable content of a page when'
    },
    {
      image: '/assets/images/pages/IoT/solution-list/solution-medical-b.png',
      title: 'Giải pháp thông minh B',
      description: 'is a long established fact that a reader will be distracted by the readable content of a page when'
    },
    {
      image: '/assets/images/pages/IoT/solution-list/solution-medical-c.png',
      title: 'Giải pháp thông minh C',
      description: 'is a long established fact that a reader will be distracted by the readable content of a page when'
    },
    {
      image: '/assets/images/pages/IoT/solution-list/solution-medical-d.png',
      title: 'Giải pháp thông minh D',
      description: 'is a long established fact that a reader will be distracted by the readable content of a page when'
    },
    {
      image: '/assets/images/pages/IoT/solution-list/solution-medical-e.png',
      title: 'Giải pháp thông minh E',
      description: 'is a long established fact that a reader will be distracted by the readable content of a page when'
    },
    {
      image: '/assets/images/pages/IoT/solution-list/solution-medical-e.png',
      title: 'Giải pháp thông minh E',
      description: 'is a long established fact that a reader will be distracted by the readable content of a page when'
    }
  ]
}

export const solutionAgricultureData = {
  breadcrumb: [
    { label: 'IoT', href: '/iot-portal', color: '#84CC0B' },
    { label: 'Nông nghiệp thông minh', href: '/iot-portal/smart-agriculture' }
  ],
  title: 'Tương lai của nền',
  highlight: 'Nông nghiệp',
  highlightColor: '#84CC0B',
  description: `VNPT IoT mở ra một kỷ nguyên mới cho ngành nông nghiệp Việt Nam bằng các giải pháp toàn diện, từ theo dõi môi trường canh tác (nhiệt độ, độ ẩm, ánh sáng, dinh dưỡng đất) đến quản lý trang trại thông minh (tưới tiêu tự động, giám sát dịch bệnh, quản lý vật nuôi). Dữ liệu thu thập được phân tích chuyên sâu, hỗ trợ nhà nông đưa ra quyết định chính xác, tối ưu hóa nguồn lực, giảm thiểu chi phí sản xuất và nâng cao năng suất, chất lượng nông sản, hướng đến một nền nông nghiệp bền vững và hiệu quả.`,
  banner: '/assets/images/pages/IoT/solution-list/agriculture-banner.png',
  filters: ['Cây giống', 'Tưới tiêu', 'Nhà kính'],
  solutions: [
    {
      image: '/assets/images/pages/IoT/solution-list/solution-agriculture-a.png',
      title: 'Giải pháp thông minh A',
      description: 'is a long established fact that a reader will be distracted by the readable content of a page when'
    },
    {
      image: '/assets/images/pages/IoT/solution-list/solution-agriculture-b.png',
      title: 'Giải pháp thông minh B',
      description: 'is a long established fact that a reader will be distracted by the readable content of a page when'
    },
    {
      image: '/assets/images/pages/IoT/solution-list/solution-agriculture-c.png',
      title: 'Giải pháp thông minh C',
      description: 'is a long established fact that a reader will be distracted by the readable content of a page when'
    },
    {
      image: '/assets/images/pages/IoT/solution-list/solution-agriculture-d.png',
      title: 'Giải pháp thông minh D',
      description: 'is a long established fact that a reader will be distracted by the readable content of a page when'
    },
    {
      image: '/assets/images/pages/IoT/solution-list/solution-agriculture-e.png',
      title: 'Giải pháp thông minh E',
      description: 'is a long established fact that a reader will be distracted by the readable content of a page when'
    },
    {
      image: '/assets/images/pages/IoT/solution-list/solution-agriculture-e.png',
      title: 'Giải pháp thông minh E',
      description: 'is a long established fact that a reader will be distracted by the readable content of a page when'
    }
  ]
}
