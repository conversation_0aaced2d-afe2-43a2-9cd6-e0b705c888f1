'use client'

import { useEffect, useMemo, useState } from 'react'

import { useRouter } from 'next/navigation'

import Image from 'next/image'

import { Button } from 'antd'

import { outstandingSolutions } from '../data/dataFakeSolution'
import { ArrowRightIcon } from '@/assets/builder-icons'
import BackgroundOutstanding from '@public/assets/images/pages/IoT/bg_outstanding.png'
import ContactFormModal from '@/views/enterprise/contact-customer/ContactFormModal'

export enum OutstandingSolutionId {
  SMART_MEDICAL = 1,
  SMART_AGRICULTURE = 2,
  SMART_CITY = 3,
  SMART_HOME = 4,
  TRANSPORT = 5,
  INDUSTRY = 6
}

// Mapping từ id sang route danh sách lĩnh vực
const solutionListRoutes: Record<number, string> = {
  1: '/iot-portal/solution/smart-medical',
  2: '/iot-portal/solution/smart-agriculture',
  3: '/iot-portal/solution/smart-city',
  4: '/iot-portal/solution/smart-home',
  5: '/iot-portal/solution/transport',
  6: '/iot-portal/solution/industry'
}

interface OutstandingSolutionProps {
  title?: string | React.ReactNode
  subTitle?: string | React.ReactNode
  currentSolutionId?: OutstandingSolutionId
}

export const OutstandingSolution = ({
  title = 'Giải Pháp Nổi Bật',
  subTitle,
  currentSolutionId
}: OutstandingSolutionProps) => {
  const [activeId, setActiveId] = useState(outstandingSolutions[0]?.id || 1)
  const router = useRouter()
  const [isModalVisible, setIsModalVisible] = useState(false)

  const filteredSolutions = useMemo(
    () => outstandingSolutions.filter(solution => solution.id !== currentSolutionId),
    [currentSolutionId]
  )

  useEffect(() => {
    if (filteredSolutions.length > 0) {
      setActiveId(filteredSolutions[0]?.id || 1)
    }
  }, [filteredSolutions])

  const activeSolution = outstandingSolutions.find(solution => solution.id === activeId) || filteredSolutions[0]

  return (
    <div className='relative w-full py-16'>
      <div className='absolute inset-0 z-0'>
        <Image src={BackgroundOutstanding} alt='BackgroundOutstanding' className='size-full object-cover' priority />
      </div>
      <div className='container relative z-10 mx-auto'>
        <div className='mb-8 text-center'>
          <p className='headline-16-medium mb-2 mt-0 text-text-accent-lime'>{title}</p>
          <h2 className='text-title-32 font-bold text-white'>
            {subTitle || (
              <>
                Các Giải Pháp <span className='text-text-accent-lime'>IoT Nổi Bật</span> Của Chúng Tôi
              </>
            )}
          </h2>
          <p className='headline-16-regular mx-auto mt-4 text-[#D3E3FE]'>
            Tối ưu vận hành, tăng hiệu suất và đảm bảo an toàn với các giải pháp IoT tiên tiến.
          </p>
        </div>

        <div className='overflow-hidden rounded-2.5xl bg-white shadow-lg'>
          <div className='flex gap-6 p-6'>
            <div className='w-1/3 border-r border-gray-100'>
              {filteredSolutions.map(solution => (
                <div
                  key={solution.id}
                  className={`cursor-pointer rounded-3xl border-b border-gray-100 p-5 pl-3 transition-colors hover:bg-gray-50 ${activeId === solution.id ? 'bg-gray-100' : ''}`}
                  onClick={() => setActiveId(solution.id)}
                >
                  <div className='flex items-center gap-4'>
                    <div
                      className={`size-10 rounded-lg p-2 text-black ${activeId === solution.id ? 'bg-[#a5ff0e] text-black' : 'bg-gray-100 text-gray-600'}`}
                    >
                      <i className={`onedx-${solution.icon} size-6`} />
                    </div>
                    <h3 className='headline-20-regular text-black'>{solution.title}</h3>
                  </div>

                  {activeId === solution.id && (
                    <div
                      className='headline-16-regular mt-[6px] space-y-4 text-neutral-120'
                      dangerouslySetInnerHTML={{ __html: solution.description }}
                    />
                  )}
                </div>
              ))}
            </div>

            <div className='flex h-[563px] w-2/3 gap-6'>
              <div className='flex h-full w-[422px] flex-col'>
                <div className='flex h-full flex-col'>
                  <div>
                    <h3 className='title-32-medium text-black'>{activeSolution.title}</h3>
                    <div
                      className='headline-16-regular mt-6 space-y-[30px] text-neutral-120'
                      dangerouslySetInnerHTML={{ __html: activeSolution.fullDescription }}
                    />
                  </div>
                  <div className='mt-auto flex gap-4'>
                    <Button
                      type='primary'
                      className='h-10 w-56 rounded-4xl'
                      onClick={() => {
                        const route = solutionListRoutes[activeId]

                        if (route) router.push(route)
                      }}
                    >
                      <div className='body-14-medium'>Xem chi tiết</div>
                      <ArrowRightIcon />
                    </Button>
                    <Button
                      type='default'
                      className='h-10 w-56 rounded-4xl border-[#0A6FD0]'
                      onClick={() => setIsModalVisible(true)}
                    >
                      <div className='body-14-medium text-text-info-default'>Nhận tư vấn</div>
                    </Button>
                  </div>
                </div>
              </div>
              <div className='relative size-full rounded-2.5xl'>
                <Image src={activeSolution.image} alt={'image'} fill className='rounded-3xl object-cover' />
              </div>
            </div>
          </div>
        </div>
      </div>
      <ContactFormModal isModalVisible={isModalVisible} setIsModalVisible={setIsModalVisible} customerType='IOT' />
    </div>
  )
}
