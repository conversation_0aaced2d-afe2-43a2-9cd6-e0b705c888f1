'use client'

import React, { useState } from 'react'

import Image from 'next/image'

import { useRouter } from 'next/navigation'

import classNames from 'classnames'
import { Button } from 'antd'
import { ArrowRightOutlined } from '@ant-design/icons'

interface Category {
  id: string
  icon: string
  title: string
  shortDesc: string
  desc1?: string
  desc2?: string
  link: string
  image: string
}

const CATEGORY_LIST: Category[] = [
  {
    id: 'agriculture',
    icon: 'onedx-tree-mini',
    title: 'Nông nghiệp thông minh',
    shortDesc: 'VNPT IoT giúp nhà nông tối ưu quy trình, tăng năng suất và chất lượng nông sản',
    desc1:
      'VNPT IoT mở ra một kỷ nguyên mới cho ngành nông nghiệp Việt Nam bằng các giải pháp toàn diện, từ theo dõi môi trường canh tác (n<PERSON><PERSON><PERSON> đ<PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON> sáng, dinh dưỡng đất) đến quản lý trang tr<PERSON>i thông minh (tướ<PERSON> tiêu tự động, gi<PERSON><PERSON> sá<PERSON> dị<PERSON> b<PERSON>, quản lý vật nuôi).',
    desc2:
      'Dữ liệu thu thập được phân tích chuyên sâu, hỗ trợ nhà nông đưa ra quyết định chính xác, tối ưu hóa nguồn lực, giảm thiểu chi phí sản xuất và nâng cao năng suất, chất lượng nông sản, hướng đến một nền nông nghiệp bền vững và hiệu quả.',
    link: '/iot-portal/solution/smart-agriculture',
    image: '/assets/images/pages/IoT/smart-agriculture.png'
  },
  {
    id: 'smart-city',
    icon: 'onedx-smart-city',
    title: 'Thành phố Thông minh',
    shortDesc: 'VNPT kiến tạo đô thị thông minh, nâng cao hiệu quả quản lý và chất lượng sống cư dân.',
    desc1: 'VNPT đóng vai trò là đối tác tin cậy trong hành trình xây dựng các đô thị thông minh trên khắp cả nước.',
    desc2:
      'Các giải pháp IoT của VNPT tích hợp đa dạng lĩnh vực, bao gồm quản lý giao thông thông minh (điều khiển đèn tín hiệu, giám sát lưu lượng), giám sát môi trường (chất lượng không khí, mực nước), quản lý năng lượng (chiếu sáng thông minh), an ninh đô thị (giám sát camera, cảnh báo), và các dịch vụ công thông minh (quản lý chất thải, cấp thoát nước). Mục tiêu là tạo ra một môi trường sống tiện nghi, an toàn, hiệu quả và bền vững cho người dân và chính quyền đô thị.',
    link: '/iot-portal/solution/smart-city',
    image: '/assets/images/pages/IoT/smart-city.png'
  },
  {
    id: 'smart-medical',
    icon: 'onedx-medical-health',
    title: 'Y tế chăm sóc sức khoẻ',
    shortDesc: 'VNPT mang đến giải pháp IoT chăm sóc sức khỏe toàn diện và kết nối.',
    desc1:
      'Ứng dụng IoT trong lĩnh vực y tế và sức khỏe của VNPT mang đến những giải pháp đột phá, hướng đến việc chăm sóc sức khỏe chủ động và toàn diện.',
    desc2:
      'Các thiết bị đeo thông minh theo dõi các chỉ số sinh học, hệ thống quản lý bệnh viện thông minh tối ưu hóa quy trình khám chữa bệnh, hồ sơ sức khỏe điện tử kết nối người bệnh và bác sĩ mọi lúc mọi nơi, các giải pháp chăm sóc sức khỏe từ xa hỗ trợ người cao tuổi và bệnh mãn tính. VNPT IoT góp phần nâng cao chất lượng dịch vụ y tế, giảm tải cho bệnh viện và mang lại sự tiện lợi, an tâm cho người dân.',
    link: '/iot-portal/solution/smart-medical',
    image: '/assets/images/pages/IoT/health-care.png'
  },
  {
    id: 'transport',
    icon: 'onedx-transport-iot',
    title: 'Giao thông vận tải',
    shortDesc: 'VNPT ứng dụng IoT để giao thông thông suốt, an toàn và hiệu quả hơn.',
    desc1: 'VNPT IoT đóng góp vào việc xây dựng hệ thống giao thông vận tải thông minh, hiệu quả và an toàn.',
    desc2:
      'Các giải pháp bao gồm giám sát hành trình và quản lý đội xe, hệ thống thu phí tự động không dừng, hệ thống thông tin giao thông thời gian thực, quản lý bãi đỗ xe thông minh, và các ứng dụng hỗ trợ lái xe an toàn. Mục tiêu là giảm thiểu ùn tắc giao thông, tai nạn, ô nhiễm môi trường và tối ưu hóa luồng di chuyển hàng hóa và hành khách.',
    link: '/iot-portal/solution/transport',
    image: '/assets/images/pages/IoT/transportation.png'
  },
  {
    id: 'industry',
    icon: 'onedx-tech-iot',
    title: 'Cộng nghiệp 4.0',
    shortDesc: 'VNPT đồng hành cùng doanh nghiệp chuyển đổi số, bứt phá trong kỷ nguyên công nghiệp 4.0.',
    desc1:
      'VNPT đồng hành cùng các doanh nghiệp trong hành trình chuyển đổi số và ứng dụng các công nghệ của cuộc cách mạng công nghiệp 4.0.',
    desc2:
      'Các giải pháp IoT cho công nghiệp bao gồm giám sát và điều khiển máy móc từ xa, thu thập và phân tích dữ liệu sản xuất, quản lý kho thông minh, bảo trì dự đoán, và các ứng dụng tự động hóa quy trình sản xuất. VNPT IoT giúp doanh nghiệp nâng cao hiệu suất, giảm chi phí, cải thiện chất lượng sản phẩm và tăng cường khả năng cạnh tranh trên thị trường.',
    link: '/iot-portal/solution/industry',
    image: '/assets/images/pages/IoT/technology40.png'
  }
]

interface SmartHomeCategoryProps {
  onContactClick?: () => void
}

export default function SmartHomeCategory({ onContactClick }: SmartHomeCategoryProps) {
  const [activeCategory, setActiveCategory] = useState(CATEGORY_LIST[0])
  const router = useRouter()

  return (
    <div className='container mx-auto mt-[60px]'>
      <div className='title-16-medium text-center text-text-info-default'>Khám phá thêm</div>
      <h2 className='title-32-bold mt-2 text-center'>
        Các Giải Pháp <span className='text-text-info-default'>IoT Nổi Bật Khác</span> Của VNPT
      </h2>
      <div className='headline-16-regular mt-6 text-center text-text-on-brights'>
        Khám phá các giải pháp IoT tiên tiến giúp tối ưu hóa hoạt động, cải thiện an toàn và tăng cường hiệu suất cho
        doanh nghiệp của bạn
      </div>

      {/* Smart Home Category */}
      <div className='mt-10 flex h-[611px] gap-3 rounded-[20px] border border-solid border-border-neutral-strong p-6'>
        <div className='w-[400px] shrink-0 space-y-[42px]'>
          {CATEGORY_LIST.map(category => (
            <div
              key={category.id}
              className={classNames(
                'cursor-pointer px-3 transition-all duration-300 ease-out hover:scale-[1.02]',
                activeCategory.id === category.id && 'bg-bg-surface-secondary py-6 rounded-[20px]'
              )}
              onClick={() => setActiveCategory(category)}
            >
              <div className='flex items-center gap-4 text-black'>
                <div
                  className={classNames(
                    'flex size-[40px] items-center justify-center rounded-[8px] transition-all duration-300 ease-out',
                    activeCategory.id === category.id
                      ? 'bg-text-accent-lime scale-110'
                      : 'bg-neutral-50 hover:bg-neutral-100'
                  )}
                >
                  <i className={classNames('size-[26px] transition-all duration-300 ease-out', category.icon)} />
                </div>
                <div className='headline-20-regular transition-all duration-300 ease-out'>{category.title}</div>
              </div>
              <div
                className={classNames(
                  'headline-16-regular mt-[6px] text-neutral-120 overflow-hidden transition-all duration-300 ease-out',
                  activeCategory.id === category.id ? 'max-h-[100px] opacity-100' : 'max-h-0 opacity-0'
                )}
              >
                {category.shortDesc}
              </div>
            </div>
          ))}
        </div>
        <div className='w-0 self-stretch border-l border-dashed border-border-neutral-strong' />
        <div className='flex w-full gap-6'>
          <div className='flex w-[422px] flex-col justify-between gap-32'>
            <div className='transition-all duration-300 ease-out'>
              <div className='title-32-medium text-black transition-all duration-300 ease-out'>
                {activeCategory.title}
              </div>
              <div className='headline-16-regular mt-6 text-neutral-120 transition-all duration-300 ease-out'>
                {activeCategory.desc1}
              </div>
              {activeCategory.desc2 && (
                <div className='headline-16-regular mt-7 text-neutral-120 transition-all duration-300 ease-out'>
                  {activeCategory.desc2}
                </div>
              )}
            </div>
            <div className='flex items-center justify-between gap-6'>
              <Button
                type='primary'
                icon={<ArrowRightOutlined />}
                iconPosition='end'
                className='body-14-medium h-10 w-1/2 rounded-full transition-all duration-300 ease-out hover:scale-105'
                onClick={() => router.push(activeCategory.link)}
              >
                Xem chi tiết
              </Button>
              <Button
                type='default'
                className='h-10 w-1/2 rounded-full transition-all duration-300 ease-out hover:scale-105'
                color='primary'
                variant='outlined'
                onClick={onContactClick}
              >
                Nhận tư vấn
              </Button>
            </div>
          </div>
          <div className='relative h-full flex-1 transition-all duration-300 ease-out hover:scale-[1.02]'>
            <Image
              src={activeCategory.image}
              alt='Smart Home Category'
              fill
              className='rounded-[20px] object-cover transition-all duration-300 ease-out'
            />
          </div>
        </div>
      </div>
    </div>
  )
}
