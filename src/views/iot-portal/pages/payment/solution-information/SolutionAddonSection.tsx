import React, { useEffect, useState } from 'react'

import { useSearchParams } from 'next/navigation'

import { useDispatch, useSelector } from 'react-redux'
import { useQuery, useMutation } from '@tanstack/react-query'
import { <PERSON><PERSON>, Divider } from 'antd'

import ModalSelectAddonByPackage from '@/components/modal/ModalSelectAddonByPackage'
import Addon from '@components/addon/Addon'
import type { AddonPackage } from '@/types/bundling'
import { getAddonToDataCalculate } from '@/views/payment/utils/serviceUtils'
import { convertAddons } from '@/views/enterprise/convert'
import SolutionInstance from '@/models/Solution'
import smeSubscriptionInstance from '@/models/SmeSubscription'
import { PRICING_PLAN } from '@/views/payment/constant/PaymentConstant'
import useBundling from '@/views/payment/hooks/useBundling'
import {
  handleCalculateAddon,
  handleUpdateQuantityAddon,
  selectAddonList,
  selectCountCalAddon,
  setAddons
} from '@/redux-store/slices/paymentBundlingSlice'
import { getSelectedAddons } from '@/utils/iot-portal/payment'

const SolutionAddonSection = () => {
  const dispatch = useDispatch()
  const searchParams = useSearchParams()
  const packageId = searchParams.get('packageId')
  const addons = useSelector(selectAddonList)
  const selectedAddons = getSelectedAddons(addons)
  const countCalAddon = useSelector(selectCountCalAddon)
  const { refetchCalculateBundling } = useBundling()

  // state đóng/mở modal chọn addon
  const [isOpenChooseAddon, setIsOpenChooseAddon] = useState(false)

  // Lấy danh sách addon theo packageId
  const { data: addonList } = useQuery<AddonPackage[]>({
    queryKey: ['getAddonsByPackageId', packageId],
    queryFn: async () => {
      const res: AddonPackage[] = await SolutionInstance.getAddonByPackageId(Number(packageId))

      dispatch(setAddons(convertAddons(res.flatMap(item => item.lstAddon))))

      return res
    },
    initialData: [],
    enabled: !!packageId
  })

  // Xử lý tăng giảm số lượng add-on
  const onChangeQuantityAddon = (data: any) => {
    dispatch(handleUpdateQuantityAddon(data))
  }

  // Xử lý button "Xác nhận" trong modal chọn add-on
  const handleAddons = () => {
    // Đóng modal
    setIsOpenChooseAddon(false)
    // Gọi api tính toán lại tiền (calculate-cart)
    refetchCalculateBundling()
  }

  // Mutation cho việc tính toán add-on
  const mutationCalculateAddon = useMutation<any, Error, any>({
    mutationFn: smeSubscriptionInstance.getCalculateAddonByPackage,
    onSuccess: response => {
      // Gộp tất cả các add-on trong response thành một mảng phẳng duy nhất.
      // Mỗi phần tử trong response chứa một mảng lstCalculateAddon riêng, flatMap sẽ hợp nhất tất cả lại.
      const lstCalculateAddon = response.flatMap((item: any) => item.lstCalculateAddon)

      dispatch(handleCalculateAddon(lstCalculateAddon))

      // Nếu addon đã hiển thị ngoài modal thì gọi api calculate-cart cập nhật trực tiếp giá
      if (!isOpenChooseAddon) refetchCalculateBundling()
    }
  })

  // gọi api calculate/addon khi thay đổi số lượng addon
  useEffect(() => {
    if (countCalAddon !== 0) {
      const dataToCalculate = getAddonToDataCalculate(selectedAddons)

      mutationCalculateAddon.mutate(dataToCalculate)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [countCalAddon])

  // region return
  return (
    <>
      {!!selectedAddons?.length && (
        <div>
          <div className='flex h-0 w-full items-center justify-center'>
            <div className='h-0 w-full border border-very-dark-green' />
          </div>
          <div className='max-h-96 w-full overflow-y-scroll'>
            {selectedAddons?.map((addon: any) => (
              <React.Fragment key={addon.id}>
                <Addon
                  {...addon}
                  hideUnitPriceIcon
                  isRequired='YES'
                  handleChangeQuantityAddon={onChangeQuantityAddon}
                  disabledState={{
                    decrease:
                      (addon.pricingPlan === PRICING_PLAN.FLAT_RATE && addon.quantity <= 1) ||
                      addon.quantity === addon.minimumQuantity,
                    input: addon.pricingPlan === PRICING_PLAN.FLAT_RATE && addon.quantity <= 1
                  }}
                />
              </React.Fragment>
            ))}
          </div>
        </div>
      )}
      {addonList?.length !== 0 && (
        <div className='py-3'>
          <Divider
            style={{
              margin: '0 0',
              borderTop: 'none',
              height: '1px',
              background:
                'repeating-linear-gradient(to right, #02173C17 0, #02173C17 10px, transparent 10px, transparent 20px)'
            }}
          />
          <Button
            type='primary'
            icon={<i className='onedx-add size-5' />}
            onClick={() => {
              setIsOpenChooseAddon(true)
            }}
            className='mt-4'
          >
            Thêm add-on
          </Button>
        </div>
      )}
      <ModalSelectAddonByPackage
        visibleAddon={isOpenChooseAddon}
        handleCloseSelectAddon={() => setIsOpenChooseAddon(false)}
        handleOkAddon={handleAddons}
        isLoadingCalculate={mutationCalculateAddon.isPending}
        addonList={addonList}
      />
    </>
  )
}

// endregion

export default SolutionAddonSection
