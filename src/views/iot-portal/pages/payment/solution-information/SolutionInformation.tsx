import React, { useMemo } from 'react'

import { useSearchParams } from 'next/navigation'

import { useSelector } from 'react-redux'
import classNames from 'classnames'

import { handleSrcImg } from '@/utils/string'
import smeSubscriptionInstance from '@/models/SmeSubscription'
import CardVoucher from '@/views/payment/components/voucher/CardVoucher'
import { IN_BUNDLING } from '@/views/payment/constant/PaymentConstant'
import useBundling from '@/views/payment/hooks/useBundling'
import { selectPaymentResponse } from '@/redux-store/slices/paymentBundlingSlice'
import SolutionAddonSection from './SolutionAddonSection'
import {
  isArrEmpty,
  renderBillSummary,
  renderCalculatePrice,
  renderDashedDivider,
  renderPrice,
  renderQuantity,
  renderServiceOrDevice,
  renderTitle
} from './SolutionInformationRender'

export const SolutionInformation = () => {
  // Lấy object chứa các tham số trên URL
  const searchParams = useSearchParams()
  // Lấy giá trị packageId từ URL
  // const packageId = searchParams.get('packageId')
  const solutionId = searchParams.get('solutionId')

  // check sme/iot portal
  const isIotPortal = searchParams.get('portal') === 'iot' // iot/sme

  // Lấy dữ liệu thanh toán hiện tại từ Redux store
  const paymentResponse = useSelector(selectPaymentResponse)

  // Lấy hàm cập nhật khuyến mại và bundlingDetail từ custom hook
  const { handleUpdatePromotion, bundlingDetail } = useBundling()

  // Xác định có hiển thị section khuyến mại không, dựa vào couponObject trong paymentResponse
  const showCoupon = useMemo(
    () =>
      paymentResponse?.couponObject?.lstItem?.some(
        (coupon: any) =>
          !isArrEmpty(coupon?.addons) || isArrEmpty(coupon?.pricingCoupons) || isArrEmpty(coupon?.serviceCoupons)
      ),
    [paymentResponse]
  )

  // Lấy danh sách các loại phí khác từ tất cả các subscription
  const otherFeeList = paymentResponse?.lstSubscriptionResponse?.map((item: any) => item?.otherFeeList).flat()

  // Hàm cập nhật lại feeObject bằng cách cộng thêm các loại phí khác vào tổng tiền và cập nhật danh sách item
  const updateFeeObject = (otherFee: { name: string; feeAmount: number }[], initFeeObject: any) => {
    if (!Array.isArray(otherFee)) {
      return initFeeObject
    }

    const totalFeeAmount: number = otherFee.reduce((sum, item) => sum + (item?.feeAmount || 0), 0)

    const feeObject = {
      ...initFeeObject,
      totalAmount: initFeeObject.totalAmount + totalFeeAmount,
      lstItem: initFeeObject.lstItem.map((item: any, index: any) =>
        index === initFeeObject.lstItem.length - 1 ? { ...item, otherFeeList: otherFee } : item
      )
    }

    return feeObject
  }

  // Đối tượng phí cuối cùng sau khi đã cộng thêm các loại phí khác
  const finalFeeObject = updateFeeObject(otherFeeList, paymentResponse?.feeObject)

  // Kiểm tra hóa đơn có rỗng không (không có phí, thuế, khuyến mại)
  const isEmpty = useMemo(
    () =>
      paymentResponse?.feeObject?.totalAmount === 0 &&
      paymentResponse?.taxObject?.totalAmount === 0 &&
      paymentResponse?.couponObject?.totalAmount === 0,
    [paymentResponse]
  )

  // Tạo mảng addons mặc định từ lstDevice và lstService
  const allAddons = useMemo(() => {
    const deviceAddons = bundlingDetail?.lstDevice?.flatMap(device => device?.addons || []) || []
    const serviceAddons = bundlingDetail?.lstService?.flatMap(service => service?.addons || []) || []

    return [...deviceAddons, ...serviceAddons]
  }, [bundlingDetail?.lstDevice, bundlingDetail?.lstService])

  // region return
  return (
    <>
      <div>
        {/* Tiêu đề */}
        <div className='flex items-center justify-start gap-2 pb-3'>
          <div className='h-4 w-0.5 bg-sme-orange-7' />
          <div className='text-sm font-semibold leading-tight tracking-tight text-gray-8'>Sản phẩm</div>
        </div>

        {/* Hiển thị danh sách dịch vụ */}
        <div className='space-y-2 rounded-lg'>
          <div className='rounded-lg'>
            <div className='w-full space-y-1 rounded-lg bg-white px-4'>
              {/* Hiển thị thông tin giải pháp */}
              <div
                className={classNames(
                  'flex items-center gap-2 py-3',
                  isIotPortal && 'border-b border-solid border-border-neutral-lighter'
                )}
              >
                {/* Avatar solution */}
                <img
                  alt={solutionId ? bundlingDetail?.solutionName : bundlingDetail?.name}
                  src={handleSrcImg(solutionId ? bundlingDetail?.solutionIconUrl : bundlingDetail?.iconUrl)}
                  className='size-12 rounded-lg object-cover'
                />
                <div className='space-y-1'>
                  {/* Tên giải pháp */}
                  <div className='headline-16-semibold text-text-primary-strong-hover'>
                    {solutionId ? bundlingDetail?.solutionName : bundlingDetail?.name}
                  </div>
                  {/* Nhà cung cấp */}
                  <div className='caption-12-regular text-text-neutral-medium'>{bundlingDetail?.providerName}</div>
                </div>
              </div>

              <div className={classNames(!isIotPortal && 'py-2 px-4 rounded-3xl bg-bg-neutral-lightest')}>
                {/* Tiêu đề, avatar, tên Dịch vụ */}
                <div className='space-y-2'>
                  <div>
                    {renderTitle(solutionId ? bundlingDetail?.name || '' : 'Giá sản phẩm')}
                    <div className='mt-2 flex items-center gap-8'>
                      {/*  Hiển thị thông tin số lượng dịch vụ/thiết bị trong bundling */}
                      {/* Ẩn thông tin dịch vụ/thiết bị khi số lượng bằng 0 */}
                      <div className='body-14-medium flex shrink grow basis-0 text-text-neutral-light'>
                        {[
                          (bundlingDetail?.serviceQuantity as number) > 0 &&
                            `${bundlingDetail?.serviceQuantity} dịch vụ`,
                          (bundlingDetail?.deviceQuantity as number) > 0 && `${bundlingDetail?.deviceQuantity} thiết bị`
                        ]
                          .filter(Boolean)
                          .join(', ')}
                      </div>
                      <div className='flex items-center gap-2'>
                        {/* Giá */}
                        {renderPrice(bundlingDetail?.priceFrom)}
                      </div>

                      {renderQuantity(1)}

                      <div className='flex w-16 items-center justify-end'>
                        {/* Render giá sau khi tính toán */}
                        {renderCalculatePrice(bundlingDetail?.priceFrom)}
                      </div>
                    </div>
                  </div>
                  {/* Khuyến mại */}
                  <div>
                    <CardVoucher
                      type={IN_BUNDLING}
                      promotionFn={smeSubscriptionInstance.getCouponBundling}
                      pricingPackage={bundlingDetail}
                      handleUpdatePromotion={handleUpdatePromotion}
                    />
                  </div>

                  {renderDashedDivider()}
                </div>

                {/* Thiết bị */}
                {bundlingDetail?.lstDevice?.map(device => renderServiceOrDevice(device))}

                {/* Dịch vụ */}
                {bundlingDetail?.lstService?.map(service => renderServiceOrDevice(service))}

                {/* Addon mặc định */}
                {/* Addon từ lstDevice và lstService */}
                {allAddons.length !== 0 && (
                  <div className='space-y-5 py-3'>
                    {allAddons.map(item => (
                      <div key={item?.id || item?.addonName} className='flex items-center gap-8'>
                        <div className='flex shrink grow basis-0 text-text-neutral-medium'>{item?.addonName}</div>
                        <div className='flex items-center gap-2'>
                          {/* Giá */}
                          {renderPrice(item?.priceUpdate)}
                        </div>

                        {renderQuantity(item?.quantity || 1)}

                        <div className='flex w-16 items-center justify-end'>
                          {/* Render giá sau khi tính toán */}
                          {renderCalculatePrice(item?.totalPreTaxFeeAmount)}
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                {/* Button Thêm Addons */}
                <SolutionAddonSection />
              </div>

              {/* Hiển thị thông tin hóa đơn cho SME Portal */}
              {!isIotPortal && <div>{renderBillSummary(paymentResponse, finalFeeObject, showCoupon, isEmpty)}</div>}
            </div>
          </div>
        </div>
      </div>

      {/* Section tổng hóa đơn cho IoT Portal */}
      {isIotPortal && (
        <div className='w-full'>
          <div className='flex items-center gap-2 pb-3'>
            <div className='h-4 w-0.5 bg-icon-primary-sme-orange-default' />
            <div className='body-14-medium text-text-neutral-medium'>Tổng hóa đơn</div>
          </div>
          {renderBillSummary(paymentResponse, finalFeeObject, showCoupon, isEmpty)}
        </div>
      )}
    </>
  )
  // endregion return
}
