'use client'
import { useEffect } from 'react'

import { useParams, usePathname, useRouter } from 'next/navigation'

import dynamic from 'next/dynamic'

import { useQuery } from '@tanstack/react-query'

import { Breadcrumb, ConfigProvider, Spin } from 'antd'

import { VIEWED_PRODUCT } from '@/constants/products'
import LocalStateProvider from '@/context/LocalStateContext'
import { useUser, useLocalState } from '@/hooks'
import ProductInstance from '@/models/Product'
import ServiceGroup from '@/models/ServiceGroup'
import { productType } from '@views/service-management/service-detail/ServiceDetail'

import ContactCustomer from '../enterprise/contact-customer/ContactCustomer'
import DefaultFeature from '../service-management/service-detail/components/DefaultFeature'
import AffixPayment from './components/AffixPayment'
import GroupHeader from './components/GroupHeader'
import ServiceGroupDetail from './components/ServiceGroupDetail'
import ServiceGroupList from './components/ServiceGroupList'
import SuggestedService from './components/SuggestedService'

import { themeAntd } from '@/configs/themeConfig'
import { handlePlanUrl } from '@/constants/commonFunction'
import { converter, convertRenderData } from './utils'

const PopupController = dynamic(() => import('./PopupController'))

const ServiceGroupContainer = () => {
  // get id in url
  const params = useParams()
  const router = useRouter()
  const pathName = usePathname()

  const { user, isLoggedIn } = useUser()
  const { convertSuggestedService } = converter
  const { setFieldsValue, state, isInitialized } = useLocalState()

  const { data: res, isRefetching } = useQuery({
    queryKey: ['getServiceGroup', params?.id],
    queryFn: async () => {
      const res = await ServiceGroup.getOneServiceGroup(params.id)

      return res
    },
    enabled: isInitialized
  })

  useEffect(() => {
    handlePlanUrl(pathName, res?.name, router)
  })

  useEffect(() => {
    if (res) {
      const convertData = convertRenderData(res, user)

      setFieldsValue(convertData)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [res, user])

  const { data: serviceViewed } = useQuery({
    queryKey: ['getServiceViewed'],
    queryFn: async () => {
      const res = await ProductInstance.getServiceView({
        serviceIgnoreId: params.id,
        type: VIEWED_PRODUCT.GROUP_SERVICE
      })

      return convertSuggestedService(res)
    },
    enabled: !!params.id && isInitialized && isLoggedIn
  })

  return (
    <ConfigProvider theme={themeAntd}>
      <Spin spinning={isRefetching}>
        <div className='transition-content relative flex flex-col bg-bg-surface'>
          {/* Breadcrums */}
          <Breadcrumb items={state.breadcrumb} className='max-w-[1280px] p-4 sm:hidden' />

          {/* header */}
          <GroupHeader />

          {/* content */}
          <div className='container mx-auto flex flex-col gap-10 py-7 sm:gap-4 sm:px-4 sm:py-6'>
            {/* Sản phẩm trong nhóm dịch vụ */}
            <ServiceGroupList />
            {/* Thông tin chi tiết */}
            <ServiceGroupDetail />
          </div>

          {/* Tính năng mặc định hiển thị */}
          <DefaultFeature type={productType.SERVICE_GROUP} />

          {/*  Sản phẩm đã xem - Sản phẩm liên quan */}
          <SuggestedService serviceGroupId={state?.serviceGroup?.id} viewedService={serviceViewed} />

          {/* Footer - Liên hệ bán hàng */}
          <ContactCustomer />

          {/* Affix - Thanh toán */}
          <AffixPayment />
        </div>
        <PopupController /> {/* popup, drawer... */}
      </Spin>
    </ConfigProvider>
  )
}

// HOCs
const ServiceGroupWrapper = () => {
  return (
    <LocalStateProvider
      initialState={{
        breadcrumb: [
          {
            title: 'Trang chủ',
            href: '/enterprise'
          },
          {
            title: 'Sản phẩm',
            href: '/enterprise/combos'
          },
          {
            title: 'Nhóm dịch vụ'
          }
        ]
      }}
    >
      <ServiceGroupContainer />
    </LocalStateProvider>
  )
}

export default ServiceGroupWrapper
