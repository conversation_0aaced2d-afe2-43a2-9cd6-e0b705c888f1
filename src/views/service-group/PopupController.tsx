import React, { useMemo } from 'react'

import { usePathname, useRouter } from 'next/navigation'

import Image from 'next/image'

import { Drawer } from 'antd'

import ConfirmModal from '@/components/modal/ConfirmModal'
import { useResponsive, useLocalState } from '@/hooks'
import { handleLoaderImg } from '@/utils/string'
import { formatCurrency } from '@/constants/commonFunction'

const PopupController = () => {
  const router = useRouter()
  const { state, setFieldValue } = useLocalState()
  const currentPath = usePathname()
  const { isMobile } = useResponsive()

  const drawerData = useMemo(() => state.PopupController?.ItemDrawer?.data, [state.PopupController?.ItemDrawer?.data])

  const setIsConfirmLogin = (value: any) => {
    setFieldValue('PopupLogin', { visible: value })
  }

  const setOpenDrawer = (value: any) => {
    setFieldValue('PopupController.ItemDrawer', { open: value })
  }

  return (
    <>
      <ConfirmModal
        width={384}
        btnText='Đăng nhập'
        modalTitle={<i className='onedx-dialog size-10' />}
        open={state.PopupLogin?.visible}
        setOpen={setIsConfirmLogin}
        onClickCancel={() => setIsConfirmLogin(false)}
        onClick={() => {
          router.push(`/sme-portal/login?redirect=${encodeURIComponent(currentPath)}`)
        }}
        description={
          <>
            <div className='text-headline-16 font-semibold text-text-neutral-strong'>Xin chào</div>
            <div className='text-body-14 font-normal'>
              Vui lòng đăng nhập hoặc{' '}
              <span
                className='cursor-pointer text-primary underline'
                onClick={() => {
                  router.push('/sme-portal/register')
                }}
              >
                Đăng ký
              </span>{' '}
              để tiếp tục mua dịch vụ
            </div>
          </>
        }
        btnCancel='Đóng'
      />
      {isMobile && (
        <Drawer
          title={
            <div className='flex items-stretch justify-between'>
              <div className='text-headline-16 font-semibold'>Thông tin sản phẩm</div>
              <div className='flex size-6 cursor-pointer'>
                <i
                  className='onedx-close-icon size-6 text-icon-neutral-medium'
                  onClick={() => {
                    setOpenDrawer(false)
                  }}
                />
              </div>
            </div>
          }
          placement={state.PopupController?.ItemDrawer?.placement || 'bottom'}
          closable={false}
          onClose={() => setOpenDrawer(false)}
          open={state.PopupController?.ItemDrawer?.open}
          key='ItemDrawer'
          loading={!drawerData}
          footer={
            <div className='flex justify-between pb-2'>
              <div className='text-body-14 font-semibold'>Thành tiền</div>
              <div className='flex items-stretch text-text-success-strong'>
                <span className='text-caption-12'>₫</span>
                <span className='text-headline-20 font-semibold'>{formatCurrency(drawerData?.total)}</span>
              </div>
            </div>
          }
          {...state.PopupController?.ItemDrawer?.drawerProps}
        >
          <div className='flex flex-col gap-2'>
            <div className=' flex w-full flex-col gap-3 rounded-xl bg-bg-neutral-lightest px-4 py-3'>
              <div className='flex items-stretch gap-2'>
                {drawerData ? (
                  <Image
                    className='rounded-lg bg-bg-neutral-lightest'
                    loader={handleLoaderImg}
                    src={drawerData?.images?.[0] ?? ''}
                    width={64}
                    height={64}
                    alt='product'
                    style={{
                      objectFit: 'contain'
                    }}
                  />
                ) : (
                  <div className='size-16 rounded-lg bg-bg-neutral-lightest'></div>
                )}
                <div className='flex flex-col justify-evenly gap-2 text-body-14 font-medium'>
                  <div className='font-semibold text-text-neutral-strong'>{drawerData?.title}</div>
                  <div className='font-medium'>
                    <span className='text-text-neutral-medium'>Phân loại: </span>
                    {drawerData?.conditionVariable?.isHaveVariant ? (
                      <span className='text-text-primary-default'>{drawerData?.variant?.variantName}</span>
                    ) : (
                      <span className='text-text-neutral-lighter'>Không có</span>
                    )}
                  </div>
                </div>
              </div>
              <div className='flex w-full items-center justify-between text-body-14 text-text-neutral-strong'>
                <span className='text-caption-12'>
                  Số lượng: {drawerData?.variant ? drawerData?.variant?.quantity : drawerData?.quantity}
                </span>
                <div className='flex items-stretch text-text-success-strong'>
                  <span className='text-caption-12'>₫</span>
                  <span className='font-semibold'>
                    {formatCurrency(
                      drawerData?.conditionVariable?.isHaveVariant
                        ? drawerData?.variant?.tempPrice
                        : drawerData?.devicePrice
                    )}
                  </span>
                </div>
              </div>
            </div>
            {drawerData?.conditionVariable?.isHavePricing && drawerData.pricing?.visible && (
              <div className=' flex w-full flex-col gap-2 rounded-xl bg-bg-neutral-lightest p-4 text-body-14 text-text-neutral-strong'>
                <span className='font-semibold'>{drawerData?.pricing?.pricingName}</span>
                {/* Chu kỳ */}
                <div aria-hidden={!drawerData?.helpers.displayCycle} className='font-medium aria-hidden:hidden'>
                  <span className='text-text-neutral-medium'>Chu kỳ: </span>
                  <span className='text-text-primary-default'>{drawerData?.helpers.cycleDescription}</span>
                </div>
                <div className='flex items-center justify-between'>
                  <div className='text-caption-12 font-medium text-text-neutral-medium'>
                    <span>Số lượng: </span>
                    <span>{drawerData?.pricing?.quantity}</span>
                  </div>
                  <div className='flex items-stretch text-text-success-strong'>
                    <span className='text-caption-12'>₫</span>
                    <span className='font-semibold'>{formatCurrency(drawerData?.pricing?.tempPrice)}</span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </Drawer>
      )}
    </>
  )
}

export default PopupController
