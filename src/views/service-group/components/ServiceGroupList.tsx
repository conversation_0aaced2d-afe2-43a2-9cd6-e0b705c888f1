'use client'
import React from 'react'

import Image from 'next/image'

import { Divider, Tooltip } from 'antd'

import { isNil } from 'lodash'

import { useResponsive, useLocalState } from '@/hooks'
import type { GroupPricing } from '../types'

import { formatCurrency } from '@/constants/commonFunction'
import { converter } from '../utils'
import { handleLoaderImg } from '@/utils/string'

import CalculateAddon from '@/components/addon/calculateAddon'
import { PRICING_PLAN_INT } from '@/views/payment/constant/PaymentConstant'

const ServiceGroupItem = ({
  item
  // arrows,
  // dots
}: {
  item: GroupPricing
  arrows?: boolean
  dots?:
    | boolean
    | {
        className?: string
      }
    | undefined
}) => {
  const { convertToCycleDescription, isRenderCycle } = converter
  const { setFieldValue } = useLocalState()
  // Condition variable
  const isHaveVariant = !isNil(item.variant)
  const isHavePricing = !isNil(item.pricing)
  const isPricing = item.objectType === 'PRICING'
  const isRenderDevicePrice = !isHaveVariant && item.quantity > 1
  const { isMobile } = useResponsive()

  const conditionVariable = {
    isHaveVariant,
    isHavePricing,
    isPricing,
    isRenderDevicePrice
  }

  const isHiddenUnitLimited = {
    collapse:
      !isHavePricing ||
      !(item.pricing?.lstUnitLimited?.length > 0) ||
      isHaveVariant ||
      [PRICING_PLAN_INT.FLAT_RATE, PRICING_PLAN_INT.UNIT].includes(item.pricing?.pricingPlan),
    expanded: !isHavePricing || !(item.pricing?.lstUnitLimited?.length > 0)
  }

  const handleExpand = () => {
    setFieldValue(`ServiceGroupList.${item?.key}.isExpanded`, !item.isExpanded)
  }

  const setDrawer = (value: any) => {
    setFieldValue('PopupController.ItemDrawer', value)
  }

  return (
    <div key={item?.key} className='container flex items-stretch gap-4 text-body-14 text-text-neutral-strong'>
      <div className='w-[400px] shrink-0 sm:hidden'>
        {/* <CarouselCustom dots={dots} arrows={arrows} draggable nextArrow={<div />} prevArrow={<div />}> */}
        {item.images.map((image: any, index: React.Key | null | undefined) => (
          <div key={index} className='rounded-xl bg-bg-neutral-lightest'>
            <Image
              loader={handleLoaderImg}
              src={image ?? ''}
              width={400}
              height={item.isExpanded ? 200 : 136}
              alt='product'
              priority
              style={{
                objectFit: 'contain',
                width: '100%'
              }}
            />
          </div>
        ))}
        {/* </CarouselCustom> */}
      </div>

      <div
        aria-expanded={item.isExpanded}
        className='relative flex flex-1 rounded-2xl bg-bg-neutral-lightest p-5 font-medium aria-expanded:flex-col sm:hidden sm:p-0'
      >
        {/* Thông tin */}
        {item.isExpanded ? (
          // #region Expanded
          <>
            <div className='flex flex-1 flex-col'>
              <div className='text-headline-18 font-semibold'>{item.title}</div>
              {/* variant */}
              {!isPricing && (
                <div className='flex justify-between pt-3 text-text-neutral-strong'>
                  <div className='flex-1'>
                    Phân loại:{' '}
                    <span className='font-semibold text-text-primary-default'>
                      {isHaveVariant ? item.variant?.variantName : ' Không có'}
                    </span>
                  </div>
                  <div className='flex basis-80 items-center justify-between gap-2 md:basis-auto'>
                    {isHaveVariant && item.variant?.price >= 0 && (
                      <span className='flex items-start md:hidden'>
                        <span className='text-caption-12'>₫</span>
                        {formatCurrency(item.variant?.price)}
                      </span>
                    )}
                    {isRenderDevicePrice && (
                      <span className='flex items-start md:hidden'>
                        <span className='text-caption-12'>₫</span>
                        {formatCurrency(item.price)}
                      </span>
                    )}
                    <span className='md:hidden'>
                      {item.variant?.quantity >= 0 ? item.variant?.quantity : isRenderDevicePrice ? item.quantity : ''}
                    </span>
                    {isHaveVariant && item.variant?.tempPrice >= 0 && (
                      <span className='flex items-start justify-self-end font-semibold text-text-neutral-strong'>
                        <span className='text-caption-12 font-normal'>₫</span>
                        {formatCurrency(item.variant?.tempPrice)}
                      </span>
                    )}
                    {!isHaveVariant && item.devicePrice > 0 && (
                      <span className='flex items-start justify-self-end font-semibold text-text-neutral-strong'>
                        <span className='text-caption-12 font-normal'>₫</span>
                        {formatCurrency(item?.devicePrice)}
                      </span>
                    )}
                  </div>
                </div>
              )}

              {/* pricing */}
              {isHavePricing && item.pricing?.visible && (
                <div className='flex justify-between pt-4 text-text-neutral-strong'>
                  <div className='flex flex-1 flex-col gap-3'>
                    <div className='text-headline-16 font-semibold'>{item.pricing?.pricingName}</div>
                    {isRenderCycle(item.pricing ?? {}) && (
                      <div className='flex gap-1'>
                        <span>Chu kỳ:</span>
                        <span className='font-semibold text-text-primary-default'>
                          {/* {convertToCycleDescription(item.pricing ?? {})} */}
                          {item.helpers?.cycleDescription}
                        </span>
                      </div>
                    )}
                  </div>
                  <div className='flex basis-80 items-center justify-between gap-2 md:basis-auto'>
                    {item.pricing?.price >= 0 && (
                      <span className='flex items-start md:hidden'>
                        <span className='text-caption-12'>₫</span>
                        {formatCurrency(item.pricing?.price)}
                        <span
                          aria-hidden={isHiddenUnitLimited.expanded}
                          className='ml-1 flex items-center aria-hidden:hidden'
                        >
                          <Tooltip
                            overlayInnerStyle={{
                              minWidth: isMobile ? '343px' : '510px',
                              width: 'fit-content',
                              background: '#ffffff'
                            }}
                            title={
                              <CalculateAddon
                                unitLimitedList={item.pricing?.lstUnitLimited}
                                quantity={item.pricing?.quantity}
                                pricingPlan={item.pricing?.pricingPlan}
                                totalPrice={item.pricing?.tempPrice}
                                getNewQuantity={false}
                              />
                            }
                            placement='bottom'
                          >
                            <i className='onedx-information size-4' />
                          </Tooltip>
                        </span>
                      </span>
                    )}
                    <span
                      aria-hidden={item.pricing?.quantity < 0}
                      className='flex items-center justify-center gap-1 place-self-center aria-hidden:invisible md:hidden'
                    >
                      <span>{item.pricing?.quantity >= 0 ? item.pricing?.quantity : ''}</span>
                      <Tooltip
                        title={`Miễn phí: ${item.pricing?.numFree}`}
                        overlayInnerStyle={{
                          padding: '4px',
                          minHeight: 'auto'
                        }}
                      >
                        <span
                          aria-hidden={!isHavePricing || !item.pricing?.numFree || item.pricing?.numFree <= 0}
                          className='inline-flex items-center justify-start gap-1 rounded bg-green-2 px-1 py-0.5 aria-hidden:hidden'
                        >
                          <i className='onedx-gift-box size-5' />
                          <span className='text-center text-xs font-medium leading-none  tracking-tight text-green-6'>
                            {/* Miễn phí {item.pricing?.numFree} */}
                            {item.pricing?.numFree}
                          </span>
                        </span>
                      </Tooltip>
                    </span>
                    {item.pricing?.tempPrice >= 0 && (
                      <span className='flex items-start justify-self-end font-semibold text-text-neutral-strong'>
                        <span className='text-caption-12 font-normal'>₫</span>
                        {formatCurrency(item.pricing?.tempPrice)}
                      </span>
                    )}
                  </div>
                </div>
              )}
            </div>
            {/* divider */}
            <Divider className='mb-5 mt-4' />
            {/* Thành tiền */}
            <div className='flex justify-between font-semibold text-text-success-strong'>
              <div className='flex-1 text-text-neutral-strong'>Thành tiền</div>
              {item.total >= 0 && (
                <div className='flex'>
                  <span className='text-caption-12 font-normal'>₫</span>
                  {formatCurrency(item.total)}
                </div>
              )}
            </div>
          </>
        ) : (
          // #endregion
          // #region Collapsed
          <div className='contents'>
            <div className='flex flex-1 flex-col gap-3'>
              <Tooltip title={item.title}>
                <span
                  className='line-clamp-2 text-headline-18 font-semibold'
                  style={{
                    overflowWrap: 'anywhere'
                  }}
                >
                  {item.title}
                </span>
              </Tooltip>
              {!isPricing && (
                <div className='text-text-neutral-strong'>
                  Phân loại:{' '}
                  <span className='font-semibold text-text-primary-default'>
                    {isHaveVariant ? item.variant?.variantName : 'Không có'}
                  </span>
                </div>
              )}
              {isHavePricing && isPricing && (
                <div className='contents'>
                  <div className='text-text-neutral-strong'>
                    Gói dịch vụ:{' '}
                    <span className='font-semibold text-text-primary-default'>{item.pricing?.pricingName}</span>
                  </div>
                  {isRenderCycle(item.pricing ?? {}) && (
                    <div className='text-text-neutral-strong'>
                      Chu kỳ:{' '}
                      <span className='font-semibold text-text-primary-default'>
                        {convertToCycleDescription(item.pricing ?? {})}
                      </span>
                    </div>
                  )}
                </div>
              )}
              {isHavePricing && !isPricing && item.pricing?.visible && (
                <div className='text-text-neutral-strong'>
                  Gói dịch vụ:{' '}
                  <span className='font-semibold text-text-primary-default'>{item.helpers.pricingName}</span>
                </div>
              )}
            </div>
            {/* Giá, số lượng, tổng */}
            <div className='grid basis-80 grid-cols-3 items-center justify-between gap-2 md:basis-auto md:grid-cols-1'>
              <span
                aria-hidden={item.priceCollapsible < 0 || item.visibleExpand}
                className='order-1 flex items-start aria-hidden:invisible md:hidden'
              >
                <span className='text-caption-12'>₫</span>
                {formatCurrency(item.priceCollapsible)}
                <span aria-hidden={isHiddenUnitLimited.collapse} className='ml-1 flex items-center aria-hidden:hidden'>
                  <Tooltip
                    overlayInnerStyle={{
                      minWidth: isMobile ? '343px' : '510px',
                      width: 'fit-content',
                      background: '#ffffff'
                    }}
                    title={
                      <CalculateAddon
                        unitLimitedList={item.pricing?.lstUnitLimited}
                        quantity={item.pricing?.quantity}
                        pricingPlan={item.pricing?.pricingPlan}
                        totalPrice={item.pricing?.tempPrice}
                        getNewQuantity={false}
                      />
                    }
                    placement='bottom'
                  >
                    <i className='onedx-information size-4' />
                  </Tooltip>
                </span>
              </span>
              <span
                aria-hidden={item.quantityCollapsible < 0 || item.visibleExpand}
                className='order-2 flex items-center justify-center gap-1
               place-self-center aria-hidden:invisible md:hidden'
              >
                <span>{item.quantityCollapsible ?? ''}</span>
                <Tooltip
                  title={`Miễn phí: ${item.pricing?.numFree}`}
                  overlayInnerStyle={{
                    padding: '4px',
                    minHeight: 'auto'
                  }}
                >
                  <span
                    aria-hidden={!isHavePricing || !item.pricing?.numFree || item.pricing?.numFree <= 0}
                    className='inline-flex items-center justify-start gap-1 rounded bg-green-2 px-1 py-0.5 aria-hidden:hidden'
                  >
                    <i className='onedx-gift-box size-5' />
                    <span className='text-center text-xs font-medium leading-none  tracking-tight text-green-6'>
                      {/* Miễn phí {item.pricing?.numFree} */}
                      {item.pricing?.numFree}
                    </span>
                  </span>
                </Tooltip>
              </span>
              <span
                aria-hidden={item.total < 0}
                className='order-3 flex items-start justify-self-end font-semibold text-text-success-strong aria-hidden:invisible'
              >
                <span className='text-caption-12 font-normal'>₫</span>
                {formatCurrency(item.total)}
              </span>
            </div>
          </div>
          // #endregion
        )}
        <div
          aria-hidden={!item.visibleExpand}
          className='absolute right-4 top-4 flex size-7 items-center justify-center rounded-md bg-light-blue-1 aria-hidden:hidden sm:hidden'
          onClick={handleExpand}
        >
          <i
            aria-expanded={item.isExpanded}
            className='onedx-chevron-down size-4 cursor-pointer text-icon-primary-default transition-transform aria-expanded:rotate-180'
          />
        </div>
      </div>

      {/* Mobile */}
      <div className='hidden w-full flex-col gap-3 rounded-2xl bg-bg-neutral-lightest p-5 sm:flex'>
        <div className='flex items-stretch gap-2'>
          <Image
            className='hidden rounded-lg bg-bg-neutral-lightest sm:block'
            loader={handleLoaderImg}
            src={item.images?.[0] ?? ''}
            width={64}
            height={64}
            alt='product'
            style={{
              objectFit: 'contain'
            }}
          />
          <div className='hidden grow flex-col gap-2 text-body-14 font-medium sm:flex'>
            <Tooltip title={item.title}>
              <div
                className='line-clamp-2 font-semibold text-text-neutral-strong'
                style={{
                  overflowWrap: 'anywhere'
                }}
              >
                {item.title}
              </div>
            </Tooltip>
            <div aria-hidden={isPricing} className='aria-hidden:hidden'>
              <span className='text-text-neutral-medium'>Phân loại: </span>
              {isHaveVariant ? (
                <span className='text-text-primary-default'>{item.variant?.variantName}</span>
              ) : (
                <span className='text-text-neutral-lighter'>Không có</span>
              )}
            </div>
            <div className='line-clamp-1'>
              <span className='text-text-neutral-medium'>Gói dịch vụ: </span>
              {isHavePricing ? (
                <Tooltip title={item.helpers.pricingName}>
                  <span className='text-text-primary-default'>
                    {item.helpers.displayCycle ? item.pricing.pricingName : item.helpers.pricingName}
                  </span>
                </Tooltip>
              ) : (
                <span className='text-text-neutral-lighter'>Không có</span>
              )}
            </div>
            <div aria-hidden={!isPricing || !item.helpers.displayCycle} className='aria-hidden:hidden'>
              <span className='text-text-neutral-medium'>Chu kỳ: </span>
              <span className='text-text-primary-default'>{item.helpers?.cycleDescription}</span>
            </div>
          </div>
        </div>
        <div className='flex w-full justify-between text-body-14 text-text-neutral-strong'>
          {item.visibleExpand ? (
            <div
              className='font-medium text-text-primary-default'
              onClick={() =>
                setDrawer({
                  open: true,
                  placement: 'bottom',
                  data: {
                    ...item,
                    conditionVariable
                  }
                })
              }
            >
              Xem chi tiết
            </div>
          ) : (
            <span className='text-caption-12'>
              Số lượng: {isPricing ? item.pricing?.quantity : isHaveVariant ? item.variant?.quantity : item.quantity}
            </span>
          )}
          <div className='flex items-center text-text-success-strong'>
            <span className='text-caption-12'>₫</span>
            <span className='font-semibold'>{formatCurrency(item.total)}</span>
          </div>
        </div>
      </div>
    </div>
  )
}

const ServiceGroupItemSkeleton = () => {
  return (
    <>
      <div className='flex gap-4 text-body-14 text-text-neutral-strong sm:hidden'>
        <div className='h-[136px] w-[400px] animate-pulse rounded-xl bg-gray-300' />
        <div className='relative flex flex-1 gap-3 rounded-2xl bg-bg-neutral-lightest p-5 font-medium'>
          {/* Thông tin */}
          <div className='flex flex-1 flex-col gap-3 self-center'>
            <div className='h-[14px] w-60 animate-pulse rounded-md bg-gray-300' />
            <div className='h-[14px] w-30 animate-pulse rounded-md bg-gray-300' />
            <div className='h-[14px] w-30 animate-pulse rounded-md bg-gray-300' />
          </div>
          {/* Giá, số lượng, tổng */}
          <div className='flex basis-80 items-center justify-between gap-2'>
            <div className='h-[14px] w-20 animate-pulse rounded-md bg-gray-300' />
            <div className='h-[14px] w-20 animate-pulse rounded-md bg-gray-300' />
            <div className='h-[14px] w-20 animate-pulse rounded-md bg-gray-300' />
          </div>
        </div>
      </div>
      <div className='hidden w-full flex-col gap-3 rounded-2xl bg-bg-neutral-lightest p-5 sm:flex'>
        <div className='flex items-stretch gap-2'>
          <div className='size-[64px] animate-pulse rounded-lg bg-gray-300' />
          <div className='flex flex-col gap-2 text-body-14 font-medium'>
            <div className='h-[14px] w-60 animate-pulse rounded-md bg-gray-300' />
            <div className='h-[14px] w-30 animate-pulse rounded-md bg-gray-300' />
            <div className='h-[14px] w-30 animate-pulse rounded-md bg-gray-300' />
          </div>
        </div>
        <div className='flex w-full justify-between text-body-14 text-text-neutral-strong'>
          <div className='h-[14px] w-30 animate-pulse rounded-md bg-gray-300' />
          <div className='flex items-center text-text-success-strong'>
            <div className='h-[14px] w-20 animate-pulse rounded-md bg-gray-300' />
          </div>
        </div>
      </div>
    </>
  )
}

const ServiceGroupList = () => {
  const { state } = useLocalState()

  return (
    <div className='flex flex-col gap-5 text-body-14 text-text-neutral-strong sm:gap-3'>
      <div className='inline-flex items-center justify-start gap-2'>
        <div className='hidden h-4 w-0.5 bg-sme-orange-7 sm:block' />
        <div className='text-title-24 font-semibold sm:text-body-14'>
          Sản phẩm trong nhóm dịch vụ ({state?.ServiceGroupList?.length})
        </div>
      </div>
      <div className='flex flex-col gap-4'>
        {!state?.ServiceGroupList?.length &&
          Array.from({ length: 3 }).map((_: any, index: React.Key) => <ServiceGroupItemSkeleton key={index} />)}
        {state?.ServiceGroupList?.map((item: any) => (
          <ServiceGroupItem
            item={item}
            key={item?.key}
            // arrows={state?.ServiceGroupList?.length > 1}
            // dots={(item?.images?.length ?? 0) > 1}
          />
        ))}
      </div>
    </div>
  )
}

export default ServiceGroupList
