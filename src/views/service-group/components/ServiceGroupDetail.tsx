import React, { useState } from 'react'

import { useRouter } from 'next/navigation'

import Image from 'next/image'

import { But<PERSON>, Divider, Steps, Tabs } from 'antd'

import classNames from 'classnames'

import DOMPurify from 'dompurify'

import { isEmpty } from 'lodash'

import type { SupportDataObject } from '../types'

import { useUser, useLocalState } from '@/hooks'

import { functions, StatusEnum } from '../utils'
import { handleLoaderImg } from '@/utils/string'

import PaymentComponents from '@views/payment/components/PaymentComponents'

// #region Sub components
const OverviewPage = ({ description }: { description: string | React.ReactNode }) => {
  const { state, setFieldValue } = useLocalState()
  const route = useRouter()
  const { isLoggedIn } = useUser()
  const { beforePaymentServiceGroup } = functions
  const stringDescription = description?.toString() || ''

  // <PERSON><PERSON> c<PERSON> nhiều ảnh chụp màn hình thì lấy ảnh đầu tiên
  const snapshotImg = state.serviceGroup?.mediaInfo?.lstSnapshotUrl?.[0]

  return (
    <section className='flex flex-col gap-6 sm:gap-4 sm:pt-6'>
      <div className='grid grid-cols-2 gap-9 sm:gap-4 md:grid-cols-1'>
        <div>
          <div className='mb-6 text-title-24 font-semibold sm:mb-4 sm:text-headline-18'>Tổng quan</div>

          {typeof description === 'string' ? (
            <div
              className='flex-1 text-body-14 text-text-neutral-strong'
              dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(stringDescription?.replace(/\n/g, '<br />')) }}
            />
          ) : (
            <div className='flex-1'>{description}</div>
          )}
        </div>

        {/* List of image */}
        {snapshotImg && (
          <div className='relative w-full' style={{ paddingBottom: '56.25%' }}>
            <Image
              loader={handleLoaderImg}
              src={snapshotImg.url}
              alt={snapshotImg.fileName}
              fill
              priority
              sizes='(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw' // Add sizes prop
              style={{ objectFit: 'contain', borderRadius: '16px' }}
            />
          </div>
        )}
      </div>

      {!description && (
        // Skeleton
        <div className='flex gap-4 md:flex-col'>
          <div className='flex flex-1 flex-col gap-4'>
            <div className='ml-10 grid grid-cols-3 gap-4'>
              <div className='col-span-2 h-4 w-full animate-pulse rounded-lg bg-gray-200' />
              <div className='h-4 w-full animate-pulse rounded-lg bg-gray-200' />
            </div>
            <div className='h-4 w-full animate-pulse rounded-lg bg-gray-200' />
            <div className='grid grid-cols-3 gap-4'>
              <div className='h-4 w-full animate-pulse rounded-lg bg-gray-200' />
              <div className='col-span-2 h-4 w-full animate-pulse rounded-lg bg-gray-200' />
            </div>
            <div className='grid grid-cols-2 gap-4'>
              <div className='h-4 w-full animate-pulse rounded-lg bg-gray-200' />
              <div className='h-4 w-full animate-pulse rounded-lg bg-gray-200' />
            </div>
            <div className='h-4 w-full animate-pulse rounded-lg bg-gray-200' />
            <div className='h-4 w-full animate-pulse rounded-lg bg-gray-200' />
            <div className='h-4 w-full animate-pulse rounded-lg bg-gray-200' />
          </div>
          <div className='size-56 animate-pulse self-center rounded-lg bg-gray-200' />
        </div>
      )}

      <div className='flex justify-center'>
        <Button
          type='primary'
          className='w-80 rounded-xl text-body-14 font-medium md:w-full'
          disabled={state?.variables?.disabledPayment}
          onClick={() => {
            const validate = beforePaymentServiceGroup(state?.serviceGroup)

            if (validate?.status === StatusEnum.ERROR) {
              return
            } else if (!isLoggedIn) {
              setFieldValue('PopupLogin', { visible: true, note: 'navigateToLogin' })
            } else if (state?.serviceGroup?.id) route.push('/checkout/service-group/' + state?.serviceGroup?.id)
          }}
        >
          Đăng ký ngay
        </Button>
      </div>
    </section>
  )
}

const SupportPage = ({ supportInformation }: any) => {
  const { renderVideo, useRenderThumbnail, renderDocGuide, downloadAll } = functions
  const chooseVideo = true
  const thumbnail = useRenderThumbnail(supportInformation?.mediaInfo?.videoGuideUrl?.url ?? null)

  const renderSupportData: SupportDataObject[] = [
    {
      title: 'Nhà phát hành:',
      content: supportInformation?.owner && (
        <div className='inline-flex flex-wrap gap-1 text-primary'>
          <span className='flex-1'>{supportInformation.owner}</span>
          <span className='pt-0.5'>
            <i className='onedx-contact size-4' />
          </span>
        </div>
      ),
      icon: 'onedx-user'
      // style: { flexBasis: '200px', flexShrink: 0 }
    },
    {
      title: 'Email hỗ trợ:',
      content: <div className='font-medium'>{supportInformation?.email}</div>,
      icon: 'onedx-banner-email'
      // style: { flexBasis: '200px' }
    },
    {
      title: 'Điện thoại hỗ trợ:',
      content: <div className='font-medium'>{supportInformation?.phoneNumber}</div>,
      icon: 'onedx-phone'
      // style: { flexBasis: '200px', flexShrink: 0 }
    }
    // {
    //   title: 'Địa chỉ:',
    //   content: <div className='font-medium'>{supportInformation?.address}</div>,
    //   icon: 'onedx-location',
    //   style: { flexBasis: '200px' }
    // }
  ]

  return (
    <section className='flex flex-col'>
      <section
        aria-hidden={!supportInformation?.mediaInfo?.videoGuideUrl && !!supportInformation}
        className='flex flex-col gap-6 pb-7 aria-hidden:hidden sm:py-3'
      >
        <div className='text-title-24 font-semibold sm:text-headline-18'>Video giới thiệu</div>
        {!supportInformation && (
          <>
            <div className='h-6 w-60 animate-pulse rounded-lg bg-gray-200' />
            <div className='flex gap-4'>
              <div className='size-48 animate-pulse rounded-lg bg-gray-200 md:size-32' />
              <div className='size-48 animate-pulse rounded-lg bg-gray-200 md:size-32' />
              <div className='size-48 animate-pulse rounded-lg bg-gray-200 md:size-32' />
            </div>
          </>
        )}

        {!!supportInformation?.mediaInfo?.videoGuideUrl && (
          <div className='flex gap-4 md:flex-col'>
            <div className='flex w-2/3 flex-col gap-4 md:w-full'>
              {renderVideo(supportInformation.mediaInfo.videoGuideUrl.url)}
              <div className='text-headline-18 font-semibold'>
                {supportInformation.mediaInfo?.videoGuideUrl?.fileName}
              </div>
            </div>
            <div className='w-1/3 sm:hidden md:w-full'>
              <div className='mb-4 flex justify-between'>
                <div className='text-base font-semibold'>Video HDSD</div>
                <div className='text-xs font-normal'>1 video</div>
              </div>
              <div
                className='flex cursor-pointer gap-3 rounded-lg border-4 p-2'
                style={
                  chooseVideo
                    ? {
                        borderColor: 'rgba(2, 23, 60, 0.09)',
                        borderStyle: 'solid',
                        borderWidth: '1px',
                        boxShadow: '0px 2px 4px rgba(165, 163, 174, 0.2)'
                      }
                    : { backgroundColor: 'rgba(242, 244, 249, 1)' }
                }
              >
                <div style={{ width: 106, height: 60 }}>{thumbnail}</div>
                <div className='pt-[3px]'>
                  <div
                    className='text-xs font-medium'
                    style={{
                      overflowWrap: 'break-word',
                      wordWrap: 'break-word',
                      wordBreak: 'break-word',
                      hyphens: 'auto'
                    }}
                  >
                    {supportInformation.mediaInfo?.videoGuideUrl?.fileName}
                  </div>
                  <div className='flex gap-2'>
                    {chooseVideo && (
                      <div
                        className='w-fit rounded-md px-2 py-0.5 text-xs font-medium'
                        style={{ backgroundColor: 'rgba(230, 245, 250, 1)', color: 'rgba(0, 112, 196, 1)' }}
                      >
                        Đang phát
                      </div>
                    )}
                    {/*<div className='font-normal text-xs self-center	'>22:30</div>*/}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </section>
      <section
        aria-hidden={isEmpty(supportInformation?.mediaInfo?.lstDocGuideUrl) && !!supportInformation}
        className='flex flex-col gap-6 pb-7 aria-hidden:hidden sm:py-3'
      >
        {!supportInformation && (
          <>
            <div className='h-6 w-60 animate-pulse rounded-lg bg-gray-200' />
            <div className='grid grid-cols-3 gap-4'>
              <div className='h-12 w-full animate-pulse rounded-lg bg-gray-200' />
              <div className='h-12 w-full animate-pulse rounded-lg bg-gray-200' />
              <div className='h-12 w-full animate-pulse rounded-lg bg-gray-200' />
            </div>
          </>
        )}

        {!!supportInformation?.mediaInfo?.lstDocGuideUrl && (
          <>
            <div className='flex justify-between'>
              <span className='text-title-24 font-semibold sm:text-headline-18'>Tài liệu hướng dẫn sử dụng</span>
              <Button
                className='w-[180px] rounded-xl border-none bg-blueLight text-body-14 text-text-primary-default sm:h-7 sm:w-auto sm:rounded-md sm:px-3 sm:py-1'
                color='primary'
                onClick={() => {
                  downloadAll(supportInformation?.mediaInfo?.lstDocGuideUrl)
                }}
              >
                <span className='font-medium'>Tải xuống tất cả</span>
              </Button>
            </div>
            <div className='grid grid-cols-3 gap-4 sm:grid-cols-1'>
              {supportInformation.mediaInfo.lstDocGuideUrl.map((doc: any, index: number) => (
                <div key={index}>{renderDocGuide(doc)}</div>
              ))}
            </div>
          </>
        )}
      </section>
      <section className='flex flex-col gap-6 pb-7 sm:py-3 sm:pb-4'>
        <div className='text-title-24 font-semibold'>Thông tin hỗ trợ</div>
        <div className='flex justify-between gap-4 rounded-xl bg-bg-neutral-lighter p-4 md:flex-col'>
          {renderSupportData.map((item, index) => {
            const { title, content, icon, style, className } = item

            const isLastItem = index === renderSupportData.length - 1

            return (
              <div
                key={index}
                className={classNames(
                  'flex items-stretch justify-between gap-3 grow basis-[200px] md:basis-0',
                  className
                )}
                style={style}
              >
                <div className='flex gap-2'>
                  <div className='size-6 text-primary'>
                    <i className={classNames(icon, 'size-6')} />
                  </div>
                  <div className='flex flex-1 flex-col text-clip'>
                    <div className='text-body-14 font-normal'>{title}</div>
                    <div className='text-wrap text-body-14'>{content}</div>
                  </div>
                </div>
                {!isLastItem && <Divider type='vertical' className='h-full md:hidden' />}
              </div>
            )
          })}
        </div>
      </section>
    </section>
  )
}

const VersionPage = () => {
  return <section className='h-full' />
}
// #endregion

// #region Main component
const ServiceGroupDetail = () => {
  const [current, setCurrent] = useState(0)
  const { state } = useLocalState()

  const steps = [
    {
      title: 'Tổng quan',
      component: <OverviewPage description={state?.ServiceGroupDetail?.description} />
    },
    // {
    //   title: 'FAQ',
    //   component: <FAQPage />
    // },
    {
      title: 'Hỗ trợ',
      component: <SupportPage supportInformation={state.ServiceGroupDetail?.supportInformation} />
    },
    {
      title: 'Phiên bản',
      component: <VersionPage />
    }
  ]

  return (
    <>
      <div className='flex flex-col gap-4 sm:hidden'>
        {/* title */}
        <PaymentComponents.Title
          title='Thông tin chi tiết'
          className='text-headline-18'
          rootClassname='px-4 sm:hidden'
        />

        {/* content */}
        <div className='flex w-full gap-4'>
          <div className='ml-4 w-48 sm:hidden'>
            <Steps
              progressDot
              direction='vertical'
              current={current}
              onChange={el => {
                setCurrent(el)
              }}
            >
              {steps.map((item, index) => {
                const { title } = item
                const isActive = current === index

                return (
                  <Steps.Step
                    status={isActive ? 'process' : 'wait'}
                    key={index}
                    title={
                      <div
                        key={index}
                        className={classNames(
                          'flex w-44 ml-1 items-center gap-2 rounded-lg px-3 py-2 text-body-14 font-medium transition-colors duration-300 text-text-neutral-strong',
                          {
                            'bg-sme-blue-1': isActive
                          }
                        )}
                      >
                        <div
                          className={classNames({
                            'text-primary': isActive
                          })}
                        >
                          {title}
                        </div>
                        <i
                          className={classNames('onedx-chevron-right size-4 transition-opacity text-primary', {
                            'opacity-0': !isActive
                          })}
                        />
                      </div>
                    }
                  />
                )
              })}
            </Steps>
          </div>
          <div className='mr-4 flex-1'>
            {steps.map((item, index) => {
              const { component } = item
              const isActive = current === index

              return (
                <div
                  key={index}
                  className={classNames({
                    'hidden-content': !isActive,
                    'visible-content': isActive
                  })}
                >
                  <div className='h-full text-body-14 text-text-neutral-strong'>{component}</div>
                </div>
              )
            })}
          </div>
        </div>
      </div>
      <Tabs
        activeKey={current.toString()}
        onChange={el => {
          setCurrent(Number(el))
        }}
        className='hidden sm:flex'
        items={steps.map((item, index) => {
          const { title } = item

          return {
            key: index.toString(),
            label: title,
            children: (
              <div className='flex flex-col gap-4'>
                <div className='flex-1'>{item.component}</div>
              </div>
            )
          }
        })}
      />
    </>
  )
}
// #endregion

export default ServiceGroupDetail
