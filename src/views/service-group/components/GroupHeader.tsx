import { message, Popover, Tooltip } from 'antd'

import { useMutation } from '@tanstack/react-query'

import classNames from 'classnames'

import { client } from '@/components/ReactQueryProvider'
import { trackProductLike } from '@/components/third-parties/matomo/utils/tracking'
import ServiceGroup from '@/models/ServiceGroup'
import { handleSrcImg } from '@/utils/string'
import { useUser, useLocalState } from '@/hooks'
import TagCustom from '../common/TagCustom'

const ShareButton = ({ children }: { children?: React.ReactNode }) => {
  return (
    <Popover
      placement='bottomRight'
      content={
        <div className='flex gap-4'>
          <div
            onClick={async () => {
              navigator.clipboard.writeText(window.location.href)
              message.success('Đã sao chép liên kết')
            }}
            className='flex rounded-md bg-blueLight p-2 align-middle text-sm font-medium text-primary hover:cursor-pointer'
          >
            <i className='onedx-share-link size-5 text-primary' />
          </div>
          <div
            onClick={() => {
              const facebookShareUrl = `https://www.facebook.com/sharer/sharer.php?u=${window.location.href}`

              window.open(facebookShareUrl, '_blank')
            }}
            className='flex rounded-md bg-blueLight p-2 align-middle text-sm font-medium text-primary hover:cursor-pointer'
          >
            <i className='onedx-facebook size-5' />
          </div>
        </div>
      }
    >
      {children ? (
        children
      ) : (
        <div className='flex cursor-pointer rounded-lg bg-white p-2 align-middle text-sm font-medium text-primary'>
          <i className='onedx-share-traffic size-5' />
        </div>
      )}
    </Popover>
  )
}

const GroupHeader = () => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { state, setFieldValue } = useLocalState()
  const renderData = state.GroupHeader
  const { user } = useUser()

  const reactingMutation = useMutation({
    mutationKey: ['likeService'],
    mutationFn: ServiceGroup.serviceGroupReaction,
    onSuccess: () => {
      if (renderData?.isReaction) {
        message.success('Đã xóa sản phẩm dịch vụ khỏi yêu thích')
      } else {
        message.success('Thêm sản phẩm dịch vụ vào yêu thích thành công')
      }

      trackProductLike(renderData?.title, !renderData?.isReaction)

      client.refetchQueries({
        queryKey: ['getServiceGroup'],
        type: 'active'
      })
    },
    onError: () => {}
  })

  const IconInfo = ({
    icon,
    text,
    value,
    hidden
  }: {
    icon: string | React.ReactNode
    text: string
    value: number
    hidden?: boolean
  }) => {
    const renderIcon = typeof icon === 'string' ? <i className={`${icon} size-5`} /> : icon

    return (
      <div aria-hidden={hidden} className='flex gap-2 text-body-14 aria-hidden:hidden sm:text-text-neutral-strong'>
        <div>{renderIcon}</div>
        <div className='flex gap-1'>
          <span>{value}</span>
          <span>{text}</span>
        </div>
      </div>
    )
  }

  return (
    <div className='flex flex-col gap-2 bg-bg-canvas'>
      {/* Moblie AppBar */}
      <div className='hidden items-stretch justify-between bg-bg-surface p-2 text-text-neutral-strong sm:flex'>
        <div className='flex items-center'>
          <div
            className='flex cursor-pointer rounded-lg bg-white p-2.5 align-middle text-sm font-medium'
            onClick={() => {
              window.history.back()
            }}
          >
            <i className='onedx-arrow-left size-5' />
          </div>
          <div className='text-headline-16 font-semibold '>Nhóm dịch vụ</div>
        </div>
        <div className='flex items-center'>
          {user?.id && (
            <div
              className='cursor-pointer rounded-lg bg-white p-2 align-middle text-sm font-medium text-primary'
              onClick={() => reactingMutation.mutate(state.serviceGroup?.id)}
            >
              <i
                className={classNames('size-5', {
                  'onedx-heart-outline': !renderData?.isReaction,
                  'onedx-heart text-red-6': renderData?.isReaction
                })}
              />
            </div>
          )}
          <ShareButton>
            <div className='flex cursor-pointer rounded-lg bg-white p-2.5 align-middle text-sm font-medium'>
              <i className='onedx-share-traffic size-5' />
            </div>
          </ShareButton>
        </div>
      </div>

      {/* Desktop Header */}
      <div className='w-full bg-bg-surface'>
        <div className='w-full flex-col gap-4 bg-bg-surface sm:mx-auto sm:flex sm:max-w-[480px] sm:px-4 sm:pt-4'>
          <div
            className='flex w-full justify-center gap-6 py-10 text-text-on-primary sm:h-[138px] sm:rounded-xl sm:py-[26px]'
            style={{
              background: `url(/assets/images/pages/enterprise/banner-service-detail.webp) center center / cover no-repeat`
            }}
          >
            <div className='flex-col gap-4 mdo:flex'>
              {renderData?.icon ? (
                <img
                  className='block size-[92px] rounded-2xl sm:size-[84px]'
                  src={handleSrcImg(renderData?.icon)}
                  alt='banner'
                />
              ) : (
                <div className='block size-[92px] animate-pulse rounded-2xl bg-bg-neutral-lighter sm:size-[84px]' />
              )}
              <div className='hidden justify-center gap-2 mdo:flex'>
                {user?.id && (
                  <div
                    className='cursor-pointer rounded-lg bg-white p-2 align-middle text-sm font-medium text-primary'
                    onClick={() => reactingMutation.mutate(state.serviceGroup?.id)}
                  >
                    <i
                      className={classNames('size-5', {
                        'onedx-heart-outline': !renderData?.isReaction,
                        'onedx-heart text-red-6': renderData?.isReaction
                      })}
                    />
                  </div>
                )}
                <ShareButton />
              </div>
            </div>
            <div className='flex flex-col sm:hidden'>
              {renderData?.title ? (
                <Tooltip title={renderData?.title} placement='top'>
                  <div className='truncate text-title-48 font-semibold'>{renderData?.title}</div>
                </Tooltip>
              ) : (
                <div className='h-8 w-80 animate-pulse rounded-lg bg-gray-300' />
              )}
              <div className='flex flex-col'>
                {renderData?.owner ? (
                  <div className='text-headline-16 font-medium'>{renderData?.owner}</div>
                ) : (
                  <div className='mt-1 h-3 w-40 animate-pulse rounded-lg bg-gray-300' />
                )}
                <div className='mt-3 flex gap-5 text-sm font-normal'>
                  {!renderData ? (
                    <div className='h-3 w-24 animate-pulse rounded-lg bg-gray-300' />
                  ) : (
                    <IconInfo
                      icon='onedx-cart-checked'
                      text='đã bán'
                      hidden={!renderData?.sold}
                      value={renderData?.sold ?? 0}
                    />
                  )}
                  {/* <IconInfo icon='onedx-heart-full' text='' value={renderData?.reaction} /> */}
                </div>
                <div className='mt-3 flex gap-2 text-text-neutral-strong sm:flex-col'>
                  <div className='flex gap-2'>
                    <TagCustom tag={renderData?.serviceType} className='text-primary' />
                    <div className='pt-0.5 text-white sm:text-orange-400'>|</div>
                    <TagCustom showToolTip tag={renderData?.category} limit={2} className='text-text-neutral-strong' />
                  </div>
                  <div className='flex gap-2'>
                    <div className='pt-0.5 text-white sm:hidden sm:text-orange-400'>|</div>
                    <TagCustom tag={renderData?.customerType} className='text-text-neutral-strong' />
                  </div>
                </div>
              </div>
            </div>
            <div className='flex h-fit flex-wrap gap-4 md:hidden'>
              <div
                onClick={() => {
                  setFieldValue('ContactCustomer.isModalVisible', true)
                }}
                className='flex shrink-0 rounded-lg bg-white px-4 py-2 align-middle text-sm font-medium text-primary hover:cursor-pointer'
              >
                Liên hệ tư vấn
              </div>
              {user?.id && (
                <div
                  className='flex cursor-pointer rounded-lg bg-white p-2 align-middle text-sm font-medium text-primary'
                  onClick={() => reactingMutation.mutate(state.serviceGroup?.id)}
                >
                  <i
                    className={classNames('size-5', {
                      'onedx-heart-outline': !renderData?.isReaction,
                      'onedx-heart text-red-6': renderData?.isReaction
                    })}
                  />
                </div>
              )}
              <ShareButton />
            </div>
          </div>
          <div className='hidden flex-col gap-3 text-text-neutral-strong sm:flex'>
            {renderData?.title ? (
              <Tooltip title={renderData?.title} placement='top'>
                <div className='truncate text-headline-18 font-semibold'>{renderData?.title}</div>
              </Tooltip>
            ) : (
              <div className='h-[18px] w-60 animate-pulse rounded-lg bg-gray-200' />
            )}
            <div className='flex flex-col gap-2'>
              <div className='flex flex-wrap gap-2'>
                <TagCustom tag={renderData?.serviceType} className='text-primary' />
                <TagCustom tag={renderData?.category} limit={2} className='text-text-neutral-strong' />
              </div>
              <div className='flex gap-2'>
                <TagCustom tag={renderData?.customerType} className='text-text-neutral-strong' />
              </div>
            </div>
            {renderData?.owner ? (
              <div className='flex gap-2'>
                <i className='onedx-user size-5' />
                <div className='flex-1 text-body-14'>{renderData?.owner}</div>
              </div>
            ) : (
              <div className='h-3 w-20 animate-pulse rounded-lg bg-gray-300' />
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default GroupHeader
