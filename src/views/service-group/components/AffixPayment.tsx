import { useRouter } from 'next/navigation'

import { Button, Divider } from 'antd'

import classNames from 'classnames'

import { formatCurrency } from '@/constants/commonFunction'
import { useUser, useLocalState } from '@/hooks'
import { functions, StatusEnum } from '../utils'
import PaymentComponents from '@/views/payment/components/PaymentComponents'

const AffixPayment = () => {
  const { state, setFieldValue } = useLocalState()
  const data = state?.AffixPayment
  const route = useRouter()
  const { isLoggedIn } = useUser()
  const { beforePaymentServiceGroup } = functions

  return (
    <div
      className={classNames('light-1 fixed z-20 bottom-0 w-full bg-inherit', {
        'visible-content': data?.visible,
        'hidden-content': !data?.visible
      })}
    >
      <div className='container mx-auto flex flex-col gap-3 py-3 text-body-14 text-text-neutral-strong'>
        <PaymentComponents.Title title='Tổng đơn hàng' rootClassname='hidden px-3 sm:inline-flex' />
        <div className='flex px-3'>
          <div className='flex-1'>
            Sản phẩm (<span>{data?.name}</span>)
          </div>
          <div className='flex'>
            <span className='text-caption-12'>đ</span>
            <span>{formatCurrency(data?.price)}</span>
          </div>
        </div>
        <Divider className='m-0' dashed />
        <div className='flex gap-10 sm:flex-col sm:gap-4'>
          <div className='flex flex-1 items-center gap-4 px-3 sm:justify-between'>
            <div>Tổng đơn hàng</div>
            <div className='flex text-text-success-strong'>
              <span className='text-caption-12'>đ</span>
              <span className='text-headline-20 font-semibold'>{formatCurrency(data?.price)}</span>
            </div>
          </div>
          <div className='flex gap-4 px-4 sm:justify-center'>
            <Button
              type='primary'
              className='min-w-[180px] border-none sm:w-1/2'
              disabled={state?.variables?.disabledPayment}
              onClick={() => {
                const validate = beforePaymentServiceGroup(state?.serviceGroup)

                if (validate?.status === StatusEnum.ERROR) {
                  return
                } else if (!isLoggedIn) {
                  setFieldValue('PopupLogin', { visible: true, note: 'navigateToLogin' })
                } else if (state?.serviceGroup?.id) route.push('/checkout/service-group/' + state?.serviceGroup?.id)
              }}
            >
              Thanh toán
            </Button>
            <Button
              className='min-w-[180px] border-none bg-bright-blue-2 font-medium text-sme-blue-7 sm:hidden'
              disabled={true}
            >
              Thêm vào giỏ hàng
            </Button>
            <Button className='hidden w-1/2 border-none bg-bright-blue-2 font-medium text-sme-blue-7 sm:block'>
              <span className='onedx-cart' />
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default AffixPayment
