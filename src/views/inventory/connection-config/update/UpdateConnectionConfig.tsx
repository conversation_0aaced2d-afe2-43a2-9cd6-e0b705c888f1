'use client'

import React, { useCallback, useEffect, useMemo, useState } from 'react'

import { usePara<PERSON>, useRouter } from 'next/navigation'

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'

import { Button, Checkbox, Form, Input, Select, Space, Spin, Tooltip } from 'antd'

import dayjs from 'dayjs'

import { InfoCircleOutlined } from '@ant-design/icons'

import { SelectPartner } from '@views/inventory/common/SelectPartner'

import ScheduleSyncData from '@views/inventory/common/ScheduleSyncData'

import { CustomHeaderCollapse } from '@views/inventory/common/collapse/CustomHeaderCollapse'

import { EmptyFormItem } from '@views/auth/register/components/RegisterPersonalForm'

import { useUrlApiLogic } from '@views/inventory/common/hooks/useUrlApiLogic'

import type { PathParam } from '@views/inventory/common/hooks/useUrlApiLogic'

import {
  buildCompleteUrl,
  buildConnectionsJson,
  buildDropdownMappingObject,
  buildRequestObject,
  buildResponseObject,
  validateDataMapping,
  validateHMACParams,
  validatorHMACSeparator
} from '@views/inventory/common/hooks/useConvertAndValidation'

import {
  authenticationList,
  connectionTypeList,
  hmacList,
  httpList,
  keyPlacementList
} from '@views/inventory/common/hooks/useDropDownValue'

import { message } from '@components/notification'

import warehouseInstance from '@/models/Warehouse'

import DataMapping from '@views/inventory/common/DataMapping'

import HeaderSection from '@components/solution-iot/service/create/HeaderSection'

import ModalCheckConnection from '@views/inventory/common/modal/ModalCheckConnection'

import { SelectCustom } from '@views/inventory/common/SelectCustom'

export default function UpdateConnectionConfig() {
  const [form] = Form.useForm()
  const { id } = useParams<{ id: string }>()

  const [initRequestResponse, setInitRequestResponse] = React.useState<any>({})

  const [isRequiredRequestObject, setIsRequiredRequestObject] = React.useState(true)

  const [pathVariables, setPathVariables] = useState<PathParam[]>([])

  const [params, setParams] = useState<PathParam[]>([])

  const [hmacParamsList, setHmacParamsList] = useState<any>([])

  const hmacParamsOptions = useMemo(() => {
    return hmacParamsList || []
  }, [hmacParamsList])

  const queryClient = useQueryClient()

  const { isFetching: isLoadingDetail } = useQuery({
    queryKey: ['getConnectionDetail', id],
    queryFn: async () => {
      const res = await warehouseInstance.detailConnection(id)

      let requestObject = null
      let responseObject = null
      let dropdownRequestObject = null
      let dropdownResponseObject = null

      if (res.partnerReqFields) {
        setHmacParamsList(res.partnerReqFields)
      }

      if (res.dxReqFields && res.partnerReqFields) {
        requestObject = buildRequestObject(res.dxReqFields, res.inputMapping)
        dropdownRequestObject = buildDropdownMappingObject(res.partnerReqFields)
      }

      if (res.dxRespFields && res.partnerRespFields) {
        responseObject = buildResponseObject(res.dxRespFields, res.outputMapping)
        dropdownResponseObject = buildDropdownMappingObject(res.partnerRespFields)
      }

      setInitRequestResponse({
        requestObject: requestObject,
        responseObject: responseObject,
        dropdownRequestObject: dropdownRequestObject,
        dropdownResponseObject: dropdownResponseObject
      })

      let startTime = res?.startTime ? dayjs(res?.startTime, 'HH:mm') : dayjs('00:00', 'HH:mm')

      let endTime = null

      let rangeTime = null

      if (res?.endTime) {
        rangeTime = [dayjs(res?.startTime, 'HH:mm'), dayjs(res?.endTime, 'HH:mm')]
        endTime = rangeTime[1]
      }

      if (res.reportFrequency == 4) {
        startTime = dayjs(res?.specificExecutionDateTime.split(' ')[1], 'HH:mm')
      }

      let frequencyMonth = 'Ngày cố định hàng tháng'

      if (res.reportFrequency == 3 && res.week != null) {
        frequencyMonth = 'Thứ trong tuần cố định hàng tháng'
      }

      const day = res?.day ? res?.day : null
      const week = res?.week ? res?.week : null

      setTimeout(() => {
        form.setFieldsValue({
          code: res.code,
          name: res.name,
          connectionType: res.type,
          authentication: res.authMethod,
          httpType: res.configMethod,
          urlApi: res.urlEndpoint,
          pathVariables: res.apiConfig?.url?.variables,
          params: res.apiConfig?.url?.params,
          tokenValue: res.auth?.bearer?.value || '',
          headerName: 'token',
          username: res.auth?.basic?.userName || '',
          password: res.auth?.basic?.password || '',
          apiKey: res.auth?.apikey[0]?.key || '',
          keyPlacement: res.auth?.apikey[0]?.type || '',
          keyName: res.auth?.apikey[0]?.value || '',
          bosPartnerCode: res.bosPartnerCode,
          partner: res.partnerId,
          vrwarehouse: res.warehousesIdList,
          syncTime: res.reportFrequency,
          frequency: res.interval,
          startDate: dayjs(dayjs(res.syncStart).format('DD/MM/YYYY'), 'DD/MM/YYYY') || null,
          startTime: startTime,
          endTime: endTime,
          rangeTime: rangeTime,
          dayOfMonth: res.reportFrequency == 3 && frequencyMonth == 'Ngày cố định hàng tháng' ? day : '1',
          dayOfWeek: res.reportFrequency == 2 ? day : null,
          weekOfMonth: week || null,
          dayOfWeekInMonth:
            res.reportFrequency == 3 && frequencyMonth == 'Thứ trong tuần cố định hàng tháng' ? res.day[0] : null,
          frequencyMonth: frequencyMonth,
          requestObject: requestObject,
          responseObject: responseObject,
          dropdownRequestObject: dropdownRequestObject,
          dropdownResponseObject: dropdownResponseObject,
          isUsingHMAC: !!res.apiConfig?.securities?.hmac?.secretKey,
          hmacParams: res.apiConfig?.securities?.hmac?.participants,
          hmacSeparator: res.apiConfig?.securities?.hmac?.separator,
          hmacAlgorithm: res.apiConfig?.securities?.hmac?.encode,
          hmacSecretKey: res.apiConfig?.securities?.hmac?.secretKey,
          hmacTechniqueName: res.apiConfig?.securities?.hmac?.field
        })
      }, 0)

      setPathVariables(res.apiConfig?.url?.variables)
      setParams(res.apiConfig?.url?.params)

      return res
    },
    initialData: {}
  })

  const updateConnection = useMutation({
    mutationFn: (data: any) => warehouseInstance.updateConnection(id, data),
    onSuccess: () => {
      message.success('Cập nhật kết nối thành công!')
      queryClient.invalidateQueries({ queryKey: ['connectConfigDetail'] })
      router.back()
    },
    onError: () => {
      message.error('Có lỗi khi cập nhật kết nối')
    }
  })

  const router = useRouter()

  const authMethod = Form.useWatch('authentication', form)

  const syncTime = Form.useWatch('syncTime', form)

  const frequencyMonth = Form.useWatch('frequencyMonth', form)

  const frequency = Form.useWatch('frequency', form)

  const httpType = Form.useWatch('httpType', form)

  const connectionType = Form.useWatch('connectionType', form)

  const isUsingHMAC = Form.useWatch('isUsingHMAC', form)

  const [paramsSearchWarehouse, setParamsSearchWarehouse] = useState<any>({
    isName: 1,
    status: 1,
    partnerIds: 'ALL'
  })

  useEffect(() => {
    if (connectionType === 'PULL_API') {
      setIsRequiredRequestObject(false)
    } else {
      setIsRequiredRequestObject(true)
    }
  }, [connectionType])

  const { onUrlChange, onPathVariableChange, onParamChange, validateUniqueKey, validateUrlNoDuplicates } =
    useUrlApiLogic(form, setPathVariables, setParams)

  const [jsonCheckConnection, setJsonCheckConnection] = React.useState<any>({})

  const [openConnection, setOpenConnection] = React.useState(false)

  const handleSubmit = () => {
    form
      .validateFields()
      .then(() => {
        if (!validateDataMapping(form.getFieldsValue())) {
          return
        }

        const dataSend = buildConnectionsJson(form.getFieldsValue())

        updateConnection.mutate(dataSend)
      })
      .catch(error => {
        console.error('Validation failed:', error)
      })
  }

  const openConnectionFunc = () => {
    const paths = pathVariables.flatMap((_, idx) => [
      ['pathVariables', idx, 'key'],
      ['pathVariables', idx, 'value']
    ])

    form
      .validateFields([
        'urlApi',
        'httpType',
        'authentication',
        'tokenValue',
        'username',
        'password',
        'apiKey',
        'keyPlacement',
        'keyName',
        ...paths
      ])
      .then(() => {
        const template = form.getFieldValue('urlApi') || ''
        const pathVariables = form.getFieldValue('pathVariables') || []

        const urlApi = buildCompleteUrl({ template, pathVariables })

        const sendData = {
          ...form.getFieldsValue(),
          urlApi
        }

        setJsonCheckConnection(sendData)
        setOpenConnection(true)
      })
      .catch(error => {
        console.error('Validation failed:', error)
      })
  }

  const handleBack = () => {
    router.back()
  }

  const updateWarehouseParams = useCallback(
    (value: any, bosPartnerCode: any, partnerLabel: any) => {
      if (!bosPartnerCode && !partnerLabel) return

      form.setFieldsValue({
        bosPartnerCode,
        vrwarehouse: []
      })

      setParamsSearchWarehouse({
        isName: 1,
        status: 1,
        partnerIds: partnerLabel
      })
    },
    [form, setParamsSearchWarehouse]
  )

  return (
    <Spin spinning={isLoadingDetail || updateConnection.isPending}>
      <div className='my-3 flex flex-1 p-2'>
        <HeaderSection title='Sửa cấu hình kết nối' onBack={handleBack} />
      </div>
      <div className='m-4 space-y-6'>
        <Form form={form} layout='vertical' onFinish={handleSubmit}>
          <Space direction='vertical' size='middle' className='w-full'>
            <CustomHeaderCollapse title='Thông tin chung'>
              <div className='grid grid-cols-3 gap-4'>
                <Form.Item
                  label='Tên kết nối'
                  name='name'
                  rules={[
                    { required: true, message: 'Tên kết nối không được bỏ trống' },
                    {
                      validator: (_, value) => {
                        if (value && value.trim().length === 0) {
                          return Promise.reject('Tên kết nối không được bỏ trống')
                        }

                        return Promise.resolve()
                      }
                    }
                  ]}
                >
                  <Input placeholder='Nhập tên kết nối' maxLength={100}></Input>
                </Form.Item>
                <Form.Item
                  label='Mã kết nối'
                  name='code'
                  rules={[{ required: true, message: 'Mã kết nối không được bỏ trống' }]}
                >
                  <Input placeholder='Nhập mã kết nối' disabled></Input>
                </Form.Item>
                {/*<div></div>*/}
                <Form.Item
                  label='Loại kết nối'
                  name='connectionType'
                  rules={[{ required: true, message: 'Loại kết nối không được bỏ trống' }]}
                >
                  <Select placeholder='Chọn loại kết nối' options={connectionTypeList} disabled></Select>
                </Form.Item>
              </div>
              <div className='grid grid-cols-2 gap-4'>
                <SelectPartner onChange={updateWarehouseParams}></SelectPartner>
                <Form.Item label='Kho ảo' name='vrwarehouse'>
                  <SelectCustom
                    searchFn={warehouseInstance.getAllVirtualWarehouse}
                    mode='multiple'
                    queryKey='searchWarehouse'
                    searchKey='value'
                    additionalParams={paramsSearchWarehouse}
                    placeholder='Chọn kho ảo'
                    optionLabel='name'
                    optionValue='id'
                    allowClear
                  />
                </Form.Item>
              </div>
            </CustomHeaderCollapse>
            <CustomHeaderCollapse title='Thông tin kết nối'>
              <div className='grid grid-cols-3 gap-4'>
                <Form.Item
                  label={
                    <div className='text-sm'>
                      Mã đối tác tích hợp{' '}
                      <Tooltip title='Mã tích hợp đối tác'>
                        <InfoCircleOutlined />{' '}
                      </Tooltip>
                    </div>
                  }
                  name='bosPartnerCode'
                >
                  <Input placeholder='Nhập mã đối tác tích hợp' maxLength={50}></Input>
                </Form.Item>
                <Form.Item
                  label='Phương thức HTTP'
                  name='httpType'
                  rules={[{ required: true, message: 'Phương thức http không được bỏ trống' }]}
                >
                  <Select placeholder='Chọn phương thức http' options={httpList}></Select>
                </Form.Item>
                <Form.Item
                  label='URL API liệt kê kho của đối tác'
                  name='urlApi'
                  rules={[
                    { required: true, message: 'Đường dẫn không được bỏ trống' },
                    { validator: validateUrlNoDuplicates }
                  ]}
                >
                  <Input placeholder='Nhập URL API' onChange={onUrlChange}></Input>
                </Form.Item>
              </div>

              {(pathVariables?.length > 0 || params?.length > 0) && (
                <div className='mb-4 flex flex-col gap-3 rounded-xl bg-gray-12 p-3'>
                  {params?.length > 0 && (
                    <EmptyFormItem label='Params'>
                      <div className='flex w-full flex-col gap-3'>
                        {params.map((param, idx) => (
                          <Space.Compact key={`query-${param.key}-${idx}`} block>
                            <EmptyFormItem
                              name={['params', idx, 'key']}
                              rules={[
                                { required: true, message: 'Key không được bỏ trống' },
                                {
                                  validator: (_, value) => validateUniqueKey(value, idx, 'params')
                                }
                              ]}
                              className='w-full'
                            >
                              <Input
                                className='border-r-0'
                                prefix='Key:'
                                maxLength={50}
                                onChange={e => onParamChange(idx, 'key', e.target.value)}
                              />
                            </EmptyFormItem>
                            <EmptyFormItem name={['params', idx, 'value']} className='w-full'>
                              <Input
                                prefix='Value:'
                                maxLength={50}
                                onChange={e => onParamChange(idx, 'value', e.target.value)}
                              />
                            </EmptyFormItem>
                          </Space.Compact>
                        ))}
                      </div>
                    </EmptyFormItem>
                  )}

                  {pathVariables?.length > 0 && (
                    <EmptyFormItem label='Path Variables'>
                      <div className='grid grid-cols-3 gap-4'>
                        {pathVariables.map((param, idx) => (
                          <Space.Compact key={idx} block>
                            <EmptyFormItem
                              name={['pathVariables', idx, 'key']}
                              rules={[
                                {
                                  validator: (_, value) => validateUniqueKey(value, idx, 'pathVariables')
                                }
                              ]}
                            >
                              <Input
                                className='border-r-0'
                                prefix='Key:'
                                maxLength={50}
                                onChange={e => onPathVariableChange(idx, e.target.value)}
                              />
                            </EmptyFormItem>

                            <EmptyFormItem
                              name={['pathVariables', idx, 'value']}
                              rules={[{ required: true, message: 'Value không được bỏ trống' }]}
                            >
                              <Input prefix='Value:' maxLength={50} />
                            </EmptyFormItem>
                          </Space.Compact>
                        ))}
                      </div>
                    </EmptyFormItem>
                  )}
                </div>
              )}

              <div className='grid grid-cols-3 gap-4'>
                <Form.Item
                  label='Phương thức xác thực API'
                  name='authentication'
                  rules={[{ required: true, message: 'Vui lòng thiết lập phương thức xác thực API' }]}
                  initialValue={'bearer'}
                >
                  <Select placeholder='Chọn phương thức xác thực API' options={authenticationList} />
                </Form.Item>

                {authMethod == 'bearer' && (
                  <>
                    <Form.Item
                      label='Token Value'
                      name='tokenValue'
                      rules={[{ required: true, message: 'Token Value không được bỏ trống' }]}
                    >
                      <Input placeholder='Nhập Token Value'></Input>
                    </Form.Item>
                  </>
                )}
                {authMethod == 'apikey' && (
                  <>
                    <Form.Item
                      label='API Key'
                      name='apiKey'
                      rules={[{ required: true, message: 'API Key không được bỏ trống' }]}
                    >
                      <Input placeholder='Nhập API Key'></Input>
                    </Form.Item>
                    <Form.Item
                      label='Key Placement'
                      name='keyPlacement'
                      rules={[{ required: true, message: 'Key Placement không được bỏ trốn' }]}
                    >
                      <Select placeholder='Chọn phương thức xác thực API' options={keyPlacementList} />
                    </Form.Item>
                    <Form.Item
                      label='Key Name'
                      name='keyName'
                      rules={[{ required: true, message: 'Key Name không được bỏ trốn' }]}
                    >
                      <Input placeholder='Nhập Key Name'></Input>
                    </Form.Item>
                  </>
                )}

                {authMethod == 'basic' && (
                  <>
                    <Form.Item
                      label='Username'
                      name='username'
                      rules={[{ required: true, message: 'Username không được bỏ trống' }]}
                    >
                      <Input placeholder='Nhập Username'></Input>
                    </Form.Item>
                    <Form.Item
                      label='Password'
                      name='password'
                      rules={[{ required: true, message: 'Password không được bỏ trốn' }]}
                    >
                      <Input placeholder='Nhập Password' type='password'></Input>
                    </Form.Item>
                  </>
                )}
              </div>
              {httpType != 'GET' && (
                <div className='grid grid-cols-3 gap-4'>
                  <Form.Item name='isUsingHMAC' valuePropName='checked'>
                    <Checkbox>Sử dụng xác thực HMAC</Checkbox>
                  </Form.Item>
                </div>
              )}
              {isUsingHMAC && (
                <>
                  <div className='grid grid-cols-2 gap-4'>
                    <Form.Item
                      label='Secret Key'
                      name='hmacSecretKey'
                      rules={[{ required: true, message: 'Secret Key không được bỏ trống' }]}
                      normalize={value => value?.trim()}
                    >
                      <Input placeholder='Nhập Secret Key' maxLength={250}></Input>
                    </Form.Item>
                    <Form.Item
                      label='Thuật toán'
                      name='hmacAlgorithm'
                      rules={[{ required: true, message: 'Thuật toán không được bỏ trống' }]}
                    >
                      <Select placeholder='Chọn thuật toán' options={hmacList}></Select>
                    </Form.Item>
                  </div>
                  <div className='grid grid-cols-3 gap-4'>
                    <Form.Item
                      label='Tên kỹ thuật'
                      name='hmacTechniqueName'
                      rules={[{ required: true, message: 'Tên kỹ thuật không được bỏ trống' }]}
                      normalize={value => value?.trim()}
                    >
                      <Input placeholder='Nhập tên kỹ thuật' maxLength={250}></Input>
                    </Form.Item>
                    <Form.Item
                      label={
                        <div className='whitespace-nowrap'>
                          <span className='mr-1 text-red-500'>*</span>
                          Tham số
                        </div>
                      }
                      name='hmacParams'
                      rules={[{ validator: validateHMACParams(hmacParamsList) }]}
                    >
                      <Select
                        placeholder='Chọn thuật toán'
                        mode='multiple'
                        showSearch
                        options={hmacParamsOptions}
                      ></Select>
                    </Form.Item>
                    <Form.Item
                      label='Ký tự ngăn cách'
                      name='hmacSeparator'
                      normalize={value => value?.trim()}
                      rules={[{ validator: validatorHMACSeparator }]}
                    >
                      <Input placeholder='Nhập ký tự ngăn cách' maxLength={250}></Input>
                    </Form.Item>
                  </div>
                </>
              )}
            </CustomHeaderCollapse>

            {connectionType == 'PULL_API' && (
              <CustomHeaderCollapse title='Lịch trình đồng bộ'>
                <ScheduleSyncData
                  syncTime={syncTime}
                  frequency={frequency}
                  frequencyMonth={frequencyMonth}
                ></ScheduleSyncData>
              </CustomHeaderCollapse>
            )}
            <DataMapping
              isRequiredRequestObject={isRequiredRequestObject}
              reqCode='DTO_REQ_INVENTORY_SYNC'
              resCode='DTO_RESP_INVENTORY_SYNC'
              form={form}
              initData={initRequestResponse}
              isVisibleRequestObject={httpType != 'GET'}
              returnRequestDataRowsList={data => {
                setHmacParamsList(data)

                // So sánh list mới và cũ, nếu có sự thay đổi thì reset lại trường hmacParams
                if (JSON.stringify(data) !== JSON.stringify(hmacParamsList)) {
                  form.setFieldsValue({
                    hmacParams: undefined
                  })
                }
              }}
            ></DataMapping>
          </Space>
        </Form>

        <ModalCheckConnection
          open={openConnection}
          setOpen={setOpenConnection}
          initValue={jsonCheckConnection}
        ></ModalCheckConnection>
      </div>
      <div className='h-16'></div>
      <div className='fixed bottom-0 left-0 z-50 w-[95%]'>
        <div className='flex w-full items-center justify-between gap-4 rounded-2xl bg-white p-4 shadow-[0_-4px_12px_rgba(0,0,0,0.1)]'>
          <div className='flex items-center gap-4'></div>
          <div className='flex items-center gap-4'>
            <Button onClick={() => openConnectionFunc()}>Kiểm tra kết nối</Button>
            <Button color='primary' variant='outlined' onClick={handleBack}>
              Hủy
            </Button>
            <Button type='primary' onClick={handleSubmit}>
              Lưu
            </Button>
          </div>
        </div>
      </div>
    </Spin>
  )
}
