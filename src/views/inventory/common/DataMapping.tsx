import React, { useEffect, useState } from 'react'

import { Collapse, Upload, Table, Button, Tabs, Select, Tag, Spin } from 'antd'

import { styled } from '@mui/material'

import clsx from 'clsx'

import { useJsonMapper } from '@views/inventory/common/hooks/useJsonMapper'

import { message } from '@components/notification'

import ConvertDataConfig from '@views/inventory/common/ConvertDataConfig'

import FormatTags from './FormatTags'

import { EmptyFormItem } from '@views/auth/register/components/RegisterPersonalForm'

const StyledCollapse = styled(Collapse)`
  .ant-collapse-content-box {
    padding-block-start: 0 !important;
  }
  background-color: white;
`

const EmptyTabNavigation = styled(Tabs)`
  &.ant-tabs > .ant-tabs-nav {
    display: none;
  }
`

const UploadFullWidth = styled(Upload)`
  .ant-upload {
    inline-size: 100%;
    display: flex;
  }
`

type Props = {
  reqCode: string
  resCode: string
  form: any
  initData?: any
  isUseInPopUp?: boolean
  isRequiredRequestObject?: boolean
  isRequiredResponseObject?: boolean
  isVisibleRequestObject?: boolean
  isVisibleResponseObject?: boolean
  returnRequestDataRowsList?: (data: any) => void
}

export default function DataMapping({
  reqCode,
  resCode,
  form,
  initData,
  isUseInPopUp = false,
  isRequiredRequestObject = true,
  isRequiredResponseObject = true,
  isVisibleRequestObject = true,
  isVisibleResponseObject = true,
  returnRequestDataRowsList
}: Props) {
  const req = useJsonMapper(reqCode)
  const res = useJsonMapper(resCode)

  const [srcField, setSrcField] = useState<any>(null)
  const [dstField, setDstField] = useState<any>(null)
  const [srcFormat, setSrcFormat] = useState<any>(null)
  const [dstFormat, setDstFormat] = useState<any>(null)
  const [initConfigData, setInitConfigData] = useState<any>(null)

  const [activeTab, setActiveTab] = useState<'request' | 'response'>(isVisibleRequestObject ? 'request' : 'response')
  const [openConvertDataConfig, setOpenConvertDataConfig] = useState(false)

  const [dataRestored, setDataRestored] = useState(false)

  useEffect(() => {
    form.setFieldsValue({
      requestObject: req.mappingRows,
      responseObject: res.mappingRows,
      dropdownRequestObject: req.dataRows,
      dropdownResponseObject: res.dataRows
    })

    if (req.dataRows.length > 0 && returnRequestDataRowsList) {
      returnRequestDataRowsList(req.dataRows)
    }
  }, [req.mappingRows, res.mappingRows, form, req.dataRows, res.dataRows, returnRequestDataRowsList])

  // Effect để restore dữ liệu từ initData
  useEffect(() => {
    if (
      !dataRestored &&
      initData &&
      (initData.requestObject?.length > 0 ||
        initData.responseObject?.length > 0 ||
        initData.dropdownRequestObject?.length > 0 ||
        initData.dropdownResponseObject?.length > 0)
    ) {
      console.log('Restoring data from initData:', initData)

      // Restore request data
      if (initData.requestObject?.length > 0) {
        req.setMappingRows(initData.requestObject)
      }

      // Restore response data
      if (initData.responseObject?.length > 0) {
        res.setMappingRows(initData.responseObject)
      }

      // Restore request dropdown data
      if (initData.dropdownRequestObject?.length > 0) {
        req.setDataRows(initData.dropdownRequestObject)
      }

      // Restore response dropdown data
      if (initData.dropdownResponseObject?.length > 0) {
        res.setDataRows(initData.dropdownResponseObject)
      }

      setDataRestored(true)
    }
  }, [initData, req, res, dataRestored])

  useEffect(() => {
    form.setFieldsValue({
      isRequiredRequestObject,
      isRequiredResponseObject
    })
  }, [form, isRequiredRequestObject, isRequiredResponseObject])

  useEffect(() => {
    form.setFieldsValue({
      isVisibleRequestObject,
      isVisibleResponseObject
    })
  }, [form, isVisibleRequestObject, isVisibleResponseObject])

  useEffect(() => {
    if (!isVisibleRequestObject) {
      setActiveTab('response')
    }

    if (!isVisibleResponseObject) {
      setActiveTab('request')
    }
  }, [isVisibleRequestObject, isVisibleResponseObject])

  const onClickConvertDataConfig = (record: any) => {
    const newSrcField = activeTab === 'request' ? record.name : record.sourceField
    const newDstField = activeTab === 'request' ? record.sourceField : record.name

    const newSrcFormat =
      activeTab === 'request'
        ? record.type
        : res.dataRows.find((item: any) => item.value === record.sourceField)?.sourceDataType

    const newDstFormat =
      activeTab === 'request'
        ? req.dataRows.find((item: any) => item.value === record.sourceField)?.sourceDataType
        : record.type

    if (record.mappingData) {
      const initData = record.mappingData

      setInitConfigData(initData)
    } else {
      setInitConfigData(null)
    }

    setSrcField(newSrcField)
    setDstField(newDstField)
    setSrcFormat(newSrcFormat)
    setDstFormat(newDstFormat)

    if (newSrcField && newDstField && newSrcFormat && newDstFormat) {
      setOpenConvertDataConfig(true)
    } else {
      message.error('Vui lòng chọn trường nguồn')
    }
  }

  const columns = [
    {
      title: 'Trường đich',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: 'Kiểu dữ liệu đích',
      dataIndex: 'type',
      key: 'type'
    },
    {
      title: 'Format đích',
      dataIndex: 'type',
      key: 'type',
      width: 300,
      render: (value: string, record: any) => {
        return <FormatTags format={record.format} maxWidth={250} />
      }
    },
    {
      title: 'Trường nguồn',
      dataIndex: 'sourceField',
      key: 'sourceField',
      width: 300,
      render: (value: string, record: any) => {
        return (
          <>
            <Select
              options={activeTab === 'request' ? req.dataRows : res.dataRows}
              value={record.sourceField}
              allowClear
              showSearch
              onChange={(val: string) => {
                const selectedItem =
                  activeTab === 'request'
                    ? req.dataRows.find(item => item.value === val)
                    : res.dataRows.find(item => item.value === val)

                activeTab === 'request'
                  ? req.setMappingRows(rows =>
                      rows.map(r =>
                        r.key === record.key
                          ? {
                              ...r,
                              sourceField: val,
                              sourceExampleValue: selectedItem?.sourceExampleValue,
                              sourceDataType: selectedItem?.sourceDataType,
                              mappingData: null
                            }
                          : r
                      )
                    )
                  : res.setMappingRows(rows =>
                      rows.map(r =>
                        r.key === record.key
                          ? {
                              ...r,
                              sourceField: val,
                              sourceExampleValue: selectedItem?.sourceExampleValue,
                              sourceDataType: selectedItem?.sourceDataType,
                              mappingData: null
                            }
                          : r
                      )
                    )
              }}
            ></Select>
          </>
        )
      }
    },
    {
      title: 'Kiểu dữ liệu nguồn',
      dataIndex: 'typeSource',
      key: 'typeSource',
      render: (value: string, record: any) => {
        return (
          <div>
            {activeTab === 'request'
              ? req.dataRows.find((item: any) => item.value === record.sourceField)?.sourceDataType
              : res.dataRows.find((item: any) => item.value === record.sourceField)?.sourceDataType}
          </div>
        )
      }
    },
    {
      title: 'Trạng thái',
      dataIndex: 'mandatory',
      key: 'mandatory',
      render: (value: boolean) => {
        return <div>{value ? <Tag color='error'>Bắt buộc</Tag> : <Tag color='success'>Không bắt buộc</Tag>}</div>
      }
    },
    {
      title: 'Chuyển đổi',
      dataIndex: 'mappingData',
      key: 'mappingData',
      render: (value: any, record: any) => {
        return (
          <div>
            {!value ? (
              <>
                <Button onClick={() => onClickConvertDataConfig(record)}>
                  <i className='onedx-setting1 size-4' /> Cấu hình
                </Button>
              </>
            ) : (
              <>
                <Button onClick={() => onClickConvertDataConfig(record)}>
                  <i className='onedx-eye size-4' /> Xem chi tiết
                </Button>
              </>
            )}
          </div>
        )
      }
    }
  ]

  const current = activeTab === 'request' ? req : res

  const headerNode = (
    <div className='flex w-full items-center justify-between'>
      <div className='border-l-4 border-solid border-yellow-6'>
        <div className='pl-2 text-base font-semibold'>Ánh xạ dữ liệu</div>
      </div>

      <div>
        <Button.Group>
          {isVisibleRequestObject && (
            <Button
              type='text'
              onClick={e => {
                e.stopPropagation()
                setActiveTab('request')
              }}
              className={clsx(
                'rounded-none !border-0 !border-b-2 px-4 py-1 font-medium',
                activeTab === 'request' ? 'border-blue-500 text-blue-500' : 'border-transparent text-gray-800'
              )}
            >
              Request
            </Button>
          )}
          <Button
            type='text'
            onClick={e => {
              e.stopPropagation()
              setActiveTab('response')
            }}
            className={clsx(
              'rounded-none !border-0 !border-b-2 px-4 py-1 font-medium',
              activeTab === 'response' ? 'border-blue-500 text-blue-500' : 'border-transparent text-gray-800'
            )}
          >
            Response
          </Button>
        </Button.Group>
        {current.dataRows.length > 0 && (
          <Button
            type='text'
            onClick={e => {
              e.stopPropagation()
              current.replaceFile()
            }}
          >
            <i className='onedx-edit size-5' /> Cập nhật file tải lên
          </Button>
        )}
      </div>
    </div>
  )

  return (
    <Spin spinning={req.isLoadingFormatAPI || res.isLoadingFormatAPI}>
      <StyledCollapse
        className={clsx(!isUseInPopUp ? 'bg-white' : 'bg-gray-12')}
        ghost
        expandIconPosition='right'
        defaultActiveKey={['1']}
      >
        <Collapse.Panel key='1' header={headerNode}>
          <div className='border-t border-solid border-slate-200 py-2'>
            {/* Hidden file input for file replacement */}
            <input
              ref={req.fileInputRef}
              type='file'
              accept='.json'
              style={{ display: 'none' }}
              onChange={req.handleFileReplace}
            />
            <input
              ref={res.fileInputRef}
              type='file'
              accept='.json'
              style={{ display: 'none' }}
              onChange={res.handleFileReplace}
            />

            <EmptyFormItem name='requestObject' hidden />
            <EmptyFormItem name='responseObject' hidden />
            <EmptyFormItem name='dropdownResponseObject' hidden />
            <EmptyFormItem name='dropdownRequestObject' hidden />
            <EmptyFormItem name='isRequiredRequestObject' hidden />
            <EmptyFormItem name='isRequiredResponseObject' hidden />
            <EmptyFormItem name='isVisibleRequestObject' hidden />
            <EmptyFormItem name='isVisibleResponseObject' hidden />

            <EmptyTabNavigation activeKey={activeTab}>
              {isVisibleRequestObject && (
                <Tabs.TabPane key='request'>
                  <EmptyFormItem name='requestFile'>
                    <div className='flex flex-col gap-4 rounded-lg bg-bg-neutral-lightest p-3'>
                      {req.dataRows.length === 0 ? (
                        <>
                          <label
                            className={clsx(
                              isRequiredRequestObject &&
                                "before:mr-1 before:inline-block before:font-[SimSun,sans-serif] before:text-sm before:leading-none before:text-[#ff4d4f] before:content-['*']"
                            )}
                          >
                            Tải file JSON lên (Request)
                          </label>
                          <UploadFullWidth
                            accept='.json'
                            fileList={req.fileList}
                            beforeUpload={() => false}
                            onChange={req.onUploadChange}
                            className='w-full rounded-lg border border-dashed border-text-info-default bg-white pl-3'
                          >
                            <div onClickCapture={e => e.preventDefault()}>
                              <div className='flex items-center justify-center gap-6 py-2'>
                                <div className='flex items-center justify-center rounded-xl bg-bg-primary-lighter p-[10px]'>
                                  <i className='onedx-json size-8' />
                                </div>
                                <div className='text-left'>
                                  <div className='body-14-medium text-text-neutral-strong'>
                                    Tải file JSON lên hoặc kéo thả
                                  </div>
                                  <div className='mt-2 text-caption-12 text-text-neutral-medium'>
                                    <>
                                      Định dạng file <strong>JSON</strong> - Dung lượng <strong>(&lt;{1}MB)</strong> -
                                      Kích thước
                                      <strong> (1170.390px)</strong>
                                    </>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </UploadFullWidth>
                        </>
                      ) : (
                        <Table columns={columns} dataSource={req.mappingRows} pagination={false} className='bg-white' />
                      )}
                    </div>
                  </EmptyFormItem>
                </Tabs.TabPane>
              )}

              {isVisibleResponseObject && (
                <Tabs.TabPane key='response'>
                  <EmptyFormItem name='requestFile'>
                    <div className='flex flex-col gap-4 rounded-lg bg-bg-neutral-lightest p-3'>
                      {res.dataRows.length === 0 ? (
                        <>
                          <label
                            className={clsx(
                              isRequiredResponseObject &&
                                "before:mr-1 before:inline-block before:font-[SimSun,sans-serif] before:text-sm before:leading-none before:text-[#ff4d4f] before:content-['*']"
                            )}
                          >
                            Tải file JSON lên (Response)
                          </label>
                          <UploadFullWidth
                            accept='.json'
                            fileList={res.fileList}
                            beforeUpload={() => false}
                            onChange={res.onUploadChange}
                            className='w-full rounded-lg border border-dashed border-text-info-default bg-white pl-3'
                          >
                            <div onClickCapture={e => e.preventDefault()}>
                              <div className='flex items-center justify-center gap-6 py-2'>
                                <div className='flex items-center justify-center rounded-xl bg-bg-primary-lighter p-[10px]'>
                                  <i className='onedx-json size-8' />
                                </div>
                                <div className='text-left'>
                                  <div className='body-14-medium text-text-neutral-strong'>
                                    Tải file JSON lên hoặc kéo thả
                                  </div>
                                  <div className='mt-2 text-caption-12 text-text-neutral-medium'>
                                    <>
                                      Định dạng file <strong>JSON</strong> - Dung lượng <strong>(&lt;{1}MB)</strong> -
                                      Kích thước
                                      <strong> (1170.390px)</strong>
                                    </>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </UploadFullWidth>
                        </>
                      ) : (
                        <Table columns={columns} dataSource={res.mappingRows} pagination={false} className='bg-white' />
                      )}
                    </div>
                  </EmptyFormItem>
                </Tabs.TabPane>
              )}
            </EmptyTabNavigation>
          </div>
        </Collapse.Panel>
        <ConvertDataConfig
          srcField={srcField}
          dstField={dstField}
          srcFormat={srcFormat}
          dstFormat={dstFormat}
          initData={initConfigData}
          open={openConvertDataConfig}
          setOpen={setOpenConvertDataConfig}
          returnData={data => {
            activeTab === 'request'
              ? req.setMappingRows(rows =>
                  rows.map(r => (r.name === data.srcField ? { ...r, mappingData: data.item } : r))
                )
              : res.setMappingRows(rows =>
                  rows.map(r => (r.name === data.dstField ? { ...r, mappingData: data.item } : r))
                )
          }}
        />
      </StyledCollapse>
    </Spin>
  )
}
