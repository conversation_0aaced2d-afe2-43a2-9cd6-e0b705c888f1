import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useState } from 'react'

import { useQuery } from '@tanstack/react-query'

import { styled } from 'styled-components'

import { clsx } from 'clsx'

import { Checkbox, Collapse, Form, Input, Select, Space, Spin, Tooltip } from 'antd'

import { InfoCircleOutlined } from '@ant-design/icons'

import { SelectPartner } from '@views/inventory/common/SelectPartner'

import ScheduleSyncData from '@views/inventory/common/ScheduleSyncData'

import { useUrlApiLogic } from '@views/inventory/common/hooks/useUrlApiLogic'

import type { PathParam } from '@views/inventory/common/hooks/useUrlApiLogic'

import {
  buildCompleteUrl,
  validateHMACParams,
  validatorHMACSeparator
} from '@views/inventory/common/hooks/useConvertAndValidation'

import { SelectCustom } from '@views/inventory/common/SelectCustom'

import ModalCheckConnection from '@views/inventory/common/modal/ModalCheckConnection'

import warehouseInstance from '@/models/Warehouse'

import { EmptyFormItem } from '@views/auth/register/components/RegisterPersonalForm'

import {
  authenticationList,
  connectionTypeList,
  hmacList,
  httpList,
  keyPlacementList
} from '@views/inventory/common/hooks/useDropDownValue'

import commonInstance from '@/models/Common'

import DataMapping from '@views/inventory/common/DataMapping'

export interface CreateConnectionProps {
  openConnection: boolean
  isPopUp?: boolean
  setOpenConnection: (open: boolean) => void
  handleSubmit: (values: any) => void
}

export interface CreateConnectionRef {
  submit: () => void
  checkConnection: () => void
}

const StyledCollapse = styled(Collapse)<{ $isPopUp: boolean }>`
  .ant-collapse-content-box {
    padding-top: 0 !important;
  }
  background-color: ${({ $isPopUp }) => (!$isPopUp ? 'white !important' : '#02173c06 !important')};
`

const CreateConnection = forwardRef<CreateConnectionRef, CreateConnectionProps>(
  ({ openConnection, isPopUp = false, setOpenConnection, handleSubmit }, ref) => {
    const [form] = Form.useForm()

    const authMethod = Form.useWatch('authentication', form)

    const syncTime = Form.useWatch('syncTime', form)

    const frequencyMonth = Form.useWatch('frequencyMonth', form)

    const frequency = Form.useWatch('frequency', form)

    const httpType = Form.useWatch('httpType', form)

    const connectionType = Form.useWatch('connectionType', form)

    const isUsingHMAC = Form.useWatch('isUsingHMAC', form)

    const [jsonCheckConnection, setJsonCheckConnection] = React.useState<any>({})

    const [pathVariables, setPathVariables] = useState<PathParam[]>([])

    const [params, setParams] = useState<PathParam[]>([])

    const [isRequiredRequestObject, setIsRequiredRequestObject] = React.useState(true)

    const [hmacParamsList, setHmacParamsList] = useState<any>([])

    const hmacParamsOptions = useMemo(() => {
      return hmacParamsList || []
    }, [hmacParamsList])

    const [paramsSearchWarehouse, setParamsSearchWarehouse] = useState<any>({
      isName: 1,
      status: 1,
      partnerIds: 'ALL'
    })

    const { onUrlChange, onPathVariableChange, onParamChange, validateUniqueKey, validateUrlNoDuplicates } =
      useUrlApiLogic(form, setPathVariables, setParams)

    useEffect(() => {
      if (connectionType === 'PULL_API') {
        setIsRequiredRequestObject(false)
      } else {
        setIsRequiredRequestObject(true)
      }
    }, [connectionType])

    useImperativeHandle(ref, () => ({
      submit: () => {
        form.submit()
      },
      checkConnection: () => {
        const paths = pathVariables.flatMap((_, idx) => [
          ['pathVariables', idx, 'key'],
          ['pathVariables', idx, 'value']
        ])

        form
          .validateFields([
            'urlApi',
            'httpType',
            'authentication',
            'tokenValue',
            'username',
            'password',
            'apiKey',
            'keyPlacement',
            'keyName',
            ...paths
          ])
          .then(() => {
            const template = form.getFieldValue('urlApi') || ''
            const pathVariables = form.getFieldValue('pathVariables') || []

            const urlApi = buildCompleteUrl({ template, pathVariables })

            const sendData = {
              ...form.getFieldsValue(),
              urlApi
            }

            setJsonCheckConnection(sendData)
            setOpenConnection(true)
          })
          .catch(error => {
            console.error('Validation failed:', error)
          })
      }
    }))

    const { isFetching: isLoadingDefaultCode } = useQuery({
      queryKey: ['dataWarehouseCode'],
      queryFn: async () => {
        // Lấy nameId cho case thêm thuộc tính mới
        const code = await commonInstance.getCode({ type: 'PARTNER_APIS' })

        form.setFieldValue('code', code.code)
      },
      enabled: true
    })

    const updateWarehouseParams = useCallback(
      (value: any, bosPartnerCode: any, partnerLabel: any) => {
        if (!bosPartnerCode && !partnerLabel) return

        form.setFieldsValue({
          bosPartnerCode,
          vrwarehouse: []
        })

        setParamsSearchWarehouse({
          isName: 1,
          status: 1,
          partnerIds: partnerLabel
        })
      },
      [form, setParamsSearchWarehouse]
    )

    return (
      <Spin spinning={isLoadingDefaultCode}>
        <Form form={form} layout='vertical' onFinish={handleSubmit}>
          <Space direction='vertical' size='middle' className='w-full'>
            <StyledCollapse
              $isPopUp={isPopUp}
              className={clsx(!isPopUp ? '!bg-white' : '!bg-gray-12')}
              expandIconPosition='right'
              defaultActiveKey={['1']}
              ghost
            >
              <Collapse.Panel
                key='1'
                header={
                  <div className='border-l-4 border-solid border-yellow-6'>
                    <div className='pl-2 text-base font-semibold'>Thông tin chung</div>
                  </div>
                }
              >
                <div className='grid grid-cols-3 gap-4 border-t border-solid border-slate-200 py-2'>
                  <Form.Item
                    label='Tên kết nối'
                    name='name'
                    rules={[
                      { required: true, message: 'Tên kết nối không được bỏ trống' },
                      {
                        validator: (_, value) => {
                          if (value && value.trim().length === 0) {
                            return Promise.reject('Tên kết nối không được bỏ trống')
                          }

                          return Promise.resolve()
                        }
                      }
                    ]}
                  >
                    <Input placeholder='Nhập tên kết nối' maxLength={100}></Input>
                  </Form.Item>
                  <Form.Item
                    label='Mã kết nối'
                    name='code'
                    rules={[{ required: true, message: 'Mã kết nối không được bỏ trống' }]}
                  >
                    <Input placeholder='Nhập mã kết nối' disabled></Input>
                  </Form.Item>
                  {/*<div></div>*/}
                  <Form.Item
                    label='Loại kết nối'
                    name='connectionType'
                    rules={[{ required: true, message: 'Loại kết nối không được bỏ trống' }]}
                  >
                    <Select placeholder='Chọn loại kết nối' options={connectionTypeList}></Select>
                  </Form.Item>
                </div>
                <div className='grid grid-cols-2 gap-4'>
                  <SelectPartner onChange={updateWarehouseParams}></SelectPartner>
                  <Form.Item label='Kho ảo' name='vrwarehouse'>
                    <SelectCustom
                      searchFn={warehouseInstance.getAllVirtualWarehouse}
                      mode='multiple'
                      queryKey='searchWarehouse'
                      searchKey='value'
                      additionalParams={paramsSearchWarehouse}
                      placeholder='Chọn kho ảo'
                      optionLabel='name'
                      optionValue='id'
                      allowClear
                    />
                  </Form.Item>
                </div>
              </Collapse.Panel>
            </StyledCollapse>
            <StyledCollapse
              $isPopUp={isPopUp}
              className={clsx(!isPopUp ? 'bg-white' : 'bg-gray-12')}
              expandIconPosition='right'
              defaultActiveKey={['1']}
              ghost
            >
              <Collapse.Panel
                key='1'
                header={
                  <div className='border-l-4 border-solid border-yellow-6'>
                    <div className='pl-2 text-base font-semibold'>Thông tin kết nối</div>
                  </div>
                }
              >
                <div className='border-t border-solid border-slate-200 py-2'>
                  <div className='grid grid-cols-3 gap-4'>
                    <Form.Item
                      label={
                        <div className='text-sm'>
                          Mã đối tác tích hợp{' '}
                          <Tooltip title='Mã tích hợp đối tác'>
                            <InfoCircleOutlined />{' '}
                          </Tooltip>
                        </div>
                      }
                      name='bosPartnerCode'
                    >
                      <Input placeholder='Nhập mã đối tác tích hợp' maxLength={50}></Input>
                    </Form.Item>
                    <Form.Item
                      label='Phương thức HTTP'
                      name='httpType'
                      rules={[{ required: true, message: 'Phương thức http không được bỏ trống' }]}
                    >
                      <Select placeholder='Chọn phương thức http' options={httpList}></Select>
                    </Form.Item>
                    <Form.Item
                      label='URL API liệt kê kho của đối tác'
                      name='urlApi'
                      rules={[
                        { required: true, message: 'Đường dẫn không được bỏ trống' },
                        { validator: validateUrlNoDuplicates }
                      ]}
                    >
                      <Input placeholder='Nhập URL API' onChange={onUrlChange}></Input>
                    </Form.Item>
                  </div>
                  {(pathVariables.length > 0 || params.length > 0) && (
                    <div className='mb-4 flex flex-col gap-3 rounded-xl bg-gray-12 p-3'>
                      {params.length > 0 && (
                        <EmptyFormItem label='Params'>
                          <div className='flex w-full flex-col gap-3'>
                            {params.map((param, idx) => (
                              <Space.Compact key={`query-${param.key}-${idx}`} block>
                                <EmptyFormItem
                                  name={['params', idx, 'key']}
                                  rules={[
                                    { required: true, message: 'Key không được bỏ trống' },
                                    {
                                      validator: (_, value) => validateUniqueKey(value, idx, 'params')
                                    }
                                  ]}
                                  className='w-full'
                                >
                                  <Input
                                    className='border-r-0'
                                    prefix='Key:'
                                    onChange={e => onParamChange(idx, 'key', e.target.value)}
                                    maxLength={50}
                                  />
                                </EmptyFormItem>
                                <EmptyFormItem name={['params', idx, 'value']} className='w-full'>
                                  <Input
                                    prefix='Value:'
                                    onChange={e => onParamChange(idx, 'value', e.target.value)}
                                    maxLength={50}
                                  />
                                </EmptyFormItem>
                              </Space.Compact>
                            ))}
                          </div>
                        </EmptyFormItem>
                      )}

                      {pathVariables.length > 0 && (
                        <EmptyFormItem label='Path Variables'>
                          <div className='grid grid-cols-3 gap-4'>
                            {pathVariables.map((param, idx) => (
                              <Space.Compact key={idx} block>
                                <EmptyFormItem
                                  name={['pathVariables', idx, 'key']}
                                  rules={[
                                    {
                                      validator: (_, value) => validateUniqueKey(value, idx, 'pathVariables')
                                    }
                                  ]}
                                >
                                  <Input
                                    className='border-r-0'
                                    prefix='Key:'
                                    onChange={e => onPathVariableChange(idx, e.target.value)}
                                    maxLength={50}
                                  />
                                </EmptyFormItem>

                                <EmptyFormItem
                                  name={['pathVariables', idx, 'value']}
                                  rules={[{ required: true, message: 'Value không được bỏ trống' }]}
                                >
                                  <Input prefix='Value:' maxLength={50} />
                                </EmptyFormItem>
                              </Space.Compact>
                            ))}
                          </div>
                        </EmptyFormItem>
                      )}
                    </div>
                  )}
                  <div className='grid grid-cols-3 gap-4'>
                    <Form.Item
                      label='Phương thức xác thực API'
                      name='authentication'
                      rules={[{ required: true, message: 'Vui lòng thiết lập phương thức xác thực API' }]}
                      initialValue={'bearer'}
                    >
                      <Select placeholder='Chọn phương thức xác thực API' options={authenticationList} />
                    </Form.Item>

                    {authMethod == 'bearer' && (
                      <>
                        <Form.Item
                          label='Token Value'
                          name='tokenValue'
                          rules={[{ required: true, message: 'Token Value không được bỏ trống' }]}
                          normalize={value => value?.trim()}
                        >
                          <Input placeholder='Nhập Token Value' maxLength={250}></Input>
                        </Form.Item>
                      </>
                    )}
                    {authMethod == 'apikey' && (
                      <>
                        <Form.Item
                          label='API Key'
                          name='apiKey'
                          rules={[
                            { required: true, message: 'API Key không được bỏ trống' },
                            {
                              validator: (_, value) => {
                                if (value && value.trim().length === 0) {
                                  return Promise.reject('API Key không được bỏ trống')
                                }

                                return Promise.resolve()
                              }
                            }
                          ]}
                        >
                          <Input placeholder='Nhập API Key' maxLength={250}></Input>
                        </Form.Item>
                        <Form.Item
                          label='Key Placement'
                          name='keyPlacement'
                          rules={[{ required: true, message: 'Key Placement không được bỏ trống' }]}
                        >
                          <Select placeholder='Chọn phương thức xác thực API' options={keyPlacementList} />
                        </Form.Item>
                        <Form.Item
                          label='Key Name'
                          name='keyName'
                          rules={[{ required: true, message: 'Key Name không được bỏ trống' }]}
                          normalize={value => value?.trim()}
                        >
                          <Input placeholder='Nhập Key Name' maxLength={250}></Input>
                        </Form.Item>
                      </>
                    )}

                    {authMethod == 'basic' && (
                      <>
                        <Form.Item
                          label='Username'
                          name='username'
                          rules={[{ required: true, message: 'Username không được bỏ trống' }]}
                          normalize={value => value?.trim()}
                        >
                          <Input placeholder='Nhập Username' maxLength={250}></Input>
                        </Form.Item>
                        <Form.Item
                          label='Password'
                          name='password'
                          rules={[{ required: true, message: 'Password không được bỏ trống' }]}
                          normalize={value => value?.trim()}
                        >
                          <Input placeholder='Nhập Password' type='password' maxLength={250}></Input>
                        </Form.Item>
                      </>
                    )}
                  </div>
                  {httpType != 'GET' && (
                    <div className='grid grid-cols-3 gap-4'>
                      <Form.Item name='isUsingHMAC' valuePropName='checked'>
                        <Checkbox>Sử dụng xác thực HMAC</Checkbox>
                      </Form.Item>
                    </div>
                  )}
                  {isUsingHMAC && (
                    <>
                      <div className='grid grid-cols-2 gap-4'>
                        <Form.Item
                          label='Secret Key'
                          name='hmacSecretKey'
                          rules={[{ required: true, message: 'Secret Key không được bỏ trống' }]}
                          normalize={value => value?.trim()}
                        >
                          <Input placeholder='Nhập Secret Key' maxLength={250}></Input>
                        </Form.Item>
                        <Form.Item
                          label='Thuật toán'
                          name='hmacAlgorithm'
                          rules={[{ required: true, message: 'Thuật toán không được bỏ trống' }]}
                        >
                          <Select placeholder='Chọn thuật toán' options={hmacList}></Select>
                        </Form.Item>
                      </div>
                      <div className='grid grid-cols-3 gap-4'>
                        <Form.Item
                          label='Tên kỹ thuật'
                          name='hmacTechniqueName'
                          rules={[{ required: true, message: 'Tên kỹ thuật không được bỏ trống' }]}
                          normalize={value => value?.trim()}
                        >
                          <Input placeholder='Nhập tên kỹ thuật' maxLength={50}></Input>
                        </Form.Item>
                        <Form.Item
                          label={
                            <div className='whitespace-nowrap'>
                              <span className='mr-1 text-red-500'>*</span>
                              Tham số
                            </div>
                          }
                          name='hmacParams'
                          rules={[{ validator: validateHMACParams(hmacParamsList) }]}
                        >
                          <Select
                            placeholder='Chọn thuật toán'
                            mode='multiple'
                            showSearch
                            options={hmacParamsOptions}
                          ></Select>
                        </Form.Item>
                        <Form.Item
                          label='Ký tự ngăn cách'
                          name='hmacSeparator'
                          normalize={value => value?.trim()}
                          rules={[{ validator: validatorHMACSeparator }]}
                        >
                          <Input placeholder='Nhập ký tự ngăn cách' maxLength={250}></Input>
                        </Form.Item>
                      </div>
                    </>
                  )}
                </div>
              </Collapse.Panel>
            </StyledCollapse>

            {connectionType == 'PULL_API' && (
              <StyledCollapse
                $isPopUp={isPopUp}
                className={clsx(!isPopUp ? 'bg-white' : 'bg-gray-12')}
                expandIconPosition='right'
                defaultActiveKey={['1']}
                ghost
              >
                <Collapse.Panel
                  key='1'
                  header={
                    <div className='border-l-4 border-solid border-yellow-6'>
                      <div className='pl-2 text-base font-semibold'>Lịch trình đồng bộ</div>
                    </div>
                  }
                >
                  <ScheduleSyncData
                    syncTime={syncTime}
                    frequency={frequency}
                    frequencyMonth={frequencyMonth}
                  ></ScheduleSyncData>
                </Collapse.Panel>
              </StyledCollapse>
            )}

            <DataMapping
              reqCode='DTO_REQ_INVENTORY_SYNC'
              resCode='DTO_RESP_INVENTORY_SYNC'
              form={form}
              isUseInPopUp={isPopUp}
              isRequiredRequestObject={isRequiredRequestObject}
              isVisibleRequestObject={httpType != 'GET'}
              returnRequestDataRowsList={data => {
                setHmacParamsList(data)

                // So sánh list mới và cũ, nếu có sự thay đổi thì reset lại trường hmacParams
                if (JSON.stringify(data) !== JSON.stringify(hmacParamsList)) {
                  form.setFieldsValue({
                    hmacParams: undefined
                  })
                }
              }}
            ></DataMapping>
          </Space>
        </Form>

        <ModalCheckConnection
          open={openConnection}
          setOpen={setOpenConnection}
          initValue={jsonCheckConnection}
        ></ModalCheckConnection>
      </Spin>
    )
  }
)

export default CreateConnection
