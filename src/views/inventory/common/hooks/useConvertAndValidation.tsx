import { message } from '@components/notification'

export const validateDataMapping = (data: any) => {
  const requiredRequestItems = data.requestObject.filter((item: any) => item.mandatory === true)
  const requiredResponseItems = data.responseObject.filter((item: any) => item.mandatory === true)

  if (data.isVisibleRequestObject) {
    if (data.isRequiredRequestObject && data.dropdownRequestObject.length === 0) {
      message.error('Vui lòng chọn file tải lên trong request object')

      return false
    }

    if (data.isRequiredRequestObject && !requiredRequestItems.every((item: any) => item.sourceField != null)) {
      message.error('Vui lòng chọn trường nguồn bắt buộc trong request object')

      return false
    }
  }

  if (data.isVisibleResponseObject) {
    if (data.isRequiredResponseObject && data.dropdownResponseObject.length === 0) {
      message.error('<PERSON>ui lòng chọn file tải lên trong response object')

      return false
    }

    if (data.isRequiredResponseObject && !requiredResponseItems.every((item: any) => item.sourceField != null)) {
      message.error('Vui lòng chọn trường nguồn bắt buộc trong response object')

      return false
    }
  }

  return true
}

export const validateHMACParams = (hmacParamsList: any[]) => {
  return (_: any, value: any) => {
    if (hmacParamsList.length === 0) {
      return Promise.reject(new Error('Vui lòng thiết lập request object để hiển thị tham số'))
    }

    if (!value) {
      return Promise.reject(new Error('Vui lòng chọn tham số'))
    }

    return Promise.resolve()
  }
}

export const validatorHMACSeparator = (rule: any, value: any) => {
  if (value && !['|', ',', '.', ';'].includes(value)) {
    return Promise.reject('Vui lòng nhập dấu ngăn cách "|" ; ",";"."')
  }

  return Promise.resolve()
}

export const buildSyncJson = (data: any) => {
  if (!validateDataMapping(data)) {
    return {}
  }

  return {
    type: 'PUSH_API',
    partnerId: data.partner,
    apiConfig: { ...buildApiInfo(data) },
    inputMapping: buildInputOutputMapping(data.requestObject, 'request'),
    outputMapping: buildInputOutputMapping(data.responseObject, 'response'),
    dxReqFields: buildDxFields(data.requestObject),
    dxRespFields: buildDxFields(data.responseObject),
    partnerReqFields: buildPartnerFields(data.dropdownRequestObject),
    partnerRespFields: buildPartnerFields(data.dropdownResponseObject)
  }
}

export const convertConfigApi = (data: any) => {
  return {
    inputMapping: buildInputOutputMapping(data.requestObject, 'request'),
    outputMapping: buildInputOutputMapping(data.responseObject, 'response'),
    dxReqFields: buildDxFields(data.requestObject),
    dxRespFields: buildDxFields(data.responseObject),
    partnerReqFields: buildPartnerFields(data.dropdownRequestObject),
    partnerRespFields: buildPartnerFields(data.dropdownResponseObject)
  }
}

export const buildApiInfo = (data: any) => {
  return {
    url: {
      raw: data.urlApi,
      variable: data.pathVariables ? data.pathVariables : [],
      params: data.params ? data.params : []
    },
    method: data.httpType,
    header:
      data.httpType === 'POST' || data.httpType === 'PUT'
        ? [
            {
              key: 'Content-Type',
              value: 'application/json'
            }
          ]
        : [],
    auth: {
      type: data.authentication,
      bearer: data.tokenValue
        ? {
            key: 'Authorization',
            value: data.tokenValue
          }
        : {},
      basic:
        data.username && data.password
          ? {
              userName: data.username,
              password: data.password
            }
          : {},
      apikey:
        data.apiKey && data.keyPlacement && data.keyName
          ? [
              {
                type: data.keyPlacement,
                key: data.keyName,
                value: data.apiKey
              }
            ]
          : []
    }
  }
}

export const buildInputOutputMapping = (data: any, type: string) => {
  return data.map((item: any) => ({
    source: type === 'request' ? item.name : item.sourceField,
    dest: type === 'request' ? item.sourceField : item.name,
    conversions: item.mappingData
      ? [
          {
            type: item.mappingData.type,
            rules: item.mappingData.ruleJson
          }
        ]
      : []
  }))
}

export const buildDxFields = (data: any) => {
  return data.map((item: any) => ({
    name: item.name,
    type: item.type,
    example: item.example,
    mandatory: item.mandatory,
    format: item.format
  }))
}

export const buildPartnerFields = (data: any) => {
  return data.map((item: any) => ({
    name: item.value,
    type: item.sourceDataType,
    example: item.sourceExampleValue
  }))
}

export const buildCreateJson = (data: any, generalJson: any) => {
  return {
    warehouseReqDTOS: data.selectedRowData,
    creationApi: generalJson
  }
}

export const buildConnectionsJson = (data: any) => {
  let day: any[] | null = null

  if (data.syncTime == 2) {
    day = data.dayOfWeek
  } else if (data.syncTime == 3) {
    if (data.frequencyMonth == 'Ngày cố định hàng tháng') {
      day = [data.dayOfMonth]
    } else {
      day = []
      day.push(data.dayOfWeekInMonth)
    }
  }

  const startDate = data.startDate ? data.startDate.format('YYYY-MM-DD') : null

  const startDateStr: string | null = data.startDate ? data.startDate.format('DD/MM/YYYY') : null

  let startTime = data.startTime ? data.startTime.format('HH:mm') : null

  let endTime = null

  if (data.rangeTime) {
    startTime = data.rangeTime[0].format('HH:mm')
    endTime = data.rangeTime[1].format('HH:mm')
  }

  const specificExecutionDateTime = data.syncTime == 4 ? startDate + ' ' + startTime : null

  return {
    name: data.name,
    code: data.code,
    type: data.connectionType,
    partnerId: data.partner,
    bosPartnerCode: data.bosPartnerCode,
    apiConfig: {
      ...buildApiInfo(data),
      securities: data.isUsingHMAC
        ? {
            hmac: {
              field: data.hmacTechniqueName,
              participants: data.hmacParams,
              encode: data.hmacAlgorithm,
              secretKey: data.hmacSecretKey,
              separator: data.hmacSeparator
            }
          }
        : {}
    },
    syncStart: startDateStr,
    reportFrequency: data.syncTime,
    interval: data.frequency,
    startTime: startTime,
    endTime: endTime,
    day: day,
    week: data.syncTime == 3 ? data.weekOfMonth : null,
    warehouseIds: data.vrwarehouse,
    specificExecutionDateTime: specificExecutionDateTime,
    status: 'ACTIVE',
    inputMapping: buildInputOutputMapping(data.requestObject, 'request'),
    outputMapping: buildInputOutputMapping(data.responseObject, 'response'),
    dxReqFields: buildDxFields(data.requestObject),
    dxRespFields: buildDxFields(data.responseObject),
    partnerReqFields: buildPartnerFields(data.dropdownRequestObject),
    partnerRespFields: buildPartnerFields(data.dropdownResponseObject)
  }
}

export const buildCompleteUrl = ({ template, pathVariables }: any) => {
  const url = template || ''

  // Tách path và query parts
  const [pathPart, ...queryParts] = url.split('?')
  const queryString = queryParts.length > 0 ? '?' + queryParts.join('?') : ''

  let processedPath = pathPart

  // Replace each :key in the path part với value
  pathVariables.forEach(({ key, value }: any) => {
    const placeholder = `:${key}`

    processedPath = processedPath.replace(new RegExp(placeholder, 'g'), value || '')
  })

  // Remove any unreplaced placeholders trong path part
  processedPath = processedPath.replace(/\/:[^/]+/g, '')

  return processedPath + queryString
}

export function buildRequestObject(dxReqFields: any, inputMapping: any): any {
  return dxReqFields.map((field: any, i: number) => {
    const map = inputMapping.find((m: any) => m.source === field.name)

    return {
      key: String(i),
      name: field.name,
      sourceField: map ? map.dest : '',
      format: field.format,
      type: field.type,
      mandatory: field.mandatory,
      mappingData:
        map && map.conversions.length > 0
          ? {
              type: map.conversions[0].type,
              ruleJson: map.conversions[0].rules
            }
          : undefined
    }
  })
}

export function buildResponseObject(dxRespFields: any, outputMapping: any): any {
  return dxRespFields.map((field: any, i: number) => {
    const map = outputMapping.find((m: any) => m.dest === field.name)

    return {
      key: String(i),
      name: field.name,
      sourceField: map ? map.source : '',
      format: field.format,
      type: field.type,
      mandatory: field.mandatory,
      mappingData:
        map && map.conversions.length > 0
          ? {
              type: map.conversions[0].type,
              ruleJson: map.conversions[0].rules
            }
          : undefined
    }
  })
}

export function buildRequestTable(dxFields: any, mappingRows: any, dropdownObject: any): any {
  const baseArray = buildRequestObject(dxFields, mappingRows)

  return baseArray.map((item: any) => {
    const dd = dropdownObject.find((d: any) => d.name === item.sourceField)

    return {
      ...item,
      sourceDataType: dd ? dd.type : undefined
    }
  })
}

export function buildResponseTable(dxFields: any, mappingRows: any, dropdownObject: any): any {
  const baseArray = buildResponseObject(dxFields, mappingRows)

  return baseArray.map((item: any) => {
    const dd = dropdownObject.find((d: any) => d.name === item.sourceField)

    return {
      ...item,
      sourceDataType: dd ? dd.type : undefined
    }
  })
}

export function buildDropdownMappingObject(data: any): any {
  return data.map((item: any) => ({
    label: item.name,
    value: item.name,
    sourceDataType: item.type,
    sourceExampleValue: item.example
  }))
}

export const authenticationString = (data: any) => {
  switch (data) {
    case 'bearer':
      return 'Bearer Token'
    case 'apikey':
      return 'API Key'
    case 'basic':
      return 'Basic Auth'
    case 'noauth':
      return 'No Auth'
    default:
      return data
  }
}
