import React, { createContext, useCallback, useContext, useState } from 'react'

import { HolderOutlined, CaretDownOutlined, CaretRightOutlined } from '@ant-design/icons'

import { useSortable } from '@dnd-kit/sortable'

import { useMutation } from '@tanstack/react-query'

import { Button, Checkbox, Collapse, DatePicker, Pagination, Popover, Select, type TableProps, Tag, Spin } from 'antd'

import dayjs from 'dayjs'

import { debounce } from 'lodash'

import { styled } from 'styled-components'

import { usePage } from '@/hooks'
import Warehouse from '@/models/Warehouse'
import type { NotificationInfoType } from '@/types/notificationModalTypes'
import { type CheckboxOption, type CheckedState } from '@components/filter'
import SettingInput from '@components/filter/SettingInput'
import { EmptySearch } from '@components/inventory/common/EmptySearch'
import { STATUS_CURRENT_INVENTORY, tagStockStatus } from '@components/inventory/constants/constants'
import { checkboxOptions } from '@components/inventory/constants/currentInventory'
import type { IParamsCurrentInventory } from '@components/inventory/types/inventory'
import { message } from '@components/notification'

import { StyledTable } from '@views/inventory/common/StyledTableWrapper'
import TableCheckboxEdit from '@views/product-catalog/common/TableCheckboxEdit'
import { pageSizeOptions } from '@views/product-catalog/constants/constants'

import ModalNotification from '../../../../components/modal/ModalNotification'

interface RowProps extends React.HTMLAttributes<HTMLTableRowElement> {
  'data-row-key': string
}

interface DragState {
  active: string
  over?: string
}

const { Panel } = Collapse

const StyledCollapse = styled(Collapse)`
  .ant-collapse-content-box {
    padding-top: 0 !important;
  }
  .ant-collapse-header-text {
    font-size: 16px;
  }
  .ant-collapse-header.ant-collapse-collapsible-icon {
    display: flex;
    align-items: center;
  }
  background-color: white;
`

const StyledTableRow = styled.tr`
  &:hover {
    .three-dot-icon {
      display: block !important;
    }
  }
`

const DragHandle = styled.div`
  cursor: move;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  inline-size: 20px;
  block-size: 20px;
  color: #999;

  &:hover {
    color: #666;
  }
`

const { RangePicker } = DatePicker

const DragContext = createContext<DragState>({ active: '', over: undefined })

export const TableRow = ({ children, ...props }: RowProps) => {
  const dragState = useContext(DragContext)

  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id: props['data-row-key']?.toString()
  })

  const style: React.CSSProperties = {
    ...props.style,
    transform: transform ? `translate3d(0, ${transform.y}px, 0)` : undefined,
    transition,
    ...(isDragging ? { position: 'relative', backgroundColor: '#fafafa' } : {}),
    ...(dragState.over === props['data-row-key']
      ? {
          borderTop: '2px dashed #1677ff',
          backgroundColor: '#f0f8ff'
        }
      : {})
  }

  return (
    <StyledTableRow {...props} ref={setNodeRef} style={style} {...attributes}>
      {React.Children.map(children, child => {
        if (!React.isValidElement(child)) return child

        if (child.key === 'checkbox') {
          return React.cloneElement(child as React.ReactElement<any>, {
            children: (
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <DragHandle {...listeners}>
                  <HolderOutlined />
                </DragHandle>
                <Checkbox {...(child as React.ReactElement<any>).props} />
              </div>
            )
          })
        }

        return child
      })}
    </StyledTableRow>
  )
}

interface Props {
  id: string
}

interface CurrentInventory {
  stockStatus: string

  idWarehouseStocks: number
  nameVariant: string
  variantSku: string
  modifiedAt: number
  availableStock: number
  reservedStock: number
  totalStock: number
  variantId: number
  serviceId: number
  serviceName: string
  filePath: string
  serviceSku: string
  stocksInfoDTOS: string
  index: number
}

export const CurrentInventory = ({ id }: Props) => {
  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([])

  const defaultSearchParams: IParamsCurrentInventory = {
    page: 0,
    size: 10,
    startTime: '',
    endTime: '',
    stockStatus: 'ALL'
  }

  const [filterParams, setFilterParams] = useState(defaultSearchParams)

  const [checked, setChecked] = useState<CheckedState>({
    isSKU: true,
    isProduct: true
  })

  const debouncedSetFilterParams = debounce(params => {
    setFilterParams(params)
  }, 200)

  const handleSearch = useCallback(
    (value: string) => {
      debouncedSetFilterParams({
        ...filterParams,
        value: value || '',
        isSKU: Number(Boolean(checked.isSKU)),
        isProduct: Number(Boolean(checked.isProduct))
      })
    },
    [filterParams, checked, debouncedSetFilterParams]
  )

  const checkBoxOptions: CheckboxOption[] = [
    { label: 'Mã SKU', key: 'isSKU' },
    { label: 'Tên sản phẩm / biến thể', key: 'isProduct' }
  ]

  const [selectedColumns, setSelectedColumns] = useState<string[]>([
    'serviceSku',
    'serviceName',
    'availableStock',
    'reservedStock',
    'totalStock',
    'modifiedAt',
    'stockStatus',
    'stt'
  ])

  const [expandedRows, setExpandedRows] = useState<number[]>([])

  const toggleExpand = (id: number) => {
    setExpandedRows(prev => (prev.includes(id) ? prev.filter(item => item !== id) : [...prev, id]))
  }

  const buildTableData = () => {
    const result: any[] = []

    content.forEach(item => {
      result.push({
        ...item,
        rowType: 'PRODUCT'
      })

      if (expandedRows.includes(item.index) && Array.isArray(item.stocksInfoDTOS)) {
        item.stocksInfoDTOS.forEach((variant: any) => {
          result.push({
            ...variant,
            parentServiceName: item.serviceName,
            parentFilePath: item.filePath,
            rowType: 'VARIANT'
          })
        })
      }
    })

    return result
  }

  const mainColumns: TableProps<CurrentInventory>['columns'] = [
    {
      title: 'Tên sản phẩm',
      dataIndex: 'serviceName',
      key: 'serviceName',
      width: '200px',
      align: 'center',
      render: (_: any, record: any) => {
        const isVariant = record.rowType === 'VARIANT'
        const img = isVariant ? record.filePath || record.parentFilePath : record.filePath
        const name = isVariant ? record.nameVariant : record.serviceName
        const isExpanded = expandedRows.includes(record.index)
        const hasVariants = Array.isArray(record.stocksInfoDTOS) && record.stocksInfoDTOS.length > 0

        return (
          <div className='flex items-center gap-2' style={{ paddingLeft: isVariant ? 24 : 0 }}>
            {!isVariant && hasVariants && (
              <div className='cursor-pointer text-lg' onClick={() => toggleExpand(record.index)}>
                {isExpanded ? <CaretDownOutlined /> : <CaretRightOutlined />}
              </div>
            )}
            <img
              src={img || '/assets/images/no-image.jpg'}
              alt='product'
              className='size-10 rounded-md border object-cover'
              onError={(e: any) => {
                e.target.onerror = null
                e.target.src = '/assets/images/no-image.jpg'
              }}
            />
            <span className={isVariant ? '' : 'font-medium'}>{name}</span>
          </div>
        )
      }
    },
    {
      title: 'Mã SKU',
      dataIndex: 'serviceSku',
      key: 'serviceSku',
      width: '150px',
      align: 'center',
      render: (_: any, record: any) => {
        const isVariant = record.rowType === 'VARIANT'

        return <>{isVariant ? record.variantSku : record.serviceSku}</>
      }
    },
    {
      title: 'SL thực tế',
      dataIndex: 'availableStock',
      key: 'availableStock',
      width: '10%',
      align: 'center',
      render: text => <div>{text}</div>
    },
    {
      title: 'SL được giữ',
      dataIndex: 'reservedStock',
      key: 'reservedStock',
      width: '10%',
      align: 'center'
    },
    {
      title: 'Tổng SL',
      dataIndex: 'totalStock',
      key: 'totalStock',
      width: '10%',
      align: 'center',
      render: text => <div>{text}</div>
    },
    {
      title: 'Trạng thái',
      dataIndex: 'stockStatus',
      key: 'stockStatus',
      width: '12%',
      align: 'center',
      render: (value: any) => {
        // @ts-ignore
        const tagInfo = tagStockStatus[value]

        return (
          <Tag color={tagInfo?.color}>
            <div style={{ color: `${tagInfo?.textColor}` }}>{tagInfo?.text}</div>
          </Tag>
        )
      }
    },
    {
      title: 'Thời gian cập nhật',
      dataIndex: 'modifiedAt',
      key: 'modifiedAt',
      width: '200px',
      align: 'center',
      render: (value: number) => {
        return <>{dayjs(value).format('DD/MM/YYYY HH:mm:ss')}</>
      }
    }
  ]

  const filteredColumns = mainColumns.filter(
    (col: any) => !col.dataIndex || selectedColumns.includes(col.dataIndex.toString())
  )

  // xử lý khi chọn 1 row
  const onSelectChange = useCallback((record: any, selected: boolean) => {
    setSelectedRowKeys(prev => {
      if (selected) {
        return [...prev, record]
      } else {
        return prev.filter(item => item.id !== record.id)
      }
    })
  }, [])

  // xử lý khi chọn nhiều row
  const onSelectAll = useCallback((selected: boolean, selectedRows: any, changeRows: any) => {
    setSelectedRowKeys(prev => {
      if (selected) {
        return [...prev, ...changeRows]
      } else {
        const changeRowKeys = changeRows.map((row: any) => row.id)

        return prev.filter(item => !changeRowKeys.includes(item.id))
      }
    })
  }, [])

  const rowSelection = {
    selectedRowKeys: selectedRowKeys?.map(item => item.id),
    onSelect: onSelectChange,
    onSelectAll: onSelectAll,
    getCheckboxProps: (record: any) => ({
      name: record.name
    })
  }

  const {
    page,
    pageSize,
    content,
    pagination,
    setPageSize,
    refetch,
    isFetching: isLoadingCurrentInventory
  } = usePage(
    ['stocks-info-list', 'table', filterParams],
    async () => {
      const params = {
        ...filterParams,
        id,
        page: page - 1,
        pageSize,
        size: pageSize
      }

      const res = await Warehouse.getCurrentInventory({ ...params })

      const resWithIndex = res.content.map((item: any, index: number) => ({
        ...item,
        index: index + 1
      }))

      return resWithIndex
    },
    {
      sort: 'modifiedAt,desc'
    }
  )

  const [infoModal, setInfoModal] = useState<NotificationInfoType>({})
  const [visibleModal, setVisibleModal] = useState(false)
  const [datePickerOpen, setDatePickerOpen] = useState(false)
  // const [openDateOption, setOpenDateOption] = useState(false)
  // const [customDate, setCustomDate] = useState<any>()

  const formatCount = (count: number): string => {
    return count > 0 ? String(count).padStart(2, '0') : '0'
  }

  const deleteWarehouseType = useMutation({
    mutationKey: ['deleteWarehouseTypeByIds'],
    mutationFn: (ids: number[]) => Warehouse.deleteWarehouseTypeByIds(ids),
    onSuccess: () => {
      message.success('Xóa loại kho thành công')
      setSelectedRowKeys([])
      refetch()
    },
    onError: error => {
      message.error('Xóa loại kho thất bại')
      console.error('Delete warehouse type error:', error?.message)
    }
  })

  const handleRemoveService = (ids: number[]) => {
    setVisibleModal(true)
    setInfoModal({
      iconType: 'WARNING',
      title: `Bạn có chắc chắn muốn xóa loại kho đang chọn?`,
      textButton: 'Xác nhận',
      isCloseModal: true,
      handleConfirm: () => deleteWarehouseType.mutate(ids),
      handleCancel: () => setVisibleModal(false)
    })
  }

  const handleFilterChange = (key: string, value: any) => {
    setFilterParams(prev => {
      const updatedParams = {
        ...prev,
        [key]: value
      }

      setTimeout(() => {
        refetch()
      }, 0)

      return updatedParams
    })
  }

  const renderPagination = () => (
    <div className='mt-[10px] flex w-full justify-between'>
      <div>
        <div className='flex gap-4'>
          <div className='pt-2'>Đã chọn: {formatCount(selectedRowKeys?.length ?? 0)}</div>
          <Button
            onClick={() => handleRemoveService(selectedRowKeys?.map((item: any) => item.id))}
            className='border-primary bg-white text-primary'
            disabled={selectedRowKeys?.length === 0}
          >
            Xóa
            <i className='onedx-delete-traffic size-4 ' />
          </Button>
        </div>
      </div>
      <Pagination {...pagination} showSizeChanger={false} />
      <Select defaultValue={10} options={pageSizeOptions} onChange={value => setPageSize(value)} />
    </div>
  )

  const renderCustomerHeader = () => (
    <div className='mt-1 flex justify-between border-l-4 border-solid border-yellow-6'>
      <div className='pl-2 font-semibold'>Tồn kho hiện tại</div>
      <div className='flex justify-between pb-1'>
        {/*comment tạm*/}
        {/*<div*/}
        {/*  className='flex gap-1 pr-4 text-primary hover:cursor-pointer self-center h-5 border-r border-solid border-slate-200'*/}
        {/*  onClick={() => []}*/}
        {/*>*/}
        {/*  <div className='pt-1'>*/}
        {/*    <i className='onedx-book size-5' />*/}
        {/*  </div>*/}
        {/*  Nhập kho*/}
        {/*</div>*/}

        {/*<div*/}
        {/*  className='flex gap-1 px-4 text-primary hover:cursor-pointer self-center h-5 border-r border-solid border-slate-200'*/}
        {/*  onClick={() => []}*/}
        {/*>*/}
        {/*  <div className='pt-1'>*/}
        {/*    <i className='onedx-download size-5' />*/}
        {/*  </div>*/}
        {/*  Xuất file*/}
        {/*</div>*/}
      </div>
    </div>
  )

  return (
    <>
      <Spin spinning={isLoadingCurrentInventory}>
        <StyledCollapse className='bg-white' collapsible='icon' expandIconPosition='end' ghost defaultActiveKey={['1']}>
          <Panel header={renderCustomerHeader()} key='1'>
            <div className='border-t border-solid border-slate-200'></div>
            <div className=' w-full gap-3 bg-white p-4'>
              <div className='mb-4 flex items-center gap-x-2'>
                {/* Tìm kiếm */}
                <SettingInput
                  placeholder='Tìm kiếm theo tên sản phẩm/ biến thể hoặc SKU'
                  styles={{ width: '100%' }}
                  checked={checked}
                  setChecked={setChecked}
                  onChange={e => handleSearch(e)}
                  checkBoxOptions={checkBoxOptions}
                />

                {/* Filter: Trạng thái */}
                <Select
                  className='h-[40px] min-w-[190px]'
                  placeholder={<div className='text-gray-6'>Trạng thái</div>}
                  options={STATUS_CURRENT_INVENTORY}
                  maxTagCount={1}
                  mode='multiple'
                  onChange={value => handleFilterChange('stockStatus', value)}
                ></Select>

                {/* Filter: Thời gian tạo */}
                <div className='flex h-6 w-[190px] cursor-pointer items-center rounded-lg border border-solid border-[#D9D9D9] py-[19px] text-gray-6'>
                  <Popover
                    placement='bottom'
                    content={
                      <RangePicker
                        renderExtraFooter={() => (
                          <div className='m-2 flex justify-end'>
                            <Button
                              type='primary'
                              onClick={() => {
                                setDatePickerOpen(false)
                                // setOpenDateOption(false)
                              }}
                            >
                              Xác nhận
                            </Button>
                          </div>
                        )}
                        open={datePickerOpen}
                        onOpenChange={open => {
                          // setCustomDate(null)
                          setDatePickerOpen(open)
                        }}
                        onChange={dates => {
                          if (dates && dates.length === 2) {
                            const [start, end] = dates
                            const startTimestamp = start?.valueOf() || '' // kiểu Long (number)
                            const endTimestamp = end?.valueOf() || ''

                            handleFilterChange('startTime', startTimestamp as any)
                            handleFilterChange('endTime', endTimestamp as any)
                          } else {
                            // Reset nếu không chọn gì
                            handleFilterChange('startTime', '' as any)
                            handleFilterChange('endTime', '' as any)
                          }
                        }}
                      />
                    }
                    trigger='click'
                  >
                    <div className='flex'>
                      <div className='mx-1	text-sm  font-normal '>Thời gian tạo</div>
                      <i className='onedx-calendar size-5' />
                    </div>
                  </Popover>
                </div>

                {/* Chỉnh sửa bảng */}
                <TableCheckboxEdit
                  selectedColumns={selectedColumns}
                  setSelectedColumns={setSelectedColumns}
                  checkboxOptions={checkboxOptions}
                  height={'40px'}
                />
              </div>

              <div className='w-full'>
                <StyledTable<CurrentInventory>
                  rowSelection={{ type: 'checkbox', ...rowSelection }}
                  className='custom-pricing-table'
                  columns={filteredColumns?.filter((column: any) => !column.hidden)}
                  dataSource={buildTableData()}
                  rowKey='id'
                  pagination={false}
                  components={{
                    body: {
                      row: TableRow
                    }
                  }}
                  locale={{
                    emptyText: (
                      <div className='flex items-center justify-center py-20'>
                        <EmptySearch title='Danh sách trống' description='Không có dữ liệu tồn kho hiện tại' />
                      </div>
                    )
                  }}
                />

                {/*  Pagination */}
                {renderPagination()}
              </div>

              <ModalNotification visibleModal={visibleModal} setVisibleModal={setVisibleModal} infoModal={infoModal} />
            </div>
          </Panel>
        </StyledCollapse>
      </Spin>
    </>
  )
}
