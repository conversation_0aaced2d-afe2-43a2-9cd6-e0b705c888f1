'use client'

import { useMemo, useState } from 'react'

import { usePara<PERSON>, usePathname, useRouter } from 'next/navigation'

import { useMutation, useQuery } from '@tanstack/react-query'

import type { TableProps } from 'antd'
import { Card, Button, Table, message } from 'antd'
import { isEmpty, isNil } from 'lodash'
import moment from 'moment'
import classNames from 'classnames'
import { useDispatch } from 'react-redux'
import { styled } from 'styled-components'

import { useRedirect, useUser } from '@/hooks'
import Quotation from '@/models/Quotation'
import DXPortal from '@/models/DXPortal'
import { bodyCartConvert, filterSelectedProduct } from '@views/payment/utils/paymentUtils'
import { shoppingCartAction } from '@/redux-store/slices/ShoppingCartSlice'
import {
  convertBodyCartToQuotationCheckout,
  convertBodyToDataTableBos,
  convertDataValidateCart,
  convertStringToEnglish,
  getTotalFromTable,
  validateCartQuotation
} from './utils'
import { keyList, QuotationStatus } from './constant'
import { PRICING_PLAN_NUMBER } from '../cart/constant'
import DiscountInfor from './modals/DiscountInfor'
import FeeInfor from './modals/FeeInfor'
import UpdateRequestModal from './modals/UpdateRequestModal'
import { ConfirmModal } from '@/components/modal'
import ShoppingCart from '@/models/ShoppingCart'

const CustomTable = styled(Table)<TableProps<any>>`
  th.ant-table-cell {
    font-weight: 500;
    font-size: 12px;
    line-height: 22px;
    color: #666;
  }

  td.ant-table-cell {
    font-weight: 500;
    font-size: 12px;
    line-height: 20px;
    color: #333;
  }
`

export const renderError = (errorTitle?: string, type?: string) => {
  if (isNil(errorTitle)) return null

  const style = {
    background: '#FFF1F0',
    color: '#EC5860'
  }

  if (type === 'info') {
    style.background = '#EAECF4'
    style.color = '#2C3D94'
  }

  return (
    <div
      className='inline-block rounded-lg px-3 py-1 text-sm font-medium'
      style={{
        background: style.background,
        color: style.color
      }}
    >
      <span className='mr-2'>
        <i className='onedx-information size-4' />
      </span>
      {errorTitle}
    </div>
  )
}

async function getReport(id: string) {
  const file = await Quotation.exportPdf(id)

  return file
}

async function downloadReport(id: string, quotation: any) {
  const file = await getReport(id)

  const nameUser = convertStringToEnglish(quotation?.objectInfo?.name).replace(/\s/g, '')

  const fileName = `Baogia_${nameUser}_${quotation?.code}`

  DXPortal.exportFile(file, fileName, 'application/pdf')
  message.success('Tải xuống thành công')
}

export const QuotationPreview = () => {
  const pathname = usePathname()
  const { quotationId } = useParams() as { quotationId: string }
  const { user } = useUser()
  const dispatch = useDispatch()
  const { handleLogin } = useRedirect()
  const router = useRouter() // Hook dùng để điều hướng

  const isCustomerPreview = pathname.includes('admin-portal') || pathname.includes('partner-portal')

  const [isModalDiscount, setisModalDiscount] = useState(false)
  const [isModalFee, setisModalFee] = useState(false)
  const [isShowUpdateConfirm, setIsShowUpdateConfirm] = useState(false)
  const [visibleModal1, setVisibleModal1] = useState(false)

  const [validate, setValidate] = useState<any>(true)
  const [expandedRowKeys, setExpandedRowKeys] = useState<any>([])
  const [dataTable, setDataTable] = useState<any>([])

  // validate detail
  const validateMutation = useMutation({ mutationFn: ShoppingCart.validateCart })

  // get detail quotation
  const { data: quotation } = useQuery({
    queryKey: ['getDetailQuotation', quotationId],
    queryFn: async () => {
      const res = await Quotation.getDetailQuotation(quotationId)

      const isStatusOrder = res?.status === QuotationStatus.ORDER

      const dataConvert = convertBodyToDataTableBos(res?.subDetails?.bodyCart)

      if (!isEmpty(res?.subDetails?.bodyCart) && !isCustomerPreview && !isStatusOrder) {
        const validateInput = convertDataValidateCart(res?.subDetails?.bodyCart, res?.objectInfo?.userId)

        validateMutation.mutate(validateInput, {
          onError: (e: any) => {
            const errorValidate = validateCartQuotation(e)

            setValidate(errorValidate)
          }
        })
      }

      setDataTable(dataConvert?.data)
      res.feeDetails = dataConvert?.feeDetails
      res.couponLst = dataConvert?.couponLst
      setExpandedRowKeys(dataConvert?.data?.map((e: any) => e.key))

      if (res.quotationSupports.filter((e: any) => e.role === 'KEY').length > 0) {
        res.quotationSupportName = res.quotationSupports.filter((e: any) => e.role === 'KEY')[0].name
        res.quotationSupportEmail = res.quotationSupports.filter((e: any) => e.role === 'KEY')[0].email
        res.quotationSupportPhoneNumber = res.quotationSupports.filter((e: any) => e.role === 'KEY')[0].phoneNumber
      }

      return res
    }
  })

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const totalAmount = useMemo(() => getTotalFromTable(dataTable, keyList), [dataTable])

  const isExpired =
    moment(quotation?.endTime, 'DD/MM/YYYY').diff(moment(moment(), 'DD/MM/YYYY'), 'days') < 0 &&
    !isNil(quotation?.duration)

  const columns = [
    {
      title: 'Dịch vụ',
      dataIndex: 'product',
      key: 'product',
      editable: true,
      isDefaultColumns: true,
      render: (text: string, record: any) => {
        if (record.level === 2) return <span>{record.name}</span>

        return <span>{text}</span>
      }
    },
    {
      title: 'Số lượng',
      dataIndex: 'quantity',
      key: 'quantity',
      editable: true,
      width: '100px',
      isDefaultColumns: true,
      render: (text: string | number, record: any) => {
        if (record.level === 2)
          return <div className={classNames(record.level === 2 ? 'text-left' : '')}>{record.quantity ?? 1}</div>

        return <span>{text}</span>
      }
    },
    {
      title: 'Đơn giá gốc',
      dataIndex: 'originalUnitPrice',
      key: 'originalUnitPrice',
      render: (text: number | null, record: any) => {
        if (
          record?.pricingPlan &&
          ![PRICING_PLAN_NUMBER.UNIT, PRICING_PLAN_NUMBER.FLAT_RATE].includes(record.pricingPlan)
        ) {
          return '--'
        }

        return <span>{`${DXPortal.formatNumberCurrency(text)} ₫`}</span>
      },
      editable: true,
      isDefaultColumns: true
    },
    {
      title: 'Đơn giá thay đổi',
      dataIndex: 'unitPrice',
      key: 'unitPrice',
      editable: true,
      isDefaultColumns: true,
      render: (text: number | null, record: any) => {
        if (
          record?.pricingPlan &&
          ![PRICING_PLAN_NUMBER.UNIT, PRICING_PLAN_NUMBER.FLAT_RATE].includes(record.pricingPlan)
        ) {
          return '--'
        }

        if (record.level === 2 && record.type !== 'title') {
          return <span>{DXPortal.formatNumberCurrency(record.price)} ₫</span>
        }

        return <span>{`${DXPortal.formatNumberCurrency(text)} ₫`}</span>
      }
    },
    {
      title: 'Tổng tạm tính',
      dataIndex: 'amountPreTax',
      key: 'amountPreTax',
      editable: true,
      isDefaultColumns: false,
      render: (text: number | null, record: any) => {
        if (record.level === 2 && record.type !== 'title')
          return (
            <div className={classNames(record?.level == 2 ? 'text-left' : '')}>
              {DXPortal.formatNumberCurrency(text)} ₫
            </div>
          )

        return <span>{`${DXPortal.formatNumberCurrency(text)} ₫`}</span>
      }
    },
    {
      title: 'Khuyến mại',
      dataIndex: 'couponAmount',
      key: 'couponAmount',
      editable: true,
      isDefaultColumns: false,
      render: (text: number | null, record: any) => {
        if (record.level === 2 && record.type !== 'title')
          return (
            <div className={classNames(record.level === 2 ? 'text-left' : '')}>
              {DXPortal.formatNumberCurrency(record.couponAmount)} ₫
            </div>
          )

        return <span>{`${DXPortal.formatNumberCurrency(text)} ₫`}</span>
      }
    },
    {
      title: 'Thuế',
      dataIndex: 'tax',
      key: 'tax',
      editable: true,
      isDefaultColumns: false,
      render: (text: number | null, record: any) => {
        if (record.level === 2 && record.type !== 'title')
          return (
            <div className={classNames(record.level === 2 ? 'text-left' : '')}>
              {DXPortal.formatNumberCurrency(text)} ₫
            </div>
          )

        return <span>{`${DXPortal.formatNumberCurrency(text)} ₫`}</span>
      }
    },
    {
      title: 'Phí',
      dataIndex: 'fee',
      key: 'fee',
      editable: true,
      isDefaultColumns: false,
      render: (text: number | null, record: any) => {
        if (record.level === 2 && record.type !== 'title')
          return (
            <div className={classNames(record.level === 2 ? 'text-left' : '')}>
              {DXPortal.formatNumberCurrency(text)} ₫
            </div>
          )

        return <span>{`${DXPortal.formatNumberCurrency(text)} ₫`}</span>
      }
    },
    {
      title: 'Giá sau thuế',
      dataIndex: 'amountAfterTax',
      key: 'amountAfterTax',
      editable: true,
      isDefaultColumns: true,
      render: (text: number | null, record: any) => {
        if (record.level === 2 && record.type !== 'title')
          return (
            <div className={classNames(record.level === 2 ? 'text-left' : '')}>
              {DXPortal.formatNumberCurrency(record.amountAfterTax)} ₫
            </div>
          )

        return <span>{`${DXPortal.formatNumberCurrency(text)} ₫`}</span>
      }
    }
  ]

  return (
    <div className='grid grid-cols-1 gap-1 py-2'>
      <div
        className={!isCustomerPreview ? 'tablet:px-6 tablet:py-5 mobile:hidden mx-auto w-[1140px] py-5' : 'col-span-1'}
      >
        <div>
          <div>
            <span className='mr-3 text-xl font-semibold'>Báo giá {quotation?.code}</span>
            {quotation?.status === QuotationStatus.CANCEL && renderError('Báo giá đã bị hủy')}
            {isExpired &&
              quotation?.status !== QuotationStatus.CANCEL &&
              renderError(`Báo giá hết hạn ngày ${quotation?.endTime}`)}
            {!isExpired &&
              user.id !== quotation?.objectInfo?.userId &&
              renderError('Vui lòng đăng nhập bằng tài khoản được báo giá để xác nhận đơn hàng')}
            {!isExpired && quotation?.status === QuotationStatus.ORDER && renderError('Đặt hàng thành công', 'info')}

            {quotation?.couponExpired &&
              !isExpired &&
              quotation?.status !== QuotationStatus.CANCEL &&
              quotation?.status !== QuotationStatus.ORDER &&
              renderError('Khuyến mại áp dụng trong báo giá đã hết hạn')}

            {quotation?.status !== QuotationStatus.ORDER && renderError(validate?.message)}
          </div>

          <div className='my-7' style={{ borderRadius: '8px', background: '#F2F2F2', padding: '12px 16px' }}>
            Khách hàng:{' '}
            <span className='font-semibold'>
              {quotation?.objectInfo?.name} - {quotation?.objectInfo?.email || quotation?.objectInfo?.emails[0]}
            </span>
          </div>
          <Card className='w-full'>
            <div className='flex flex-wrap'>
              {[
                { label: 'Nhân sự phụ trách', value: quotation?.quotationSupportName },
                { label: 'Email', value: quotation?.quotationSupportEmail },
                { label: 'Số điện thoại', value: quotation?.quotationSupportPhoneNumber },
                { label: 'Ngày hiệu lực báo giá', value: quotation?.startTime },
                { label: 'Ngày hết hạn báo giá', value: !quotation?.duration ? 'Vô thời hạn' : quotation?.endTime }
              ].map(({ label, value }) => (
                <div key={label} className='w-1/5'>
                  <div className='mb-1 text-xs'>{label}</div>
                  <span className='text-sm font-medium leading-[22px]'>{value}</span>
                </div>
              ))}
            </div>

            <CustomTable
              className='beauty-scroll-table mt-10'
              columns={columns}
              dataSource={dataTable}
              // requiredOptions={['product', 'variant', 'quantity', 'unitPrice']}
              expandable={{
                expandedRowKeys,
                expandIcon: ({ expanded, record }: { expanded: any; record: any }) => {
                  if (!record?.children || record?.children.length === 0 || record.level === 1) return ''

                  return expanded ? (
                    <i
                      className='onedx-minus size-4'
                      onClick={() => setExpandedRowKeys(expandedRowKeys.filter((o: any) => o !== record.key))}
                    />
                  ) : (
                    <i
                      className='onedx-plus size-4'
                      onClick={() => setExpandedRowKeys(expandedRowKeys.concat([record?.key]))}
                    />
                  )
                }
              }}
              pagination={false}
              rowClassName={(record: any) => (record.level === 2 ? 'text-gray-500' : '')}
            />

            <div className='mt-10 w-full text-right'>
              <div className='flex justify-end'>
                <div className='w-48 pb-4 text-left font-medium'>Tổng tạm tính</div>
                <div className='w-48 flex-initial pb-4 text-sm font-semibold'>
                  {`${DXPortal.formatNumberCurrency(totalAmount?.amountPreTax)} ₫`}
                </div>
              </div>

              <div className='flex justify-end'>
                <DiscountInfor
                  openModal={isModalDiscount}
                  setOpenModal={setisModalDiscount}
                  quotation={quotation}
                  totalAmount={totalAmount}
                />
              </div>

              <div className='flex justify-end'>
                <div className='w-48 pb-4 text-left font-medium'>Thuế</div>
                <div className='w-48 flex-initial pb-4 text-sm font-semibold'>
                  {`${DXPortal.formatNumberCurrency(totalAmount?.tax)} ₫`}
                </div>
              </div>

              <div className='flex justify-end'>
                <FeeInfor
                  openModal={isModalFee}
                  setOpenModal={setisModalFee}
                  quotation={quotation}
                  totalAmount={totalAmount}
                />
              </div>

              <div className='flex justify-end'>
                <div className='w-48 text-left font-bold'>Tổng sau thuế</div>
                <div className='w-48 font-bold'>{`${DXPortal.formatNumberCurrency(totalAmount?.amountAfterTax)} ₫`}</div>
              </div>
            </div>
            {quotation?.description && (
              <div className='mt-10 text-xs'>
                <span className='font-semibold'>Mô tả thêm:</span> <span>{quotation?.description} </span>
              </div>
            )}
            <div className='mt-10'>
              {!isExpired &&
                [QuotationStatus.PROCESSING, QuotationStatus.NEW, QuotationStatus.SENT].includes(quotation?.status) && (
                  <>
                    {/* trường hợp Kh đăng nhập đúng tài khoản của báo giá */}
                    {!isCustomerPreview && user.id && user.id === quotation?.objectInfo?.userId && (
                      <div className='flex justify-end'>
                        {!quotation?.couponExpired && !validate?.hasError && (
                          <Button
                            color='primary'
                            className=' mr-2'
                            onClick={() => {
                              // Filter các sản phẩm được chọn
                              const cartDetailFilter = filterSelectedProduct(quotation.subDetails.bodyCart?.lstProduct)

                              // Convert body cho API Calculate-Cart
                              const convertCalculateData = bodyCartConvert(cartDetailFilter)

                              const body = {
                                ...convertCalculateData,
                                userId: user.id
                              }

                              dispatch(
                                shoppingCartAction.saveCalculateCartApiBody({
                                  ...body,
                                  paymentMethod: quotation?.paymentMethod
                                })
                              )
                              dispatch(
                                shoppingCartAction.saveCalculateCartResponse({
                                  ...quotation?.calculateCart,
                                  quotationId: Number(quotationId),
                                  paymentMethod: quotation?.paymentMethod
                                })
                              )
                              dispatch(
                                shoppingCartAction.initLstProduct(
                                  convertBodyCartToQuotationCheckout(quotation.subDetails.bodyCart?.lstProduct)
                                )
                              )
                              dispatch(shoppingCartAction.setIsLoadingDetailCart(true))
                              router.push('/checkout/cart')
                            }}
                          >
                            Xác nhận báo giá
                          </Button>
                        )}
                        {!validate?.hasError && (
                          <Button
                            type='default'
                            className='mr-2'
                            onClick={() => {
                              setIsShowUpdateConfirm(true)
                            }}
                          >
                            Yêu cầu cập nhật
                          </Button>
                        )}

                        {!(validate?.hasError && validate?.code === 'disabledProduct') && (
                          <div className='inline-block text-right'>
                            <Button
                              onClick={() => {
                                downloadReport(quotationId, quotation)
                              }}
                              type='primary'
                              className='bg-mbprimary'
                            >
                              Tải xuống PDF
                            </Button>
                          </div>
                        )}
                      </div>
                    )}
                    {/* trường hợp KH đã đăng nhập nhưng sai tài khoản */}
                    {!isCustomerPreview && user.id && user.id !== quotation?.objectInfo?.userId && (
                      <>
                        <div className='flex justify-end'>
                          <div className='inline-block text-right'>
                            <Button
                              onClick={() => {
                                downloadReport(quotationId, quotation)
                              }}
                              type='primary'
                              className='bg-mbprimary mr-2'
                            >
                              Tải xuống PDF
                            </Button>
                          </div>
                        </div>
                      </>
                    )}
                    {/* trường hợp kh chưa đăng nhập */}
                    {!isCustomerPreview && !user.id && (
                      <div className='flex justify-end'>
                        <Button
                          type='default'
                          className='mr-2 '
                          onClick={() => {
                            setVisibleModal1(true)
                          }}
                        >
                          Xác nhận báo giá
                        </Button>
                        <Button
                          onClick={() => {
                            downloadReport(quotationId, quotation)
                          }}
                          type='primary'
                          className='bg-mbprimary mr-2'
                        >
                          Tải xuống PDF
                        </Button>
                      </div>
                    )}

                    <UpdateRequestModal
                      openModal={isShowUpdateConfirm}
                      setOpenModal={setIsShowUpdateConfirm}
                      quotationId={quotationId}
                    />
                    <ConfirmModal
                      width={384}
                      btnText='Đăng nhập'
                      modalTitle={<i className='onedx-dialog size-10' />}
                      open={visibleModal1}
                      setOpen={setVisibleModal1}
                      onClickCancel={() => setVisibleModal1(false)}
                      onClick={handleLogin}
                      description={
                        <>
                          <div className='text-headline-16 font-semibold text-text-neutral-strong'>Xin chào</div>
                          <div className='text-body-14 font-normal'>
                            Vui lòng đăng nhập hoặc{' '}
                            <span
                              className='cursor-pointer text-primary underline'
                              onClick={() => router.push('/sme-portal/register')}
                            >
                              Đăng ký
                            </span>{' '}
                            để tiếp tục mua dịch vụ
                          </div>
                        </>
                      }
                      btnCancel='Đóng'
                    />
                  </>
                )}
              {/* các trường hợp màn hình admin */}
              {isCustomerPreview && (
                <div className='flex justify-end gap-2'>
                  {[QuotationStatus.PROCESSING, QuotationStatus.NEW, QuotationStatus.SENT].includes(
                    quotation?.status
                  ) &&
                    !validate?.hasError && (
                      <Button type='default' disabled>
                        Yêu cầu cập nhật
                      </Button>
                    )}
                  {[QuotationStatus.PROCESSING, QuotationStatus.NEW, QuotationStatus.SENT].includes(
                    quotation?.status
                  ) &&
                    !quotation?.couponExpired &&
                    !validate?.hasError && (
                      <Button type='default' disabled>
                        Xác nhận báo giá
                      </Button>
                    )}
                  {!(validate?.hasError && validate?.code === 'disabledProduct') && (
                    <Button
                      type='primary'
                      onClick={() => {
                        downloadReport(quotationId, quotation)
                      }}
                    >
                      Tải xuống PDF
                    </Button>
                  )}
                </div>
              )}
            </div>
          </Card>
        </div>
      </div>
    </div>
  )
}
