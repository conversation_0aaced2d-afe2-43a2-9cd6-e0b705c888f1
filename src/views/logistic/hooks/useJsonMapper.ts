import type React from 'react'
import { useEffect, useRef, useState } from 'react'

import type { UploadFile } from 'antd/es/upload/interface'

import { useMutation, useQuery } from '@tanstack/react-query'

import { isEmpty } from 'lodash'

import { message } from '@components/notification'
import LogisticInstance from '@/models/Logistic'
import type { ErrorMapping } from '@/types/logistic/carrier'

export function useJsonMapper(code: string, requestType: 'req' | 'res' = 'req', maxSizeMB: number = 200) {
  const [errorMapping, setErrorMapping] = useState<any[]>([])

  const convertAPI = useMutation({
    mutationKey: ['logistic-data'],
    mutationFn: LogisticInstance.uploadJSON,
    onSuccess: () => {
      console.log('API post executing with code:', code)
      // if (res.RESPONSE_CODE === '00') {
      //   message.success('L<PERSON>y dữ liệu thành công')
      // } else {
      //   message.error('Có lỗi xảy ra')
      //   console.log('C<PERSON> lỗi xảy ra, useJsonMapper, onSuccess')
      // }
    },
    onError: () => {
      message.error('Có lỗi xảy ra, useJsonMapper, onError')
      console.log('Có lỗi xảy ra, useJsonMapper, onError')
    }
  })

  const onSendFormatAPI = async (data: any) => {
    try {
      const response = await convertAPI.mutateAsync(data)

      return response
    } catch (error) {
      console.error('Error sending data:', error)
      throw error
    }
  }

  const convertErrorMapping = (data: any): ErrorMapping[] => {
    if (Array.isArray(data)) {
      return data.map(item => ({
        httpStatusCode: item.httpStatus,
        partnerCode: item.httpCode,
        note: item.description
      }))
    }

    return []
  }

  const { data: lstDataTemplate } = useQuery({
    queryKey: ['logisticTemplate', code, requestType],
    queryFn: async () => {
      const res = await LogisticInstance.dataTemplate(code)

      if (requestType === 'res' && res.errorMapping) {
        setErrorMapping(convertErrorMapping(res.errorMapping))
      }

      return requestType === 'req' ? res.request : res.response
    },
    enabled: !!code
  })

  const fileInputRef = useRef<HTMLInputElement>(null)

  const [fileList, setFileList] = useState<UploadFile[]>([])
  const [dataRows, setDataRows] = useState<any[]>([])
  const [mappingRows, setMappingRows] = useState<any[]>([])
  const [currentFileName, setCurrentFileName] = useState<string>('')

  // when the query comes back:
  useEffect(() => {
    if (!isEmpty(lstDataTemplate) && Array.isArray(lstDataTemplate)) {
      setMappingRows(
        lstDataTemplate.map((r: any, i: number) => ({
          key: r.key ?? String(i),
          ...r,
          sourceField: r.sourceField ?? null
        }))
      )
    } else if (lstDataTemplate === null) {
      setMappingRows([])
    }
  }, [lstDataTemplate])

  // Hàm đệ quy để parse tất cả JSON strings trong object
  function parseNestedJsonStrings(obj: any): any {
    if (obj === null || obj === undefined) {
      return obj
    }

    // Nếu là array, đệ quy từng phần tử
    if (Array.isArray(obj)) {
      return obj.map(item => parseNestedJsonStrings(item))
    }

    // Nếu là object
    if (typeof obj === 'object') {
      const result: any = {}

      for (const [key, value] of Object.entries(obj)) {
        result[key] = parseNestedJsonStrings(value)
      }

      return result
    }

    // Nếu là string, thử parse JSON
    if (typeof obj === 'string') {
      try {
        // Kiểm tra xem có phải JSON string không
        if ((obj.startsWith('{') && obj.endsWith('}')) || (obj.startsWith('[') && obj.endsWith(']'))) {
          const parsed = JSON.parse(obj)
          // Đệ quy tiếp để parse các nested JSON strings

          return parseNestedJsonStrings(parsed)
        }
      } catch {
        // Không phải JSON string, giữ nguyên
      }
    }

    // Các kiểu dữ liệu khác (number, boolean, etc.) giữ nguyên
    return obj
  }

  function getValueByPath(obj: any, path: any) {
    try {
      if (path.includes('[*]')) {
        const parts = path.split('.')
        let current = obj

        for (const part of parts) {
          if (part.includes('[*]')) {
            const arrayKey = part.replace('[*]', '')

            if (current[arrayKey] && Array.isArray(current[arrayKey])) {
              for (const item of current[arrayKey]) {
                if (item !== undefined) {
                  current = item
                  break
                }
              }
            } else {
              return undefined
            }
          } else {
            current = current?.[part]
          }

          if (current === undefined) break
        }

        return current
      } else {
        return path.split('.').reduce((curr: any, key: any) => curr?.[key], obj)
      }
    } catch (error) {
      return undefined
    }
  }

  function getDataType(value: any): string {
    if (value === null) return 'Null'
    if (value === undefined) return 'Undefined'
    if (Array.isArray(value)) return 'Array'

    // Kiểm tra Date object
    if (value instanceof Date) {
      return isNaN(value.getTime()) ? 'Invalid Date' : 'Date'
    }

    // Kiểm tra string có phải là date format không
    if (typeof value === 'string') {
      const trimmedValue = value.trim()

      // Nếu là string rỗng hoặc chỉ chứa số (như '222')
      if (trimmedValue === '' || /^\d+$/.test(trimmedValue)) {
        return 'String'
      }

      // Kiểm tra các format date cụ thể
      const dateRegexes = [
        /^\d{4}-\d{2}-\d{2}$/, // YYYY-MM-DD
        /^\d{2}\/\d{2}\/\d{4}$/, // DD/MM/YYYY hoặc MM/DD/YYYY
        /^\d{2}-\d{2}-\d{4}$/, // DD-MM-YYYY hoặc MM-DD-YYYY
        /^\d{4}\/\d{2}\/\d{2}$/, // YYYY/MM/DD
        /^\d{1,2}\/\d{1,2}\/\d{4}$/, // D/M/YYYY hoặc M/D/YYYY
        /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/, // ISO format
        /^\d{2}\/\d{2}\/\d{2}$/ // DD/MM/YY
      ]

      // Chỉ kiểm tra Date constructor nếu match với các pattern trên
      const hasDateFormat = dateRegexes.some(regex => regex.test(trimmedValue))

      if (hasDateFormat) {
        const date = new Date(trimmedValue)

        if (!isNaN(date.getTime())) {
          return 'Date String'
        }
      }

      // Kiểm tra một số format date phổ biến khác (không phải chỉ số)
      const commonDateFormats = [
        /^(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s+\d{1,2},?\s+\d{4}$/i, // Jan 1, 2023
        /^\d{1,2}\s+(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s+\d{4}$/i, // 1 Jan 2023
        /^(Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday)/i // Các ngày trong tuần
      ]

      const hasCommonDateFormat = commonDateFormats.some(regex => regex.test(trimmedValue))

      if (hasCommonDateFormat) {
        const date = new Date(trimmedValue)

        if (!isNaN(date.getTime())) {
          return 'Date String'
        }
      }

      return 'String'
    }

    if (typeof value === 'object') return 'Object'

    if (typeof value === 'number') {
      if (Number.isInteger(value)) {
        const valueStr = value.toString()

        if (valueStr.length === 10 || valueStr.length === 13) {
          const date = new Date(valueStr.length === 10 ? value * 1000 : value)

          if (!isNaN(date.getTime())) {
            return 'Timestamp'
          }
        }
      }

      return 'Number'
    }

    if (typeof value === 'boolean') return 'Boolean'

    return 'Unknown'
  }

  function createOptionsWithTypes(jsonObj: any, pathsArray: any): any[] {
    return pathsArray.map((path: any) => {
      const value = getValueByPath(jsonObj, path)
      const sourceDataType = getDataType(value)

      return {
        label: path,
        value: path,
        sourceDataType: sourceDataType,
        sourceExampleValue: value
      }
    })
  }

  function onUploadChange(info: { file: any; fileList: UploadFile[] }) {
    const newList = info.fileList

    setFileList(newList)

    const lastFileRecord = newList[newList.length - 1]
    const rawFile = lastFileRecord?.originFileObj

    if (!rawFile) {
      console.warn('No originFileObj found:', lastFileRecord)

      return
    }

    const isLtMaxSize = rawFile.size / 1024 / 1024 < maxSizeMB

    if (!isLtMaxSize) {
      message.error(`Dung lượng file tải lên tối đa ${maxSizeMB}MB!`)
      setFileList([])
      setDataRows([])

      return
    }

    setCurrentFileName(rawFile.name)

    const reader = new FileReader()

    reader.onload = async e => {
      try {
        let json = JSON.parse(e.target?.result as string)

        json = parseNestedJsonStrings(json)

        const apiResponse = await onSendFormatAPI(json)

        setDataRows(createOptionsWithTypes(json, apiResponse))
      } catch {
        message.error('Vui lòng tải file định dạng JSON')
        setFileList([])
        setDataRows([])
      }
    }

    reader.readAsText(rawFile)
  }

  // Thay thế hàm reset bằng hàm replaceFile
  function replaceFile() {
    if (fileInputRef.current) {
      fileInputRef.current.click()
    }
  }

  // Xử lý khi chọn file thay thế
  function handleFileReplace(event: React.ChangeEvent<HTMLInputElement>) {
    const file = event.target.files?.[0]

    if (!file) return

    setCurrentFileName(file.name)

    // Tạo UploadFile object giống như Ant Design Upload
    const uploadFile: UploadFile = {
      uid: Date.now().toString(),
      name: file.name,
      status: 'done',
      originFileObj: file as any
    }

    // Cập nhật fileList
    setFileList([uploadFile])

    // Đọc và xử lý file
    const reader = new FileReader()

    reader.onload = async e => {
      try {
        let json = JSON.parse(e.target?.result as string)

        json = parseNestedJsonStrings(json)
        const apiResponse = await onSendFormatAPI(json)

        setDataRows(createOptionsWithTypes(json, apiResponse))

        // Reset mapping rows về trạng thái ban đầu với template
        if (lstDataTemplate) {
          setMappingRows(
            lstDataTemplate.map((r: any, i: number) => ({
              key: r.key ?? String(i),
              ...r,
              sourceField: r.sourceField ?? null
            }))
          )
        } else if (lstDataTemplate === null) {
          setMappingRows([])
        }
      } catch {
        message.error('Có lỗi xảy ra, reader.onload handleFileReplace')
        console.error('Có lỗi xảy ra, reader.onload handleFileReplace')
        setFileList([])
        setDataRows([])
      }
    }

    reader.readAsText(file)

    // Reset input value để có thể chọn lại file cùng tên
    event.target.value = ''
  }

  return {
    errorMapping,
    setErrorMapping,
    fileList,
    dataRows,
    setDataRows,
    onUploadChange,
    replaceFile,
    handleFileReplace,
    fileInputRef,
    mappingRows,
    setMappingRows,
    isLoadingFormatAPI: convertAPI.isPending,
    currentFileName
  }
}
