'use client'

import React from 'react'
import type { FC } from 'react'

import { useRouter } from 'next/navigation'

import { Button } from 'antd'

import { CarrierTable } from '@components/logistic/CarrierTable'

const CarriersView: FC = () => {
  // const [carriers] = useState<LogisticCarrier[]>([])
  const router = useRouter()

  const handleCreateNew = () => {
    router.push('/carrier/create')
  }

  return (
    <div className='max-w-full overflow-hidden rounded-[12px] bg-[#F2F4f9]'>
      <div className='mb-2 flex w-full items-center justify-between bg-white p-4'>
        <div className='flex items-center gap-3 text-xl font-semibold text-black'>
          <div className='h-5 w-1 rounded-sm bg-gray-3'></div>
          Danh sách đơn vị vận chuyển
        </div>
        <div className='flex gap-4'>
          <Button onClick={handleCreateNew} type='primary'>
            <i className='onedx-add' /> Tạo đơn vị vận chuyển
          </Button>
        </div>
      </div>
      <CarrierTable />
    </div>
  )
}

export default CarriersView
