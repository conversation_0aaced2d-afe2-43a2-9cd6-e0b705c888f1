import React from 'react'

import { Modal, Button, Upload } from 'antd'

import { styled } from '@mui/material'

interface UpdateFileModalProps {
  visible: boolean
  onClose: () => void
  dataObject: any
  currentFileName: string
}

const UploadFullWidth = styled(Upload)`
  .ant-upload {
    width: 100%;
    display: flex;
  }
  .ant-modal-content {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
`

const UpdateFileModal: React.FC<UpdateFileModalProps> = ({ visible, onClose, dataObject, currentFileName }) => {
  const onConfirm = () => {
    const event = {
      target: { files: [dataObject.fileList[0].originFileObj] }
    }

    dataObject.handleFileReplace(event)
    onClose()
  }

  const fileNameCopy = currentFileName

  return (
    <Modal
      title={
        <div>
          <div className='px-6 text-base font-semibold'>Cập nhật file</div>
        </div>
      }
      open={visible}
      onCancel={() => {
        onClose()
      }}
      width={680}
      height={446}
      footer={null}
    >
      <div className='w-full border-t border-solid border-slate-200'></div>
      <div className='px-6'>
        <div className='caption-12-medium mb-1 mt-5 text-text-neutral-strong'>File đã tải</div>
        <div className='flex flex-col gap-4 rounded-lg bg-bg-neutral-lightest'>
          <div className='flex items-center gap-2 py-2'>
            <div className='flex rounded-xl'>
              <i className='onedx-json size-8' />
            </div>
            <div className='text-left text-text-neutral-strong'>{fileNameCopy}</div>
          </div>
        </div>

        <div className='caption-12-medium mb-1 mt-5 text-text-neutral-strong'>Cập nhật file mới</div>
        <div className='flex flex-col gap-4 rounded-lg bg-bg-neutral-lightest p-3'>
          <label className="before:mr-1 before:inline-block before:font-[SimSun,sans-serif] before:text-sm before:leading-none before:text-[#ff4d4f] before:content-['*']">
            Tải file JSON của đối tác
          </label>
          <UploadFullWidth
            accept='.json'
            beforeUpload={() => false}
            onChange={dataObject.onUploadChange}
            maxCount={1}
            showUploadList={false}
            fileList={dataObject.fileList}
            className='w-full rounded-lg border border-dashed border-text-info-default bg-white pl-3'
          >
            <div onClickCapture={e => e.preventDefault()}>
              <div className='flex items-center justify-center gap-6 py-2'>
                <div className='flex items-center justify-center rounded-xl bg-bg-primary-lighter p-[10px]'>
                  <i className='onedx-json size-8' />
                </div>
                {dataObject.fileList.length > 0 ? (
                  <div className='text-left text-text-neutral-strong'>{dataObject.fileList[0].name}</div>
                ) : (
                  <div className='text-left'>
                    <div className='body-14-medium text-text-neutral-strong'>Tải file JSON lên hoặc kéo thả</div>
                    <div className='mt-2 text-caption-12 text-text-neutral-medium'>
                      <>
                        Định dạng file <strong>JSON</strong> - Dung lượng <strong>(&lt;{1}MB)</strong> - Kích thước
                        <strong> (1170.390px)</strong>
                      </>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </UploadFullWidth>
        </div>
      </div>
      <div className='my-4 w-full border-t border-solid border-slate-200'></div>
      <div className='my-4 flex px-6'>
        <div className={'ml-auto flex gap-2'}>
          <Button
            key='cancel'
            color='primary'
            variant='outlined'
            onClick={() => {
              onClose()
            }}
          >
            Đóng
          </Button>
          <Button key='submit' type='primary' onClick={onConfirm}>
            Xác nhận
          </Button>
        </div>
      </div>
    </Modal>
  )
}

export default UpdateFileModal
