import { useMemo, useState, type FC } from 'react'

import { useRouter, useSearchParams } from 'next/navigation'

import { Form } from 'antd'

import { useMutation, useQueries, useQueryClient } from '@tanstack/react-query'

import { isNil } from 'lodash'

import type {
  CarrierActionList,
  CreateCarrierApiConfigDto,
  CreateLogisticCarrierDto,
  UpdateCarrierApiConfigDto,
  UpdateLogisticCarrierDto
} from '@/types/logistic/carrier'

import { CarrierForm } from '@/components/logistic/CarrierForm'
import { LogisticTitle } from '@/components/logistic/common/LogisticComponents'
import CreateApiConfigDrawer from '@/components/logistic/api-config/CreateApiConfigDrawer'
import {
  convertSubmitData,
  handleConvertConnectItem,
  validateCarrier,
  validateDuplicateConnectItem,
  validateDataMapping
} from '@/components/logistic/common/utils'
import LogisticInstance from '@/models/Logistic'
import { message } from '@/components/notification'
import { useLocalState } from '@/hooks'

/**
 * @note Component này xử dụng Form.Provider để quản lý các form con bên trong, bao gồm cả form tạo đơn vị vận chuyển và form cấu hình API kết nối.
 * @note Khi submit form, sẽ gọi hàm onFormFinish để xử lý dữ liệu
 * @note Hàm này sẽ kiểm tra tên form và gọi hàm xử lý tương ứng
 * @note Nếu là form 'carrier-form', sẽ lấy dữ liệu từ form và gọi hàm handleSubmit để xử lý tạo hoặc cập nhật đơn vị vận chuyển
 * @note Nếu là form 'api-config-form', sẽ lấy dữ liệu từ form và gọi hàm addConnectItemToCarrierForm để thêm cấu hình kết nối vào danh sách kết nối của đơn vị vận chuyển (connectList)
 * @returns
 */
const CreateCarrierView: FC<{ id?: string }> = ({ id }) => {
  const router = useRouter()
  const searchParams = useSearchParams()
  const openDrawerParam = searchParams.get('openDrawer')

  const queryClient = useQueryClient()
  const { setFieldValue: setLocalField } = useLocalState()

  const [openDrawerCreate, setOpenDrawerCreate] = useState(false)

  const [titleDrawer, setTitleDrawer] = useState('Tạo cấu hình kết nối API')

  const [initialConnectItem, setInitialConnectItem] = useState<
    Partial<CreateCarrierApiConfigDto | UpdateCarrierApiConfigDto> | undefined
  >()

  const [carrierActionQuery, carrierDetailQuery] = useQueries({
    queries: [
      {
        queryKey: ['carrier-action-list'],
        queryFn: async () => {
          const response: CarrierActionList[] = await LogisticInstance.getActionList()

          setLocalField('carrierActionList', response)

          return {
            data: response,
            options:
              response?.map((item: any) => ({
                label: item.name || item.action || item.code,
                value: item.code || item.action
              })) ?? [],
            requiredList: response?.filter(item => item.required) ?? []
          }
        }
      },
      {
        queryKey: ['carrier', id],
        queryFn: async () => {
          const detail: UpdateLogisticCarrierDto | undefined = await LogisticInstance.getCarrierDetail(id)

          const connectList = detail?.connectList
          const isEmptyConnectList = !Array.isArray(connectList) || connectList.length === 0

          const isOpenDrawer =
            !isEmptyConnectList && !!openDrawerParam && connectList.find((item: any) => item.action === openDrawerParam)

          if (isOpenDrawer) {
            setTitleDrawer('Cập nhật cấu hình kết nối API')
            setInitialConnectItem(connectList.find((item: any) => item.action === openDrawerParam))
            setOpenDrawerCreate(true)
          }

          return detail
        },
        enabled: !!id
      }
    ]
  })

  const initialData = useMemo(() => {
    return {
      ...carrierDetailQuery.data,
      id: Number(id) || undefined
    }
  }, [carrierDetailQuery.data, id])

  const carrierAction = carrierActionQuery.data
  const isLoading = carrierDetailQuery.isLoading || carrierActionQuery.isLoading

  const createCarrierMutation = useMutation({
    mutationFn: LogisticInstance.createCarrier,
    onSuccess: () => {
      router.push(`/carrier/list`)

      queryClient.invalidateQueries({
        queryKey: ['getCarrierList']
      })

      // Handle success, e.g., show a notification or redirect
      message.success('Tạo đơn vị vận chuyển thành công')
    },
    onError: error => {
      console.error('Error creating carrier:', error)
      message.error('Tạo đơn vị vận chuyển thất bại')
      // Handle error, e.g., show an error message
    }
  })

  const updateCarrierMutation = useMutation({
    mutationFn: LogisticInstance.updateCarrier,
    onSuccess: () => {
      message.success('Cập nhật đơn vị vận chuyển thành công')
    },
    onError: error => {
      console.error('Error updating carrier:', error)
      message.error('Cập nhật đơn vị vận chuyển thất bại')
      // Handle error, e.g., show an error message
    }
  })

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const handleSubmit = (data: CreateLogisticCarrierDto | UpdateLogisticCarrierDto) => {
    if (!id) {
      // This is a create operation
      const createData = data as CreateLogisticCarrierDto

      // Handle create
      createCarrierMutation.mutate(createData)
    } else {
      // This is an update operation
      const updateData = data as UpdateLogisticCarrierDto

      // Handle update
      updateCarrierMutation.mutate({
        id: Number(id),
        ...updateData
      })
    }
  }

  const handleDraftSubmit = (data: CreateLogisticCarrierDto | UpdateLogisticCarrierDto) => {
    if (!!id) {
      return // This is an update operation
    }

    const draftData = {
      ...data,
      status: 'INACTIVE',
      state: 'DRAFT'
    } as CreateLogisticCarrierDto

    createCarrierMutation.mutate(draftData)
  }

  const addConnectItemToCarrierForm = (forms: any) => {
    const form = forms['api-config-form']
    const carrierForm = forms['carrier-form']
    const values = form.getFieldsValue(true)

    const connectListItem = handleConvertConnectItem(values)

    const oldConnectList = carrierForm.getFieldValue('connectList') || []

    /**
     * @note Có thể thêm pageType vào carrierForm để xác định update hay create
     * @note Hiện tại đang sử dụng isNil(values.key) để xác định xem có phải là tạo mới hay không
     * @note do key chỉ được thêm vào sau khi submit form, nên nếu không có key thì sẽ là tạo mới
     */
    const isValidConnectItem = validateDuplicateConnectItem({
      data: connectListItem,
      extra: { connectList: oldConnectList },
      enabled: isNil(values.key)
    })

    if (!isValidConnectItem) {
      console.warn('Tạo cấu hình kết nối không hợp lệ, vui lòng kiểm tra lại thông tin cấu hình API.')

      return
    }

    const existingIndex = oldConnectList.findIndex(
      (item: { action: string | number | undefined }) => item.action === connectListItem.action
    )

    let newConnectList

    if (existingIndex !== -1) {
      newConnectList = oldConnectList.map((item: any, index: any) => (index === existingIndex ? connectListItem : item))
    } else {
      newConnectList = [...oldConnectList, connectListItem]
    }

    carrierForm.setFieldsValue({
      connectList: newConnectList
    })

    form.resetFields()
  }

  const handleOpenApiConfig = ({
    type = 'create',
    initData
  }: {
    type?: 'create' | 'update' | undefined
    initData?: Partial<CreateCarrierApiConfigDto | UpdateCarrierApiConfigDto>
  }) => {
    if (type === 'create') {
      setTitleDrawer('Tạo cấu hình kết nối API')
      setInitialConnectItem(undefined)
      setOpenDrawerCreate(true)
    } else if (type === 'update' && !isNil(initData?.action)) {
      setTitleDrawer('Cập nhật cấu hình kết nối API')
      setInitialConnectItem(initData)
      setOpenDrawerCreate(true)
    } else {
      console.warn('Invalid type or missing key for update operation')
    }
  }

  const handleFormProviderFinish = (name: string, { forms }: { forms: any }) => {
    if (name === 'carrier-form') {
      const form = forms['carrier-form']
      const values = form.getFieldsValue(true)
      const submitData = convertSubmitData(values)

      // Validate the data mapping before proceeding
      const requiredActions: CarrierActionList[] = Array.isArray(carrierAction?.requiredList)
        ? carrierAction.requiredList
        : []

      const { isValid, type } = validateCarrier(submitData, {
        checkActiveStatus: true,
        requiredActions
      })

      if (!isValid && type !== 'invalid') {
        console.warn('Validation failed, cannot proceed with carrier creation.')

        return
      }

      handleSubmit(submitData)
    } else if (name === 'api-config-form') {
      const isValid = validateDataMapping(forms['api-config-form'].getFieldsValue(true))

      if (!isValid) {
        console.warn('Validation failed, cannot proceed with API config creation.')

        return
      }

      addConnectItemToCarrierForm(forms)

      setOpenDrawerCreate(false) // Close the drawer after submission
    } else if (name === 'draft-form') {
      const form = forms['carrier-form']
      const values = form.getFieldsValue(true)
      const submitData = convertSubmitData(values)

      // Validate the data mapping before proceeding
      handleDraftSubmit(submitData)
    } else {
      console.warn(`Unhandled form name: ${name}`)
    }

    setOpenDrawerCreate(false) // Close the drawer after submission
  }

  return (
    <Form.Provider onFormFinish={handleFormProviderFinish}>
      <div className='flex min-h-screen flex-col gap-2'>
        <LogisticTitle navigation={{ goBack: () => router.push('/carrier/list') }}>
          {id ? 'Cập nhật đơn vị vận chuyển' : 'Tạo đơn vị vận chuyển'}
        </LogisticTitle>
        <CarrierForm onOpenApiConfig={handleOpenApiConfig} initialData={initialData} />
        <CreateApiConfigDrawer
          carrierAction={{
            ...carrierAction,
            isLoading
          }}
          open={openDrawerCreate}
          setOpen={setOpenDrawerCreate}
          initialData={initialConnectItem}
          title={titleDrawer}
        />
      </div>
    </Form.Provider>
  )
}

export default CreateCarrierView
