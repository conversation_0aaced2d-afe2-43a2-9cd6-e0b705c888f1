'use client'

import type { FC } from 'react'

import { useParams } from 'next/navigation'

import { useQuery } from '@tanstack/react-query'

import Logistic from '@/models/Logistic'

import type { LogisticCarrier } from '@/types/logistic/carrier'

import CarrierDetails from '@/components/logistic/CarrierDetails'

const CarrierDetailsView: FC = () => {
  const { id } = useParams<{ id: string }>()

  const { data: carrier } = useQuery<LogisticCarrier>({
    queryKey: ['carrierDetail', id],
    queryFn: () => Logistic.getCarrierDetail(id),
    enabled: !!id
  })

  if (!carrier) {
    return <div>Loading...</div>
  }

  return (
    <div>
      <CarrierDetails carrier={carrier} />
    </div>
  )
}

export default CarrierDetailsView
