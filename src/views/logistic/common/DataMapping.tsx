import React, { useEffect, useMemo, useState } from 'react'

import { Upload, Table, Button, Tabs, Select, Spin, Tag, Form } from 'antd'
import clsx from 'clsx'

import { styled } from 'styled-components'

import { isEmpty } from 'lodash'

import { message } from '@components/notification'
import { useJsonMapper } from '../hooks/useJsonMapper'
import ConvertDataConfig from './ConvertDataConfig'
import FormatTags from './FormatTags'
import { EmptyFormItem } from '@views/auth/register/components/RegisterPersonalForm'
import UpdateFileModal from '../UpdateFileModal'
import { If } from '@/components/common'
import { LogisticCard, LogisticHeader } from '@/components/logistic/common/LogisticComponents'
import { stylesCard } from '@/components/logistic/common/constant'
import { errorMappingColumns, shouldUpdateFields } from '@/components/logistic/common/utils'
import type { ErrorMapping } from '@/types/logistic/carrier'

const EmptyTabNavigation = styled(Tabs)`
  &.ant-tabs > .ant-tabs-nav {
    display: none;
  }
`

const UploadFullWidth = styled(Upload)`
  .ant-upload {
    inline-size: 100%;
    display: flex;
  }
`

type Props = {
  reqCode: string
  resCode: string
  form: any
  initData?: any
  isUseInPopUp?: boolean
  isRequiredRequestObject?: boolean
  isRequiredResponseObject?: boolean
  isVisibleRequestObject?: boolean
  isVisibleResponseObject?: boolean
}

export default function DataMapping({
  reqCode,
  resCode,
  form,
  initData,
  isRequiredRequestObject = true,
  isRequiredResponseObject = true,
  isVisibleRequestObject = true,
  isVisibleResponseObject = true
}: Props) {
  const req = useJsonMapper(reqCode, 'req', 10)
  const res = useJsonMapper(resCode, 'res', 10)

  const [srcField, setSrcField] = useState<any>(null)
  const [dstField, setDstField] = useState<any>(null)
  const [srcFormat, setSrcFormat] = useState<any>(null)
  const [dstFormat, setDstFormat] = useState<any>(null)
  const [initConfigData, setInitConfigData] = useState<any>(null)

  const [activeTab, setActiveTab] = useState<'request' | 'response'>(isVisibleRequestObject ? 'request' : 'response')
  const [openConvertDataConfig, setOpenConvertDataConfig] = useState(false)
  const [openUploadFile, setOpenUploadFile] = useState(false)

  const [dataRestored, setDataRestored] = useState(false)

  useEffect(() => {
    form.setFieldsValue({
      requestObject: req.mappingRows,
      responseObject: res.mappingRows,
      dropdownRequestObject: req.dataRows,
      dropdownResponseObject: res.dataRows,
      errorMapping: res.errorMapping
    })
  }, [req.mappingRows, res.mappingRows, form, req.dataRows, res.dataRows, res.errorMapping])

  useEffect(() => {
    if (
      !dataRestored &&
      initData &&
      (initData.requestObject?.length > 0 ||
        initData.responseObject?.length > 0 ||
        initData.dropdownRequestObject?.length > 0 ||
        initData.dropdownResponseObject?.length > 0 ||
        initData.errorMapping?.length > 0)
    ) {
      if (initData.requestObject?.length > 0) {
        req.setMappingRows(initData.requestObject)
      }

      if (initData.responseObject?.length > 0) {
        res.setMappingRows(initData.responseObject)
      }

      if (initData.dropdownRequestObject?.length > 0) {
        req.setDataRows(initData.dropdownRequestObject)
      }

      if (initData.dropdownResponseObject?.length > 0) {
        res.setDataRows(initData.dropdownResponseObject)
      }

      if (initData.errorMapping?.length > 0) {
        res.setErrorMapping(initData.errorMapping)
      }

      setDataRestored(true)
    }
  }, [initData, req, res, dataRestored])

  useEffect(() => {
    form.setFieldsValue({
      isRequiredRequestObject,
      isRequiredResponseObject
    })
  }, [form, isRequiredRequestObject, isRequiredResponseObject])

  useEffect(() => {
    form.setFieldsValue({
      isVisibleRequestObject,
      isVisibleResponseObject
    })
  }, [form, isVisibleRequestObject, isVisibleResponseObject])

  useEffect(() => {
    if (!isVisibleRequestObject) {
      setActiveTab('response')
    }

    if (!isVisibleResponseObject) {
      setActiveTab('request')
    }
  }, [isVisibleRequestObject, isVisibleResponseObject])

  const onClickConvertDataConfig = (record: any) => {
    const newSrcField = activeTab === 'request' ? record.name : record.sourceField
    const newDstField = activeTab === 'request' ? record.sourceField : record.name

    const newSrcFormat =
      activeTab === 'request'
        ? record.type
        : res.dataRows.find((item: any) => item.value === record.sourceField)?.sourceDataType

    const newDstFormat =
      activeTab === 'request'
        ? req.dataRows.find((item: any) => item.value === record.sourceField)?.sourceDataType
        : record.type

    if (record.mappingData) {
      const initData = record.mappingData

      setInitConfigData(initData)
    } else {
      setInitConfigData(null)
    }

    setSrcField(newSrcField)
    setDstField(newDstField)
    setSrcFormat(newSrcFormat)
    setDstFormat(newDstFormat)

    if (newSrcField && newDstField && newSrcFormat && newDstFormat) {
      setOpenConvertDataConfig(true)
    } else {
      message.error('Vui lòng chọn trường nguồn')
    }
  }

  const getAvailableOptions = useMemo(() => {
    return (currentRecordKey: string) => {
      const allOptions = activeTab === 'request' ? req.dataRows : res.dataRows
      const mappingRows = activeTab === 'request' ? req.mappingRows : res.mappingRows

      // Sử dụng Set cho performance tốt hơn: O(1) lookup thay vì O(n)
      const usedFieldsSet = new Set(
        mappingRows
          .filter((row: any) => row.key !== currentRecordKey && row.sourceField)
          .map((row: any) => row.sourceField)
      )

      return allOptions.filter((option: any) => !usedFieldsSet.has(option.value))
    }
  }, [activeTab, req.dataRows, res.dataRows, req.mappingRows, res.mappingRows])

  const columns = [
    {
      title: 'Trường đích',
      dataIndex: 'name',
      width: 200,
      key: 'name'
    },
    {
      title: 'Kiểu dữ liệu đích',
      dataIndex: 'type',
      width: 150,
      key: 'type'
    },
    {
      title: 'Format đích',
      dataIndex: 'format',
      key: 'format',
      width: 300,
      render: (_: string, record: any) => {
        return <FormatTags format={record.format} maxWidth={250} />
      }
    },
    {
      title: 'Trường nguồn',
      dataIndex: 'sourceField',
      key: 'sourceField',
      width: 300,
      render: (_: string, record: any) => {
        return (
          <>
            <Select
              options={getAvailableOptions(record.key)}
              value={record.sourceField}
              allowClear
              showSearch
              onChange={(val: string) => {
                const selectedItem = getAvailableOptions(record.key).find(
                  (item: { value: string }) => item.value === val
                )

                activeTab === 'request'
                  ? req.setMappingRows((rows: any[]) =>
                      rows.map((r: any) =>
                        r.key === record.key
                          ? {
                              ...r,
                              sourceField: val,
                              sourceExampleValue: selectedItem?.sourceExampleValue,
                              sourceDataType: selectedItem?.sourceDataType,
                              mappingData: null
                            }
                          : r
                      )
                    )
                  : res.setMappingRows((rows: any) =>
                      rows.map((r: any) =>
                        r.key === record.key
                          ? {
                              ...r,
                              sourceField: val,
                              sourceExampleValue: selectedItem?.sourceExampleValue,
                              sourceDataType: selectedItem?.sourceDataType,
                              mappingData: null
                            }
                          : r
                      )
                    )
              }}
            ></Select>
          </>
        )
      }
    },
    {
      title: 'Kiểu dữ liệu nguồn',
      dataIndex: 'typeSource',
      key: 'typeSource',
      width: 100,
      render: (value: string, record: any) => {
        return (
          <div>
            {activeTab === 'request'
              ? req.dataRows.find((item: any) => item.value === record.sourceField)?.sourceDataType
              : res.dataRows.find((item: any) => item.value === record.sourceField)?.sourceDataType}
          </div>
        )
      }
    },
    {
      title: 'Trạng thái',
      dataIndex: 'mandatory',
      key: 'mandatory',
      hidden: activeTab === 'response',
      render: (value: boolean) => {
        return <div>{value ? <Tag color='error'>Bắt buộc</Tag> : <Tag color='success'>Không bắt buộc</Tag>}</div>
      }
    },
    {
      title: 'Chuyển đổi',
      dataIndex: 'mappingData',
      key: 'mappingData',
      width: 150,
      render: (value: any, record: any) => {
        return (
          <If condition={!!record.sourceField}>
            {!value ? (
              <>
                <Button onClick={() => onClickConvertDataConfig(record)}>
                  <i className='onedx-setting1 size-4' /> Cấu hình
                </Button>
              </>
            ) : (
              <>
                <Button onClick={() => onClickConvertDataConfig(record)}>
                  <i className='onedx-eye size-4' /> Xem chi tiết
                </Button>
              </>
            )}
          </If>
        )
      }
    }
  ]

  const current = activeTab === 'request' ? req : res

  return (
    <Spin spinning={req.isLoadingFormatAPI || res.isLoadingFormatAPI}>
      <LogisticCard
        title='Ánh xạ dữ liệu'
        styles={stylesCard}
        rightContent={
          <div className='flex items-center gap-2'>
            <Button.Group>
              {isVisibleRequestObject && (
                <Button
                  type='text'
                  onClick={e => {
                    e.stopPropagation()
                    setActiveTab('request')
                  }}
                  className={clsx(
                    'rounded-none !border-0 !border-b-2 px-4 py-1 font-medium',
                    activeTab === 'request' ? 'border-blue-500 text-blue-500' : 'border-transparent text-gray-800'
                  )}
                >
                  Request
                </Button>
              )}
              <Button
                type='text'
                onClick={e => {
                  e.stopPropagation()
                  setActiveTab('response')
                }}
                className={clsx(
                  'rounded-none !border-0 !border-b-2 px-4 py-1 font-medium',
                  activeTab === 'response' ? 'border-blue-500 text-blue-500' : 'border-transparent text-gray-800'
                )}
              >
                Response
              </Button>
            </Button.Group>
            {current.dataRows.length > 0 && (
              <Button
                type='text'
                className='text-primary-blue'
                onClick={e => {
                  e.stopPropagation()
                  // current.replaceFile()
                  setOpenUploadFile(true)
                }}
              >
                <i className='onedx-edit size-5' /> Cập nhật file tải lên
              </Button>
            )}
          </div>
        }
      >
        <div className='flex flex-col gap-4'>
          <div>
            <input
              ref={req.fileInputRef}
              type='file'
              accept='.json'
              style={{ display: 'none' }}
              onChange={req.handleFileReplace}
            />
            <input
              ref={res.fileInputRef}
              type='file'
              accept='.json'
              style={{ display: 'none' }}
              onChange={res.handleFileReplace}
            />

            <EmptyFormItem name='requestObject' hidden />
            <EmptyFormItem name='responseObject' hidden />
            <EmptyFormItem name='dropdownResponseObject' hidden />
            <EmptyFormItem name='dropdownRequestObject' hidden />
            <EmptyFormItem name='isRequiredRequestObject' hidden />
            <EmptyFormItem name='isRequiredResponseObject' hidden />
            <EmptyFormItem name='isVisibleRequestObject' hidden />
            <EmptyFormItem name='isVisibleResponseObject' hidden />

            <EmptyTabNavigation activeKey={activeTab}>
              {isVisibleRequestObject && (
                <Tabs.TabPane key='request'>
                  <EmptyFormItem name='requestFile'>
                    <div className='flex flex-col gap-4'>
                      {req.dataRows.length === 0 ? (
                        <>
                          <label
                            className={clsx(
                              isRequiredRequestObject &&
                                "before:mr-1 before:inline-block before:font-[SimSun,sans-serif] before:text-sm before:leading-none before:text-[#ff4d4f] before:content-['*']"
                            )}
                          >
                            Tải file JSON lên (Request)
                          </label>
                          <UploadFullWidth
                            accept='.json'
                            fileList={req.fileList}
                            beforeUpload={() => false}
                            onChange={req.onUploadChange}
                            className='w-full rounded-lg border border-dashed border-text-info-default bg-white pl-3'
                          >
                            <div onClickCapture={e => e.preventDefault()}>
                              <div className='flex items-center justify-center gap-6 py-2'>
                                <div className='flex items-center justify-center rounded-xl bg-bg-primary-lighter p-[10px]'>
                                  <i className='onedx-json size-8' />
                                </div>
                                <div className='text-left'>
                                  <div className='body-14-medium text-text-neutral-strong'>
                                    Tải file JSON lên hoặc kéo thả
                                  </div>
                                  <div className='mt-2 text-caption-12 text-text-neutral-medium'>
                                    <>
                                      Định dạng file <strong>JSON</strong> - Dung lượng <strong>(&lt;{10}MB)</strong> -
                                      Kích thước
                                      <strong> (1170.390px)</strong>
                                    </>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </UploadFullWidth>
                        </>
                      ) : (
                        <Table
                          columns={columns}
                          scroll={{ x: 'max-content' }}
                          dataSource={req.mappingRows}
                          pagination={false}
                        />
                      )}
                    </div>
                  </EmptyFormItem>
                </Tabs.TabPane>
              )}

              {isVisibleResponseObject && (
                <Tabs.TabPane key='response'>
                  <EmptyFormItem name='requestFile'>
                    <div className='flex flex-col gap-4 bg-transparent'>
                      {res.dataRows.length === 0 ? (
                        <>
                          <label
                            className={clsx(
                              isRequiredResponseObject &&
                                "before:mr-1 before:inline-block before:font-[SimSun,sans-serif] before:text-sm before:leading-none before:text-[#ff4d4f] before:content-['*']"
                            )}
                          >
                            Tải file JSON lên (Response)
                          </label>
                          <UploadFullWidth
                            accept='.json'
                            fileList={res.fileList}
                            beforeUpload={() => false}
                            onChange={res.onUploadChange}
                            className='w-full rounded-lg border border-dashed border-text-info-default bg-white pl-3'
                          >
                            <div onClickCapture={e => e.preventDefault()}>
                              <div className='flex items-center justify-center gap-6 py-2'>
                                <div className='flex items-center justify-center rounded-xl bg-bg-primary-lighter p-[10px]'>
                                  <i className='onedx-json size-8' />
                                </div>
                                <div className='text-left'>
                                  <div className='body-14-medium text-text-neutral-strong'>
                                    Tải file JSON lên hoặc kéo thả
                                  </div>
                                  <div className='mt-2 text-caption-12 text-text-neutral-medium'>
                                    <>
                                      Định dạng file <strong>JSON</strong> - Dung lượng <strong>(&lt;{10}MB)</strong> -
                                      Kích thước
                                      <strong> (1170.390px)</strong>
                                    </>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </UploadFullWidth>
                        </>
                      ) : (
                        <Table
                          columns={columns}
                          scroll={{ x: 'max-content' }}
                          dataSource={res.mappingRows}
                          pagination={false}
                        />
                      )}
                    </div>
                  </EmptyFormItem>
                </Tabs.TabPane>
              )}
            </EmptyTabNavigation>
          </div>

          <Form.Item noStyle shouldUpdate={shouldUpdateFields.errorMapping}>
            {({ getFieldValue }) => {
              const errorMapping: ErrorMapping[] = getFieldValue('errorMapping') || []
              const dropdownResponseObject = getFieldValue('dropdownResponseObject') || []

              const isExistDropdownResponse =
                !!dropdownResponseObject && !isEmpty(dropdownResponseObject) && dropdownResponseObject.length > 0

              const shouldRenderErrorMapping = isExistDropdownResponse && errorMapping.length > 0

              if (!shouldRenderErrorMapping) {
                return null
              }

              return (
                <>
                  <LogisticHeader color='green' type='medium' title='Xử lý lỗi và mapping response' />
                  <Table<ErrorMapping> columns={errorMappingColumns} dataSource={errorMapping} />
                </>
              )
            }}
          </Form.Item>
        </div>
      </LogisticCard>
      <ConvertDataConfig
        srcField={srcField}
        dstField={dstField}
        srcFormat={srcFormat}
        dstFormat={dstFormat}
        initData={initConfigData}
        open={openConvertDataConfig}
        setOpen={setOpenConvertDataConfig}
        returnData={data => {
          activeTab === 'request'
            ? req.setMappingRows((rows: any) =>
                rows.map((r: any) => (r.name === data.srcField ? { ...r, mappingData: data.item } : r))
              )
            : res.setMappingRows((rows: any) =>
                rows.map((r: any) => (r.name === data.dstField ? { ...r, mappingData: data.item } : r))
              )
        }}
      />

      <UpdateFileModal
        visible={openUploadFile}
        onClose={() => setOpenUploadFile(false)}
        currentFileName={activeTab === 'request' ? req.currentFileName : res.currentFileName}
        dataObject={activeTab === 'request' ? req : res}
      />
    </Spin>
  )
}
