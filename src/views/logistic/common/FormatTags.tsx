import React, { useState, useEffect, useRef } from 'react'

import { Tag, Tooltip } from 'antd'

interface FormatLength {
  min?: number | null
  max?: number | null
}

interface FormatValue {
  min?: number | null
  max?: number | null
}

interface FormatConfig {
  length?: FormatLength
  value?: FormatValue
  regex?: string
  dateTimeFormat?: string
}

interface JsonFormatTagsProps {
  format: FormatConfig
  maxWidth?: number
}

const FormatTags: React.FC<JsonFormatTagsProps> = ({ format, maxWidth = 100 }) => {
  const [visibleTags, setVisibleTags] = useState<string[]>([])
  const [hiddenTags, setHiddenTags] = useState<string[]>([])
  const containerRef = useRef<HTMLDivElement>(null)
  const measureRef = useRef<HTMLDivElement>(null)

  const formatConstraints = (format: FormatConfig): string[] => {
    if (!format) return []

    const constraints: string[] = []

    // Length constraints
    if (format.length) {
      if (format.length.max !== null && format.length.max !== undefined) {
        constraints.push(`Maxlength: ${format.length.max}`)
      }

      if (format.length.min !== null && format.length.min !== undefined) {
        constraints.push(`Minlength: ${format.length.min}`)
      }
    }

    // Value constraints
    if (format.value) {
      if (format.value.max !== null && format.value.max !== undefined) {
        constraints.push(`Maxvalue: ${format.value.max}`)
      }

      if (format.value.min !== null && format.value.min !== undefined) {
        constraints.push(`Minvalue: ${format.value.min}`)
      }
    }

    // Pattern constraint
    if (format.regex) {
      constraints.push(`pattern:${format.regex}`)
    }

    // DateTime format
    if (format.dateTimeFormat) {
      constraints.push(`pattern:${format.dateTimeFormat}`)
    }

    return constraints
  }

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const calculateVisibleTags = () => {
    if (!measureRef.current) return

    const tags = formatConstraints(format)
    const tagElements = measureRef.current.querySelectorAll('.measure-tag')

    let totalWidth = 0
    let visibleCount = 0
    const plusTagWidth = 35 // Estimated width for +n tag

    tagElements.forEach((tagElement, index) => {
      const tagWidth = (tagElement as HTMLElement).offsetWidth + 4 // 4px margin

      // Check if we can fit this tag and potentially the +n tag
      const wouldFitWithPlusTag = totalWidth + tagWidth + plusTagWidth <= maxWidth
      const wouldFitWithoutPlusTag = totalWidth + tagWidth <= maxWidth
      const isLastTag = index === tags.length - 1

      if (isLastTag && wouldFitWithoutPlusTag) {
        // Last tag and fits without +n tag
        totalWidth += tagWidth
        visibleCount++
      } else if (wouldFitWithPlusTag && !isLastTag) {
        // Not last tag and fits with +n tag
        totalWidth += tagWidth
        visibleCount++
      }
    })

    setVisibleTags(tags.slice(0, visibleCount))
    setHiddenTags(tags.slice(visibleCount))
  }

  useEffect(() => {
    const timer = setTimeout(calculateVisibleTags, 50)

    return () => clearTimeout(timer)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [format, maxWidth])

  const renderTag = (constraint: string, index: number, isMeasure = false) => {
    const isPattern = constraint.startsWith('pattern:')
    const displayText = isPattern && constraint.length > 15 ? constraint.substring(0, 15) + '...' : constraint

    const tagElement = (
      <Tag
        className={isMeasure ? 'measure-tag' : 'constraint-tag'}
        color={'default'}
        style={{
          fontSize: '12px',
          padding: '2px 6px',
          margin: '2px',
          cursor: 'pointer',
          display: 'inline-block'
        }}
      >
        {displayText}
      </Tag>
    )

    if (isMeasure) {
      return (
        <div key={index} style={{ display: 'inline-block' }}>
          {tagElement}
        </div>
      )
    }

    return (
      <Tooltip title={constraint} key={index}>
        {tagElement}
      </Tooltip>
    )
  }

  const allTags = formatConstraints(format)

  return (
    <div
      ref={containerRef}
      style={{
        maxWidth: `${maxWidth}px`,
        display: 'flex',
        flexWrap: 'nowrap',
        overflow: 'hidden',
        alignItems: 'center'
      }}
    >
      {/* Hidden div for measurement */}
      <div
        ref={measureRef}
        style={{
          position: 'absolute',
          visibility: 'hidden',
          whiteSpace: 'nowrap',
          top: '-9999px'
        }}
      >
        {allTags.map((constraint, index) => renderTag(constraint, index, true))}
      </div>

      {/* Render visible tags */}
      {visibleTags.map((constraint, index) => renderTag(constraint, index))}

      {/* Render +n tag if there are hidden tags */}
      {hiddenTags.length > 0 && (
        <Tooltip
          title={
            <div>
              {hiddenTags.map((constraint, index) => (
                <div key={index} style={{ marginBottom: '4px' }}>
                  {constraint}
                </div>
              ))}
            </div>
          }
        >
          <Tag
            style={{
              fontSize: '12px',
              padding: '2px 6px',
              margin: '2px',
              backgroundColor: '#f0f0f0',
              color: '#666',
              cursor: 'pointer'
            }}
          >
            +{hiddenTags.length}
          </Tag>
        </Tooltip>
      )}
    </div>
  )
}

export default FormatTags
