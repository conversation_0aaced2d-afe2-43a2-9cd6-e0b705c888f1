import React from 'react'

import { Table } from 'antd'

import { WorkflowTag } from '@views/order/detail/tabs/common/components/WorkflowTag'

import { useUser } from '@/hooks'

import { usePageScroll } from '@/hooks/usePageScroll'
import OrderManagement from '@/models/OrderManagement'

import { CustomCollapse } from '@views/order/detail/tabs/common/components'

interface Props {
  productOrderId: string
}

const TRIGGER_CONVERT: { [key: string]: string } = {
  AUTO: 'Tự động',
  MANUAL: 'Thủ công'
}

// Tab hiển thị thông tin lịch sử tương tác
export const HistoryInfoTab = ({ productOrderId }: Props) => {
  const { user } = useUser()

  const role = user?.portalType === 'ADMIN' ? 'ADMIN' : 'DEV'

  /** Gọi API lấy danh sách lịch sử tương tác */
  const {
    content: statusHistoryData,
    isFetching: isFetchingStatus,
    onScroll: onScrollStatus
  } = usePageScroll(
    ['getStatusHistory'],
    async params => OrderManagement.getStatusHistory(productOrderId, role?.toLowerCase(), params),
    {},
    {
      pageSize: 5,
      enabled: !!productOrderId
    }
  )

  /** Gọi API lấy danh sách tương tác */
  const {
    content: interactedHistoryData,
    isFetching,
    onScroll
  } = usePageScroll(
    ['interactedHistoryData'],
    async params => OrderManagement.getInteractedHistory(productOrderId, role?.toLowerCase(), params),
    {},
    {
      pageSize: 5,
      enabled: !!productOrderId
    }
  )

  // Cột của bảng
  const interactedHistoryColumns: any = [
    {
      title: 'STT',
      dataIndex: 'stt',
      key: 'stt',
      render: (_: any, __: any, index: number) => index + 1,
      width: 70,
      align: 'center'
    },
    {
      title: 'Ngày tương tác',
      dataIndex: 'modifiedAt',
      key: 'modifiedAt',
      width: 200
    },
    {
      title: 'Người thực hiện',
      dataIndex: 'modifiedName',
      key: 'modifiedName',
      width: 200
    },
    {
      title: 'Nội dung',
      dataIndex: 'content',
      key: 'content'
    }
  ]

  // Cột của bảng
  const statusHistoryColumns: any = [
    {
      title: 'STT',
      dataIndex: 'stt',
      key: 'stt',
      render: (_: any, __: any, index: number) => index + 1,
      align: 'center',
      fixed: 'left',
      width: 70
    },
    {
      title: 'Ngày thay đổi trạng thái',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 200
    },
    {
      title: 'Đối tượng',
      dataIndex: 'serviceName',
      key: 'serviceName',
      width: 200
    },
    {
      title: 'Trạng thái trước',
      dataIndex: 'previousStatus',
      key: 'previousStatus',
      width: 250,
      render: (value: any, record: any) => <WorkflowTag status={value} bgColor={record?.previousStatusColor} />
    },
    {
      title: 'Trạng thái sau',
      dataIndex: 'nextStatus',
      key: 'nextStatus',
      width: 250,
      render: (value: any, record: any) => <WorkflowTag status={value} bgColor={record?.nextStatusColor} />
    },
    {
      title: 'Trigger kích hoạt',
      dataIndex: 'triggerType',
      key: 'triggerType',
      width: 200,
      render: (value: any) => TRIGGER_CONVERT[value]
    },
    {
      title: 'Người thực hiện',
      dataIndex: 'userDisplayName',
      key: 'userDisplayName',
      width: 200
    },
    {
      title: 'Ghi chú',
      dataIndex: 'note',
      key: 'note',
      width: 200
    }
  ]

  return (
    <div className='space-y-2 bg-gray-100'>
      <CustomCollapse title='Lịch sử thay đổi tương tác'>
        <Table
          loading={isFetchingStatus}
          columns={statusHistoryColumns}
          dataSource={statusHistoryData}
          size='small'
          pagination={false}
          scroll={{ x: 'max-content', y: 300 }}
          onScroll={onScrollStatus}
        />
      </CustomCollapse>
      <CustomCollapse title='Lịch sử tương tác'>
        <Table
          loading={isFetching}
          onScroll={onScroll}
          size='small'
          pagination={false}
          scroll={{ x: 'max-content', y: 300 }}
          columns={interactedHistoryColumns}
          dataSource={interactedHistoryData}
        />
      </CustomCollapse>
    </div>
  )
}
