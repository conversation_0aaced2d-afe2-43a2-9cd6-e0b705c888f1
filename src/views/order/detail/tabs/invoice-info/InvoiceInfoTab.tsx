import React from 'react'

import moment from 'moment'

import { Table } from 'antd'

import { styled } from 'styled-components'

import { API_ROOT } from '@/models/Base'

import OrderManagement from '@/models/OrderManagement'

import { BillingStatusRender } from '@views/billing/detail/components'

import { FormattedCurrency } from '@views/order/detail/tabs/common/components/FormattedCurrency'

import { usePage, useUser } from '@/hooks'

import { CustomCollapse } from '@views/order/detail/tabs/common/components'

const StyledTable = styled(Table)`
  .hover-row:hover {
    color: #2a6aeb !important;
    cursor: pointer;
  }
`

interface Props {
  productOrderId: string
}

export const InvoiceInfoTab = ({ productOrderId }: Props) => {
  const { user } = useUser()

  const role = user?.portalType === 'ADMIN' ? 'ADMIN' : 'DEV'

  const columns: any[] = [
    {
      title: 'STT',
      dataIndex: 'index',
      key: 'index',
      fixed: 'left',
      width: 60,
      render: (value: any, record: any, index: number) => (page - 1) * pageSize + index + 1,
      align: 'center'
    },
    {
      title: 'Mã hóa đơn',
      dataIndex: 'billingCode',
      key: 'billingCode',
      width: 150
    },
    {
      title: 'ID thuê bao',
      dataIndex: 'subCode',
      key: 'subCode',
      width: 150
    },
    {
      title: 'Tổng tiền',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      width: 150,
      render: (value: any) => <FormattedCurrency value={value} />
    },
    {
      title: 'Ngày yêu cầu thanh toán',
      dataIndex: 'requirePaymentDate',
      key: 'requirePaymentDate',
      width: 170,
      render: (value: any) => value && moment(value, 'DD/MM/YYYY HH:mm').format('DD/MM/YYYY')
    },
    {
      title: 'Trạng thái thanh toán',
      dataIndex: 'status',
      key: 'status',
      width: 200,
      render: (value: any) => (
        <div className='w-1/2'>
          <BillingStatusRender status={value} />
        </div>
      )
    }
  ]

  const { page, pageSize, content, pagination, isFetching } = usePage(
    ['getLstInvoice'],
    async () => {
      const params = {
        page: page - 1,
        pageSize,
        size: pageSize
      }

      const res = await OrderManagement.getOrderBilling(productOrderId, role?.toLowerCase(), params)

      return res
    },
    {
      sort: 'updatedTime,desc'
    },
    { enabled: !!productOrderId }
  )

  return (
    <div className='space-y-2 bg-gray-100'>
      <CustomCollapse title='Hóa đơn'>
        <div className='space-y-4'>
          <StyledTable
            loading={isFetching}
            columns={columns}
            dataSource={content}
            size='small'
            scroll={{ x: 'max-content', y: 450 }}
            rowClassName={() => 'hover-row'}
            onRow={(record: any) => {
              return {
                onClick: () => {
                  // Điều hướng sang màn chi tiết hóa đơn
                  const url =
                    role === 'ADMIN'
                      ? `/admin-portal/billing/list/${record?.billingCode}?productOrderId=${productOrderId}`
                      : `/partner-portal/invoice/list/${record?.billingCode}?productOrderId=${productOrderId}`

                  window.open(`${API_ROOT}${url}`)
                }
              }
            }}
            pagination={pagination}
          />
        </div>
      </CustomCollapse>
    </div>
  )
}
