import React, { useMemo } from 'react'

import { Tooltip } from 'antd'

import { FormattedCurrency } from '@views/order/detail/tabs/common/components/FormattedCurrency'

import DetailCommon from '@views/payment/components/product-information/DetailCommon'

import { ContentRender, TransparentBorder } from '@views/order/detail/tabs/common/components'

interface Props {
  orderInfo: any
}

export const GeneralInfoTab = ({ orderInfo }: Props) => {
  const generalField = useMemo(() => {
    return [
      { label: 'Mã đơn hàng', content: orderInfo?.orderCode },
      { label: 'Thời gian tạo', content: orderInfo?.createdAt },
      { label: 'Mã nhân viên giới thiệu', content: orderInfo?.employeeCode },
      {
        label: 'Trạng thái đơn hàng',
        content: orderInfo?.workflowStepName
      }
    ]
  }, [orderInfo])

  const cartInfo = useMemo(() => {
    return orderInfo?.shoppingCartFormulaResDTO
  }, [orderInfo])

  return (
    <div>
      <div className='grid grid-cols-2 gap-4 bg-gray-100'>
        <div className='bg-white px-4 py-3'>
          <div className='border-l-4 border-solid border-yellow-6 bg-white pl-2 text-headline-16 font-medium'>
            Thông tin chung đơn hàng
          </div>

          <div className='mt-3 border-t border-solid border-slate-200 pt-2'>
            <div className='grid grid-cols-2 gap-2'>
              {generalField?.map(field => (
                <>
                  <ContentRender label={field.label} content={field.content} />
                </>
              ))}
            </div>

            <div className='mt-2 rounded-md bg-gray-50 p-2'>
              <div className='text-gray-8'>Ghi chú</div>
              <Tooltip title={orderInfo?.notes} color='#fff' placement='topLeft'>
                <div className='line-clamp-3 overflow-hidden font-semibold'>{orderInfo?.notes}</div>
              </Tooltip>
            </div>
          </div>
        </div>

        <div className='bg-white px-4 py-3'>
          <div className='border-l-4 border-solid border-yellow-6 bg-white pl-2 text-headline-16 font-medium'>
            Giá trị đơn hàng
          </div>
          <div className='mt-3 border-t border-solid border-slate-200 pt-2'>
            <div className='flex justify-between px-4'>
              <div>Tổng tiền hàng</div>
              <FormattedCurrency className='font-semibold' value={cartInfo?.totalAmountPreTaxFeeFinal} />
            </div>

            <div className='text-gray-8'>
              <DetailCommon
                title='Thuế'
                info={!orderInfo?.isCartCode ? cartInfo?.lstSubscriptionResponse[0]?.taxObject : cartInfo?.taxObject}
                type='Taxes'
              />
              <DetailCommon
                title='Phí'
                info={!orderInfo?.isCartCode ? cartInfo?.lstSubscriptionResponse[0]?.feeObject : cartInfo?.feeObject}
                type='Fees'
              />
              <DetailCommon
                title='Khuyến mại'
                info={
                  !orderInfo?.isCartCode ? cartInfo?.lstSubscriptionResponse[0]?.couponObject : cartInfo?.couponObject
                }
                type='Coupons'
              />
            </div>

            <div className='px-4 pt-2'>
              <TransparentBorder />
              <div className='flex justify-between pt-2'>
                <div>Thành tiền</div>
                <FormattedCurrency className='font-semibold' value={cartInfo?.totalAmountAfterTaxFinal} />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
