import React, { useState } from 'react'

import { DownOutlined, UpOutlined } from '@ant-design/icons'

import { <PERSON>lapse, Drawer, Tabs } from 'antd'
import { styled } from 'styled-components'

import { WorkflowTag } from '@views/order/detail/tabs/common/components/WorkflowTag'

import { CYCLE_TYPE_NUMBER } from '@views/cart/constant'

import { FormattedCurrency } from '@views/order/detail/tabs/common/components/FormattedCurrency'

import { SubHistory } from '@views/order/detail/tabs/service-info/components/SubHistory'
import { SubDetail } from '@views/order/detail/tabs/service-info/components/SubDetail'
import { TransparentBorder } from '@views/order/detail/tabs/common/components'

const { Panel } = Collapse

const { TabPane } = Tabs

const StyledCollapse = styled(Collapse)`
  .ant-collapse-content-box {
    padding-top: 0 !important;
  }
  background-color: white;
`

interface Props {
  addedPricingServices: any
  title: string
}

export const ServiceInfoCollapse = ({ addedPricingServices, title }: Props) => {
  const [openItems, setOpenItems] = useState<any[]>([])

  const [drawerData, setDrawerData] = useState<any>({ data: null, open: false })

  const onClose = () => setDrawerData({ data: null, open: false })

  const toggleItem = (id: number) => {
    setOpenItems(prev => (prev.includes(id) ? prev.filter(i => i !== id) : [...prev, id]))
  }

  const renderAddonOutline = (service: any) =>
    service?.addons?.length > 0 && (
      <div onClick={() => toggleItem(service?.subId)}>
        {openItems.includes(service.subId) ? (
          <UpOutlined className='text-gray-500' />
        ) : (
          <DownOutlined className='text-gray-500' />
        )}
      </div>
    )

  return (
    <>
      <StyledCollapse className='bg-white' collapsible='icon' expandIconPosition='end' ghost defaultActiveKey={['1']}>
        <Panel
          header={
            <div className='grid grid-cols-12 border-l-4 border-solid border-yellow-6'>
              <div className='col-span-6 pl-2 font-semibold'>
                {title} ({addedPricingServices?.length})
              </div>
              <div className='col-span-1 text-gray-8'>SKU</div>
              <div className='col-span-1 text-gray-8'>Đơn giá</div>
              <div className='col-span-1 text-gray-8'>Số lượng</div>
              <div className='col-span-1 text-gray-8'>Thành tiền</div>
              <div className='col-span-2 text-end text-gray-8'>Trạng thái</div>
            </div>
          }
          key='1'
        >
          <div className='pt-2'>
            {addedPricingServices?.map((service: any, index: number) => (
              <>
                <div
                  className='grid grid-cols-12 pr-5'
                  onClick={() => {
                    setDrawerData({ data: service, open: true })
                  }}
                >
                  <div className='col-span-6 flex gap-2 pl-2 font-semibold'>
                    <img alt='' className='size-8 rounded-md' src={service?.image} />
                    <div>
                      <div className='font-medium hover:cursor-pointer'>{service?.serviceName}</div>
                      <div className='flex gap-2 text-xs text-gray-8'>
                        {service?.pricingName}
                        <div>
                          {Number(service.paymentCycle) > 0 && (
                            <div>
                              {`(${service.paymentCycle} ${CYCLE_TYPE_NUMBER[service.cycleType]?.toLowerCase()}, ${
                                Number(service.numberOfCycles) > 0
                                  ? `${service.numberOfCycles} chu kỳ`
                                  : 'Không giới hạn'
                              })`}
                            </div>
                          )}
                        </div>
                        <div>{!service?.variantName && renderAddonOutline(service)}</div>
                      </div>
                      <div className='flex gap-2 text-xs text-gray-8'>
                        {service?.variantName} {renderAddonOutline(service)}
                      </div>
                    </div>
                  </div>
                  <div className='col-span-1 text-gray-8'>{service?.SKU}</div>
                  <div className='col-span-1 flex items-center font-semibold text-gray-8'>
                    <FormattedCurrency value={service?.amount || 0} />
                  </div>
                  <div className='col-span-1 flex items-center pl-4 font-semibold text-gray-8'>{service?.quantity}</div>
                  <div className='col-span-1 flex items-center font-semibold text-gray-8'>
                    <FormattedCurrency value={service?.totalAmount} />
                  </div>
                  <div
                    title={service?.progressDetail?.finalStateDisplayName}
                    className='col-span-2 flex items-center justify-end text-gray-8'
                  >
                    <WorkflowTag
                      status={service?.progressDetail?.finalStateDisplayName}
                      bgColor={service?.progressDetail?.finalStateColorCode}
                    />
                  </div>
                </div>

                {/* Hiển thị addon kèm gói */}
                {openItems.includes(service.subId) &&
                  service?.addons?.map((addon: any) => (
                    <>
                      <div className='py-2'>
                        <TransparentBorder />
                      </div>
                      <div className='grid grid-cols-12 pr-5'>
                        <div className='col-span-8 pl-12 font-semibold'>{addon?.addonName}</div>
                        <div className='col-span-1'>{addon?.quantity}</div>
                        <div className='col-span-1'>
                          <FormattedCurrency value={addon?.totalAmount} />
                        </div>
                      </div>
                    </>
                  ))}

                {index < addedPricingServices?.length - 1 && (
                  <div className='py-2'>
                    <TransparentBorder />
                  </div>
                )}
              </>
            ))}
          </div>
        </Panel>
      </StyledCollapse>

      <Drawer
        title='Thông tin chi tiết'
        placement='right'
        onClose={onClose}
        open={drawerData.open}
        width={800}
        bodyStyle={{ paddingTop: 0 }}
      >
        <Tabs defaultActiveKey='1' tabBarStyle={{ padding: '0 0 0 0', marginBottom: 0 }}>
          <TabPane tab='Chi tiết' key='1'>
            <SubDetail isService onClose={onClose} service={drawerData.data} />
          </TabPane>
          <TabPane tab='Lịch sử' key='2'>
            <SubHistory service={drawerData.data} />
          </TabPane>
        </Tabs>
      </Drawer>
    </>
  )
}
