'use client'

import React, { useCallback, useMemo, useState } from 'react'

import * as antdIcons from '@ant-design/icons'

import { useMutation, useQueryClient } from '@tanstack/react-query'

import { message, Steps } from 'antd'

import clsx from 'clsx'

import { styled } from 'styled-components'

import * as customIcons from '@assets/builder-icons'

import { DocumentIcon } from '@/assets/component-icons'

import OrderManagement from '@/models/OrderManagement'

import { ConfirmModal } from '@views/custom-field/editor/sections/create-service/variant/modal/ConfirmModal'

// Types
interface ProgressDetail {
  progress: number
  time: string
}

interface OrderProcessProps {
  progressDetail?: any
  type: string
  subId: string
  onCloseAction: () => void
  isService?: boolean
}

// Colors
const colors = {
  green100: '#E6F7F0',
  green600: '#14C780',
  white: '#FFFFFF',
  neutral200: '#E5E7EB',
  neutral1100: '#374151',
  accentPurple: '#645FEC',
  accentPurpleLight: '#CBD1FE'
}

// Styled component
const CustomSteps = styled(Steps)`
  .ant-steps-item-title {
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
    letter-spacing: 0.07px;
  }
  .ant-steps-item-description {
    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
    letter-spacing: 0.09px;
  }
  .ant-steps-item-tail {
    inset-block-start: 19px !important;
    padding-block: 4.5px !important;
    padding-inline: 16px !important;
  }
  .ant-steps-item-icon {
    margin-inline-start: 35px !important;
  }
  .ant-steps-item-finish > .ant-steps-item-container > .ant-steps-item-tail::after {
    background-color: #645fec;
  }
  .ant-steps-item-finish > .ant-steps-item-container > .ant-steps-item-content > .ant-steps-item-description {
    font-weight: 500;
    font-size: 10px;
    line-height: 16px;
    color: #0f131a !important;
  }

  .ant-steps-item-tail::after {
    transition: background 0.3s;
  }

  /* Custom nửa thanh (thanh giữa currentStep và bước kế tiếp)*/
  .half-progress .ant-steps-item-tail::after {
    background: linear-gradient(to right, #645fec 50%, #e5e7eb 50%);
  }
`

const { Step } = Steps

export function OrderProcess({
  type,
  progressDetail,
  subId,
  onCloseAction,
  isService
}: OrderProcessProps): React.ReactElement {
  const queryClient = useQueryClient()

  const [openModalConfirm, setOpenModalConfirm] = useState(false)
  const [infoModal, setInfoModal] = useState({})

  const lstProgressDetail = useMemo(() => {
    return progressDetail?.lstProgressDetail || []
  }, [progressDetail])

  const currentStep = useMemo(() => {
    return lstProgressDetail?.findIndex((step: any) => step.stateId === progressDetail?.finalStateId) + 1
  }, [lstProgressDetail, progressDetail])

  const getStepStyle = useCallback(
    (stepIndex: number) => {
      if (stepIndex < currentStep) {
        return {
          color: colors.white,
          backgroundColor: colors?.accentPurple
        }
      }

      if (stepIndex === currentStep) {
        return {
          color: colors?.accentPurple,
          backgroundColor: colors.accentPurpleLight,
          border: `1px solid ${colors?.accentPurple}`
        }
      }

      return {
        color: colors.neutral1100,
        backgroundColor: colors.neutral200,
        border: 'none'
      }
    },
    [currentStep]
  )

  const getDesStyle = useCallback(
    (stepIndex: number) => {
      if (stepIndex < currentStep) {
        return {
          color: colors?.accentPurple,
          fontWeight: 'semi-bold'
        }
      }

      if (stepIndex === currentStep) {
        return {
          color: colors?.accentPurple,
          fontWeight: 'normal'
        }
      }

      return { color: '#394867', fontWeight: 'normal' }
    },
    [currentStep]
  )

  const getStepDescription = useCallback(
    (
      details: ProgressDetail[],
      step:
        | { label: string; value: string; icon: React.JSX.Element; id: number; tagColor: string }
        | { label: string; value: string; icon: null; id: number; tagColor?: undefined }
    ) => {
      const matched = details.find((e: any) => e.progress === step.value)

      if (matched) return matched.time

      return null
    },
    []
  )

  const updateProgress = useMutation({
    mutationKey: ['updateProgressStatus'],
    mutationFn: OrderManagement.updateProgress,
    onSuccess: () => {
      message.success('Cập nhật tiến trình thành công').then()
      queryClient.invalidateQueries({ queryKey: ['getServiceProduct'] }).then()
      onCloseAction()
      setOpenModalConfirm(false)
      setInfoModal({})
    },
    onError: () => {
      message.success('Đã có lỗi xảy ra, vui lòng thử lại').then()
    }
  })

  const renderIconValue = (value: any, stepIndex: number) => {
    let color = colors.neutral1100

    if (stepIndex < currentStep) {
      color = colors?.white
    } else if (stepIndex === currentStep) {
      color = colors?.accentPurple
    }

    if (!value) return null
    // @ts-ignore
    // eslint-disable-next-line import/namespace
    const IconComponent = antdIcons[value as string] || customIcons[value as string]

    return (
      <div className='flex items-center text-text-neutral-strong'>
        {IconComponent && <IconComponent style={{ fontSize: '14px', color }} />}
      </div>
    )
  }

  return (
    <div>
      <CustomSteps current={currentStep - 1} labelPlacement='vertical'>
        {lstProgressDetail?.map((step: any, index: number) => (
          <Step
            onClick={() => {
              // Chỉ cho phép chuyển trạng thái sau
              if (index === currentStep && !['OS_SAAS', 'ON_SAAS'].includes(type) && !isService) {
                setInfoModal({
                  iconType: 'NOTIFY',
                  title: 'Xác nhận cập nhật trạng thái',
                  description: (
                    <span className='py-4 text-body-14 font-medium'>
                      Bạn có muốn chuyển sang trạng thái {step?.stateDisplayName}?
                    </span>
                  ),
                  confirmButtonText: 'Xác nhận',
                  onClickConfirm: () => {
                    updateProgress.mutate({
                      stateTransitionItemId: step.stateTransitionItemId,
                      subscriptionId: subId,
                      triggerMode: 'MANUAL'
                    })
                  },
                  onClickCancel: () => setOpenModalConfirm(false)
                })
                setOpenModalConfirm(true)
              }
            }}
            key={step.id}
            title={<div style={getDesStyle(index + 1)}>{step.stateDisplayName}</div>}
            description={getStepDescription(lstProgressDetail, step as any)}
            className={clsx({ 'half-progress': index + 1 === currentStep }, !isService && 'hover:cursor-pointer')}
            icon={
              <div className={clsx('flex h-10 items-center rounded-xl p-[10px]')} style={getStepStyle(index + 1)}>
                {step?.stateIcon ? renderIconValue(step?.stateIcon, index + 1) : <DocumentIcon />}
              </div>
            }
          />
        ))}
      </CustomSteps>
      {openModalConfirm && (
        <ConfirmModal openModal={openModalConfirm} setOpenModal={setOpenModalConfirm} infoModal={infoModal} />
      )}
    </div>
  )
}
