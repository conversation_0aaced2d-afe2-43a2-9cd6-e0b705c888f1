import React, { useState, useEffect, useMemo } from 'react'

import { useSearchParams } from 'next/navigation'

import { Drawer } from 'antd'

import { WorkflowTag } from '@views/order/detail/tabs/common/components/WorkflowTag'

import { PricingInfo, SubDetail } from '@views/order/detail/tabs/service-info/components'

import { ProcessBar } from '@views/order-management/common'

export const ProgressInfoTab = ({ serviceInfo }: { serviceInfo: any }) => {
  const [drawerData, setDrawerData] = useState<any>({ data: null, open: false })
  const searchParams = useSearchParams()

  const onClose = () => setDrawerData({ data: null, open: false })

  useEffect(() => {
    const openDrawer = searchParams.get('openDrawer')
    const serviceId = searchParams.get('serviceId')

    if (openDrawer === 'true' && serviceId) {
      let foundService = null

      serviceInfo?.lstDevice?.length > 0 &&
        (foundService = serviceInfo.lstDevice.find((service: any) => service.serviceId === Number(serviceId)))

      serviceInfo?.lstSim?.length > 0 &&
        (foundService = serviceInfo.lstSim.find((service: any) => service.serviceId === Number(serviceId)))

      if (foundService) {
        setDrawerData({ data: foundService, open: true })
      }
    }
  }, [searchParams, serviceInfo])

  const titleConvert: any = {
    variant: 'Thiết bị vật lý',
    sim: 'Sim kết nối'
  }

  const statusConvert: any = {
    variant: 'Trạng thái kích hoạt',
    sim: 'Trạng thái kết nối'
  }

  const currentStep = useMemo(() => {
    const step = serviceInfo?.lstWorkflowStep?.find((step: any) => step?.id === serviceInfo?.workflowStepId)

    return step?.index || 1
  }, [serviceInfo])

  const renderProduct = (type: string, list: any) => {
    return (
      <div className='pt-4'>
        <div className='grid grid-cols-12 gap-2 py-2 pl-6 pr-8 text-xs text-gray-8'>
          <div className='col-span-4'>{titleConvert[type]}</div>
          <div className='col-span-2'>{statusConvert[type]}</div>
          <div className='col-span-2'>Đơn vị phụ trách</div>
          <div className='col-span-2'>Thời gian giao dự kiến</div>
          <div className='col-span-2'>Tiến trình xử lý</div>
        </div>

        <div className='space-y-2 py-2 pl-4 pr-8'>
          {list?.map((service: any) => (
            <div
              key={service?.id}
              className='grid grid-cols-12 gap-2 rounded-md border border-solid border-slate-200 py-3 pl-2'
              onClick={() => {
                setDrawerData({ data: service, open: true })
              }}
            >
              <div className='col-span-4 flex gap-2'>
                <img alt='' className='size-8 rounded-md' src={service?.image} />
                <div>
                  <div className='font-medium hover:cursor-pointer'>{service?.serviceName}</div>
                  <div className='text-xs text-gray-8'>{service?.pricingName}</div>
                </div>
              </div>
              <div className='col-span-2'>
                <div className='rounded-md  p-0.5 text-center text-xs'></div>
              </div>
              <div className='col-span-2 font-medium'></div>
              <div className='col-span-2'></div>
              <div title={service?.progressDetail?.finalStateDisplayName} className='col-span-2 pr-2'>
                <WorkflowTag
                  status={service?.progressDetail?.finalStateDisplayName}
                  bgColor={service?.progressDetail?.finalStateColorCode}
                />
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className='bg-white'>
      <div className='space-y-2 px-4 pt-3'>
        <div className='mb-4 flex justify-between border-l-4 border-solid border-yellow-6'>
          <div className='pl-2 font-semibold'>Tiến trình xử lý</div>
          {serviceInfo?.orderPlacementDate && (
            <div className='text-xs text-gray-8'>Ngày bắt đầu đặt đơn: {serviceInfo?.orderPlacementDate}</div>
          )}
        </div>

        {serviceInfo?.lstWorkflowStep?.length && (
          <ProcessBar steps={serviceInfo?.lstWorkflowStep} currentStep={currentStep} />
        )}
      </div>

      {!!serviceInfo?.packageId && <PricingInfo pricing={serviceInfo} />}
      {serviceInfo?.lstDevice?.length > 0 && renderProduct('variant', serviceInfo?.lstDevice)}
      {serviceInfo?.lstSim?.length > 0 && renderProduct('sim', serviceInfo?.lstSim)}

      <Drawer
        title='Thông tin chi tiết'
        placement='right'
        onClose={onClose}
        open={drawerData?.open}
        width={800}
        bodyStyle={{ paddingTop: 0 }}
      >
        <SubDetail onClose={onClose} service={drawerData?.data} />
      </Drawer>
    </div>
  )
}
