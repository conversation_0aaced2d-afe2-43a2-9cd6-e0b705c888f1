import React, { useCallback, useMemo, useState } from 'react'

import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'

import { debounce } from 'lodash'

import { useQuery } from '@tanstack/react-query'

import { Checkbox, Collapse, DatePicker, Empty, Input, Popover, Select, Spin, Table } from 'antd'

import { styled } from 'styled-components'

import dayjs from 'dayjs'

import { WorkflowTag } from '@views/order/detail/tabs/common/components/WorkflowTag'

import { scrollXStyle, scrollYStyle } from '@views/order/detail/tabs/common/constants/style'

import { isCartCode } from '@/views/order-management/constants'

import { SUBSCRIPTION_API_KEY } from '@/views/subscription/constants'
import { usePageScroll } from '@/hooks/usePageScroll'

import OrderManagement from '@/models/OrderManagement'

import { handleSrcImg } from '@/utils/string'

import { CUSTOMER_TYPE_NAME } from '@/constants/convert'

import { FormattedCurrency } from '@views/order/detail/tabs/common/components/FormattedCurrency'

import { ContentRender, CustomCollapse } from '@views/order/detail/tabs/common/components'

const { Panel } = Collapse

const StyledCollapse = styled(Collapse)`
  .ant-collapse-content-box {
    padding-block-start: 0 !important;
  }
  background-color: white;
`

const inputLabelConvert: any = { isServiceName: 'Tên dịch vụ', isCartCode: 'Mã đơn hàng' }

const CUSTOMER_STATUS_CONVERT = {
  0: 'existence',
  1: 'leave',
  null: 'potential',
  undefined: 'potential'
}

const { RangePicker } = DatePicker

export const CustomerInfoTab = () => {
  const route = useRouter()
  const { id } = useParams<{ id: string }>()

  const [inputSearch, setInputSearch] = useState<any>({
    isServiceName: 1,
    isCartCode: 1
  })

  const [filterParams, setFilterParams] = useState<any>({})

  const [rangeValue, setRangeValue] = useState<any>([null, null])

  /** Gọi API lấy chi tiết khách hàng */
  const { data: customerInfo } = useQuery({
    queryKey: ['getCustomerInfo'],
    queryFn: async () => {
      const res = isCartCode(id)
        ? await OrderManagement.getCartCustomerInfo(id)
        : await OrderManagement.getCustomerInfo(id)

      return res
    },
    initialData: {},
    enabled: !!id
  })

  /** Gọi API lấy chi tiết khách hàng */
  const { data: stepOptions } = useQuery({
    queryKey: ['getStepOptions'],
    queryFn: async () => {
      const res = await OrderManagement.getStepOptions({ userId: customerInfo?.id, size: 500, page: 0 })

      return res?.content?.map((item: any) => ({ value: item?.id, label: item?.name }))
    },
    initialData: {},
    enabled: !!customerInfo?.id
  })

  /** Gọi API lấy danh sách đơn hàng */
  const { content, isFetching, onScroll } = usePageScroll(
    [SUBSCRIPTION_API_KEY, 'ListSubscription', filterParams, inputSearch],
    async body => OrderManagement.getCustomerHistory(customerInfo?.id, { ...body, ...filterParams, ...inputSearch }),
    { ...filterParams, value: '' },
    {
      pageSize: 10,
      enabled: !!customerInfo?.id
    }
  )

  /** Gọi API lấy thông tin sản phẩm quan tâm */
  const {
    content: usedServices,
    isFetching: isFetchingService,
    onScroll: onScrollUsedService
  } = usePageScroll(
    [SUBSCRIPTION_API_KEY, 'usedServices'],
    async body => OrderManagement.getUsedServices(customerInfo?.id, body),
    {},
    {
      pageSize: 10,
      enabled: !!customerInfo?.id
    }
  )

  /** Gọi API lấy thông tin sản phẩm quan tâm */
  const {
    content: interestedServices,
    isFetching: fetchingInterestedService,
    onScroll: onScrollInterestedService
  } = usePageScroll(
    [SUBSCRIPTION_API_KEY, 'interestedServices'],
    async body => OrderManagement.getInterestedServices(customerInfo?.id, body),
    {},
    {
      pageSize: 6,
      enabled: !!customerInfo?.id
    }
  )

  const isPersonal = useMemo(() => customerInfo?.customerType === 'PERSONAL', [customerInfo])

  const customerField = useMemo(() => {
    return [
      { label: 'Đối tượng khách hàng', content: CUSTOMER_TYPE_NAME[customerInfo?.customerType] },
      { label: 'Tên khách hàng', content: customerInfo?.smeName },
      {
        label: isPersonal ? 'Số chứng thực' : 'Mã số thuế',
        content: isPersonal ? customerInfo?.smePersonalCertNumber : customerInfo?.smeTaxCode
      },
      { label: 'Địa chỉ', content: customerInfo?.smeAddressDefault },
      { label: 'Người đại diện', content: customerInfo?.repFullName, hidden: isPersonal },
      { label: 'Mã BHXH', content: customerInfo?.smeSocialInsuranceNumber, hidden: isPersonal }
    ]
  }, [isPersonal, customerInfo])

  const contactField = useMemo(() => {
    return [
      { label: 'Email', content: customerInfo?.contactEmail },
      {
        label: 'Số điện thoại',
        content: customerInfo?.contactPhone
      }
    ]
  }, [customerInfo])

  // Cấu hình cột
  const columns = [
    {
      title: 'STT',
      dataIndex: 'stt',
      key: 'stt',
      render: (text: any, record: any, index: number) => index + 1,
      width: 70
    },
    {
      title: 'Mã đơn hàng',
      dataIndex: 'subCode',
      key: 'subCode',
      render: (value: any, record: any) => (
        <div
          className='cursor-pointer truncate text-text-primary-default'
          onClick={() => {
            route.push(`/order/detail/${record?.isCart ? record?.subCode : record?.subId}?tab=OVERVIEW`)
          }}
        >
          {value}
        </div>
      )
    },
    {
      title: 'Ngày đặt hàng',
      dataIndex: 'createdAt',
      key: 'createdAt'
    },
    {
      title: 'Sản phẩm',
      dataIndex: 'orderItems',
      render: (orderItems: any, record: any) => {
        if (orderItems?.length >= 1) {
          const extraItems = orderItems.slice(1)
          const isPackage = record?.packageId

          const title = isPackage ? record?.solutionName || record?.packageName : orderItems[0]?.serviceName

          return (
            <div className='flex gap-2'>
              <div
                title={title}
                className='w-fit max-w-[200px] truncate rounded-md bg-bg-neutral-lighter px-2 text-text-neutral-medium'
              >
                {title}
              </div>
              {extraItems.length > 0 && !isPackage && (
                <Popover
                  content={
                    <div className='space-y-1'>
                      {extraItems?.map((item: any, index: number) => (
                        <div key={index} className='rounded-md bg-bg-neutral-lighter px-2 text-text-neutral-medium'>
                          {item?.serviceName}
                        </div>
                      ))}
                    </div>
                  }
                >
                  <div className='rounded-md bg-bg-neutral-lighter px-2 text-text-neutral-medium'>
                    +{extraItems.length}
                  </div>
                </Popover>
              )}
            </div>
          )
        }

        return ''
      }
    },
    {
      title: 'Tổng tiền',
      dataIndex: 'billTotal',
      key: 'billTotal',
      render: (amount: any) => <FormattedCurrency value={amount} />
    },
    {
      title: 'Tiến trình đơn hàng',
      dataIndex: 'workflowStepName',
      key: 'workflowStepName',
      render: (status: any, record: any) => {
        return (
          <div
            style={{ color: 'white', backgroundColor: record?.workflowStepColor || '#CDE4FE' }}
            className='truncate rounded-md px-1 text-center text-xs'
            title={status}
          >
            {status}
          </div>
        )
      }
    }
  ]

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const handleSearch = useCallback(
    debounce((value: string) => {
      setFilterParams({ ...filterParams, value: value?.replace(/%/g, '\\%').replace(/_/g, '\\_') || '' })
    }, 400),
    []
  )

  // region render component
  const renderTypeAddress = (type: number) => {
    return type === 0 ? (
      <div className='w-[140px] items-center justify-center rounded-md bg-bg-warning-default px-2 py-1 text-caption-12 font-medium text-white'>
        Nhà riêng/ Chung cư
      </div>
    ) : (
      <div className=' w-30 items-center justify-center rounded-md bg-bg-success-default px-2 py-1 text-caption-12 font-medium text-white'>
        Cơ quan/ Công ty
      </div>
    )
  }

  const disableCheckBox = (fieldName: any) => {
    if (inputSearch[fieldName] === 1) {
      return Object?.values(inputSearch)?.filter(Boolean).length === 1
    }

    return false
  }

  const renderContent = () => (
    <div>
      {Object?.keys(inputSearch)?.map((fieldName: any, index: any) => (
        <div className='flex gap-2' key={index}>
          <div className='pt-1'>{inputLabelConvert[fieldName]}</div>
          <Checkbox
            checked={inputSearch[fieldName] === 1}
            onChange={value => {
              setInputSearch({ ...inputSearch, [fieldName]: value?.target?.checked ? 1 : 0 })
            }}
            disabled={disableCheckBox(fieldName)}
          />
        </div>
      ))}
    </div>
  )

  const renderSuffix = () => (
    <Popover content={renderContent} placement='bottom'>
      <i className='onedx-setting-traffic' />
    </Popover>
  )

  const renderCustomerHeader = () => (
    <div className='flex justify-between border-l-4 border-solid border-yellow-6'>
      <div className='pl-2 font-semibold'>Thông tin khách hàng</div>
      <div className='flex justify-between'>
        <div
          className='flex gap-1 pr-4 text-primary hover:cursor-pointer'
          onClick={() => {
            const { enterpriseId, customerType, customerLifecycleStatus } = customerInfo

            const customerStatusConvert =
              CUSTOMER_STATUS_CONVERT[customerLifecycleStatus as keyof typeof CUSTOMER_STATUS_CONVERT]

            const url = `/admin-portal/customer-${
              customerStatusConvert === 'potential' ? 'potential' : 'manager'
            }/${customerType?.toLowerCase()}/${customerStatusConvert}/${enterpriseId}/edit`

            const data = {
              url,
              type: 'redirect.within'
            }

            window.parent.postMessage(data, '*')
          }}
        >
          <div className='pt-1'>
            <i className='onedx-system-edit size-4' />
          </div>
          Chỉnh sửa thông tin
        </div>
        <div
          className='flex gap-1 pl-4 text-primary hover:cursor-pointer'
          onClick={() => route.push(`/order/create?customerId=${customerInfo?.id}`)}
        >
          <div className='pt-1'>
            <i className='onedx-add size-4' />
          </div>
          Tạo đơn hàng
        </div>
      </div>
    </div>
  )

  const handleCalendarChange = (dateChange: any) => {
    setRangeValue(dateChange)

    if (!dateChange?.[0] && !dateChange?.[1]) {
      setFilterParams({ ...filterParams, startDate: undefined, endDate: undefined })
    } else {
      if (dateChange?.[0]) {
        setFilterParams({ ...filterParams, startDate: dateChange[0].format('DD/MM/YYYY') })
      }

      if (dateChange?.[1]) {
        setFilterParams({ ...filterParams, endDate: dateChange[1].format('DD/MM/YYYY') })
      }
    }
  }

  // Hàm disable ngày không hợp lệ cho RangePicker
  const disabledDate = (current: any) => {
    const today = dayjs().endOf('day')

    // Nếu chưa chọn ngày Từ, chỉ disable ngày lớn hơn hôm nay
    if (!rangeValue?.[0]) {
      return current && current > today
    }

    // Nếu đã chọn ngày Từ, disable ngày < ngày Từ hoặc > hôm nay
    return (current && current < dayjs(rangeValue[0]).startOf('day')) || (current && current > today)
  }

  return (
    <div className='space-y-2 bg-gray-100'>
      <StyledCollapse className='bg-white' collapsible='icon' expandIconPosition='end' ghost defaultActiveKey={['1']}>
        <Panel header={renderCustomerHeader()} key='1'>
          <div className='border-t border-solid border-slate-200 py-2'>
            <div className='grid grid-cols-4 gap-2'>
              {customerField
                ?.filter(item => !item.hidden)
                .map(field => (
                  <>
                    <ContentRender label={field.label} content={field.content} />
                  </>
                ))}
            </div>
          </div>
        </Panel>
      </StyledCollapse>

      <CustomCollapse title='Thông tin liên hệ'>
        <div className='grid grid-cols-4 gap-2'>
          {contactField?.map(field => (
            <>
              <ContentRender label={field.label} content={field.content} />
            </>
          ))}
        </div>
      </CustomCollapse>

      <CustomCollapse title='Thông tin địa chỉ'>
        <div className='grid grid-cols-2 gap-2'>
          {customerInfo?.address?.map((item: any, index: any) => (
            <div key={index}>
              <div className='font-medium text-gray-8'>{item?.typeName}</div>
              <div style={{ minHeight: 114 }} className='rounded-md bg-[#F9F9FA] p-3'>
                <div>
                  {item?.type === 0 ? (
                    <div className='font-semibold'>
                      {isPersonal
                        ? ''.concat(item?.lastName, ' ', item?.firstName, ' | ', item?.tin)
                        : ''.concat(item?.smeName, ' | ', item?.tin)}
                    </div>
                  ) : (
                    <>
                      <div className='font-semibold'>
                        {`${item?.lastName} ${item?.firstName} ${item?.phoneNumber ? ` | ${item?.phoneNumber}` : ''}`}
                      </div>
                      <div className='text-gray-11'>{item?.smeName}</div>
                    </>
                  )}

                  <div className='text-gray-11'>{item?.address}</div>
                  <div className='flex gap-2'>
                    {item?.defaultLocation === 1 && (
                      <div className='rounded-md bg-bg-neutral-medium px-2 py-1 text-caption-12 font-medium text-white'>
                        Mặc định
                      </div>
                    )}

                    {renderTypeAddress(item?.typeAddress)}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CustomCollapse>

      <CustomCollapse title='Lịch sử đơn hàng'>
        <div className='flex gap-2 pb-2'>
          <Input
            onChange={e => handleSearch(e?.target?.value)}
            placeholder='Tìm theo tên dịch vụ'
            prefix={<i className='onedx-search' />}
            suffix={renderSuffix()}
            allowClear
          />
          <Select
            placeholder='Tiến trình đơn hàng'
            options={stepOptions}
            onChange={e => setFilterParams({ ...filterParams, workflowStepIds: e })}
          />
          <Select
            mode='multiple'
            options={[
              { label: 'Tất cả', value: 'UNSET' },
              { label: 'Hàng hóa vật lý', value: 'PHYSICAL_PRODUCT' },
              { label: 'Giải pháp - Gói dịch vụ', value: 'PACKAGE_BUNDLING' }
            ]}
            placeholder='Loại dịch vụ đã đặt'
            style={{ minWidth: '200px' }}
            defaultValue={['UNSET']}
            maxTagCount='responsive'
            className='whitespace-nowrap'
            onChange={e => {
              const formattedProductItemTypes = Array.isArray(e) ? (e as string[]).filter(Boolean).join(',') : e

              setFilterParams({ ...filterParams, productItemTypes: formattedProductItemTypes })
            }}
          />
          <RangePicker
            format='DD-MM-YYYY'
            size='small'
            placeholder={['Từ', 'Đến']}
            onCalendarChange={handleCalendarChange}
            allowEmpty={[true, true]}
            value={rangeValue}
            disabledDate={disabledDate}
          />
        </div>

        <Table
          loading={isFetching}
          columns={columns}
          dataSource={content}
          pagination={false}
          size='small'
          scroll={{ y: 200 }}
          onScroll={onScroll}
          locale={{
            emptyText: <Empty description={<span>Không có dữ liệu theo thông tin tìm kiếm</span>} />
          }}
        />
      </CustomCollapse>

      <CustomCollapse title='Sản phẩm và dịch vụ đang sử dụng'>
        <div className='max-h-[200px] space-y-2 overflow-y-scroll' style={scrollYStyle} onScroll={onScrollUsedService}>
          <Spin spinning={isFetchingService}>
            <div style={scrollXStyle}>
              {usedServices?.map((service, index: number) => (
                <div
                  key={index}
                  className='mb-2 grid grid-cols-12 gap-2 rounded-md border border-solid border-slate-200 p-2'
                >
                  <div className='col-span-4 flex items-center gap-2'>
                    <img alt='' className='size-8 rounded-md' src={handleSrcImg(service?.serviceIconUrl)} />
                    <div>
                      <div title={service?.serviceName} className='w-[250px] truncate font-medium'>
                        {service?.serviceName}
                      </div>
                      <div className='text-xs text-gray-8'>{service?.pricingName}</div>
                      <div title={service?.variantName} className='w-[250px] truncate text-xs text-gray-8'>
                        {service?.variantName}
                      </div>
                    </div>
                  </div>
                  <div className='col-span-2 flex items-center font-semibold'>
                    <FormattedCurrency value={service?.unitPrice} />
                  </div>
                  <div className='col-span-2 flex items-center'>{service?.quantity}</div>
                  <div className='col-span-2 flex items-center font-semibold'>
                    <FormattedCurrency value={service?.totalBillAmount} />
                  </div>
                  <div title={service?.stateDisplayName} className='col-span-2 flex items-center'>
                    <WorkflowTag status={service?.stateDisplayName} bgColor={service?.stateColorCode} />
                  </div>
                </div>
              ))}
            </div>
          </Spin>
        </div>
      </CustomCollapse>

      <CustomCollapse title='Sản phẩm dịch vụ quan tâm gần đây'>
        <div
          className='max-h-[200px] space-y-2 overflow-y-scroll'
          style={scrollYStyle}
          onScroll={onScrollInterestedService}
        >
          <Spin spinning={fetchingInterestedService}>
            <div style={scrollXStyle} className='space-2 grid grid-cols-3 gap-4'>
              {interestedServices?.map((service, index: number) => (
                <div
                  key={index}
                  className='flex w-[380px] justify-between rounded-md border border-solid border-slate-200 p-2'
                >
                  <div className='col-span-4 flex items-center gap-2'>
                    <img alt='' className='size-8 rounded-md' src={handleSrcImg(service?.avatar)} />
                    <div>
                      <div title={service?.name} className='w-[200px] truncate font-medium'>
                        {service?.name}
                      </div>
                      <div className='text-xs text-gray-8'>{service?.pricingName}</div>
                      <div title={service?.variantName} className='w-[150px] truncate text-xs text-gray-8'>
                        {service?.variantName}
                      </div>
                    </div>
                  </div>
                  <div className='col-span-2 flex items-center font-semibold'>
                    <FormattedCurrency value={service?.price} />
                  </div>
                </div>
              ))}
            </div>
          </Spin>
        </div>
      </CustomCollapse>

      <CustomCollapse title='Mô tả khách hàng & ghi chú nội bộ'>{customerInfo?.notes}</CustomCollapse>
    </div>
  )
}
