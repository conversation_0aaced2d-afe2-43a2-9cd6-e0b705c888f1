'use client'

import React, { useMemo, useState, useEffect, useCallback } from 'react'

import { usePara<PERSON>, useRouter, useSearchParams, usePathname } from 'next/navigation'

import { ArrowLeftOutlined } from '@ant-design/icons'

import type { UserComponent } from '@craftjs/core'

import { useQuery } from '@tanstack/react-query'

import { Button, Spin } from 'antd'

import { useUser } from '@/hooks'
import OrderManagement from '@/models/OrderManagement'
import { isCartCode } from '@/views/order-management/constants'

import { SubscriptionTabs } from '@components/custom-field/selectors'

import {
  CustomerInfoTab,
  GeneralInfoTab,
  HistoryInfoTab,
  InvoiceInfoTab,
  PaymentInfoTab,
  ProgressInfoTab,
  ServiceInfoTab
} from '@views/order/detail/tabs'

import { ContentRender } from '@views/order/detail/tabs/common/components'

import { FormattedCurrency } from '@views/order/detail/tabs/common/components/FormattedCurrency'

export const OrderDetail: UserComponent = () => {
  const searchParams = useSearchParams()
  const pathname = usePathname()
  const router = useRouter()

  const tabParam = searchParams.get('tab')

  const [activeKey, setActiveKey] = useState(tabParam || 'OVERVIEW')

  const { id } = useParams<{ id: string }>()

  const { user } = useUser()

  const portalType = user?.portalType === 'ADMIN' ? 'ADMIN' : 'DEV'

  const handleChangeActiveKey = useCallback(
    (key: string) => {
      setActiveKey(key)
      // Cập nhật URL khi thay đổi tab
      const params = new URLSearchParams(searchParams.toString())

      params.set('tab', key)

      // Xóa các tham số openDrawer và serviceId khi chuyển khỏi tab PROGRESS
      if (key !== 'PROGRESS') {
        params.delete('openDrawer')
        params.delete('serviceId')
      }

      router.push(`${pathname}?${params.toString()}`)
    },
    [pathname, router, searchParams]
  )

  // Đồng bộ URL với state khi URL thay đổi từ bên ngoài
  useEffect(() => {
    if (tabParam && tabParam !== activeKey) {
      setActiveKey(tabParam)
    }
  }, [tabParam, activeKey])

  // API chi tiết dịch vụ
  const { data: orderInfo, isLoading } = useQuery({
    queryKey: ['getOrderInfo', id],
    queryFn: async () => {
      const res = isCartCode(id)
        ? await OrderManagement.getDetailOrderCart(id, portalType)
        : await OrderManagement.getDetailOrder(id, portalType)

      res.isCartCode = isCartCode(id)

      return res
    },
    enabled: !!id
  })

  const { data: serviceInfo } = useQuery({
    queryKey: ['getServiceProduct', id],
    queryFn: async () => {
      const res = isCartCode(id)
        ? await OrderManagement.getCartServiceProduct(id)
        : await OrderManagement.getSubServiceProduct(id)

      return res
    },
    enabled: !!id
  })

  const subscriptionDetailTabs = [
    {
      key: 'OVERVIEW',
      label: 'Tổng quan',
      children: <GeneralInfoTab orderInfo={orderInfo} />
    },
    {
      key: 'CUSTOMER',
      label: 'Khách hàng',
      children: <CustomerInfoTab />
    },
    {
      key: 'SERVICE',
      label: 'Sản phẩm & Dịch vụ',
      children: <ServiceInfoTab serviceInfo={serviceInfo} orderInfo={orderInfo} />
    },
    {
      key: 'PROGRESS',
      label: 'Tiến trình',
      children: <ProgressInfoTab serviceInfo={serviceInfo} />
    },
    {
      key: 'PAYMENT',
      label: 'Thanh toán',
      children: <PaymentInfoTab productOrderId={orderInfo?.productOrderId} />
    },
    // {
    //   key: 'SHIPPING',
    //   label: 'Vận chuyển',
    //   children: <ShippingInfoTab />
    // },
    {
      key: 'INVOICE',
      label: 'Hóa đơn',
      children: <InvoiceInfoTab productOrderId={orderInfo?.productOrderId} />
    },
    {
      key: 'HISTORY',
      label: 'Lịch sử tương tác',
      children: <HistoryInfoTab productOrderId={orderInfo?.productOrderId} />
    }
  ]

  const renderStatus = (status: any, bgColor: any) => {
    return (
      <div
        style={{ color: 'white', backgroundColor: bgColor || '#CDE4FE' }}
        className='rounded-md px-2 py-0.5 text-center text-caption-12'
      >
        {status}
      </div>
    )
  }

  const commonField = useMemo(() => {
    return [
      {
        label: <div className='text-body-14 font-normal text-text-neutral-medium'>Thời gian tạo</div>,
        content: <div className='text-body-14 font-medium text-text-neutral-strong'>{orderInfo?.createdAt}</div>
      },
      {
        label: <div className='text-body-14 font-normal text-text-neutral-medium'>Kênh tạo đơn</div>,
        content: <div className='text-body-14 font-medium text-text-neutral-strong'>{orderInfo?.createdSource}</div>
      },
      {
        label: <div className='text-body-14 font-normal text-text-neutral-medium'>Giá trị đơn hàng</div>,
        content: (
          <div
            onClick={() => handleChangeActiveKey('SERVICE')}
            className='text-body-14 font-medium text-text-info-default underline hover:cursor-pointer'
          >
            <FormattedCurrency value={orderInfo?.shoppingCartFormulaResDTO?.totalAmountAfterTaxFinal} />
          </div>
        )
      },
      {
        label: <div className='text-body-14 font-normal text-text-neutral-medium'>Khách hàng</div>,
        content: (
          <div
            onClick={() => handleChangeActiveKey('CUSTOMER')}
            className='text-body-14 font-medium text-text-info-default underline hover:cursor-pointer'
          >
            {orderInfo?.customerName}
          </div>
        )
      }
    ]
  }, [orderInfo, handleChangeActiveKey])

  return (
    <div className='w-full p-6'>
      <Spin spinning={isLoading}>
        <div className='my-2 bg-gray-100 pb-2'>
          <div className='my-2 rounded-t-2xl bg-white p-4'>
            <div className='flex items-center'>
              <Button
                type='text'
                icon={<ArrowLeftOutlined />}
                style={{ marginRight: 8 }}
                onClick={() => {
                  router.push('/order/list')
                }}
              />
              <div className='flex w-full items-center gap-2'>
                <div className='m-0 text-headline-20 font-semibold text-text-neutral-strong'>
                  Đơn hàng {orderInfo?.orderCode}
                </div>
                {renderStatus(orderInfo?.workflowStepName, orderInfo?.workflowbgColor)}
              </div>
            </div>
          </div>
        </div>

        {/* Thông tin chung */}
        <div className='mt-2 bg-white px-10 py-2'>
          <div className='grid grid-cols-4 gap-2   '>
            {commonField?.map(field => (
              <>
                <ContentRender label={field.label} content={field.content} />
              </>
            ))}
          </div>
        </div>

        <SubscriptionTabs activeKey={activeKey} onChange={handleChangeActiveKey} items={subscriptionDetailTabs} />
      </Spin>
    </div>
  )
}
