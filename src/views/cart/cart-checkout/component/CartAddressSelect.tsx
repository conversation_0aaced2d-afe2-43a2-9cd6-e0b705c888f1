import React, { useState } from 'react'

import { useQuery } from '@tanstack/react-query'

import { Button, Form, Modal, Radio, Spin, Tabs } from 'antd'

import classNames from 'classnames'

import { useUser } from '@/hooks'

import SmeAddress from '@/models/SmeAddress'

import { CartAddressEdit } from './CartAddressEdit'

// Popup chọn địa chỉ của khách hàng
export const CartAddressSelect = ({
  value = [],
  onChange = () => undefined,
  children
}: {
  value?: any
  onChange?: any
  children?: any
}) => {
  // Lấy thông tin user
  const { user } = useUser()

  // Tab loại địa chỉ hiện tại (Popup đóng nếu không có thông tin tab)
  const [type, changeTab] = useState<any>(undefined)

  // Trạng thái đóng mở popup
  const opened = type != null

  // Trạng thái là địa chỉ xuất hóa đơn
  const isBilling = type === '0'

  // Trạng thái là khách hàng cá nhân
  const isPersonal = user?.customerType === 'PERSONAL'

  // Danh sách các loại địa chỉ
  const { data: addressTypes, isFetching: loadingAddressTypes } = useQuery({
    queryKey: ['getTypeAddressForSelect'],
    queryFn: SmeAddress.getTypeAddress,
    select: res => res?.map((addressType: any) => ({ type: String(addressType?.id), typeName: addressType?.name })),
    enabled: opened
  })

  // Gọi API lấy địa chỉ KH dựa theo tab loại địa chỉ
  const { data: addresses } = useQuery({
    queryKey: ['getUserAddresses', user?.id, type],
    queryFn: () => SmeAddress.getUserAddresses(user?.id, type),
    enabled: !!user?.id && opened,
    initialData: []
  })

  // ID của địa chỉ khách hàng mặc định
  const defaultAddressId = value?.id || value?.[type]?.id

  // Xử lý mở popup
  const open = () => changeTab(String(value?.type ?? 0))

  // Xử lý đóng popup
  const close = (formData: any) => {
    // Thông tin địa chỉ mới
    const newAddress = addresses?.find(
      address =>
        String(address?.type) === String(value?.type ?? type) && address?.id === (formData?.id || defaultAddressId)
    )

    // Xử lý tiếp nếu có địa chỉ mới
    if (newAddress) {
      // Gán thông tin tên và loại địa chỉ cho dữ liệu mới
      newAddress.type = type
      newAddress.typeName = addressTypes.find((item: any) => item?.type === type)?.typeName

      // Trường hợp dữ liệu đầu vào là một địa chỉ (bởi vì có loại địa chỉ)
      if (value?.type != null) {
        onChange(newAddress)
      }
      // Trường hợp dữ liệu đầu vào là nhiều địa chỉ
      else {
        onChange({ ...value, [type]: newAddress })
      }
    }

    // Đóng popup
    changeTab(undefined)
  }

  return (
    <>
      {/* Nút bật tắt màn chọn địa chỉ KH */}
      <div className='cursor-pointer' onClick={open}>
        {children || (
          <Button type='text' className='w-full overflow-hidden p-0'>
            <div className='flex size-full items-center justify-center gap-2 bg-gray-12'>
              <i className='onedx-add size-5' />
              <div className='text-sm font-medium'>Thêm địa chỉ</div>
            </div>
          </Button>
        )}
      </div>
      {/* Màn chọn địa chỉ KH */}
      <Modal centered destroyOnClose open={opened} title='Sổ địa chỉ' width={680} footer={null} onCancel={close}>
        <Spin spinning={loadingAddressTypes}>
          <Form name='info-address-select' onFinish={close}>
            <div className='-mx-6 my-4 border-y border-solid border-neutral-200 px-6 pb-4'>
              {/* Các tab chứa từng loại địa chỉ KH */}
              <Tabs
                activeKey={type}
                onChange={changeTab}
                items={addressTypes?.map((item: any) => ({
                  key: String(item.type),
                  label: item.typeName,
                  children: (
                    // Trường lưu id địa chỉ KH đã chọn trong Form
                    <Form.Item
                      name='id'
                      key={defaultAddressId}
                      initialValue={defaultAddressId}
                      preserve={false}
                      noStyle
                    >
                      {/* Danh sách địa chỉ KH có thể chọn */}
                      <Radio.Group
                        className='beauty-scroll flex max-h-[360px] w-full flex-col gap-3 overflow-y-scroll'
                        style={{ wordBreak: 'break-word' }}
                      >
                        {addresses?.length ? (
                          addresses?.map(address => (
                            // Địa chỉ KH có thể chọn
                            <Radio
                              key={address?.id}
                              value={address?.id}
                              className={classNames(
                                'w-full rounded bg-gray-12 p-3 pr-1 m-0',
                                '[&_.ant-radio-label]:!w-full [&_.ant-radio]:!self-start [&_.ant-radio]:!p-[3px]',
                                address?.defaultLocation && 'order-first'
                              )}
                            >
                              <div className='flex w-full items-start justify-between'>
                                <div className='w-full space-y-1'>
                                  <div className='text-sm font-semibold text-black'>
                                    {isBilling
                                      ? isPersonal
                                        ? `${address?.lastName} ${address?.firstName} | ${address?.repPersonalCertNumber}`
                                        : `${address?.smeName} | ${address?.tin ?? address?.repPersonalCertNumber}`
                                      : `${address?.lastName} ${address?.firstName} ${address?.phoneNumber ? ` | ${address?.phoneNumber}` : ''}`}
                                  </div>
                                  <div className='text-sm'>{address?.address}</div>
                                  <div className='flex gap-2 pt-1 empty:hidden'>
                                    {!!address?.defaultLocation && (
                                      <div className='rounded bg-gray-6 px-2 py-0.5 text-xs text-white'>Mặc định</div>
                                    )}
                                    {isPersonal &&
                                      (address?.typeAddress ? (
                                        <div className='rounded bg-success px-2 py-0.5 text-xs text-white'>
                                          Cơ quan/ Công ty
                                        </div>
                                      ) : (
                                        <div className='rounded bg-warning px-2 py-0.5 text-xs text-white'>
                                          Nhà riêng/ Chung cư
                                        </div>
                                      ))}
                                  </div>
                                </div>
                                {/* Chỉnh sửa địa chỉ */}
                                <CartAddressEdit value={address} />
                              </div>
                            </Radio>
                          ))
                        ) : (
                          // Màn thông báo chưa có địa chỉ KH và thêm địa chỉ
                          <div className='flex h-[200px] w-full items-center justify-center gap-3 rounded bg-gray-1'>
                            <i className='onedx-address size-24' />
                            <div className='space-y-1 pb-4'>
                              <div className='text-sm font-medium'>Chưa có địa chỉ</div>
                              <CartAddressEdit value={{ type }}>
                                <Button type='primary' className='text-sm font-medium'>
                                  <i className='onedx-add size-4' />
                                  Thêm địa chỉ
                                </Button>
                              </CartAddressEdit>
                            </div>
                          </div>
                        )}
                      </Radio.Group>
                    </Form.Item>
                  )
                }))}
              />
            </div>
            <div className='flex w-full items-center justify-between'>
              {/* Nút thêm địa chỉ - hiển thị khi đã có địa chỉ KH */}
              {addresses?.length > 0 && (
                <CartAddressEdit value={{ type }}>
                  <Button color='primary' variant='outlined'>
                    Thêm địa chỉ
                  </Button>
                </CartAddressEdit>
              )}
              <div className='flex flex-1 items-center justify-end gap-3'>
                <Button color='primary' variant='outlined' onClick={close}>
                  Huỷ
                </Button>
                <Button type='primary' htmlType='submit' className='text-sm font-medium'>
                  Xác nhận
                </Button>
              </div>
            </div>
          </Form>
        </Spin>
      </Modal>
    </>
  )
}
