import React, { useCallback, useMemo, useState } from 'react'

import { usePathname } from 'next/navigation'

import classNames from 'classnames'

import { useQuery } from '@tanstack/react-query'

import { Checkbox, Input, Popover, Skeleton, Tag } from 'antd'

import { debounce, range } from 'lodash'

import { FORMAT_COUPON_LIST_DTO } from '@views/order-management/create/convert'

import SmeSubscription from '@/models/SmeSubscription'

import { useUser } from '@/hooks'

import { formatCurrency } from '@/constants/commonFunction'

import ShoppingCart from '@/models/ShoppingCart'

import type { CartCouponType } from '@views/cart/type'

import { PROMOTION_TYPE_NUMBER } from '@views/subscription/constants'

// Xác định icon của khuyến mại
const voucherIcon = (coupon: any) => {
  if (coupon.couponPopupDTO?.promotionType === 'PRODUCT') return <i className='onedx-discount-gift size-6' />
  if (coupon.couponPopupDTO?.discountType === 'PRICE' || coupon.mcPopupDTO?.iconIndicator === 'DISCOUNT_PRICE')
    return <i className='onedx-discount-price size-6' />
  if (coupon.couponPopupDTO?.discountType === 'PERCENT' || coupon.mcPopupDTO?.iconIndicator === 'DISCOUNT_PERCENT')
    return <i className='onedx-discount-percent size-6' />

  return <i className='onedx-discount-price size-6' />
}

// Popup xem khuyến mại màn chi tiết giỏ hàng
export const CartSelectCoupon = ({
  type,
  value = undefined,
  onChange = () => undefined
}: {
  type: 'total' | 'bundling' | 'pricing' | 'addon'
  value?: any
  onChange?: (data: any) => any
}) => {
  // Lấy thông tin user
  const { user } = useUser()

  // Thông tin đường dẫn
  const portalType = usePathname()?.includes('partner-portal') ? 'DEV' : 'ADMIN'

  // Trạng thái hiển thị của popup
  const [opened, open] = useState(false)

  // Danh sách khuyến mại đã chọn lấy từ object dữ liệu đầu vào
  const selectedPromotions = useMemo(() => value?.[type + 'Promotions'] || [], [value, type])

  // Trạng thái đã được tích chọn của khuyến mại
  const checked: (coupon: CartCouponType) => boolean = useCallback(
    coupon =>
      selectedPromotions.some(
        (promotion: any) =>
          (coupon.promotionPopupType === 'COUPON' && coupon?.couponPopupDTO?.id === promotion.id) ||
          (coupon.promotionPopupType === 'MC' &&
            coupon?.mcPopupDTO?.mcId === promotion.id &&
            coupon?.mcPopupDTO?.activityIdx === promotion?.activityIdx)
      ),
    [selectedPromotions]
  )

  // Gọi API để lấy cấu hình khuyến mại và kiểm tra xem có đang chỉ cho phép chọn 1 khuyến mại hay không
  const { data: singlePromotionConfig } = useQuery({
    initialData: false,
    queryKey: ['couponConfig'],
    queryFn: () => SmeSubscription.getInfoConfigCoupon({ paramType: 'COUPON' }).then(res => res?.paramValue === '1')
  })

  // Xử lý tích chọn khuyến mại
  const check: (coupon: any) => void = useCallback(
    coupon => {
      // Thông tin của khuyến mại mới
      const newPromotion = !coupon.promotionPopupType
        ? coupon
        : coupon.promotionPopupType === 'MC'
          ? {
              id: coupon?.mcPopupDTO?.mcId,
              activityIdx: coupon?.mcPopupDTO?.activityIdx,
              mcName: coupon?.mcPopupDTO?.mcName
            }
          : {
              id: coupon?.couponPopupDTO?.id,
              activityIdx: undefined,
              couponName: coupon?.couponPopupDTO?.couponName
            }

      // Kiểm tra xem khuyến mại mới đã được chọn trước đó hay chưa
      const hasNewPromotion = selectedPromotions.some(
        (item: any) => item?.id === newPromotion?.id && item?.activityIdx === newPromotion?.activityIdx
      )

      // Thông tin khuyến mại mới
      const newPromotions: (typeof newPromotion)[] =
        // Thêm khuyến mại vừa chọn vào mảng các khuyến mại đã chọn trước đó (Nếu cấu hình chỉ cho phép chọn 1 khuyến mại thì mảng khuyến mại đã chọn sẽ luôn là rỗng)
        [singlePromotionConfig ? [] : selectedPromotions, newPromotion]
          .flat()
          // Lọc bỏ khuyến mại vừa chọn trong trường hợp nó đã được chọn và tồn tại trong mảng các khuyến mại đã chọn trước đó rồi
          .filter(
            (item: any) =>
              !hasNewPromotion || item?.id !== newPromotion?.id || item?.activityIdx !== newPromotion?.activityIdx
          )

      // Truyền thông tin khuyến mại mới ra ngoài
      onChange(Object.assign({}, value, { [type + 'Promotions']: newPromotions }))
    },
    [onChange, selectedPromotions, singlePromotionConfig, type, value]
  )

  // Gọi API lấy danh sách khuyến mại của gói - addon
  const { data: coupons, isPending: loadingCoupons } = useQuery({
    enabled: opened,
    queryKey: [
      'getCoupon',
      type,
      value?.packageId,
      value?.pricingId,
      value?.pricingMultiPlanId,
      value?.id,
      value?.addonMultiPlanId,
      value?.price,
      value?.quantity
    ],
    queryFn: async () =>
      // Gọi API danh sách khuyến mại
      ShoppingCart.getCartCoupon(
        FORMAT_COUPON_LIST_DTO({ ...value, type, portalType, customerType: user?.customerType, customerId: user?.id })
      ),
    select: res => res?.content || []
  })

  // Mã khuyến mại được nhập vào
  const [couponCode, setCouponCode] = useState<string | undefined>()

  // Xử lý nhập mã khuyến mại ở ô input
  const changeCouponCode = useMemo(
    () => debounce((event: React.ChangeEvent) => setCouponCode((event.target as HTMLInputElement).value), 300),
    []
  )

  // Gọi API lấy khuyến mại dựa theo mã được nhập vào
  const { data: couponsByCode, isFetching: loadingCouponsByCode } = useQuery({
    enabled: !!couponCode,
    queryKey: [
      'getCouponByCode',
      type,
      value?.packageId,
      value?.pricingId,
      value?.pricingMultiPlanId,
      value?.id,
      value?.addonMultiPlanId,
      couponCode
    ],
    queryFn: async () => {
      // Gọi API lấy khuyến mại dựa theo mã đã nhập
      const res: any = await ShoppingCart.getCartCouponByCode({
        code: couponCode,
        pricingId: value?.packageId || value?.pricingId,
        type: PROMOTION_TYPE_NUMBER[type],
        pricingMultiPlanId: value?.pricingMultiPlanId
      })

      // Trả về khuyến mại theo cấu trúc dữ liệu chung của khuyến mại trong trang
      return res?.couponId ? [{ couponPopupDTO: { ...res, id: res.couponId }, promotionPopupType: 'COUPON' }] : []
    }
  })

  // Danh sách khuyến mại trả về
  const couponList = useMemo(() => (couponCode ? couponsByCode : coupons), [couponCode, coupons, couponsByCode])

  // Hàm lấy thứ tự của khuyến mại (sắp xếp theo tứ tự Coupon tiền mặt - Coupon % - MC tiền mặt - MC % - Coupon tặng sản phẩm)
  const couponOrder = (coupon: any) => {
    if (coupon.promotionPopupType === 'COUPON') {
      if (coupon?.couponPopupDTO?.promotionType === 'DISCOUNT') {
        if (coupon?.couponPopupDTO?.discountType === 'PRICE') {
          return 'order-1'
        }

        return 'order-2'
      }

      return 'order-5'
    } else {
      if (coupon?.mcPopupDTO?.iconIndicator === 'DISCOUNT_PERCENT') {
        return 'order-3'
      }

      return 'order-4'
    }
  }

  // Trạng thái loading của API trong trang
  const loading = useMemo(() => loadingCoupons || loadingCouponsByCode, [loadingCoupons, loadingCouponsByCode])

  return (
    <div className={classNames('flex w-fit items-center gap-2', type === 'total' && 'flex-row-reverse')}>
      {/* Danh sách khuyến mại đã chọn */}
      {selectedPromotions.length > 0 && (
        <div className='flex w-fit items-center gap-2'>
          {/* Khuyến mại đầu tiên */}
          <Tag
            key={selectedPromotions[0]?.id}
            className='m-0 flex w-fit items-center gap-1'
            color='red'
            closable
            onClose={() => check(selectedPromotions[0])}
          >
            <i className='onedx-shopping-voucher mb-[2px] size-5' />
            {selectedPromotions[0]?.mcName || selectedPromotions[0]?.couponName}{' '}
          </Tag>
          {/* Các khuyến mại sau đó gói gọn vào */}
          {selectedPromotions.length > 1 && (
            <Popover
              placement='bottom'
              trigger='hover'
              content={
                <div key={selectedPromotions.length} className='flex flex-col gap-2'>
                  {selectedPromotions.map(
                    (promotion: any, promotionIndex: any) =>
                      promotionIndex > 0 && (
                        <Tag
                          key={promotion?.id}
                          className='m-0 flex w-fit items-center gap-1'
                          color='red'
                          closable
                          onClose={() => check(promotion)}
                        >
                          <i className='onedx-shopping-voucher mb-[2px] size-5' />
                          {promotion?.mcName || promotion?.couponName}{' '}
                        </Tag>
                      )
                  )}
                </div>
              }
            >
              <Tag className='m-0 flex w-fit items-center' color='red'>
                <i className='onedx-shopping-voucher mb-[2px] size-5' />+{selectedPromotions.length - 1}...
              </Tag>
            </Popover>
          )}
        </div>
      )}
      {/* Chọn khuyến mại */}
      <Popover
        trigger='click'
        placement='right'
        onOpenChange={open}
        open={opened}
        title={
          <div className='flex w-full items-center justify-between p-3 text-base font-[600] text-black'>
            Chọn voucher
            <i className='onedx-close-icon size-6 cursor-pointer' onClick={() => open(false)} />
          </div>
        }
        content={
          <div className='w-[360px] space-y-2'>
            {/* Nhập mã khuyến mại */}
            <div className='flex w-full items-center gap-2 pb-2'>
              {/* Ô nhập mã khuyến mại */}
              <Input className='w-full' placeholder='Nhập voucher' onChange={changeCouponCode} allowClear />
            </div>
            <div className='border-l-2 border-solid border-sme-orange-7 pl-2 text-sm font-[600] text-neutral-600'>
              Ưu đãi dành riêng cho bạn
            </div>
            {/* Danh sách khuyến mại */}
            <div className='beauty-scroll-transparent flex h-[283px] flex-col gap-2 overflow-y-auto'>
              {loading ? (
                // Trạng thái khi đang load của danh sách khuyến mại
                range(0, 3).map((_, index) => (
                  <div key={index} className='flex gap-2 rounded-xl border border-solid border-neutral-200 p-3'>
                    <div className='flex size-12 items-center justify-center rounded-full bg-sme-blue-3 text-primary'>
                      <Skeleton.Avatar active size={24} />
                    </div>
                    <div className='flex-1'>
                      <Skeleton.Input active size={12 as any} />
                      <Skeleton.Input active size={12 as any} />
                      <div className='cursor-pointer text-xs font-[600] text-primary'>Điều kiện</div>
                    </div>
                  </div>
                ))
              ) : // Danh sách khuyến mại lấy được theo mã đã nhập - Danh sách khuyến mại khả dụng
              couponList?.length ? (
                couponList?.map((coupon: CartCouponType, index: number) => (
                  <div
                    key={index}
                    className={classNames(
                      'flex items-start gap-2 rounded-xl border border-solid border-neutral-200 p-3',
                      couponOrder(coupon)
                    )}
                  >
                    <div className='flex size-12 items-center justify-center rounded-full bg-sme-blue-3 text-primary'>
                      {voucherIcon(coupon)}
                    </div>
                    <div className='flex-1'>
                      <div className='text-sm font-[600] text-black'>
                        {coupon.promotionPopupType === 'COUPON'
                          ? `${coupon.couponPopupDTO?.code} - ${coupon.couponPopupDTO?.promotionValue || `${formatCurrency(coupon.couponPopupDTO?.discountValue)}${Number(coupon.couponPopupDTO?.discountValue) > 100 ? ' VND' : '%'}`}`
                          : coupon.mcPopupDTO?.activityName}
                      </div>
                      <div className='text-sm font-[400] text-neutral-600'>
                        {coupon.couponPopupDTO?.couponName || coupon.mcPopupDTO?.mcName}
                      </div>
                      <Popover
                        trigger='click'
                        placement='right'
                        content={
                          <div className='w-[300px] space-y-2 p-1'>
                            {!!coupon.couponPopupDTO?.endDate && (
                              <div className='flex items-center gap-4 text-xs font-[500] text-neutral-600'>
                                Hạn sử dụng
                                <div className='text-black'>{coupon.couponPopupDTO.endDate}</div>
                              </div>
                            )}
                            <div className='text-xs font-[500] text-neutral-600'>Điều kiện</div>
                            <div className='space-y-1 rounded-lg bg-gray-1 p-2 pl-3 text-xs font-[400] text-black'>
                              {coupon.couponPopupDTO?.conditions?.length ? (
                                coupon.couponPopupDTO.conditions.map((condition, index) => (
                                  <div key={index} className='flex gap-1'>
                                    &#x2022;<div>{condition}</div>
                                  </div>
                                ))
                              ) : (
                                <div>&#x2022; Không có</div>
                              )}
                            </div>
                          </div>
                        }
                      >
                        <div className='w-fit cursor-pointer text-xs font-[600] text-primary'>Điều kiện</div>
                      </Popover>
                    </div>
                    <Checkbox checked={checked(coupon)} onClick={() => check(coupon)} />
                  </div>
                ))
              ) : (
                // Trường hợp không có khuyến mại
                <div className='flex size-full flex-col items-center justify-center text-neutral-600'>
                  <i className='onedx-empty size-30' />
                  <div className='text-sm font-[500]'>Không tìm thấy voucher</div>
                </div>
              )}
            </div>
          </div>
        }
      >
        {/* Xem thêm khuyến mại */}
        <div className='flex cursor-pointer items-center text-sm font-[500] text-primary'>
          {selectedPromotions.length ? (
            'Xem thêm voucher'
          ) : (
            <>
              {/* Icon chọn khuyến mại */}
              <i className='onedx-shopping-voucher mr-2 size-6' />
              Chọn voucher
            </>
          )}
        </div>
      </Popover>
    </div>
  )
}
