import React, { useState } from 'react'

import { useQuery, useQueryClient } from '@tanstack/react-query'

import { Button, Checkbox, Form, Modal, Select } from 'antd'

import { message } from '@components/notification'

import { useUser } from '@/hooks'

import SmeAddress from '@/models/SmeAddress'

import { normalizeBooleanValue, validateRequireInput } from '@/validator'

import { CartAddressEditContact } from '@views/cart/cart-checkout/component/CartAddressEditContact'

import { CartAddressEditLocation } from '@views/cart/cart-checkout/component/CartAddressEditLocation'

// Popup chỉnh sửa địa chỉ khách hàng
export const CartAddressEdit = ({
  children,
  value,
  onChange = () => undefined
}: {
  children?: any
  value?: any
  onChange?: any
}) => {
  // Lấy thông tin user
  const { user } = useUser()

  // Khai báo client của useQuery
  const queryClient = useQueryClient()

  // Trạng thái đóng mở của popup sửa địa chỉ KH
  const [opened, open] = useState(false)

  // Hàm đóng mở popup sửa địa chỉ KH
  const toggleOpen = () => open(!opened)

  // Hàm xử lý xác nhận tạo - sửa địa chỉ khách hàng
  const editAddress = (formValue: any) => {
    // Gọi API tạo nếu không có dữ liệu khởi điểm, hoặc sửa nếu có dữ liệu khởi điểm
    SmeAddress[value?.id ? 'editAddress' : 'createAddress']({ ...formValue, userId: user?.id })
      .then((res: any) => {
        // Truyền dữ liệu mới ra ngoài theo hàm onChange
        onChange(res)
        // Hiển thị thông báo thành công
        message.success(value?.id ? 'Chỉnh sửa địa chỉ thành công' : 'Tạo mới địa chỉ thành công')
        // Đóng popup sửa địa chỉ KH
        toggleOpen()
        // Gọi lại API chi tiết gói
        queryClient.invalidateQueries({ queryKey: ['getUserAddresses'] }).then()
        queryClient.invalidateQueries({ queryKey: ['getSetupAddress'] }).then()
      })
      .catch(() =>
        // Xử lý khi API gọi thất bại
        message.error('Có lỗi xảy ra. Vui lòng thử lại sau.')
      )
  }

  // Danh sách các loại địa chỉ
  const { data: addressTypes, isFetching: loadingAddressTypes } = useQuery({
    queryKey: ['getTypeAddressForEdit'],
    queryFn: SmeAddress.getTypeAddress,
    select: res => res?.map((addressType: any) => ({ value: String(addressType?.id), label: addressType?.name })),
    enabled: opened
  })

  return (
    <>
      {/* Nút đóng mở popup */}
      <div className='cursor-pointer' onClick={toggleOpen}>
        {children || (
          <Button type='text' className='size-fit p-0'>
            <i className='onedx-edit size-5 cursor-pointer' />
          </Button>
        )}
      </div>
      {/* Màn chỉnh sửa - tạo mới địa chỉ Khách hàng */}
      <Modal
        centered
        destroyOnClose
        width={650}
        open={opened}
        title={
          <div className='flex items-center gap-3'>
            <div className='flex items-center justify-center rounded-full bg-light-blue-1 p-1'>
              <i className='onedx-location-marker size-7 text-primary' />
            </div>
            {value?.id ? 'Chỉnh sửa địa chỉ' : 'Thêm địa chỉ'}
          </div>
        }
        footer={null}
        onCancel={toggleOpen}
      >
        <Form
          name='info-address-edit'
          layout='vertical'
          initialValues={{ ...value, type: value?.type != null ? String(value?.type) : '0' }}
          onFinish={editAddress}
        >
          <Form.Item name='id' hidden />
          <Form.Item name='userId' hidden />
          <div className='-mx-6 my-4 space-y-6 border-t border-solid border-neutral-200 p-6'>
            {/* Phân loại địa chỉ */}
            <div className='space-y-4'>
              <div className='border-l-2 border-solid border-sme-orange-7 px-2 font-[14px] font-medium leading-5'>
                Thông tin phân loại
              </div>
              <Form.Item
                name='type'
                label={<div className='text-xs font-medium'>Phân loại địa chỉ</div>}
                rules={[validateRequireInput('Phân loại địa chỉ không được bỏ trống')]}
              >
                <Select options={addressTypes} loading={loadingAddressTypes} />
              </Form.Item>
            </div>
            {/* Thông tin xuất hóa đơn */}
            <CartAddressEditContact />
            {/* Thông tin địa chỉ */}
            <CartAddressEditLocation />
            {/* Chọn lưu làm địa chỉ mặc định */}
            <Form.Item name='defaultLocation' noStyle valuePropName='checked' normalize={normalizeBooleanValue}>
              <Checkbox>Đặt làm địa chỉ mặc định</Checkbox>
            </Form.Item>
          </div>
          <div className='flex w-full items-center justify-end gap-3'>
            <Button color='primary' variant='outlined' onClick={toggleOpen}>
              Huỷ
            </Button>
            <Button type='primary' htmlType='submit' className='text-sm font-medium'>
              Xác nhận
            </Button>
          </div>
        </Form>
      </Modal>
    </>
  )
}
