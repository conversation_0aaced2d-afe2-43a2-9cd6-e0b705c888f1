import React from 'react'

import { useQuery } from '@tanstack/react-query'

import { Button, Spin, Tooltip } from 'antd'

import SystemParam from '@/models/SystemParam'

import { useUser } from '@/hooks'
import SmeAddress from '@/models/SmeAddress'

import { CartAddressSelect } from '@views/cart/cart-checkout/component/CartAddressSelect'

// Thông tin từng loại địa chỉ của màn thanh toán giỏ hàng
export const CartAddressDetail = ({
  value,
  onChange,
  isDefault = false
}: {
  value?: any
  onChange?: any
  isDefault?: boolean
}) => {
  // Thông tin khách hàng
  const { user } = useUser()

  // Trạng thái là địa chỉ xuất hóa đơn
  const isBilling = String(value?.type) === '0'

  // Trạng thái là khách hàng cá nhân
  const isPersonal = user?.customerType === 'PERSONAL'

  // Lấy và đặt giá trị mặc định của địa chỉ khách hàng (Chỉ gọi khi địa chỉ hiện tại là địa chỉ mặc định)
  const { isFetching: loadingAddress } = useQuery({
    queryKey: ['getUserDefaultAddresses', user?.id, String(value?.type)],
    queryFn: () =>
      SmeAddress.getUserDefaultAddresses(user?.id, value?.type).then(res =>
        onChange({ ...res, type: value?.type, typeName: value?.typeName })
      ),
    enabled: !!user?.id && isDefault
  })

  // Lấy thông tin cấu hình xuất hóa đơn (chỉ chạy trong trường hợp là địa chỉ xuất hóa đơn)
  const { data: billingConfig } = useQuery({
    queryKey: ['getSystemParamExportBilling'],
    queryFn: SystemParam.getSystemParamExportBilling,
    enabled: isBilling
  })

  // Hàm xử lý xóa địa chỉ
  const removeAddress = () => onChange(undefined)

  return (
    <Spin spinning={loadingAddress}>
      <div className='w-full p-4 text-sm leading-5'>
        <div className='mb-1 space-y-1 border-b border-solid border-neutral-200 pb-2'>
          <div className='flex w-full items-center justify-between gap-2 font-medium'>
            <div className='flex flex-1 items-center gap-2'>
              <i className='onedx-dot size-2 text-primary' />
              {value?.typeName}
            </div>
            {/* Nút chỉnh sửa địa chỉ */}
            <CartAddressSelect value={value} onChange={onChange}>
              <Button className='size-fit p-1'>
                <i className='onedx-edit size-4' />
              </Button>
            </CartAddressSelect>
            {/* Cho phép xóa nếu không phải là địa chỉ mặc định */}
            {!isDefault && (
              <Button className='size-fit p-1' onClick={removeAddress}>
                <i className='onedx-delete size-4' />
              </Button>
            )}
          </div>
          {isBilling ? (
            <div className='font-semibold'>
              {isPersonal
                ? `${value?.lastName} ${value?.firstName} | ${value?.repPersonalCertNumber}`
                : `${value?.smeName} | ${value?.tin ?? value?.repPersonalCertNumber}`}
            </div>
          ) : (
            <div className='font-semibold'>
              {`${value?.lastName} ${value?.firstName} ${value?.phoneNumber ? ` | ${value?.phoneNumber}` : ''}`}
            </div>
          )}
          <Tooltip title={value?.address || 'Chưa có thông tin địa chỉ'}>
            <div className='line-clamp-1 w-fit'>{value?.address || 'Chưa có thông tin địa chỉ'}</div>
          </Tooltip>
        </div>
        {isBilling && (
          <div className='text-xs text-gray-400'>
            Hóa đơn được tự động xuất sau {billingConfig?.numberOfDayExportBill} ngày
          </div>
        )}
        <div className='flex items-center gap-2 pt-1 empty:hidden'>
          {!!value?.defaultLocation && (
            <div className='w-fit rounded bg-gray-6 px-2 py-0.5 text-xs text-white'>Mặc định</div>
          )}
          {user?.customerType === 'PERSONAL' &&
            (value?.typeAddress ? (
              <div className='rounded bg-success px-2 py-0.5 text-xs text-white'>Cơ quan/ Công ty</div>
            ) : (
              <div className='rounded bg-warning px-2 py-0.5 text-xs text-white'>Nhà riêng/ Chung cư</div>
            ))}
        </div>
      </div>
    </Spin>
  )
}
