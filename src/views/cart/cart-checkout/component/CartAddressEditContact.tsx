import React from 'react'

import { Form, Input, Radio } from 'antd'

import { REGEX_MAIL_OR_TAX_CODE_OR_CERT } from '@views/auth/utils/constants'

import { useUser } from '@/hooks'

import { validateCustomPattern, validateEmail, validatePhoneNumber, validateRequireInput } from '@/validator'

// Thông tin liên hệ - Phần chỉnh sửa địa chỉ khách hàng
export const CartAddressEditContact = () => {
  // Lấy thông tin user
  const { user } = useUser()

  // Trạng thái là khách hàng cá nhân
  const isPersonal = user?.customerType === 'PERSONAL'

  // Trạng thái là loại địa chỉ xuất hóa đơn
  const isBilling = Form.useWatch('type') === '0'

  // Trạng thái là địa chỉ xuất hóa đơn doanh nghiệp
  const isEnterpriseAddress = Form.useWatch('typeAddress') === 1 && isBilling && isPersonal

  return (
    <div className='w-full space-y-2'>
      <div className='body-14-semibold mb-4 border-l-2 border-solid border-sme-orange-7 px-2'>
        {isBilling ? ' Thông tin khách hàng' : 'Thông tin liên hệ'}
      </div>
      {isBilling ? (
        // Thông tin địa chỉ trường hợp là địa chỉ xuất hóa đơn
        isPersonal ? (
          // Thông tin địa chỉ xuất hóa đơn của khách hàng cá nhân
          <>
            <div className='grid grid-cols-2 gap-x-4'>
              {/* Họ */}
              <Form.Item
                className='m-0'
                label={<div className='text-xs font-medium text-black'>Họ</div>}
                name='lastName'
                rules={[validateRequireInput('Họ không được bỏ trống')]}
              >
                <Input placeholder='Nhập họ' maxLength={20} />
              </Form.Item>
              {/* Tên */}
              <Form.Item
                className='m-0'
                label={<div className='text-xs font-medium text-black'>Tên</div>}
                name='firstName'
                rules={[validateRequireInput('Tên không được bỏ trống')]}
              >
                <Input placeholder='Nhập tên' maxLength={20} />
              </Form.Item>
            </div>
            {/* Tên doanh nghiệp trong trường hợp là địa chỉ xuất hóa đơn doanh nghiệp */}
            {isEnterpriseAddress && (
              <Form.Item
                className='m-0'
                label={<div className='text-xs font-medium text-black'>Tên doanh nghiệp</div>}
                name='smeName'
                rules={[validateRequireInput('Tên doanh nghiệp không được bỏ trống')]}
              >
                <Input placeholder='Nhập tên doanh nghiệp' maxLength={300} />
              </Form.Item>
            )}
            {/* Số chứng thực cá nhân */}
            <Form.Item
              className='m-0'
              label={<div className='text-xs font-medium text-black'>Số chứng thực cá nhân</div>}
              name='repPersonalCertNumber'
              rules={[validateRequireInput('Số chứng thực cá nhân không được bỏ trống')]}
            >
              <Input placeholder='Nhập số chứng thực cá nhân' maxLength={30} />
            </Form.Item>
            {/* Loại địa chỉ */}
            <Form.Item
              label={<div className='text-xs font-medium text-black'>Loại địa chỉ</div>}
              name='typeAddress'
              initialValue={0}
              preserve={false}
              className='m-0'
            >
              <Radio.Group
                className='grid w-full grid-cols-2 gap-4'
                options={[
                  { label: 'Nhà riêng/ Chung cư', value: 0 },
                  { label: 'Cơ quan/ Công ty', value: 1 }
                ]}
              />
            </Form.Item>
          </>
        ) : (
          // Thông tin địa chỉ xuất hóa đơn của khách hàng doanh nghiệp
          <>
            {/* Tên doanh nghiệp */}
            <Form.Item
              className='m-0'
              label={<div className='text-xs font-medium text-black'>Tên doanh nghiệp</div>}
              name='smeName'
              rules={[validateRequireInput('Tên doanh nghiệp không được bỏ trống')]}
            >
              <Input placeholder='Nhập tên doanh nghiệp' maxLength={300} />
            </Form.Item>
            {/* Số chứng thực */}
            <Form.Item
              label='Số chứng thực'
              name='repPersonalCertNumber'
              rules={[
                validateRequireInput('Số giấy chứng thực không được bỏ trống'),
                validateCustomPattern(REGEX_MAIL_OR_TAX_CODE_OR_CERT, 'Sai định dạng số chứng thực')
              ]}
              className='m-0'
              required
            >
              <Input allowClear maxLength={13} placeholder={'Nhập số chứng thực'} />
            </Form.Item>
          </>
        )
      ) : (
        // Thông tin địa chỉ chung của các loại địa chỉ còn lại
        <div className='grid grid-cols-2 gap-x-4 gap-y-2'>
          {/* Họ */}
          <Form.Item
            className='m-0'
            label={<div className='text-xs font-medium text-black'>Họ</div>}
            name='lastName'
            rules={[validateRequireInput('Họ không được bỏ trống')]}
          >
            <Input placeholder='Nhập họ' maxLength={20} />
          </Form.Item>
          {/* Tên */}
          <Form.Item
            className='m-0'
            label={<div className='text-xs font-medium text-black'>Tên</div>}
            name='firstName'
            rules={[validateRequireInput('Tên không được bỏ trống')]}
          >
            <Input placeholder='Nhập tên' maxLength={20} />
          </Form.Item>
          {/* Số điện thoại */}
          <Form.Item
            className='m-0'
            label={<div className='text-xs font-medium text-black'>Số điện thoại liên hệ</div>}
            name='phoneNumber'
            rules={[
              validateRequireInput('Số điện thoại liên hệ không được bỏ trống'),
              validatePhoneNumber('Số điện thoại liên hệ không hợp lệ')
            ]}
          >
            <Input placeholder='Nhập số điện thoại liên hệ' />
          </Form.Item>
          {/* Email */}
          <Form.Item
            className='m-0'
            label={<div className='text-xs font-medium text-black'>Email</div>}
            name='email'
            rules={[validateEmail('Sai định dạng email')]}
          >
            <Input placeholder='Nhập email' />
          </Form.Item>
        </div>
      )}
    </div>
  )
}
