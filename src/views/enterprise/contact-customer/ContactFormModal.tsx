import type { ReactElement } from 'react'

import React, { useRef, useState } from 'react'

import { useMutation, useQuery } from '@tanstack/react-query'

import { Button, Drawer, Form, Input, message, Modal, Select, Spin } from 'antd'

// eslint-disable-next-line import/no-named-as-default
import ReCA<PERSON><PERSON><PERSON> from 'react-google-recaptcha'

import { CUSTOMER_TYPE } from '@/constants/custom-field'

import { useResponsive } from '@/hooks'
import NewAddress from '@/models/NewAddress'
import SmeContact from '@/models/SmeContact'
import {
  validateEmail,
  validateKeyboardCharacters,
  validatePhoneNumber2,
  validateRequireInput,
  validateSpecialCharacters
} from '@/validator'

interface ContactFormProps {
  isModalVisible: boolean
  setIsModalVisible: React.Dispatch<React.SetStateAction<boolean>>
  customerType?: string
  createdSource?: string | null
}

interface ContactFormValues {
  name: string
  phoneNum: string
  email: string
  message: string
  city: number
}

const ContactFormWrapper = ({ children, props }: { children: ReactElement; props: any }) => {
  const { isMobile } = useResponsive()

  if (isMobile) {
    return (
      <Drawer
        {...props}
        placement='bottom'
        height={'96%'}
        onClose={() => props.onCancel()}
        styles={{ body: { padding: 16 } }}
        closeIcon={null}
        title={
          <div className='flex items-center justify-between'>
            <div className='text-center font-bold'>{props.title}</div>
            <i className='onedx-close-icon size-5 cursor-pointer' onClick={() => props.onCancel()} />
          </div>
        }
      >
        {children}
      </Drawer>
    )
  }

  return <Modal {...props}>{children}</Modal>
}

const ContactFormModal: React.FC<ContactFormProps> = ({
  isModalVisible,
  setIsModalVisible,
  customerType = CUSTOMER_TYPE.KHDN,
  createdSource = null
}) => {
  const [form] = Form.useForm()

  const [isDirty, setDirty] = useState<boolean>(true)

  const captchaRef = useRef(null)

  // Token captcha
  const captchaToken = Form.useWatch('captchaToken', form)

  // province
  const currentProvinceValue = Form.useWatch('city', form)

  // API
  // API lấy danh sách liên hệ (lấy ra liên hệ theo thành phố)
  const { data: currentContact, isFetching: isFetchingCurrentContact } = useQuery({
    queryKey: ['getContactProvinces', currentProvinceValue],
    queryFn: async () => {
      const res = await SmeContact.getContactProvinces()

      const temp = res.map(
        (item: { id: number; provinceId: number; name: string; phoneNo: number; email: string }) => ({
          value: item.provinceId,
          label: item.name,
          phoneNo: item.phoneNo,
          email: item.email,
          contactId: item.id
        })
      )

      // Liên hệ theo thành phố
      const currentProvince = temp?.find((item: { value: number }) => item.value === currentProvinceValue)

      return currentProvince
    },
    enabled: isModalVisible && !!currentProvinceValue
  })

  // API lấy danh sách tỉnh thành
  const { data: lstProvince, isLoading } = useQuery({
    queryKey: ['getLstProvince'],
    queryFn: async () => {
      const res = await NewAddress.getProvinces()

      return res?.content?.map((item: any) => ({ label: item.name, value: item.id })) || []
    },
    enabled: isModalVisible
  })

  // API gửi thông tin liên hệ
  const sendContactApi = useMutation({
    mutationKey: ['sendContact'],
    mutationFn: async (body: any) => {
      const data = {
        fullName: body.fullName,
        phoneNum: body.phoneNum,
        email: body.email,
        message: body.message,
        customerType,
        createdSource,
        captchaToken: body.captchaToken
      }

      await SmeContact.sendContact({ id: currentContact?.contactId, data })
    },
    onSuccess: () => {
      message.success('Gửi thông tin liên hệ thành công')
      form.resetFields()
      setIsModalVisible(false)
    },
    onError: () => {
      message.error('Đã có lỗi xảy ra, vui lòng thử lại sau')
    }
  })

  const handleFinish = (values: ContactFormValues) => {
    sendContactApi.mutate(values)
  }

  const handleCancel = () => {
    setIsModalVisible(false)
    form.resetFields()
  }

  return (
    <ContactFormWrapper
      props={{
        width: 600,
        title: 'Liên hệ',
        open: isModalVisible,
        onCancel: handleCancel,
        footer: null,
        maskClosable: false,
        centered: true,
        style: { padding: 0 },
        destroyOnClose: true
      }}
    >
      <Spin spinning={isLoading}>
        <div className='mx-auto rounded-lg bg-white p-2'>
          <Form
            form={form}
            layout='vertical'
            onFinish={handleFinish}
            onValuesChange={() => setDirty(false)}
            initialValues={{ city: 21 }}
          >
            <Form.Item label='Tỉnh/ Thành phố' name='city'>
              <Select
                showSearch
                notFoundContent='Không có dữ liệu'
                disabled={isLoading}
                options={lstProvince}
                filterOption={(input, option) => {
                  const label = option?.label

                  if (typeof label === 'string') {
                    return label.toLowerCase().includes(input.toLowerCase())
                  }

                  return false
                }}
              />
            </Form.Item>

            <Spin spinning={isFetchingCurrentContact}>
              <div className='text-sm text-gray-500'>
                <p>
                  Địa chỉ liên hệ:{' '}
                  <a href={`mailto:${currentContact?.email}`} className='text-primary'>
                    {currentContact?.email}
                  </a>
                </p>
                <p>
                  Số điện thoại:{' '}
                  <a href={`tel:${currentContact?.phoneNo}`} className='text-primary'>
                    {currentContact?.phoneNo}
                  </a>
                </p>
                <p>
                  Hotline:{' '}
                  <a href='tel:18001260' className='text-primary'>
                    18001260
                  </a>
                </p>
              </div>
            </Spin>

            <div className='grid grid-cols-2 gap-4 sm:grid-cols-1 sm:gap-2'>
              <Form.Item
                label='Họ và tên'
                name='fullName'
                rules={[
                  validateRequireInput('Họ và tên không được bỏ trống'),
                  validateKeyboardCharacters('Không cho phép nhập ký tự đặc biệt'),
                  validateSpecialCharacters('Không cho phép nhập ký tự đặc biệt')
                ]}
                className='mb-4'
              >
                <Input maxLength={100} placeholder='Nhập họ và tên' />
              </Form.Item>

              <Form.Item
                label='Số điện thoại'
                name='phoneNum'
                rules={[
                  validateRequireInput('Số điện thoại không được bỏ trống'),
                  validatePhoneNumber2('Số điện thoại không hợp lệ')
                ]}
                className='mb-4'
              >
                <Input maxLength={11} placeholder='Nhập số điện thoại' />
              </Form.Item>
            </div>

            <Form.Item
              label='Địa chỉ email'
              name='email'
              validateFirst
              rules={[validateRequireInput('Địa chỉ email không được bỏ trống'), validateEmail('Sai định dạng email')]}
              className='mb-4'
            >
              <Input maxLength={100} placeholder='Nhập địa chỉ email' />
            </Form.Item>

            <Form.Item
              label='Lời nhắn'
              name='message'
              required
              rules={[validateRequireInput('Lời nhắn không được bỏ trống')]}
              className='mb-10'
            >
              <Input.TextArea placeholder='Thông tin muốn tư vấn' maxLength={500} showCount rows={4} />
            </Form.Item>

            <div className='mb-4'>
              <Form.Item name='captchaToken' noStyle hidden />
              <ReCAPTCHA
                ref={captchaRef}
                sitekey={process.env.NEXT_PUBLIC_RECAPCHA_KEY || '6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI'}
                hl='vi'
                onChange={val => {
                  form.setFieldsValue({ captchaToken: val })
                }}
              />
            </div>

            <Button
              disabled={isDirty || !captchaToken}
              type='primary'
              htmlType='submit'
              className='w-full'
              size='large'
            >
              Gửi
            </Button>
          </Form>
        </div>
      </Spin>
    </ContactFormWrapper>
  )
}

export default ContactFormModal
