'use client'
import React, { useState } from 'react'

import { usePathname, useRouter } from 'next/navigation'

import { isNil } from 'lodash'

import { CUSTOMER_TYPE_LABEL } from '@/constants/common'
import { useResponsive, useUser, useLocalState } from '@/hooks'
import DXPortal from '@/models/DXPortal'
import { documentHostname } from '@/utils/SEO/seoConfigs'
import CardContact from './CardContact'
import ContactFormModal from './ContactFormModal'

interface ContactCustomerProps {
  customerType?: string
}

const ContactCustomer: React.FC<ContactCustomerProps> = () => {
  const pathname = usePathname()

  // Xử lý để lấy customerType từ pathname
  // Ví dụ: /enterprise/products
  const pathParts = pathname.split('/')
  const customerType = pathParts[1] // Lấy customerType từ đường dẫn, ví dụ: /enterprise/products/1

  const router = useRouter()
  const { isMobile } = useResponsive()
  const { user, isLoggedIn } = useUser()
  const [modalVisible, setModalVisible] = useState(false)
  const { state, setFieldValue } = useLocalState()

  const isModalVisible = state?.ContactCustomer?.isModalVisible ?? modalVisible

  const handleSetModalVisible = (value: boolean | ((prevState: boolean) => boolean)) => {
    if (!isNil(state?.ContactCustomer)) {
      setFieldValue('ContactCustomer.isModalVisible', value)
    } else {
      setModalVisible(value)
    }
  }

  return (
    <div className='container mx-auto'>
      <div className=' w-full '>
        <div className='container-original mx-auto w-full space-y-8 py-10'>
          <h2 className='text-title-32 font-semibold text-neutral-950 sm:px-4 sm:text-title-24'>Liên hệ</h2>

          <div className='mt-8 grid grid-cols-3 gap-8 sm:mt-6 sm:grid-cols-1'>
            <CardContact
              icon={<i className='onedx-comment size-8' />}
              title='Tư vấn mua hàng'
              content=''
              textBtn='Liên hệ ngay'
              showSeparator={!isMobile}
              onClick={() => handleSetModalVisible(true)}
            />

            <CardContact
              icon={<i className='onedx-support size-8' />}
              title='Hỗ trợ kỹ thuật'
              content=''
              textBtn='Liên hệ ngay'
              showSeparator
              onClick={() => (window.location.href = documentHostname)}
            />

            <CardContact
              icon={<i className='onedx-search-app size-8' />}
              title='Tra cứu đơn hàng & bảo hành'
              content=''
              textBtn='Tra cứu'
              showSeparator={isMobile}
              onClick={() => {
                isLoggedIn
                  ? router.push(
                      DXPortal.getPortalByCustomerType(user?.customerType).createPath('/account/subscription')
                    )
                  : router.push('/sme-portal/login')
              }}
            />
          </div>
        </div>
      </div>

      {/* Modal */}
      <ContactFormModal
        isModalVisible={isModalVisible}
        setIsModalVisible={handleSetModalVisible}
        customerType={CUSTOMER_TYPE_LABEL[customerType]}
      />
    </div>
  )
}

export default ContactCustomer
