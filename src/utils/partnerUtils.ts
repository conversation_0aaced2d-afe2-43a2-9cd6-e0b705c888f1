import moment from 'moment'

import {
  capabilityMapping,
  CURRENCY_THRESHOLDS,
  DATE_FORMAT,
  DOCUMENT_TYPES,
  FILE_ICONS,
  REVENUE_DATE_FORMAT,
  SPECIAL_FORMATTED_TIME,
  SPECIAL_LABEL_TIME
} from '@/components/partner-management/common/constants'

export const convertCapabilities = (codes: string[]): string[] => {
  return codes.map(code => capabilityMapping[code] || code)
}

export const getFileIcon = (fileName: string) => {
  const ext = fileName.split('.').pop()?.toLowerCase()

  return FILE_ICONS[ext as keyof typeof FILE_ICONS] || FILE_ICONS.default
}

export const getTypeDisplay = (type: keyof typeof DOCUMENT_TYPES) => DOCUMENT_TYPES[type] || type

export const getDocumentStatus = (expireAt: string) => {
  const expireDate = new Date(expireAt.split('/').reverse().join('-'))

  return expireDate > new Date() ? 'active' : 'expired'
}

export const formatCurrency = (amount: number | null | undefined): string => {
  if (!amount) return '0'

  if (amount >= CURRENCY_THRESHOLDS.BILLION) {
    return `${(amount / CURRENCY_THRESHOLDS.BILLION).toFixed(2)}B`
  } else if (amount >= CURRENCY_THRESHOLDS.MILLION) {
    return `${(amount / CURRENCY_THRESHOLDS.MILLION).toFixed(2)}M`
  } else if (amount >= CURRENCY_THRESHOLDS.THOUSAND) {
    return `${(amount / CURRENCY_THRESHOLDS.THOUSAND).toFixed(0)}K`
  }

  return amount.toString()
}

export const calculateGrowthRate = (currentValue: number | null, previousValue: number | null): string | null => {
  if (!previousValue || previousValue === 0) return null
  if (!currentValue) currentValue = 0
  const growth = ((currentValue - previousValue) / Math.abs(previousValue)) * 100

  return growth.toFixed(1)
}

export const getStartOfMonth = (amount: number): string =>
  moment().subtract(amount, 'months').startOf('month').format(DATE_FORMAT)

export const getCurrentOfMonth = (amount: number): string => moment().subtract(amount, 'months').format(DATE_FORMAT)

export const formatCurrencyMoney = (value: number): string => {
  return new Intl.NumberFormat('vi-VN').format(value)
}

export const formatDisplayTime = (labelTime: string): string => {
  if (!labelTime || typeof labelTime !== 'string') return labelTime

  const parts = labelTime.split('/')

  if (parts.length === 2) {
    // Convert YYYY/MM to MM/YYYY if needed
    if (parts[0].length === 4) {
      return `${parts[1]}/${parts[0]}`
    }
  }

  return labelTime
}

export const isCurrentMonth = (labelTime: string): boolean => {
  const currentMonth = moment().format('MM/YYYY')
  const formattedTime = formatDisplayTime(labelTime)

  return formattedTime === currentMonth ||
    formattedTime === SPECIAL_FORMATTED_TIME ||
    labelTime === SPECIAL_LABEL_TIME

}

export const processRevenueData = (data: any[]) => {
  if (!data || !Array.isArray(data)) return []

  return data
    .map((item, index) => {
      const formattedName = formatDisplayTime(item.labelTime)

      return {
        ...item,
        id: index + 1,
        name: formattedName,
        clickRate: item.revenue || 0,
        isCurrentMonth: isCurrentMonth(item.labelTime),
        originalData: item
      }
    })
    .sort((a, b) => {
      const dateA = moment(a.name, REVENUE_DATE_FORMAT)
      const dateB = moment(b.name, REVENUE_DATE_FORMAT)

      if (dateA.isValid() && dateB.isValid()) {
        return dateA.diff(dateB)
      }

      return 0
    })
}
