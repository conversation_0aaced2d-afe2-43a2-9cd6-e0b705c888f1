import dayjs from 'dayjs'
import type { Dayjs } from 'dayjs'

// Dayjs formats - converted from Java Spring Boot formats
export const DATE_FORMATS = {
  DD_MM_YYYY: 'DDMMYYYY', // 'ddMMyyyy'
  DD_MM_YYYY_DASH: 'DD-MM-YYYY', // 'dd-MM-yyyy'
  DD_MM_YYYY_HH_MM: 'DD/MM/YYYY HH:mm', // 'dd/MM/yyyy HH:mm'
  DD_MM_YYYY_HH_MM_A: 'DD/MM/YYYY hh:mmA', // 'dd/MM/yyyy hh:mma'
  DD_MM_YYYY_HH_MM_SS: 'DD/MM/YYYY HH:mm:ss', // 'dd/MM/yyyy HH:mm:ss'
  DD_MM_YYYY_SLASH: 'DD/MM/YYYY', // 'dd/MM/yyyy'
  DD_MM_SLASH: 'DD/MM', // 'dd/MM'
  DD_MM_YY_HH_MM_SS: 'DD/MM/YY HH:mm:ss', // 'dd/MM/yy HH:mm:ss'
  FILE_DD_MM_YYYY_HH_MM_SS: 'DDMMYYYYHHmmss', // 'ddMMyyyyHHmmss'
  HH_MM_DD_MM_YYYY: 'HH:mm DD-MM-YYYY', // 'HH:mm dd-MM-yyyy'
  MM_YYYY: 'MM/YYYY', // 'MM/uuuu'
  YY_MMDD_HHMM: 'YYMMDDHHmm', // 'yyMMddHHmm'
  YYYY: 'YYYY', // 'yyyy'
  YYYY_MM_DD: 'YYYY-MM-DD', // 'yyyy-MM-dd'
  YYYY_MM_DD_2: 'YYYY/MM/DD', // 'YYYY/MM/dd'
  YYYY_MM_DD_HH_MM_SS: 'YYYY-MM-DD HH:mm:ss', // 'yyyy-MM-dd HH:mm:ss'
  YYYY_MM_DD_T_HH_MM_SS: 'YYYY-MM-DD[T]HH:mm:ss', // "yyyy-MM-dd'T'HH:mm:ss"
  YYYYMMDDHH: 'YYYYMMDDH', // 'yyyyMMddHH'
  YYYYMMDD_HHMMSS: 'YYYYMMDD_HHmmss', // 'yyyyMMdd_HHmmss'
  YYYY_MMDD_HHMM: 'YYYYMMDDHHmm', // 'yyyyMMddHHmm'
  YYYY_MMDD_HHMMSS: 'YYYYMMDDHHmmss', // 'yyyyMMddHHmmss'
  YYYY_MMDD_HHMMSS_SSS: 'YYYYMMDDHHmmssSSS' // 'yyyyMMddHHmmssSSS'
} as const

export const TIME_ZONES = {
  ICT: 'Asia/Jakarta',
  VIETNAM: 'Asia/Ho_Chi_Minh'
} as const

export const DATE_CONSTANTS = {
  SEARCH_START_DATE_SUFFIX: ' 00:00:00',
  SEARCH_END_DATE_SUFFIX: ' 23:59:59',
  DATE_OF_WEEK: 7
} as const

export const convertJavaFormatToDayjs = (javaFormat: string): string => {
  return javaFormat
    .replace(/yyyy/g, 'YYYY')
    .replace(/yy/g, 'YY')
    .replace(/dd/g, 'DD')
    .replace(/MM/g, 'MM')
    .replace(/HH/g, 'HH')
    .replace(/mm/g, 'mm')
    .replace(/ss/g, 'ss')
    .replace(/SSS/g, 'SSS')
    .replace(/hh/g, 'hh')
    .replace(/a/g, 'A')
}

export const convertDayjsFormatToJava = (dayjsFormat: string): string => {
  return dayjsFormat
    .replace(/YYYY/g, 'yyyy')
    .replace(/YY/g, 'yy')
    .replace(/DD/g, 'dd')
    .replace(/MM/g, 'MM')
    .replace(/HH/g, 'HH')
    .replace(/mm/g, 'mm')
    .replace(/ss/g, 'ss')
    .replace(/SSS/g, 'SSS')
    .replace(/hh/g, 'hh')
    .replace(/A/g, 'a')
}

/**
 * Kiểm tra input date có nằm trước ngày hiện tại hay không
 * @param current Ngày hiện tại
 * @param inclusive Có bao gồm ngày hiện tại không (so sánh bằng)
 * @returns
 */
export const disabledBeforeCurrent = (current: Dayjs, inclusive: boolean = true) => {
  if (inclusive) {
    return current && current <= dayjs().startOf('day')
  }

  return current && current < dayjs().startOf('day')
}

/**
 * Kiểm tra xem ngày hiện tại có bị vô hiệu hóa không
 * @param current Ngày hiện tại
 * @param inclusive Có bao gồm ngày hiện tại không (so sánh bằng)
 * @returns
 */
export const disabledAfterCurrent = (current: Dayjs, inclusive: boolean = true) => {
  if (inclusive) {
    return current && current > dayjs().endOf('day')
  }

  return current && current >= dayjs().endOf('day')
}

export const getEndOfDay = (date?: Dayjs, format?: string): Dayjs => {
  if (!date) {
    return dayjs().endOf('day')
  }

  const currentFormat = format || DATE_FORMATS.YYYY_MM_DD

  return dayjs(date, currentFormat).endOf('day')
}

export const getStartOfDay = (date?: string, format?: string): Dayjs => {
  if (!date) {
    return dayjs().startOf('day')
  }

  const currentFormat = format || DATE_FORMATS.YYYY_MM_DD

  return dayjs(date, currentFormat).startOf('day')
}

export const formatDate = (date: Dayjs | string | undefined, format: string = DATE_FORMATS.YYYY_MM_DD): string => {
  if (!date) {
    return ''
  }

  const dayjsDate = typeof date === 'string' ? dayjs(date, format) : date

  // Thêm kiểm tra isValid
  if (!dayjsDate || typeof dayjsDate.isValid !== 'function' || !dayjsDate.isValid()) {
    return ''
  }

  return dayjsDate.format(format)
}

export const getDate = (
  date: Dayjs | string | undefined,
  format: string = DATE_FORMATS.YYYY_MM_DD
): Dayjs | undefined => {
  if (!date) {
    return undefined
  }

  if (typeof date === 'string') {
    return dayjs(date, format)
  }

  return date
}
