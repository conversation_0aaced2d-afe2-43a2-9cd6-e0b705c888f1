import { concat, trim } from 'lodash'
import moment from 'moment'

import { PAYMENT_METHOD_STR } from '@/constants/subscriptions'

export const renderPaymentMethod = (paymentMethod: number) => {
  switch (paymentMethod) {
    case 0:
      return 'Chuyển khoản'
    case 1:
    case 3:
      return 'Thanh toán trả trước'
    case 2:
      return 'Thanh toán tiền mặt'
    default:
      return null
  }
}

export const renderPaymentMethodByString = (paymentMethod: string) => {
  switch (paymentMethod) {
    case PAYMENT_METHOD_STR.BY_CASH:
      return 'Thanh toán tiền mặt'
    case PAYMENT_METHOD_STR.VNPTPAY:
    case PAYMENT_METHOD_STR.VNPTPAY_QR:
      return 'Thanh toán trả trước'
    default:
      return null
  }
}

export const convertCustomerInfo = (customerInfo: any) => ({
  id: customerInfo?.customerAddressId,
  smeName: customerInfo?.companyName || customerInfo?.smeName,
  firstName: customerInfo?.customerFirstName || customerInfo?.firstName,
  lastName: customerInfo?.customerLastName || customerInfo?.lastName,
  repPersonalCertNumber: customerInfo?.identityNo || customerInfo?.repPersonalCertNumber,
  tin: customerInfo?.customerTaxCode || customerInfo?.taxNo,
  address: customerInfo?.customerAddress || customerInfo?.address,
  typeAddress: customerInfo?.addressType || customerInfo?.typeAddress,
  provinceId: customerInfo?.customerProvinceId,
  provinceCode: customerInfo?.customerProvinceCode,
  provinceName: customerInfo?.customerProvinceName,
  districtId: customerInfo?.customerDistrictId,
  districtCode: customerInfo?.customerDistrictCode,
  districtName: customerInfo?.customerDistrictName,
  wardId: customerInfo?.customerWardId,
  wardCode: customerInfo?.customerWardCode,
  wardName: customerInfo?.customerWardName,
  streetId: customerInfo?.customerStreetId,
  streetName: customerInfo?.customerStreetName
})

export const totalRefundAmount = (applyList: any) => {
  const result = applyList.reduce((total: number, item: { amountRefund: number }) => {
    return total + (item.amountRefund || 0)
  }, 0)

  return result
}

export const addDays = (dateObj: Date, numDays: number) => {
  dateObj.setDate(dateObj.getDate() + numDays)

  return dateObj
}

export const formatDate = (dateString: string) => {
  if (moment(dateString, 'DD-MM-YYYY', true).isValid()) {
    return dateString
  }

  return moment(dateString, 'DD/MM/YYYY').format('DD-MM-YYYY')
}

export const TYPE_COUPON = {
  COUPON: 'COUPON',
  MC: 'MC'
}

// Gộp ds coupon
export const mergeCouponList = ({
  obj: {
    invoiceCouponPrices = [],
    invoiceCouponPercents = [],
    lstMcInvoicePercent = [],
    lstMcInvoicePrice = [],
    lstMcGift = [],
    couponPrices = [],
    couponPercent = [],
    lstMcPrivatePercent = [],
    lstMcPrivatePrice = []
  },
  isDisabled = false
}: {
  obj: {
    invoiceCouponPrices: any[]
    invoiceCouponPercents: any[]
    lstMcInvoicePercent: any[]
    lstMcInvoicePrice: any[]
    lstMcGift: any[]
    couponPrices: any[]
    couponPercent: any[]
    lstMcPrivatePercent: any[]
    lstMcPrivatePrice: any[]
  }
  isDisabled?: boolean
}) => {
  const invoicePriceCoupons = concat(invoiceCouponPrices, invoiceCouponPercents).map((coupon: any) => ({
    ...coupon,
    promotionPopupType: TYPE_COUPON.COUPON,
    isDisabled
  }))

  const invoiceMcCoupons = concat(lstMcInvoicePercent, lstMcInvoicePrice, lstMcGift).map((coupon: any) => ({
    ...coupon,
    promotionPopupType: TYPE_COUPON.MC,
    isDisabled
  }))

  const pricingPriceCoupons = concat(couponPrices, couponPercent).map((coupon: any) => ({
    ...coupon,
    promotionPopupType: TYPE_COUPON.COUPON,
    isDisabled
  }))

  const pricingMcCoupons = concat(lstMcPrivatePercent, lstMcPrivatePrice).map((coupon: any) => ({
    ...coupon,
    promotionPopupType: TYPE_COUPON.MC,
    isDisabled
  }))

  return {
    couponList: [...pricingPriceCoupons, ...pricingMcCoupons],
    coupons: [...invoicePriceCoupons, ...invoiceMcCoupons]
  }
}

// Sắp xếp theo isRequired
export const sortByRequired = (list: { isRequired: string }[]) => {
  const newList = [...list]

  return newList.sort((a, b) => {
    const isARequired = a.isRequired === 'YES' ? 1 : 0
    const isBRequired = b.isRequired === 'YES' ? 1 : 0

    return isBRequired - isARequired
  })
}

export const formatNormalizeTaxCodev2 = (value = '') => {
  if (value && trim(value)) {
    const taxCode = value.toString().trim()

    if (taxCode.length > 10) {
      return `${taxCode.slice(0, 10)}-${taxCode.slice(10)}`
    }

    return taxCode
  }

  return value
}
