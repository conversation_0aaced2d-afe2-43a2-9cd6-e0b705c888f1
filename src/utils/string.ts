import type { ImageLoader } from 'next/image'

import type { FilterFunc } from 'rc-select/lib/Select'

import { encode } from 'html-entities'

import { isBoolean } from 'lodash'

import { API_ROOT } from '@/models/Base'

export const isEmail = (str: string) => /[a-zA-Z]/.test(str)

/** Hàm lấy chữ cái đầu tiên */
export const getFirstLetter = (str: string): string => {
  const firstLetter = str?.charAt(0).toUpperCase()

  return firstLetter
}

/** Hàm lấy link ảnh */
export const handleSrcImg = (src: string | undefined): string => {
  if (!src) return '/assets/images/no-image.jpg'

  if (isExternalFile(src as string)) {
    return `${src}`
  }

  return `${API_ROOT}${src}`
}

/** Hàm lấy ảnh của Nextjs */
export const handleLoaderImg: ImageLoader = ({ src, width, quality }) => {
  if (isExternalFile(src)) {
    return src
  }

  return `${API_ROOT}${src}?w=${width}&q=${quality || 75}`
}

/** Hàm lọc khi nhập tìm kiếm */
export const filterOption: FilterFunc<any> = (input, option) => {
  const normalizedValue = input?.normalize('NFD').replace(/[\u0300-\u036f]/g, '')

  const normalizedOption = option.label?.normalize('NFD').replace(/[\u0300-\u036f]/g, '')

  return normalizedOption?.toLowerCase().includes(normalizedValue?.toLowerCase())
}

export const filterOptionTrim: FilterFunc<any> = (input, option) => {
  const normalizedValue = input?.normalize('NFD').replace(/[\u0300-\u036f]/g, '')
  const normalizedOption = option.label?.normalize('NFD').replace(/[\u0300-\u036f]/g, '')

  return normalizedOption?.toLowerCase().includes(normalizedValue?.trim()?.toLowerCase())
}

/**
 * Hàm so sánh chuỗi có hỗ trợ tiếng Việt không dấu
 * @param input - Chuỗi tìm kiếm
 * @param target - Chuỗi được tìm kiếm
 * @param options - Tùy chọn cho việc so sánh
 * @returns true nếu target chứa input (không phân biệt hoa thường và dấu tiếng Việt)
 */
export const compareStringWithVietnamese = (
  input: string | null | undefined,
  target: string | null | undefined,
  options: {

    /** Có trim input không? Mặc định: true */
    trimInput?: boolean

    /** Có so sánh chính xác không? Mặc định: false (sử dụng includes) */
    exact?: boolean

    /** Có phân biệt hoa thường không? Mặc định: false */
    caseSensitive?: boolean
  } = {}
): boolean => {
  const { trimInput = true, exact = false, caseSensitive = false } = options

  // Xử lý trường hợp null/undefined
  if (!input || !target) return false

  // Normalize để loại bỏ dấu tiếng Việt
  const normalizedInput = input.normalize('NFD').replace(/[\u0300-\u036f]/g, '')
  const normalizedTarget = target.normalize('NFD').replace(/[\u0300-\u036f]/g, '')

  // Xử lý trim input
  const processedInput = trimInput ? normalizedInput.trim() : normalizedInput

  // Xử lý case sensitive
  const finalInput = caseSensitive ? processedInput : processedInput.toLowerCase()
  const finalTarget = caseSensitive ? normalizedTarget : normalizedTarget.toLowerCase()

  // So sánh chính xác hoặc contains
  return exact ? finalTarget === finalInput : finalTarget.includes(finalInput)
}

export const isExternalFile = (fileUrl: string | null | undefined) => {
  return (
    typeof fileUrl === 'string' &&
    (fileUrl.includes('http://') || fileUrl.includes('https://') || fileUrl.startsWith('data:image/'))
  )
}

export const encodeString = (input = '') => {
  return encode(input || '')
    .replace(/\(/g, '&#40;')
    .replace(/\)/g, '&#41;')
}

// Format tiếng việt không dấu
export const convertStringToEnglish = (str: string) => {
  str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, 'a')
  str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, 'e')
  str = str.replace(/ì|í|ị|ỉ|ĩ/g, 'i')
  str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, 'o')
  str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, 'u')
  str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, 'y')
  str = str.replace(/đ/g, 'd')
  str = str.replace(/À|Á|Ạ|Ả|Ã|Â|Ầ|Ấ|Ậ|Ẩ|Ẫ|Ă|Ằ|Ắ|Ặ|Ẳ|Ẵ/g, 'A')
  str = str.replace(/È|É|Ẹ|Ẻ|Ẽ|Ê|Ề|Ế|Ệ|Ể|Ễ/g, 'E')
  str = str.replace(/Ì|Í|Ị|Ỉ|Ĩ/g, 'I')
  str = str.replace(/Ò|Ó|Ọ|Ỏ|Õ|Ô|Ồ|Ố|Ộ|Ổ|Ỗ|Ơ|Ờ|Ớ|Ợ|Ở|Ỡ/g, 'O')
  str = str.replace(/Ù|Ú|Ụ|Ủ|Ũ|Ư|Ừ|Ứ|Ự|Ử|Ữ/g, 'U')
  str = str.replace(/Ỳ|Ý|Ỵ|Ỷ|Ỹ/g, 'Y')
  str = str.replace(/Đ/g, 'D')

  return str
}

export const replaceSpecialCharacters = (input: string) => {
  const values = convertStringToEnglish(input)
  const regex = /[!@#$%^&*()+?<>:_;\s`~\-=[\]{}\\|'",./]/g

  return values.replace(regex, '')
}

/**
 * Kiểm tra chuỗi có chứa emoji hay không
 * @param text Chuỗi cần kiểm tra
 * @returns true nếu chuỗi chứa emoji, false nếu không
 */
export const hasEmoji = (text: string): boolean => {
  // Kiểm tra xem chuỗi có phải là chỉ số hoặc chữ và số bình thường không
  if (/^[a-zA-Z0-9]+$/.test(text)) {
    // Chuỗi chỉ chứa chữ cái Latin và số, không phải emoji
    return false
  }

  // Bảng tra đầy đủ cho các khoảng emoji Unicode
  // Cập nhật đến Unicode 15.0
  const emojiRanges = [
    // Các emoji phổ biến và biểu tượng cảm xúc
    [0x1f600, 0x1f64f], // Emoticons
    [0x1f300, 0x1f5ff], // Misc Symbols and Pictographs
    [0x1f680, 0x1f6ff], // Transport and Map
    [0x1f700, 0x1f77f], // Alchemical Symbols
    [0x1f780, 0x1f7ff], // Geometric Shapes
    [0x1f800, 0x1f8ff], // Supplemental Arrows-C
    [0x1f900, 0x1f9ff], // Supplemental Symbols and Pictographs
    [0x1fa00, 0x1fa6f], // Chess Symbols
    [0x1fa70, 0x1faff], // Symbols and Pictographs Extended-A
    [0x2600, 0x26ff], // Misc Symbols
    [0x2700, 0x27bf], // Dingbats
    [0x2b50, 0x2b50], // Star symbol
    [0x2764, 0x2764], // Heart symbol
    [0x1f004, 0x1f004], // Mahjong Tile Red Dragon
    [0x1f0cf, 0x1f0cf], // Playing Card Black Joker
    [0x1f18e, 0x1f18e], // Squared AB
    [0x1f191, 0x1f19a], // Squared CL
    [0x1f201, 0x1f20f], // Squared Katakana
    [0x1f21a, 0x1f21a], // Squared CJK Unified Ideograph-7121
    [0x1f22f, 0x1f22f], // Squared CJK Unified Ideograph-6307
    [0x1f232, 0x1f23a], // Squared CJK Unified Ideograph-7981
    [0x1f250, 0x1f251], // Circled Ideograph Advantage
    [0x1f3fb, 0x1f3ff], // Emoji Modifier Fitzpatrick
    [0x1f926, 0x1f937], // Face Palm
    [0x1f970, 0x1f9a2], // Smiling Face with Hearts
    [0x1f9d1, 0x1f9dd] // Person
  ]

  // Kiểm tra từng ký tự trong chuỗi
  for (let i = 0; i < text.length; i++) {
    const codePoint = text.codePointAt(i)

    // Bỏ qua nếu không lấy được code point
    if (!codePoint) continue

    // Kiểm tra xem code point có nằm trong khoảng emoji nào không
    for (const [rangeStart, rangeEnd] of emojiRanges) {
      if (codePoint >= rangeStart && codePoint <= rangeEnd) {
        return true
      }
    }

    // Xử lý cho các ký tự surrogate pairs
    if (codePoint > 0xffff) {
      i++ // Bỏ qua phần surrogate pair thứ hai
    }
  }

  return false
}

export const toBoolean = (value: string | number | boolean): boolean => {
  if (isBoolean(value)) {
    return value
  }

  if (typeof value === 'string') {
    const lowerValue = value.toLowerCase().trim()

    return ['true', '1', 'yes', 'on'].includes(lowerValue)
  }

  if (typeof value === 'number') {
    return value === 1
  }

  return false
}

export const isBase64 = (str?: string): boolean => {
  if (!str || typeof str !== 'string') return false
  // Loại bỏ khoảng trắng đầu/cuối
  const trimmed = str.trim()

  // Độ dài phải chia hết cho 4
  if (trimmed.length % 4 !== 0) return false
  // Regex kiểm tra base64
  const base64Regex = /^(?:[A-Za-z0-9+\/]{4})*(?:[A-Za-z0-9+\/]{2}==|[A-Za-z0-9+\/]{3}=)?$/

  return base64Regex.test(trimmed)
}

export const generatePassword = () => {
  const letters = 'abcdefghijklmnopqrstuvwxyz'
  const letters1 = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
  const number = '0123456789'
  const specialCharacters = '!@#$%^&+='
  const temp: any[] = []

  const randomCharacters = (length: number, characters: string) => {
    for (let i = 0; i < length; i++) temp.push(characters.charAt(Math.floor(Math.random() * characters.length)))
  }

  randomCharacters(3, letters)
  randomCharacters(3, letters1)
  randomCharacters(2, number)
  randomCharacters(2, specialCharacters)

  return temp
    .sort(() => Math.random() - 0.5)
    .toString()
    .replaceAll(',', '')
}
