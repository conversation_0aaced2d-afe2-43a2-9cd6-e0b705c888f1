/**
 * Utility function để mapping auth type thành display text
 * @param authType - Loại xác thực API
 * @returns Display text cho auth type
 */
export const getAuthTypeDisplay = (authType: string): string => {
  switch (authType) {
    case 'noauth':
      return 'No Auth'
    case 'bearer':
      return 'Bearer Token'
    case 'apikey':
      return 'API Key'
    case 'basic':
      return 'Basic Auth'
    default:
      return authType
  }
}
