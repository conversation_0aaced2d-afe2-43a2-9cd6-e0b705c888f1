const OBJECT_TYPE_MAP: Record<string, string> = {
  ORDER: 'Đơn hàng',
  PRODUCT: 'Sản phẩm',
  SERVICE: 'Dịch vụ',
  CUSTOMER: '<PERSON>h<PERSON>ch hàng'
}

const PRODUCT_TYPE_MAP: Record<string, string> = {
  PHYSICAL: 'Hàng hoá vật lý',
  PACKAGE: 'Gói Bundling',
  DIGITAL: 'Sản phẩm kỹ thuật số',
  SERVICE: 'Dịch vụ'
}

export const convertObjectTypes = (objectTypes: string[] = []): string => {
  if (!objectTypes || objectTypes.length === 0) {
    return 'Không có'
  }

  return objectTypes.map(type => OBJECT_TYPE_MAP[type] || type).join(', ')
}

export const convertProductTypes = (productTypes: string[] = []): string => {
  if (!productTypes || productTypes.length === 0) {
    return 'Không có'
  }

  return productTypes.map(type => PRODUCT_TYPE_MAP[type] || type).join(', ')
}

export const convertTags = (tags: string[] = []): string => {
  if (!tags || tags.length === 0) {
    return 'Không có'
  }

  return tags.join(', ')
}

export const generateClassificationFields = (data: {
  tags?: string[]
  productType?: string[]
  ObjectType?: string[]
}) => {
  return [
    {
      label: 'Đối tượng áp dụng',
      content: convertObjectTypes(data.ObjectType),
      hidden: false
    },
    {
      label: 'Nhãn phân loại',
      content: convertTags(data.tags),
      hidden: false
    },
    {
      label: 'Loại sản phẩm',
      content: convertProductTypes(data.productType),
      hidden: false
    }
  ]
}
