import type { Node, Edge } from '@xyflow/react'

import type { TransitionStateTableItem } from '@/types/workflow/transition'
import { calculateNodePositions, calculateGridLayout } from './diagramLayout'

/**
 * Chuyển đổi dữ liệu từ TransitionStateTableItem thành ReactFlow nodes
 * @param stateTableData - Mảng dữ liệu trạng thái từ bảng
 * @param layoutType - Ki<PERSON>u bố cục ('horizontal' | 'even' | 'grid')
 * @param containerWidth - Chiều rộng container
 * @param gridColumns - Số cột cho bố cục lưới
 * @returns Mảng nodes cho ReactFlow
 */
export const convertTableDataToNodes = (
  stateTableData: TransitionStateTableItem[],
  layoutType: 'horizontal' | 'even' | 'grid' = 'horizontal',
  containerWidth: number = 1200,
  gridColumns: number = 3
): Node[] => {
  if (!stateTableData || stateTableData.length === 0) {
    return []
  }

  // Tạo nodes từ dữ liệu bảng
  const nodes: Node[] = stateTableData.map((item, index) => ({
    id: item.key || item.code || `state_${index}`,
    type: 'custom',
    data: {
      label: item.displayName,
      name: item.name,
      icon: item.icon,
      color: item.color,
      stateCode: item.code,
      stateType: item.type,
      description: item.description,
      objectType: item.objectType
    },
    position: { x: 0, y: 0 } // Sẽ được tính toán lại
  }))

  // Tính toán vị trí cho các nodes dựa trên layout type
  if (layoutType === 'grid') {
    return calculateGridLayout(nodes, containerWidth, gridColumns)
  }

  return calculateNodePositions(nodes, containerWidth)
}

/**
 * Chuyển đổi dữ liệu từ TransitionStateTableItem thành ReactFlow edges
 * @param stateTableData - Mảng dữ liệu trạng thái từ bảng
 * @returns Mảng edges cho ReactFlow
 */
export const convertTableDataToEdges = (stateTableData: TransitionStateTableItem[]): Edge[] => {
  if (!stateTableData || stateTableData.length === 0) {
    return []
  }

  const edges: Edge[] = []
  const stateMap = new Map<string, TransitionStateTableItem>()

  // Tạo map để tra cứu nhanh các state
  stateTableData.forEach(item => {
    stateMap.set(item.name, item)
    stateMap.set(item.code, item)
  })

  stateTableData.forEach(currentState => {
    // Xử lý previousState để tạo edges
    if (currentState.previousState && currentState.previousState !== '-') {
      const previousStates = currentState.previousState.split(', ')

      previousStates.forEach(prevStateName => {
        const prevState = stateMap.get(prevStateName.trim())

        if (prevState) {
          const edgeId = `e_${prevState.key || prevState.code}_${currentState.key || currentState.code}`

          // Tạo thông tin chuyển đổi dựa trên triggers
          const transitions: { key: string; label: string }[] = []

          if (currentState.triggers && Array.isArray(currentState.triggers)) {
            currentState.triggers.forEach(trigger => {
              if (trigger && trigger.type) {
                const triggerLabel = getTriggerLabel(trigger.type)

                transitions.push({
                  key: trigger.type.toLowerCase(),
                  label: triggerLabel
                })
              }
            })
          }

          edges.push({
            id: edgeId,
            source: prevState.code,
            target: currentState.code,
            type: 'custom',
            animated: true,
            data: {
              label: transitions.length > 0 ? `${transitions.length} chuyển đổi` : '1 chuyển đổi',
              color: prevState.color || '#2A6AEB',
              transitions: transitions,
              sourceName: prevState.name,
              targetName: currentState.name,
              triggers: currentState.triggers
            }
          })
        }
      })
    }
  })

  return edges
}

/**
 * Lấy nhãn hiển thị cho loại trigger
 * @param triggerType - Loại trigger
 * @returns Nhãn hiển thị
 */
const getTriggerLabel = (triggerType: string): string => {
  const triggerLabels: Record<string, string> = {
    MANUAL: 'Manual',
    API: 'API Call',
    WEBHOOK: 'Webhook',
    SCHEDULE: 'Lịch trình',
    RULE_ENGINE: 'Rule Engine'
  }

  return triggerLabels[triggerType] || triggerType
}

/**
 * Chuyển đổi dữ liệu từ TransitionStateTableItem thành cả nodes và edges
 * @param stateTableData - Mảng dữ liệu trạng thái từ bảng
 * @param layoutType - Kiểu bố cục
 * @param containerWidth - Chiều rộng container
 * @param gridColumns - Số cột cho bố cục lưới
 * @returns Object chứa nodes và edges
 */
export const convertTableDataToDiagram = (
  stateTableData: TransitionStateTableItem[],
  layoutType: 'horizontal' | 'even' | 'grid' = 'horizontal',
  containerWidth: number = 1200,
  gridColumns: number = 3
) => {
  const nodes = convertTableDataToNodes(stateTableData, layoutType, containerWidth, gridColumns)
  const edges = convertTableDataToEdges(stateTableData)

  return { nodes, edges }
}
