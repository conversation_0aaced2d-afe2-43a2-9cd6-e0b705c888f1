import {
  RULES_ENGINE_CONDITIONS,
  OPERAND_CODE,
  SCHEDULE_TYPE_VALUES,
  OPERATOR_CODE,
  TRIGGER_OPTIONS_CREATE,
  STATUS_CONDITION_VALUES
} from '@/constants/workflow/trigger'

import { CATEGORIES_MAP } from '@/constants/workflow/transition'
import type { TableConfigFormData } from '@/types/workflow/transition'
import type { TriggerUpdateRef } from '@/components/workflow/transition-management'

export const renderConditionByTriggerCreate = (trigger: any): string => {
  if (!trigger || !trigger.type) return ''

  // Manual/Webhook: empty
  if (trigger.type === 'MANUAL' || trigger.type === 'WEBHOOK') return ''

  // API: group outputMapping by dest and format values
  if (trigger.type === 'API') {
    const mappings = trigger.api?.outputMapping

    if (Array.isArray(mappings) && mappings.length > 0) {
      const grouped: Record<string, any[]> = {}

      mappings.forEach((m: any) => {
        if (!m?.dest) return
        if (!grouped[m.dest]) grouped[m.dest] = []

        const rawVal = m.value
        const formatted = typeof rawVal === 'string' ? `"${rawVal}"` : rawVal

        grouped[m.dest].push(formatted)
      })

      const parts = Object.entries(grouped).map(([dest, values]) => `"${dest}": [${values.join(', ')}]`)

      return parts.join('; ')
    }

    return ''
  }

  // SCHEDULE: label from SCHEDULE_TYPE_VALUES + date/time segments
  if (trigger.type === 'SCHEDULE') {
    const s = trigger.schedule || {}

    const typeLabel = SCHEDULE_TYPE_VALUES.find(item => item.value === s.type)?.label || s.type || ''
    const start = s.startDate ? `từ ${s.startDate}` : ''
    const end = s.endDate ? ` đến ${s.endDate}` : ''
    const time = s.startTime ? `${s.startTime}${s.endTime ? ` đến ${s.endTime}` : ''}` : ''

    const segments = [typeLabel, [start, end].join(''), time].filter(Boolean)

    return segments.join(' - ')
  }

  // RULE_ENGINE: map operandId -> label via OPERAND_CODE/RULES_ENGINE_CONDITIONS; operator 1 -> '='
  if (trigger.type === 'RULE_ENGINE') {
    const conditions = trigger.ruleEngine?.conditions

    if (!Array.isArray(conditions)) return ''

    const texts: string[] = []

    conditions.forEach((c: any) => {
      const ifconds = Array.isArray(c?.ifconds) ? c.ifconds : []

      ifconds.forEach((cond: any) => {
        const operandId = Number(cond?.operandId ?? cond?.key)
        const operandKey = Object.keys(OPERAND_CODE).find(key => (OPERAND_CODE as any)[key] === operandId)

        const operandLabel =
          RULES_ENGINE_CONDITIONS.find(item => item.value === (operandKey as any))?.label || String(operandId)

        const operator = cond?.operator === 1 ? '=' : (cond?.operator ?? '=')
        const vals: any[] = Array.isArray(cond?.data?.value) ? cond.data.value : []
        const valueText = vals.map(v => (typeof v === 'string' ? `"${v}"` : String(v))).join(', ')

        texts.push(`${operandLabel} ${operator} ${valueText}`)
      })
    })

    return texts.join(', ')
  }

  return ''
}

// Lấy danh sách danh mục dựa trên đối tượng được chọn
export const getCategoriesByObject = (objectType?: string) => {
  if (!objectType) return []

  return CATEGORIES_MAP[objectType] || []
}

// Kiểm tra form có hợp lệ không
export const checkFormValidity = (values: any) => {
  const valid = !!values.configName && !!values.targetObject && !!values.description

  return valid
}

// Kiểm tra form TableConfig có hợp lệ không
export const checkTableConfigFormValidity = (values: Partial<TableConfigFormData>) => {
  // Kiểm tra các trường bắt buộc cơ bản
  const hasBasicFields = !!values.stateName && !!values.stateCode && !!values.stateType

  // Kiểm tra trạng thái tiền nhiệm
  const hasPredecessorStates = Array.isArray(values.predecessorStates) && values.predecessorStates.length > 0

  // Kiểm tra các trường bổ sung dựa trên loại trigger
  let hasRequiredTriggerFields = true

  if (values.triggerType === 'api' && !values.apiEndpoint) {
    hasRequiredTriggerFields = false
  } else if (values.triggerType === 'webhook' && !values.webhookUrl) {
    hasRequiredTriggerFields = false
  } else if (values.triggerType === 'schedule' && (!values.scheduleType || !values.scheduleTime)) {
    hasRequiredTriggerFields = false
  } else if (values.triggerType === 'rule' && !values.ruleCondition) {
    hasRequiredTriggerFields = false
  }

  return hasBasicFields && hasPredecessorStates && hasRequiredTriggerFields
}

/**
 * Hàm chuyển đổi condition từ ConditionGroup[] sang format mới cho rule engine
 * @param conditionGroups - Mảng các condition groups
 * @returns Formatted conditions theo cấu trúc mới
 */
export const formatConditionsForRuleEngine = (conditionGroups: any[]) => {
  if (!conditionGroups || conditionGroups.length === 0) {
    return []
  }

  let conditionId = 1

  return conditionGroups
    .map((group, groupIndex) => {
      const currentConditionId = conditionId++
      const key = groupIndex === 0 ? '1' : Date.now().toString() + Math.random().toString(36).substr(2, 9)

      const ifconds = group.conditions
        .filter(
          (condition: any) =>
            condition.field && condition.value !== '' && condition.value !== null && condition.value !== undefined
        )
        .map((condition: any, condIndex: number) => {
          const currentIfCondId = conditionId++

          const ifCondKey =
            condIndex === 0 && groupIndex === 0 ? '1' : Date.now().toString() + Math.random().toString(36).substr(2, 9)

          // Lấy operandId từ OPERAND_CODE
          const operandId = OPERAND_CODE[condition.field as keyof typeof OPERAND_CODE]

          // Chuyển đổi operator: 0 -> EQUAL(1), 1 -> NOT_EQUAL(2)
          const operatorCode = condition.operator === 1 ? OPERATOR_CODE.EQUAL : OPERATOR_CODE.NOT_EQUAL

          // Xử lý value theo định dạng mới
          let valueData: any = null

          if (condition.value !== null && condition.value !== undefined && condition.value !== '') {
            // Xử lý theo từng loại trường
            if (condition.field === 'ID') {
              // ID là mảng number
              if (Array.isArray(condition.value)) {
                valueData = condition.value.map(Number)
              } else {
                valueData = [Number(condition.value)]
              }
            } else if (condition.field === 'SERVICE_TYPE') {
              // SERVICE_TYPE là number
              if (Array.isArray(condition.value)) {
                valueData = condition.value.map(Number)
              } else {
                valueData = Number(condition.value)
              }
            } else {
              // Các trường còn lại là string: SUPPORT_SETUP_TYPE, SERVICE_OWNER, INTEGRATE_BSS, SUPPORT_SETUP_TIME
              if (Array.isArray(condition.value)) {
                valueData = condition.value.map(String)
              } else {
                valueData = [String(condition.value)]
              }
            }
          }

          return {
            id: currentIfCondId,
            key: ifCondKey,
            operandId: operandId,
            operator: operatorCode,
            data: {
              value: valueData
            }
          }
        })

      return {
        id: currentConditionId,
        key: key,
        ifconds: ifconds
      }
    })
    .filter(group => group.ifconds && group.ifconds.length > 0) // Chỉ giữ lại những group có ifconds
}

/**
 * Lấy dữ liệu trigger từ các component TriggerUpdate
 * @param selectedPreviousStates - Danh sách trạng thái tiền nhiệm
 * @param singleTriggerRef - Ref của TriggerUpdate đơn lẻ
 * @param tabTriggerRefs - Refs của TriggerUpdate trong tabs
 * @returns Object chứa dữ liệu trigger của từng state
 */
export const getTriggerData = (
  selectedPreviousStates: { id: string; name: string }[],
  singleTriggerRef: React.RefObject<TriggerUpdateRef>,
  tabTriggerRefs: React.MutableRefObject<Record<string, any>>
): Record<string, any> => {
  const allTriggerData: Record<string, any> = {}

  if (selectedPreviousStates.length <= 1) {
    // Lấy từ single trigger ref
    if (singleTriggerRef.current) {
      const triggerComponent = singleTriggerRef.current as any
      const stateId = selectedPreviousStates[0]?.id || 'default'

      // Kiểm tra và lấy dữ liệu từ ref một cách an toàn
      allTriggerData[stateId] = {
        ruleEngineConditions: Array.isArray(triggerComponent.ruleEngineConditions)
          ? triggerComponent.ruleEngineConditions
          : [],
        scheduleConditions: Array.isArray(triggerComponent.scheduleConditions)
          ? triggerComponent.scheduleConditions
          : [],
        selectedTriggers: Array.isArray(triggerComponent.selectedTriggers) ? triggerComponent.selectedTriggers : [],
        webhookData: Array.isArray(triggerComponent.webhookData) ? triggerComponent.webhookData : [],
        requestMappingData: Array.isArray(triggerComponent.requestMappingData)
          ? triggerComponent.requestMappingData
          : [],
        responseMappingData: Array.isArray(triggerComponent.responseMappingData)
          ? triggerComponent.responseMappingData
          : []
      }
    }
  } else {
    // Lấy từ tab trigger refs
    Object.keys(tabTriggerRefs.current).forEach(stateId => {
      const ref = tabTriggerRefs.current[stateId]

      if (ref && ref.current) {
        const triggerComponent = ref.current as any

        // Kiểm tra và lấy dữ liệu từ ref một cách an toàn
        allTriggerData[stateId] = {
          ruleEngineConditions: Array.isArray(triggerComponent.ruleEngineConditions)
            ? triggerComponent.ruleEngineConditions
            : [],
          scheduleConditions: Array.isArray(triggerComponent.scheduleConditions)
            ? triggerComponent.scheduleConditions
            : [],
          selectedTriggers: Array.isArray(triggerComponent.selectedTriggers) ? triggerComponent.selectedTriggers : [],
          webhookData: Array.isArray(triggerComponent.webhookData) ? triggerComponent.webhookData : [],
          requestMappingData: Array.isArray(triggerComponent.requestMappingData)
            ? triggerComponent.requestMappingData
            : [],
          responseMappingData: Array.isArray(triggerComponent.responseMappingData)
            ? triggerComponent.responseMappingData
            : []
        }
      }
    })
  }

  return allTriggerData
}

/**
 * Xử lý trigger loại SCHEDULE
 */
export const processScheduleTrigger = (stateData: any, scheduleSyncValues: any, stateId: string): any => {
  // Lấy điều kiện nếu có
  const formattedScheduleConditions =
    stateData.scheduleConditions?.length > 0 ? formatConditionsForRuleEngine(stateData.scheduleConditions) : null

  // Lấy thông tin lịch hẹn
  const scheduleSync =
    (scheduleSyncValues as Record<string, any>)?.[stateId]?.schedule || (scheduleSyncValues as any)?.schedule

  return {
    type: TRIGGER_OPTIONS_CREATE.SCHEDULE,
    schedule: {
      conditions: formattedScheduleConditions,
      type: scheduleSync?.type || 'ONCE',
      intervalType: scheduleSync?.intervalType || null,
      startTime: scheduleSync?.startTime || '17:30',
      startDate: scheduleSync?.startDate || null,
      endDate: scheduleSync?.endDate || null,
      dayOfWeek: scheduleSync?.dayOfWeek || null,
      dayOfMonth: scheduleSync?.dayOfMonth
        ? Array.isArray(scheduleSync.dayOfMonth)
          ? scheduleSync.dayOfMonth
          : [Number(scheduleSync.dayOfMonth)]
        : null,
      weekOfMonth: scheduleSync?.weekOfMonth || null
    }
  }
}

/**
 * Xử lý trigger loại RULE_ENGINE
 */
export const processRuleEngineTrigger = (stateData: any): any => {
  const formattedRuleEngineConditions = formatConditionsForRuleEngine(stateData.ruleEngineConditions)

  return {
    type: TRIGGER_OPTIONS_CREATE.RULE_ENGINE,
    ruleEngine: {
      conditions: formattedRuleEngineConditions
    }
  }
}

/**
 * Xử lý trigger loại MANUAL
 */
export const processManualTrigger = (formValues: any, stateId: string): any => {
  const manualData = (formValues as any)[stateId] || (formValues as any)['default']

  return {
    type: TRIGGER_OPTIONS_CREATE.MANUAL,
    manual: {
      agentTypes: [manualData?.actor || null],
      roles: [manualData?.role || null]
    }
  }
}

/**
 * Xử lý trigger loại WEBHOOK
 */
export const processWebhookTrigger = (stateData: any): any => {
  // Lấy dữ liệu webhook mapping từ stateData
  const webhookData = stateData.webhookData || []

  // Tạo mảng chứa các giá trị status
  const statusValues: string[] = []

  // Lặp qua tất cả các item trong webhookData để tìm các trường status
  webhookData.forEach((item: any) => {
    if (item.field === 'status' && item.value) {
      // item.value có thể là string hoặc array -> chuẩn hoá thành mảng string flatted
      const values = Array.isArray(item.value) ? item.value : [item.value]

      values.forEach((v: string) => {
        const statusOption = STATUS_CONDITION_VALUES.find((option: any) => option.value === v)

        statusValues.push(statusOption ? statusOption.label : v)
      })
    }
  })

  return {
    type: TRIGGER_OPTIONS_CREATE.WEBHOOK,
    webhook: {
      action: 'aliqua',
      status: statusValues
    }
  }
}

/**
 * Xử lý trigger loại API_CALL
 */
export const processApiTrigger = (stateData: any, formValues: any, stateId: string, scheduleSyncValues?: any): any => {
  // Lấy dữ liệu từ form và stateData
  const apiEndpointName = `${stateId || 'default'}_trigger_endpoint`
  const apiEndpointData = (formValues as any)[apiEndpointName] || {}

  // Lấy params và path variables từ formValues
  const pathVariables = formValues.pathVariables || []
  const params = formValues.params || []

  // Tạo mảng variables từ path variables và params
  const variables: { key: string; value: string }[] = []

  // Thêm path variables vào mảng variables
  if (pathVariables && pathVariables.length > 0) {
    pathVariables.forEach((variable: any) => {
      if (variable && variable.key) {
        variables.push({
          key: variable.key,
          value: variable.value || ''
        })
      }
    })
  }

  // Thêm params vào mảng variables
  if (params && params.length > 0) {
    params.forEach((param: any) => {
      if (param && param.key) {
        variables.push({
          key: param.key,
          value: param.value || ''
        })
      }
    })
  }

  // Tạo mảng headers từ apiEndpointData
  const headers = []

  // Thêm header mặc định
  headers.push({
    key: 'x-api-key',
    value: 'DXONESME#KhhYtpipnKA3qshGHcdprJqK6eIwQa3pMAQDwQIK'
  })

  // Tạo auth object dựa trên apiKeyValue
  const createAuthConfig = () => {
    const baseAuth = {
      type: apiEndpointData.authMethod || 'bearer'
    }

    if (apiEndpointData.authMethod === 'api_key') {
      // Nếu là 'apikey' thì chỉ truyền apikey
      return {
        ...baseAuth,
        apikey: [
          {
            type: apiEndpointData.keyPlacement,
            key: apiEndpointData.keyValue,
            value: apiEndpointData.apiKeyValue
          }
        ]
      }
    } else {
      // Nếu không phải 'apikey' thì chỉ truyền bearer
      return {
        ...baseAuth,
        bearer: {
          key: 'Authorization',
          value: apiEndpointData.tokenValue
        }
      }
    }
  }

  // Lấy dữ liệu mapping từ stateData
  const requestMappingData = stateData.requestMappingData || []
  const responseMappingData = stateData.responseMappingData || []

  // Tạo inputMapping từ request data
  const inputMapping = requestMappingData
    .filter((item: any) => item.defaultValue && item.defaultValue !== 'Chọn tham số')
    .map((item: any) => ({
      dest: item.field, // Trường đối tác
      source: item.defaultValue, // Tham số đầu vào
      isRequired: item.required, // Bắt buộc
      type: item.type // Kiểu dữ liệu
    }))

  // Tạo outputMapping từ response data
  const outputMapping = responseMappingData
    .filter((item: any) => item.targetField && item.value)
    .map((item: any) => ({
      source: item.targetField, // Trường đối tác
      dest: item.sourceField, // Trường nguồn
      value: item.value // Giá trị
    }))

  // Tạo constraints từ outputMapping
  const constraints = outputMapping
    .filter((item: any) => item.source && item.value)
    .map((item: any) => ({
      field: item.source,
      values: [item.value]
    }))

  return {
    type: TRIGGER_OPTIONS_CREATE.API,
    api: {
      constraints: constraints.length > 0 ? constraints : null,
      apiConfig: {
        method: apiEndpointData.connectionMethod || 'GET',
        headers: headers,
        url: {
          raw: apiEndpointData.rawUrl,
          variables: variables
        },
        auth: createAuthConfig(),
        securities: {
          hmac: {
            field: 'SECURE_CODE',
            participants: ['code', 'data'],
            encode: 'SHA256'
          }
        }
      },
      outputMapping: outputMapping.length > 0 ? outputMapping : null,
      inputMapping: inputMapping.length > 0 ? inputMapping : null
    },
    schedule: scheduleSyncValues.apiSchedule
      ? {
          ...scheduleSyncValues.apiSchedule,
          dayOfMonth: scheduleSyncValues.apiSchedule.dayOfMonth
            ? Array.isArray(scheduleSyncValues.apiSchedule.dayOfMonth)
              ? scheduleSyncValues.apiSchedule.dayOfMonth
              : [Number(scheduleSyncValues.apiSchedule.dayOfMonth)]
            : null
        }
      : null
  }
}

/**
 * Tạo triggers cho một trạng thái cụ thể
 * @param stateData - Dữ liệu trigger của state
 * @param formValues - Giá trị từ form
 * @param scheduleSyncValues - Giá trị từ ScheduleSync
 * @param stateId - ID của state
 * @returns Mảng các triggers
 */
export const createTriggersForState = (
  stateData: any,
  formValues: any,
  scheduleSyncValues: any,
  stateId: string
): any[] => {
  const triggers: any[] = []

  // Kiểm tra xem stateData có đúng định dạng không
  if (!stateData || !stateData.selectedTriggers || !Array.isArray(stateData.selectedTriggers)) {
    return triggers
  }

  // Xử lý các loại trigger
  stateData.selectedTriggers.forEach((triggerType: string) => {
    switch (triggerType) {
      case TRIGGER_OPTIONS_CREATE.SCHEDULE:
        triggers.push(processScheduleTrigger(stateData, scheduleSyncValues, stateId))
        break

      case TRIGGER_OPTIONS_CREATE.RULE_ENGINE:
        if (stateData.ruleEngineConditions?.length > 0) {
          triggers.push(processRuleEngineTrigger(stateData))
        }

        break

      case TRIGGER_OPTIONS_CREATE.MANUAL:
        triggers.push(processManualTrigger(formValues, stateId))
        break

      case TRIGGER_OPTIONS_CREATE.API:
        triggers.push(processApiTrigger(stateData, formValues, stateId, scheduleSyncValues))
        break

      case TRIGGER_OPTIONS_CREATE.WEBHOOK:
        const webhookTrigger = processWebhookTrigger(stateData)

        triggers.push(webhookTrigger)
        break

      default:
        break
    }
  })

  return triggers
}

/**
 * Hàm chính để mapping dữ liệu cho API
 * @param formValues - Giá trị từ form
 * @param scheduleSyncValues - Giá trị từ ScheduleSync components
 * @param selectedPreviousStates - Danh sách trạng thái tiền nhiệm đã chọn
 * @param singleTriggerRef - Ref của TriggerUpdate đơn lẻ
 * @param tabTriggerRefs - Refs của TriggerUpdate trong tabs
 * @returns Dữ liệu đã được mapping theo format API
 */
export const mapDataConfigCreate = (
  formValues: any,
  scheduleSyncValues: any,
  selectedPreviousStates: { id: string; name: string }[],
  singleTriggerRef: React.RefObject<TriggerUpdateRef>,
  tabTriggerRefs: React.MutableRefObject<Record<string, any>>
) => {
  // Helper: build postActions from form values (notifications)
  const buildPostActions = (fv: any) => {
    try {
      const notifications = fv?.notifications || {}
      const selectedTypes: { email?: boolean; notification?: boolean } = notifications?.selectedTypes || {}

      const results: any[] = []

      // Email notification
      if (selectedTypes.email) {
        const emailCfg = notifications?.email || {}
        const contentType = emailCfg?.contentType === 'CUSTOM' ? 'CUSTOM' : 'TEMPLATE'

        const emailAction = {
          postActionType: 'NOTIFICATION',
          webhook: null,
          notifications: {
            notificationType: 'EMAIL',
            contentType,
            customTitle: contentType === 'CUSTOM' ? emailCfg?.customTitle || null : null,
            customContent: contentType === 'CUSTOM' ? emailCfg?.customContent || null : null,
            templateCode: contentType === 'TEMPLATE' ? emailCfg?.templateCode || null : null
          }
        }

        // Basic validation: ensure required fields exist based on contentType
        const isValidEmail =
          (contentType === 'TEMPLATE' && !!emailAction.notifications.templateCode) ||
          (contentType === 'CUSTOM' &&
            !!emailAction.notifications.customTitle &&
            !!emailAction.notifications.customContent)

        if (isValidEmail) results.push(emailAction)
      }

      // In-app notification
      if (selectedTypes.notification) {
        const appCfg = notifications?.app || {}

        const appAction = {
          postActionType: 'NOTIFICATION',
          webhook: null,
          notifications: {
            notificationType: 'NOTIFICATION',
            contentType: 'CUSTOM',
            customTitle: appCfg?.title || null,
            customContent: appCfg?.content || null,
            templateCode: null
          }
        }

        const isValidApp = !!appAction.notifications.customTitle && !!appAction.notifications.customContent

        if (isValidApp) results.push(appAction)
      }

      // WEBHOOK action: xây theo logic giống api_call
      if (fv?.actionType === 'webhook') {
        const actionEndpoint = fv?.action_endpoint || {}

        // Lấy params & path variables: dùng chung params/pathVariables hiện tại của form
        const pathVariables = Array.isArray(fv?.pathVariables) ? fv.pathVariables : []
        const params = Array.isArray(fv?.params) ? fv.params : []

        const variables: { key: string; value: string }[] = []

        pathVariables.forEach((variable: any) => {
          if (variable && variable.key) {
            variables.push({ key: variable.key, value: variable.value || '' })
          }
        })

        params.forEach((param: any) => {
          if (param && param.key) {
            variables.push({ key: param.key, value: param.value || '' })
          }
        })

        const headers = [
          {
            key: 'x-api-key',
            value: 'DXONESME#KhhYtpipnKA3qshGHcdprJqK6eIwQa3pMAQDwQIK'
          }
        ]

        const buildAuth = () => {
          const method = actionEndpoint?.authMethod || 'bearer_token'

          if (method === 'api_key') {
            return {
              type: 'api_key',
              apikey: [
                {
                  type: actionEndpoint?.keyPlacement,
                  key: actionEndpoint?.keyValue,
                  value: actionEndpoint?.apiKeyValue
                }
              ]
            }
          }

          if (method === 'no_auth') {
            return { type: 'no_auth' }
          }

          return {
            type: 'bearer_token',
            bearer: {
              key: 'Authorization',
              value: actionEndpoint?.tokenValue
            }
          }
        }

        const requestMappingData = Array.isArray(fv?.actionRequestMappingData) ? fv.actionRequestMappingData : []
        const responseMappingData = Array.isArray(fv?.actionResponseMappingData) ? fv.actionResponseMappingData : []

        const inputMapping = requestMappingData
          .filter((item: any) => item.defaultValue && item.defaultValue !== 'Chọn tham số')
          .map((item: any) => ({
            dest: item.field,
            source: item.defaultValue,
            isRequired: item.required,
            type: item.type
          }))

        const outputMapping = responseMappingData
          .filter((item: any) => item.targetField && item.value)
          .map((item: any) => ({
            source: item.targetField,
            dest: item.sourceField,
            value: item.value
          }))

        const webhookAction = {
          postActionType: 'WEBHOOK',
          notifications: null,
          webhook: {
            apiConfig: {
              method: actionEndpoint?.connectionMethod || 'GET',
              headers,
              url: {
                raw: actionEndpoint?.rawUrl,
                variables
              },
              auth: buildAuth(),
              securities: {
                hmac: {
                  field: 'SECURE_CODE',
                  participants: ['code', 'data'],
                  encode: 'SHA256'
                }
              }
            },
            inputMapping: inputMapping.length > 0 ? inputMapping : null,
            outputMapping: outputMapping.length > 0 ? outputMapping : null
          }
        }

        // Validate cơ bản: cần có rawUrl
        if (webhookAction.webhook.apiConfig.url.raw) {
          results.push(webhookAction)
        }
      }

      return results.length > 0 ? results : null
    } catch {
      return null
    }
  }

  // Lấy dữ liệu trigger từ các component
  const allTriggerData = getTriggerData(selectedPreviousStates, singleTriggerRef, tabTriggerRefs)

  // Tạo mảng các triggers cho từng trạng thái tiền nhiệm
  const allTriggers: Record<string, any[]> = {}

  // Xử lý từng previous state (trạng thái tiền nhiệm)
  selectedPreviousStates.forEach(previousState => {
    const stateId = previousState.id
    const stateData = allTriggerData[stateId] || {}

    // Tạo triggers cho state này
    allTriggers[stateId] = createTriggersForState(stateData, formValues, scheduleSyncValues, stateId)
  })

  // Tạo cấu trúc dữ liệu cuối cùng theo format API
  const mappedData = {
    items: [
      {
        stateId: typeof formValues.stateId === 'string' ? null : formValues.stateId,
        status: formValues.status,
        name: formValues.stateName,
        description: formValues.description,
        colorCode: formValues.colorCode,
        displayName: formValues.displayName,
        code: formValues.stateCode,
        typeId: formValues.typeId,
        applyAll: formValues.applyAll,
        icon: formValues.icon,
        // ID trạng thái đích
        triggerConditions: selectedPreviousStates.map(previousState => {
          return {
            preStateId: parseInt(previousState.id),
            preStateName: previousState.name,
            triggers: allTriggers[previousState.id] || []
          }
        }),
        postActions: buildPostActions(formValues)
      }
    ]
  }

  return mappedData
}

/**
 * Chuẩn hóa dữ liệu API mappedData.items -> TransitionStateTableItem[] (dataTable)
 */
export const mapApiToDataTable = (mappedData: any, formValues: any): any[] => {
  try {
    const items = Array.isArray(mappedData?.items) ? mappedData.items : []

    const rows = items.map((item: any, idx: number) => {
      const triggerConds = Array.isArray(item?.triggerConditions) ? item.triggerConditions : []

      // Tổng hợp các trigger objects từ tất cả các điều kiện (giữ nguyên format API)
      const allTriggers: any[] = triggerConds
        .flatMap((tc: any) => (Array.isArray(tc?.triggers) ? tc.triggers : []))
        .filter((t: any) => t && t.type) // Chỉ lấy trigger có type

      // Vai trò cập nhật (từ trigger MANUAL)
      const manualRoles: string[] = allTriggers
        .filter((t: any) => t?.type === 'MANUAL')
        .flatMap((t: any) => (t?.manual?.roles ? t.manual.roles : []))
        .filter((r: any) => typeof r === 'string' && r.length > 0)

      const uniqueRoles = Array.from(new Set(manualRoles))

      const ROLE_LABEL_MAP: Record<string, string> = {
        PARTNER: 'Đối tác',
        ADMIN: 'Admin'
      }

      const labeledRoles = uniqueRoles.map(role => ROLE_LABEL_MAP[role] || role)
      const updateRole = labeledRoles.length > 0 ? labeledRoles.join(', ') : ''

      // Hành động hệ thống (từ postActions)
      const postActions = Array.isArray(item?.postActions) ? item.postActions : []

      const systemActions = postActions
        .map((action: any) => {
          if (action?.postActionType === 'NOTIFICATION') {
            return action?.notifications?.notificationType === 'EMAIL' ? 'Gửi email' : 'Thông báo'
          }

          return action?.postActionType || ''
        })
        .filter(Boolean)

      const systemAction = systemActions.length > 0 ? systemActions.join(', ') : ''

      // previousState: join các preStateName duy nhất
      const previousStates = Array.from(
        new Set(
          triggerConds.map((tc: any) => tc?.preStateName).filter((v: any) => typeof v === 'string' && v.length > 0)
        )
      )

      // Lấy preStateId từ triggerCondition đầu tiên nếu có
      const preStateId = triggerConds.length > 0 ? triggerConds[0]?.preStateId || '' : ''

      const previousStateText = previousStates.length > 0 ? previousStates.join(', ') : '-'

      // Tạo condition text từ triggers
      const conditionText =
        allTriggers.length > 0
          ? allTriggers
              .map((trigger: any) => {
                switch (trigger.type) {
                  case 'MANUAL':
                    return 'Thủ công'
                  case 'API':
                    return 'API Call'
                  case 'WEBHOOK':
                    return 'Webhook'
                  case 'SCHEDULE':
                    return 'Lịch trình'
                  case 'RULE_ENGINE':
                    return 'Rule Engine'
                  default:
                    return trigger.type
                }
              })
              .join(', ')
          : ''

      return {
        key: item?.code || item?.stateCode || `${Date.now()}_${idx}`,
        index: 0,
        name: item?.name,
        displayName: item?.displayName,
        code: item?.code,
        type: formValues?.stateType || 'workflow_state',
        previousState: previousStateText,
        previousStateId: preStateId,
        triggers: allTriggers,
        condition: conditionText,
        color: item?.colorCode || '#2A6AEB',
        description: item?.description || '',
        objectType: formValues?.targetObject || '',
        icon: item?.icon,
        updateRole,
        systemAction,
        stateId: item?.stateId,
        status: formValues?.status,
        typeId: item?.typeId,
        applyAll: item?.applyAll,
        postActions: item?.postActions,
        triggerConditions: item?.triggerConditions
      }
    })

    return rows
  } catch (e) {
    console.error('mapApiToDataTable error:', e)

    return []
  }
}

// Utility functions for URL formatting
export const formatUrlToRaw = (url: string): string => {
  try {
    // Tách URL thành base URL và query string
    const [baseUrl, queryString] = url.split('?')

    if (!queryString) {
      return url // Nếu không có query parameters, trả về URL gốc
    }

    // Parse query parameters và convert thành placeholder format
    const params = queryString.split('&')

    const rawParams = params.map(param => {
      const [key] = param.split('=')

      // Convert thành format :key thay vì key=value
      return `${key}=:${key}`
    })

    // Ghép lại thành URL raw với placeholder
    return `${baseUrl}?${rawParams.join('&')}`
  } catch (error) {
    console.error('Error formatting URL:', error)

    return url
  }
}

export const buildRawUrlWithPlaceholders = (
  baseUrl: string,
  params: Array<{ key: string; value: string }> = []
): string => {
  try {
    // Lấy base URL (phần trước dấu ?)
    const cleanBaseUrl = baseUrl.split('?')[0]

    if (params.length === 0) {
      return cleanBaseUrl
    }

    // Tạo query string với placeholder format
    const placeholderParams = params
      .filter(param => param.key) // Chỉ lấy những param có key
      .map(param => `${param.key}=:${param.key}`)

    return placeholderParams.length > 0 ? `${cleanBaseUrl}?${placeholderParams.join('&')}` : cleanBaseUrl
  } catch (error) {
    console.error('Error building raw URL:', error)

    return baseUrl
  }
}

// Hàm tạo URL raw từ base URL, params và path variables
export const generateRawUrl = (baseUrl: string, currentParams: Array<{ key: string; value: string }>) => {
  try {
    // Bắt đầu với base URL
    let processedUrl = baseUrl

    // Nếu có params, tạo URL với placeholder format
    if (currentParams.length > 0) {
      processedUrl = buildRawUrlWithPlaceholders(processedUrl, currentParams)
    } else {
      // Nếu không có params mới nhưng URL gốc có query string, format nó
      if (processedUrl.includes('?')) {
        processedUrl = formatUrlToRaw(processedUrl)
      }
    }

    return processedUrl
  } catch (error) {
    console.error('Error generating raw URL:', error)

    return baseUrl
  }
}
