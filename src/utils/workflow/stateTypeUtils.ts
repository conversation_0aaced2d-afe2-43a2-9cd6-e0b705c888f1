/**
 * <PERSON><PERSON><PERSON> hàm tiện ích cho quản lý loại trạng thái
 */

// Tái sử dụng formatDate từ stateUtils

/**
 * Tạo mã loại trạng thái ngẫu nhiên theo định dạng LTTxxxxxx
 */
export const generateStateTypeCode = (): string => {
  // Tạo 6 chữ số ngẫu nhiên
  const randomDigits = Math.floor(Math.random() * 1000000)
    .toString()
    .padStart(6, '0')

  return `LTT${randomDigits}`
}

/**
 * Kiểm tra tên loại trạng thái đã tồn tại
 */
export const isStateTypeNameExists = (
  stateTypeName: string,
  existingStateTypes: Array<{ id: string; stateTypeName: string }>,
  currentId?: string
): boolean => {
  return existingStateTypes.some(
    stateType =>
      stateType.stateTypeName.toLowerCase() === stateTypeName.toLowerCase() &&
      (currentId ? stateType.id !== currentId : true)
  )
}

/**
 * Kiểm tra loại trạng thái có hợp lệ
 */
export const validateStateType = (stateType: {
  stateTypeName: string
  stateTypeCode: string
}): { isValid: boolean; errors: Record<string, string> } => {
  const errors: Record<string, string> = {}

  if (!stateType.stateTypeName) {
    errors.stateTypeName = 'Tên loại trạng thái không được bỏ trống'
  } else if (stateType.stateTypeName.length > 250) {
    errors.stateTypeName = 'Tên loại trạng thái không được vượt quá 250 ký tự'
  }

  if (!stateType.stateTypeCode) {
    errors.stateTypeCode = 'Mã loại trạng thái không được bỏ trống'
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  }
}
