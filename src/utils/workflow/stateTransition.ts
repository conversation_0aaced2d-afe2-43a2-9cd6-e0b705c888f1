import type {
  IStateTransitionConfigResponse,
  IStateTransitionTableResponse,
  ITrigger,
  IPreState
} from '@/types/workflow/stateTransition'
import { STATE_TYPE_MAP } from '@/constants/workflow/stateTransition'

export const getStateType = (stateTypeName?: string): string => {
  return STATE_TYPE_MAP[stateTypeName || ''] || 'Pending'
}

// Hàm lấy các trạng thái tiền nhiệm
export const getPreviousStates = (preStates?: IPreState[]): string => {
  if (!preStates || preStates.length === 0) return '-'

  return preStates.map(state => state.stateName).join(', ')
}

// Cập nhật function getConditions để xử lý trigger object đơn
export const getConditions = (preStates?: IPreState[]): string => {
  if (!preStates || preStates.length === 0) return '-'

  // <PERSON><PERSON><PERSON> t<PERSON><PERSON> cả conditions từ ruleEngine triggers
  const allConditions: Array<{ key: string }> = []

  preStates.forEach(preState => {
    if (preState.triggers) {
      preState.triggers.forEach(trigger => {
        if (trigger.type === 'RULE_ENGINE' && trigger.ruleEngine?.conditions) {
          allConditions.push(...trigger.ruleEngine.conditions)
        }
      })
    }
  })

  if (allConditions.length === 0) return '-'

  return allConditions.map(condition => condition.key).join(' AND ') || '-'
}

export const transformApiDataToTableFormat = (
  apiData: IStateTransitionConfigResponse
): IStateTransitionTableResponse => {
  const result: IStateTransitionConfigResponse = []

  if (!Array.isArray(apiData)) return []

  // Nếu có nhiều trạng thái tiền nhiệm thì tách ra thành nhiều item (item chuyển đổi = 1 record trong bảng)
  apiData.forEach(item => {
    // nếu không có conditionDetails thì push nguyên
    if (!item.conditionDetails || item.conditionDetails.length === 0) {
      result.push(item)
    } else {
      item.conditionDetails.forEach(condition => {
        result.push({
          ...item,
          conditionDetails: [condition] // chỉ giữ 1 condition
        })
      })
    }
  })

  return result.map((item, index) => {
    const conditionDetail = item.conditionDetails?.[0]

    return {
      key: item.stateCode,
      index: index + 1,
      name: item.stateName || '',
      displayName: item.stateDisplayName || '',
      code: item.stateCode || '',
      type: getStateType(conditionDetail?.stateTypeName),
      icon: item?.stateIcon || '',
      previousState: getPreviousStates(conditionDetail?.preStates),
      objectType: item.objectType || '',
      description: item?.description || '',
      postActions: conditionDetail?.postActions || [],
      triggers: conditionDetail?.preStates?.[0]?.triggers,
      condition: getConditions(conditionDetail?.preStates),
      color: item.stateColorCode
    }
  })
}

// Cập nhật utility function để mapping trigger data từ API
export const mapTriggerData = (triggers?: ITrigger | ITrigger[]) => {
  const triggerMap = {
    manual: {},
    apiCall: {},
    apiSchedule: {},
    webhook: {},
    schedule: {},
    condition: {}
  }

  if (!triggers) {
    return triggerMap
  }

  // Xử lý triggers như mảng
  const triggerArray = Array.isArray(triggers) ? triggers : [triggers]

  // Lặp qua từng trigger trong mảng
  triggerArray.forEach(trigger => {
    if (!trigger || !trigger.type) return

    switch (trigger.type) {
      case 'MANUAL':
        // Merge với dữ liệu manual hiện có nếu có
        const existingManual = triggerMap.manual as any

        triggerMap.manual = {
          agentTypes: [...(existingManual.agentTypes || []), ...(trigger.manual?.agentTypes || [])],
          roles: [...(existingManual.roles || []), ...(trigger.manual?.roles || [])]
        }
        break

      case 'API': {
        const apiConfig = trigger.api?.apiConfig || {}
        const headersArray = apiConfig.headers || []
        const headersObj: Record<string, string> = {}

        headersArray.forEach((h: any) => {
          if (h?.key) headersObj[h.key] = h.value
        })

        triggerMap.apiCall = {
          url: apiConfig.url?.raw || '',
          method: apiConfig.method || 'GET',
          headers: headersObj,
          params: trigger.api?.inputMapping || [],
          authMethod: apiConfig.auth?.type || '',
          tokenValue: apiConfig.auth?.bearer?.value || apiConfig.auth?.apikey?.value || '',
          inputMapping: trigger.api?.inputMapping || [],
          outputMapping: trigger.api?.outputMapping || []
        }

        // Map schedule if exists inside API trigger
        if (trigger.schedule) {
          triggerMap.apiSchedule = {
            type: trigger.schedule?.type || '', //vd: 'ONCE', 'DAILY', 'WEEKLY', 'MONTHLY'
            intervalType: trigger.schedule?.intervalType || '', //vd: 'EVERY_15_MIN', 'EVERY_30_MIN', etc.
            startTime: trigger.schedule?.startTime || '',
            endTime: trigger.schedule?.endTime || '',
            startDate: trigger.schedule?.startDate || '',
            endDate: trigger.schedule?.endDate || '',
            dayOfWeek: trigger.schedule?.dayOfWeek || [], // vd: ['MON', 'SAT']
            dayOfMonth: trigger.schedule?.dayOfMonth || [], // vd: [1, 2, 3]
            weekOfMonth: trigger.schedule?.weekOfMonth || [], // vd: [1]
            frequency: trigger.schedule?.frequency || '',
            interval: trigger.schedule?.interval || 1
          }
        }

        break
      }

      case 'WEBHOOK':
        triggerMap.webhook = {
          url: trigger.webhook?.url || '',
          payload: trigger.webhook?.payload || {}
        }
        break
      case 'SCHEDULE':
        triggerMap.schedule = {
          type: trigger.schedule?.type || '', //vd: 'ONCE', 'DAILY', 'WEEKLY', 'MONTHLY'
          intervalType: trigger.schedule?.intervalType || '', //vd: 'EVERY_15_MIN', 'EVERY_30_MIN', etc.
          startTime: trigger.schedule?.startTime || '',
          endTime: trigger.schedule?.endTime || '',
          startDate: trigger.schedule?.startDate || '',
          endDate: trigger.schedule?.endDate || '',
          dayOfWeek: trigger.schedule?.dayOfWeek || [], // vd: ['MON', 'SAT']
          dayOfMonth: trigger.schedule?.dayOfMonth || [], // vd: [1, 2, 3]
          weekOfMonth: trigger.schedule?.weekOfMonth || [], // vd: [1]
          frequency: trigger.schedule?.frequency || '',
          interval: trigger.schedule?.interval || 1,
          conditions: trigger.schedule?.conditions || [] // Điều kiện
        }
        break
      case 'RULE_ENGINE':
        // Merge với dữ liệu condition hiện có nếu có
        const existingCondition = triggerMap.condition as any

        triggerMap.condition = {
          conditions: [...(existingCondition.conditions || []), ...(trigger.ruleEngine?.conditions || [])]
        }
        break
    }
  })

  return triggerMap
}

// Function để format agent types và roles
export const formatAgentTypesAndRoles = (manual: { agentTypes: string[]; roles: string[] }) => {
  const agentTypeMap: { [key: string]: string } = {
    ADMIN: 'Quản trị viên',
    USER: 'Người dùng',
    SYSTEM: 'Hệ thống'
  }

  const roleMap: { [key: string]: string } = {
    FULL_ADMIN: 'Quản trị toàn quyền',
    ADMIN: 'Quản trị viên',
    USER: 'Người dùng'
  }

  const agentTypes = manual.agentTypes.map(type => agentTypeMap[type] || type).join(', ')
  const roles = manual.roles.map(role => roleMap[role] || role).join(', ')

  return { agentTypes, roles }
}
