export interface PostActionSchedule {
  type: 'ONCE' | 'DAILY' | 'WEEKLY' | 'MONTHLY' | null
  intervalType:
    | 'ONCE'
    | 'EVERY_15_MIN'
    | 'EVERY_30_MIN'
    | 'EVERY_1_HOUR'
    | 'EVERY_2_HOURS'
    | 'EVERY_3_HOURS'
    | 'MONTHLY_FIXED_DATE'
    | 'MONTHLY_FIXED_WEEKDAY'
    | null
  startTime: string | null // HH:mm
  endTime: string | null // HH:mm
  startDate: string // dd/MM/yyyy
  dayOfWeek: string[] // MON, TUE, WED, THU, FRI, SAT, SUN
  dayOfMonth: number[]
  weekOfMonth: number[]
}

export interface PostActionNotification {
  notificationType: 'EMAIL' | 'NOTIFICATION'
  contentType: 'CUSTOM' | 'TEMPLATE'
  customTitle?: string | null
  customContent?: string | null
  templateCode?: string | null
  schedule: PostActionSchedule
}

export interface PostAction {
  postActionType: 'NOTIFICATION'
  webhook: null
  notifications: PostActionNotification
}

export interface FormValues {
  postActionType: 'NOTIFICATION'
  notificationType: string[]
  contentType?: 'CUSTOM' | 'TEMPLATE'
  emailTitle?: string
  emailContent?: string
  notificationTitle?: string
  notificationContent?: string
  templateCode?: string
  // Schedule data từ SendNotificationFrequency component
  sendNotificationType?: string
  syncTimeOnce?: any
  startDateOnce?: any
  frequencyDaily?: string
  syncTimeDaily?: any
  syncTimeDailyRange?: any[]
  startDateDaily?: any
  weekDays?: string[]
  frequencyDayInWeek?: string
  syncTimeDayInWeek?: any
  startDateDayInWeek?: any
  frequencyDayInMonth?: string
  dayOfMonth?: string
  syncTime?: any
  startDateDayInMonth?: any
}

const mapScheduleFromFormValues = (values: FormValues): PostActionSchedule => {
  const type = values.sendNotificationType || 'ONCE'

  const formatTime = (timeValue: any) => {
    if (!timeValue) return '17:30'

    // Nếu là dayjs object, gọi format
    if (typeof timeValue.format === 'function') {
      return timeValue.format('HH:mm')
    }

    // Nếu đã là string, kiểm tra format và trả về
    if (typeof timeValue === 'string') {
      // Kiểm tra xem string có đúng format HH:mm không
      const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/

      if (timeRegex.test(timeValue)) {
        return timeValue
      }
    }

    // Fallback
    return '17:30'
  }

  const formatDate = (dateValue: any) => {
    if (!dateValue) return new Date().toLocaleDateString('vi-VN')

    // Nếu là dayjs object, gọi format
    if (typeof dateValue.format === 'function') {
      return dateValue.format('DD/MM/YYYY')
    }

    // Nếu đã là string, kiểm tra format và trả về
    if (typeof dateValue === 'string') {
      // Kiểm tra xem string có đúng format DD/MM/YYYY không
      const dateRegex = /^([0-2][0-9]|3[0-1])\/(0[1-9]|1[0-2])\/\d{4}$/

      if (dateRegex.test(dateValue)) {
        return dateValue
      }
    }

    // Fallback
    return new Date().toLocaleDateString('vi-VN')
  }

  const result: PostActionSchedule = {
    type: type as 'ONCE' | 'DAILY' | 'WEEKLY' | 'MONTHLY',
    intervalType: 'ONCE',
    startTime: null,
    endTime: null,
    startDate: new Date().toLocaleDateString('vi-VN'),
    dayOfWeek: [],
    dayOfMonth: [],
    weekOfMonth: []
  }

  switch (type) {
    case 'ONCE':
      result.startTime = formatTime(values.syncTimeOnce)
      result.startDate = formatDate(values.startDateOnce)
      result.intervalType = 'ONCE'
      break

    case 'DAILY':
      const frequencyDaily = values.frequencyDaily || 'ONCE'

      result.intervalType = frequencyDaily as any

      if (frequencyDaily === 'ONCE') {
        result.startTime = formatTime(values.syncTimeDaily)
        result.endTime = formatTime(values.syncTimeDaily)
      } else {
        const rangeValue = values.syncTimeDailyRange

        if (Array.isArray(rangeValue)) {
          result.startTime = formatTime(rangeValue[0])
          result.endTime = formatTime(rangeValue[1])
        } else {
          result.startTime = '01:00'
          result.endTime = '01:00'
        }
      }

      result.startDate = formatDate(values.startDateDaily)
      break

    case 'WEEKLY':
      const frequencyWeek = values.frequencyDayInWeek || 'ONCE'

      result.intervalType = frequencyWeek as any
      result.dayOfWeek = values.weekDays || []

      if (frequencyWeek === 'ONCE') {
        result.startTime = formatTime(values.syncTimeDayInWeek)
        result.endTime = formatTime(values.syncTimeDayInWeek)
      } else {
        const rangeValue = values.syncTimeDayInWeek

        if (Array.isArray(rangeValue)) {
          result.startTime = formatTime(rangeValue[0])
          result.endTime = formatTime(rangeValue[1])
        } else {
          result.startTime = '01:00'
          result.endTime = '01:00'
        }
      }

      result.startDate = formatDate(values.startDateDayInWeek)
      break

    case 'MONTHLY':
      const frequencyMonth = values.frequencyDayInMonth || 'FIXED_DATE'

      if (frequencyMonth === 'FIXED_DATE') {
        const dayOfMonth = values.dayOfMonth

        result.dayOfMonth = dayOfMonth ? [parseInt(dayOfMonth)] : [1]
        result.intervalType = 'MONTHLY_FIXED_DATE'
      } else {
        // For FIXED_WEEK_DAY, we need to get the selected day and week from the form
        // This would need to be passed from the component state
        result.dayOfWeek = ['MON'] // Default value, should be updated from component
        result.weekOfMonth = [1] // Default value, should be updated from component
        result.intervalType = 'MONTHLY_FIXED_WEEKDAY'
      }

      result.startTime = formatTime(values.syncTime)
      result.endTime = formatTime(values.syncTime)
      result.startDate = formatDate(values.startDateDayInMonth)
      break
  }

  return result
}

export const mapFormToPostActions = (values: FormValues): PostAction[] => {
  const types: string[] = values.notificationType || []
  const schedule = mapScheduleFromFormValues(values)

  return types.map(type => {
    const isEmail = type === 'EMAIL'
    const contentType = isEmail ? values.contentType || 'TEMPLATE' : 'CUSTOM'

    const notifications: PostActionNotification = {
      notificationType: type as 'EMAIL' | 'NOTIFICATION',
      contentType,
      customTitle:
        contentType === 'CUSTOM' ? (isEmail ? values.emailTitle || null : values.notificationTitle || null) : null,
      customContent:
        contentType === 'CUSTOM' ? (isEmail ? values.emailContent || null : values.notificationContent || null) : null,
      templateCode: contentType === 'TEMPLATE' ? values.templateCode || null : null,
      schedule
    }

    return {
      postActionType: 'NOTIFICATION',
      webhook: null,
      notifications
    }
  })
}

export const getConditionTypeLabel = (value: string) => {
  const labelMap = {
    STATE: 'Điều kiện trạng thái',
    RULE_ENGINE: 'Điều kiện dữ liệu',
    SCHEDULE: 'Điều kiện thời gian',
    MANUAL: 'Điều kiện thủ công'
  }

  return labelMap[value as keyof typeof labelMap] || null
}

export const convertProductTypeName = (obj: any) =>
  (obj.physical?.length > 0 && 'Hàng hóa vật lý') ||
  (obj.service?.length > 0 && 'Sản phẩm dịch vụ') ||
  (obj.digital?.length > 0 && 'Hàng hóa kỹ thuật số') ||
  ''

export const getNotificationType = (value: string) => {
  const labelMap = {
    EMAIL: 'Email',
    NOTIFICATION: 'Notification'
  }

  return labelMap[value as keyof typeof labelMap] || null
}
