import { createSelector, createSlice } from '@reduxjs/toolkit'

import { get, isEmpty, merge } from 'lodash'

import { CUSTOMER_TYPE } from '@/constants/custom-field'

import { mergeCouponList } from '@/utils/subscription'

import type { RootState } from '@/redux-store'
import { IN_ADDON, IN_COMBO, IN_PRICING, IN_TOTAL, IN_TOTAL_COMBO } from '@views/payment/constant/PaymentConstant'

import type { QRCodeFunctionProps, QRCodeStateProps } from '@/views/payment/components/order-summary/QRCodeModal'
import type { Item } from '@/types/subscription/Subscription'

interface QrCodeState {
  image: string
  timeLeft: number
  billId: string | number | string[] | number[]
  name: string
  state?: Partial<QRCodeStateProps>
  functions?: Partial<QRCodeFunctionProps>
}

interface PaymentServiceState {
  counts: {
    countCal: number
    countCalAddon: number
    countCalDetail: number
  }
  pricing?: any
  deviceInfo?: any
  addons?: any
  service?: any
  combo?: any
  serviceGroup?: any
  totalCoupon?: number
  totalAmountAfterTaxFinal?: string
  totalAmountPreTaxFinal?: string
  modal: {
    isOpenMyAddress: boolean
    isOpenCreateAddress: boolean
    isOpenEInvoiceAddress: boolean
    isEditAddress: boolean
    isOpenPaymentSuccess: boolean
    isOpenModalContact: boolean
    isModalProfiles: boolean
    isOpenProfileInfo: boolean
    isOpenCustomerAddressList: boolean
    isOpenCreateCustomerAddress: boolean
    isOpenPaymentError: boolean
    isOpenQRCodeModal: boolean
  }
  updateSetupAddress: any
  invoiceInfoList: any
  customerAddressList: any
  customerInfo: any
  profileInfo: any
  idSetupAddressUpdate: number
  idCustomerAddressUpdate: number
  numberOfDayExportBill: number
  isOpenModalVoucherPricing: boolean
  isOpenModalVoucherAddon: boolean
  isOpenModalVoucherTotal: boolean
  isOpenModalVoucherCombo: boolean
  isOpenModalVoucherComboTotal: boolean
  coupons: any
  configCoupon: string
  checkedPolicy: boolean
  isNewAcc: boolean
  qrCode: QrCodeState
}

const initialState: PaymentServiceState = {
  counts: {
    countCal: 0,
    countCalAddon: 0,
    countCalDetail: 0
  },
  addons: [],
  modal: {
    isOpenMyAddress: false,
    isOpenCreateAddress: false,
    isOpenEInvoiceAddress: false,
    isEditAddress: false,
    isOpenPaymentSuccess: false,
    isOpenModalContact: false,
    isModalProfiles: false,
    isOpenProfileInfo: false,
    isOpenCustomerAddressList: false,
    isOpenCreateCustomerAddress: false,
    isOpenPaymentError: false,
    isOpenQRCodeModal: false
  },
  pricing: {},
  deviceInfo: {},
  service: {},
  combo: {},
  serviceGroup: {},
  invoiceInfoList: [],
  customerAddressList: [],
  customerInfo: {},
  profileInfo: {},
  updateSetupAddress: {},
  idSetupAddressUpdate: 0,
  idCustomerAddressUpdate: 0,
  numberOfDayExportBill: 0,
  isOpenModalVoucherPricing: false,
  isOpenModalVoucherCombo: false,
  isOpenModalVoucherAddon: false,
  isOpenModalVoucherTotal: false,
  isOpenModalVoucherComboTotal: false,
  coupons: [],
  configCoupon: '',
  checkedPolicy: false,
  isNewAcc: false,
  qrCode: {
    image: '',
    timeLeft: 0,
    billId: -1,
    name: ''
  }
}

export const genKeyCoupon = (
  coupon: any,
  options?: {
    couponField?: string
    mcField?: string
    checkCoupon?: (coupon: any) => boolean
  }
) => {
  const { couponField = 'id', mcField = 'mcId', checkCoupon } = options ?? {}

  const isCoupon = checkCoupon ? checkCoupon(coupon) : coupon.promotionPopupType === 'COUPON'

  if (isCoupon) return get(coupon, couponField) ?? coupon.id

  return `${coupon.activityIdx}-${get(coupon, mcField) ?? coupon.mcId ?? coupon.id}`
}

// Update coupon discount values
export const updateCouponDiscounts = (coupons: any, couponMap: Map<number, any>) => {
  coupons.forEach((item: any) => {
    const coupon = couponMap.get(genKeyCoupon(item))

    if (coupon) {
      item.price = coupon.price
    }
  })
}

function initQuantityBaseOnMinMax(item: Item): Item {
  const itemCopy: Item = { ...item }

  itemCopy.minimumQuantity = item.minimumQuantity ?? 1
  itemCopy.maximumQuantity = item.maximumQuantity ?? Infinity

  // dành cho addon không bắt buộc
  if (itemCopy.isRequired === 'NO') {
    // Lưu trữ số lượng trước đó
    itemCopy.preQuantity = item.quantity
  } else {
    itemCopy.preQuantity = itemCopy.quantity
  }

  if (item.quantity > 0) {
    itemCopy.quantity = Math.max(item.quantity, itemCopy.minimumQuantity ?? 1)
  }

  return itemCopy
}

export const taxFeeInfo = (taxFeeData?: {
  objectFees?: any[]
  totalFinalFee?: number
  objectTaxes?: any[]
  totalFinalTax?: number
}) => {
  return {
    feeList: taxFeeData?.objectFees && taxFeeData.objectFees.length > 0 ? taxFeeData.objectFees : [],
    totalFinalFee: taxFeeData?.totalFinalFee || 0,
    taxList: taxFeeData?.objectTaxes && taxFeeData.objectTaxes.length > 0 ? taxFeeData.objectTaxes : [],
    totalFinalTax: taxFeeData?.totalFinalTax || 0
  }
}

export const paymentSlice = createSlice({
  name: 'paymentService',
  initialState,
  reducers: {
    initBilling(state, { payload }) {
      state.coupons = []
      state.deviceInfo = {}
      payload.pricing = { ...payload.pricing, paymentMethod: state.pricing.paymentMethod }
      Object.assign(state, payload)
      Object.assign(state, {
        isOpenModalVoucherPricing: false,
        isOpenModalVoucherAddon: false,
        isOpenModalVoucherTotal: false,
        isOpenModalVoucherCombo: false,
        isOpenModalVoucherComboTotal: false
      })
    },
    initBillingSubscription(state, { payload }) {
      const pricing = initQuantityBaseOnMinMax(payload)

      const addons = payload.addonsList.map((item: any) => {
        const result = initQuantityBaseOnMinMax(item)

        return result
      })

      Object.assign(state.pricing, pricing)
      Object.assign(state.addons, addons)
      state.coupons = [...payload.coupons]

      state.counts.countCalDetail = 0
    },
    applyCalculateSubscription(state, { payload }) {
      // Tối ưu: Sử dụng object destructuring để giảm thiểu lặp lại mã

      const {
        totalAmountAfterTaxFinal,
        totalAmountPreTax,
        totalAmountPreTaxFinal,
        totalAmountAfterRefund,
        creditNoteList,
        object,
        addons = [],
        priceVariant,
        variantName,
        serviceName,
        taxFeePricing,
        taxFeeDevice
      } = payload

      const subscription = {
        ...state.pricing,
        totalAmountAfterTaxFinal,
        totalAmountPreTax,
        totalAmountPreTaxFinal,
        totalAmountAfterRefund,
        creditNoteList,
        preAmountTax: object?.preAmountTax,
        objectVariant: payload?.objectVariant || payload?.objectService,
        priceVariant,
        variantName,
        serviceName,
        taxFeePricing,
        listUnitLimited: object?.planPriceList,
        taxFeeDevice
      }

      Object.assign(state.pricing, subscription)

      const couponObj = mergeCouponList({ obj: object })
      const couponListPricing = couponObj.couponList
      const invoiceCoupon = couponObj.coupons

      // Create maps for efficient lookup
      const couponPricingCalculateMap = new Map(couponListPricing.map(coupon => [genKeyCoupon(coupon), coupon]))
      const couponTotalCalculateMap = new Map(invoiceCoupon.map(coupon => [genKeyCoupon(coupon), coupon]))

      if (payload?.lstRejectPromotion?.length > 0) {
        const rejectPromotionMap = new Map(
          payload?.lstRejectPromotion.map((coupon: any) => [
            genKeyCoupon(coupon, {
              couponField: 'couponId',
              mcField: 'mcId',
              checkCoupon: (coupon: any) => coupon.type === 'COUPON'
            }),
            coupon
          ])
        )

        state.pricing.couponList = state.pricing.couponList.filter((coupon: any) => {
          const key = genKeyCoupon(coupon)

          return !rejectPromotionMap.has(key)
        })
      }

      updateCouponDiscounts(state.coupons, couponTotalCalculateMap)
      updateCouponDiscounts(state.pricing.couponList, couponPricingCalculateMap)

      // Xử lý từng addon
      const addonsMap: Map<number, any> = new Map(addons.map((addon: any) => [addon.id, addon]))

      state.addons.forEach((addon: any) => {
        const addonCalculate = addonsMap.get(addon.id)

        if (!addonCalculate) {
          addon.preAmountTax = 0
          addon.listUnitLimited = addon.unitLimitedList

          return
        }

        const addonCouponObj = mergeCouponList({ obj: addonCalculate })

        const couponListAddon = addonCouponObj.couponList
        const invoiceCouponAddon = addonCouponObj.coupons

        // Create maps for efficient lookup
        const couponAddonCalculateMap = new Map(couponListAddon.map(coupon => [genKeyCoupon(coupon), coupon]))
        const couponAddonTotalCalculateMap = new Map(invoiceCouponAddon.map(coupon => [genKeyCoupon(coupon), coupon]))

        updateCouponDiscounts(addon.couponList, couponAddonCalculateMap)
        updateCouponDiscounts(addon.coupons, couponAddonTotalCalculateMap)

        const newAddonInfo = {
          ...addonCalculate,

          taxList: addon?.taxes?.map((e: any) => ({
            ...e,
            percent: (e?.percent * 100).toFixed(0)
          })),
          taxSetupFeeList: addon.setupFee?.taxes,
          listUnitLimited: !!addonCalculate.planPriceList
            ? addonCalculate.planPriceList?.sort((a: any, b: any) => a.unitFrom - b.unitFrom)
            : addon.unitLimitedList
        }

        merge(addon, newAddonInfo)
      })

      // Kiểm tra tính khả dụng
      // checkAvailable(state.subscription, state.subscription.totalAmountPreTax)
    },
    changeAmountPricing(state, { payload }) {
      const { amount } = payload

      // Preserve previous quantity
      state.pricing.preQuantity = state.pricing.quantity ?? state.pricing.preQuantity

      // Update current quantity
      state.pricing.quantity = amount

      // Manage zero amount state
      // state.subscription.checkAmountZero.pricing = amount === 0

      // Increment calculation trackers
      state.counts.countCalDetail++
    },
    changeAmountAddon(state, { payload }) {
      const { addonId, amount } = payload

      const calculateAddon = state.addons.find((el: any) => el.id === addonId)

      // Update current addon quantity
      calculateAddon.quantity = amount

      // Manage zero amount state for the specific addon
      // state.subscription.checkAmountZero[index] = amount === 0

      // Increment calculation trackers
      state.counts.countCalDetail++
    },
    handelChangeVariant(state, { payload }) {
      const newDeviceInfo = { ...state.deviceInfo, ...payload }

      if (!newDeviceInfo?.variantSelected?.id) {
        state.totalAmountAfterTaxFinal = '0'
        state.totalAmountPreTaxFinal = '0'
      }

      state.deviceInfo = { ...state.deviceInfo, ...payload }
    },
    handleUpdateService(state, { payload }) {
      const {
        name,
        icon,
        developerName,
        paymentMethod,
        serviceDraftId,
        id,
        serviceOwner,
        productType,
        priceService,
        urlPreOrder,
        externalLinkIcon
      } = payload

      const infoService = {
        id,
        name,
        avatar: icon ?? externalLinkIcon,
        developerName,
        draftId: serviceDraftId,
        isDevice: productType === 1,
        priceService: priceService,
        serviceOwner,
        urlPreOrder
      }

      state.pricing.paymentMethod = paymentMethod
      Object.assign(state.service, infoService)
    },
    handleUpdatePricing(state, { payload }) {
      // danh cho luc an F5 reload trang
      if (!state.pricing.hasOwnProperty('draftId')) {
        const { pricing, addons } = payload

        Object.assign(state.pricing, pricing)
        Object.assign(state.addons, addons)
        state.counts.countCal++
      }
    },
    handleUpdatePricingState(state, { payload }) {
      // để update Pricing
      state.pricing = payload
    },
    applyCalculate(state, { payload }) {
      let calculateDevice = {}

      if (payload?.isDevice) {
        const {
          feeList: deviceFees,
          taxList: deviceTaxes,
          totalFinalFee,
          totalFinalTax: totalFinalTaxes
        } = taxFeeInfo(payload?.taxFeeDevice)

        const priceService =
          payload?.objectService &&
          payload?.objectService?.priceService?.length > 0 &&
          payload?.objectService?.priceService?.filter((e: any) => e.type === 7)?.length > 0
            ? payload?.objectService?.priceService?.filter((e: any) => e.type === 7)[0]
            : null

        const priceVariant =
          payload?.objectVariant &&
          payload?.objectVariant?.priceVariant?.length > 0 &&
          payload?.objectVariant?.priceVariant?.filter((e: any) => e.type === 5)?.length > 0
            ? payload?.objectVariant?.priceVariant?.filter((e: any) => e.type === 5)[0]
            : null

        const priceDevice = priceService ?? priceVariant ?? { amount: 0, amountPreTax: 0, quantity: 1 }

        calculateDevice = {
          deviceFees,
          totalFinalFee,
          deviceTaxes,
          totalFinalTaxes,
          priceObj: {
            amount: priceDevice?.amount,
            amountPreTax: priceDevice?.amountPreTax,
            quantity: priceDevice?.quantity
          }
        }
      }

      const {
        object,
        creditNoteList,
        addons,
        lstRejectPromotion,
        totalCoupon,
        totalAmountPreTaxFinal,
        totalAmountAfterTaxFinal,
        taxFeePricing,
        couponList: calculateCouponList
      } = payload

      const { preAmountTax, setupFee, planPriceList } = object

      const { feeList, taxList, totalFinalFee, totalFinalTax } = taxFeeInfo(taxFeePricing)

      // Update state with payload data
      Object.assign(state, {
        totalAmountAfterTaxFinal,
        totalAmountPreTaxFinal,
        totalCoupon,
        deviceInfo: { ...state.deviceInfo, calculateDevice },
        pricing: {
          ...state.pricing,
          preAmountTax,
          creditNoteList,
          multiPlanId: object.multiPlanId,
          price: object.price,
          setupFee,
          unitLimitedList: planPriceList,
          feeList,
          taxList,
          totalFinalFee,
          totalFinalTax,
          calculateCouponList
        }
      })
      const couponObj = mergeCouponList({ obj: object })

      const couponListPricing = couponObj.couponList
      const invoiceCoupon = couponObj.coupons

      // Create maps for efficient lookup
      const couponPricingCalculateMap = new Map(couponListPricing.map(coupon => [genKeyCoupon(coupon), coupon]))
      const couponTotalCalculateMap = new Map(invoiceCoupon.map(coupon => [genKeyCoupon(coupon), coupon]))

      if (lstRejectPromotion?.length > 0) {
        const rejectPromotionMap = new Map(
          lstRejectPromotion.map((coupon: any) => [
            genKeyCoupon(coupon, {
              couponField: 'couponId',
              mcField: 'mcId',
              checkCoupon: (coupon: any) => coupon.type === 'COUPON'
            }),
            coupon
          ])
        )

        state.pricing.couponList = state.pricing.couponList.filter((coupon: any) => {
          const key = genKeyCoupon(coupon)

          return !rejectPromotionMap.has(key)
        })

        state.coupons = state.coupons.filter((coupon: any) => {
          const key = genKeyCoupon(coupon)

          return !rejectPromotionMap.has(key)
        })
      }

      updateCouponDiscounts(state.coupons, couponTotalCalculateMap)
      updateCouponDiscounts(state.pricing.couponList, couponPricingCalculateMap)

      // Process addons
      if (addons.length) {
        const addonsMap: Map<number, any> = new Map(addons.map((addon: any) => [addon.id, addon]))

        state.addons.forEach((addon: any) => {
          const addonCalculate = addonsMap.get(addon.id)

          if (!addonCalculate) return

          addon.id = addonCalculate.id
          const addonCouponObj = mergeCouponList({ obj: addonCalculate })

          const couponListAddon = addonCouponObj.couponList
          const invoiceCouponAddon = addonCouponObj.coupons

          // Create maps for efficient lookup
          const couponAddonCalculateMap = new Map(couponListAddon.map(coupon => [genKeyCoupon(coupon), coupon]))
          const couponAddonTotalCalculateMap = new Map(invoiceCouponAddon.map(coupon => [genKeyCoupon(coupon), coupon]))

          if (lstRejectPromotion?.length > 0) {
            const rejectPromotionMap = new Map(
              lstRejectPromotion.map((coupon: any) => [
                genKeyCoupon(coupon, {
                  couponField: 'couponId',
                  mcField: 'mcId',
                  checkCoupon: (coupon: any) => coupon.type === 'COUPON'
                }),
                coupon
              ])
            )

            addon.coupons = addon.coupons.filter((coupon: any) => {
              const key = genKeyCoupon(coupon)

              return !rejectPromotionMap.has(key)
            })
          }

          updateCouponDiscounts(addon.couponList, couponAddonCalculateMap)

          updateCouponDiscounts(addon.coupons, couponAddonTotalCalculateMap)

          // Map qua coupons và update price nếu có addon tương ứng
          state.coupons.forEach((c: any) => {
            const addonCoupon = couponAddonTotalCalculateMap.get(genKeyCoupon(c))

            if (addonCoupon) {
              c.price = c.price + addonCoupon.price
            }
          })

          addon.preAmountTax = addonCalculate.preAmountTax
          addon.unitLimitedList =
            addonCalculate.planPriceList &&
            addonCalculate.planPriceList.sort((a: any, b: any) => a.unitFrom - b.unitFrom)
        })
      }
    },
    applyCalculateDevice(state, { payload }) {
      const { totalAmountAfterTaxFinal, totalAmountPreTaxFinal } = payload

      const deviceFees =
        payload?.taxFeeDevice?.objectFees && payload?.taxFeeDevice?.objectFees?.length > 0
          ? payload?.taxFeeDevice?.objectFees
          : []

      const totalFinalFee = payload?.taxFeeDevice?.totalFinalFee ? payload?.taxFeeDevice?.totalFinalFee : 0

      const deviceTaxes =
        payload?.taxFeeDevice?.objectTaxes && payload?.taxFeeDevice?.objectTaxes?.length > 0
          ? payload?.taxFeeDevice?.objectTaxes
          : []

      const totalFinalTaxes = payload?.taxFeeDevice?.totalFinalTax ? payload?.taxFeeDevice?.totalFinalTax : 0

      const priceService =
        payload?.objectService &&
        payload?.objectService?.priceService?.length > 0 &&
        payload?.objectService?.priceService?.filter((e: any) => e.type === 7)?.length > 0
          ? payload?.objectService?.priceService?.filter((e: any) => e.type === 7)[0]
          : null

      const priceVariant =
        payload?.objectVariant &&
        payload?.objectVariant?.priceVariant?.length > 0 &&
        payload?.objectVariant?.priceVariant?.filter((e: any) => e.type === 5)?.length > 0
          ? payload?.objectVariant?.priceVariant?.filter((e: any) => e.type === 5)[0]
          : null

      state.deviceInfo = {
        ...state.deviceInfo,
        calculateDevice: {
          deviceFees,
          totalFinalFee,
          deviceTaxes,
          totalFinalTaxes,
          priceObj: {
            amount: priceService?.amount || priceVariant?.amount || 0,
            amountPreTax: priceService?.amountPreTax || priceVariant?.amountPreTax || 0,
            quantity: priceService?.quantity || priceVariant?.quantity || 1
          }
        }
      }
      state.totalAmountAfterTaxFinal = totalAmountAfterTaxFinal
      state.totalAmountPreTaxFinal = totalAmountPreTaxFinal
    },
    handleToggleModal(state, { payload }) {
      const { value } = payload

      state.modal.isOpenMyAddress = value
    },
    handleToggleProfileInfo(state, { payload }) {
      const { value } = payload

      state.modal.isOpenProfileInfo = value
    },
    handleSetIsNewAcc(state, { payload }) {
      const { value } = payload

      state.isNewAcc = value
    },
    handleToggleModalCreateSetupAddress(state, { payload }) {
      const { value } = payload

      state.modal.isOpenCreateAddress = value
    },
    handleToggleModalUpdateEInvoiceAddress(state, { payload }) {
      const { value } = payload

      state.modal.isOpenEInvoiceAddress = value
    },
    handleCreateContact(state, { payload }) {
      state.modal.isOpenModalContact = payload
    },
    handleToggleModalPaymentSuccess(state, { payload }) {
      state.modal.isOpenPaymentSuccess = payload
    },
    handleUpdateSetupAddress(state, { payload }) {
      state.invoiceInfoList = payload
    },
    handleUpdateQuantityAddon(state, { payload }) {
      const { addonId, value, type } = payload
      const addon = state.addons?.find((addon: any) => addon.id === addonId)

      if ((value || 0) > 0) addon.selected = true

      addon.quantity = value

      if (type !== 'modal') state.counts.countCal++
      state.counts.countCalAddon++
    },
    handleChangeUpdateAddress(state, { payload }) {
      state.modal.isEditAddress = true
      state.idSetupAddressUpdate = payload
    },
    handleCreateAddress(state) {
      state.modal.isEditAddress = false
    },

    handleChangeProfileInfo(state, { payload }) {
      state.profileInfo = payload

      //update bỏ taxcode vì thêm định danh repIdentityNo nên ko xác định đc bắt buộc là taxcode hay repIdentityNo
      const requiredFields = ['repFullName', 'provinceId', 'wardId', 'streetId', 'address', 'phoneNumber']

      const personalRequiredFields = [
        'firstName',
        'lastName',
        'email',
        'wardId',
        'streetId',
        'address',
        'phoneNumber',
        'repIdentityType',
        'repIdentityNo',
        'identityCreatedDate'
      ]

      // check các field bắt buộc
      const missingField = requiredFields.some(field => !get(state?.profileInfo, field))

      if (missingField && payload?.customerType !== CUSTOMER_TYPE.CN) {
        state.modal.isOpenProfileInfo = true
        state.isNewAcc = true
      } else if (
        personalRequiredFields.some(field => !get(state.profileInfo, field)) &&
        payload?.customerType === 'CN'
      ) {
        state.modal.isOpenProfileInfo = true
        state.isNewAcc = true
      } else {
        state.isNewAcc = false
      }
    },

    handleChangeCustomerInfo(state, { payload }) {
      // Init for form (auto fill data)
      const customerInfoForm = {
        smeName: payload?.smeName,
        taxNo: payload?.taxNo,
        address: payload?.address,
        phoneNo: payload?.phoneNo,
        email: payload?.email,
        districtId: payload?.districtId,
        districtName: payload?.districtName,
        provinceId: payload?.provinceId,
        provinceName: payload?.provinceName,
        wardId: payload?.wardId,
        wardName: payload?.wardName,
        streetId: payload?.streetId,
        setupAddress: payload?.setupAddress,
        userId: payload?.userId ?? payload?.id,
        identityNo: payload?.identityNo,
        lastName: payload?.lastName,
        firstName: payload?.firstName
      }

      const newCustomerInfo = {
        CustomerInfoForm: customerInfoForm,
        ...payload,
        ...payload?.update
      }

      state.customerInfo = Object.fromEntries(Object.entries(newCustomerInfo).filter(([key]) => key !== 'update'))
    },
    handleSetCustomerSetupAddress(state, { payload }) {
      const defaultAddress = payload.defaultAddress

      if (!isEmpty(defaultAddress)) {
        const defaultSetupAddress = {
          streetId: defaultAddress?.streetId,
          streetName: defaultAddress?.streetName,
          wardId: defaultAddress?.wardId,
          wardCode: defaultAddress?.wardCode,
          wardName: defaultAddress?.wardName,
          districtId: defaultAddress?.districtId,
          districtCode: defaultAddress?.districtCode,
          districtName: defaultAddress?.districtName,
          provinceId: defaultAddress?.provinceId,
          provinceCode: defaultAddress?.provinceCode,
          provinceName: defaultAddress?.provinceName,
          setupAddress: defaultAddress?.address
        }

        state.customerInfo = {
          ...state.customerInfo,
          CustomerInfoForm: {
            ...state.customerInfo.CustomerInfoForm,
            ...defaultSetupAddress
          },
          ...defaultSetupAddress
        }
      }
    },
    handleChangeInvoiceAddress(state, { payload }) {
      state.invoiceInfoList.forEach((address: any) => {
        address.checked = false

        if (address.id === payload.id) {
          address.checked = payload.checked
        }
      })
    },
    handleChangeSetupAddress(state, { payload }) {
      state.customerAddressList.forEach((address: any) => {
        address.checked = false

        if (address.id === payload.id) {
          address.checked = payload.checked
        }
      })
    },
    handleChangeUpdateService(state, { payload }) {
      const { id, value } = payload

      if (state.pricing.id !== id) return

      state.pricing.quantity = value
      state.counts.countCal++
    },
    handleChangeUpdateQuantityDevice(state, { payload }) {
      const { value } = payload

      if (!state.deviceInfo.quantityDevice) return

      state.deviceInfo.quantityDevice = value
    },
    handleCalculateAddon(state, { payload }) {
      if (!payload.length) return

      const addonsMap = new Map(payload.map((addon: any) => [addon.id, addon]))

      state.addons = state.addons.map((addon: any) => {
        const addonCalculate = addonsMap.get(addon.id) as any

        if (!addonCalculate) return addon

        if (addonCalculate.planPriceList?.length && addonCalculate.quantity) {
          // cập nhật unitLimitedList khi quantity thay đổi
          const newUnitLimitedList = addon?.unitLimitedList?.map((addonRow: any) => {
            const matchingAddonCalculateRow = addonCalculate?.planPriceList?.find(
              (addonCalculateRow: any) => addonCalculateRow?.unitFrom === addonRow?.unitFrom
            )

            return matchingAddonCalculateRow
              ? { ...addonRow, amountBeforeTax: matchingAddonCalculateRow?.amountBeforeTax }
              : addonRow
          })

          return {
            ...addon,
            price: addonCalculate.price,
            preAmountTax: addonCalculate.quantity ? addonCalculate.preAmountTax : 0,
            unitLimitedList: newUnitLimitedList
          }
        }

        return {
          ...addon,
          price: addonCalculate.price,
          preAmountTax: addonCalculate.quantity ? addonCalculate.preAmountTax : 0
        }
      })
    },
    handleGetConfigExportInvoice(state, { payload }) {
      state.numberOfDayExportBill = payload
    },
    handleUpdateEmployeeCode(state, { payload }) {
      state.customerInfo.employeeCode = payload
    },
    handleToggleModalCoupon(state, { payload }) {
      const { type, value, addonId } = payload

      const addon = state.addons.find((addon: any) => addon.id === addonId)

      switch (type) {
        case IN_PRICING:
          state.isOpenModalVoucherPricing = value
          break
        case IN_ADDON:
          addon.isOpenModalVoucherAddon = value
          break
        case IN_TOTAL:
          state.isOpenModalVoucherTotal = value
          break
        case IN_COMBO:
          state.isOpenModalVoucherCombo = value
          break
        case IN_TOTAL_COMBO:
          state.isOpenModalVoucherComboTotal = value
      }
    },
    handleConfigCoupon(state, { payload }) {
      state.configCoupon = payload
    },
    handleChoiceCoupon(state, { payload }) {
      const { coupon, type, addonId } = payload
      const addon = state.addons.find((addon: any) => addon.id === addonId)
      const isSingleCoupon = state.configCoupon === '1'

      const updateList = (list: any): any => (isSingleCoupon ? [coupon] : [...list, coupon])

      switch (type) {
        case IN_PRICING:
          state.pricing.couponList = updateList(state.pricing.couponList)
          state.isOpenModalVoucherPricing = false
          break
        case IN_ADDON:
          addon.couponList = updateList(addon.couponList)
          addon.isOpenModalVoucherAddon = false
          break
        case IN_TOTAL:
          state.coupons = [coupon]
          state.pricing.coupons = [coupon]
          state.addons.forEach((addon: any) => {
            addon.coupons = [coupon]
          })
          state.isOpenModalVoucherTotal = false
          break
        case IN_COMBO:
          state.combo.comboPlan.couponList = updateList(state.combo.comboPlan.couponList)
          state.isOpenModalVoucherCombo = false
          break
        case IN_TOTAL_COMBO:
          state.coupons = [coupon]
          // state.combo.comboPlan.coupons = [coupon]
          // state.addons.forEach((addon: any) => {
          //   addon.coupons = [coupon]
          // })
          state.isOpenModalVoucherComboTotal = false
          break
      }

      state.counts.countCal++
      state.counts.countCalDetail++
    },
    handleDeleteCoupon(state, { payload }) {
      const { coupon, type, addonId } = payload

      const addon = state.addons.find((addon: any) => addon.id === addonId)

      switch (type) {
        case IN_PRICING:
          state.pricing.couponList = state.pricing.couponList.filter((e: any) => {
            if (e.promotionPopupType === 'COUPON') return e.id !== coupon.id

            return e.activityIdx !== coupon.activityIdx && (e.id ?? e.mcId) !== (coupon.id ?? coupon.mcId)
          })
          state.isOpenModalVoucherPricing = false
          break
        case IN_COMBO:
          state.combo.comboPlan.couponList = state.combo.comboPlan.couponList.filter((e: any) => {
            if (e.promotionPopupType === 'COUPON') return e.id !== coupon.id

            return e.activityIdx !== coupon.activityIdx && (e.id ?? e.mcId) !== (coupon.id ?? coupon.mcId)
          })
          state.isOpenModalVoucherPricing = false
          break
        case IN_ADDON:
          addon.couponList = addon.couponList.filter((e: any) => {
            if (e.promotionPopupType === 'COUPON') return e.id !== coupon.id

            return e.activityIdx !== coupon.activityIdx && (e.id ?? e.mcId) !== (coupon.id ?? coupon.mcId)
          })
          addon.isOpenModalVoucherAddon = false
          break
        case IN_TOTAL:
          state.coupons = state.coupons.filter((e: any) => {
            if (e.promotionPopupType === 'COUPON') return e.id !== coupon.id

            return e.activityIdx !== coupon.activityIdx && (e.id ?? e.mcId) !== (coupon.id ?? coupon.mcId)
          })
          state.pricing.coupons = state.pricing.coupons.filter((e: any) => {
            if (e.promotionPopupType === 'COUPON') return e.id !== coupon.id

            return e.activityIdx !== coupon.activityIdx && (e.id ?? e.mcId) !== (coupon.id ?? coupon.mcId)
          })
          state.addons.forEach((addon: any) => {
            addon.coupons = addon.coupons.filter((e: any) => {
              if (e.promotionPopupType === 'COUPON') return e.id !== coupon.id

              return e.activityIdx !== coupon.activityIdx && (e.id ?? e.mcId) !== (coupon.id ?? coupon.mcId)
            })
          })
          state.isOpenModalVoucherTotal = false
          break
        case IN_TOTAL_COMBO:
          state.coupons = state.coupons.filter((e: any) => {
            if (e.promotionPopupType === 'COUPON') return e.id !== coupon.id

            return e.activityIdx !== coupon.activityIdx && (e.id ?? e.mcId) !== (coupon.id ?? coupon.mcId)
          })
          state.combo.comboPlan.coupons = state.combo.comboPlan.coupons.filter((e: any) => {
            if (e.promotionPopupType === 'COUPON') return e.id !== coupon.id

            return e.activityIdx !== coupon.activityIdx && (e.id ?? e.mcId) !== (coupon.id ?? coupon.mcId)
          })
          state.addons.forEach((addon: any) => {
            addon.coupons = addon.coupons.filter((e: any) => {
              if (e.promotionPopupType === 'COUPON') return e.id !== coupon.id

              return e.activityIdx !== coupon.activityIdx && (e.id ?? e.mcId) !== (coupon.id ?? coupon.mcId)
            })
          })
          state.isOpenModalVoucherComboTotal = false
          break
      }

      state.counts.countCal++
      state.counts.countCalDetail++
    },
    handleUpdateContact(state, { payload }) {
      const { contactName, contactPhone } = payload

      state.customerInfo.contactName = contactName ?? state.customerInfo.contactName
      state.customerInfo.contactPhone = contactPhone ?? state.customerInfo.contactPhone
    },
    handleToggleCustomerAddressList(state, { payload }) {
      state.modal.isOpenCustomerAddressList = payload
    },
    handleToggleModalPaymentError(state, { payload }) {
      state.modal.isOpenPaymentError = payload
    },
    handleUpdateCustomerAddressList(state, { payload }) {
      state.customerAddressList = payload
    },
    handleToggleCreateCustomerAddress(state, { payload }) {
      state.modal.isOpenCreateCustomerAddress = payload
    },
    handleSetCustomerAddressId(state, { payload }) {
      state.idCustomerAddressUpdate = payload
    },
    handleResetCustomerAddressForm(state) {
      state.idCustomerAddressUpdate = 0
      state.modal.isOpenCreateCustomerAddress = false
    },
    handleToggleQRCodeModal(state, { payload }) {
      state.modal.isOpenQRCodeModal = payload
    },
    handleSetQRCodeInfo(state, { payload }) {
      Object.assign(state.qrCode, payload)
    },
    handleResetQRCodeInfo(state) {
      state.qrCode.image = ''
      state.qrCode.timeLeft = 0
    },
    handleChangePaymentMethod(state, { payload }) {
      state.pricing.paymentMethod = payload
    },
    handleResetSubscriptionInfo(state) {
      const countReset = {
        countCal: 0,
        countCalAddon: 0,
        countCalDetail: 0
      }

      state.counts = countReset
      state.addons = []
      state.pricing = {}
      state.coupons = []
    },

    /* START: COMBO*/

    handleInitCombo(state, { payload }) {
      const {
        name,
        developerName,
        banner,
        externalLinkBanner,
        icon,
        externalLinkIcon,
        periodType,
        periodValue,
        numberOfTrial,
        comboOwner,
        comboPlan
      } = payload

      Object.assign(state.combo, {
        comboName: name,
        provider: developerName,
        quantity: 1,
        avatar: banner ?? externalLinkBanner ?? icon ?? externalLinkIcon,
        comboPlan,
        periodType,
        periodValue,
        numberOfTrial,
        comboOwner
      })
    },

    handleInitComboPlan(state, { payload }) {
      const newComboPlan = { ...payload }

      delete newComboPlan.addonsList

      state.combo.comboPlan = newComboPlan
      state.addons = payload.addonsList
      state.counts.countCalAddon++
    },

    handleUpdateComboPlan(state, { payload }) {
      // danh cho luc an F5 reload trang
      const { comboPlan, totalAmountPreTaxFinal, totalAmountAfterTaxFinal } = payload

      state.combo.comboPlan = comboPlan
      state.totalAmountPreTaxFinal = totalAmountPreTaxFinal
      state.totalAmountAfterTaxFinal = totalAmountAfterTaxFinal
    },

    handleInitInfoCombo(state, { payload }) {
      state.addons = payload.comboPlan.addonsList
      Object.assign(state.combo, payload)
    },

    applyCalculateCombo(state, { payload }) {
      const {
        object,
        creditNoteList,
        addons,
        lstRejectPromotion,
        taxFeePricing,
        taxFeeDevice,
        couponList: calculateCouponList,
        totalCoupon,
        totalAmountPreTaxFinal,
        totalAmountAfterTaxFinal
      } = payload

      const { preOriginTaxAmount, preAmountTax, setupFee } = object

      const { feeList, taxList, totalFinalFee, totalFinalTax } = taxFeeInfo(
        !isEmpty(taxFeeDevice) ? taxFeeDevice : taxFeePricing
      )

      state.totalAmountAfterTaxFinal = totalAmountAfterTaxFinal
      state.totalAmountPreTaxFinal = totalAmountPreTaxFinal
      state.combo = {
        ...state.combo,
        feeList,
        taxList,
        totalFinalFee,
        totalFinalTax,
        calculateCouponList,
        totalCoupon,
        comboPlan: {
          ...state.combo.comboPlan,
          preAmountTax,
          preOriginTaxAmount,
          creditNoteList,
          price: object.price,
          originalUnitPrice: object.originalUnitPrice,
          setupFee
        }
      }
      // Update state with payload data
      const couponObj = mergeCouponList({ obj: object })

      const invoiceCouponCombo = couponObj.coupons
      const couponListCombo = couponObj.couponList

      // Create maps for efficient lookup
      const couponComboCalculateMap = new Map(couponListCombo.map(coupon => [genKeyCoupon(coupon), coupon]))
      const couponTotalCalculateMap = new Map(invoiceCouponCombo.map(coupon => [genKeyCoupon(coupon), coupon]))

      if (lstRejectPromotion?.length > 0) {
        const rejectPromotionMap = new Map(
          lstRejectPromotion.map((coupon: any) => [
            genKeyCoupon(coupon, {
              couponField: 'couponId',
              mcField: 'mcId',
              checkCoupon: (coupon: any) => coupon.type === 'COUPON'
            }),
            coupon
          ])
        )

        state.combo.comboPlan.couponList = state.combo.comboPlan.couponList.filter((coupon: any) => {
          const key = genKeyCoupon(coupon)

          return !rejectPromotionMap.has(key)
        })
      }

      updateCouponDiscounts(state.coupons, couponTotalCalculateMap)
      updateCouponDiscounts(state.combo.comboPlan.couponList, couponComboCalculateMap)

      // Process addons
      if (addons.length) {
        const addonsMap: Map<number, any> = new Map(addons.map((addon: any) => [addon.id, addon]))

        state.addons.forEach((addon: any) => {
          const addonCalculate = addonsMap.get(addon.id)

          if (!addonCalculate) return

          addon.id = addonCalculate.id
          const addonCouponObj = mergeCouponList({ obj: addonCalculate })

          const couponListAddon = addonCouponObj.couponList
          const invoiceCouponAddon = addonCouponObj.coupons

          // Create maps for efficient lookup
          const couponAddonCalculateMap = new Map(couponListAddon.map(coupon => [genKeyCoupon(coupon), coupon]))
          const couponAddonTotalCalculateMap = new Map(invoiceCouponAddon.map(coupon => [genKeyCoupon(coupon), coupon]))

          updateCouponDiscounts(addon.couponList, couponAddonCalculateMap)
          updateCouponDiscounts(addon.coupons, couponAddonTotalCalculateMap)
          addonCalculate.planPriceList =
            !isEmpty(addonCalculate.planPriceList) &&
            addonCalculate.planPriceList.map!((unitLimited: any) => ({
              ...unitLimited,
              price: unitLimited.unitPrice
            }))

          addon.preAmountTax = addonCalculate.preAmountTax
          addon.price = addonCalculate.price
          addon.unitLimitedList = !isEmpty(addonCalculate.planPriceList)
            ? addonCalculate.planPriceList?.sort((a: any, b: any) => a.unitFrom - b.unitFrom)
            : addon.unitLimitedList
        })
      }
    },

    /* GROUP SERVICE */

    initInfoServiceGroup(state, { payload }) {
      const { taxFeePricing, totalAmountAfterTaxFinal } = payload
      const { feeList, taxList, totalFinalFee, totalFinalTax } = taxFeeInfo(taxFeePricing)

      state.totalAmountAfterTaxFinal = totalAmountAfterTaxFinal

      Object.assign(state.serviceGroup, {
        ...payload,
        feeList,
        taxList,
        totalFinalFee,
        totalFinalTax
      })
    }
  }
})

const paymentServiceState = (state: RootState) => state.paymentReducer

export const infoServiceSelect = createSelector([paymentServiceState], paymentService => paymentService)
export const isOpenMyAddressSelect = createSelector(
  [paymentServiceState],
  paymentService => paymentService?.modal?.isOpenMyAddress
)

export const isOpenUpdateProfileInfo = createSelector(
  [paymentServiceState],
  paymentService => paymentService?.modal?.isOpenProfileInfo
)

export const selectPricingId = createSelector([paymentServiceState], paymentService => paymentService.pricing.id)

export const selectComboPlanId = createSelector(
  [paymentServiceState],
  paymentService => paymentService.combo.comboPlan?.id
)

export const pricingState = createSelector([paymentServiceState], paymentService => paymentService.pricing)

export const selectPricingMultiPlanId = createSelector(
  [paymentServiceState],
  paymentService => paymentService.pricing.multiPlanId
)

export const isOpenCreateAddressSelect = createSelector(
  [paymentServiceState],
  paymentService => paymentService.modal.isOpenCreateAddress
)

export const setupAddressListSelect = createSelector(
  [paymentServiceState],
  paymentService => paymentService.invoiceInfoList
)

export const addonsSelect = createSelector([paymentServiceState], paymentService => paymentService.addons)

export const isEditAddressSelect = createSelector(
  [paymentServiceState],
  paymentService => paymentService?.modal?.isEditAddress
)
export const profileInfoSelect = createSelector([paymentServiceState], paymentService => paymentService.profileInfo)
export const idSetupAddressUpdateSelect = createSelector(
  [paymentServiceState],
  paymentService => paymentService.idSetupAddressUpdate
)

export const isOpenEInvoiceAddressSelect = createSelector(
  [paymentServiceState],
  paymentService => paymentService?.modal?.isOpenEInvoiceAddress
)

export const isOpenPaymentSuccessSelect = createSelector(
  [paymentServiceState],
  paymentService => paymentService?.modal?.isOpenPaymentSuccess
)

export const isOpenPaymentErrorSelect = createSelector(
  [paymentServiceState],
  paymentService => paymentService?.modal?.isOpenPaymentError
)

export const isOpenModalContactSelect = createSelector(
  [paymentServiceState],
  paymentService => paymentService?.modal?.isOpenModalContact
)

export const countCalAddonSelect = createSelector(
  [paymentServiceState],
  paymentService => paymentService.counts.countCalAddon
)

export const countCalDetailState = createSelector(
  [paymentServiceState],
  paymentService => paymentService.counts.countCalDetail
)

export const customerInfoSelect = createSelector([paymentServiceState], paymentService => paymentService.customerInfo)

export const customerAddressListSelect = createSelector(
  [paymentServiceState],
  paymentService => paymentService.customerAddressList
)

export const customerAddressUpdateIdSelect = createSelector(
  [paymentServiceState],
  paymentService => paymentService.idCustomerAddressUpdate
)

export const isOpenCustomerAddressList = createSelector(
  [paymentServiceState],
  paymentService => paymentService?.modal?.isOpenCustomerAddressList
)

export const isOpenCreateCustomerAddress = createSelector(
  [paymentServiceState],
  paymentService => paymentService?.modal?.isOpenCreateCustomerAddress
)

export const numberOfDayExportBillSelect = createSelector(
  [paymentServiceState],
  paymentService => paymentService.numberOfDayExportBill
)

export const isOpenQRCodeModal = createSelector(
  [paymentServiceState],
  paymentService => paymentService?.modal?.isOpenQRCodeModal
)

export const isOpenModalVoucherPricingState = createSelector(
  [paymentServiceState],
  paymentService => paymentService?.isOpenModalVoucherPricing
)

export const totalAmountAfterTaxFinalState = createSelector(
  [paymentServiceState],
  paymentService => paymentService?.totalAmountAfterTaxFinal
)

export const isOpenModalVoucherTotalState = createSelector(
  [paymentServiceState],
  paymentService => paymentService?.isOpenModalVoucherTotal
)

export const couponsState = createSelector([paymentServiceState], paymentService => paymentService?.coupons)

export const isAccountNewSelect = createSelector([paymentServiceState], paymentService => paymentService?.isNewAcc)

export const selectQRCodeInfo = createSelector([paymentServiceState], paymentService => paymentService?.qrCode)

export const paymentActions = paymentSlice.actions

export default paymentSlice.reducer
