import type { PayloadAction } from '@reduxjs/toolkit'
import { createSlice } from '@reduxjs/toolkit'

import type { RootState } from '..'

interface Progress {
  nodes: any[]
  edges: any[]
}

// Constants
const initialState: Progress = {
  nodes: [
    {
      id: '1',
      type: 'textUpdater',
      position: {
        x: 0,
        y: 0
      },
      data: {
        value: {
          name: '2323',
          colorCode: '#2A6AEB',
          icon: 'AlignLeftOutlined',
          code: 'ABCXYZ',
          des: '12'
        }
      }
    },
    {
      id: '2',
      type: 'textUpdater',
      position: {
        x: 0,
        y: 180
      },
      data: {
        value: {
          name: '1',
          colorCode: '#2A6AEB',
          icon: 'AccountBookOutlined',
          code: 'ABCXYZ',
          des: '1212'
        }
      }
    }
  ],
  edges: [
    {
      id: '1',
      source: '1',
      target: '2',
      type: 'custom-edge',
      data: {
        value: {
          name: '1',
          colorCode: '#2A6AEB',
          icon: 'AccountBookOutlined',
          code: 'ABCXYZ',
          des: '1212'
        }
      }
    }
  ]
}

export const progressSlice = createSlice({
  name: 'progress',
  initialState,
  reducers: {
    setNodes(state, action: PayloadAction<any>) {
      state.nodes = action.payload
    },
    setEdges(state, action: PayloadAction<any>) {
      state.edges = action.payload
    }
  }
})

// region selectors
// Selector state builder
export const nodeSelector = (state: RootState) => state.progressReducer.nodes

export const edgeSelector = (state: RootState) => state.progressReducer.edges

// endregion

// Export các action
export const { setNodes, setEdges } = progressSlice.actions

// Export reducer để tích hợp vào store
export default progressSlice.reducer
