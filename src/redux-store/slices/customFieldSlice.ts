import type { PayloadAction } from '@reduxjs/toolkit'
import { createSlice } from '@reduxjs/toolkit'

import type { NodeTree } from '@craftjs/core'

import type { RootState } from '..'
import type { Attribute } from '@/types/custom-field/custom-conponents/attribute'

// type CustomerType = 'ENTERPRISE' | 'HOUSE_HOLD' | 'PERSONAL' | 'ALL'

type Option = {
  value: number
  label: string
}

interface ICustomFieldSlice {
  dataDependency?: any[]

  // Thông tin node mới được kéo vào
  newNodeTreeId?: string
  newNodeTree?: {
    parentId: string
    data: NodeTree
  }
  // Thông tin về node mới được chỉnh sửa(khi người dùng )
  newNodePropsEdit?: {
    nodeId: string
    propKey: string
    changeValue: any
    onChange?: (value: any) => void
  }
  // Node Id được xóa bên màn Tạo
  deleteNodeId?: string

  // Bước hiện tại trong màn Tạo dịch vụ
  currentStepCreateService?: number

  // Bước cuối trong màn Tạo dịch vụ
  lastStep?: number

  // Bước hiện tại trong màn Tạo gói dịch vụ
  currentStepCreatePricing?: number

  // tab trong chi tiết dịch vụ
  activeKey?: string

  // tab trong chi tiết solution
  activeKeySolution?: string

  // Phân loại hiện tại ở Bước 1 Tạo dịch vụ
  classification?: 'PHYSICAL' | 'DIGITAL' | 'SERVICE' | 'UNSET'

  // Loại dịch vụ: 0 - Online service, 1 - Order service
  onOsType?: 0 | 1

  // Danh mục
  categoryIds?: number[]

  // Nhà cung cấp
  providerId?: number
  // Thông tin nhà cung cấp
  providerDTO?: {
    id: number
    name: string
  }
  // Thông tin loại tiền tệ
  currencyDTO?: Option[]
  // Thông tin loại thuế
  taxDTO?: Option[]

  // check quyền hiển thị biến thể
  variantCheck?: boolean

  variants?: any[]
  attributes?: Attribute[]

  // Thông tin gói ở bảng giá bước 3 Tạo dịch vụ
  pricingData?: any[]

  // Thông tin vận chuyển ở bước 4 Tạo dịch vụ
  shippingInfo?: any

  // Thông số kỹ thuật ở  bước 1
  specifications?: any

  // check có đang ở màn tạo gói ở trong dịch vụ
  isServiceCreatePricing?: boolean

  // check quyền update
  canUpdate?: boolean

  // lấy thông tin Áp dụng điều kiện bổ sung
  conditionsApply?: any

  // lấy thông tin tab mình đã ấn vô
  tabId?: any
}

// Constants
const initialState: ICustomFieldSlice = {
  dataDependency: [],
  newNodeTreeId: undefined,
  newNodeTree: undefined,
  newNodePropsEdit: undefined,
  deleteNodeId: undefined,

  currentStepCreateService: 0,
  activeKey: 'PROCESSING',

  activeKeySolution: 'UNAPPROVED',

  classification: undefined,

  variants: undefined,

  pricingData: undefined,

  shippingInfo: undefined,

  specifications: undefined,

  tabId: undefined
}

export const customFieldSlice = createSlice({
  name: 'customField',
  initialState,
  reducers: {
    updateInfoCustomField(state, action: PayloadAction<ICustomFieldSlice>) {
      Object.assign(state, action.payload)
    },
    updateTabs(state, action: PayloadAction<string>) {
      state.activeKey = action.payload
    },
    updateTabsSolution(state, action: PayloadAction<string>) {
      state.activeKeySolution = action.payload
    },
    updateCurrentStep(state, action: PayloadAction<number>) {
      state.currentStepCreateService = action.payload
    },
    updateTabId(state, action: PayloadAction<string>) {
      state.tabId = action.payload
    }
  }
})

// region selectors
// Selector state builder
export const selectScreenType = (state: RootState) => state.builderReducer.screenType
export const selectActiveKey = (state: RootState) => state.customFieldReducer.activeKey
export const selectActiveKeySolution = (state: RootState) => state.customFieldReducer.activeKeySolution
export const selectTabId = (state: RootState) => state.customFieldReducer.tabId
export const selectProviderDTO = (state: RootState) => state.customFieldReducer.providerDTO
// endregion

// Export các action
export const { updateInfoCustomField, updateTabs, updateTabsSolution, updateCurrentStep, updateTabId } =
  customFieldSlice.actions

// Export reducer để tích hợp vào store
export default customFieldSlice.reducer
