import { cloneDeep } from 'lodash'

import { API_ROOT } from '@/models/Base'

export function convertArrayToString(
  object: any, // obj cần convert các giá trị thành list string
  listNotConvert?: string[] // danh sách các trường không mong muốn convert, giữ nguyên kiểu dữ liệu
) {
  const cloneObject = cloneDeep(object)

  Object.keys(cloneObject).forEach(key => {
    if (Array.isArray(cloneObject[key]) && !listNotConvert?.includes(key)) {
      cloneObject[key] = cloneObject[key].join(',')
    }
  })

  return cloneObject
}

/** Thay thế các kí tự đặc biệt mà string không truyền xuống được */
export function formatSpecialDigits(value: string | undefined) {
  return (value || '').replace(/%/g, '\\%').replace(/_/g, '\\_').trim()
}

/** Mã hóa các kí tự đặc biệt mà string không truyền xuống được */
export function encodeSpecialDigits(value: string | undefined) {
  return encodeURIComponent(formatSpecialDigits(value))
}

/** Hàm chặn các hành động thừa làm ảnh hưởng đến component cha khi tương tác với component hiện tại */
export const preventExcessAction = (event: any) => {
  event?.stopPropagation()
  event?.preventDefault()
}

/** Convert enterprise -> KHDN , tương tự các đối tượng KH khác */
export const CUSTOMER_TYPE_LABEL: { [key: string]: string } = {
  enterprise: 'KHDN',
  'house-hold': 'HKD',
  personal: 'CN'
}

/** Role admin trong admin-portal */
export const ADMIN_PORTAL_ROLE = 9

/** Role admin trong partner-portal */
export const DEV_PORTAL_ADMIN_ROLE = 4

// TODO: Sử dụng để check môi trường production, xóa sau khi hoàn thiện các tính năng
export const isProduction = (API_ROOT as string) === 'https://onesme.vn'
