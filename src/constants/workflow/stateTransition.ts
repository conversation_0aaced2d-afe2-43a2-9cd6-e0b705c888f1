// Constants for state-transition

import type { FilterOption } from '@/views/order-management/common/SelectFilterPopup'

// Tab keys của DetailTransitionView
export const TAB_KEYS = {
  OVERVIEW: 'overview',
  HISTORY: 'history',
  APPLIED: 'applied'
} as const

export type TabKey = (typeof TAB_KEYS)[keyof typeof TAB_KEYS]

// Danh sách loại trạng thái
export const STATE_TRANSITION_TYPES = [
  { label: 'Completed', value: 1 },
  { label: 'Processing', value: 2 },
  { label: 'Pending', value: 3 },
  { label: 'Cancelled', value: 4 }
]

// G<PERSON><PERSON> lập danh sách tên trạng thái đã tồn tại để kiểm tra trùng
export const EXISTING_STATE_NAMES = {
  physical: ['Đang giao hàng', 'Đã giao hàng'],
  digital: ['Đang xử lý', 'Đã xử lý'],
  service: ['Đang cung cấp', '<PERSON><PERSON>n thành']
}

// Màu mặc định cho trạng thái mới
export const DEFAULT_STATE_COLOR = '#8B5CF6'

// Danh sách màu đề xuất
export const SUGGESTED_COLORS = [
  '#57A184', // Xanh lá
  '#2A6AEB', // Xanh dương
  '#EB4542', // Đỏ
  '#F59E0B', // Cam
  '#8B5CF6', // Tím
  '#EC4899', // Hồng
  '#0EA5E9', // Xanh nhạt
  '#6B7280', // Xám
  '#000000' // Đen
]

// Mapping trạng thái sang màu
export const STATUS_COLOR_MAP: Record<string, string> = {
  New: 'bg-green-6',
  Active: 'bg-icon-info-default',
  Maintenance: 'bg-bg-error-default',
  Faulty: 'bg-bg-warning-default',
  Deactive: 'bg-icon-accent-orange'
}

// Mapping notification type
export const NOTIFICATION_TYPE_MAP: Record<string, string> = {
  EMAIL: 'Email',
  NOTIFICATION: 'Notification'
}

// Post Action Types
export const POST_ACTION_TYPE = {
  WEBHOOK: 'WEBHOOK',
  NOTIFICATION: 'NOTIFICATION'
} as const

// Notification Types
export const NOTIFICATION_TYPE = {
  EMAIL: 'EMAIL',
  NOTIFICATION: 'NOTIFICATION'
} as const

// Danh sách cột mặc định và cột tuỳ chỉnh
export const DEFAULT_COLUMNS_CONFIG = [
  { key: 'code', title: 'Mã trạng thái', disabled: true },
  { key: 'name', title: 'Tên trạng thái', disabled: true },
  { key: 'type', title: 'Loại trạng thái', disabled: true },
  { key: 'object', title: 'Đối tượng', disabled: true }
]

export const CUSTOM_COLUMNS_CONFIG = [
  {
    title: 'Mô tả',
    key: 'description'
  },
  {
    title: 'Trạng thái',
    key: 'status'
  },
  {
    title: 'Người tạo',
    key: 'creator'
  },
  {
    title: 'Thời gian cập nhật',
    key: 'modifiedAt'
  }
]

// Các option cho filter nâng cao
export const ADV_OBJECT_OPTIONS = [
  { label: 'Tất cả', value: 'all' },
  { label: 'Dùng chung', value: 'common' },
  { label: 'Hàng hoá vật lý', value: 'physical' },
  { label: 'Hàng hoá kỹ thuật số', value: 'digital' },
  { label: 'Sản phẩm dịch vụ', value: 'service' }
]

// Các tùy chọn checkbox cho bộ lọc tìm kiếm
export const SEARCH_CHECKBOX_OPTIONS = [
  { label: 'Mã trạng thái', key: 'isCode' },
  { label: 'Tên trạng thái', key: 'isName' },
  { label: 'Loại trạng thái', key: 'isType' }
]

export const STATE_OBJECT_CHECKBOX_OPTIONS = [
  { value: 'PHYSICAL', label: 'Hàng hóa vật lý' },
  { value: 'DIGITAL', label: 'Hàng hóa kỹ thuật số' },
  { value: 'SERVICE', label: 'Sản phẩm dịch vụ' }
]

export const OBJECT_CONFIG_CHECKBOX_OPTIONS = [
  { value: 'physical', label: 'Hàng hóa vật lý' },
  { value: 'digital', label: 'Hàng hóa kỹ thuật số' },
  { value: 'service', label: 'Sản phẩm dịch vụ' }
]

// Các tùy chọn checkbox cho bộ lọc tìm kiếm
export const SEARCH_CONFIG_CHECKBOX_OPTIONS = [{ label: 'Tên cấu hình', key: 'configName' }]

export const LIST_FILTER = [
  {
    label: 'Khoảng thời gian',
    value: 'startTime,endTime',
    type: 'rangePicker' as const
  },
  {
    label: 'Trạng thái',
    value: 'status',
    type: 'singleSelect' as const,
    listOptions: [
      { label: 'Hoạt động', value: 'ACTIVE' },
      { label: 'Không hoạt động', value: 'INACTIVE' }
    ]
  }
]

export const DEFAULT_FILTER_PARAM = {
  startTime: undefined,
  endTime: undefined,
  status: undefined,
  objectTypes: 'ALL',
  applyAll: undefined
}

export const CLASSIFICATION_NAME = {
  DIGITAL: 'Hàng hóa kỹ thuật số',
  SERVICE: 'Sản phẩm dịch vụ',
  PHYSICAL: 'Hàng hóa vật lý'
}

export const DEFAULT_COLOR = '#645FEC'

export const DEFAULT_QUERY_PARAMS = {
  search: '',
  isName: 1,
  isCode: 1,
  lstTypeId: null,
  lstTriggerType: '',
  lstTriggerRole: '',
  postActionTypes: '',
  startTime: '',
  endTime: '',
  isStateApplyAll: ''
}

export const STATE_TYPE_MAP: Record<string, string> = {
  initial: 'Initial',
  intermediate: 'In_progress',
  final: 'Completed'
}

export const TRIGGER_TYPE_MAP: Record<string, string> = {
  MANUAL: 'Manual',
  API: 'API Call',
  WEBHOOK: 'Webhook',
  SCHEDULE: 'Lịch trình',
  RULE_ENGINE: 'Rule Engine'
}

// region const filter Lịch sử cấu hình
export const HISTORY_FILTER = [
  {
    label: 'Tên cấu hình',
    key: 'isName'
  },
  {
    label: 'Mô tả hoạt động',
    key: 'isChangeSummary'
  },
  {
    label: 'Người thực hiện',
    key: 'isActor'
  }
]

export const VERSION_FILTER = [
  {
    label: 'Tất cả',
    value: 'ALL'
  },
  {
    label: 'Cũ nhất',
    value: 'OLDEST'
  },
  {
    label: 'Mới nhất',
    value: 'LATEST'
  }
]
// endregion

// region const filter Bảng cấu hình chuyển đổi trạng thái
// Định nghĩa các cột có thể hiển thị trong bảng
export const TABLE_CHECKBOX_OPTIONS = [
  { label: 'STT', value: 'index' },
  { label: 'Tên trạng thái', value: 'name' },
  { label: 'Tên hiển thị', value: 'displayName' },
  { label: 'Mã trạng thái', value: 'code' },
  { label: 'Loại trạng thái', value: 'type' },
  { label: 'Trạng thái tiền nhiệm', value: 'previousState' },
  { label: 'Trigger', value: 'triggers' },
  { label: 'Điều kiện chuyển trạng thái', value: 'condition' },
  { label: 'Vai trò cập nhật', value: 'updateRole' },
  { label: 'Hành động hệ thống', value: 'systemAction' }
]

export const STATE_SEARCH_CHECKBOX_OPTIONS = [
  { label: 'Mã trạng thái', key: 'isCode' },
  { label: 'Tên trạng thái', key: 'isName' }
]

// Cấu hình các filter options
export const FILTER_OPTIONS: FilterOption[] = [
  {
    name: 'lstTriggerType',
    label: 'Trigger cập nhật',
    type: 'select',
    multiple: true,
    options: [
      { label: 'Manual', value: 'MANUAL' },
      { label: 'API Call', value: 'API' },
      { label: 'Webhook', value: 'WEBHOOK' },
      { label: 'Rule Engine', value: 'RULE_ENGINE' },
      { label: 'Lịch trình', value: 'SCHEDULE' }
    ]
  },
  {
    name: 'postActionTypes',
    label: 'Hành động hệ thống',
    type: 'select',
    multiple: true,
    options: [
      { label: 'Gửi webhook để hệ thống khác', value: 'WEBHOOK' },
      { label: 'Gửi thông báo', value: 'NOTIFICATION' }
    ]
  },
  {
    name: 'timeRange',
    label: 'Khoảng thời gian',
    type: 'rangePicker',
    value: 'startTime,endTime',
    format: 'dd/mm/yyyy'
  },
  {
    name: 'isStateApplyAll',
    label: 'Dùng chung',
    type: 'singleSelect',
    options: [
      { label: 'Dùng chung', value: 'YES' },
      { label: 'Không dùng chung', value: 'NO' }
    ]
  }
]

// Options cho vai trò cập nhật
export const TRIGGER_ROLE_OPTIONS = [
  { label: 'Admin', value: 'ADMIN' },
  { label: 'Đối tác', value: 'PARTNER' }
]

// Configuration Section Constants
export const DEFAULT_SEARCH_FILTERS = {
  searchValue: '',
  checkedFilter: {
    isCode: true,
    isName: true
  },
  lstTypeId: [],
  lstTriggerRole: [],
  lstTriggerType: [],
  postActionTypes: [],
  startTime: '',
  endTime: '',
  isStateApplyAll: ''
}

export const DEFAULT_SELECTED_COLUMNS = ['index', 'name', 'displayName', 'type', 'previousState', 'triggers']

// endregion
