// <PERSON><PERSON> liệu mẫu cho các trạng thái
export const MOCK_STATES = [
  { id: '1', name: 'Khởi tạo', code: 'INIT', type: 'initial' },
  { id: '2', name: '<PERSON><PERSON> xử lý', code: 'PROCESSING', type: 'intermediate' },
  { id: '3', name: 'Chờ phê duyệt', code: 'PENDING', type: 'intermediate' },
  { id: '4', name: 'Đã phê duyệt', code: 'APPROVED', type: 'intermediate' },
  { id: '5', name: '<PERSON><PERSON><PERSON> thành', code: 'COMPLETED', type: 'final' },
  { id: '6', name: 'Hủy', code: 'CANCELLED', type: 'final' }
]

// Các loại trạng thái
export const STATE_TYPES = [
  { label: 'Trạng thái khởi tạo', value: 'initial' },
  { label: 'Trạng thái trung gian', value: 'intermediate' },
  { label: 'Trạng thái kết thúc', value: 'final' }
]

// <PERSON><PERSON><PERSON> loại trigger
export const TRIGGER_TYPES = [
  { label: 'Manual', value: 'manual' },
  { label: 'API call', value: 'api' },
  { label: 'Webhook', value: 'webhook' },
  { label: 'Lịch trình', value: 'schedule' },
  { label: 'Rule engine', value: 'rule' }
]

// Các loại hành động
export const ACTION_TYPES = [
  { label: 'Gửi email', value: 'email' },
  { label: 'Gửi thông báo', value: 'notification' },
  { label: 'Webhook', value: 'webhook' },
  { label: 'API call', value: 'api' }
]
