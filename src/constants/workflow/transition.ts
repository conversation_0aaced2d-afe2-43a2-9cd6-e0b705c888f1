// Constants cho đối tượng và danh mục
export const TARGET_OBJECTS = [
  { label: 'Hàng hóa vật lý', value: 'PHYSICAL' },
  { label: 'Sản phẩm dịch vụ', value: 'SERVICE' },
  { label: 'Hàng hóa kỹ thuật số', value: 'DIGITAL' }
]

// Options cho checkbox tìm kiếm trạng thái
export const SEARCH_STATE_CHECKBOX_OPTIONS = [
  { label: 'Mã trạng thái', value: 'isCode' },
  { label: 'Tên trạng thái', value: 'isName' },
  { label: 'Loại trạng thái', value: 'isType' }
]

// Options cho checkbox tìm kiếm đơn hàng
export const ORDER_SEARCH_CHECKBOX_OPTIONS = [
  { label: 'Mã đơn hàng', key: 'isCartCode' },
  { label: 'Tên sản phẩm dịch vụ', key: 'isServiceName' },
  { label: 'Tên khách hàng', key: 'isCustomerName' },
  { label: 'Nhà cung cấp', key: 'isProviderName' }
]

// Options cho checkbox đối tượng
export const TRANSITION_OBJECT_CHECKBOX_OPTIONS = [
  {
    value: 'PHYSICAL_GOODS',
    label: 'Hàng hoá vật lý'
  },
  {
    value: 'DIGITAL_GOODS',
    label: 'Hàng hoá kỹ thuật số'
  },
  {
    value: 'SERVICE',
    label: 'Sản phẩm dịch vụ'
  }
]

// Mẫu dữ liệu danh mục, sẽ được thay thế bằng API thực tế
export const CATEGORIES_MAP: Record<string, { label: string; value: string }[]> = {
  physical: [
    { label: 'Điện thoại', value: 'phone' },
    { label: 'Máy tính', value: 'computer' },
    { label: 'Thiết bị mạng', value: 'network' }
  ],
  service: [
    { label: 'Dịch vụ CNTT', value: 'it-service' },
    { label: 'Dịch vụ viễn thông', value: 'telecom-service' }
  ],
  digital: [
    { label: 'Phần mềm', value: 'software' },
    { label: 'Nội dung số', value: 'digital-content' }
  ]
}

// Mock data cho bảng trạng thái
export const STATE_TABLE_DATA = [
  {
    key: '1',
    index: 1,
    name: 'Chờ xuất kho',
    displayName: 'Đã gửi yêu cầu xuất kho',
    code: 'Pending_shipping',
    type: 'Pending',
    previousState: '-',
    trigger: 'Manual',
    condition: '-',
    color: '#645FEC'
  },
  {
    key: '2',
    index: 2,
    name: 'Đang giao hàng',
    displayName: 'Đang chuẩn bị hàng trong kho',
    code: 'In_transit',
    type: 'In_progress',
    previousState: 'Pending_shipping',
    trigger: 'Manual',
    condition: 'Checklist.confirm = true',
    color: '#2A6AEB'
  },
  {
    key: '3',
    index: 3,
    name: 'Đã giao hàng',
    displayName: 'Đã xuất kho sẵn sàng giao hàng',
    code: 'Delivered',
    type: 'Completed',
    previousState: 'In_transit',
    trigger: 'Api_webhook',
    condition: '-',
    color: '#14C780'
  },
  {
    key: '4',
    index: 4,
    name: 'Chờ lắp đặt',
    displayName: 'Chờ giao hàng',
    code: 'Waiting_install',
    type: 'Pending',
    previousState: 'Delivered',
    trigger: 'Api_webhook',
    condition: 'Delivery_status = "delivered"',
    color: '#EB4542'
  },
  {
    key: '5',
    index: 5,
    name: 'Đang lắp đặt',
    displayName: 'Đang giao hàng',
    code: 'In_installation',
    type: 'In_progress',
    previousState: 'Waiting_install',
    trigger: 'Manual',
    condition: 'Installation_status = "ok"',
    color: '#FFB200'
  },
  {
    key: '6',
    index: 6,
    name: 'Lắp đặt thành công',
    displayName: 'Chờ lắp đặt',
    code: 'Installed',
    type: 'Completed',
    previousState: 'In_installation',
    trigger: 'Api_webhook',
    condition: '-',
    color: '#A5FF0E'
  }
]

// Mock data đơn hàng áp dụng
export const ORDER_APPLY_CONFIGURATION_TABLE_DATA = [
  {
    key: '1',
    index: 1,
    time: '01/02/2025 - 14:00',
    name: 'Đơn hàng gói bundling Mã DH123',
    result: 'Chờ lắp đặt',
    code: 'DH123',
    customerName: 'Khách hàng 1',
    providerName: 'Cung cấp 1'
  },
  {
    key: '2',
    index: 2,
    time: '04/02/2025 - 17:00',
    name: 'Đơn hàng gói bundling Mã DH123',
    result: 'Thành công',
    code: 'DH123',
    customerName: 'Khách hàng 1',
    providerName: 'Cung cấp 1'
  },
  {
    key: '3',
    index: 3,
    time: '01/02/2025 - 14:00',
    name: 'Đơn hàng gói bundling Mã DH123',
    result: 'Thành công',
    code: 'DH123',
    customerName: 'Khách hàng 1',
    providerName: 'Cung cấp 1'
  },
  {
    key: '4',
    index: 4,
    time: '02/02/2025 - 15:30',
    name: 'Đơn hàng gói bundling Mã DH123',
    result: 'Thành công',
    code: 'DH123',
    customerName: 'Khách hàng 1',
    providerName: 'Cung cấp 1'
  },
  {
    key: '5',
    index: 5,
    time: '03/02/2025 - 16:45',
    name: 'Đơn hàng gói bundling Mã DH123',
    result: 'Thành công',
    code: 'DH123',
    customerName: 'Khách hàng 1',
    providerName: 'Cung cấp 1'
  },
  {
    key: '6',
    index: 6,
    time: '01/02/2025 - 14:00',
    name: 'Đơn hàng gói bundling Mã DH123',
    result: 'Thành công',
    code: 'DH123',
    customerName: 'Khách hàng 1',
    providerName: 'Cung cấp 1'
  },
  {
    key: '7',
    index: 7,
    time: '01/02/2025 - 14:00',
    name: 'Đơn hàng gói bundling Mã DH123',
    result: 'Thành công',
    code: 'DH123',
    customerName: 'Khách hàng 1',
    providerName: 'Cung cấp 1'
  },
  {
    key: '8',
    index: 8,
    time: '01/02/2025 - 14:00',
    name: 'Đơn hàng gói bundling Mã DH123',
    result: 'Thành công',
    code: 'DH123',
    customerName: 'Khách hàng 1',
    providerName: 'Cung cấp 1'
  }
]

// Mock data cho thông tin chung
export const GENERAL_INFO = {
  name: 'Cấu hình chuyển đổi trạng thái Thiết bị',
  targetObject: 'Hàng hoá vật lý',
  category: 'Camera, Cảm biến nhiệt',
  product: 'Camera, Cảm biến nhiệt',
  description: 'Mô tả quy trình'
}

export const INPUT_PARAMETERS = [
  { label: '$SUBSCRIPTION_CODE', value: '$SUBSCRIPTION_CODE' },
  { label: '$PROVINCE_CODE', value: '$PROVINCE_CODE' },
  { label: '$CUSTOM_FIELDS', value: '$CUSTOM_FIELDS' }
]

// Danh sách phương thức kết nối
export const CONNECTION_METHODS = [
  { value: 'GET', label: 'GET' },
  { value: 'POST', label: 'POST' },
  { value: 'PUT', label: 'PUT' },
  { value: 'PATCH', label: 'PATCH' },
  { value: 'DELETE', label: 'DELETE' }
]

// Danh sách phương thức xác thực
export const AUTH_METHODS = [
  { value: 'bearer_token', label: 'Bearer Token' },
  { value: 'api_key', label: 'API Key' },
  { value: 'no_auth', label: 'No Auth' }
]
