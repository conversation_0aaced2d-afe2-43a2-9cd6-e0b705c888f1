'use client'
import { styled } from 'styled-components'

import { Table, type TableProps } from 'antd'

export const BlueHeaderTable = styled(Table)<TableProps<any>>`
  .ant-table-thead > tr > th {
    background-color: #f2f4f9;
    color: #5e7699;
    padding-block: 12px;
    font-size: 12px;
    font-weight: 600;
  }
`

export const ServiceInfoTable = styled(Table)<TableProps<any>>`
  .ant-table-thead > tr > th {
    padding-block: 12px;
    font-size: 12px;
    font-weight: 600;
  }
  .ant-table-row .ant-table-row-expand-icon-cell {
    padding-inline: 0px !important;
  }
  .ant-table-row.tb-subscription .ant-table-cell {
    font-weight: 500;
  }
  .ant-table-row.tb-subscription .ant-table-cell:nth-last-child(2) {
    padding-inline: 0px;
  }
`

export const statusArray = [
  {
    value: 'FUTURE',
    label: 'Đang chờ',
    color: '#0070C4'
  },
  {
    value: 'IN_TRIAL',
    label: 'Dùng thử',
    color: '#B940F5'
  },
  {
    value: 'ACTIVE',
    label: 'Đang hoạt động',
    color: '#14C780'
  },
  {
    value: 'CANCELLED',
    label: 'Đã hủy',
    color: '#EB4542'
  },
  {
    value: 'CANCELED',
    label: 'Đã hủy',
    color: '#EB4542'
  },
  {
    value: 'NOT_EXTEND',
    label: 'Không gia hạn',
    color: '#FFB200'
  },
  {
    value: 'NON_RENEWING',
    label: 'Kết thúc',
    color: '#5E7699'
  }
]

export const orderServiceStatus: { [index: string]: any } = {
  0: {
    color: '#0070C4',
    label: 'Đặt hàng thành công',
    value: '0'
  },
  1: {
    color: '#FFB200',
    label: 'Tiếp nhận đơn hàng',
    value: '1'
  },
  2: {
    color: '#B940F5',
    label: 'Đang triển khai',
    value: '2'
  },
  3: {
    color: '#EB4542',
    label: 'Hủy đơn hàng',
    value: '4'
  },
  4: {
    color: '#14C780',
    label: 'Hoàn thành',
    value: '3'
  },
  5: {
    color: '#0070C4',
    label: 'Đặt hàng thành công',
    value: '5'
  }
}

export const cycleArray = [
  {
    value: 'DAILY',
    label: 'ngày'
  },
  {
    value: 'WEEKLY',
    label: 'tuần'
  },
  {
    value: 'MONTHLY',
    label: 'tháng'
  },
  {
    value: 'QUARTERLY',
    label: 'quý'
  },
  {
    value: 'YEARLY',
    label: 'năm'
  }
]

export const CREATE_SUB_SPDV = ['Gặp sự cố', 'Đã cài đặt', 'Đang cài đặt']

export const paymentStatusArray = [
  {
    color: '#54C5EB',
    label: 'Khởi tạo',
    value: 'INIT',
    index: 0
  },
  {
    color: '#F4BF1B',
    label: 'Chờ thanh toán',
    value: 'WAITING',
    index: 1
  },
  {
    color: '#5BB98E',
    label: 'Đã thanh toán',
    value: 'PAID',
    index: 2
  },
  {
    color: '#F77178',
    label: 'Thanh toán thất bại',
    value: 'FAILURE',
    index: 3
  },
  {
    color: '#CCCCCC',
    label: 'Quá hạn thanh toán',
    value: 'OUT_OF_DATE',
    index: 4
  }
]

export const PAYMENT_METHOD: { [index: string]: any } = {
  CREDIT: 0,
  VNPTPAY: 1,
  CASH: 2,
  VNPTPAY_QR: 3
}

export const PAYMENT_METHOD_STR = { BY_CASH: 'BY_CASH', VNPTPAY: 'VNPTPAY', VNPTPAY_QR: 'VNPTPAY_QR', UNSET: 'UNSET' }

export const paymentMethodText: Record<string, string> = {
  VNPTPAY: 'Thanh toán trả trước',
  BY_CASH: 'Thanh toán tiền mặt',
  VNPTPAY_QR: 'Thanh toán trả trước - QR Code'
}

export const SERVICE_STATUS = {
  CANCELLED: 'CANCELLED',
  FUTURE: 'FUTURE',
  IN_TRIAL: 'IN_TRIAL',
  ACTIVE: 'ACTIVE',
  NON_RENEWING: 'NON_RENEWING',
  NOT_EXTEND: 'NOT_EXTEND'
}

export const SETUP_TAX = 1

export const DEVICE_TYPE = {
  VARIANT: 5,
  SERVICE: 7
}

export const activityCodeLabel: { [index: string]: any } = {
  RENEW_PRICING_SPDV: 'Gia hạn',
  CHANGE_PRICING_SPDV: 'Đổi gói',
  UPDATE_PRICING_SPDV: 'Cập nhật',
  CANCEL_SPDV: 'Huỷ',
  CANCEL_SPDV_END_OF_PERIOD: 'Huỷ cuối chu kỳ'
}

export const statusLabel: { [index: string]: any } = {
  0: ' thất bại',
  1: ' thành công'
}

export const STATUS_EMPLOYEE = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE'
}

export const ADDRESS_TYPE = {
  HOME: 0,
  COMPANY: 1
}

export const OWNER_TYPE = {
  VNPT: 'VNPT',
  SAAS: 'SAAS',
  OTHER: 'OTHER',
  NONE: 'NONE'
}

export const OSSteps = [
  {
    key: 5,
    title: 'Đặt hàng thành công',
    icon: 'onedx-installed'
  },
  {
    key: 1,
    title: 'Tiếp nhận đơn hàng',
    icon: 'onedx-installed'
  },
  {
    key: 2,
    title: 'Đang triển khai',
    icon: 'onedx-installed'
  },
  {
    key: 3,
    title: 'Hủy đơn hàng',
    icon: 'onedx-circle-warning'
  },
  {
    key: 4,
    title: 'Hoàn thành',
    icon: 'onedx-installed'
  }
]

export const ComboSteps = [
  {
    key: 0,
    title: 'Đặt hàng thành công',
    icon: 'onedx-installed'
  },
  {
    key: 1,
    title: 'Tiếp nhận đơn hàng',
    icon: 'onedx-installed'
  },
  {
    key: 2,
    title: 'Đang triển khai',
    icon: 'onedx-installed'
  },
  {
    key: 3,
    title: 'Hủy đơn hàng',
    icon: 'onedx-installed'
  },
  {
    key: 4,
    title: 'Hoàn thành',
    icon: 'onedx-installed'
  }
]

export const ONSteps = [
  {
    key: 0,
    title: 'Đang cài đặt',
    icon: 'onedx-repair'
  },
  {
    key: 1,
    title: 'Đã cài đặt',
    icon: 'onedx-installed'
  },
  {
    key: 2,
    title: 'Gặp sự cố',
    icon: 'onedx-circle-warning'
  }
]

export const SIMSteps = [
  {
    key: 0,
    title: 'Tạo đơn',
    icon: 'onedx-edit'
  },
  {
    key: 1,
    title: 'Pending',
    icon: 'onedx-circle-warning'
  },
  {
    key: 2,
    title: 'Thất bại',
    icon: 'onedx-close-outlined'
  },
  {
    key: 3,
    title: 'Thành công',
    icon: 'onedx-installed'
  }
]

// Trạng thái đơn hàng Sim số
export const statusOrderIdToKeyMap: Record<number, number> = {
  5: 0, // Trạng thái tạo đơn
  1012: 0, // Tạo đơn
  1003: 1, // Pending
  1002: 2, // Thất bại
  1001: 3 // Thành công
}

export const getStepKeySIM = (statusOrderId?: number): number | undefined =>
  statusOrderId !== undefined ? statusOrderIdToKeyMap[statusOrderId] : undefined

export const INVOICE_STATUS = {
  INIT: 0,
  WAITING: 1,
  PAID: 2,
  FAILED: 3,
  OUT_DATE: 4
}

export const SIM_TYPE: Record<number, string> = {
  2: 'Sim vật lý',
  1: 'eSim'
}

export const SIM_SUB_TYPE: Record<number, string> = {
  21: 'Trả trước',
  20: 'Trả sau'
}

export const MIGRATION_SERVICE_TYPE = {
  SIM_KEM_GOI: 'SIM_KEM_GOI',
  DATA: 'DATA',
  TICH_HOP: 'TICH_HOP',
  INTERNET_TRUYEN_HINH: 'INTERNET_TRUYEN_HINH',
  INTERNET_CAP_QUANG: 'INTERNET_CAP_QUANG',
  INTERNET_TRUYEN_HINH_VA_DI_DONG: 'INTERNET_TRUYEN_HINH_VA_DI_DONG',
  MY_TV_IP_TV: 'MY_TV_IP_TV',
  UNSET: 'UNSET'
}

export enum Status {
  INITIAL = 0,
  PENDING_PAYMENT = 1,
  PAID = 2,
  PAYMENT_FAILED = 3,
  PAYMENT_EXPIRED = 4
}

export enum PaymentMethod {
  BANK_TRANSFER = 0,
  VNPTPAY = 1,
  BY_CASH = 2,
  VNPTPAY_QR = 3
}

export const SIM_SUB_PRICE: Record<number, number> = {
  21: 25000, // Giá sim trả trước
  20: 35000 // Giá sim trả sau
}

export const SIM_PRICE: Record<number, number> = {
  2: 12500, // Giá sim vật lý
  1: 25000 // Giá eSIM
}

export const INVOICE_TYPE = {
  OTHER: 'OTHER',
  NONE: 'NONE'
}
