export const productCatalogMobile = [
  {
    id: 'product_mobile_1',
    data: {
      image: {
        desktop: {
          imgSrc: 'https://placehold.co/600x400/E3F2FD/0277BD?text=D169G',
          width: '100%',
          height: 160,
          imageFit: 'fill',
          align: 'center'
        },
        tablet: {
          imgSrc: 'https://placehold.co/600x400/E3F2FD/0277BD?text=D169G',
          width: '100%',
          height: 160,
          imageFit: 'fill',
          align: 'center'
        },
        mobile: {
          imgSrc: 'https://placehold.co/600x400/E3F2FD/0277BD?text=D169G',
          width: '100%',
          height: 160,
          imageFit: 'fill',
          align: 'center'
        }
      },
      title: {
        desktop: {
          text: 'D169G',
          fontSize: 18,
          fontWeight: 700,
          className: 'font-cartoon text-lg'
        },
        tablet: {
          text: 'D169G',
          fontSize: 18,
          fontWeight: 700,
          className: 'font-cartoon text-lg'
        },
        mobile: {
          text: 'D169G',
          fontSize: 18,
          fontWeight: 700,
          className: 'font-cartoon text-lg'
        }
      },
      features: {
        desktop: {
          text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ 7GB/ngày (210GB/tháng)<br>✓ Miễn phí 2000 phút nội mạng<br>✓ Miễn phí 150 phút ngoại mạng</div>',
          fontSize: 14,
          color: '#6b7280',
          textAlign: 'left'
        },
        tablet: {
          text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ 7GB/ngày (210GB/tháng)<br>✓ Miễn phí 2000 phút nội mạng<br>✓ Miễn phí 150 phút ngoại mạng</div>',
          fontSize: 14,
          color: '#6b7280',
          textAlign: 'left'
        },
        mobile: {
          text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ 7GB/ngày (210GB/tháng)<br>✓ Miễn phí 2000 phút nội mạng<br>✓ Miễn phí 150 phút ngoại mạng</div>',
          fontSize: 14,
          color: '#6b7280',
          textAlign: 'left'
        }
      },
      price: {
        desktop: {
          text: '169.000đ<span class="text-base font-normal">/tháng</span>',
          className: 'text-xl font-bold text-fun-dark mt-auto pt-2'
        },
        tablet: {
          text: '169.000đ<span class="text-base font-normal">/tháng</span>',
          className: 'text-xl font-bold text-fun-dark mt-auto pt-2'
        },
        mobile: {
          text: '169.000đ<span class="text-base font-normal">/tháng</span>',
          className: 'text-xl font-bold text-fun-dark mt-auto pt-2'
        }
      },
      detailBtn: {
        desktop: {
          text: 'Chi tiết',
          fontSize: 14,
          fontWeight: 600,
          color: '#2a2a2a',
          buttonType: 'primary',
          buttonBackgroundColor: '#e0e0e0',
          border: '2px solid #2a2a2a',
          radius: 12,
          padding: { top: 8, bottom: 8, left: 16, right: 16 },
          size: 'middle',
          width: '100%'
        },
        tablet: {
          text: 'Chi tiết',
          fontSize: 14,
          fontWeight: 600,
          color: '#2a2a2a',
          buttonType: 'primary',
          buttonBackgroundColor: '#e0e0e0',
          border: '2px solid #2a2a2a',
          radius: 12,
          padding: { top: 8, bottom: 8, left: 16, right: 16 },
          size: 'middle',
          width: '100%'
        },
        mobile: {
          text: 'Chi tiết',
          fontSize: 14,
          fontWeight: 600,
          color: '#2a2a2a',
          buttonType: 'primary',
          buttonBackgroundColor: '#e0e0e0',
          border: '2px solid #2a2a2a',
          radius: 12,
          padding: { top: 8, bottom: 8, left: 16, right: 16 },
          size: 'middle',
          width: '100%'
        }
      },
      buyBtn: {
        desktop: {
          text: 'Mua ngay',
          fontSize: 14,
          fontWeight: 600,
          color: '#2a2a2a',
          buttonType: 'primary',
          buttonBackgroundColor: '#ffd600',
          border: '2px solid #2a2a2a',
          boxShadow: '2px 2px 0 #2a2a2a',
          radius: 12,
          padding: { top: 8, bottom: 8, left: 16, right: 16 },
          size: 'middle',
          width: '100%',
          className: 'btn-cartoon-primary'
        },
        tablet: {
          text: 'Mua ngay',
          fontSize: 14,
          fontWeight: 600,
          color: '#2a2a2a',
          buttonType: 'primary',
          buttonBackgroundColor: '#ffd600',
          border: '2px solid #2a2a2a',
          boxShadow: '2px 2px 0 #2a2a2a',
          radius: 12,
          padding: { top: 8, bottom: 8, left: 16, right: 16 },
          size: 'middle',
          width: '100%',
          className: 'btn-cartoon-primary'
        },
        mobile: {
          text: 'Mua ngay',
          fontSize: 14,
          fontWeight: 600,
          color: '#2a2a2a',
          buttonType: 'primary',
          buttonBackgroundColor: '#ffd600',
          border: '2px solid #2a2a2a',
          boxShadow: '2px 2px 0 #2a2a2a',
          radius: 12,
          padding: { top: 8, bottom: 8, left: 16, right: 16 },
          size: 'middle',
          width: '100%',
          className: 'btn-cartoon-primary'
        }
      }
    }
  },
  {
    id: 'product_mobile_2',
    data: {
      image: {
        desktop: {
          imgSrc: 'https://placehold.co/600x400/E3F2FD/0277BD?text=BIG120',
          width: '100%',
          height: 160,
          imageFit: 'fill',
          align: 'center'
        },
        tablet: {
          imgSrc: 'https://placehold.co/600x400/E3F2FD/0277BD?text=BIG120',
          width: '100%',
          height: 160,
          imageFit: 'fill',
          align: 'center'
        },
        mobile: {
          imgSrc: 'https://placehold.co/600x400/E3F2FD/0277BD?text=BIG120',
          width: '100%',
          height: 160,
          imageFit: 'fill',
          align: 'center'
        }
      },
      title: {
        desktop: {
          text: 'BIG120',
          fontSize: 18,
          fontWeight: 700,
          className: 'font-cartoon text-lg'
        },
        tablet: {
          text: 'BIG120',
          fontSize: 18,
          fontWeight: 700,
          className: 'font-cartoon text-lg'
        },
        mobile: {
          text: 'BIG120',
          fontSize: 18,
          fontWeight: 700,
          className: 'font-cartoon text-lg'
        }
      },
      features: {
        desktop: {
          text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ 2GB/ngày (60GB/tháng)<br>✓ Miễn phí data xem MyTV</div>',
          fontSize: 14,
          color: '#6b7280',
          textAlign: 'left'
        },
        tablet: {
          text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ 2GB/ngày (60GB/tháng)<br>✓ Miễn phí data xem MyTV</div>',
          fontSize: 14,
          color: '#6b7280',
          textAlign: 'left'
        },
        mobile: {
          text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ 2GB/ngày (60GB/tháng)<br>✓ Miễn phí data xem MyTV</div>',
          fontSize: 14,
          color: '#6b7280',
          textAlign: 'left'
        }
      },
      price: {
        desktop: {
          text: '120.000đ<span class="text-base font-normal">/tháng</span>',
          className: 'text-xl font-bold text-fun-dark mt-auto pt-2'
        },
        tablet: {
          text: '120.000đ<span class="text-base font-normal">/tháng</span>',
          className: 'text-xl font-bold text-fun-dark mt-auto pt-2'
        },
        mobile: {
          text: '120.000đ<span class="text-base font-normal">/tháng</span>',
          className: 'text-xl font-bold text-fun-dark mt-auto pt-2'
        }
      },
      detailBtn: {
        desktop: {
          text: 'Chi tiết',
          fontSize: 14,
          fontWeight: 600,
          color: '#2a2a2a',
          buttonType: 'primary',
          buttonBackgroundColor: '#e0e0e0',
          border: '2px solid #2a2a2a',
          radius: 8,
          padding: { top: 8, bottom: 8, left: 16, right: 16 },
          size: 'middle',
          width: '100%'
        },
        tablet: {
          text: 'Chi tiết',
          fontSize: 14,
          fontWeight: 600,
          color: '#2a2a2a',
          buttonType: 'primary',
          buttonBackgroundColor: '#e0e0e0',
          border: '2px solid #2a2a2a',
          radius: 8,
          padding: { top: 8, bottom: 8, left: 16, right: 16 },
          size: 'middle',
          width: '100%'
        },
        mobile: {
          text: 'Chi tiết',
          fontSize: 14,
          fontWeight: 600,
          color: '#2a2a2a',
          buttonType: 'primary',
          buttonBackgroundColor: '#e0e0e0',
          border: '2px solid #2a2a2a',
          radius: 8,
          padding: { top: 8, bottom: 8, left: 16, right: 16 },
          size: 'middle',
          width: '100%'
        }
      },
      buyBtn: {
        desktop: {
          text: 'Mua ngay',
          fontSize: 14,
          fontWeight: 600,
          color: '#2a2a2a',
          buttonType: 'primary',
          buttonBackgroundColor: '#ffd600',
          border: '2px solid #2a2a2a',
          boxShadow: '2px 2px 0 #2a2a2a',
          radius: 12,
          padding: { top: 8, bottom: 8, left: 16, right: 16 },
          size: 'middle',
          width: '100%',
          className: 'btn-cartoon-primary'
        },
        tablet: {
          text: 'Mua ngay',
          fontSize: 14,
          fontWeight: 600,
          color: '#2a2a2a',
          buttonType: 'primary',
          buttonBackgroundColor: '#ffd600',
          border: '2px solid #2a2a2a',
          boxShadow: '2px 2px 0 #2a2a2a',
          radius: 12,
          padding: { top: 8, bottom: 8, left: 16, right: 16 },
          size: 'middle',
          width: '100%',
          className: 'btn-cartoon-primary'
        },
        mobile: {
          text: 'Mua ngay',
          fontSize: 14,
          fontWeight: 600,
          color: '#2a2a2a',
          buttonType: 'primary',
          buttonBackgroundColor: '#ffd600',
          border: '2px solid #2a2a2a',
          boxShadow: '2px 2px 0 #2a2a2a',
          radius: 12,
          padding: { top: 8, bottom: 8, left: 16, right: 16 },
          size: 'middle',
          width: '100%',
          className: 'btn-cartoon-primary'
        }
      }
    }
  },
  {
    id: 'product_mobile_3',
    data: {
      image: {
        desktop: {
          imgSrc: 'https://placehold.co/600x400/E3F2FD/0277BD?text=D15G',
          width: '100%',
          height: 160,
          imageFit: 'fill',
          align: 'center'
        },
        tablet: {
          imgSrc: 'https://placehold.co/600x400/E3F2FD/0277BD?text=D15G',
          width: '100%',
          height: 160,
          imageFit: 'fill',
          align: 'center'
        },
        mobile: {
          imgSrc: 'https://placehold.co/600x400/E3F2FD/0277BD?text=D15G',
          width: '100%',
          height: 160,
          imageFit: 'fill',
          align: 'center'
        }
      },
      title: {
        desktop: {
          text: 'D15G',
          fontSize: 18,
          fontWeight: 700,
          className: 'font-cartoon text-lg'
        },
        tablet: {
          text: 'D15G',
          fontSize: 18,
          fontWeight: 700,
          className: 'font-cartoon text-lg'
        },
        mobile: {
          text: 'D15G',
          fontSize: 18,
          fontWeight: 700,
          className: 'font-cartoon text-lg'
        }
      },
      features: {
        desktop: {
          text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ 500MB/ngày (15GB/tháng)<br>✓ Gói cước siêu tiết kiệm</div>',
          fontSize: 14,
          color: '#6b7280',
          textAlign: 'left'
        },
        tablet: {
          text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ 500MB/ngày (15GB/tháng)<br>✓ Gói cước siêu tiết kiệm</div>',
          fontSize: 14,
          color: '#6b7280',
          textAlign: 'left'
        },
        mobile: {
          text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ 500MB/ngày (15GB/tháng)<br>✓ Gói cước siêu tiết kiệm</div>',
          fontSize: 14,
          color: '#6b7280',
          textAlign: 'left'
        }
      }, // Không có promotion cho product3
      price: {
        desktop: {
          text: '70.000đ<span class="text-base font-normal">/tháng</span>',
          className: 'text-xl font-bold text-fun-dark mt-auto pt-2'
        },
        tablet: {
          text: '70.000đ<span class="text-base font-normal">/tháng</span>',
          className: 'text-xl font-bold text-fun-dark mt-auto pt-2'
        },
        mobile: {
          text: '70.000đ<span class="text-base font-normal">/tháng</span>',
          className: 'text-xl font-bold text-fun-dark mt-auto pt-2'
        }
      },
      detailBtn: {
        desktop: {
          text: 'Chi tiết',
          fontSize: 14,
          fontWeight: 600,
          color: '#2a2a2a',
          buttonType: 'primary',
          buttonBackgroundColor: '#e0e0e0',
          border: '2px solid #2a2a2a',
          radius: 8,
          padding: { top: 8, bottom: 8, left: 16, right: 16 },
          size: 'middle',
          width: '100%'
        },
        tablet: {
          text: 'Chi tiết',
          fontSize: 14,
          fontWeight: 600,
          color: '#2a2a2a',
          buttonType: 'primary',
          buttonBackgroundColor: '#e0e0e0',
          border: '2px solid #2a2a2a',
          radius: 8,
          padding: { top: 8, bottom: 8, left: 16, right: 16 },
          size: 'middle',
          width: '100%'
        },
        mobile: {
          text: 'Chi tiết',
          fontSize: 14,
          fontWeight: 600,
          color: '#2a2a2a',
          buttonType: 'primary',
          buttonBackgroundColor: '#e0e0e0',
          border: '2px solid #2a2a2a',
          radius: 8,
          padding: { top: 8, bottom: 8, left: 16, right: 16 },
          size: 'middle',
          width: '100%'
        }
      },
      buyBtn: {
        desktop: {
          text: 'Mua ngay',
          fontSize: 14,
          fontWeight: 600,
          color: '#2a2a2a',
          buttonType: 'primary',
          buttonBackgroundColor: '#ffd600',
          border: '2px solid #2a2a2a',
          boxShadow: '2px 2px 0 #2a2a2a',
          radius: 12,
          padding: { top: 8, bottom: 8, left: 16, right: 16 },
          size: 'middle',
          width: '100%',
          className: 'btn-cartoon-primary'
        },
        tablet: {
          text: 'Mua ngay',
          fontSize: 14,
          fontWeight: 600,
          color: '#2a2a2a',
          buttonType: 'primary',
          buttonBackgroundColor: '#ffd600',
          border: '2px solid #2a2a2a',
          boxShadow: '2px 2px 0 #2a2a2a',
          radius: 12,
          padding: { top: 8, bottom: 8, left: 16, right: 16 },
          size: 'middle',
          width: '100%',
          className: 'btn-cartoon-primary'
        },
        mobile: {
          text: 'Mua ngay',
          fontSize: 14,
          fontWeight: 600,
          color: '#2a2a2a',
          buttonType: 'primary',
          buttonBackgroundColor: '#ffd600',
          border: '2px solid #2a2a2a',
          boxShadow: '2px 2px 0 #2a2a2a',
          radius: 12,
          padding: { top: 8, bottom: 8, left: 16, right: 16 },
          size: 'middle',
          width: '100%',
          className: 'btn-cartoon-primary'
        }
      }
    }
  },
  {
    id: 'product_mobile_4',
    data: {
      image: {
        desktop: {
          imgSrc: 'https://placehold.co/600x400/E3F2FD/0277BD?text=SIM+SỐ',
          width: '100%',
          height: 160,
          imageFit: 'fill',
          align: 'center'
        },
        tablet: {
          imgSrc: 'https://placehold.co/600x400/E3F2FD/0277BD?text=SIM+SỐ',
          width: '100%',
          height: 128,
          imageFit: 'fill',
          align: 'center'
        },
        mobile: {
          imgSrc: 'https://placehold.co/600x400/E3F2FD/0277BD?text=SIM+SỐ',
          width: '100%',
          height: 96,
          imageFit: 'fill',
          align: 'center'
        }
      },
      title: {
        desktop: {
          text: 'SIM Số Đẹp',
          fontSize: 18,
          fontWeight: 700,
          className: 'font-cartoon text-lg'
        },
        tablet: {
          text: 'SIM Số Đẹp',
          fontSize: 18,
          fontWeight: 700,
          className: 'font-cartoon text-lg'
        },
        mobile: {
          text: 'SIM Số Đẹp',
          fontSize: 18,
          fontWeight: 700,
          className: 'font-cartoon text-lg'
        }
      },
      features: {
        desktop: {
          text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Kho sim hàng triệu số<br>✓ Chọn số theo yêu cầu<br>✓ Hỗ trợ đăng ký chính chủ</div>',
          fontSize: 14,
          color: '#6b7280',
          textAlign: 'left'
        },
        tablet: {
          text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Kho sim hàng triệu số<br>✓ Chọn số theo yêu cầu<br>✓ Hỗ trợ đăng ký chính chủ</div>',
          fontSize: 14,
          color: '#6b7280',
          textAlign: 'left'
        },
        mobile: {
          text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Kho sim hàng triệu số<br>✓ Chọn số theo yêu cầu<br>✓ Hỗ trợ đăng ký chính chủ</div>',
          fontSize: 14,
          color: '#6b7280',
          textAlign: 'left'
        }
      },
      price: {
        desktop: {
          text: 'Liên hệ',
          className: 'text-xl font-bold text-fun-dark mt-auto pt-2'
        },
        tablet: {
          text: 'Liên hệ',
          className: 'text-xl font-bold text-fun-dark mt-auto pt-2'
        },
        mobile: {
          text: 'Liên hệ',
          className: 'text-xl font-bold text-fun-dark mt-auto pt-2'
        }
      },
      detailBtn: {
        desktop: {
          text: 'Chi tiết',
          fontSize: 14,
          fontWeight: 600,
          color: '#2a2a2a',
          buttonType: 'primary',
          buttonBackgroundColor: '#e0e0e0',
          border: '2px solid #2a2a2a',
          radius: 8,
          padding: { top: 8, bottom: 8, left: 16, right: 16 },
          size: 'middle',
          width: '100%'
        },
        tablet: {
          text: 'Chi tiết',
          fontSize: 14,
          fontWeight: 600,
          color: '#2a2a2a',
          buttonType: 'primary',
          buttonBackgroundColor: '#e0e0e0',
          border: '2px solid #2a2a2a',
          radius: 8,
          padding: { top: 8, bottom: 8, left: 16, right: 16 },
          size: 'middle',
          width: '100%'
        },
        mobile: {
          text: 'Chi tiết',
          fontSize: 14,
          fontWeight: 600,
          color: '#2a2a2a',
          buttonType: 'primary',
          buttonBackgroundColor: '#e0e0e0',
          border: '2px solid #2a2a2a',
          radius: 8,
          padding: { top: 8, bottom: 8, left: 16, right: 16 },
          size: 'middle',
          width: '100%'
        }
      },
      buyBtn: {
        desktop: {
          text: 'Mua ngay',
          fontSize: 14,
          fontWeight: 600,
          color: '#2a2a2a',
          buttonType: 'primary',
          buttonBackgroundColor: '#ffd600',
          border: '2px solid #2a2a2a',
          boxShadow: '2px 2px 0 #2a2a2a',
          radius: 8,
          padding: { top: 8, bottom: 8, left: 16, right: 16 },
          size: 'middle',
          width: '100%',
          className: 'btn-cartoon-primary'
        },
        tablet: {
          text: 'Mua ngay',
          fontSize: 14,
          fontWeight: 600,
          color: '#2a2a2a',
          buttonType: 'primary',
          buttonBackgroundColor: '#ffd600',
          border: '2px solid #2a2a2a',
          boxShadow: '2px 2px 0 #2a2a2a',
          radius: 8,
          padding: { top: 8, bottom: 8, left: 16, right: 16 },
          size: 'middle',
          width: '100%',
          className: 'btn-cartoon-primary'
        },
        mobile: {
          text: 'Mua ngay',
          fontSize: 14,
          fontWeight: 600,
          color: '#2a2a2a',
          buttonType: 'primary',
          buttonBackgroundColor: '#ffd600',
          border: '2px solid #2a2a2a',
          boxShadow: '2px 2px 0 #2a2a2a',
          radius: 8,
          padding: { top: 8, bottom: 8, left: 16, right: 16 },
          size: 'middle',
          width: '100%',
          className: 'btn-cartoon-primary'
        }
      }
    }
  }
]
