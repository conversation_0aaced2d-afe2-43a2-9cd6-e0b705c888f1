import type { ButtonSize } from 'antd/es/button'

import { RESET_MARGIN } from './common'
import { defaultImageProps } from './images'
import { defaultRowProps } from './row'

const defaultAffiliateRowProps = {
  ...defaultRowProps(),
  padding: { top: 40, bottom: 40, left: 0, right: 0 },
  colWidths: [3, 6, 3],
  backgroundColor: '#ffffff',
  backgroundType: 'COLOR',
  gap: 10,
  height: 520
}

const defaultButtonRowProps = {
  ...defaultRowProps(),
  colWidths: [5, 5, 2],
  backgroundColor: '#ffffff',
  backgroundType: '',
  gap: 3
}

const defaultImageAffiliateProps = {
  ...defaultImageProps(),
  width: 299,
  height: 420
}

const defaultTextHeadingProps = {
  text: 'Bán hàng cùng oneSME',
  fontSize: 32,
  fontWeight: 600,
  color: '#000000',
  textAlign: 'left'
}

const defaultTextProps = {
  text: 'Affiliate by oneSME là chương trình tiếp thị liên kết tập trung vào các sản ph<PERSON>m, d<PERSON><PERSON> v<PERSON> viễn thông, công nghệ thông tin (VT-CNTT) dành cho các SMEs, hộ kinh doanh và cá nhân của VNPT được giới thiệu trên nền tảng https://onesme.vn',
  fontSize: 14,
  fontWeight: 400,
  color: '#394867',
  textAlign: 'left'
}

const defaultColumnProps = {
  ...defaultRowProps(),
  justifyContent: 'center'
}

const defaultButtonProps = {
  size: 'large' as ButtonSize,
  color: '#ffffff',
  iconColor: '#000000',
  radius: 12,
  buttonType: 'primary',
  width: '100%'
}

export const defaultResponseAffiliateProps = {
  container: {
    height: 'auto',
    background: '#FFFFFF',
    padding: ['0', '0', '0', '0']
  },
  rowContainer: {
    desktop: defaultAffiliateRowProps,
    tablet: defaultAffiliateRowProps,
    mobile: {
      ...defaultAffiliateRowProps,
      height: 'auto',
      isBreakLine: true,
      padding: { top: 24, bottom: 24, left: 16, right: 16 }
    }
  },
  image1: {
    desktop: { ...defaultImageAffiliateProps, imgSrc: '/assets/images/pages/home/<USER>' },
    tablet: { ...defaultImageAffiliateProps, imgSrc: '/assets/images/pages/home/<USER>' },
    mobile: {
      ...defaultImageAffiliateProps,
      imgSrcMobile: '/assets/images/pages/home/<USER>',
      hide: true
    }
  },
  image2: {
    desktop: { ...defaultImageAffiliateProps, imgSrc: '/assets/images/pages/home/<USER>' },
    tablet: { ...defaultImageAffiliateProps, imgSrc: '/assets/images/pages/home/<USER>' },
    mobile: { ...defaultImageAffiliateProps, imgSrcMobile: '/assets/images/pages/home/<USER>' }
  },
  textHeading: {
    desktop: defaultTextHeadingProps,
    tablet: defaultTextHeadingProps,
    mobile: { ...defaultTextHeadingProps, fontSize: 24, textAlign: 'center' }
  },
  text: { desktop: defaultTextProps, tablet: defaultTextProps, mobile: { ...defaultTextProps, fontSize: 14 } },
  spacer1: { desktop: { height: 40 }, tablet: { height: 40 }, mobile: { height: 8 } },
  spacer2: { desktop: { height: 40 }, tablet: { height: 40 }, mobile: { height: 24 } },
  spacer3: { desktop: { height: 0 }, tablet: { height: 0 }, mobile: { height: 24 } },
  column: { desktop: defaultColumnProps, tablet: defaultColumnProps, mobile: defaultColumnProps },
  button1: {
    desktop: {
      ...defaultButtonProps,
      text: 'Tìm hiểu thêm',
      buttonBackgroundColor: '#0070C4',
      href: 'https://onesme.vn/home-page/affiliate'
    },
    tablet: {
      ...defaultButtonProps,
      text: 'Tìm hiểu thêm',
      buttonBackgroundColor: '#0070C4',
      href: 'https://onesme.vn/home-page/affiliate'
    },
    mobile: {
      ...defaultButtonProps,
      text: 'Tìm hiểu thêm',
      buttonBackgroundColor: '#0070C4',
      href: 'https://onesme.vn/home-page/affiliate',
      width: '100%',
      margin: { ...RESET_MARGIN, bottom: 8 }
    }
  },
  button2: {
    desktop: {
      ...defaultButtonProps,
      text: 'Đăng ký ngay',
      buttonBackgroundColor: '#EF9304',
      href: 'https://onesme.vn/affiliate/register'
    },
    tablet: {
      ...defaultButtonProps,
      text: 'Đăng ký ngay',
      buttonBackgroundColor: '#EF9304',
      href: 'https://onesme.vn/affiliate/register'
    },
    mobile: {
      ...defaultButtonProps,
      text: 'Đăng ký ngay',
      buttonBackgroundColor: '#EF9304',
      href: 'https://onesme.vn/affiliate/register',
      width: '100%'
    }
  },
  buttonRow: {
    desktop: defaultButtonRowProps,
    tablet: defaultButtonRowProps,
    mobile: { ...defaultButtonRowProps, hide: true }
  },
  buttonRowMobile: {
    desktop: { ...defaultButtonRowProps, hide: true },
    tablet: { ...defaultButtonRowProps, hide: true },
    mobile: { ...defaultButtonRowProps, isBreakLine: true }
  }
}
