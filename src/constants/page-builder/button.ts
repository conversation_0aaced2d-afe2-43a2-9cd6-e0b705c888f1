import type { ButtonCustomizeProps } from '@/types/page-builder'
import { DEFAULT_BASE_PROPS, FONTSIZE_DEFAULT } from './common'
import type { ScreenTypeValues } from '@/redux-store/slices/builderSlice'

export const createDefaultButtonProps = (isWorkplace: boolean) => {
  return (screenType: ScreenTypeValues): Required<ButtonCustomizeProps> => ({
    ...DEFAULT_BASE_PROPS,
    fontSize: FONTSIZE_DEFAULT[screenType],
    size: 'middle',
    buttonType: 'default',
    buttonAlign: 'start',
    radius: 4,
    href: '',
    hasIcon: false,
    icon: '',
    iconAlign: 'start',
    iconColor: '#000000',
    isButtonWorkplace: isWorkplace,
    width: '100%',
    hide: false,
    buttonBackgroundColor: '#2C3D94',
    color: '#2C3D94',
    border: '',
    className: '',
    boxShadow: '',
    containerClassName: ''
  })
}

export const defaultButtonProps = createDefaultButtonProps(false)

export const defaultResponseButtonProps = {
  desktop: defaultButtonProps('desktop'),
  tablet: defaultButtonProps('tablet'),
  mobile: defaultButtonProps('mobile')
}

export const BUTTON_STYLE_OPTIONS = [
  { value: 'primary', label: 'Fill' },
  { value: 'default', label: 'Outline' }
]

export const BUTTON_SIZE_OPTIONS = [
  { value: 'large', label: 'large' },
  { value: 'middle', label: 'medium' },
  { value: 'small', label: 'small' }
]
