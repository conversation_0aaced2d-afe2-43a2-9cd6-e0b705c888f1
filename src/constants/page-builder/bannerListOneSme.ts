import type { ScreenTypeValues } from '@/redux-store/slices/builderSlice'
import type { BannerItemProps, BannerListCustomizeProps } from '@/types/page-builder/bannerList'
import { DEFAULT_BASE_PROPS } from './common' // Reusable base properties

const defaultImagesBanners: BannerItemProps[] = [
  {
    id: 'image1',
    backgroundColor: '#f0f0f0',
    url: '',
    urlMobile: '',
    urlLibrary: '',
    urlLibraryMobile: '',
    imageSelectType: 'library',
    imageAlign: 'center',
    imageFit: 'fit',
    contentAlign: 'left',
    imgSrc: '',
    fontWeightTitle: 200,
    fontWeightDescription: 200,
    fontSizeTitle: 24,
    fontSizeDescription: 16,
    colorTitle: '#ffffff',
    colorDescription: '#ffffff',
    textAlignTitle: 'center',
    paddingButton: {
      top: 0,
      right: 0,
      bottom: 0,
      left: 0
    },
    marginButton: {
      top: 0,
      bottom: 0
    },
    titleText: 'Title',
    descriptionText: 'Description',
    buttonText: 'Button',
    buttonBackgroundColor: '#ffffff',
    buttonTextColor: '#ffffff',
    buttonStyle: 'default',
    buttonSize: 'medium',
    buttonRadius: 4,
    buttonFontSize: 14,
    buttonFontWeight: 500,
    hasIcon: false,
    icon: '',
    iconAlign: 'start',
    iconColor: '#ffffff',
    showTitle: {
      desktop: true,
      mobile: true,
      tablet: true
    },
    showDescription: {
      desktop: true,
      mobile: true,
      tablet: true
    },
    showButton: {
      desktop: true,
      mobile: true,
      tablet: true
    },
    href: '',
    isShow: {
      desktop: true,
      mobile: true,
      tablet: true
    },
    isAdded: true
  }
]

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const defaultBannerListProps = (screenType: ScreenTypeValues): Required<BannerListCustomizeProps> => {
  return {
    ...DEFAULT_BASE_PROPS,
    backgroundColor: '#EF9304',
    autoPlaySpeed: 5000,
    responsive: {
      desktop: true,
      tablet: true,
      mobile: true
    },
    width: '100%',
    hasButton: true,
    currentSlide: 0,
    buttonType: 'default', // add this property
    buttonAlign: 'center',
    size: 'large',
    href: '',
    hasIcon: false,
    icon: '',
    iconAlign: 'start',
    iconColor: '#ffffff',
    isButtonWorkplace: false,
    slidesPerSection: 1,
    images: defaultImagesBanners
  } as Required<BannerListCustomizeProps>
}

// const defaultResponsiveBannerListProps = {
//   desktop: defaultBannerListProps('desktop'),
//   tablet: defaultBannerListProps('tablet'),
//   mobile: defaultBannerListProps('mobile')
// }

const defaultOneSmeBannerListProps = (screenType: ScreenTypeValues): Required<BannerListCustomizeProps> => {
  return {
    ...defaultBannerListProps(screenType),
    images: [
      {
        id: 'image1',
        backgroundColor: '#DBEAFE',
        imageAlign: 'center',
        imageFit: 'fill',
        contentAlign: 'center',
        url: '',
        urlMobile: '',
        urlLibrary: '',
        urlLibraryMobile: '',
        imageSelectType: 'library',
        imgSrc: '',
        fontWeightTitle: 600,
        fontWeightDescription: 400,
        fontSizeTitle: 32,
        fontSizeDescription: 14,
        colorTitle: '#00529c',
        colorDescription: '#00529c',
        textAlignTitle: 'center',
        textAlignDescription: 'center',
        overlayColor: '#DBEAFE', // blue-100
        overlayOpacity: 0.6,
        paddingButton: {
          top: 10,
          right: 16,
          bottom: 10,
          left: 16
        },
        marginButton: {
          top: 10,
          bottom: 10
        },
        titleText: 'Vui Hè Cùng Gói Data VNPT',
        descriptionText: 'Lướt web thả ga, không lo về giá!',
        buttonText: 'Button',
        buttonBackgroundColor: '#ffffff',
        buttonTextColor: '#ffffff',
        buttonStyle: 'default',
        buttonSize: 'medium',
        buttonFontSize: 14,
        buttonFontWeight: 500,
        hasIcon: false,
        icon: '',
        iconAlign: 'start',
        iconColor: '#ffffff',
        showTitle: {
          desktop: true,
          mobile: true,
          tablet: true
        },
        showDescription: {
          desktop: true,
          mobile: true,
          tablet: true
        },
        showButton: {
          desktop: false,
          mobile: false,
          tablet: false
        },
        href: '',
        isShow: {
          desktop: true,
          mobile: true,
          tablet: true
        },
        isAdded: true
      },
      {
        id: 'image2',
        backgroundColor: '#FFEDD5',
        imageAlign: 'center',
        imageFit: 'fill',
        contentAlign: 'center',
        url: '',
        urlMobile: '',
        urlLibrary: '',
        urlLibraryMobile: '',
        imageSelectType: 'library',
        imgSrc: '',
        fontWeightTitle: 600,
        fontWeightDescription: 400,
        fontSizeTitle: 32,
        fontSizeDescription: 14,
        colorTitle: '#c2410c',
        colorDescription: '#c2410c',
        textAlignTitle: 'center',
        textAlignDescription: 'center',
        overlayColor: 'rgba(194, 65, 12, 0.8)', // orange-800
        overlayOpacity: 0.6,
        paddingButton: {
          top: 10,
          right: 16,
          bottom: 10,
          left: 16
        },
        marginButton: {
          top: 10,
          bottom: 10
        },
        titleText: 'Home Camera - An Toàn Cho Cả Gia Đình',
        descriptionText: 'Giám sát mọi lúc, an tâm mọi nơi.',
        buttonText: 'Button',
        buttonBackgroundColor: '#00529c',
        buttonTextColor: '#00529c',
        buttonStyle: 'default',
        buttonSize: 'medium',
        buttonFontSize: 14,
        buttonFontWeight: 500,
        hasIcon: false,
        icon: '',
        iconAlign: 'start',
        iconColor: '#00529c',
        showTitle: {
          desktop: true,
          mobile: true,
          tablet: true
        },
        showDescription: {
          desktop: true,
          mobile: true,
          tablet: true
        },
        showButton: {
          desktop: false,
          mobile: false,
          tablet: false
        },
        href: '',
        isShow: {
          desktop: true,
          mobile: true,
          tablet: true
        },
        isAdded: true
      },
      {
        id: 'image3',
        backgroundColor: '#DCFCE7',
        imageAlign: 'center',
        imageFit: 'fill',
        contentAlign: 'center',
        url: '',
        urlMobile: '',
        urlLibrary: '',
        urlLibraryMobile: '',
        imageSelectType: 'library',
        imgSrc: '',
        fontWeightTitle: 600,
        fontWeightDescription: 400,
        fontSizeTitle: 32,
        fontSizeDescription: 14,
        colorTitle: '#166534',
        colorDescription: '#166534',
        textAlignTitle: 'center',
        textAlignDescription: 'center',
        overlayColor: 'rgba(21, 128, 61, 0.8)', // green-800
        overlayOpacity: 0.6,
        paddingButton: {
          top: 10,
          right: 16,
          bottom: 10,
          left: 16
        },
        marginButton: {
          top: 10,
          bottom: 10
        },
        titleText: 'Mesh - Internet Không Gián Đoạn',
        descriptionText: 'Phủ sóng Wifi khắp mọi ngóc ngách.',
        buttonText: 'Button',
        buttonBackgroundColor: '#ffffff',
        buttonTextColor: '#ffffff',
        buttonStyle: 'default',
        buttonSize: 'medium',
        buttonFontSize: 14,
        buttonFontWeight: 500,
        hasIcon: false,
        icon: '',
        iconAlign: 'start',
        iconColor: '#ffffff',
        showTitle: {
          desktop: true,
          mobile: true,
          tablet: true
        },
        showDescription: {
          desktop: true,
          mobile: true,
          tablet: true
        },
        showButton: {
          desktop: false,
          mobile: false,
          tablet: false
        },
        href: '',
        isShow: {
          desktop: true,
          mobile: true,
          tablet: true
        },
        isAdded: true
      }
    ]
  }
}

export const defaultResponsiveOneSmeBannerListProps = {
  desktop: defaultOneSmeBannerListProps('desktop'),
  tablet: defaultOneSmeBannerListProps('tablet'),
  mobile: defaultOneSmeBannerListProps('mobile')
}
