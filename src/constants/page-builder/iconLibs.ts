import type { ScreenTypeValues } from '@/redux-store/slices/builderSlice'
import type { IconLibraryCustomizeProps } from '@/types/page-builder/iconLibTypes'
import { DEFAULT_BASE_PROPS, RESET_PADDING } from './common'
import type { BorderType } from '@/types/page-builder'

export const defaultIconLibsProps = (
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  screenType: ScreenTypeValues = 'desktop'
): Required<IconLibraryCustomizeProps> => ({
  ...DEFAULT_BASE_PROPS,
  icon: 'PackageReceivedIcon',
  iconColor: '#0070C4',
  iconAlign: 'start',
  size: 36,
  borderIcon: {
    style: 'none' as BorderType,
    color: '#212D6E',
    thickness: 0,
    radius: 0
  },
  bgIconColor: '#ffffff',
  paddingIcon: {
    top: 0,
    right: 0,
    bottom: 0,
    left: 0
  },
  title: 'Lorem ipsum dolor....',
  titleFontSize: 28,
  titleFontWeight: 600,
  titleColor: '#0070C4',
  titleAlign: 'left',
  desc: 'Lorem ipsum dolor....',
  descFontSize: 10,
  descFontWeight: 500,
  descColor: '#00263F',
  descAlign: 'left',
  backgroundColor: '#ffffff',
  backgroundType: 'COLOR',
  border: {
    style: 'none' as BorderType,
    color: '#E6F5FA',
    thickness: 1,
    radius: 8
  },
  contentPadding: RESET_PADDING,
  showIcon: {
    desktop: true,
    tablet: true,
    mobile: true
  },
  showTitle: {
    desktop: true,
    tablet: true,
    mobile: true
  },
  showDesc: {
    desktop: true,
    tablet: true,
    mobile: true
  },
  background: '',
  width: 'max-content',
  height: 'auto',
  titleLineHeight: '40px',
  descLineHeight: '16px',
  link: '',
  // imgType
  imgSrc: '',
  imgSrcMobile: '',
  externalLink: '',
  externalLinkMobile: '',
  typeUpload: 'EXIST',
  imageFit: 'fill',
  align: 'center' as const
})

export const defaultResponseIconLibraryProps = {
  desktop: defaultIconLibsProps('desktop'),
  tablet: defaultIconLibsProps('tablet'),
  mobile: defaultIconLibsProps('mobile')
}
