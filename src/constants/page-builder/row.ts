import type { ScreenTypeValues } from '@/redux-store/slices/builderSlice'
import type { Align, BorderType } from '@/types/page-builder'
import { DEFAULT_BASE_PROPS } from './common'

export const COL_SPAN_DEFAULT = {
  desktop: 12,
  tablet: 12,
  mobile: 6
} as const

export const IMG_BACKGROUND_ALIGN = {
  start: 'left',
  center: 'center',
  end: 'right'
}

export const defaultRowProps = (screenType: ScreenTypeValues = 'desktop') => ({
  ...DEFAULT_BASE_PROPS,
  backgroundColor: undefined,
  imgSrc: null,
  externalLink: null,
  imgSrcMobile: null,
  externalLinkMobile: null,
  typeUpload: 'EXIST',
  imageFit: 'fit',
  shadow: 0,
  width: '100%',
  height: 'auto',
  border: {
    style: 'none' as BorderType,
    color: '#212D6E',
    thickness: 0,
    radius: 0
  },
  backgroundType: '',
  borderStyle: 'none',
  contentAlign: 'start' as <PERSON><PERSON>,
  justifyContent: 'center' as <PERSON><PERSON>,
  align: 'center' as Align,
  colWidths: [COL_SPAN_DEFAULT[screenType]],
  gap: 4,
  isBreakLine: false,
  hide: false
})

export const defaultResponseRowProps = {
  desktop: defaultRowProps('desktop'),
  tablet: defaultRowProps('tablet'),
  mobile: defaultRowProps('mobile')
}

export const defaultResponseColumnProps = {
  desktop: { ...defaultRowProps('desktop'), imageFit: 'fill' },
  tablet: { ...defaultRowProps('tablet'), imageFit: 'fill' },
  mobile: { ...defaultRowProps('mobile'), imageFit: 'fill' }
}
