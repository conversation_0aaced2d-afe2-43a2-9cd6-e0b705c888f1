export const productCatalogInternet = [
  {
    id: 'product_internet_1',
    data: {
      image: {
        desktop: {
          imgSrc: 'https://placehold.co/600x400/FFF3E0/E65100?text=Home+Mesh+2',
          width: '100%',
          height: 160,
          imageFit: 'fill',
          align: 'center'
        },
        tablet: {
          imgSrc: 'https://placehold.co/600x400/FFF3E0/E65100?text=Home+Mesh+2',
          width: '100%',
          height: 160,
          imageFit: 'fill',
          align: 'center'
        },
        mobile: {
          imgSrc: 'https://placehold.co/600x400/FFF3E0/E65100?text=Home+Mesh+2',
          width: '100%',
          height: 160,
          imageFit: 'fill',
          align: 'center'
        }
      },
      title: {
        desktop: {
          text: 'Home Mesh 2+',
          fontSize: 18,
          fontWeight: 700,
          className: 'font-cartoon text-lg'
        },
        tablet: {
          text: 'Home Mesh 2+',
          fontSize: 18,
          fontWeight: 700,
          className: 'font-cartoon text-lg'
        },
        mobile: {
          text: 'Home Mesh 2+',
          fontSize: 18,
          fontWeight: 700,
          className: 'font-cartoon text-lg'
        }
      },
      features: {
        desktop: {
          text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Tốc độ Internet 150Mbps<br>✓ Truyền hình MyTV Nâng cao<br>✓ Trang bị 01 Wifi Mesh</div>',
          fontSize: 14,
          color: '#6b7280',
          textAlign: 'left'
        },
        tablet: {
          text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Tốc độ Internet 150Mbps<br>✓ Truyền hình MyTV Nâng cao<br>✓ Trang bị 01 Wifi Mesh</div>',
          fontSize: 14,
          color: '#6b7280',
          textAlign: 'left'
        },
        mobile: {
          text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Tốc độ Internet 150Mbps<br>✓ Truyền hình MyTV Nâng cao<br>✓ Trang bị 01 Wifi Mesh</div>',
          fontSize: 14,
          color: '#6b7280',
          textAlign: 'left'
        }
      },
      price: {
        desktop: {
          text: '245.000đ<span class="text-base font-normal">/tháng</span>',
          className: 'text-xl font-bold text-fun-dark mt-auto pt-2'
        },
        tablet: {
          text: '245.000đ<span class="text-base font-normal">/tháng</span>',
          className: 'text-xl font-bold text-fun-dark mt-auto pt-2'
        },
        mobile: {
          text: '245.000đ<span class="text-base font-normal">/tháng</span>',
          className: 'text-xl font-bold text-fun-dark mt-auto pt-2'
        }
      },
      detailBtn: {
        desktop: {
          text: 'Chi tiết',
          fontSize: 14,
          fontWeight: 600,
          color: '#2a2a2a',
          buttonType: 'primary',
          buttonBackgroundColor: '#e0e0e0',
          border: '2px solid #2a2a2a',
          radius: 12,
          padding: { top: 8, bottom: 8, left: 16, right: 16 },
          size: 'middle',
          width: '100%'
        },
        tablet: {
          text: 'Chi tiết',
          fontSize: 14,
          fontWeight: 600,
          color: '#2a2a2a',
          buttonType: 'primary',
          buttonBackgroundColor: '#e0e0e0',
          border: '2px solid #2a2a2a',
          radius: 12,
          padding: { top: 8, bottom: 8, left: 16, right: 16 },
          size: 'middle',
          width: '100%'
        },
        mobile: {
          text: 'Chi tiết',
          fontSize: 14,
          fontWeight: 600,
          color: '#2a2a2a',
          buttonType: 'primary',
          buttonBackgroundColor: '#e0e0e0',
          border: '2px solid #2a2a2a',
          radius: 12,
          padding: { top: 8, bottom: 8, left: 16, right: 16 },
          size: 'middle',
          width: '100%'
        }
      },
      buyBtn: {
        desktop: {
          text: 'Mua ngay',
          fontSize: 14,
          fontWeight: 600,
          color: '#2a2a2a',
          buttonType: 'primary',
          buttonBackgroundColor: '#ffd600',
          border: '2px solid #2a2a2a',
          boxShadow: '2px 2px 0 #2a2a2a',
          radius: 12,
          padding: { top: 8, bottom: 8, left: 16, right: 16 },
          size: 'middle',
          width: '100%',
          className: 'btn-cartoon-primary'
        },
        tablet: {
          text: 'Mua ngay',
          fontSize: 14,
          fontWeight: 600,
          color: '#2a2a2a',
          buttonType: 'primary',
          buttonBackgroundColor: '#ffd600',
          border: '2px solid #2a2a2a',
          boxShadow: '2px 2px 0 #2a2a2a',
          radius: 12,
          padding: { top: 8, bottom: 8, left: 16, right: 16 },
          size: 'middle',
          width: '100%',
          className: 'btn-cartoon-primary'
        },
        mobile: {
          text: 'Mua ngay',
          fontSize: 14,
          fontWeight: 600,
          color: '#2a2a2a',
          buttonType: 'primary',
          buttonBackgroundColor: '#ffd600',
          border: '2px solid #2a2a2a',
          boxShadow: '2px 2px 0 #2a2a2a',
          radius: 12,
          padding: { top: 8, bottom: 8, left: 16, right: 16 },
          size: 'middle',
          width: '100%',
          className: 'btn-cartoon-primary'
        }
      }
    }
  },
  {
    id: 'product_internet_2',
    data: {
      image: {
        desktop: {
          imgSrc: 'https://placehold.co/600x400/FFF3E0/E65100?text=Home+Net',
          width: '100%',
          height: 160,
          imageFit: 'fill',
          align: 'center'
        },
        tablet: {
          imgSrc: 'https://placehold.co/600x400/FFF3E0/E65100?text=Home+Net',
          width: '100%',
          height: 160,
          imageFit: 'fill',
          align: 'center'
        },
        mobile: {
          imgSrc: 'https://placehold.co/600x400/FFF3E0/E65100?text=Home+Net',
          width: '100%',
          height: 160,
          imageFit: 'fill',
          align: 'center'
        }
      },
      title: {
        desktop: {
          text: 'Home Net 1',
          fontSize: 18,
          fontWeight: 700,
          className: 'font-cartoon text-lg'
        },
        tablet: {
          text: 'Home Net 1',
          fontSize: 18,
          fontWeight: 700,
          className: 'font-cartoon text-lg'
        },
        mobile: {
          text: 'Home Net 1',
          fontSize: 18,
          fontWeight: 700,
          className: 'font-cartoon text-lg'
        }
      },
      features: {
        desktop: {
          text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Tốc độ Internet 100Mbps<br>✓ Phù hợp cho cá nhân, gia đình nhỏ</div>',
          fontSize: 14,
          color: '#6b7280',
          textAlign: 'left'
        },
        tablet: {
          text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Tốc độ Internet 100Mbps<br>✓ Phù hợp cho cá nhân, gia đình nhỏ</div>',
          fontSize: 14,
          color: '#6b7280',
          textAlign: 'left'
        },
        mobile: {
          text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Tốc độ Internet 100Mbps<br>✓ Phù hợp cho cá nhân, gia đình nhỏ</div>',
          fontSize: 14,
          color: '#6b7280',
          textAlign: 'left'
        }
      },
      price: {
        desktop: {
          text: '165.000đ<span class="text-base font-normal">/tháng</span>',
          className: 'text-xl font-bold text-fun-dark mt-auto pt-2'
        },
        tablet: {
          text: '165.000đ<span class="text-base font-normal">/tháng</span>',
          className: 'text-xl font-bold text-fun-dark mt-auto pt-2'
        },
        mobile: {
          text: '165.000đ<span class="text-base font-normal">/tháng</span>',
          className: 'text-xl font-bold text-fun-dark mt-auto pt-2'
        }
      },
      detailBtn: {
        desktop: {
          text: 'Chi tiết',
          fontSize: 14,
          fontWeight: 600,
          color: '#2a2a2a',
          buttonType: 'primary',
          buttonBackgroundColor: '#e0e0e0',
          border: '2px solid #2a2a2a',
          radius: 8,
          padding: { top: 8, bottom: 8, left: 16, right: 16 },
          size: 'middle',
          width: '100%'
        },
        tablet: {
          text: 'Chi tiết',
          fontSize: 14,
          fontWeight: 600,
          color: '#2a2a2a',
          buttonType: 'primary',
          buttonBackgroundColor: '#e0e0e0',
          border: '2px solid #2a2a2a',
          radius: 8,
          padding: { top: 8, bottom: 8, left: 16, right: 16 },
          size: 'middle',
          width: '100%'
        },
        mobile: {
          text: 'Chi tiết',
          fontSize: 14,
          fontWeight: 600,
          color: '#2a2a2a',
          buttonType: 'primary',
          buttonBackgroundColor: '#e0e0e0',
          border: '2px solid #2a2a2a',
          radius: 8,
          padding: { top: 8, bottom: 8, left: 16, right: 16 },
          size: 'middle',
          width: '100%'
        }
      },
      buyBtn: {
        desktop: {
          text: 'Mua ngay',
          fontSize: 14,
          fontWeight: 600,
          color: '#2a2a2a',
          buttonType: 'primary',
          buttonBackgroundColor: '#ffd600',
          border: '2px solid #2a2a2a',
          boxShadow: '2px 2px 0 #2a2a2a',
          radius: 12,
          padding: { top: 8, bottom: 8, left: 16, right: 16 },
          size: 'middle',
          width: '100%',
          className: 'btn-cartoon-primary'
        },
        tablet: {
          text: 'Mua ngay',
          fontSize: 14,
          fontWeight: 600,
          color: '#2a2a2a',
          buttonType: 'primary',
          buttonBackgroundColor: '#ffd600',
          border: '2px solid #2a2a2a',
          boxShadow: '2px 2px 0 #2a2a2a',
          radius: 12,
          padding: { top: 8, bottom: 8, left: 16, right: 16 },
          size: 'middle',
          width: '100%',
          className: 'btn-cartoon-primary'
        },
        mobile: {
          text: 'Mua ngay',
          fontSize: 14,
          fontWeight: 600,
          color: '#2a2a2a',
          buttonType: 'primary',
          buttonBackgroundColor: '#ffd600',
          border: '2px solid #2a2a2a',
          boxShadow: '2px 2px 0 #2a2a2a',
          radius: 12,
          padding: { top: 8, bottom: 8, left: 16, right: 16 },
          size: 'middle',
          width: '100%',
          className: 'btn-cartoon-primary'
        }
      }
    }
  },
  {
    id: 'product_internet_3',
    data: {
      image: {
        desktop: {
          imgSrc: 'https://placehold.co/600x400/FFF3E0/E65100?text=Home+TV',
          width: '100%',
          height: 160,
          imageFit: 'fill',
          align: 'center'
        },
        tablet: {
          imgSrc: 'https://placehold.co/600x400/FFF3E0/E65100?text=Home+TV',
          width: '100%',
          height: 160,
          imageFit: 'fill',
          align: 'center'
        },
        mobile: {
          imgSrc: 'https://placehold.co/600x400/FFF3E0/E65100?text=Home+TV',
          width: '100%',
          height: 160,
          imageFit: 'fill',
          align: 'center'
        }
      },
      title: {
        desktop: {
          text: 'Home TV 1',
          fontSize: 18,
          fontWeight: 700,
          className: 'font-cartoon text-lg'
        },
        tablet: {
          text: 'Home TV 1',
          fontSize: 18,
          fontWeight: 700,
          className: 'font-cartoon text-lg'
        },
        mobile: {
          text: 'Home TV 1',
          fontSize: 18,
          fontWeight: 700,
          className: 'font-cartoon text-lg'
        }
      },
      features: {
        desktop: {
          text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Tốc độ Internet 80Mbps<br>✓ Truyền hình MyTV Chuẩn<br>✓ Gói cước tiết kiệm</div>',
          fontSize: 14,
          color: '#6b7280',
          textAlign: 'left'
        },
        tablet: {
          text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Tốc độ Internet 80Mbps<br>✓ Truyền hình MyTV Chuẩn<br>✓ Gói cước tiết kiệm</div>',
          fontSize: 14,
          color: '#6b7280',
          textAlign: 'left'
        },
        mobile: {
          text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Tốc độ Internet 80Mbps<br>✓ Truyền hình MyTV Chuẩn<br>✓ Gói cước tiết kiệm</div>',
          fontSize: 14,
          color: '#6b7280',
          textAlign: 'left'
        }
      },
      price: {
        desktop: {
          text: '175.000đ<span class="text-base font-normal">/tháng</span>',
          className: 'text-xl font-bold text-fun-dark mt-auto pt-2'
        },
        tablet: {
          text: '175.000đ<span class="text-base font-normal">/tháng</span>',
          className: 'text-xl font-bold text-fun-dark mt-auto pt-2'
        },
        mobile: {
          text: '175.000đ<span class="text-base font-normal">/tháng</span>',
          className: 'text-xl font-bold text-fun-dark mt-auto pt-2'
        }
      },
      detailBtn: {
        desktop: {
          text: 'Chi tiết',
          fontSize: 14,
          fontWeight: 600,
          color: '#2a2a2a',
          buttonType: 'primary',
          buttonBackgroundColor: '#e0e0e0',
          border: '2px solid #2a2a2a',
          radius: 8,
          padding: { top: 8, bottom: 8, left: 16, right: 16 },
          size: 'middle',
          width: '100%'
        },
        tablet: {
          text: 'Chi tiết',
          fontSize: 14,
          fontWeight: 600,
          color: '#2a2a2a',
          buttonType: 'primary',
          buttonBackgroundColor: '#e0e0e0',
          border: '2px solid #2a2a2a',
          radius: 8,
          padding: { top: 8, bottom: 8, left: 16, right: 16 },
          size: 'middle',
          width: '100%'
        },
        mobile: {
          text: 'Chi tiết',
          fontSize: 14,
          fontWeight: 600,
          color: '#2a2a2a',
          buttonType: 'primary',
          buttonBackgroundColor: '#e0e0e0',
          border: '2px solid #2a2a2a',
          radius: 8,
          padding: { top: 8, bottom: 8, left: 16, right: 16 },
          size: 'middle',
          width: '100%'
        }
      },
      buyBtn: {
        desktop: {
          text: 'Mua ngay',
          fontSize: 14,
          fontWeight: 600,
          color: '#2a2a2a',
          buttonType: 'primary',
          buttonBackgroundColor: '#ffd600',
          border: '2px solid #2a2a2a',
          boxShadow: '2px 2px 0 #2a2a2a',
          radius: 12,
          padding: { top: 8, bottom: 8, left: 16, right: 16 },
          size: 'middle',
          width: '100%',
          className: 'btn-cartoon-primary'
        },
        tablet: {
          text: 'Mua ngay',
          fontSize: 14,
          fontWeight: 600,
          color: '#2a2a2a',
          buttonType: 'primary',
          buttonBackgroundColor: '#ffd600',
          border: '2px solid #2a2a2a',
          boxShadow: '2px 2px 0 #2a2a2a',
          radius: 12,
          padding: { top: 8, bottom: 8, left: 16, right: 16 },
          size: 'middle',
          width: '100%',
          className: 'btn-cartoon-primary'
        },
        mobile: {
          text: 'Mua ngay',
          fontSize: 14,
          fontWeight: 600,
          color: '#2a2a2a',
          buttonType: 'primary',
          buttonBackgroundColor: '#ffd600',
          border: '2px solid #2a2a2a',
          boxShadow: '2px 2px 0 #2a2a2a',
          radius: 12,
          padding: { top: 8, bottom: 8, left: 16, right: 16 },
          size: 'middle',
          width: '100%',
          className: 'btn-cartoon-primary'
        }
      }
    }
  },
  {
    id: 'product_internet_4',
    data: {
      image: {
        desktop: {
          imgSrc: 'https://placehold.co/600x400/FFF3E0/E65100?text=Home+Combo',
          width: '100%',
          height: 160,
          imageFit: 'fill',
          align: 'center'
        },
        tablet: {
          imgSrc: 'https://placehold.co/600x400/FFF3E0/E65100?text=Home+Combo',
          width: '100%',
          height: 128,
          imageFit: 'fill',
          align: 'center'
        },
        mobile: {
          imgSrc: 'https://placehold.co/600x400/FFF3E0/E65100?text=Home+Combo',
          width: '100%',
          height: 96,
          imageFit: 'fill',
          align: 'center'
        }
      },
      title: {
        desktop: {
          text: 'Home Combo',
          fontSize: 18,
          fontWeight: 700,
          className: 'font-cartoon text-lg'
        },
        tablet: {
          text: 'Home Combo',
          fontSize: 18,
          fontWeight: 700,
          className: 'font-cartoon text-lg'
        },
        mobile: {
          text: 'Home Combo',
          fontSize: 18,
          fontWeight: 700,
          className: 'font-cartoon text-lg'
        }
      },
      features: {
        desktop: {
          text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Internet + TV + Di động<br>✓ Tiết kiệm đến 50%<br>✓ Nhiều lựa chọn gói</div>',
          fontSize: 14,
          color: '#6b7280',
          textAlign: 'left'
        },
        tablet: {
          text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Internet + TV + Di động<br>✓ Tiết kiệm đến 50%<br>✓ Nhiều lựa chọn gói</div>',
          fontSize: 14,
          color: '#6b7280',
          textAlign: 'left'
        },
        mobile: {
          text: '<div class="text-sm text-gray-600 my-3 space-y-1 flex-grow">✓ Internet + TV + Di động<br>✓ Tiết kiệm đến 50%<br>✓ Nhiều lựa chọn gói</div>',
          fontSize: 14,
          color: '#6b7280',
          textAlign: 'left'
        }
      },
      price: {
        desktop: {
          text: 'Từ 239.000đ',
          className: 'text-xl font-bold text-fun-dark mt-auto pt-2'
        },
        tablet: {
          text: 'Từ 239.000đ',
          className: 'text-xl font-bold text-fun-dark mt-auto pt-2'
        },
        mobile: {
          text: 'Từ 239.000đ',
          className: 'text-xl font-bold text-fun-dark mt-auto pt-2'
        }
      },
      detailBtn: {
        desktop: {
          text: 'Chi tiết',
          fontSize: 14,
          fontWeight: 600,
          color: '#2a2a2a',
          buttonType: 'primary',
          buttonBackgroundColor: '#e0e0e0',
          border: '2px solid #2a2a2a',
          radius: 8,
          padding: { top: 8, bottom: 8, left: 16, right: 16 },
          size: 'middle',
          width: '100%'
        },
        tablet: {
          text: 'Chi tiết',
          fontSize: 14,
          fontWeight: 600,
          color: '#2a2a2a',
          buttonType: 'primary',
          buttonBackgroundColor: '#e0e0e0',
          border: '2px solid #2a2a2a',
          radius: 8,
          padding: { top: 8, bottom: 8, left: 16, right: 16 },
          size: 'middle',
          width: '100%'
        },
        mobile: {
          text: 'Chi tiết',
          fontSize: 14,
          fontWeight: 600,
          color: '#2a2a2a',
          buttonType: 'primary',
          buttonBackgroundColor: '#e0e0e0',
          border: '2px solid #2a2a2a',
          radius: 8,
          padding: { top: 8, bottom: 8, left: 16, right: 16 },
          size: 'middle',
          width: '100%'
        }
      },
      buyBtn: {
        desktop: {
          text: 'Mua ngay',
          fontSize: 14,
          fontWeight: 600,
          color: '#2a2a2a',
          buttonType: 'primary',
          buttonBackgroundColor: '#ffd600',
          border: '2px solid #2a2a2a',
          boxShadow: '2px 2px 0 #2a2a2a',
          radius: 8,
          padding: { top: 8, bottom: 8, left: 16, right: 16 },
          size: 'middle',
          width: '100%',
          className: 'btn-cartoon-primary'
        },
        tablet: {
          text: 'Mua ngay',
          fontSize: 14,
          fontWeight: 600,
          color: '#2a2a2a',
          buttonType: 'primary',
          buttonBackgroundColor: '#ffd600',
          border: '2px solid #2a2a2a',
          boxShadow: '2px 2px 0 #2a2a2a',
          radius: 8,
          padding: { top: 8, bottom: 8, left: 16, right: 16 },
          size: 'middle',
          width: '100%',
          className: 'btn-cartoon-primary'
        },
        mobile: {
          text: 'Mua ngay',
          fontSize: 14,
          fontWeight: 600,
          color: '#2a2a2a',
          buttonType: 'primary',
          buttonBackgroundColor: '#ffd600',
          border: '2px solid #2a2a2a',
          boxShadow: '2px 2px 0 #2a2a2a',
          radius: 8,
          padding: { top: 8, bottom: 8, left: 16, right: 16 },
          size: 'middle',
          width: '100%',
          className: 'btn-cartoon-primary'
        }
      }
    }
  }
]
