import type { ButtonSize } from 'antd/es/button'

import { baseColorLight } from '@/utils/colors'
import { RESET_MARGIN, RESET_PADDING } from './common'
import { defaultImageProps } from './images'
import { defaultRowProps } from './row'
import type { BorderType } from '@/types/page-builder'

export const defaultButtonRowProps = {
  ...defaultRowProps(),
  colWidths: [6, 6],
  backgroundColor: 'transparent'
}

const defaultButtonProps = {
  size: 'large' as ButtonSize,
  color: '#ffffff',
  radius: 12,
  buttonType: 'primary',
  width: '100%'
}

const defaultImageBackgroundSupplyPartnerProps = {
  ...defaultImageProps(),
  margin: RESET_MARGIN,
  padding: RESET_PADDING,
  width: 600,
  height: '100%'
}

const defaultSupplyPartnerRowProps = {
  ...defaultRowProps(),
  colWidths: [6, 6],
  gap: 20,
  width: '100%',
  imgSrc: '/assets/images/pages/home/<USER>',
  imgSrcMobile: '/assets/images/pages/home/<USER>',
  backgroundType: 'IMAGE',
  imageFit: 'fill'
}

const defaultColumnLeftProps = {
  ...defaultRowProps(),
  backgroundColor: '#ffffff',
  backgroundType: 'COLOR',
  border: {
    thickness: 1,
    style: 'solid',
    color: '#E6F5FA',
    radius: 16
  }
}

export const defaultTextProps = {
  fontSize: 32,
  fontWeight: 600,
  color: '#000000'
}

export const defaultResponsiveSupplyPartnerProps = {
  container: {
    height: 'auto',
    background: '#FFFFFF',
    padding: ['0', '0', '0', '0']
  },
  rowContainer: {
    desktop: defaultSupplyPartnerRowProps,
    tablet: defaultSupplyPartnerRowProps,
    mobile: {
      ...defaultSupplyPartnerRowProps,
      isBreakLine: true
    }
  },
  column1: {
    desktop: {
      ...defaultColumnLeftProps,
      margin: { top: 54, bottom: 54, left: 0, right: 0 },
      padding: { top: 30, bottom: 30, left: 30, right: 30 },
      backgroundColor: '#ffffff',
      backgroundType: 'COLOR'
    },
    tablet: {
      ...defaultColumnLeftProps,
      margin: { top: 54, bottom: 54, left: 0, right: 0 },
      padding: { top: 30, bottom: 30, left: 30, right: 30 }
    },
    mobile: {
      ...defaultColumnLeftProps,
      padding: { top: 8, bottom: 24, left: 16, right: 16 },
      backgroundColor: 'transparent',
      border: {
        style: 'solid' as BorderType,
        color: '#212d6e',
        thickness: 0,
        radius: 0
      }
    }
  },
  column2: {
    desktop: {
      ...defaultColumnLeftProps,
      backgroundColor: baseColorLight['sme-blue-9'],
      backgroundType: 'COLOR',
      border: { thickness: 0 }
    },
    tablet: {
      ...defaultColumnLeftProps,
      backgroundColor: baseColorLight['sme-blue-9'],
      backgroundType: 'COLOR',
      border: { thickness: 0 }
    },
    mobile: {
      ...defaultColumnLeftProps,
      hide: true
    }
  },
  rightImage: {
    desktop: {
      ...defaultImageBackgroundSupplyPartnerProps,
      imgSrc: '/assets/images/pages/home/<USER>'
    },
    tablet: {
      ...defaultImageBackgroundSupplyPartnerProps,
      imgSrc: '/assets/images/pages/home/<USER>'
    },
    mobile: {
      ...defaultImageBackgroundSupplyPartnerProps,
      imgSrcMobile: '/assets/images/pages/home/<USER>'
    }
  },
  title: {
    desktop: {
      ...defaultTextProps,
      marginTop: 8,
      fontSize: 32,
      fontWeight: 600,
      text: 'Trở thành Đối tác cung cấp sản phẩm dịch vụ',
      lineHeight: '31px',
      fontStyle: 'normal'
    },
    tablet: {
      ...defaultTextProps,
      marginTop: 8,
      fontSize: 32,
      fontWeight: 600,
      text: 'Trở thành Đối tác cung cấp sản phẩm dịch vụ',
      lineHeight: '31px',
      fontStyle: 'normal'
    },
    mobile: {
      ...defaultTextProps,
      marginTop: 8,
      fontSize: 24,
      fontWeight: 600,
      text: 'Trở thành Đối tác cung cấp sản phẩm dịch vụ',
      lineHeight: '32px',
      fontStyle: 'normal',
      color: '#ffffff',
      textAlign: 'center'
    }
  },
  description: {
    desktop: {
      ...defaultTextProps,
      margin: {
        top: 40
      },
      color: '#394867',
      fontSize: 14,
      fontWeight: 400,
      text: 'Trở thành đối tác kinh doanh trên oneSME – Cơ Hội Mở Rộng Kinh Doanh và Tăng Trưởng Doanh Thu Với nền tảng công nghệ hiện đại và hệ sinh thái đa dạng, oneSME mang đến cơ hội tiếp cận hàng nghìn khách hàng tiềm năng, mở rộng thị trường và gia tăng doanh thu một cách đột phá. Tham gia ngay để được hỗ trợ toàn diện từ quảng bá sản phẩm, quản lý bán hàng đến chăm sóc khách hàng, giúp doanh nghiệp bứt phá và thành công bền vững trong môi trường kinh doanh số.',
      lineHeight: '13px',
      fontStyle: 'normal',
      iconAlign: 'start'
    },
    tablet: {
      ...defaultTextProps,
      color: '#394867',
      fontSize: 14,
      fontWeight: 400,
      text: 'Trở thành đối tác kinh doanh trên oneSME – Cơ Hội Mở Rộng Kinh Doanh và Tăng Trưởng Doanh Thu Với nền tảng công nghệ hiện đại và hệ sinh thái đa dạng, oneSME mang đến cơ hội tiếp cận hàng nghìn khách hàng tiềm năng, mở rộng thị trường và gia tăng doanh thu một cách đột phá. Tham gia ngay để được hỗ trợ toàn diện từ quảng bá sản phẩm, quản lý bán hàng đến chăm sóc khách hàng, giúp doanh nghiệp bứt phá và thành công bền vững trong môi trường kinh doanh số.',
      lineHeight: '13px',
      fontStyle: 'normal',
      iconAlign: 'start'
    },
    mobile: {
      ...defaultTextProps,
      color: '#ffffff',
      fontSize: 14,
      fontWeight: 400,
      text: 'Trở thành đối tác kinh doanh trên oneSME – Cơ Hội Mở Rộng Kinh Doanh và Tăng Trưởng Doanh Thu Với nền tảng công nghệ hiện đại và hệ sinh thái đa dạng, oneSME mang đến cơ hội tiếp cận hàng nghìn khách hàng tiềm năng, mở rộng thị trường và gia tăng doanh thu một cách đột phá. Tham gia ngay để được hỗ trợ toàn diện từ quảng bá sản phẩm, quản lý bán hàng đến chăm sóc khách hàng, giúp doanh nghiệp bứt phá và thành công bền vững trong môi trường kinh doanh số.',
      lineHeight: '31px',
      fontStyle: 'normal',
      iconAlign: 'start'
    }
  },
  spacer: {
    desktop: { height: 40 },
    tablet: { height: 40 },
    mobile: { height: 8 }
  },
  spacer2: {
    desktop: { height: 40 },
    tablet: { height: 40 },
    mobile: { height: 32 }
  },
  buttonRow: {
    desktop: { ...defaultButtonRowProps },
    tablet: { ...defaultButtonRowProps },
    mobile: { ...defaultButtonRowProps, isBreakLine: true }
  },
  button1: {
    desktop: {
      ...defaultButtonProps,
      text: 'Tìm hiểu thêm',
      buttonBackgroundColor: '#0070C4',
      href: 'https://onesme.vn/partner-portal/register'
    },
    tablet: {
      ...defaultButtonProps,
      text: 'Tìm hiểu thêm',
      buttonBackgroundColor: '#0070C4',
      href: 'https://onesme.vn/partner-portal/register'
    },
    mobile: {
      ...defaultButtonProps,
      text: 'Tìm hiểu thêm',
      buttonBackgroundColor: '#0070C4',
      width: '100%',
      margin: { ...RESET_MARGIN, bottom: 8 },
      href: 'https://onesme.vn/partner-portal/register'
    }
  },
  button2: {
    desktop: {
      ...defaultButtonProps,
      text: 'Đăng ký ngay',
      buttonBackgroundColor: '#EF9304',
      href: 'https://onesme.vn/partner-portal/register'
    },
    tablet: {
      ...defaultButtonProps,
      text: 'Đăng ký ngay',
      buttonBackgroundColor: '#EF9304',
      href: 'https://onesme.vn/partner-portal/register'
    },
    mobile: {
      ...defaultButtonProps,
      text: 'Đăng ký ngay',
      buttonBackgroundColor: '#EF9304',
      width: '100%',
      href: 'https://onesme.vn/partner-portal/register'
    }
  },
  columnMobile: {
    desktop: { ...defaultColumnLeftProps, hide: true },
    tablet: { ...defaultColumnLeftProps, hide: true },
    mobile: {
      ...defaultColumnLeftProps,
      hide: false,
      backgroundColor: 'transparent',
      border: {
        style: 'solid' as BorderType,
        color: '#212d6e',
        thickness: 0,
        radius: 0
      }
    }
  },
  imageMobile: {
    desktop: {},
    tablet: {},
    mobile: {
      ...defaultImageBackgroundSupplyPartnerProps,
      imgSrcMobile: '/assets/images/pages/home/<USER>',
      width: '100%'
    }
  }
}
