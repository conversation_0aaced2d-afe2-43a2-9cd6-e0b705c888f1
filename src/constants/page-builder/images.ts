import type { SelectProps } from 'antd/lib'

import type { CSSProperties } from 'styled-components'

import type { BorderType, ImageCustomizeProps } from '@/types/page-builder'
import { DEFAULT_BASE_PROPS } from './common'
import type { ScreenTypeValues } from '@/redux-store/slices/builderSlice'

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const defaultImageProps = (screenType: ScreenTypeValues = 'desktop'): Required<ImageCustomizeProps> => ({
  ...DEFAULT_BASE_PROPS,
  hide: false,
  link: '',
  alt: '',
  imgSrc: '',
  imgSrcMobile: '',
  externalLink: '',
  externalLinkMobile: '',
  typeUpload: 'EXIST',
  imageFit: 'fill',
  align: 'center',
  border: {
    style: 'none' as BorderType,
    color: '',
    thickness: 0,
    radius: 0
  },
  width: '',
  height: '',
  hasOverlay: false,
  imgSrcFallBack: '/assets/images/noImage.svg'
})

export const IMAGE_FIT_OPTIONS: SelectProps['options'] = [
  { value: 'fill', label: 'Fill' },
  { value: 'fit', label: 'Fit' }
] as const

export const IMAGE_SIZE_OPTIONS: SelectProps['options'] = [
  { value: 'large', label: 'Large' },
  { value: 'medium', label: 'Medium' },
  { value: 'small', label: 'Small' }
] as const

export const IMAGE_SIZE_CONSTRAINTS: {
  [key: string]: {
    small: {
      maxHeight: number
      maxWidth: number
      minHeight: number
      minWidth: number
    }
    medium: {
      maxHeight: number
      maxWidth: number
      minHeight: number
      minWidth: number
    }
    large: {
      maxHeight: number
      maxWidth: number
      minHeight: number
      minWidth: number
    }
  }
} = {
  desktop: {
    small: {
      maxHeight: 130,
      maxWidth: 370,
      minHeight: 0,
      minWidth: 0
    },
    medium: {
      maxHeight: 390,
      maxWidth: 570,
      minHeight: 131,
      minWidth: 371
    },
    large: {
      maxHeight: Infinity,
      maxWidth: Infinity,
      minHeight: 391,
      minWidth: 580
    }
  },
  mobile: {
    small: {
      maxHeight: 56,
      maxWidth: 79,
      minHeight: 0,
      minWidth: 0
    },
    medium: {
      maxHeight: 234,
      maxWidth: 234,
      minHeight: 57,
      minWidth: 80
    },
    large: {
      maxHeight: Infinity,
      maxWidth: Infinity,
      minHeight: 235,
      minWidth: 235
    }
  }
}

export const getImageSizeStyles = (screenType: string, imageSize: 'small' | 'medium' | 'large'): CSSProperties => {
  // Map screenType về đúng key
  let screenTypeKey = screenType.toLowerCase()

  // Nếu là tablet, sử dụng constraints của desktop
  if (screenTypeKey === 'tablet') {
    screenTypeKey = 'desktop'
  }

  const sizeConfig = IMAGE_SIZE_CONSTRAINTS[screenTypeKey]?.[imageSize]

  console.log('sizeConfig:', sizeConfig)

  // Thêm check safety
  if (!sizeConfig) {
    console.warn('No size config found for:', { screenTypeKey, imageSize })

    return {
      height: 'auto',
      width: 'auto',
      objectFit: 'contain' as const
    }
  }

  return {
    maxHeight: sizeConfig.maxHeight === Infinity ? 'none' : `${sizeConfig.maxHeight}px`,
    maxWidth: sizeConfig.maxWidth === Infinity ? 'none' : `${sizeConfig.maxWidth}px`,
    minHeight: sizeConfig.minHeight !== undefined ? `${sizeConfig.minHeight}px` : 'auto',
    minWidth: sizeConfig.minWidth !== undefined ? `${sizeConfig.minWidth}px` : 'auto',
    height: 'auto',
    width: 'auto',
    objectFit: 'contain' as const
  }
}

export const defaultResponseImageProps = {
  desktop: defaultImageProps('desktop'),
  tablet: defaultImageProps('tablet'),
  mobile: defaultImageProps('mobile')
}
