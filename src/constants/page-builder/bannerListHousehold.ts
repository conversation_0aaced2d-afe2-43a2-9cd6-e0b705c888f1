import type { ScreenTypeValues } from '@/redux-store/slices/builderSlice'
import type { BannerItemProps, BannerListCustomizeProps } from '@/types/page-builder/bannerList'
import { DEFAULT_BASE_PROPS } from './common' // Reusable base properties

export const defaultImagesBanners: BannerItemProps[] = [
  {
    id: 'image1',
    backgroundColor: '#f0f0f0',
    url: '',
    urlMobile: '',
    urlLibrary: '',
    urlLibraryMobile: '',
    imageSelectType: 'library',
    imageAlign: 'center',
    imageFit: 'fit',
    contentAlign: 'left',
    imgSrc: '',
    fontWeightTitle: 200,
    fontWeightDescription: 200,
    fontSizeTitle: 24,
    fontSizeDescription: 16,
    colorTitle: '#ffffff',
    colorDescription: '#ffffff',
    textAlignTitle: 'center',
    paddingButton: {
      top: 0,
      right: 0,
      bottom: 0,
      left: 0
    },
    marginButton: {
      top: 0,
      bottom: 0
    },
    titleText: 'Title',
    descriptionText: 'Description',
    buttonText: 'Button',
    buttonBackgroundColor: '#ffffff',
    buttonTextColor: '#ffffff',
    buttonStyle: 'default',
    buttonSize: 'medium',
    buttonRadius: 4,
    buttonFontSize: 14,
    buttonFontWeight: 500,
    hasIcon: false,
    icon: '',
    iconAlign: 'start',
    iconColor: '#ffffff',
    showTitle: {
      desktop: true,
      mobile: true,
      tablet: true
    },
    showDescription: {
      desktop: true,
      mobile: true,
      tablet: true
    },
    showButton: {
      desktop: true,
      mobile: true,
      tablet: true
    },
    href: '',
    isShow: {
      desktop: true,
      mobile: true,
      tablet: true
    },
    isAdded: true
  }
]

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const defaultBannerListProps = (screenType: ScreenTypeValues): Required<BannerListCustomizeProps> => {
  return {
    ...DEFAULT_BASE_PROPS,
    backgroundColor: '#EF9304',
    autoPlaySpeed: 5000,
    responsive: {
      desktop: true,
      tablet: true,
      mobile: true
    },
    width: '100%',
    hasButton: true,
    currentSlide: 0,
    buttonType: 'default', // add this property
    buttonAlign: 'center',
    size: 'large',
    href: '',
    hasIcon: false,
    icon: '',
    iconAlign: 'start',
    iconColor: '#ffffff',
    isButtonWorkplace: false,
    slidesPerSection: 1,
    images: defaultImagesBanners
  } as Required<BannerListCustomizeProps>
}

export const defaultResponsiveBannerListProps = {
  desktop: defaultBannerListProps('desktop'),
  tablet: defaultBannerListProps('tablet'),
  mobile: defaultBannerListProps('mobile')
}

export const defaultHouseHoldBannerListProps = (screenType: ScreenTypeValues): Required<BannerListCustomizeProps> => {
  return {
    ...defaultBannerListProps(screenType),
    images: [
      {
        id: 'image1',
        backgroundColor: '#6e82c7',
        imageAlign: 'center',
        imageFit: 'fill',
        contentAlign: 'center',
        url: '',
        urlMobile: '',
        urlLibrary: '',
        urlLibraryMobile: '',
        imageSelectType: 'library',
        imgSrc: '',
        fontWeightTitle: 600,
        fontWeightDescription: 400,
        fontSizeTitle: 32,
        fontSizeDescription: 14,
        colorTitle: '#ffffff',
        colorDescription: '#ffffff',
        textAlignTitle: 'center',
        textAlignDescription: 'center',
        overlayColor: 'rgba(30, 64, 175, 0.8)', // blue-800
        overlayOpacity: 0.6,
        paddingButton: {
          top: 10,
          right: 16,
          bottom: 10,
          left: 16
        },
        marginButton: {
          top: 10,
          bottom: 10
        },
        titleText: 'Internet & TV Cho Cả Nhà',
        descriptionText: 'Giải trí bất tận, kết nối không giới hạn.',
        buttonText: 'Button',
        buttonBackgroundColor: '#ffffff',
        buttonTextColor: '#ffffff',
        buttonStyle: 'default',
        buttonSize: 'medium',
        buttonFontSize: 14,
        buttonFontWeight: 500,
        hasIcon: false,
        icon: '',
        iconAlign: 'start',
        iconColor: '#ffffff',
        showTitle: {
          desktop: true,
          mobile: true,
          tablet: true
        },
        showDescription: {
          desktop: true,
          mobile: true,
          tablet: true
        },
        showButton: {
          desktop: false,
          mobile: false,
          tablet: false
        },
        href: '',
        isShow: {
          desktop: true,
          mobile: true,
          tablet: true
        },
        isAdded: true
      },
      {
        id: 'image2',
        backgroundColor: '#caa69d',
        imageAlign: 'center',
        imageFit: 'fill',
        contentAlign: 'center',
        url: '',
        urlMobile: '',
        urlLibrary: '',
        urlLibraryMobile: '',
        imageSelectType: 'library',
        imgSrc: '',
        fontWeightTitle: 600,
        fontWeightDescription: 400,
        fontSizeTitle: 32,
        fontSizeDescription: 14,
        colorTitle: '#ffffff',
        colorDescription: '#ffffff',
        textAlignTitle: 'center',
        textAlignDescription: 'center',
        overlayColor: 'rgba(194, 65, 12, 0.8)', // orange-800
        overlayOpacity: 0.6,
        paddingButton: {
          top: 10,
          right: 16,
          bottom: 10,
          left: 16
        },
        marginButton: {
          top: 10,
          bottom: 10
        },
        titleText: 'Home Camera - An Toàn Cho Cả Gia Đình',
        descriptionText: 'Giám sát mọi lúc, an tâm mọi nơi.',
        buttonText: 'Button',
        buttonBackgroundColor: '#ffffff',
        buttonTextColor: '#ffffff',
        buttonStyle: 'default',
        buttonSize: 'medium',
        buttonFontSize: 14,
        buttonFontWeight: 500,
        hasIcon: false,
        icon: '',
        iconAlign: 'start',
        iconColor: '#ffffff',
        showTitle: {
          desktop: true,
          mobile: true,
          tablet: true
        },
        showDescription: {
          desktop: true,
          mobile: true,
          tablet: true
        },
        showButton: {
          desktop: false,
          mobile: false,
          tablet: false
        },
        href: '',
        isShow: {
          desktop: true,
          mobile: true,
          tablet: true
        },
        isAdded: true
      },
      {
        id: 'image3',
        backgroundColor: '#699998',
        imageAlign: 'center',
        imageFit: 'fill',
        contentAlign: 'center',
        url: '',
        urlMobile: '',
        urlLibrary: '',
        urlLibraryMobile: '',
        imageSelectType: 'library',
        imgSrc: '',
        fontWeightTitle: 600,
        fontWeightDescription: 400,
        fontSizeTitle: 32,
        fontSizeDescription: 14,
        colorTitle: '#ffffff',
        colorDescription: '#ffffff',
        textAlignTitle: 'center',
        textAlignDescription: 'center',
        overlayColor: 'rgba(21, 128, 61, 0.8)', // green-800
        overlayOpacity: 0.6,
        paddingButton: {
          top: 10,
          right: 16,
          bottom: 10,
          left: 16
        },
        marginButton: {
          top: 10,
          bottom: 10
        },
        titleText: 'Smart Home - Nâng Tầm Cuộc Sống',
        descriptionText: 'Tận hưởng không gian sống hiện đại, tiện nghi.',
        buttonText: 'Button',
        buttonBackgroundColor: '#ffffff',
        buttonTextColor: '#ffffff',
        buttonStyle: 'default',
        buttonSize: 'medium',
        buttonFontSize: 14,
        buttonFontWeight: 500,
        hasIcon: false,
        icon: '',
        iconAlign: 'start',
        iconColor: '#ffffff',
        showTitle: {
          desktop: true,
          mobile: true,
          tablet: true
        },
        showDescription: {
          desktop: true,
          mobile: true,
          tablet: true
        },
        showButton: {
          desktop: false,
          mobile: false,
          tablet: false
        },
        href: '',
        isShow: {
          desktop: true,
          mobile: true,
          tablet: true
        },
        isAdded: true
      }
    ]
  }
}

export const defaultResponsiveHouseHoldBannerListProps = {
  desktop: defaultHouseHoldBannerListProps('desktop'),
  tablet: defaultHouseHoldBannerListProps('tablet'),
  mobile: defaultHouseHoldBannerListProps('mobile')
}
