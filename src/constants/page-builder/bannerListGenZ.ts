import type { ScreenTypeValues } from '@/redux-store/slices/builderSlice'
import type { BannerItemProps, BannerListCustomizeProps } from '@/types/page-builder/bannerList'
import { DEFAULT_BASE_PROPS } from './common' // Reusable base properties

const defaultImagesBanners: BannerItemProps[] = [
  {
    id: 'image1',
    backgroundColor: '#f0f0f0',
    url: '',
    urlMobile: '',
    urlLibrary: '',
    urlLibraryMobile: '',
    imageSelectType: 'upload',
    imageAlign: 'center',
    imageFit: 'fit',
    contentAlign: 'left',
    imgSrc: '',
    fontWeightTitle: 200,
    fontWeightDescription: 200,
    fontSizeTitle: 24,
    fontSizeDescription: 16,
    colorTitle: '#ffffff',
    colorDescription: '#ffffff',
    textAlignTitle: 'center',
    paddingButton: {
      top: 0,
      right: 0,
      bottom: 0,
      left: 0
    },
    marginButton: {
      top: 0,
      bottom: 0
    },
    titleText: 'Title',
    descriptionText: 'Description',
    buttonText: 'Button',
    buttonBackgroundColor: '#ffffff',
    buttonTextColor: '#ffffff',
    buttonStyle: 'default',
    buttonSize: 'medium',
    buttonRadius: 4,
    buttonFontSize: 14,
    buttonFontWeight: 500,
    hasIcon: false,
    icon: '',
    iconAlign: 'start',
    iconColor: '#ffffff',
    showTitle: {
      desktop: true,
      mobile: true,
      tablet: true
    },
    showDescription: {
      desktop: true,
      mobile: true,
      tablet: true
    },
    showButton: {
      desktop: true,
      mobile: true,
      tablet: true
    },
    href: '',
    isShow: {
      desktop: true,
      mobile: true,
      tablet: true
    },
    isAdded: true
  }
]

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const defaultBannerListProps = (screenType: ScreenTypeValues): Required<BannerListCustomizeProps> => {
  return {
    ...DEFAULT_BASE_PROPS,
    backgroundColor: '#EF9304',
    autoPlaySpeed: 5000,
    responsive: {
      desktop: true,
      tablet: true,
      mobile: true
    },
    width: '100%',
    hasButton: true,
    currentSlide: 0,
    buttonType: 'default', // add this property
    buttonAlign: 'center',
    size: 'large',
    href: '',
    hasIcon: false,
    icon: '',
    iconAlign: 'start',
    iconColor: '#ffffff',
    isButtonWorkplace: false,
    slidesPerSection: 1,
    showDots: false,
    images: defaultImagesBanners
  } as Required<BannerListCustomizeProps>
}

// const defaultResponsiveBannerListProps = {
//   desktop: defaultBannerListProps('desktop'),
//   tablet: defaultBannerListProps('tablet'),
//   mobile: defaultBannerListProps('mobile')
// }

const defaultGenZBannerListProps = (screenType: ScreenTypeValues): Required<BannerListCustomizeProps> => {
  return {
    ...defaultBannerListProps(screenType),
    images: [
      {
        id: 'image1',
        backgroundColor: 'linear-gradient(75deg, rgba(127, 90, 240, 0.9), rgba(255, 112, 166, 0.9))',
        imageAlign: 'center',
        imageFit: 'fill',
        contentAlign: 'center',
        url: '/assets/images/pages/builder/genz-banner.jpg',
        urlMobile: '/assets/images/pages/builder/genz-banner.jpg',
        urlLibrary: '/assets/images/pages/builder/genz-banner.jpg',
        urlLibraryMobile: '/assets/images/pages/builder/genz-banner.jpg',
        imageSelectType: 'library',
        imgSrc: '',
        fontWeightTitle: 900,
        fontWeightDescription: 400,
        fontSizeTitle: 48,
        fontSizeDescription: 24,
        colorTitle: '#ffffff',
        colorDescription: '#ffffff',
        textAlignTitle: 'center',
        textAlignDescription: 'center',
        overlayColor: '',
        overlayOpacity: 1,
        buttonRadius: 24,
        paddingButton: {
          top: 24,
          right: 32,
          bottom: 24,
          left: 32
        },
        marginButton: {
          top: 10,
          bottom: 10
        },
        titleText: 'DEAL XỊN CHO GEN Z',
        descriptionText: 'Data căng đét, giá cực mượt. Lướt "tóp tóp", chiến game thả ga!',
        buttonText: 'SĂN DEAL NGAY!',
        buttonBackgroundColor: '#E28800',
        buttonTextColor: '#ffffff',
        buttonStyle: 'primary',
        buttonSize: 'large',
        buttonFontSize: 18,
        buttonFontWeight: 700,
        hasIcon: false,
        icon: '',
        iconAlign: 'start',
        iconColor: '#ffffff',
        showTitle: {
          desktop: true,
          mobile: true,
          tablet: true
        },
        showDescription: {
          desktop: true,
          mobile: true,
          tablet: true
        },
        showButton: {
          desktop: true,
          mobile: true,
          tablet: true
        },
        href: '',
        isShow: {
          desktop: true,
          mobile: true,
          tablet: true
        },
        isAdded: true
      }
    ]
  }
}

export const defaultResponsiveGenZBannerListProps = {
  desktop: defaultGenZBannerListProps('desktop'),
  tablet: defaultGenZBannerListProps('tablet'),
  mobile: defaultGenZBannerListProps('mobile')
}
