import moment from 'moment'

import { RESET_PADDING, RESET_MARGIN } from './common'
import type { ButtonSize } from '@/types/page-builder'
import { defaultTextProps } from './text'

import { defaultRowProps } from './row'
import type { ScreenTypeValues } from '@/redux-store/slices/builderSlice'

const defaultButtonProps = {
  text: 'Khám phá ngay',
  size: 'large' as ButtonSize,
  buttonBackgroundColor: '#EF9304',
  color: '#ffffff',
  radius: 12,
  buttonType: 'primary',
  width: 136,
  margin: {
    ...RESET_MARGIN,
    top: 30
  },
  padding: RESET_PADDING
}

const bannerText2 = `Được ưa thích trong tháng ${moment().month() + 1}`

const defaultRowContainerProps = (screenType: ScreenTypeValues) => {
  return {
    ...defaultRowProps(screenType),
    colWidths: [6, 6],
    height: 458,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 4
  }
}

const defaultBannerColumnProps = {
  ...defaultRowProps('desktop'),
  imgSrc: '/assets/images/pages/house-hold/banner-telecommunication.webp',
  imgSrcMobile: '/assets/images/pages/house-hold/banner-telecommunication.webp',
  backgroundType: 'IMAGE',
  contentAlign: 'center',
  justifyContent: 'center',
  height: 'auto',
  border: {
    radius: 24
  }
}

const defaultContentColumnProps = {
  ...defaultRowProps('desktop'),
  backgroundColor: 'rgb(230, 245, 250)',
  backgroundType: 'COLOR',
  contentAlign: 'left',
  height: '100%',
  padding: {
    top: 40,
    bottom: 40,
    left: 30,
    right: 30
  },
  border: {
    radius: 24
  }
}

const defaultCustomTextProps = {
  ...defaultTextProps('desktop'),
  text: 'Top sản phẩm',
  color: '#ffffff',
  backgroundColor: 'transparent',
  fontSize: 42,
  fontWeight: 600,
  textAlign: 'center'
}

export const defaultTextConfigs = {
  heading: {
    desktop: {
      ...defaultTextProps('desktop'),
      text: 'Danh mục sản phẩm',
      backgroundColor: 'transparent'
    },
    tablet: {},
    mobile: {
      ...defaultTextProps('desktop'),
      text: 'Danh mục sản phẩm',
      backgroundColor: 'transparent'
    }
  },
  title: {
    desktop: {
      ...defaultTextProps('desktop'),
      width: '450px',
      backgroundColor: 'transparent',
      fontSize: 56,
      fontWeight: 500
    },
    tablet: {},
    mobile: {
      ...defaultTextProps('desktop'),
      backgroundColor: 'transparent',
      fontSize: 24,
      fontWeight: 600,
      width: '100%'
    }
  },
  description: {
    desktop: {
      ...defaultTextProps('desktop'),
      backgroundColor: 'transparent',
      fontSize: 16,
      fontWeight: 400
    },
    tablet: {},
    mobile: {
      ...defaultTextProps('desktop'),
      backgroundColor: 'transparent',
      fontSize: 14,
      fontWeight: 400
    }
  }
}

export const defaultTelecomProductsProps = {
  hasBannerButton: false,
  rowContainer: {
    desktop: {
      ...defaultRowContainerProps('desktop')
    },
    tablet: {
      ...defaultRowContainerProps('desktop')
    },
    mobile: {
      ...defaultRowContainerProps('mobile'),
      colWidths: [12],
      gap: 6,
      height: 'auto'
    }
  },
  bannerRow: {
    desktop: {
      ...defaultRowContainerProps('desktop'),
      colWidths: [12],
      height: 458
    },
    tablet: {},
    mobile: {
      ...defaultRowContainerProps('mobile'),
      colWidths: [12],
      height: 265,
      margin: {
        ...RESET_MARGIN,
        top: 24
      }
    }
  },
  contentRow: {
    desktop: {
      ...defaultRowContainerProps('desktop'),
      colWidths: [12],
      height: 458
    },
    tablet: {
      ...defaultRowContainerProps('desktop'),
      colWidths: [12],
      height: 458
    },
    mobile: {
      ...defaultRowContainerProps('mobile'),
      colWidths: [12],
      height: 265,
      margin: {
        ...RESET_MARGIN
      }
    }
  },
  // Props cột bên trái
  bannerColumn: {
    desktop: {
      ...defaultBannerColumnProps
    },
    tablet: { ...defaultBannerColumnProps },
    mobile: {
      ...defaultBannerColumnProps
    }
  },
  bannerText1: {
    desktop: {
      ...defaultCustomTextProps
    },
    tablet: { ...defaultCustomTextProps },
    mobile: {
      ...defaultCustomTextProps,
      fontSize: 24,
      fontWeight: 600
    }
  },
  bannerText2: {
    desktop: {
      ...defaultCustomTextProps,
      text: bannerText2,
      fontSize: 32,
      fontWeight: 400,
      width: '100%'
    },
    tablet: {
      ...defaultCustomTextProps,
      text: bannerText2,
      fontSize: 32,
      fontWeight: 400,
      width: '100%'
    },
    mobile: {
      ...defaultCustomTextProps,
      text: bannerText2,
      fontSize: 18,
      fontWeight: 400,
      textAlign: 'center',
      width: '100%'
    }
  },
  bannerButton: {
    desktop: {
      ...defaultButtonProps
    },
    tablet: {
      ...defaultButtonProps
    },
    mobile: {
      ...defaultButtonProps,
      margin: {
        ...RESET_MARGIN,
        top: 20
      }
    }
  },

  // Props cột bên phải
  contentColumn: {
    desktop: {
      ...defaultContentColumnProps,
      backgroundColor: 'rgb(230, 245, 250)',
      backgroundType: 'COLOR',
      contentAlign: 'left',
      height: '100%',
      padding: {
        top: 40,
        bottom: 40,
        left: 30,
        right: 30
      },
      border: {
        radius: 24
      }
    },
    tablet: {
      ...defaultContentColumnProps
    },
    mobile: {
      ...defaultContentColumnProps,

      padding: {
        top: 16,
        bottom: 16,
        left: 16,
        right: 16
      }
    }
  },
  contentText1: {
    desktop: { ...defaultTextProps('desktop'), text: 'Danh mục sản phẩm', backgroundColor: 'transparent' },
    tablet: {},
    mobile: {
      ...defaultTextProps('desktop'),
      text: 'Danh mục sản phẩm',
      backgroundColor: 'transparent'
    }
  },
  contentText2: {
    desktop: {
      ...defaultTextProps('desktop'),
      text: 'Các Sản Phẩm Viễn Thông',
      width: '450px',
      backgroundColor: 'transparent',
      fontSize: 56,
      fontWeight: 500
    },
    tablet: {},
    mobile: {
      ...defaultTextProps('desktop'),
      text: 'Các Sản Phẩm Viễn Thông',
      backgroundColor: 'transparent',
      fontSize: 24,
      fontWeight: 600,
      width: '100%'
    }
  },
  contentText3: {
    desktop: {
      ...defaultTextProps('desktop'),
      text: 'Sản phẩm dịch vụ và giải pháp số từ những nhà cung cấp hàng đầu, được đóng gói và sẵn sàng tích hợp theo nhu cầu của doanh nghiệp.',
      backgroundColor: 'transparent',
      fontSize: 16,
      fontWeight: 400
    },
    tablet: {},
    mobile: {
      ...defaultTextProps('desktop'),
      text: 'Sản phẩm dịch vụ và giải pháp số từ những nhà cung cấp hàng đầu, được đóng gói và sẵn sàng tích hợp theo nhu cầu của doanh nghiệp.',
      backgroundColor: 'transparent',
      fontSize: 14,
      fontWeight: 400
    }
  }
}
