import { baseColorLight } from '@/utils/colors'
import type { ImageCustomizeProps, ResponsiveBaseCustomizeProps, RowCustomizeProps } from '@/types/page-builder'
import { defaultRowProps } from '@/constants/page-builder/row'
import { defaultButtonProps, defaultImageProps } from '@/constants/page-builder'

// region constants
const newPaddingMarginRow = {
  padding: { top: 8, bottom: 8, left: 8, right: 8 }
}

const responsiveRowProps: ResponsiveBaseCustomizeProps<RowCustomizeProps> = {
  desktop: {
    ...defaultRowProps('desktop'),
    ...newPaddingMarginRow,
    colWidths: [1, 11],
    backgroundType: 'COLOR',
    backgroundColor: baseColorLight['sme-blue-2']
  },
  tablet: {
    ...defaultRowProps('tablet'),
    ...newPaddingMarginRow,
    colWidths: [1, 11],
    backgroundType: 'COLOR',
    backgroundColor: baseColorLight['sme-blue-2']
  },
  mobile: {
    ...defaultRowProps('mobile'),
    ...newPaddingMarginRow,
    colWidths: [1, 11],
    backgroundType: 'COLOR',
    backgroundColor: baseColorLight['sme-blue-2']
  }
}

const responsiveColumn1Props: ResponsiveBaseCustomizeProps<RowCustomizeProps> = {
  desktop: {
    ...defaultRowProps('desktop'),
    backgroundColor: baseColorLight['sme-blue-2']
  },
  tablet: {
    ...defaultRowProps('tablet'),
    backgroundColor: baseColorLight['sme-blue-2']
  },
  mobile: {
    ...defaultRowProps('mobile'),
    backgroundColor: baseColorLight['sme-blue-2']
  }
}

const responsiveColumn2Props: ResponsiveBaseCustomizeProps<RowCustomizeProps> = {
  desktop: {
    ...defaultRowProps('desktop'),
    padding: { top: 8, bottom: 0, left: 0, right: 0 },
    backgroundColor: baseColorLight['sme-blue-2'],
    contentAlign: 'start'
  },
  tablet: {
    ...defaultRowProps('tablet'),
    padding: { top: 8, bottom: 0, left: 0, right: 0 },
    backgroundColor: baseColorLight['sme-blue-2'],
    contentAlign: 'start'
  },
  mobile: {
    ...defaultRowProps('mobile'),
    padding: { top: 8, bottom: 0, left: 0, right: 0 },
    backgroundColor: baseColorLight['sme-blue-2'],
    contentAlign: 'start'
  }
}

export const responsiveImageProps: ResponsiveBaseCustomizeProps<ImageCustomizeProps> = {
  desktop: {
    ...defaultImageProps('desktop'),
    imgSrc: '/assets/images/logo.svg',
    align: 'start',
    height: '36px',
    backgroundColor: baseColorLight['sme-blue-2'],
    imageFit: 'fit'
  },
  tablet: {
    ...defaultImageProps('tablet'),
    imgSrc: '/assets/images/logo.svg',
    align: 'start',
    height: '36px',
    backgroundColor: baseColorLight['sme-blue-2'],
    imageFit: 'fit'
  },
  mobile: {
    ...defaultImageProps('mobile'),
    imgSrc: '/assets/images/logo.svg',
    align: 'start',
    height: '36px',
    backgroundColor: baseColorLight['sme-blue-2'],
    imageFit: 'fit'
  }
}

export const defaultResponsiveHeaderProps = {
  row: responsiveRowProps,
  column1: responsiveColumn1Props,
  column2: responsiveColumn2Props,
  image: responsiveImageProps,
  desktop: {
    backgroundColor: '#FFFFFF',
    fixedHeader: true,
    iconColor: '#0F1319',
    icon: 'NotificationIcon',
    iconSize: 32,
    iconAlign: 'start',
    loginButton: {
      ...defaultButtonProps('desktop'),
      fontSize: 14,
      fontWeight: 500,
      radius: 6,
      text: 'Đăng nhập',
      buttonType: 'primary',
      buttonBackgroundColor: '#FFFFFF',
      color: '#0F1319',
      href: '/sme-portal/login',
      width: '104px',
      padding: { top: 8, right: 16, bottom: 8, left: 16 }
    },
    registerButton: {
      ...defaultButtonProps('desktop'),
      fontSize: 14,
      fontWeight: 500,
      radius: 6,
      text: 'Đăng ký',
      buttonType: 'primary',
      buttonBackgroundColor: '#0070c4',
      color: '#FFFFFF',
      href: '/sme-portal/register',
      width: '87px',
      padding: { top: 8, right: 16, bottom: 8, left: 16 }
    }
  },
  tablet: {
    backgroundColor: '#FFFFFF',
    fixedHeader: true,
    iconColor: '#0F1319',
    icon: 'NotificationIcon',
    iconSize: 32,
    iconAlign: 'start',
    loginButton: defaultButtonProps('tablet'),
    registerButton: defaultButtonProps('tablet')
  },
  mobile: {
    backgroundColor: '#FFFFFF',
    fixedHeader: true,
    iconColor: '#0F1319',
    icon: 'NotificationIcon',
    iconSize: 32,
    iconAlign: 'start',
    loginButton: defaultButtonProps('mobile'),
    registerButton: defaultButtonProps('mobile')
  }
}
// endregion
