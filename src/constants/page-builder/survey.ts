import type { ButtonSize } from '@/types/page-builder'
import type { TypeInput } from '@/types/page-builder/inputFormTypes'
import type { SurveyCustomizeProps } from '@/types/page-builder/surveyTypes'
import { DEFAULT_BASE_PROPS } from './common'

import { defaultRowProps as defaultColumnProps } from './row'

export const defaultSurveyColumnProps = {
  ...defaultColumnProps(),
  padding: {
    top: 24,
    bottom: 24,
    left: 24,
    right: 24
  },
  imgSrc: '/assets/images/pages/page-builder/survey.jpg',
  backgroundType: 'IMAGE',
  imageAlign: 'center',
  imageFit: 'fill',
  margin: { top: 0, bottom: 0 },
  backgroundColor: '#ffffff'
}

export const defaultTextProps = {
  text: 'Khảo sát với chúng tôi !',
  fontSize: 36,
  fontWeight: 700,
  color: '#ffffff',
  margin: { top: 0, bottom: 0 },
  textAlign: 'center' as const
}

export const defaultSpacerProps = {
  height: 72
}

export const defaultInputFormProps = {
  placeHolder: 'Nhập email',
  maxWidth: '75%',
  padding: DEFAULT_BASE_PROPS.padding,
  margin: DEFAULT_BASE_PROPS.margin,
  typeInput: 'INPUT' as TypeInput,
  size: 'middle' as const
}

export const defaultButtonRowProps = {
  ...defaultColumnProps(),
  colWidths: [4, 4, 4],
  padding: DEFAULT_BASE_PROPS.padding,
  margin: DEFAULT_BASE_PROPS.margin,
  backgroundColor: 'transparent'
}

export const defaultButtonProps = {
  text: 'Tra cứu khảo sát',
  size: 'middle' as ButtonSize,
  buttonBackgroundColor: '#ffffff',
  color: '#0070C4',
  borderColor: '#0070C4',
  radius: 12
}

export const defaultResponsiveSurveyProps: Partial<SurveyCustomizeProps> = {
  column: {
    desktop: defaultSurveyColumnProps,
    tablet: defaultSurveyColumnProps,
    mobile: defaultSurveyColumnProps
  },
  text1: {
    desktop: defaultTextProps,
    tablet: defaultTextProps,
    mobile: {
      ...defaultTextProps,
      fontSize: 20,
      fontWeight: 700
    }
  },
  text2: {
    desktop: {
      ...defaultTextProps,
      text: 'Khảo sát ngay và luôn !',
      fontSize: 14,
      fontWeight: 500
    },
    tablet: {
      text: 'Khảo sát ngay và luôn !',
      fontSize: 14,
      fontWeight: 500
    },
    mobile: {
      ...defaultTextProps,
      text: 'Khảo sát ngay và luôn !',
      fontSize: 12,
      fontWeight: 500
    }
  },
  spacer1: {
    desktop: defaultSpacerProps,
    tablet: {
      ...defaultSpacerProps,
      height: 64
    },
    mobile: {
      ...defaultSpacerProps,
      height: 64
    }
  },
  inputForm: {
    desktop: defaultInputFormProps,
    tablet: {
      ...defaultInputFormProps,
      maxWidth: '90%'
    },
    mobile: {
      ...defaultInputFormProps,
      maxWidth: '100%'
    }
  },
  spacer2: {
    desktop: {
      ...defaultSpacerProps,
      height: 24
    },
    tablet: {
      ...defaultSpacerProps,
      height: 24
    },
    mobile: {
      ...defaultSpacerProps,
      height: 24
    }
  },
  row: {
    desktop: {
      ...defaultButtonRowProps
    },
    tablet: defaultButtonRowProps,
    mobile: defaultButtonRowProps
  },
  button1: {
    desktop: defaultButtonProps,
    tablet: defaultButtonProps,
    mobile: defaultButtonProps
  },
  button2: {
    desktop: {
      ...defaultButtonProps,
      text: 'Khảo sát của VNPT'
    },
    tablet: {
      ...defaultButtonProps,
      text: 'Khảo sát của VNPT'
    },
    mobile: {
      ...defaultButtonProps,
      text: 'Khảo sát của VNPT'
    }
  },
  button3: {
    desktop: {
      ...defaultButtonProps,
      text: 'Khảo sát của TT & TT'
    },
    tablet: {
      ...defaultButtonProps,
      text: 'Khảo sát của TT & TT'
    },
    mobile: {
      ...defaultButtonProps,
      text: 'Khảo sát của TT & TT'
    }
  }
}
