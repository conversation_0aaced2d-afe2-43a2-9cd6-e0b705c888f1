import type { BorderType } from '@/types/page-builder'
import { defaultIconLibsProps } from './iconLibs'
import { defaultImageProps } from './images'
import { defaultRowProps } from './row'
import { RESET_MARGIN, RESET_PADDING } from './common'

const defaultWhyChooseUsRowProps = {
  ...defaultRowProps(),
  padding: { top: 40, bottom: 44, left: 10, right: 10 },
  colWidths: [4, 3, 5],
  width: '100%',
  imgSrc: '/assets/images/pages/home/<USER>',
  backgroundType: 'IMAGE',
  height: 520,
  imageFit: 'fill'
}

const defaultImageWhyChooseUsProps = {
  ...defaultImageProps(),
  margin: RESET_MARGIN,
  padding: RESET_PADDING,
  width: 295,
  height: 436,
  imgSrc: '/assets/images/pages/home/<USER>',
  imgSrcMobile: '/assets/images/pages/home/<USER>'
}

const defaultColumnContainerProps = {
  ...defaultRowProps(),
  contentAlign: 'start',
  justifyContent: 'start',
  backgroundColor: 'transparent'
}

const defaultTextHeadingProps = {
  text: 'Tại sao chọn VNPT',
  fontSize: 32,
  fontWeight: 600,
  color: '#ffffff',
  textAlign: 'left'
}

const defaultTextProps = {
  fontSize: 14,
  fontWeight: 400,
  color: '#FFFFFF',
  textAlign: 'left'
}

const defaultButtonProps = {
  text: 'Tìm hiểu thêm',
  buttonBackgroundColor: '#EF9304',
  color: '#ffffff',
  iconColor: '#000000',
  radius: 12,
  buttonType: 'primary',
  width: 185,
  href: 'https://onesme.vn/blog/about',
  size: 'large'
}

const defaultIconLibProps = {
  ...defaultIconLibsProps(),
  margin: RESET_MARGIN,
  padding: RESET_PADDING,
  backgroundType: '',
  size: 11,
  icon: 'DotIcon',
  iconColor: '#95DBF9',
  bgIconColor: 'transparent',
  showDesc: {
    desktop: false,
    tablet: false,
    mobile: false
  },
  titleFontSize: 20,
  titleColor: '#95DBF9',
  border: {
    style: 'none' as BorderType,
    color: '',
    thickness: 0,
    radius: 0
  },
  contentPadding: RESET_PADDING
}

export const defaultWhyChooseUsProps = {
  container: {
    height: 'auto',
    background: '#FFFFFF',
    padding: ['0', '0', '0', '0']
  },
  rowContainer: {
    desktop: defaultWhyChooseUsRowProps,
    tablet: defaultWhyChooseUsRowProps,
    mobile: {
      ...defaultWhyChooseUsRowProps,
      height: 'auto',
      isBreakLine: true,
      padding: { top: 24, bottom: 24, left: 16, right: 16 },
      imgSrcMobile: '/assets/images/pages/home/<USER>'
    }
  },
  image: {
    desktop: defaultImageWhyChooseUsProps,
    tablet: defaultImageWhyChooseUsProps,
    mobile: {
      ...defaultImageWhyChooseUsProps,
      width: 306,
      height: 483
    }
  },
  columnContainer1: {
    desktop: defaultColumnContainerProps,
    tablet: defaultColumnContainerProps,
    mobile: defaultColumnContainerProps
  },
  textHeading1: {
    desktop: defaultTextHeadingProps,
    tablet: defaultTextHeadingProps,
    mobile: { ...defaultTextHeadingProps, fontSize: 24, textAlign: 'center' }
  },
  textContent1: {
    desktop: {
      ...defaultTextProps,
      text: 'Tại oneSME, VNPT giới thiệu nhiều sản phẩm, dịch vụ số thiết thực với doanh nghiệp phục vụ công tác chuyển đổi số, đồng thời không ngừng thay đổi & cải thiện nền tảng. Đến với oneSME các doanh nghiệp sẽ dễ dàng tìm kiếm, chủ động lựa chọn và tích hợp các dịch vụ, giải pháp số phù hợp với nhu cầu của doanh nghiệp mình.'
    },
    tablet: {
      ...defaultTextProps,
      text: 'Tại oneSME, VNPT giới thiệu nhiều sản phẩm, dịch vụ số thiết thực với doanh nghiệp phục vụ công tác chuyển đổi số, đồng thời không ngừng thay đổi & cải thiện nền tảng. Đến với oneSME các doanh nghiệp sẽ dễ dàng tìm kiếm, chủ động lựa chọn và tích hợp các dịch vụ, giải pháp số phù hợp với nhu cầu của doanh nghiệp mình.'
    },
    mobile: {
      ...defaultTextProps,
      text: 'Tại oneSME, VNPT giới thiệu nhiều sản phẩm, dịch vụ số thiết thực với doanh nghiệp phục vụ công tác chuyển đổi số, đồng thời không ngừng thay đổi & cải thiện nền tảng. Đến với oneSME các doanh nghiệp sẽ dễ dàng tìm kiếm, chủ động lựa chọn và tích hợp các dịch vụ, giải pháp số phù hợp với nhu cầu của doanh nghiệp mình.',
      fontSize: 14
    }
  },
  buttonDesktop: {
    desktop: defaultButtonProps,
    tablet: defaultButtonProps,
    mobile: { ...defaultButtonProps, hide: true }
  },
  buttonMobile: {
    desktop: { ...defaultButtonProps, hide: true },
    tablet: defaultButtonProps,
    mobile: { ...defaultButtonProps, hide: false, width: '100%' }
  },
  spacer1: { desktop: { height: 28 }, tablet: { height: 28 }, mobile: { height: 24 } },
  spacer2: { desktop: { height: 14 }, tablet: { height: 14 }, mobile: { height: 14 } },
  spacer3: { desktop: { height: 20 }, tablet: { height: 20 }, mobile: { height: 20 } },
  spacer4: { desktop: { height: 0 }, tablet: { height: 0 }, mobile: { height: 24 } },

  columnContainer2: {
    desktop: defaultColumnContainerProps,
    tablet: defaultColumnContainerProps,
    mobile: { ...defaultColumnContainerProps, hide: true }
  },
  columnChild: {
    desktop: defaultColumnContainerProps,
    tablet: defaultColumnContainerProps,
    mobile: defaultColumnContainerProps
  },
  iconLib1: {
    desktop: { ...defaultIconLibProps, title: 'Bộ giải pháp toàn diện' },
    tablet: { ...defaultIconLibProps, title: 'Bộ giải pháp toàn diện' },
    mobile: { ...defaultIconLibProps, title: 'Bộ giải pháp toàn diện' }
  },
  iconLib2: {
    desktop: { ...defaultIconLibProps, title: 'Mua Hàng dễ dàng' },
    tablet: { ...defaultIconLibProps, title: 'Mua Hàng dễ dàng' },
    mobile: { ...defaultIconLibProps, title: 'Mua Hàng dễ dàng' }
  },
  iconLib3: {
    desktop: { ...defaultIconLibProps, title: 'Tư vấn chuyên sâu' },
    tablet: { ...defaultIconLibProps, title: 'Tư vấn chuyên sâu' },
    mobile: { ...defaultIconLibProps, title: 'Tư vấn chuyên sâu' }
  },
  textContent2: {
    desktop: {
      ...defaultTextProps,
      text: 'Các dịch vụ được sắp xếp theo từng nhóm lĩnh vực như Viễn thông, CNTT, Quản trị doanh nghiệp, Giao dịch điện tử, các giải pháp chuyên ngành cho Y tế, Giáo dục, Nông nghiệp, Dược phẩm, Logistic, ...',
      padding: { top: 0, right: 0, bottom: 0, left: 28 }
    },
    tablet: {
      ...defaultTextProps,
      text: 'Các dịch vụ được sắp xếp theo từng nhóm lĩnh vực như Viễn thông, CNTT, Quản trị doanh nghiệp, Giao dịch điện tử, các giải pháp chuyên ngành cho Y tế, Giáo dục, Nông nghiệp, Dược phẩm, Logistic, ...',
      padding: { top: 0, right: 0, bottom: 0, left: 28 }
    },
    mobile: {
      ...defaultTextProps,
      text: 'Các dịch vụ được sắp xếp theo từng nhóm lĩnh vực như Viễn thông, CNTT, Quản trị doanh nghiệp, Giao dịch điện tử, các giải pháp chuyên ngành cho Y tế, Giáo dục, Nông nghiệp, Dược phẩm, Logistic, ...',
      padding: { top: 0, right: 0, bottom: 0, left: 28 }
    }
  },
  textContent3: {
    desktop: {
      ...defaultTextProps,
      text: 'oneSME tự động tổng hợp nhu cầu của khách hàng để đưa ra các ưu đãi thích hợp. Đặc biệt, oneSME là nền tảng chuyển đổi số đầu tiên tại Việt Nam cung cấp các giải pháp số toàn trình trên môi trường online.',
      padding: { top: 0, right: 0, bottom: 0, left: 28 }
    },
    tablet: {
      ...defaultTextProps,
      text: 'oneSME tự động tổng hợp nhu cầu của khách hàng để đưa ra các ưu đãi thích hợp. Đặc biệt, oneSME là nền tảng chuyển đổi số đầu tiên tại Việt Nam cung cấp các giải pháp số toàn trình trên môi trường online.',
      padding: { top: 0, right: 0, bottom: 0, left: 28 }
    },
    mobile: {
      ...defaultTextProps,
      text: 'oneSME tự động tổng hợp nhu cầu của khách hàng để đưa ra các ưu đãi thích hợp. Đặc biệt, oneSME là nền tảng chuyển đổi số đầu tiên tại Việt Nam cung cấp các giải pháp số toàn trình trên môi trường online.',
      padding: { top: 0, right: 0, bottom: 0, left: 28 }
    }
  },
  textContent4: {
    desktop: {
      ...defaultTextProps,
      text: 'Với hệ thống trải rộng & đội ngũ kỹ sư CNTT giàu kinh nghiệm trên toàn quốc, VNPT sẵn sàng hỗ trợ 24/7 khi khách hàng cần, đồng thời có thể “may đo” sản phẩm, dịch vụ số phù hợp với nhu cầu của tất cả các doanh nghiệp SME cũng như các hộ kinh doanh.',
      padding: { top: 0, right: 0, bottom: 0, left: 28 }
    },
    tablet: {
      ...defaultTextProps,
      text: 'Với hệ thống trải rộng & đội ngũ kỹ sư CNTT giàu kinh nghiệm trên toàn quốc, VNPT sẵn sàng hỗ trợ 24/7 khi khách hàng cần, đồng thời có thể “may đo” sản phẩm, dịch vụ số phù hợp với nhu cầu của tất cả các doanh nghiệp SME cũng như các hộ kinh doanh.',
      padding: { top: 0, right: 0, bottom: 0, left: 28 }
    },
    mobile: {
      ...defaultTextProps,
      text: 'Với hệ thống trải rộng & đội ngũ kỹ sư CNTT giàu kinh nghiệm trên toàn quốc, VNPT sẵn sàng hỗ trợ 24/7 khi khách hàng cần, đồng thời có thể “may đo” sản phẩm, dịch vụ số phù hợp với nhu cầu của tất cả các doanh nghiệp SME cũng như các hộ kinh doanh.',
      padding: { top: 0, right: 0, bottom: 0, left: 28 }
    }
  }
}
