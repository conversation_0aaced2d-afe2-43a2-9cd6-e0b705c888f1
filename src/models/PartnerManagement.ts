import type { CertificateType } from '@/types/partner-portal/partner'
import Base from './Base'

function appendIdToUrl(url: string, id?: number | string): string {
  return id ? `${url}/${id}` : url
}

class PartnerManagement extends Base {
  adminCreatePartner = async (data: any) => this.apiPost('/users-admin/create-dev', data)

  devCreatePartner = async (data: any) => this.apiPost('/users-dev/register', data)

  checkPhoneNumber = async (value: string, id?: number) => {
    return this.apiGet(appendIdToUrl(`/users/check-phone/${value}`, id))
  }

  checkPrimaryEmail = async (value: string, id?: number) => {
    return this.apiGet(appendIdToUrl(`/users/check-primary-email/${value}`, id))
  }

  checkEmail = async (value: string, id?: number) => {
    return this.apiGet(appendIdToUrl(`/users/check-email/${value}`, id))
  }

  checkTaxCode = async (value: string, id?: number) => {
    return this.apiGet(appendIdToUrl(`/users/check-tax-code/${value}`, id))
  }

  checkRepPersonalCertNumber = async (value: string, id?: number, repPersonalCertType?: CertificateType) => {
    const url = appendIdToUrl(`/users/check-rep-personal-cert-number/${repPersonalCertType}/${value}`, id)

    return this.apiGet(url)
  }

  // Lấy thông tin chi tiết đối tác
  getPartnerDetailInfo = async (id: string | number) => {
    const res = await this.apiGet(`/users/${id}/partner`)

    return res
  }

  getDocumentsList = async (id: string | number, params: any) => {
    const res = await this.apiGet(`/users/partner/contract/${id}`, params)

    return res
  }

  approvePartner = (body: any) => this.apiPostWithoutPrefix('/api/users-admin/approve', body)

  updatePassword = (body: any) => this.apiPutWithoutPrefix('/api/users-admin/change-password', body)

  deteleContract = (ids: any[]) => this.apiDelete('/users/delete-contract', ids)
}
const partnerManagementInstance = new PartnerManagement({ apiPrefix: '', isOauth: true })

export default partnerManagementInstance
