import Base from './Base'

class NewAddress extends Base {
  getAllRegion = () => this.apiGetWithoutPrefix(`/report/api/locations/regions`) as Promise<any>

  getProvinces = (params?: any) =>
    this.apiGetWithoutPrefix(`/report/api/locations/provinces?size=50`, params) as Promise<any>

  getWards = (query: any) =>
    this.apiGetWithoutPrefix(`/report/api/locations/provinces/${query?.provinceId}/wards?size=200`) as Promise<any>

  // Lấy danh sách phường/xã dựa trên list province id
  getWardsByParams = (params: any) => this.apiGetWithoutPrefix(`/report/api/locations/wards`, params) as Promise<any>

  getStreets = (query: any) =>
    this.apiGetWithoutPrefix(
      `/report/api/locations/provinces/${query?.provinceId}/wards/${query?.wardId}/streets?size=200`
    ) as Promise<any>

  // L<PERSON>y danh sách đường dựa trên list province id
  getStreetsByParams = (params: any) =>
    this.apiGetWithoutPrefix(`/report/api/locations/streets`, params) as Promise<any>
}

// eslint-disable-next-line import/no-anonymous-default-export
export default new NewAddress({
  apiPrefix: ''
})
