import Base from './Base'
import type { VirtualWarehouse, WarehouseType } from '@/types/inventory/CreateWarehouseType'

class Warehouse extends Base {
  uploadJSON = async (data: any) => this.apiPost('/common/mapping/flatten-keys', data)

  dataTemplate = async (code: any) => this.apiGet(`/template-dtos/${code}/fields`)

  getRuleTypes = async () => this.apiGet('/common/get-all-conversion-rule-types')

  applyRules = async (data: any) => this.apiPost('/common/apply-conversion-rule', data)

  getListPartner = async () => this.apiGet('/partners')

  getListWarehouseTypes = async (params: any) => this.apiGet('/common/warehouseType', params)

  searchConnectionConfigList = async (params: any) => this.apiGet('/partnerApis/getAll', params)

  createManually = async (data: any) => this.apiPost('/warehouses', data)

  syncData = async (data: any) => this.apiPost('/warehouses/virtual-warehouses/sync', data)

  connectionTest = async (data: any) => this.apiPost('/partnerApis/connection-test', data)

  getWarehouseListForForm = async (params: any) => this.apiGet('/warehouses/get-all-warehouse', params)

  createConnection = async (data: any) => this.apiPost('/partnerApis', data)

  createWarehouseAuto = async (data: any) => this.apiPost('/warehouses/create-auto', data)

  detailConnection = async (id: any) => this.apiGet(`/partnerApis/${id}`)

  updateConnection = async (id: number | string, data: any) => this.apiPut(`/partnerApis/${id}`, data)

  // danh sách loại kho
  getAllWarehouseType = async (params: any) => await this.apiGet('/warehouse-types', params)

  //tạo loại kho
  createWarehouseType = (data: WarehouseType) => this.apiPost('/warehouse-types', data)

  //xóa loại kho
  deleteWarehouseTypeByIds = (ids: number[]) => {
    const query = `ids=${ids.join(',')}`

    return this.apiDelete(`/warehouse-types?${query}`)
  }

  //xem chi tiết loại kho
  getWarehouseTypeDetail = async (id: any) => await this.apiGet(`/warehouse-types/${id}`)

  //sửa loại kho
  updateWarehouseType = (data: WarehouseType, id: string | number) => this.apiPut(`/warehouse-types/${id}`, data)

  //advance search: danh sách kho ảo áp dụng
  getListVirtualWarehouse = () => this.apiGet('/warehouse-types/virtual')

  //danh sách kho ảo
  getAllVirtualWarehouse = async (params: any) => this.apiGet('/warehouses/search', params)

  //advance search: danh sách loại kho
  getListWarehouseType = () => this.apiGet('/common/warehouseType')

  //advance search: danh sách mã bưu chính
  getListZipCode = () => this.apiGet('/common/get-list-zipcodes')

  //advance search: danh sách tên liên hệ
  getListContactName = () => this.apiGet('/common/get-list-contact-names')

  //advance search: danh sách sdt liên hệ
  getListContactPhone = () => this.apiGet('/common/get-list-contact-phones')

  //advance search: danh sách email liên hệ
  getListContactEmail = () => this.apiGet('/common/get-list-contact-emails')

  // xóa kho ảo
  deleteVirtualWarehouseByIds = (ids: number[]) => {
    return this.apiDelete(`/warehouses/delete`, {
      ids
    })
  }

  //chi tiết kho ảo: Thông tin chung
  getGeneralInfo = async (id: any) => await this.apiGet(`/warehouses/common-info?id=${id}`)

  // tab Thông tin chung: Chỉnh sửa kho ảo thủ công
  updateVirtualWarehouseById = (data: VirtualWarehouse) => this.apiPut('/warehouses/updateById', data)

  //chi tiết kho ảo: Cấu hình kết nối
  getConnectConfig = async (id: any) => await this.apiGet(`/warehouses/connection-configs?id=${id}`)

  //chi tiết kho ảo: Tồn kho hiện tại
  getCurrentInventory = async (params: any) => await this.apiGet('/warehouses/stocks-info', params)

  //chi tiết kho ảo: Lịch sử thay đổi
  getChangeHistory = async (params: any) => await this.apiGet('/warehouses/change-history', params)

  // danh sách cấu hình kết nối
  getAllConnectionConfig = async (params: any) => await this.apiGet('/partnerApis', params)

  // xóa cấu hình kết nối
  deleteConnectConfigByIds = (ids: number[]) => {
    const query = `ids=${ids.join(',')}`

    return this.apiDelete(`/partnerApis?${query}`)
  }

  // chi tiết cấu hình kết nối
  getConnectConfigDetail = async (id: any) => await this.apiGet(`/partnerApis/${id}`)

  // chi tiết cấu hình kết nối: tab Lịch sử đồng bộ
  getSyncHistory = async (id: any) => await this.apiGet(`/partnerApis/history/${id}`)

  //danh sách lịch sử đồng bộ
  getAllWarehouseHistory = async (params: any) => await this.apiGet('/warehouse-history', params)

  // chi tiết lịch sử đồng bộ: Thông tin chung
  getGeneralWarehouseHistory = async (id: any) => await this.apiGet(`/warehouse-history?id=${id}`)

  // chi tiết lịch sử đồng bộ
  getWarehouseHistoryDetail = async (id: any, params: any) =>
    await this.apiGet(`/warehouse-history/detail/${id}/`, params)

  updateStockByAction = async (data: any) => this.apiPost('/warehouse-stocks/update', data)

  getInventoryByProductId = async (id: any) => await this.apiGet(`/warehouse-stocks/service/${id}`)
}

const warehouseInstance = new Warehouse({ apiPrefix: '', isInventory: true })

export default warehouseInstance
