import Base from './Base'

class DashboradManagement extends Base {
  // Tổng quan thống kê khách hàng (overview)
  overviewCustomerByMonth = async (query?: any) => {
    const res = this.apiPostWithoutPrefix('/report/api/dev-portal/dashboard/overview-customers', query)

    return res
  }

  // Tổng quan thống kê doanh thu (overview)
  overviewRevenueByMonth = async (query?: any) => {
    const res = this.apiPostWithoutPrefix(
      `/report/api/dev-portal/dashboard/overview-revenue-by-month/${query?.id}`,
      query
    )

    return res
  }

  // Preview thống kê doanh thu (preview)
  previewRevenueByMonth = async (query?: any) => {
    const res = this.apiPostWithoutPrefix(`/report/api/dev-portal/dashboard/preview-revenue-by-month`, query)

    return res
  }

  // Bi<PERSON>u đồ doanh thu (overview)
  overviewServiceRevenue = async (query?: any) => {
    const res = this.apiPostWithoutPrefix(
      `/report/api/dev-portal/dashboard/overview-service-revenue/${query.id}`,
      query
    )

    return res
  }

  // Biểu đồ top gói cước có doanh thu cao nhất (overview)
  overviewTopPricing = async (query?: any) => {
    const res = this.apiPostWithoutPrefix(
      `/report/api/dev-portal/dashboard/overview-pricing-revenue/${query.id}`,
      query
    )

    return res
  }

  // Export dữ liệu chi tiết biểu đồ doanh thu
  exportTopPricingData = async (query: any) => {
    const res = this.apiPostWithoutPrefixDownload('/report/api/dev-portal/dashboard/export-pricing-revenue', query)

    return res
  }

  reportTopPricingData = async (query: any) => {
    const res = this.apiPostWithoutPrefixDownload('/report/api/dev-portal/dashboard/report-pricing-revenue', query)

    return res
  }

  getAccountHistory = async (id: string | number, params?: any) => {
    const res = await this.apiGetWithoutPrefix(`/api/admin-portal/account-history/${id}`, params)

    return res
  }

  getOverviewCustomer = async (query: any) => {
    const res = await this.apiPostWithoutPrefix(
      `/report/api/dev-portal/dashboard/overview-customers/${query.id}`,
      query
    )

    return res
  }

  getProviceList = async (params: any) => {
    return this.apiGetWithoutPrefix('/report/api/dev-portal/dashboard/combobox/province', params)
  }

  getServiceList = async (params: any) => {
    return this.apiGetWithoutPrefix('/report/api/dev-portal/dashboard/combobox/service-product', params)
  }
}

// eslint-disable-next-line import/no-anonymous-default-export
export default new DashboradManagement({ apiPrefix: '' })
