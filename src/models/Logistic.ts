import type { CarrierActionList } from '@/types/logistic/carrier'
import Base from './Base'

class Logistic extends Base {
  // Generate mã định danh
  generateCarrierCode = async () => await this.apiGet('/carrier/generate-code')

  //Lấy danh sách đơn vị vận chuyển
  getAllCarrier = async (params: any) => await this.apiGetWithoutPrefix('/logistic/api/carrier', params)

  deleteByIds = (params: number[]) => {
    return this.apiDeleteWithoutPrefix(`/logistic/api/carrier?ids=${params}`, {})
  }

  //Lấy thông tin chi tiết đơn vị vận chuyển
  getCarrierDetail = async (id: any) => await this.apiGetWithoutPrefix(`/logistic/api/carrier/${id}`)

  //Lấy danh sách đơn hàng áp dụng đơn vị vận chuyển
  getApplyOrder = async (id: any, params: any) =>
    await this.apiGetWithoutPrefix(`/logistic/api/carrier/apply-order/${id}`, params)

  //Lấy danh sách lịch sử cấu hình kết nối
  getMappingLog = async (id: any, params: any) =>
    await this.apiGetWithoutPrefix(`/logistic/api/carrier/history-config/${id}`, params)

  //Lấy danh sách lịch sử gọi api
  getApiCallLog = async (id: any, params: any) =>
    await this.apiGetWithoutPrefix(`/logistic/api/carrier/history-call-api/${id}`, params)

  // Tạo đơn vận chuyển
  createCarrier = (params: any) => this.apiPost('/carrier', params)

  // Update đơn vị vận chuyển
  updateCarrier = (params: any) => this.apiPut(`/carrier`, params)

  uploadJSON = async (data: any) => this.apiPost('/carrier/mapping/flatten-keys', data)

  // Lấy dữ liệu trường đích khi ánh xạ ( request + response )
  dataTemplate = async (code: any) => this.apiGet(`/template-dtos/convert-field/${code}`)

  getEndpontList = async (id: string, params: any) =>
    await this.apiGetWithoutPrefix(`/logistic/api/carrier/endpoint-mapping/${id}`, params)

  getEndpointConfigDetail = async (id: string, action: string) =>
    await this.apiGetWithoutPrefix(`/logistic/api/carrier/${id}/${action}`)

  // Danh sách các loại quy tắc chuyển đổi
  getRuleTypes = async () => this.apiGet('/template-dtos/conversion-rule-types')

  // Chuyển đổi dữ liệu
  applyRules = async (data: any) => this.apiPost('/template-dtos/apply-conversion-rule', data)

  // Lấy thông tin các action
  getActionList: () => Promise<CarrierActionList[]> = async () => this.apiGet(`/template-dtos`)

  // Validate tên đơn vị vận chuyển
  validateCarrierName = async ({ name, id }: { name: string; id?: number }) =>
    this.apiGet(`/carrier/validate-name`, { name, id })

  // Validate mã định danh
  validateCarrierCode = async ({ name, id }: { name: string; id?: number }) =>
    this.apiGet(`/carrier/validate-partner-code`, { name, id })
}
const LogisticInstance = new Logistic({ apiPrefix: '/logistic/api', isControlled: true })

export default LogisticInstance
