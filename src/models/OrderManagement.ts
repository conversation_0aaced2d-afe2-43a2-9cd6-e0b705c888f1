import Base from './Base'

class OrderManagement extends Base {
  getListOrder = async (query: any, portalType: 'admin' | 'dev') => {
    const res = await this.apiGetWithoutPrefix(`/api/${portalType}-portal/subscription/get-list-order`, query)

    return this.formatResPagination(res)
  }

  // Get detail order cart
  getDetailOrderCart = async (id: string, portalType: string) => {
    const res = await this.apiGetWithoutPrefix(
      `/api/portal/subscription/detail/bos/cart/${id}?portalType=${portalType}`
    )

    return res
  }

  // Get detail order
  getDetailOrder = async (id: string, portalType: string) => {
    const res = await this.apiGetWithoutPrefix(`/api/portal/subscription/detail/bos/${id}?portalType=${portalType}`)

    return res
  }

  // Get order overview
  getOrderOverview = async (orderCode: any, portalType: 'admin' | 'dev') => {
    const res = await this.apiGetWithoutPrefix(`/api/${portalType}-portal/subscription/${orderCode}/order-overview`)

    return res
  }

  // Get order overview
  getOrderOverviewCart = async (cartCode: any, portalType: 'admin' | 'dev') => {
    const res = await this.apiGetWithoutPrefix(`/api/${portalType}-portal/subscription/cart/${cartCode}/order-overview`)

    return res
  }

  // Lấy thông tin khách hàng với sub
  getCustomerInfo = async (subId: string | number) => {
    const res = await this.apiGetWithoutPrefix(`/api/portal/subscription/detail/bos/${subId}/customer`)

    return res
  }

  // Lấy thông tin khách hàng với Cart
  getCartCustomerInfo = async (cartCode: string | number) => {
    const res = await this.apiGetWithoutPrefix(`/api/portal/subscription/detail/bos/cart/${cartCode}/customer`)

    return res
  }

  // Lấy thông tin lịch sử mua hàng
  getCustomerHistory = async (userId: string | number, body: any) => {
    const res = await this.apiGetWithoutPrefix(`/api/admin-portal/subscription/${userId}/order-history`, body)

    return res
  }

  // Lấy thông tin sản phẩm đang sử dụng
  getUsedServices = async (userId: string | number, body: any) => {
    const res = await this.apiGetWithoutPrefix(`/api/admin-portal/subscription/${userId}/used-services`, body)

    return res
  }

  // Lấy thông tin sản phẩm đang sử dụng
  getInterestedServices = async (userId: string | number, body: any) => {
    const res = await this.apiGetWithoutPrefix(`/api/portal/subscription/detail/bos/${userId}/interested-service`, body)

    return res
  }

  // Lấy chi tiết tab sản phẩm dịch vụ với sub
  getSubServiceProduct = async (subId: string | number) => {
    const res = await this.apiGetWithoutPrefix(`/api/portal/subscription/detail/bos/${subId}/service-and-product`)

    return res
  }

  // Lấy chi tiết tab sản phẩm dịch vụ với cart
  getCartServiceProduct = async (cartCode: string | number) => {
    const res = await this.apiGetWithoutPrefix(
      `/api/portal/subscription/detail/bos/cart/${cartCode}/service-and-product`
    )

    return res
  }

  // Lấy danh sách lịch sử tương tác
  getStatusHistory = async (productOrderId: string | number, role: string, params?: any) => {
    const res = await this.apiGet(`/${role}-portal/subscription/detail/${productOrderId}/status/history`, params)

    return res
  }

  // Lấy danh sách tương tác
  getInteractedHistory = async (productOrderId: string | number, role: string, params?: any) => {
    const res = await this.apiGet(`/${role}-portal/subscription/detail/${productOrderId}/history`, params)

    return res
  }

  // Lấy chi tiết lịch sử thanh toán tổng quan
  getPaymentInfo = async (productOrderId: string | number, role: string) => {
    const res = await this.apiGet(`/${role}-portal/subscription/detail/${productOrderId}/payment-detail/history`)

    return res
  }

  // Lấy danh sách lịch sử thanh toán
  getPaymentHistory = async (productOrderId: string | number, role: string, params?: any) => {
    const res = await this.apiGet(`/${role}-portal/subscription/detail/${productOrderId}/payment/history`, params)

    return res
  }

  // Lấy danh sách hóa đơn
  getOrderBilling = async (productOrderId: string | number, role: string, params?: any) => {
    const res = await this.apiGet(`/${role}-portal/subscription/orders/${productOrderId}/billing`, params)

    return res
  }

  getListMst = async (query: any) => {
    const res = await this.apiGetWithoutPrefix('/api/common/combobox/get-list-sub-user-mst', query)

    return this.formatResPagination(res)
  }

  // Lấy thông tin lịch sử sub
  getSubHistory = async (subId: string | number, role: string, params?: any) => {
    const res = await this.apiGet(`/${role}-portal/subscription/${subId}/history`, params)

    return res
  }

  // Lấy thông tin lịch sử sub
  updateProgress = async (body?: any) => {
    const res = await this.apiPut(`/portal/subscription/order-item-status`, body)

    return res
  }

  // Lấy thông tin lịch sử sub
  getStepOptions = async (params?: any) => {
    const res = await this.apiGet(`/admin-portal/workflows/steps/combobox`, params)

    return res
  }
}

// eslint-disable-next-line import/no-anonymous-default-export
export default new OrderManagement({ apiPrefix: '' })
