import Base from '@/models/Base'
import type { ListProcessParams, ProcessListType } from '@/types/workflow/processManagement'

class Workflow extends Base {
  // danh sách tiến trình
  getListProcess = (params?: ListProcessParams) => this.apiGet('', params)

  // tạo tiến trình
  createProcess = (params: any) => this.apiPost('', params)

  // tạo tiến trình nháp
  createSavepoint = (params: any) => this.apiPost('/savepoint', params)

  // chi tiết tiến trình
  detailProcess = (id: any) => this.apiGet(`/${id}`) as Promise<{ workflowStepsDTO?: any[] }>

  // cập nhật tiến trình
  editProcess = (params: any) => this.apiPut(`/${params?.id}`, params)

  // xóa 1 tiến trình
  deleteProcessById = (body: ProcessListType) => this.apiDelete(`/${body.id}`, body.id)

  // xóa nhiều tiến trình
  deleteListProcessByIds = (body: any[]) => this.apiDelete(`/delete-list`, body)

  // sinh mã tiến trình
  generateCode = () => this.apiGet(`/generate-code`) as Promise<string>

  // sinh mã bước tiến trình
  generateStepCode = () => this.apiGet(`/steps/generate-code`) as Promise<string>

  // danh sách trạng thái của sản phẩm
  productStates = (params: any) => this.apiGetWithoutPrefix(`/api/common/combobox/states`, params) as Promise<any[]>

  // danh sách trạng thái cấu hình chuyển đổi của sản phẩm
  productTransitionStates = (params: any) =>
    this.apiGetWithoutPrefix(`/api/common/combobox/state-transitions`, params) as Promise<any[]>
}

// eslint-disable-next-line import/no-anonymous-default-export
export default new Workflow({
  apiPrefix: '/admin-portal/workflows'
})
