import Base from './Base'

class AccountDEV extends Base {
  getList = (data: any) => this.apiGet('/users-dev', data)

  //xóa danh sách đối tác => chưa xong vì chưa có api
  deleteByIds = (params: number[]) => {
    return this.apiDelete(`/users-dev/partner?ids=${params}`, {})
  }

  getAdminAndDev = (data: any) => this.apiGet('/users/admin-dev', data)
}

// eslint-disable-next-line import/no-anonymous-default-export
export default new AccountDEV({ isOauth: true })
