import Cookie from 'js-cookie'

import { logout } from '@/components/auth/logout'
import Base, { resetPassword, updatePassword } from './Base'

interface Role {
  portal?: Portal[]
  permissions?: Permission[]
  name?: string
}

interface Portal {
  name: string
}

interface Permission {
  code: string
  parentId: number
}

interface User {
  roles: Role[]
  [key: string]: any
}

interface AffiliateProfile {
  affiliateStatus: string
  id: string
  affiliateCode: string
  affiliateLevel: number
  canLoginAffiliate: boolean
  isApproved: boolean
  referralCode: string
}

export const PARAM_LENGTH_OTP_COOKIE_NAME = 'lengthOTP'

class Users extends Base {
  ACC_STATUS = {
    INACTIVE: 'INACTIVE',
    DENIED: 'DENIED',
    DENIED_FROM_LOGIN: 'DENIED_FROM_LOGIN'
  }

  sendOTP = async (data: any) => {
    const otp = await this.apiPostWithoutPrefix(`/api/users-sme/send-otp`, data)

    Cookie.set(PARAM_LENGTH_OTP_COOKIE_NAME, otp?.lengthOTP)

    return otp
  }

  logout = logout

  getMyProfile = async () => {
    const user: User = await this.apiGet('/profile')

    const permissionData: string[] = []
    const portalData: string[] = []
    const adminPermission: string[] = []
    const smePermission: string[] = []
    const devPermission: string[] = []

    user.roles.forEach((role: Role) => {
      let isAdmin = false
      let isDev = false
      let isSme = false

      role.portal?.forEach((x: Portal) => {
        portalData.push(x.name)
        if (x.name === 'ADMIN') isAdmin = true
        if (x.name === 'DEV') isDev = true
        if (x.name === 'SME') isSme = true
      })

      role.permissions?.forEach((permission: Permission) => {
        if (!permissionData.includes(permission.code) && permission.parentId !== -1) {
          permissionData.push(permission.code)
        }

        if (isAdmin && !adminPermission.includes(permission.code) && permission.parentId !== -1) {
          adminPermission.push(permission.code)
        }

        if (isSme && !smePermission.includes(permission.code) && permission.parentId !== -1) {
          smePermission.push(permission.code)
        }

        if (isDev && !devPermission.includes(permission.code) && permission.parentId !== -1) {
          devPermission.push(permission.code)
        }
      })
    })

    const rolesNameFull = user?.roles?.map((e: Role) => e?.name)
    let data: User

    if (rolesNameFull?.includes('ROLE_AFFILIATE_CANHAN') || rolesNameFull?.includes('ROLE_AFFILIATE_DAILY')) {
      const affiliateResponse = await this.apiGet(`/affiliate/profile`)
      const affiliateProfile: AffiliateProfile = affiliateResponse.data // Access the data property here

      data = {
        ...user,
        isAffiliate:
          (rolesNameFull.filter(e => e === 'ROLE_SME').length === 1 ||
            rolesNameFull.filter(e => e === 'ROLE_ADMIN').length === 2) &&
          (rolesNameFull?.includes('ROLE_AFFILIATE_CANHAN') || rolesNameFull?.includes('ROLE_AFFILIATE_DAILY'))
            ? 'YES'
            : 'NO',
        isAdminAffiliate:
          (rolesNameFull?.includes('ROLE_AFFILIATE_CANHAN') || rolesNameFull?.includes('ROLE_AFFILIATE_DAILY')) &&
          rolesNameFull.filter(e => e === 'ROLE_ADMIN').length === 1 &&
          rolesNameFull.length > 2,
        affiliateStatus: affiliateProfile?.affiliateStatus,
        affId: affiliateProfile?.id,
        affCode: affiliateProfile?.affiliateCode,
        affiliateLevel: affiliateProfile?.affiliateLevel,
        canLoginAffiliate: affiliateProfile?.canLoginAffiliate,
        isApprovedAffiliate: affiliateProfile?.isApproved,
        referralCode: affiliateProfile?.referralCode
      }
    } else data = { ...user, isAffiliate: 'NO', affiliateLevel: -1, isAdminAffiliate: false }

    return {
      ...data,
      permissions: {
        adminPermission,
        smePermission,
        devPermission,
        permissionData
      },
      portals: portalData
    } as any
  }

  /**
   * render 1 chuỗi random dùng để set giá trị sessionId
   */
  renderSessionId = (length: number) => {
    let result = ''
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    const charactersLength = characters.length

    for (let i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * charactersLength))
    }

    return result
  }

  createStoreSessionInCookie = () => {
    const storeSession: any = {
      createSessionDate: new Date().toLocaleDateString(),
      sessionId: this.renderSessionId(25),
      '-1': this.getSessionInfoDefault()
    }

    const userId = Cookie.get('userId')

    if (userId) {
      storeSession[`${userId}`] = this.getSessionInfoDefault()
    }

    return storeSession
  }

  getSessionInfoDefault = () => ({
    listItemClick: [],
    listItemView: [],
    listActivitiIdAndCampaignIdFromEmail: [],
    listActivitiIdAndCampaignIdFromNoti: []
  })

  updateRegistryInfo = (data: any) => this.apiPostWithoutPrefix('/api/users-sme/update-registry-info', data)

  registerSme = async (registerSme: any) => {
    const res = await this.apiPost('-sme/register', registerSme)

    return res
  }

  registerSmePersonal = (registerSme: any) => this.apiPostWithoutPrefix('/api/users-sme/register_personal', registerSme)

  // Tạo KH mới phía Admin
  registerByAdmin = (data: any) =>
    this.apiPostWithoutPrefix(`/api/users-admin/create-sme?customerType=${data?.customerType}`, data)

  resendActivationMail = (data: any) => this.apiPostWithoutPrefix('/api/users-sme/resend-activation-mail', data)

  forgotPassword = (query: any) => this.apiPut('/forgot-password', query)

  verifyResetPasswordOTP = (data: any) => this.apiPostWithoutPrefix('/api/users/verify-otp-reset-password', data)

  verifyRegisterOTP = (data: any) => this.apiPostWithoutPrefix('/api/users-sme/verify-otp-activate', data)

  verifyLoginOTP = (data: any) => this.apiPostWithoutPrefix('/verify-otp-login', data)

  resendOTP = (data: any) => this.apiPostWithoutPrefix(`/api/users-sme/resend-otp`, data)

  resetPassword = resetPassword

  /** API Kích hoạt tài khoản */
  activeAccount = ({ id, activeKey }: { id: string; activeKey: string }) => this.apiGet(`/verify/${id}/${activeKey}`)

  /** api đổi mật khẩu */
  changePassword = (changePassword: any) => this.apiPut('/change-password', changePassword)

  /** api kiểm tra mật khẩu */
  validatePassword = (params: any) => this.apiGetWithoutPrefix(`/api/users-sme/validate-password`, params)

  /** api kiểm tra mật khẩu có mạnh không */
  validatePasswordQuality = (password: any) => this.apiGetWithoutPrefix(`/api/users-sme/password/validation`, password)

  /** api vô hiệu hóa tài khoản */
  changeStatusAccount = ({ id, status, reason }: any) =>
    this.apiPutWithoutPrefix(`/api/users-sme/${id}/status/${status}`, reason)

  /** api lấy thông tin doanh nghiệp */
  getSmeInfor = (params: any) => this.apiGet(`/business`, params)

  /** api lấy thông tin người đại diện */
  getRepresent = () => this.apiGet('/represent')

  /** api cập nhật hồ sơ người dùng */
  updateProfileUser = (user: any) => this.apiPut('/profile', user)

  /** api cập nhật hồ sơ doanh nghiệp */
  updateSmeProfile = (values: any) => this.apiPut(`/sme`, values)

  /** api cập nhật hồ sơ người đại diện */
  updateRepresentProfile = (data: any) => this.apiPut(`/represent`, data)

  /** danh sách tài khoản doanh nghiệp */
  getListSwitchAccount = (data: { email: string; phoneNumber: string }) =>
    this.apiGetWithoutPrefix(
      `/api/users-sme/get-list-switch-account?email=${data.email}&phoneNumber=${data.phoneNumber}`
    )

  /** api chi tiết nhân viên */
  getEmployeeInforByID = async (id: any) => {
    const user = await this.apiGet(`-sme/employees/${id}`)

    return user
  }

  /** api cập nhật thông tin nhân viên */
  updateEmployeeInforById = ({ id, values }: any) => this.apiPut(`-sme/employees/${id}`, values)

  /** api thông tin người dùng */
  getUserInfo = (id: any) => this.apiGet(`/${id}`)

  /** api cập nhật thông tin người dùng */
  updateUserInfo = (body: any) => this.apiPut(`/${body?.id}/sme`, body)

  /* api cập nhật mật khẩu */
  updatePassword = updatePassword

  /* api yêu cầu khôi phục tài khoản */
  recoveryAccount = (body: any) => this.apiPost('/recovery-account', body)

  /** api cập nhật hồ sơ doanh nghiệp */
  updateSmeTransferInfo = (req: any) =>
    this.apiPut(`/update-identity/${req?.id}?identity=${req?.repPersonalCertNumber}`, null)
}

// eslint-disable-next-line import/no-anonymous-default-export
export default new Users({
  apiPrefix: '/users',
  isOauth: true
})
