// API services cho quản lý trạng thái

import Base from '../Base'

import type {
  StateFormData,
  StateDetailData,
  StateListResponse,
  StateFilterParams,
  CreateStateTransitionConfigRequest,
  CreateStateTransitionConfigResponse,
  IStateTransitionHistoryParams
} from '@/types/workflow/stateTransition'

class StateTransitionAdmin extends Base {
  /** Lấy danh sách mail template */
  getMailTemplates = async (params?: { emailType?: number; actionType?: string }) => {
    const queryParams: Record<string, any> = {}

    if (params) {
      if (params.emailType !== undefined) queryParams.emailType = params.emailType
      if (params.actionType !== undefined) queryParams.actionType = params.actionType
    } else {
      queryParams.emailType = -1
      queryParams.actionType = ''
    }

    // Truyền cứng đối tượng áp dụng
    queryParams.lstObjectApply = 'STATE'

    return this.apiGetWithoutPrefix(`/api/admin-portal/crm/automation-rule/get-list-mail-template`, queryParams)
  }

  /** Lấy danh sách trạng thái */
  getStateList = async (params?: StateFilterParams): Promise<StateListResponse> => {
    const queryParams: Record<string, any> = {}

    if (params) {
      if (params.search) queryParams.search = params.search
      if (params.isName !== undefined) queryParams.isName = params.isName
      if (params.isCode !== undefined) queryParams.isCode = params.isCode
      if (params.status) queryParams.status = params.status
      if (params.startTime) queryParams.startTime = params.startTime
      if (params.endTime) queryParams.endTime = params.endTime
      if (params.isType !== undefined) queryParams.isType = params.isType
      if (params.applyAll !== undefined) queryParams.applyAll = params.applyAll
      if (params.objectTypes) queryParams.objectTypes = params.objectTypes
      if (params.page !== undefined) queryParams.page = params.page
      if (params.size !== undefined) queryParams.size = params.size
    }

    return this.apiGetWithoutPrefix(`/api/admin-portal/states`, queryParams)
  }

  /** Lấy chi tiết trạng thái theo ID */
  getStateById = async (id: string): Promise<StateDetailData | null> => {
    return this.apiGetWithoutPrefix(`/api/admin-portal/states/${id}`)
  }

  /** Thêm trạng thái mới */
  addState = async (data: StateFormData): Promise<StateDetailData> => {
    return this.apiPostWithoutPrefix(`/api/admin-portal/states`, data)
  }

  /** Cập nhật trạng thái */
  updateState = async (id: string, data: StateFormData): Promise<StateDetailData | null> => {
    return this.apiPutWithoutPrefix(`/api/admin-portal/states/${id}`, data)
  }

  /** Xóa danh sách trạng thái */
  deleteState = (ids: string[]) => this.apiDeleteWithoutPrefix(`/api/admin-portal/states`, { ids })

  /** Lấy combobox loại trạng thái */
  getStateTypeCombobox = async (params?: { page?: number; size?: number; search?: string }): Promise<any> => {
    const queryParams: Record<string, any> = {}

    if (params) {
      if (params.page !== undefined) queryParams.page = params.page
      if (params.size !== undefined) queryParams.size = params.size
      if (params.search !== undefined) queryParams.search = params.search
    }

    return this.apiGetWithoutPrefix(`/api/admin-portal/state-types/combobox`, queryParams)
  }

  /** Tạo mã trạng thái từ backend */
  generateStateCode = () => this.apiGetWithoutPrefix(`/api/admin-portal/states/generate/code`)

  /** Validate tên trạng thái */
  validateStateName = (data: { id?: string; name: string }) =>
    this.apiPostWithoutPrefix(`/api/admin-portal/states/validate-name`, data)

  /** Tạo cấu hình chuyển đổi trạng thái */
  createStateTransitionConfig = async (
    data: CreateStateTransitionConfigRequest
  ): Promise<CreateStateTransitionConfigResponse> => {
    return this.apiPostWithoutPrefix(`/api/admin-portal/state-transition`, data)
  }

  /** Lấy lịch sử chuyển đổi trạng thái */
  getHistory = async (draftId: string, params?: IStateTransitionHistoryParams) => {
    return this.apiGetWithoutPrefix(`/api/admin-portal/state-transition/${draftId}/history`, params)
  }
}

export const stateTransitionAdmin = new StateTransitionAdmin({})
