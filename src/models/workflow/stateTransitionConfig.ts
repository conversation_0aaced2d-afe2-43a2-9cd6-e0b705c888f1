// API services cho quản lý trạng thái

import type { IStateTransitionSearchParams } from '@/types/workflow/stateTransition'
import Base from '../Base'

import type { TransitionFilterParams, TransitionListResponse } from '@/types/workflow/transition'

class StateTransitionConfig extends Base {
  /** L<PERSON>y danh sách cấu hình chuyển đổi */
  getTransitionList = async (params?: TransitionFilterParams): Promise<TransitionListResponse> => {
    const queryParams: Record<string, any> = {}

    if (params) {
      if (params.search) queryParams.search = params.search
      if (params.lstObjectType) queryParams.lstObjectType = params.lstObjectType
      if (params.lstCategoryId) queryParams.lstCategoryId = params.lstCategoryId
      if (params.page !== undefined) queryParams.page = params.page
      if (params.size !== undefined) queryParams.size = params.size
      if (params.sort !== undefined) queryParams.sort = params.sort
      if (params.startTime !== undefined) queryParams.startTime = params.startTime
      if (params.endTime !== undefined) queryParams.endTime = params.endTime
    }

    return this.apiGetWithoutPrefix(`/api/admin-portal/state-transition`, queryParams)
  }

  getComboboxCategory = async (): Promise<any> => {
    return this.apiGetWithoutPrefix(`/api/admin-portal/categories/getAll`)
  }

  /** Lấy chi tiết state-transition theo ID */
  getStateTransitionById = async (stateTransitionId: string): Promise<any> => {
    return this.apiGet(`/${stateTransitionId}`)
  }

  /** Lấy cấu hình chi tiết state-transition theo ID */
  getStateTransitionConfig = async (stateTransitionId: string, params?: IStateTransitionSearchParams): Promise<any> => {
    return this.apiGet(`/${stateTransitionId}/config`, params)
  }

  /** Tải file cấu hình */
  uploadJSONConfigFile = async (data: any) => this.apiPost('/flatten', data)

  /** Combobox trạng thái tiền nhiệm */
  getComboboxPreviousState = async (params?: {
    page?: number
    size?: number
    search?: string
    idsIgnore?: string[]
  }): Promise<any> => {
    const queryParams: Record<string, any> = {}

    if (params) {
      if (params.page !== undefined) queryParams.page = params.page
      if (params.size !== undefined) queryParams.size = params.size
      if (params.search !== undefined) queryParams.search = params.search
      if (params.idsIgnore !== undefined) queryParams.idsIgnore = params.idsIgnore
    }

    return this.apiGetWithoutPrefix(`/api/admin-portal/states/combobox`, queryParams)
  }

  // Chi tiết TT: tab danh sách đơn hàng - danh sách đơn hàng áp dụng
  getOrderApplyList = async (id?: any, query?: any) => {
    return this.apiGet(`/${id}/orders`, query)
  }

  // Lấy lịch sử đơn hàng theo subscriptionId
  getOrderHistory = async (subscriptionId: number) => {
    return this.apiGet(`/orders/${subscriptionId}/history`)
  }
}

export const stateTransitionConfig = new StateTransitionConfig({ apiPrefix: '/admin-portal/state-transition' })
