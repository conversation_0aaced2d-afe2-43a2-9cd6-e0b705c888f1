import type { ListProcessParams, ProcessListType } from '@/types/workflow/processManagement'
import Base from '../Base'

class ProcessManagementAdmin extends Base {
  // region list
  // danh sách tiến trình
  getListProcess = async (params?: ListProcessParams) => {
    return this.apiGet('', params)
  }

  // xóa 1 tiến trình
  deleteProcessById = async (body: ProcessListType) => {
    return this.apiDelete(`/${body.id}`, body.id)
  }

  // xóa nhiều tiến trình
  deleteListProcessByIds = async (body: any[]) => {
    return this.apiDelete(`/delete-list`, body)
  }

  // region detail
  // chi tiết tiến trình
  getDetailProcessById = async (id?: any) => {
    return this.apiGet(`/${id}`)
  }

  // tab danh sách đơn hàng - thống kê theo step
  getStatisticOrderByStep = async (id?: any) => {
    return this.apiGet(`/${id}/statistics`)
  }

  // tab danh sách đơn hàng - chi tiết chuyển đổi step
  getLstStepHistory = async (id?: any) => {
    return this.apiGet(`/step-history/orders/${id}`)
  }

  // tab danh sách đơn hàng - danh sách đơn hàng áp dụng
  getListOrderApply = async (id?: any, query?: any) => {
    return this.apiGet(`/${id}/orders`, query)
  }

  // tab lịch sử hoạt động
  getProcessActivityHistory = async (draftId: any, params: any) => {
    return this.apiGet(`/${draftId}/history`, params)
  }

  // lấy danh sách trạng thái cho combobox
  getStepsCombobox = async () => {
    return this.apiGet('/steps/combobox')
  }

  // region create
  getListTemplateEmail = async (query?: any) => {
    return this.apiGetWithoutPrefix(`/api/admin-portal/crm/automation-rule/get-list-mail-template`, query)
  }
}

export const processManagement = new ProcessManagementAdmin({ apiPrefix: '/admin-portal/workflows' })
