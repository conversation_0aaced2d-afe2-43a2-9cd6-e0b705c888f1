import Base from './Base'

class Common extends Base {
  getCode = async (params: any) => this.apiGetWithoutPrefix('/api/portal/generate/key', params)

  getTaxCodeList = async (query: any = {}) => {
    try {
      return await this.apiGetWithoutPrefix(`/api/common/combobox/get-partner-tax`, query)
    } catch (e) {
      return []
    }
  }

  getRepPersonalCertNumberList = async (query: any = {}) => {
    try {
      return await this.apiGetWithoutPrefix(`/api/common/combobox/get-partner-identity-number`, query)
    } catch (e) {
      return []
    }
  }
}
const commonInstance = new Common({ apiPrefix: '' })

export default commonInstance
