import Cookie from 'js-cookie'

import type { CalculateService } from '@/types/PaymentType'

import Base from './Base'

class SmeSubscription extends Base {
  getDataCalculate = ({
    planId,
    subcriptionInfo
  }: {
    planId: number | null
    subcriptionInfo: CalculateService | any
  }) => this.apiPost(`/calculate/new/pricing/${planId}`, subcriptionInfo)

  getDataCalculateDevice = (body: any) => this.apiPost(`/calculate/new/service`, body)

  getCalculateAddon = (data: any) => this.apiPost(`/calculate/addon`, data)

  getCalculateAddonByPackage = (data: any) => this.apiPostWithoutPrefix(`/api/portal/packages/calculate/addon`, data)

  getInfoPricing = (serviceId: number | null) => this.apiGetWithoutPrefix(`/api/sme-portal/services/${serviceId}`)

  /**
   * type: 0 -> xu<PERSON>t hóa đơn, 1 -> <PERSON><PERSON><PERSON> hàng
   * */
  getListAddressByTypeAndUserId = (type: number, uid: number, employeeType?: number) =>
    this.apiGetWithoutPrefix(
      `/api/sme-portal/address/getAddress/${uid}/${type}${employeeType === 1 ? `?employeeType=1` : ``}`
    )

  addSubscription = async (data: any) => {
    const ipAddress = await this.getClientIp()

    const affiliateLinkCode = Cookie.get('affiliate_link_code')

    let trafficId = null
    const trafficUser = Cookie.get('aff_u')
    let trafficSource = null

    if (Cookie.get('aff_content')) {
      trafficId = Cookie.get('aff_content')
      trafficSource = Cookie.get('aff_source')
    } else if (Cookie.get('_aff_sid')) {
      trafficId = Cookie.get('_aff_sid')
      trafficSource = Cookie.get('_aff_network')
    } else if (Cookie.get('APINFO')) {
      trafficId = Cookie.get('APINFO')
      trafficSource = 'apinfo'
    }

    const res = await this.apiPost(
      data.subscriptionId
        ? `/${data.subscriptionId}?ipAddress=${ipAddress}&portalType=SME`
        : `?ipAddress=${ipAddress}&portalType=SME`,
      {
        ...data,
        affiliateLinkCode,
        trafficId,
        trafficUser,
        trafficSource
      }
    )

    return res
  }

  addShoppingCartSubscription = async (data: any) => {
    const ipAddress = await this.getClientIp()

    const res = await this.apiPostWithoutPrefix(
      `/api/sme-portal/shopping-cart/register-from-cart?ipAddress=${ipAddress}`,
      {
        ...data
      }
    )

    return res
  }

  payment = async ({ id, paymentMethod }: { id: number; paymentMethod: string }) => {
    const ipAddress = await this.getClientIp()

    const res = await this.apiPostWithoutPrefix(
      `/api/sme-portal/billings/payment?ipAddress=${ipAddress}&portalType=SME`,
      {
        billingId: id,
        paymentMethod
      }
    )

    return res
  }

  getConfigExportInvoice = () => this.apiGetWithoutPrefix(`/api/sme-portal/system-param/detail/EXPORT_BILLING`)

  getInfoCustomer = () => this.apiGetWithoutPrefix(`/api/sme-portal/subscription/customer`)

  /**
   *
   * @param companyId
   * @param pricingId
   * @param params
   * @param body
   */
  getCouponPricing = ({ companyId, id, params, body }: { companyId: number; id: number; params: any; body: any }) =>
    this.apiPostWithoutPrefixParams(
      `/api/portal/subscription/pricing/coupon-mc-promotion/${companyId}/${id}`,
      params,
      body
    )

  /**
   *
   * @param companyId
   * @param pricingId
   * @param params
   * @param body
   */
  getCouponBundling = ({ companyId, id, params, body }: { companyId: number; id: number; params: any; body: any }) =>
    this.apiPostWithoutPrefixParams(
      `/api/portal/subscription/bundling/coupon-mc-promotion/${companyId}/${id}`,
      params,
      body
    )

  /**
   *
   * @param companyId
   * @param addonId
   * @param params
   * @param body
   */
  getCouponAddon = ({ companyId, id, params, body }: { companyId: number; id: number; params: any; body: any }) =>
    this.apiPostWithoutPrefixParams(
      `/api/portal/subscription/addon/coupon-mc-promotion/${companyId}/${id}`,
      params,
      body
    )

  /**
   *
   * @param companyId
   * @param pricingId
   * @param variantId
   * @param params
   * @param body
   */

  getCouponTotalPricing = ({
    companyId,
    id,
    variantId,
    params,
    body
  }: {
    companyId: number
    id: number
    variantId?: number
    params: any
    body: any
  }) =>
    this.apiPostWithoutPrefixParams(
      `/api/portal/subscription/total/coupon-mc-promotion/${companyId}/${id}?variantId=${variantId}`,
      params,
      body
    )

  /**
   *
   * @param companyId
   * @param comboPlanId
   * @param params
   * @param body
   */

  getCouponOfComboPlan = ({ companyId, id, params, body }: { companyId: number; id: number; params: any; body: any }) =>
    this.apiPostWithoutPrefixParams(
      `/api/portal/subscription/combo-plan/coupon-mc-promotion/${companyId}/${id}`,
      params,
      body
    )

  /**
   *
   * @param companyId
   * @param comboPlanId
   * @param params
   * @param body
   */
  getInfoCouponTotalCombo = ({
    companyId,
    id,
    params,
    body
  }: {
    companyId: number
    id: number
    params: any
    body: any
  }) =>
    this.apiPostWithoutPrefixParams(
      `/api/portal/subscription/combo-plan/total/coupon-mc-promotion/${companyId}/${id}`,
      params,
      body
    )

  // API lấy cấu hình đã thiết lập bên admin
  getInfoConfigCoupon = ({ paramType }: { paramType: string }) =>
    this.apiGetWithoutPrefix(`/api/sme-portal/system-param/detail/${paramType}`) as Promise<{ paramValue?: string }>

  //api danh sach credit note
  getListCreditNote = (id: any, params: any) => this.apiGet(`/pricing/${id}/credit-notes`, params)

  //api chi tiet credit note
  getCreditNoteDetail = async (id: string) => {
    const res = await this.apiGetWithoutPrefix(`/api/sme-portal/credit-note/detail/${id}`)

    return res
  }

  // kích hoạt lại dịch vụ
  putReActiveSub = ({ id, body }: any) => this.apiPut(`/re-active/${id}`, body)

  checkCouponCode = ({
    code,
    pricingId,
    type,
    pricingMultiPlanId
  }: {
    code: string
    pricingId: number
    type: number
    pricingMultiPlanId: number
  }) =>
    pricingMultiPlanId
      ? this.apiGetWithoutPrefix(
          `/api/coupon-item/check?code=${code}&pricingId=${pricingId}&type=${type}&multiPlanId=${pricingMultiPlanId}`
        )
      : this.apiGetWithoutPrefix(`/api/coupon-item/check?code=${code}&pricingId=${pricingId}&type=${type}`)

  checkCouponCodeCombo = ({ code, comboPlanId, type }: { code: string; comboPlanId: number; type: number }) =>
    this.apiGetWithoutPrefix(`/api/coupon-item/check?code=${code}&comboPlanId=${comboPlanId}&type=${type}`)

  // API tìm kiếm khuyến mại của bundling
  checkCouponCodeBundling = ({
    code,
    packageDraftId,
    solutionDraftId,
    type
  }: {
    code: string
    packageDraftId: number
    solutionDraftId: number
    type: number
  }) =>
    this.apiGetWithoutPrefix(
      `/api/coupon-item/check?code=${code}&packageDraftId=${packageDraftId}&solutionDraftId=${solutionDraftId}&type=${type}`
    )

  // thay doi trang thai nhan vien
  modifyUserUsing = ({ id, query, typeSubscription }: any) =>
    this.apiPut(`/${id}/users${typeSubscription ? `?type=${typeSubscription}` : ''}`, query)

  // download credit note
  downloadDetailCreditNote = async (id: number) => {
    const res = await this.apiGetWithoutPrefixDownload(`/api/sme-portal/credit-note/${id}/export-pdf`)

    return res
  }

  getListTin1 = (tin: any) => this.apiGetWithoutPrefix(`/api/enterprise/checkTin/${tin}`)

  updateSmeProfile = (values: any) => this.apiPutWithoutPrefix(`/auth-server/api/users/sme`, values)

  // Tạo preOder trên cache
  reqCreatePreOder = (params?: any) => this.apiPostWithoutPrefix('/api/sme-portal/pre-order', params)

  // check preOder đã được đang ký trên cache
  reqCheckPreOder = ({ preOrderId }: any) => this.apiGetWithoutPrefix(`/api/sme-portal/pre-order/${preOrderId}`)

  getDataCalculateCombo = (data: any) =>
    this.apiPostWithoutPrefix('/api/sme-portal/subscription/calculate/combo/new', data)

  getInfoServiceGroup = async (id: number) => {
    const res = await this.apiGetWithoutPrefix(`/api/sme-portal/subscription/service-group/${id}`)

    return res
  }

  calculateServiceGroup = async (id: number) => {
    const res = await this.apiGetWithoutPrefix(`/api/sme-portal/subscription/calculate/new/service-group/${id}`)

    return res
  }

  addSubscriptionServiceGroup = async (data: any) => {
    const ipAddress = await this.getClientIp()

    const res = await this.apiPostWithoutPrefix(
      `/api/sme-portal/subscription?ipAddress=${ipAddress}&portalType=SME`,
      data
    )

    return res
  }

  // Lấy thông tin chi tiết cart
  getDetailCart = () => this.apiGetWithoutPrefix(`/api/sme-portal/shopping-cart/get-detail`)

  // Api tính toán cart
  calculateCart = async (body: any) => this.apiPostWithoutPrefix(`/api/sme-portal/shopping-cart/calculate-cart`, body)

  // lấy thông tin gói dịch vụ
  getServiceAfterSubscription = (pricingId: number) => this.apiGet(`/detail/${pricingId}`)

  // chi tiết combo pack subscription
  getComboPackDetailSubscription = (id: number) => this.apiGet(`/${id}/combo`)

  getDataServiceCalculate = (subscription: any) => this.apiPost(`/calculate/new/service`, subscription)

  getDataCalculatePricing = ({ planId, subcriptionInfo }: { planId: number; subcriptionInfo: object }) =>
    this.apiPost(`/calculate/new/pricing/${planId}`, subcriptionInfo)

  // Popup xem trước chi phí
  postPreviewCost = ({ cycleNo, subscriptionInfo }: { cycleNo: any; subscriptionInfo: any }) =>
    this.apiPost(`/bill-incurred?cycleNo=${cycleNo}`, subscriptionInfo)

  // chỉnh sửa thông tin gói dịch vụ
  putServiceAfterSubscription = async (id: number, body: any) => {
    const ipAddress = await this.getClientIp()

    const res = await this.apiPut(`/update/${id}?ipAddress=${ipAddress}&portalType=SME`, body)

    return res
  }

  checkOrderQr = (billId: number | string) =>
    this.apiGetWithoutPrefix(`/api/portal/subscription/check-order-qr/${billId}`)

  reqRegisterComboTrial = ({ comboPlanId, employeeCode }: any) =>
    this.apiPostWithoutPrefix('/api/sme-portal/subscription/combo/trial', {
      comboPlanId,
      employeeCode
    })

  reqRegisterSubscriptionTrial = ({ pricingId, pricingMultiPlanId, employeeCode }: any) =>
    this.apiPostWithoutPrefix('/api/sme-portal/subscription/trial', {
      pricingId,
      pricingMultiPlanId,
      employeeCode
    })

  getSubscriptionsByIds = async (ids: number[] | string[]) => {
    const query = `subIds=${ids.join(',')}`

    return this.apiGetWithoutPrefix(`/api/portal/subscription/getSubscriptionsByIds?${query}`)
  }
}

const smeSubscriptionInstance = new SmeSubscription({
  apiPrefix: '/sme-portal/subscription'
})

export default smeSubscriptionInstance
