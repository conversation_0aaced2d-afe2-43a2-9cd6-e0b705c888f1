import Base from './Base'

class DevPricing extends Base {
  // lấy thông tin thiếp lập
  getListPricingForService = async (id: any, params: any) => {
    try {
      const res = await this.apiGet(`/service/${id}`, params)

      return this.formatResPagination(res.body || res)
    } catch (e) {
      return null
    }
  }

  // Lấy thông tin gói dịch vụ
  getPricing = (
    portal: 'admin-portal' | 'dev-portal' | 'partner-portal',
    id: any,
    status: 'PROCESSING' | 'APPROVED' | any
  ) =>
    this.apiGetWithoutPrefix(`/api/${portal}/pricing/${id}/type/${status}`) as any

  // lấy thông tin thiết lập
  getSettingInfo = async (serviceId: any, type: any) => {
    try {
      const res = await this.apiGetWithoutPrefix(`/api/portal/pricing/setting/${serviceId}/${type}`)

      return res.body || res
    } catch (e) {
      return null
    }
  }

  getServiceInfo = async (serviceId: any, type: any) => {
    try {
      const res = await this.apiGetWithoutPrefix(`/api/dev-portal/pricing/${serviceId}/type/${type}`)

      return res
    } catch (e) {
      return null
    }
  }

  insertPricingByServiceId = ({ portal, serviceId, data }: any) =>
    this.apiPostWithoutPrefix(`/api/${portal}/pricing/service/${serviceId}`, data)

  updatePricing = ({ portal, serviceId, pricingId, data }: any) =>
    this.apiPutWithoutPrefix(`/api/${portal}/pricing/${pricingId}/service/${serviceId}`, data)

  getDetailPricingHistory = async (
    portal: 'admin-portal' | 'dev-portal' | 'partner-portal',
    id: any,
    approve: any
  ) => {
    try {
      const res = await this.apiGetWithoutPrefix(`/api/${portal}/pricing/${id}/approve/${approve}`)

      return res.body || res
    } catch (e) {
      console.log(e)
      throw e
    }
  }
}

const PricingInstance = new DevPricing({
  apiPrefix: '/dev-portal/pricing'
})

export default PricingInstance

