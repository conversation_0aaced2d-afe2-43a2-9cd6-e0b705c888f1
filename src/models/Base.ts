import Cookie from 'js-cookie'
import { isObject, omit } from 'lodash'
import superagent from 'superagent'
import { v4 } from 'uuid'

import { notification } from '@/components/notification'
import { store } from '@/redux-store'
import { appActions } from '@/redux-store/slices/appSlice'

// region ENV
// env variables
// export const API_ROOT = 'https://onesme.vn'
// export const OAUTH_ROOT = 'https://onesme.vn/auth-server'

// export const API_ROOT = 'https://staging.onesme.vn';
// export const OAUTH_ROOT = 'https://staging.onesme.vn/auth-server';

// export const API_ROOT = 'https://devonedx.vnpt-technology.vn';
// export const OAUTH_ROOT = 'https://devonedx.vnpt-technology.vn/auth-server';

// export const API_ROOT = 'https://hotfixonedx.vnpt-technology.vn:9443';
// export const OAUTH_ROOT = 'https://hotfixonedx.vnpt-technology.vn:9443/auth-server';
// export const API_MIGRATE_ROOT = 'https://hotfixonedx.vnpt-technology.vn:9443/migrate';

// export const API_ROOT = 'https://business-dev.onesme.vn';
// export const OAUTH_ROOT = 'https://business-dev.onesme.vn/auth-server';
// export const API_MIGRATE_ROOT = 'https://business-dev.onesme.vn/migrate';

// export const API_ROOT = 'http://**********:8083'
// export const OAUTH_ROOT = 'http://**********:8082/auth-server'

// export const API_ROOT = 'http://localhost:8083';
// export const OAUTH_ROOT = 'http://localhost:8082/auth-server';
// export const API_MIGRATE_ROOT = 'http://localhost:8086/migrate';

// export const API_ROOT = 'https://stagingonedx.vnpt-technology.vn:6443'
// export const OAUTH_ROOT = 'https://stagingonedx.vnpt-technology.vn:6443/auth-server'

// export const API_ROOT = 'https://smarthome.vnpt-technology.vn:6888';
// export const OAUTH_ROOT = 'https://smarthome.vnpt-technology.vn:6888/auth-server';

// export const API_ROOT = 'https://smarthome.vnpt.vn';
// export const OAUTH_ROOT = 'https://smarthome.vnpt.vn/auth-server';

// export const API_ROOT = 'https://devonedx.vnpt-technology.vn:5443';
// export const OAUTH_ROOT = 'https://devonedx.vnpt-technology.vn:5443/auth-server';

// export const API_ROOT = 'https://bos.vnpt.vn'
// export const OAUTH_ROOT = 'https://bos.vnpt.vn/auth-server'

export const API_ROOT = process.env.NEXT_PUBLIC_API_ROOT || 'https://staging.onesme.vn'
export const OAUTH_ROOT = process.env.NEXT_PUBLIC_OAUTH_ROOT || 'https://staging.onesme.vn/auth-server'
export const API_MIGRATE_ROOT = process.env.NEXT_PUBLIC_API_MIGRATE_ROOT || 'https://staging.onesme.vn/migrate'

// export const API_INVENTORY_ROOT = 'https://kho-smarthome.vnpt-technology.vn';
export const API_INVENTORY_ROOT = process.env.NEXT_PUBLIC_API_INVENTORY_ROOT || 'https://staging.onesme.vn/inventory'

export const API_DEVICE_ROOT = process.env.NEXT_PUBLIC_API_DEVICE_ROOT || 'https://staging.onesme.vn/device-mgmt'

const USERNAME = process.env.NEXT_PUBLIC_SERVER_USERNAME || 'vnpt_clientid'
const PASSWORD = process.env.NEXT_PUBLIC_SERVER_PASSWORD || 'secret'

export const SUB_DOMAIN_ROOT = process.env.NEXT_PUBLIC_SUB_DOMAIN

export const TOKEN_PARAM = process.env.NEXT_PUBLIC_TOKEN_PARAM || 'ssoToken'

export const PARTNER_ACCOUNT_ID = process.env.NEXT_PUBLIC_PARTNER_ACCOUNT_ID || '3626'

// endregion

// token defined
let accessInfo: any
let refreshToken: any

export const parseAccess = () => {
  try {
    accessInfo = JSON.parse(Cookie.get(TOKEN_PARAM) as any)
    refreshToken = Cookie.get('refresh_token')
  } catch {
    accessInfo = null
  }
}

parseAccess()

export const getAccessToken = () => {
  parseAccess()

  return accessInfo?.access_token || ''
}

export const setVnptToken = (token: any, ttl: any) => {
  if (token) {
    Cookie.set('vnptToken', JSON.stringify(token), {
      expires: ttl / (24 * 60 * 60),

      // secure: true,
      sameSite: 'Lax'
    })
  } else {
    Cookie.remove('vnptToken')
  }
}

export const setGoogleToken = (token: any, ttl: any) => {
  if (token) {
    Cookie.set('googleToken', JSON.stringify(token), {
      expires: ttl / (24 * 60 * 60),

      // secure: true,
      sameSite: 'Lax'
    })
  } else {
    Cookie.remove('googleToken')
  }
}

export const setFacebookToken = (token: any, ttl: any) => {
  if (token) {
    Cookie.set('facebookToken', JSON.stringify(token), {
      expires: ttl / (24 * 60 * 60),

      // secure: true,
      sameSite: 'Lax'
    })
  } else {
    Cookie.remove('facebookToken')
  }
}

const getVnptToken = () => Cookie.get('vnptToken')

const configSetToken = {
  domain: SUB_DOMAIN_ROOT,
  path: '/'
}

export const setToken = async (access?: any, isWorkplace = false) => {
  const sessionInfoGetFromCookie = Cookie.get('sessionInfo')

  const cookieConfig: any = {
    secure: true,
    sameSite: isWorkplace ? 'None' : 'Lax'
  }

  if (access) {
    Cookie.set(TOKEN_PARAM, JSON.stringify(access), {
      expires: access.expires_in / (24 * 60 * 60),
      ...(process.env.NODE_ENV !== 'development' && configSetToken),
      ...cookieConfig
    })
    Cookie.set('refresh_token', access.refresh_token || refreshToken, {
      expires: 30,
      ...(process.env.NODE_ENV !== 'development' && configSetToken),
      ...cookieConfig
    })
    accessInfo = access
    refreshToken = access.refresh_token || refreshToken
  } else {
    Cookie.remove(TOKEN_PARAM, process.env.NODE_ENV !== 'development' && (configSetToken as any))
    Cookie.remove('refresh_token', process.env.NODE_ENV !== 'development' && (configSetToken as any))

    if (sessionInfoGetFromCookie !== undefined) {
      Cookie.set(
        'sessionInfo',
        JSON.stringify({
          sessionId: JSON.parse(sessionInfoGetFromCookie).sessionId,
          listItemClick: JSON.parse(sessionInfoGetFromCookie).listItemClick,
          listItemView: JSON.parse(sessionInfoGetFromCookie).listItemView,
          listActivitiIdAndCampaignIdFromEmail:
            JSON.parse(sessionInfoGetFromCookie).listActivitiIdAndCampaignIdFromEmail,
          listActivitiIdAndCampaignIdFromNoti: JSON.parse(sessionInfoGetFromCookie).listActivitiIdAndCampaignIdFromNoti,
          userId: null,
          createSessionDate: new Date().toLocaleDateString()
        }),
        { expires: 1 / 48 }
      )
    }

    Cookie.remove('userId')
    accessInfo = null
    refreshToken = null
    localStorage.removeItem('user')
  }
}

export const clearToken = () => setToken(null)

// http inject, parse
export const responseBody = (res: any) => (res.body ? res.body : res.text)

const responseBodyPost = (res: any) => res

export const tokenPlugin = (req: any) => {
  // if (!accessInfo) {
  parseAccess()

  // }
  if (accessInfo) {
    req.set('Authorization', `Bearer ${accessInfo.access_token}`)
  }
}

// region Error
// catch error
export const catchError = (e: any) => {
  let error = e.response?.body?.error

  if (!isObject(error)) {
    error = e.response?.body || e?.response || {}
  }

  error.status = e.status
  const errorCheckLstAddon = e.response?.body?.lstAddon
  const errorCheckLstPricing = e.response?.body?.lstPricing
  let statusACCOUNT = ''

  if (error.error_description === 'User is not activated') {
    statusACCOUNT = 'NOT_ACTIVE'
    store.dispatch(
      appActions.updateUser({
        statusACCOUNT
      })
    )
    error.dontCatchError = true
  } else if (
    error.errorCode === 'error.user.disable' ||
    error.error_code === 'error.user.inactive' ||
    error.error_description === 'User is disabled'
  ) {
    // Modal.error({
    //   title: 'Tài khoản đã bị vô hiệu hóa',
    //   content: 'Liên hệ với quản trị viên để được hỗ trợ'
    // })
    statusACCOUNT = 'INACTIVE'
    store.dispatch(
      appActions.updateUser({
        statusACCOUNT
      })
    )
    error.dontCatchError = true
    setToken()
  } else if (
    e.status === 403 ||
    error.errorCode === 'error.ticket.user.not.be.supporter' ||
    error.errorCode === 'error.ticket.sme.not.be.owner' ||
    error.errorCode === 'error.department.user.not.own' ||
    (error.errorCode === 'error.no.have.access' && error.object === 'customer_ticket')
  ) {
    statusACCOUNT = 'DENIED'
    store.dispatch(appActions.changeStatus(statusACCOUNT))
    error.dontCatchError = true
  } else if (e.status === 401) {
    store.dispatch(appActions.updateUser({}))
    error.dontCatchError = true
  } else if ((error.status === 400 && error.error === 'invalid_grant') || error.errorCode === 'error.data.format') {
    // do
  } else if (e.status === 502) {
    notification.error({
      message: 'Server đang bảo trì, vui lòng thử lại sau'
    })
  } else if (errorCheckLstAddon || errorCheckLstPricing) {
    // do
  } else if (
    !error.field &&
    !error.fields &&
    !e.response?.error?.url?.includes('/auth-server/api/users-sme/import/users') &&
    !e.response?.error?.url?.includes('/auth-server/api/users-sme/import-url/users') &&
    !error?.error?.url?.includes('/admin-portal/email-template/')
  ) {
    notification.error({
      message: 'Đã có lỗi xảy ra, vui lòng thử lại sau ít phút.'
    })
  }

  throw error
}
// endregion

// region Login
// auth http
export const getTokenByUsernamePassword = async (data: any) => {
  if (data.access_token) {
    setToken(data)

    return data
  }

  const res = await superagent
    .post(`${OAUTH_ROOT}/oauth/login`, {
      ...data,
      grant_type: 'password',
      scope: accessInfo?.scope || v4()

      // TODO : remove when deploy , only use when team stc dev
      // scope: 'dzcdc',
    })
    .type('form')
    .auth(USERNAME, PASSWORD)
    .then(responseBody)
    .catch(catchError)

  setToken(res)

  return res
}

export const getTokenByUsernamePasswordForSme = async (data: any) => {
  if (data.access_token) {
    setToken(data)

    return data
  }

  const res = await superagent
    .post(`${OAUTH_ROOT}/oauth/login`, {
      ...data,
      grant_type: 'password',
      scope: accessInfo?.scope || v4()

      // TODO : remove when deploy , only use when team stc dev
      // scope: 'dzcdc',
    })
    .type('form')
    .auth(USERNAME, PASSWORD)
    .then(responseBody)
    .catch(catchError)

  // setToken(res);
  return res
}

// Cập nhật mẩt khẩu
export const updatePassword = (data: any) => {
  return superagent
    .put(`${OAUTH_ROOT}/api/users/update-password`, data)
    .auth(USERNAME, PASSWORD) // Thêm basic auth
    .then(responseBody)
    .catch(catchError)
}

// Reset mẩt khẩu
export const resetPassword = ({ id, resetToken, newPassword }: any) => {
  return superagent
    .put(`${OAUTH_ROOT}/api/users/${id}/reset-password/${resetToken}`, newPassword)
    .auth(USERNAME, PASSWORD) // Thêm basic auth
    .then(responseBody)
    .catch(catchError)
}

// Dùng để lấy token ném vào api nào cần
export const getNewToken = async (err: any) => {
  try {
    if (!err || err.status !== 401) {
      return false
    }

    parseAccess()
    const scope = accessInfo?.scope

    if (!refreshToken || !scope) {
      throw new Error('no-token')
    }

    const res = await superagent
      .post(`${OAUTH_ROOT}/oauth/login`, {
        refresh_token: refreshToken,
        grant_type: 'refresh_token',
        scope
      } as any)
      .type('form')
      .auth(USERNAME, PASSWORD)
      .then(responseBody)

    setToken({
      scope,
      ...res
    })

    return true
  } catch (e) {
    clearToken()

    return false
  }
}
// endregion

// region Logout
export const logoutSSO = async () => {
  const tokenVnpt = getVnptToken() || ''

  if (!tokenVnpt) {
    clearToken()
  }

  const SANDBOX = process.env.NEXT_PUBLIC_SANDBOX_VNPT
  const loginUrl = `${window.location.origin}/sme-portal/login`
  const token = tokenVnpt.replaceAll('"', '')
  const logOutUrl = `${SANDBOX}/oidc/logout?post_logout_redirect_uri=${loginUrl}&id_token_hint=${token}`

  window.location.href = logOutUrl
}

// endregion

// region Requests
const requests = {
  del: (url: string, body: any) =>
    superagent.del(url, body).retry(1, getNewToken).use(tokenPlugin).then(responseBody).catch(catchError),
  get: (url: string, query: any) =>
    superagent.get(url, query).retry(1, getNewToken).use(tokenPlugin).then(responseBody).catch(catchError),
  put: (url: string, body: any) =>
    superagent.put(url, body).retry(1, getNewToken).use(tokenPlugin).then(responseBody).catch(catchError),
  post: (url: string, body: any) =>
    superagent.post(url, body).retry(1, getNewToken).use(tokenPlugin).then(responseBody).catch(catchError),
  postDownload: (url: string, body: any) =>
    superagent
      .post(url, body)
      .retry(1, getNewToken)
      .responseType('blob')
      .use(tokenPlugin)
      .then(responseBodyPost)
      .catch(catchError),
  putDownload: (url: string, body: any) =>
    superagent
      .put(url, body)
      .retry(1, getNewToken)
      .responseType('blob')
      .use(tokenPlugin)
      .then(responseBodyPost)
      .catch(catchError),
  download: (url: string, query: any) =>
    superagent
      .get(url, query)
      .retry(1, getNewToken)
      .responseType('blob')
      .use(tokenPlugin)
      .then(responseBody)
      .catch(catchError),
  postDownloadAPI: (url: string, query: any) =>
    superagent
      .post(url, query)
      .retry(1, getNewToken)
      .responseType('blob')
      .use(tokenPlugin)
      .then(responseBody)
      .catch(catchError),
  postUploadAPI: (url: string, body: any) =>
    superagent
      .post(url, body)
      .retry(1, getNewToken)
      .responseType('blob')
      .use(tokenPlugin)
      .then(responseBodyPost)
      .catch(catchError),

  postWithParams: (url: string, params: any, body: any) =>
    superagent
      .post(url, body)
      .query(params)
      .retry(1, getNewToken)
      .use(tokenPlugin)
      .then(responseBody)
      .catch(catchError),

  postFileWithFullResp: (url: string, body: any) =>
    superagent.post(url, body).retry(1, getNewToken).use(tokenPlugin).then(responseBodyPost).catch(catchError)
}
// endregion

// region Base
class Base {
  apiRoot: string
  apiPrefix: string

  constructor({
    apiPrefix = '',
    isOauth = false,
    isMigrate = false,
    isInventory = false,
    isControlled = false,
    isDevice = false
  }: {
    apiPrefix?: string
    isOauth?: boolean
    isMigrate?: boolean
    isInventory?: boolean
    isControlled?: boolean
    isDevice?: boolean
  }) {
    if (isOauth) {
      this.apiRoot = OAUTH_ROOT!
      this.apiPrefix = `${OAUTH_ROOT}/api${apiPrefix}`
    } else if (isMigrate) {
      this.apiRoot = API_MIGRATE_ROOT!
      this.apiPrefix = `${API_MIGRATE_ROOT}/api${isMigrate}`
    } else if (isInventory) {
      this.apiRoot = API_INVENTORY_ROOT!
      this.apiPrefix = `${API_INVENTORY_ROOT}/api${apiPrefix}`
    } else if (isControlled) {
      this.apiRoot = API_ROOT
      this.apiPrefix = `${this.apiRoot}${apiPrefix}`
    } else if (isDevice) {
      this.apiRoot = API_DEVICE_ROOT!
      this.apiPrefix = `${API_DEVICE_ROOT}/api${apiPrefix}`
    } else {
      this.apiRoot = API_ROOT!
      this.apiPrefix = `${API_ROOT}/api${apiPrefix}`
    }
  }

  apiGetWithoutPrefix = (url: string, query = {}) => requests.get(`${this.apiRoot}${url}`, this.normalizeQuery(query))

  apiPutWithoutPrefix = (url: string, body: any) => requests.put(`${this.apiRoot}${url}`, body)

  apiPostWithoutPrefix = (url: string, body: any) => requests.post(`${this.apiRoot}${url}`, body)

  apiDeleteWithoutPrefix = (url: string, body: any) => requests.del(`${this.apiRoot}${url}`, body)

  apiGet = (url: string, query = {}) => requests.get(`${this.apiPrefix}${url}`, this.normalizeQuery(query))

  apiPost = (url: string, body: any) => requests.post(`${this.apiPrefix}${url}`, body)

  apiPostWithoutPrefixDownload = (url: string, body = {}) => requests.postDownload(`${this.apiRoot}${url}`, body)

  apiGetWithoutPrefixDownload = (url: string, body = {}) => requests.download(`${this.apiRoot}${url}`, body)

  apiPostWithFullResponse = (url: string, body = {}) => requests.postFileWithFullResp(`${this.apiRoot}${url}`, body)

  apiPutWithoutPrefixDownload = (url: string, body = {}) => requests.putDownload(`${this.apiRoot}${url}`, body)

  apiDownload = (url: string, query = {}) => requests.download(`${this.apiPrefix}${url}`, this.normalizeQuery(query))

  apiPostDownload = (url: string, query = {}) =>
    requests.postDownloadAPI(`${this.apiPrefix}${url}`, this.normalizeQuery(query))

  apiPostUpload = (url: string, body: any) => requests.postUploadAPI(`${this.apiPrefix}${url}`, body)

  apiPut = (url: string, body: any) => requests.put(`${this.apiPrefix}${url}`, body)

  apiDelete = (url: string, body?: any) => requests.del(`${this.apiPrefix}${url}`, body)

  getOneById = (id: any) => this.apiGet(`/${id}`)

  getAll = (query: any) => this.apiGet('', query)

  getAllPagination = async (query: any) => {
    const res = await this.getAll(query)

    return this.formatResPagination(res)
  }

  insert = (data: any) => this.apiPost('', data)

  updateById = ({ id, data }: any) => this.apiPut(`/${id}`, data)

  deleteById = (id: any) => this.apiDelete(`/${id}`)

  apiPostWithoutPrefixParams = (url: string, params: any, body = {}) =>
    requests.postWithParams(`${this.apiRoot}${url}`, params, body)

  formatResPagination = (res: any) => {
    if (res?.data?.content) {
      // console.log("total ", res);
      return {
        content: res.data.content,
        total: res.data.totalElements,
        ...omit(res, ['data'])
      }
    }

    if (res?.content) {
      // console.log("total ", res);
      return {
        content: res.content,
        total: res.totalElements
      }
    }

    if (res?.length > 0) {
      return {
        content: res,
        total: res.length
      }
    }

    if (res?.data) {
      return {
        content: res.data,
        total: res.totalElements
      }
    }

    return {
      content: [],
      total: 0
    }
  }

  formatToPageWithFiveElementOnePage = (res: any) => {
    if (res?.length > 0) {
      return {
        content: res,
        total: 5
      }
    }

    return {
      content: [],
      total: 0
    }
  }

  formatResPaginationCoupon = (res: any) => {
    if (res?.data?.content) {
      // console.log("total ", res);
      return {
        content: res.data.content,
        total: res.data.numberOfElements,
        ...omit(res, ['data'])
      }
    }

    if (res?.content) {
      // console.log("total ", res);
      return {
        content: res.content,
        total: res.numberOfElements
      }
    }

    if (res?.length > 0) {
      return {
        content: res,
        total: res.length
      }
    }

    return {
      content: [],
      total: 0
    }
  }

  normalizeQuery = (query: any) => {
    const formatQuery: any = {}

    Object.keys(query).forEach(key => {
      if (query[key] !== null && typeof query[key] === 'string') {
        formatQuery[key] = query[key].trim()
      } else if (query[key] !== null && !Number.isNaN(query[key])) {
        formatQuery[key] = query[key]
      }
    })

    return formatQuery
  }

  getClientIp = async () => {
    let ip

    try {
      // Sử dụng các API miễn phí để lấy IP trong môi trường trình duyệt
      if (typeof window !== 'undefined') {
        // Đang chạy trong trình duyệt
        const response = await fetch('https://api.ipify.org?format=json')
        const data = await response.json()

        ip = data.ip
      } else {
        // Đang chạy trên server (Node.js)
        // Nếu cần sử dụng public-ip ở server, chỉ import khi cần
        const { publicIpv4 } = await import('public-ip')

        ip = await publicIpv4()
      }
    } catch (error) {
      console.log('error', error)
    }

    return ip
  }
}

export default Base

// endregion
