/** @type {import('next').NextConfig} */
import crypto from 'crypto'

import bundleAnalyzer from '@next/bundle-analyzer'
import CompressionPlugin from 'compression-webpack-plugin'
import CssMinimizerPlugin from 'css-minimizer-webpack-plugin'

const withBundleAnalyzer = bundleAnalyzer({
  enabled: process.env.NEXT_PUBLIC_ANALYZE === 'true'
})

export const customerTypes = ['home', 'enterprise', 'house-hold', 'personal']
export const pageTypes = [
  'account',
  'products',
  'service',
  'combos',
  'combo',
  'service-group',
  'cart',
  'contact-us',
  'search',
  'pricing',
  'van-ban-chap-thuan-xu-ly-bao-ve-du-lieu-ca-nhan',
  'sim-package',
  'data-package',
  'internet-television',
  'card',
  'solutions',
  'solution'
]

export const serviceTypes = ['service', 'combo', 'combos', 'products']

const createRedirectRules = () => [
  {
    source: '/affiliate/:path*',
    destination: `${process.env.NEXT_PUBLIC_API_ROOT}/affiliate/:path*`,
    permanent: true
  },
  // Đảm bảo rằng không có redirect nào cho file `firebase-messaging-sw.js`
  {
    source: '/firebase-messaging-sw.js',
    destination: '/firebase-messaging-sw.js', // Giữ nguyên để tránh redirect
    permanent: false // Đảm bảo rằng redirect không xảy ra
  }
]

const createRewriteRules = () => [
  ...customerTypes.flatMap(customerType =>
    pageTypes.map(pageType => ({
      source: `/${customerType}/${pageType}/:path*`,
      destination: `/${pageType}/:path*`
    }))
  ),
  {
    source: '/',
    destination: '/home'
  },
  // Chuyển hướng tự động cho trang iot-portal
  {
    source: '/iot-portal',
    destination: '/iot-portal/home'
  },
  // Chuyển hướng tự động cho trang partner-portal
  {
    source: '/partner-portal',
    destination: '/partner-portal/home'
  },
  // Chuyển hướng tự động cho trang quản lý chuyển đổi trạng thái
  {
    source: '/workflow/state-transition/transition-management',
    destination: '/workflow/state-transition/transition-management/list'
  },
  // Chuyển hướng tự động cho trang quản lý tiến trình
  {
    source: '/workflow/process-management',
    destination: '/workflow/process-management/list'
  }
]

const nextConfig = {
  reactStrictMode: true,
  devIndicators: {
    autoPrerender: false
  },
  async redirects() {
    return createRedirectRules()
  },

  async rewrites() {
    return createRewriteRules()
  },

  images: {
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    formats: ['image/webp', 'image/avif']
  },

  experimental: {
    optimizeCss: true,
    scrollRestoration: true,
    largePageDataBytes: 128 * 1000,
    optimizePackageImports: [
      '@mui/material',
      '@mui/icons-material',
      '@mui/lab',
      'lodash',
      'react-use',
      'antd',
      '@emotion/react',
      '@emotion/styled',
      'moment',
      'swiper',
      '@tanstack/react-query'
    ]
  },

  output: 'standalone',
  webpack: (config, { dev, isServer }) => {
    // Xử lý fallback cho các module browser-only
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        canvas: false,
        fs: false,
        net: false,
        tls: false,
        path: false,
        encoding: false,
        'pdfjs-dist/build/pdf.worker.js': false
      }
    }

    // Thêm một alias cho 'canvas' để điều hướng đến một file rỗng
    config.resolve.alias = {
      ...config.resolve.alias,
      canvas: false
    }

    if (!dev) {
      config.optimization.splitChunks = {
        chunks: 'all',
        minSize: 10000,
        maxSize: 200000,
        maxInitialRequests: Infinity,
        maxAsyncRequests: Infinity,
        cacheGroups: {
          default: false,
          vendors: false,
          framework: {
            chunks: 'all',
            name: 'framework',
            test: /(?<!node_modules.*)[\\/]node_modules[\\/](react|react-dom|scheduler|prop-types|use-subscription)[\\/]/,
            priority: 40,
            enforce: true,
            reuseExistingChunk: true
          },
          lib: {
            test(module) {
              return module.size() > 80000 && /node_modules[/\\]/.test(module.identifier())
            },
            name(module) {
              return crypto.createHash('sha1').update(module.identifier()).digest('hex').slice(0, 8)
            },
            priority: 30,
            minChunks: 1,
            reuseExistingChunk: true
          },
          uiLib: {
            test: /[\\/]node_modules[\\/]antd[\\/]/,
            name: 'ui-lib',
            chunks: 'all',
            priority: 35,
            enforce: true,
            reuseExistingChunk: true
          },
          commons: {
            name: 'commons',
            minChunks: 2,
            priority: 20
          },
          shared: {
            name(module, chunks) {
              return (
                crypto
                  .createHash('sha1')
                  .update(chunks.map(c => c.name).join('_'))
                  .digest('hex') + '_shared'
              )
            },
            priority: 10,
            minChunks: 2,
            reuseExistingChunk: true
          }
        }
      }

      config.optimization.minimize = true
      config.optimization.minimizer.push(new CssMinimizerPlugin())

      if (process.env.NEXT_PUBLIC_ANALYZE === 'true') {
        config.plugins.push(
          new CompressionPlugin({
            filename: '[path][base].gz',
            algorithm: 'gzip',
            test: /\.(js|css|html|svg)$/,
            threshold: 10240,
            minRatio: 0.8
          }),
          new CompressionPlugin({
            filename: '[path][base].br',
            algorithm: 'brotliCompress',
            test: /\.(js|css|html|svg)$/,
            threshold: 10240,
            minRatio: 0.8
          })
        )
      }
    }

    if (!isServer) {
      config.optimization.moduleIds = 'deterministic'
      config.optimization.runtimeChunk = 'single'
    }

    config.output.globalObject = 'self'

    return config
  },

  compress: true,
  productionBrowserSourceMaps: false,
  transpilePackages: ['lodash-es', 'ky']
}

export default withBundleAnalyzer(nextConfig)
