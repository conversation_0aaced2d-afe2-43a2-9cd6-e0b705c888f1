#!/bin/bash

# L<PERSON>y thời gian hiện tại
current_timestamp=$(date +%s)

# Tính toán timestamp của 3 tháng trước
three_months_ago=$((current_timestamp - 90*24*60*60))

# Danh sách các nh<PERSON>h đ<PERSON><PERSON> bảo vệ
protected_branches="master|main|internal_staging|staging|production"

echo "<PERSON>ang cập nhật thông tin từ remote..."
git fetch --prune

echo "Đang kiểm tra các nhánh remote..."
echo "-----------------------------"

# <PERSON><PERSON><PERSON> danh sách tất cả các nhánh remote
git branch -r | grep -v "HEAD\|$protected_branches" | sed 's/origin\///' | while read branch; do
    # Lấy timestamp của commit cuối cùng trên nhánh
    last_commit_timestamp=$(git log -1 --format=%at "origin/$branch" 2>/dev/null)

    if [ -n "$last_commit_timestamp" ]; then
        if [ "$last_commit_timestamp" -lt "$three_months_ago" ]; then
            # <PERSON><PERSON><PERSON> thông tin chi tiết về nhánh
            last_commit_date=$(date -r "$last_commit_timestamp" "+%Y-%m-%d")
            last_commit_msg=$(git log -1 --format=%s "origin/$branch")
            author=$(git log -1 --format=%an "origin/$branch")

            echo "Đang xóa nhánh remote: $branch"
            echo "Commit cuối cùng bởi: $author"
            echo "Ngày: $last_commit_date"
            echo "Message: $last_commit_msg"

            # Xóa nhánh remote
            git push origin --delete "$branch"
            echo "Đã xóa thành công nhánh remote: $branch"
            echo "-----------------------------"
        fi
    fi
done

echo "Hoàn tất quá trình dọn dẹp!"

# Tối ưu repository
echo "Đang chạy git garbage collection..."
git gc --prune=now

echo "Script hoàn tất!"
