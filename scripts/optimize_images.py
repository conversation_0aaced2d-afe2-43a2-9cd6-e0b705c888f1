import os
from PIL import Image
from svglib.svglib import svg2rlg
from reportlab.graphics import renderPM

# Đường dẫn đến thư mục chứa ảnh và mã nguồn
images_dir = os.path.join(os.getcwd(), 'public/')
src_dir = os.path.join(os.getcwd(), 'src')

def convert_to_webp(image_path):
    try:
        img = Image.open(image_path)
        webp_path = os.path.splitext(image_path)[0] + '.webp'
        img.save(webp_path, 'webp', quality=80)
        os.remove(image_path)  # X<PERSON>a ảnh gốc sau khi chuyển đổi
        print(f"Converted {image_path} to {webp_path}")
        return webp_path
    except Exception as e:
        print(f"Error converting {image_path}: {e}")
        return None

def convert_svg_to_png(svg_path):
    try:
        png_path = os.path.splitext(svg_path)[0] + '.png'
        print(f"Converting SVG to PNG: {svg_path}")
        drawing = svg2rlg(svg_path)
        renderPM.drawToFile(drawing, png_path, fmt='PNG')
        print(f"PNG saved to: {png_path}")
        return png_path
    except Exception as e:
        print(f"Error converting {svg_path} to PNG: {e}")
        return None

def process_images(directory):
    converted_images = {}
    for root, _, files in os.walk(directory):
        print(f"Processing directory: {root}")
        for file in files:
            file_path = os.path.join(root, file)
            if file.lower().endswith(('.png', '.jpg', '.jpeg')):
              print(f"Processing image file: {file_path}")
              converted_image = convert_to_webp(file_path)
              if converted_image:
                  old_path = os.path.relpath(file_path, images_dir).replace('\\', '/')
                  new_path = os.path.relpath(converted_image, images_dir).replace('\\', '/')
                  converted_images[old_path] = new_path
            elif file.lower().endswith('.svg'):
                print(f"Processing SVG file: {file_path}")
                converted_image = convert_svg_to_png(file_path)
                if converted_image:
                    webp_image = convert_to_webp(converted_image)
                    if webp_image:
                        old_path = os.path.relpath(file_path, images_dir).replace('\\', '/')
                        new_path = os.path.relpath(webp_image, images_dir).replace('\\', '/')
                        converted_images[old_path] = new_path
    return converted_images

def update_references(directory, converted_images):
    for root, _, files in os.walk(directory):
        for file in files:
            if file.endswith(('.js', '.jsx', '.ts', '.tsx', '.html', '.css')):
                file_path = os.path.join(root, file)
                print(f"Updating references in: {file_path}")
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                updated_content = content
                for old_path, new_path in converted_images.items():
                    print(f"Replacing {old_path} with {new_path} in {file_path}")
                    updated_content = updated_content.replace(old_path, new_path)

                if updated_content != content:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(updated_content)
                    print(f"Updated references in: {file_path}")

# Thực hiện tối ưu hóa các ảnh trong thư mục images và cập nhật các references
print("Starting image conversion...")
converted_images = process_images(images_dir)
print("Starting reference update...")
update_references(src_dir, converted_images)
print("Image conversion and reference update complete.")
